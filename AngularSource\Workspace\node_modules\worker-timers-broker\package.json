{"_from": "worker-timers-broker@^6.0.29", "_id": "worker-timers-broker@6.1.8", "_inBundle": false, "_integrity": "sha512-FUCJu9jlK3A8WqLTKXM9E6kAmI/dR1vAJ8dHYLMisLNB/n3GuaFIjJ7pn16ZcD1zCOf7P6H62lWIEBi+yz/zQQ==", "_location": "/worker-timers-broker", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "worker-timers-broker@^6.0.29", "name": "worker-timers-broker", "escapedName": "worker-timers-broker", "rawSpec": "^6.0.29", "saveSpec": null, "fetchSpec": "^6.0.29"}, "_requiredBy": ["/worker-timers"], "_resolved": "https://registry.npmjs.org/worker-timers-broker/-/worker-timers-broker-6.1.8.tgz", "_shasum": "08f64e5931b77fadc55f0c7388c077a7dd17e4c7", "_spec": "worker-timers-broker@^6.0.29", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\worker-timers", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/chrisguttandin/worker-timers-broker/issues"}, "bundleDependencies": false, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "dependencies": {"@babel/runtime": "^7.24.5", "fast-unique-numbers": "^8.0.13", "tslib": "^2.6.2", "worker-timers-worker": "^7.0.71"}, "deprecated": false, "description": "The broker which is used by the worker-timers package.", "devDependencies": {"@babel/core": "^7.24.5", "@babel/plugin-external-helpers": "^7.24.1", "@babel/plugin-transform-runtime": "^7.24.3", "@babel/preset-env": "^7.24.5", "@commitlint/cli": "^19.3.0", "@commitlint/config-angular": "^19.3.0", "@rollup/plugin-babel": "^6.0.4", "chai": "^4.3.10", "commitizen": "^4.3.0", "cz-conventional-changelog": "^3.3.0", "eslint": "^8.57.0", "eslint-config-holy-grail": "^59.0.8", "grunt": "^1.6.1", "grunt-cli": "^1.4.3", "grunt-sh": "^0.2.1", "husky": "^8.0.3", "karma": "^6.4.3", "karma-chrome-launcher": "^3.2.0", "karma-firefox-launcher": "^2.1.3", "karma-mocha": "^2.0.1", "karma-sauce-launcher": "^4.3.6", "karma-sinon-chai": "^2.0.2", "karma-webkit-launcher": "^2.4.0", "karma-webpack": "^5.0.1", "lint-staged": "^15.2.2", "load-grunt-config": "^4.0.1", "mocha": "^10.4.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup": "^4.17.2", "sinon": "^17.0.1", "sinon-chai": "^3.7.0", "ts-loader": "^9.5.1", "tsconfig-holy-grail": "^15.0.1", "tslint": "^6.1.3", "tslint-config-holy-grail": "^56.0.1", "typescript": "^5.4.5", "webpack": "^5.91.0"}, "files": ["build/es2019/", "build/es5/", "src/"], "homepage": "https://github.com/chrisguttandin/worker-timers-broker", "license": "MIT", "main": "build/es5/bundle.js", "module": "build/es2019/module.js", "name": "worker-timers-broker", "repository": {"type": "git", "url": "git+https://github.com/chrisguttandin/worker-timers-broker.git"}, "scripts": {"build": "rimraf build/* && tsc --project src/tsconfig.json && rollup --config config/rollup/bundle.mjs", "lint": "npm run lint:config && npm run lint:src && npm run lint:test", "lint:config": "eslint --config config/eslint/config.json --ext .js --report-unused-disable-directives config/", "lint:src": "tslint --config config/tslint/src.json --project src/tsconfig.json src/*.ts src/**/*.ts", "lint:test": "eslint --config config/eslint/test.json --ext .js --report-unused-disable-directives test/", "prepare": "husky install", "prepublishOnly": "npm run build", "test": "grunt lint && grunt test"}, "types": "build/es2019/module.d.ts", "version": "6.1.8"}