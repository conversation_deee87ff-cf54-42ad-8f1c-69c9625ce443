(window.webpackJsonp=window.webpackJsonp||[]).push([[55],{jezL:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),l=i("mrSG"),a=i("447K"),o=i("ZYCi"),s=i("D00E"),d=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.jsonReader=new a.L,n.screenName=a.x.call("getBundle","text","label-corporateEntries","Corporate Entries"),n.versionNumber="1.1.0007",n.baseURLMETHOD="",n.corpAccName="",n.baseURL=a.Wb.getBaseURL(),n.actionPath="",n.actionMethod="",n.requestParams=[],n.saveFlag=!0,n.menuAccessIdParent=0,n.corpSeqNo="",n.inputData=new a.G(n.commonService),n.swtAlert=new a.bb(e),n}return l.d(e,t),e.prototype.ngOnInit=function(){},e.prototype.onLoad=function(){var t=this;this.cGrid=this.corpAccContainer.addChild(a.hb),this.cGrid.onFilterChanged=this.disableButtons.bind(this),this.cGrid.onSortChanged=this.disableButtons.bind(this),this.dateFormat=a.x.call("eval","dateFormat"),this.testDate=a.x.call("eval","testDate"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.menuAccessIdParent=a.x.call("eval","menuAccessIdParent"),1==this.menuAccessIdParent&&(this.addButton.enabled=!1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1),this.actionPath="corporateAccount.do?",this.actionMethod="method=display",this.actionMethod=this.actionMethod+"&selectedDate="+a.x.call("eval","selectedDate")+"&selectedEntityId="+a.x.call("eval","selectedEntityId")+"&selectedEntityName="+a.x.call("eval","selectedEntityName"),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.cGrid.onRowClick=function(e){t.obtainCell(e)}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this.swtAlert.error(t.fault.faultstring+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail)},e.prototype.inputDataResult=function(t){if(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.lostConnectionText.visible=!1,this.jsonReader.getRequestReplyStatus()&&this.lastReceivedJSON!=this.prevReceivedJSON)if(this.valueDate.text=this.jsonReader.getScreenAttributes().valueDate,this.entityId.text=this.jsonReader.getScreenAttributes().selectedEntityId,this.entityName.text=this.jsonReader.getScreenAttributes().selectedEntityName,this.jsonReader.isDataBuilding())this.dataBuildingText.visible=!0;else{this.dataBuildingText.visible=!1;var e={columns:this.jsonReader.getColumnData()};this.cGrid.CustomGrid(e),this.cGrid.gridData=this.jsonReader.getGridData(),this.cGrid.setRowSize=this.jsonReader.getRowSize()}},e.prototype.retrieveAfterSaveInputDataResult=function(t){this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.lostConnectionText.visible=!1,this.jsonReader.getRequestReplyStatus()?this.lastReceivedJSON!=this.prevReceivedJSON&&(this.valueDate.text=this.jsonReader.getScreenAttributes().valueDate,this.jsonReader.isDataBuilding()?this.dataBuildingText.visible=!0:(this.dataBuildingText.visible=!1,this.cGrid.gridData=this.jsonReader.getGridData(),this.cGrid.setRowSize=this.jsonReader.getRowSize(),this.cGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1)):"errors.DataIntegrityViolationExceptioninAdd"==this.jsonReader.getRequestReplyMessage()&&this.swtAlert.warning(a.x.call("getBundle","text","label-recordExists","Record already exists"),a.x.call("getBundle","text","alert-warning","Warning"))},e.prototype.retrieveAfterSaveInputDataFault=function(t){this.lostConnectionText.visible=!0},e.prototype.addCropName=function(t){this.win=a.Eb.createPopUp(this,s.a,{title:a.x.call("getBundle","text","label-addCorporateEntries","Add Corporate Entries")}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="500",this.win.height="170",this.win.showControls=!0,this.win.display(),this.saveFlag=!0},e.prototype.updateCropName=function(t){this.win=a.Eb.createPopUp(this,s.a,{title:a.x.call("getBundle","text","label-changeCorporateEntries","Change Corporate Entries"),corpName:this.cGrid.selectedItem.name.content,amount:this.cGrid.selectedItem.amount.content}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="500",this.win.height="190",this.win.showControls=!0,this.win.display(),this.saveFlag=!1,this.corpSeqNo=this.cGrid.selectedItem.name.id},e.prototype.closeHandler=function(t){a.x.call("CallBackApp"),a.x.call("close")},e.prototype.deleteCropName=function(t){this.swtAlert.question(a.x.call("getBundle","text","label-wantToDelete","Are you sure you want to delete"),a.x.call("getBundle","text","alert-delete","Delete"),a.c.OK|a.c.CANCEL,null,this.alertListener.bind(this))},e.prototype.alertListener=function(t){var e=this;t.detail==a.c.OK&&this.cGrid.selectedIndex>=0&&(this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.retrieveAfterSaveInputDataResult(t)},this.inputData.cbFault=this.retrieveAfterSaveInputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="corporateAccount.do?",this.actionMethod="method=deleteCorporateAccountDetails",this.requestParams.selectedDate=this.valueDate.text,this.requestParams.corpName=this.cGrid.selectedItem.name.content,this.requestParams.amount=this.cGrid.selectedItem.amount.content,this.requestParams.selectedEntityId=this.entityId.text,this.requestParams.selectedEntityName=this.entityName.text,this.requestParams.corporateSeqNo=this.cGrid.selectedItem.name.id,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},e.prototype.saveParent=function(t,e){var i=this;this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){i.retrieveAfterSaveInputDataResult(t)},this.inputData.cbFault=this.retrieveAfterSaveInputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="corporateAccount.do?",this.actionMethod="method=saveCorporateAccountDetails",this.requestParams.selectedDate=this.valueDate.text,this.requestParams.corpName=t,this.requestParams.amount=e,this.requestParams.selectedEntityId=this.entityId.text,this.requestParams.selectedEntityName=this.entityName.text,this.requestParams.editFlag=this.saveFlag,this.saveFlag||(this.requestParams.corporateSeqNo=this.corpSeqNo),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.obtainCell=function(t){this.cGrid.selectedIndex>=0?(this.changeButton.enabled=!0,this.deleteButton.enabled=!0):(this.changeButton.enabled=!1,this.deleteButton.enabled=!1)},e.prototype.disableButtons=function(){-1==this.cGrid.selectedIndex&&(this.changeButton.enabled=!1,this.deleteButton.enabled=!1)},e}(a.yb),u=[{path:"",component:d}],r=(o.l.forChild(u),function(){return function(){}}()),c=i("pMnS"),h=i("RChO"),b=i("t6HQ"),p=i("WFGK"),g=i("5FqG"),m=i("Ip0R"),R=i("gIcY"),w=i("t/Na"),f=i("sE5F"),C=i("OzfB"),v=i("T7CS"),x=i("S7LP"),D=i("6aHO"),I=i("WzUx"),S=i("A7o+"),y=i("zCE2"),B=i("Jg5P"),N=i("3R0m"),T=i("hhbb"),A=i("5rxC"),k=i("Fzqc"),O=i("21Lb"),L=i("hUWP"),E=i("3pJQ"),J=i("V9q+"),P=i("VDKW"),G=i("kXfT"),q=i("BGbe");i.d(e,"CorporateAccountModuleNgFactory",function(){return M}),i.d(e,"RenderType_CorporateAccount",function(){return _}),i.d(e,"View_CorporateAccount_0",function(){return j}),i.d(e,"View_CorporateAccount_Host_0",function(){return U}),i.d(e,"CorporateAccountNgFactory",function(){return z});var M=n.Gb(r,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[c.a,h.a,b.a,p.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,z]],[3,n.n],n.J]),n.Rb(4608,m.m,m.l,[n.F,[2,m.u]]),n.Rb(4608,R.c,R.c,[]),n.Rb(4608,R.p,R.p,[]),n.Rb(4608,w.j,w.p,[m.c,n.O,w.n]),n.Rb(4608,w.q,w.q,[w.j,w.o]),n.Rb(5120,w.a,function(t){return[t,new a.tb]},[w.q]),n.Rb(4608,w.m,w.m,[]),n.Rb(6144,w.k,null,[w.m]),n.Rb(4608,w.i,w.i,[w.k]),n.Rb(6144,w.b,null,[w.i]),n.Rb(4608,w.f,w.l,[w.b,n.B]),n.Rb(4608,w.c,w.c,[w.f]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.g,f.b,[]),n.Rb(5120,f.i,f.j,[]),n.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),n.Rb(4608,f.f,f.a,[]),n.Rb(5120,f.d,f.k,[f.h,f.f]),n.Rb(5120,n.b,function(t,e){return[C.j(t,e)]},[m.c,n.O]),n.Rb(4608,v.a,v.a,[]),n.Rb(4608,x.a,x.a,[]),n.Rb(4608,D.a,D.a,[n.n,n.L,n.B,x.a,n.g]),n.Rb(4608,I.c,I.c,[n.n,n.g,n.B]),n.Rb(4608,I.e,I.e,[I.c]),n.Rb(4608,S.l,S.l,[]),n.Rb(4608,S.h,S.g,[]),n.Rb(4608,S.c,S.f,[]),n.Rb(4608,S.j,S.d,[]),n.Rb(4608,S.b,S.a,[]),n.Rb(4608,S.k,S.k,[S.l,S.h,S.c,S.j,S.b,S.m,S.n]),n.Rb(4608,I.i,I.i,[[2,S.k]]),n.Rb(4608,I.r,I.r,[I.L,[2,S.k],I.i]),n.Rb(4608,I.t,I.t,[]),n.Rb(4608,I.w,I.w,[]),n.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),n.Rb(1073742336,m.b,m.b,[]),n.Rb(1073742336,R.n,R.n,[]),n.Rb(1073742336,R.l,R.l,[]),n.Rb(1073742336,y.a,y.a,[]),n.Rb(1073742336,B.a,B.a,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,N.a,N.a,[]),n.Rb(1073742336,S.i,S.i,[]),n.Rb(1073742336,I.b,I.b,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,w.d,w.d,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,T.b,T.b,[]),n.Rb(1073742336,A.b,A.b,[]),n.Rb(1073742336,C.c,C.c,[]),n.Rb(1073742336,k.a,k.a,[]),n.Rb(1073742336,O.d,O.d,[]),n.Rb(1073742336,L.c,L.c,[]),n.Rb(1073742336,E.a,E.a,[]),n.Rb(1073742336,J.a,J.a,[[2,C.g],n.O]),n.Rb(1073742336,P.b,P.b,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,q.b,q.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,r,r,[]),n.Rb(256,w.n,"XSRF-TOKEN",[]),n.Rb(256,w.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,S.m,void 0,[]),n.Rb(256,S.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,o.i,function(){return[[{path:"",component:d}]]},[])])}),F=[[""]],_=n.Hb({encapsulation:0,styles:F,data:{}});function j(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{lblEntity:0}),n.Zb(402653184,3,{entityId:0}),n.Zb(402653184,4,{entityName:0}),n.Zb(402653184,5,{valueDate:0}),n.Zb(402653184,6,{lblDate:0}),n.Zb(402653184,7,{dataBuildingText:0}),n.Zb(402653184,8,{lostConnectionText:0}),n.Zb(402653184,9,{corpAccContainer:0}),n.Zb(402653184,10,{addButton:0}),n.Zb(402653184,11,{changeButton:0}),n.Zb(402653184,12,{deleteButton:0}),n.Zb(402653184,13,{closeButton:0}),n.Zb(402653184,14,{loadingImage:0}),(t()(),n.Jb(14,0,null,null,47,"SwtModule",[["height","450"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,l=t.component;"creationComplete"===e&&(n=!1!==l.onLoad()&&n);return n},g.ad,g.hb)),n.Ib(15,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(16,0,null,0,45,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(17,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(18,0,null,0,17,"SwtCanvas",[["height","14%"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(19,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(20,0,null,0,15,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(21,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(22,0,null,0,7,"HBox",[["height","50%"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(23,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(24,0,null,0,1,"SwtLabel",[["text","Entity"],["width","100"]],null,null,null,g.Yc,g.fb)),n.Ib(25,4440064,[[2,4],["lblEntity",4]],0,a.vb,[n.r,a.i],{width:[0,"width"],text:[1,"text"]},null),(t()(),n.Jb(26,0,null,0,1,"SwtTextInput",[["editable","false"],["width","120"]],null,null,null,g.kd,g.sb)),n.Ib(27,4440064,[[3,4],["entityId",4]],0,a.Rb,[n.r,a.i],{width:[0,"width"],editable:[1,"editable"]},null),(t()(),n.Jb(28,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,g.Yc,g.fb)),n.Ib(29,4440064,[[4,4],["entityName",4]],0,a.vb,[n.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),n.Jb(30,0,null,0,5,"HBox",[["height","50%"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(31,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(32,0,null,0,1,"SwtLabel",[["text","Value Date"],["width","100"]],null,null,null,g.Yc,g.fb)),n.Ib(33,4440064,[[6,4],["lblDate",4]],0,a.vb,[n.r,a.i],{width:[0,"width"],text:[1,"text"]},null),(t()(),n.Jb(34,0,null,0,1,"SwtTextInput",[["editable","false"],["width","120"]],null,null,null,g.kd,g.sb)),n.Ib(35,4440064,[[5,4],["valueDate",4]],0,a.Rb,[n.r,a.i],{width:[0,"width"],editable:[1,"editable"]},null),(t()(),n.Jb(36,0,null,0,1,"SwtCanvas",[["height","67%"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(37,4440064,[[9,4],["corpAccContainer",4]],0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(38,0,null,0,23,"SwtCanvas",[["height","7%"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(39,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(40,0,null,0,21,"HBox",[],null,null,null,g.Dc,g.K)),n.Ib(41,4440064,null,0,a.C,[n.r,a.i],null,null),(t()(),n.Jb(42,0,null,0,9,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(43,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(44,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","addButton"],["label","Add"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.addCropName(i)&&n);return n},g.Mc,g.T)),n.Ib(45,4440064,[[10,4],["addButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(46,0,null,0,1,"SwtButton",[["buttonMode","true"],["enabled","false"],["id","changeButton"],["label","Change"],["toolTip",""],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.updateCropName(i)&&n);return n},g.Mc,g.T)),n.Ib(47,4440064,[[11,4],["changeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],enabled:[3,"enabled"],label:[4,"label"],buttonMode:[5,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(48,0,null,0,1,"SwtButton",[["buttonMode","true"],["enabled","false"],["id","deleteButton"],["label","Delete"],["toolTip",""],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.deleteCropName(i)&&n);return n},g.Mc,g.T)),n.Ib(49,4440064,[[12,4],["deleteButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],enabled:[3,"enabled"],label:[4,"label"],buttonMode:[5,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(50,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","closeButton"],["label","Close"],["toolTip",""],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.closeHandler(i)&&n);return n},g.Mc,g.T)),n.Ib(51,4440064,[[13,4],["closeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],label:[3,"label"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(52,0,null,0,5,"HBox",[["paddingTop","8"]],null,null,null,g.Dc,g.K)),n.Ib(53,4440064,null,0,a.C,[n.r,a.i],{paddingTop:[0,"paddingTop"]},null),(t()(),n.Jb(54,0,null,0,1,"SwtLabel",[["color","red"],["text","DATA BUILD IN PORGRESS"],["visible","false"]],null,null,null,g.Yc,g.fb)),n.Ib(55,4440064,[[7,4],["dataBuildingText",4]],0,a.vb,[n.r,a.i],{visible:[0,"visible"],text:[1,"text"],color:[2,"color"]},null),(t()(),n.Jb(56,0,null,0,1,"SwtLabel",[["color","red"],["text","CONNECTION ERROR"],["visible","false"]],null,null,null,g.Yc,g.fb)),n.Ib(57,4440064,[[8,4],["lostConnectionText",4]],0,a.vb,[n.r,a.i],{visible:[0,"visible"],text:[1,"text"],color:[2,"color"]},null),(t()(),n.Jb(58,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingRight","5"]],null,null,null,g.Dc,g.K)),n.Ib(59,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),n.Jb(60,0,null,0,1,"SwtLoadingImage",[],null,null,null,g.Zc,g.gb)),n.Ib(61,114688,[[14,4],["loadingImage",4]],0,a.xb,[n.r],null,null)],function(t,e){t(e,15,0,"100%","450");t(e,17,0,"100%","100%","5","5","5","5");t(e,19,0,"100%","14%");t(e,21,0,"100%","100%");t(e,23,0,"100%","50%");t(e,25,0,"100","Entity");t(e,27,0,"120","false");t(e,29,0,"normal");t(e,31,0,"100%","50%");t(e,33,0,"100","Value Date");t(e,35,0,"120","false");t(e,37,0,"100%","67%");t(e,39,0,"100%","7%"),t(e,41,0);t(e,43,0,"100%","5");t(e,45,0,"addButton","70","Add","true");t(e,47,0,"changeButton","","70","false","Change","true");t(e,49,0,"deleteButton","","70","false","Delete","true");t(e,51,0,"closeButton","","70","Close","true");t(e,53,0,"8");t(e,55,0,"false","DATA BUILD IN PORGRESS","red");t(e,57,0,"false","CONNECTION ERROR","red");t(e,59,0,"right","5"),t(e,61,0)},null)}function U(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-corporate-account",[],null,null,null,j,_)),n.Ib(1,4440064,null,0,d,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var z=n.Fb("app-corporate-account",d,U,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);