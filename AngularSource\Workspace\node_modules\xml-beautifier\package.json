{"_from": "xml-beautifier@0.4.1", "_id": "xml-beautifier@0.4.1", "_inBundle": false, "_integrity": "sha512-IYPO6MLkrc5z+69iNuuU3+E6x1rxKaAKWKTYKyjzAN9AwmSivgs4rzZDzpoZMK5KVWz0YH+tdG+NIyHbwUSrdw==", "_location": "/xml-beautifier", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "xml-beautifier@0.4.1", "name": "xml-beautifier", "escapedName": "xml-beautifier", "rawSpec": "0.4.1", "saveSpec": null, "fetchSpec": "0.4.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/xml-beautifier/-/xml-beautifier-0.4.1.tgz", "_shasum": "c48665c4ef7abdb4f9cee6f11cde2880b6c5bba0", "_spec": "xml-beautifier@0.4.1", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/jonathanp/xml-beautifier/issues"}, "bundleDependencies": false, "dependencies": {"repeat-string": "1.6.1"}, "deprecated": false, "description": "Beautifies XML documents by putting each tag and text node on their own line and correctly indents everything", "devDependencies": {"babel-cli": "6.26.0", "babel-preset-env": "1.7.0", "tape": "4.10.2"}, "homepage": "https://github.com/jonathanp/xml-beautifier#readme", "license": "MIT", "main": "dist/index.js", "name": "xml-beautifier", "repository": {"type": "git", "url": "git+https://github.com/jonathanp/xml-beautifier.git"}, "scripts": {"build": "babel src --out-dir dist --presets env", "prepare": "npm run build", "test": "npm run build && node dist/test/test.js"}, "title": "XML Beautifier", "version": "0.4.1"}