{"_from": "ngx-monaco-editor@8.1.1", "_id": "ngx-monaco-editor@8.1.1", "_inBundle": false, "_integrity": "sha512-SmUGFG5m2HHWybHD6qXdB1FrtC/XySSHm5O/E1cDGW7moIWzJGqiitCKLJdSh9D2hsoe8oBNEg74vYF1UGznsQ==", "_location": "/ngx-monaco-editor", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "ngx-monaco-editor@8.1.1", "name": "ngx-monaco-editor", "escapedName": "ngx-monaco-editor", "rawSpec": "8.1.1", "saveSpec": null, "fetchSpec": "8.1.1"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/ngx-monaco-editor/-/ngx-monaco-editor-8.1.1.tgz", "_shasum": "5998e7f42b4f39c5959fe61200795cb4a8214286", "_spec": "ngx-monaco-editor@8.1.1", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "<PERSON><PERSON>"}, "bugs": {"url": "https://github.com/atularen/ngx-monaco-editor/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>"}], "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "description": "Monaco Code Editor for Angular", "es2015": "fesm2015/ngx-monaco-editor.js", "esm2015": "esm2015/ngx-monaco-editor.js", "esm5": "esm5/ngx-monaco-editor.js", "fesm2015": "fesm2015/ngx-monaco-editor.js", "fesm5": "fesm5/ngx-monaco-editor.js", "homepage": "https://github.com/atularen/ngx-monaco-editor#readme", "keywords": ["angular", "angular 2+", "angular 2", "angular 4", "angular 5", "angular 6", "angular 7", "angular 8", "monaco", "monaco-editor", "code", "editor", "reusable", "component"], "license": "MIT", "main": "bundles/ngx-monaco-editor.umd.js", "metadata": "ngx-monaco-editor.metadata.json", "module": "fesm5/ngx-monaco-editor.js", "name": "ngx-monaco-editor", "peerDependencies": {"@angular/common": ">= 2.0.0", "@angular/core": ">= 2.0.0"}, "private": false, "repository": {"type": "git", "url": "git+https://github.com/atularen/ngx-monaco-editor.git"}, "sideEffects": false, "typings": "ngx-monaco-editor.d.ts", "version": "8.1.1"}