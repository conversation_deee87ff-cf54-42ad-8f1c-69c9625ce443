(window.webpackJsonp=window.webpackJsonp||[]).push([[39],{Uxr2:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),l=i("mrSG"),o=i("447K"),a=(i("tp8m"),i("ZYCi")),s=i("wd/R"),r=i.n(s),c=i("BfSf"),d=i("6blF"),u=(i("0GgQ"),i("ik3b")),h=(i("EVdn"),function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.selected="pdf",n.callFrom="CM",n.inputData=new o.G(n.commonService),n.alertingData=new o.G(n.commonService),n.ordertData=new o.G(n.commonService),n.widthData=new o.G(n.commonService),n.updateFontSize=new o.G(n.commonService),n.updateRefreshRate=new o.G(n.commonService),n.logicUpdate=new o.G(n.commonService),n.actionMethod="",n.jsonReader=new o.L,n.comboOpen=!1,n.comboChange=!1,n.entityComboChange=!1,n.baseURL=o.Wb.getBaseURL(),n.fontSize=null,n.selectedSort=null,n.arrayOftabs=[],n.refreshRate=10,n.autoRefresh=!1,n.selectedRowData=null,n.invalidComms=null,n.dateValue=!0,n.currencyDay="",n.fontValue="",n.fontLabel="",n.fontRequest="",n.acctSelectedIndex=-1,n.indexSelectedPay=-1,n.versionNumber="1.1.0043",n.screenVersion=new o.V(n.commonService),n.today="Today",n.columnDefinitionsTempArray=[],n.columnsNewWidths="",n.tooltipEntityId=null,n.tooltipCurrencyCode=null,n.tooltipSelectedAccount=null,n.tooltipFacilityId=null,n.tooltipSelectedDate=null,n.tooltipOtherParams=[],n.selectedNodeId=null,n.treeLevelValue=null,n.lastSelectedTooltipParams=null,n.eventsCreated=!1,n.customTooltip=null,n.swtAlert=new o.bb(e),n}return l.d(e,t),e.prototype.resizeGrids=function(t){try{this.totalGrid.setRefreshColumnWidths(this.accountGrid.gridObj.getColumns())}catch(e){console.log("resizeGrids",e)}},e.prototype.ngOnInit=function(){var t=this;instanceElement=this,this.accountGrid=this.gridCanvas.addChild(o.hb),this.totalGrid=this.totalsContainer.addChild(o.Ub),this.totalGrid.initialColumnsToSkip=4,this.accountGrid.lockedColumnCount=4,this.totalGrid.lockedColumnCount=1,this.totalGrid.selectable=!1,this.accountGrid.columnWidthChanged.subscribe(function(e){t.resizeGrids(e)}),this.accountGrid.columnOrderChanged.subscribe(function(e){t.resizeGrids(e)}),this.accountGrid.onFilterChanged=this.disableButtons.bind(this),this.accountGrid.listenHorizontalScrollEvent=!0,this.totalGrid.fireHorizontalScrollEvent=!0,this.accountGrid.hideHorizontalScrollBar=!0,this.entityLabel.text=o.x.call("getBundle","text","entity","Entity"),this.entityCombo.toolTip=o.x.call("getBundle","tip","entity","Select an Entity ID"),this.currencyLabel.text=o.x.call("getBundle","text","currency","Currency"),this.ccyCombo.toolTip=o.x.call("getBundle","tip","currency","Select currency code"),this.accountTypeLabel.text=o.x.call("getBundle","text","accounttype","Account Type"),this.typeCombo.toolTip=o.x.call("getBundle","tip","accounttype","Select account type"),this.accoutClassLabel.text=o.x.call("getBundle","text","accountclass","Account Class"),this.classCombo.toolTip=o.x.call("getBundle","tip","accountclass","Select account class"),this.dateLabel.text=o.x.call("getBundle","text","date","Date"),this.startDate.toolTip=this.hideZeroLabel.text=o.x.call("getBundle","text","hidezero","Hide Zero Balances"),this.hideZero.toolTip=o.x.call("getBundle","tip","hidezero","Hide Zero Balances"),this.applyCurrencyLabel.text=o.x.call("getBundle","text","threshold","Apply Currency Threshold"),this.currencyThreshold.toolTip=o.x.call("getBundle","tip","threshold","Apply Currency Threshold"),this.refreshButton.label=o.x.call("getBundle","text","button-refresh","Refresh window"),this.refreshButton.toolTip=o.x.call("getBundle","tip","button-refresh","Refresh"),this.optionsButton.label=o.x.call("getBundle","text","button-options","Options"),this.optionsButton.toolTip=o.x.call("getBundle","tip","button-options","Change Options"),this.sumButton.label=o.x.call("getBundle","text","button-sum","Sum"),this.sumButton.toolTip=o.x.call("getBundle","tip","button-sum","Manipulate Sum flag"),this.manswpButton.label=o.x.call("getBundle","text","button-manswp","ManSwp"),this.manswpButton.toolTip=o.x.call("getBundle","tip","button-manswp","Manual sweep"),this.closeButton.label=o.x.call("getBundle","text","button-close","Close"),this.closeButton.toolTip=o.x.call("getBundle","tip","button-close","Close window"),this.dataBuildingText.text=o.x.call("getBundle","text","label-buildInProgress","DATA BUILD IN PORGRESS"),this.lostConnectionText.text=o.x.call("getBundle","text","label-connectionError","CONNECTION ERROR"),this.lastRefText.text=o.x.call("getBundle","text","label-lastRefresh","Last Refresh:"),this.sodLabel.text=o.x.call("getBundle","text","sod","Start of day balance"),this.sodText.toolTip=o.x.call("getBundle","tip","sod","Start of day balance"),this.openLabel.text=o.x.call("getBundle","text","openunexpected","Open Unexpected"),this.unexpectedText.toolTip=o.x.call("getBundle","tip","openunexpected","Afficher \xe9quilibre inattendu ouvert"),this.breakLabel.text=o.x.call("getBundle","text","breakdownlegend","Breakdown"),this.accountRadio.label=o.x.call("getBundle","text","accountbreakdown","Account Breakdown"),this.accountRadio.toolTip=o.x.call("getBundle","tip","accountbreakdown","Refresh Account Breakdown Grid"),this.movementRadio.label=o.x.call("getBundle","text","movement","Movement"),this.movementRadio.toolTip=o.x.call("getBundle","tip","movement","Select Movement to view movement summary display")},e.ngOnDestroy=function(){instanceElement=null},e.prototype.onLoad=function(){var t=this;this.initializeMenus(),this.accountGrid.extraHTMLContentFunction=this.gridsExtraContetnItemRender.bind(this),this.testDate=o.x.call("eval","dbDate"),this.callFrom=o.x.call("eval","callFrom"),this.menuAccessId=o.x.call("eval","menuAccessId"),"CM"!=this.callFrom&&(this.callFrom="AM"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.requestParams=[],this.d=this.getUrlParams(),this.d.entityId&&(this.requestParams["accountMonitorNew.dateAsString"]=this.d.valueDate,this.requestParams["accountMonitorNew.entityId"]=this.d.entityId,this.requestParams["accountMonitorNew.currencyCode"]=this.d.currencyCode,this.requestParams["accountMonitorNew.accountType"]="All",this.requestParams["accountMonitorNew.accountClass"]="All",this.actionMethod="method=displayFilteredDetails"),this.actionMethod=""==this.actionMethod?"method=unspecified":this.actionMethod,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="acctmonitornew.do?",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.callFrom=this.callFrom,this.requestParams.menuAccessId=this.menuAccessId,this.inputData.send(this.requestParams,null),this.accountGrid.ITEM_CLICK.subscribe(function(e){t.itemClickFunction(e),t.cellLogic(e)}),d.a.fromEvent(document.body,"click").subscribe(function(e){t.positionX=e.clientX,t.positionY=e.clientY}),o.v.subscribe(function(e){t.report(e)})},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,"Account Monitor Screen",this.versionNumber,"");var t=new o.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=o.Eb.createPopUp(this,o.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.isModal=!0,this.showJSONPopup.display()},e.prototype.getUrlParams=function(){var t=o.x.call("document_location_href").split("?")[1],e={};if(t)for(var i=(t=t.split("#")[0]).split("&"),n=0;n<i.length;n++){var l=i[n].split("="),a=l[0],s=void 0===l[1]||l[1];if(a.match(/\[(\d+)?\]$/)){var r=a.replace(/\[(\d+)?\]/,"");if(e[r]||(e[r]=[]),a.match(/\[\d+\]$/)){var c=/\[(\d+)\]/.exec(a)[1];e[r][c]=s}else e[r].push(s)}else e[a]?e[a]&&"string"==typeof e[a]?(e[a]=[e[a]],e[a].push(s)):e[a].push(s):e[a]=s}return e},e.prototype.startOfComms=function(){this.loader.setVisible(!0),this.disableInterface(),this.startDate.enabled=!1},e.prototype.endOfComms=function(){this.loader.setVisible(!1),this.enableInterface(),this.startDate.enabled=!0},e.prototype.disableInterface=function(){this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1},e.prototype.enableInterface=function(){this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0},e.prototype.inputDataResult=function(t){var e=this;if(this.inputData.isBusy())this.inputData.cbStop();else{this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.lostConnectionText.visible=!1;var i=this.jsonReader.getScreenAttributes().lastRefTime;this.currencyPattern=this.jsonReader.getScreenAttributes().currencyformat;var n=i.replace(/\\u0028/g,"(").replace(/\\u0029/g,")");if(this.fontLabel=this.jsonReader.getScreenAttributes().currfontsize,this.lastRefTime.text=n,this.lastRecievedJSON!=this.prevRecievedJSON&&this.jsonReader.getRequestReplyStatus()&&!this.jsonReader.isDataBuilding()){if(this.sysDateFrmSession=this.jsonReader.getScreenAttributes().sysDateFrmSession,this.manualSweepAccessId=this.jsonReader.getScreenAttributes().manualSweepAccessId,this.dateFormat=this.startDate.formatString=this.jsonReader.getScreenAttributes().dateformat,this.fillComboDataAndText(),""!=this.lastRecievedJSON.accountmonitor.tabs){if(0==this.arrayOftabs.length){if(this.tabs.getTabChildren().length>0)for(var l=0;l<this.arrayOftabs.length;l++)this.tabs.removeChild(this.arrayOftabs[l]);this.displayContainerToday=this.tabs.addChild(o.Xb),this.displayContainerTodayPlus=this.tabs.addChild(o.Xb),this.displayContainerTodayPlusPlus=this.tabs.addChild(o.Xb),this.displayContainerTodayPlusThree=this.tabs.addChild(o.Xb),this.displayContainerTodayPlusFour=this.tabs.addChild(o.Xb),this.displayContainerTodayPlusFive=this.tabs.addChild(o.Xb),this.displayContainerTodayPlusSix=this.tabs.addChild(o.Xb),this.displayContainerSelected=this.tabs.addChild(o.Xb),this.arrayOftabs=[],this.arrayOftabs=[this.displayContainerToday,this.displayContainerTodayPlus,this.displayContainerTodayPlusPlus,this.displayContainerTodayPlusThree,this.displayContainerTodayPlusFour,this.displayContainerTodayPlusFive,this.displayContainerTodayPlusSix,this.displayContainerSelected]}for(l=0;l<this.lastRecievedJSON.accountmonitor.tabs.predictdate.length;l++)this.arrayOftabs[l].label=this.lastRecievedJSON.accountmonitor.tabs.predictdate[l].dateLabel,this.arrayOftabs[l].businessday=this.lastRecievedJSON.accountmonitor.tabs.predictdate[l].businessday,0==this.arrayOftabs[l].businessday?this.arrayOftabs[l].setTabHeaderStyle("color","darkgray"):this.arrayOftabs[l].setTabHeaderStyle("color","black");this.arrayOftabs[7].label="Selected"}if(this.handleDate(),o.Z.isTrue(this.jsonReader.getScreenAttributes().databuilding))this.dataBuildingText.visible=!0;else{this.dataBuildingText.visible=!1,this.accountGrid.currencyFormat=this.currencyPattern;var a={columns:this.jsonReader.getColumnData()};this.accountGrid.CustomGrid(a),this.jsonReader.getGridData().size>0?(this.accountGrid.gridData=this.jsonReader.getGridData(),this.accountGrid.setRowSize=this.jsonReader.getRowSize(),this.dataExport.enabled=!0):(this.accountGrid.gridData={row:{},size:0},this.dataExport.enabled=!1);for(l=0;l<this.accountGrid.columnDefinitions.length;l++){var s=this.accountGrid.columnDefinitions[l];if("alerting"==s.field){var r="./"+o.x.call("eval","alertOrangeImage"),c="./"+o.x.call("eval","alertRedImage");"Normal"==this.fontLabel?s.properties={enabled:!1,columnName:"alerting",imageEnabled:r,imageCritEnabled:c,imageDisabled:"",_toolTipFlag:!0,style:" display: block; margin-left: auto; margin-right: auto;"}:s.properties={enabled:!1,columnName:"alerting",imageEnabled:r,imageCritEnabled:c,imageDisabled:"",_toolTipFlag:!0,style:"height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;"},this.accountGrid.columnDefinitions[l].editor=null,this.accountGrid.columnDefinitions[l].formatter=u.a}}this.accountGrid.colWidthURL(this.baseURL+"acctmonitornew.do?"),this.accountGrid.colOrderURL(this.baseURL+"acctmonitornew.do?"),this.accountGrid.entityID=this.entityCombo.selectedLabel,this.accountGrid.saveWidths=!0,this.accountGrid.saveColumnOrder=!0,null!=this.acctSelectedIndex?(this.indexSelectedPay=this.accountGrid.gridData.findIndex(function(t){return t.account==e.acctSelectedIndex}),this.indexSelectedPay>=0?this.accountGrid.selectedIndex=this.indexSelectedPay:this.disableButtons()):this.accountGrid.selectedIndex=-1,this.totalGrid.CustomGrid(a),this.totalGrid.gridData=this.jsonReader.getTotalsData(),this.refreshRate=this.jsonReader.getScreenAttributes().refresh,"Normal"==this.fontLabel?(this.selectedFont=0,this.accountGrid.rowHeight=18):(this.accountGrid.rowHeight=15,this.selectedFont=1),this.accountGrid.selectedIndex>-1&&"0"==this.menuEntityCurrGrpAccess?("C"==this.accountGrid.dataset[this.accountGrid.selectedIndex].sum&&"false"==this.accountGrid.dataset[this.accountGrid.selectedIndex].slickgrid_rowcontent.sum.summable||"C"==this.accountGrid.dataset[this.accountGrid.selectedIndex].accountStatus?(this.sumButton.enabled=!1,this.sumButton.buttonMode=!1):(this.sumButton.enabled=!0,this.sumButton.buttonMode=!0),"0"==this.menuAccessId&&"2"!=this.manualSweepAccessId&&(this.manswpButton.enabled=!0,this.manswpButton.buttonMode=!0)):(this.sumButton.enabled=!1,this.sumButton.buttonMode=!1,this.manswpButton.enabled=!1,this.manswpButton.buttonMode=!1);var d=this.refreshRate;clearInterval(this.interval),this.interval=setInterval(function(){e.dataRefresh()},1e3*d),this.accountGrid.rowColorFunction=function(t,i,n,l){return e.drawRowBackground(t,i,n,l)},1==this.entityComboChange?this.entityComboChange=!1:this.prevRecievedJSON=this.lastRecievedJSON}}this.prevRecievedJSON=this.lastRecievedJSON}},e.prototype.disableButtons=function(){-1==this.accountGrid.selectedIndex&&(this.sumButton.enabled=!1,this.sumButton.buttonMode=!1,this.manswpButton.enabled=!1,this.manswpButton.buttonMode=!1,this.sumButton.toolTip="",this.manswpButton.toolTip="")},e.prototype.drawRowBackground=function(t,e,i,n){var l;try{var o;if("D"==(o=t.slickgrid_rowcontent&&t.slickgrid_rowcontent.colorflag?t.slickgrid_rowcontent.colorflag.content:""))l="#AAAAAA";else if("L"==o)l="#DDDDDD";else if("predicted"===n){var a=t.slickgrid_rowcontent&&t.slickgrid_rowcontent.includeLoroInPredictedColor?t.slickgrid_rowcontent.includeLoroInPredictedColor.content:"";""!=a&&(l=a)}else if("lorocurr"===n){var s=t.slickgrid_rowcontent&&t.slickgrid_rowcontent.includePredictedInLoroColor?t.slickgrid_rowcontent.includePredictedInLoroColor.content:"";""!=s&&(l=s)}}catch(r){console.log("error drawRowBackground ",r)}return l},e.prototype.gridsExtraContetnItemRender=function(t,e,i,n){try{return"predicted"===e?"<span style='position:absolute;left:2px;top:0px; width:18px;'>"+(null!=t.includeLoroInPredictedIndicator&&""!=t.includeLoroInPredictedIndicator?t.includeLoroInPredictedIndicator:"&nbsp")+"</span>":"lorocurr"===e?"<span style='position:absolute;left:2px;top:0px; width:18px;'>"+(null!=t.includePredictedInLoroIndicator&&""!=t.includePredictedInLoroIndicator?t.includePredictedInLoroIndicator:"&nbsp")+"</span>":""}catch(l){console.log(l)}return i},e.prototype.fillComboDataAndText=function(){try{this.entityCombo.setComboData(this.jsonReader.getSelects(),!1),this.ccyCombo.setComboData(this.jsonReader.getSelects(),!1),this.typeCombo.setComboData(this.jsonReader.getSelects(),!0),this.classCombo.setComboData(this.jsonReader.getSelects(),!0),this.selectedEntity.text=this.entityCombo.selectedItem.value,this.selectedCcy.text=this.ccyCombo.selectedItem.value,this.startDate.text=this.jsonReader.getScreenAttributes().datefrom,""==o.Z.trim(this.selectedCcy.text)&&(null!=this.autoRefresh&&clearInterval(this.interval),this.SwtAlert.error("Invalid: your role does not specify access to currencies/groups for this entity")),this.currencyThreshold.selected=this.jsonReader.getScreenAttributes().currencythreshold,this.hideZero.selected=this.jsonReader.getScreenAttributes().hidezerobalances,this.dateValue=this.jsonReader.getScreenAttributes().dateComparing,this.menuEntityCurrGrpAccess=this.jsonReader.getScreenAttributes().menuentitycurrgrpaccess,this.dateValue?this.today="":this.today="Today",this.currencyDay=this.ccyCombo.selectedLabel,this.sodText.text=null!=this.jsonReader.getSingletons().sod.content?this.jsonReader.getSingletons().sod.content:"",null!=this.jsonReader.getSingletons().openunexpected.content?this.unexpectedText.text=this.jsonReader.getSingletons().openunexpected.content:this.unexpectedText.text="";var t=null!=this.jsonReader.getSingletons().scenarioAlerting?this.jsonReader.getSingletons().scenarioAlerting:"";"Y"==t?(this.alertImage.source=this.baseURL+o.x.call("eval","alertOrangeImage"),this.alertImage.toolTip="alerts Available"):"C"==t?(this.alertImage.source=this.baseURL+o.x.call("eval","alertRedImage"),this.alertImage.toolTip="alerts Available"):(this.alertImage.source=this.baseURL+o.x.call("eval","blankImage"),this.alertImage.toolTip="")}catch(e){console.log("eee fillComboDataAndText",e)}},e.prototype.cellLogic=function(t){void 0===t&&(t=null);try{var e=t.target.field,i=t.target.data.slickgrid_rowcontent[e]?t.target.data.slickgrid_rowcontent[e].clickable:"";if(this.accountGrid.selectedIndex>=0&&"0"==this.menuEntityCurrGrpAccess?(this.acctSelectedIndex=this.accountGrid.selectedItem.account.content,this.selectedRowData=t,"C"==this.selectedRowData.target.data.sum&&"false"==this.selectedRowData.target.data.slickgrid_rowcontent.sum.summable||"C"==this.selectedRowData.target.data.accountStatus?(this.sumButton.enabled=!1,this.sumButton.buttonMode=!1,this.sumButton.toolTip=""):(this.sumButton.enabled=!0,this.sumButton.buttonMode=!0,this.sumButton.toolTip="Manipulate Sum flag"),"0"==this.menuAccessId&&"2"!=this.manualSweepAccessId&&(this.manswpButton.enabled=!0,this.manswpButton.buttonMode=!0,this.manswpButton.toolTip="Manual sweep")):(this.acctSelectedIndex=null,this.sumButton.enabled=!1,this.sumButton.buttonMode=!1,this.manswpButton.enabled=!1,this.manswpButton.buttonMode=!1,this.sumButton.toolTip="",this.manswpButton.toolTip=""),i){var n=t.target.name;"lorocurr"==e&&(n="Loro"),this.accountGrid.selectedIndex>-1&&this.clickLink(this.entityCombo.selectedLabel,this.accountGrid.dataProvider[this.accountGrid.selectedIndex].ccy,this.accountGrid.dataProvider[this.accountGrid.selectedIndex].account,this.startDate.text,this.currencyThreshold.selected,this.hideZero.selected,this.breakdown.selectedValue.toString(),n)}}catch(l){console.log("error in cell click",l)}},e.prototype.imageClickFunction=function(t){var e=null!=this.jsonReader.getSingletons().scenarioAlerting?this.jsonReader.getSingletons().scenarioAlerting:"";"Y"!=e&&"C"!=e||(this.tooltipSelectedAccount=null,this.tooltipSelectedDate=null,this.tooltipCurrencyCode=this.ccyCombo.selectedLabel,this.tooltipEntityId=this.entityCombo.selectedLabel,this.tooltipFacilityId="ACCOUNT_MONITOR_SOD_BALANCE",this.tooltipOtherParams=[],this.tooltipOtherParams.currencythresholdFlag=this.currencyThreshold.selected?"Y":"N",this.createTooltip(t))},e.prototype.getParamsFromParent=function(){return{sqlParams:this.lastSelectedTooltipParams,facilityId:this.tooltipFacilityId,selectedNodeId:this.selectedNodeId,treeLevelValue:this.treeLevelValue,tooltipCurrencyCode:this.tooltipCurrencyCode,tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate,tooltipSelectedAccount:this.tooltipSelectedAccount,tooltipOtherParams:this.tooltipOtherParams}},e.prototype.createTooltip=function(t){var e=this;this.customTooltip&&this.customTooltip.close&&this.removeTooltip();try{this.customTooltip=o.Eb.createPopUp(parent,o.u,{}),this.customTooltip.enableResize=!1,null!=t&&(this.positionX=t.clientX,this.positionY=t.clientY),this.customTooltip.width="430",this.customTooltip.height="450",this.customTooltip.enableResize=!1,this.customTooltip.title="Alert Summary Tooltip",this.customTooltip.showControls=!0,window.innerHeight<this.positionY+450&&(this.positionY=200),this.customTooltip.setWindowXY(this.positionX+20,this.positionY),this.customTooltip.showHeader=!0,this.customTooltip.parentDocument=this,this.customTooltip.processBox=this,this.customTooltip.display(),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe(function(t){e.lastSelectedTooltipParams=t.noode.data,o.x.call("openAlertInstanceSummary","openAlertInstSummary",e.selectedNodeId,e.treeLevelValue)})},0),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe(function(t){e.getScenarioFacility(t.noode.data.scenario_id),e.lastSelectedTooltipParams=t.noode.data,e.hostId=t.hostId})},0),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().ITEM_CLICK.subscribe(function(t){e.selectedNodeId=t.noode.data.id,e.treeLevelValue=t.noode.data.treeLevelValue,e.customTooltip.getChild().linkToSpecificButton.enabled=!1,"ACCOUNT_MONITOR_ACCOUNT_ROW"==e.tooltipFacilityId&&1==t.noode.data.count&&0==t.noode.isBranch&&(e.customTooltip.getChild().linkToSpecificButton.enabled=!0)})},0)}catch(i){console.log("SwtCommonGrid -> createTooltip -> error",i)}},e.prototype.getScenarioFacility=function(t){var e=this;this.requestParams=[],this.alertingData.cbStart=this.startOfComms.bind(this),this.alertingData.cbStop=this.endOfComms.bind(this),this.alertingData.cbFault=this.inputDataFault.bind(this),this.alertingData.encodeURL=!1,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getScenarioFacility",this.requestParams.scenarioId=t,this.alertingData.url=this.baseURL+this.actionPath+this.actionMethod,this.alertingData.cbResult=function(t){e.openGoToScreen(t)},this.alertingData.send(this.requestParams)},e.prototype.openGoToScreen=function(t){if(t&&t.ScenarioSummary&&t.ScenarioSummary.scenarioFacility){var e=t.ScenarioSummary.scenarioFacility,i=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.entity_id?this.lastSelectedTooltipParams.entity_id:this.tooltipEntityId,n=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.currency_code?this.lastSelectedTooltipParams.currency_code:this.tooltipCurrencyCode,l=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.match_id?this.lastSelectedTooltipParams.match_id:null,a=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.movement_id?this.lastSelectedTooltipParams.movement_id:null,s=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.sweep_id?this.lastSelectedTooltipParams.sweep_id:null;o.x.call("goTo",e,this.hostId,i,l,n,a,s,"")}},e.prototype.removeTooltip=function(){null!=this.customTooltip&&this.customTooltip.close()},e.prototype.itemClickFunction=function(t){var e=this;null==t.target||null==t.target.field||"alerting"!=t.target.field||"C"!=t.target.data.alerting&&"Y"!=t.target.data.alerting?this.removeTooltip():(this.tooltipCurrencyCode=this.ccyCombo.selectedLabel,this.tooltipEntityId=this.entityCombo.selectedLabel,this.tooltipSelectedAccount=t.target.data.account,this.tooltipFacilityId="ACCOUNT_MONITOR_ACCOUNT_ROW",this.tooltipSelectedDate=this.startDate.text,this.tooltipOtherParams=[],this.tooltipOtherParams.currencythresholdFlag=this.currencyThreshold.selected?"Y":"N",setTimeout(function(){e.createTooltip(null)},100))},e.prototype.clickLink=function(t,e,i,n,l,a,s,r){o.x.call("clickLink",t,e,i,n,l.toString(),a.toString(),s,r)},e.prototype.dataRefresh=function(){try{this.comboOpen||this.updateData("yes")}catch(t){console.log(t)}},e.prototype.tabIndexchangeHandler=function(){try{var t=void 0;(t=new Date(o.j.parseDate(this.sysDateFrmSession,this.dateFormat.toUpperCase()))).setHours(12,0,0),"Selected"!=this.tabs.selectedLabel&&(this.startDate.selectedDate=new Date(t.setDate(t.getDate()+this.tabs.selectedIndex))),this.updateData("no")}catch(e){console.log("tabIndexchangeHandler error",e)}},e.prototype.closedCombo=function(t){this.comboOpen=!1},e.prototype.entityChangeCombo=function(){this.comboChange=!0,this.entityComboChange=!0,this.updateData("no")},e.prototype.updateData=function(t){var e="";this.requestParams=[],this.selectedSort=this.getSortedGridColumn(),this.entityComboChange||(e=this.ccyCombo.selectedItem.content),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.encodeURL=!1,this.actionPath="acctmonitornew.do?",this.actionMethod="method=displayFilteredDetails",this.requestParams["accountMonitorNew.dateAsString"]=this.startDate.text,this.requestParams["accountMonitorNew.entityId"]=this.entityCombo.selectedItem.content,this.requestParams["accountMonitorNew.currencyCode"]=e,this.requestParams["accountMonitorNew.accountType"]=this.typeCombo.selectedItem.value,this.requestParams["accountMonitorNew.accountClass"]=this.classCombo.selectedItem.value,this.requestParams.applyCurrencyThreshold=this.currencyThreshold.selected?"Y":"N",this.requestParams.hideZeroBalances=this.hideZero.selected?"Y":"N",this.requestParams.selectedTabIndex=this.tabs.selectedIndex,this.requestParams.autoRefresh=t,this.requestParams.systemDate=this.sysDateFrmSession,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.callFrom=this.callFrom,this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.existingEntityId=this.jsonReader.getScreenAttributes().existingEntityId,this.inputData.send(this.requestParams)},e.prototype.changeCombo=function(t){this.acctSelectedIndex=null,this.updateData("no")},e.prototype.handleDate=function(){var t=!1,e=r()(this.sysDateFrmSession,this.dateFormat.toUpperCase()),i=r()(this.startDate.text,this.dateFormat.toUpperCase());if(i)for(var n=0;n<this.tabs.getTabChildren().length-1;n++)if(e=r()(this.sysDateFrmSession,this.dateFormat.toUpperCase()).add(n,"days"),0==i.diff(e)){t=!0,this.tabs.selectedIndex=n;break}t||(this.tabs.selectedIndex=7)},e.prototype.validateDate=function(){this.validateDateField(this.startDate)&&(this.handleDate(),this.updateData("no"))},e.prototype.validateDateField=function(t){var e=this;try{var i=void 0,n=o.Wb.getPredictMessage("alert.enterValidDate",null);if(!t.text)return this.swtAlert.error(n,null,null,null,function(){e.setFocusDateField(t)}),!1;if(!(i=r()(t.text,this.dateFormat.toUpperCase(),!0)).isValid())return this.swtAlert.error(n,null,null,null,function(){e.setFocusDateField(t)}),!1;t.selectedDate=i.toDate()}catch(l){}return!0},e.prototype.setFocusDateField=function(t){t.setFocus(),t.text=this.jsonReader.getScreenAttributes().datefrom},e.prototype.doHelp=function(){o.x.call("help")},e.prototype.optionsHandler=function(){var t=this;try{clearInterval(this.interval),this.win=o.Eb.createPopUp(this,c.a,{title:"Options",fontSizeValue:this.selectedFont,refreshText:this.refreshRate.toString()}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="370",this.win.height="190",this.win.showControls=!0,this.win.id="optionsAccountMonitor",this.win.onClose.subscribe(function(e){t.submitFontSize(e),setTimeout(function(){t.submitRate(e)},0)}),this.win.display()}catch(e){}},e.prototype.submitRate=function(t){isNaN(t.refreshRate)||""==t.refreshRate?this.swtAlert.error("Not a number"):(this.refreshRate=Number(t.refreshRate),this.refreshRate<5&&(this.refreshRate=5,this.swtAlert.error("Refresh rate selected was below minimum.\nSet to 5 seconds")));var e=o.x.call("getUpdateRefreshRequest",this.refreshRate);if(null!=e&&""!=e){var i;i=this.updateFontSize.url,this.updateRefreshRate.url=this.baseURL+e,this.updateRefreshRate.send(),this.updateRefreshRate.url=i}this.autoRefreshAfterStop()},e.prototype.submitFontSize=function(t){if(this.fontValue=t.fontSize.value,"N"==this.fontValue?(this.selectedFont=0,this.fontLabel=t.fontSize.label,this.accountGrid.styleName="dataGridNormal",this.accountGrid.rowHeight=18,this.fontRequest=o.x.call("getUpdateFontSize",this.fontLabel)):"S"==this.fontValue&&(this.selectedFont=1,this.fontLabel=t.fontSize.label,this.accountGrid.styleName="dataGridSmall",this.accountGrid.rowHeight=15,this.fontRequest=o.x.call("getUpdateFontSize",this.fontLabel)),null!=this.fontRequest&&""!=this.fontRequest){var e;e=this.updateFontSize.url,this.updateFontSize.url=this.baseURL+this.fontRequest,this.updateFontSize.send(),this.updateFontSize.url=e}},e.prototype.closeHandler=function(){o.x.call("close")},e.prototype.getSortedGridColumn=function(){var t=!1,e=-1,i="";try{e=this.accountGrid.sortColumnIndex;var n=this.accountGrid.getFilterColumns();return-1!=e?(t=this.accountGrid.sortDirection,i=n[e].field+"|"+t+"|"):i=null,i}catch(l){console.log(l)}},e.prototype.inputDataFault=function(t){this.lostConnectionText.visible=!0;var e=o.Wb.getPredictMessage("label.genericException",null);this.swtAlert.error(e)},e.prototype.connError=function(){this.SwtAlert.error(""+this.invalidComms)},e.prototype.sumHandler=function(){var t=this,e=this.entityCombo.selectedLabel,i=this.accountGrid.dataProvider[this.accountGrid.selectedIndex].ccy,n=this.accountGrid.dataProvider[this.accountGrid.selectedIndex].account,l=this.accountGrid.dataProvider[this.accountGrid.selectedIndex].sum,a=this.accountGrid.selectedItem.sum.summable?this.accountGrid.selectedItem.sum.summable:"false",s="Include/Exclude this account and all linked accounts from the totals?",r=s+" This change will not update accounts that are flagged to sum according to cut-off",c=null,d=(new Date).getTime();clearInterval(this.interval),"Y"==l?(c="ExcludeFromTotals",this.swtAlert.question("Exclude this account and all linked accounts from the totals?",null,o.c.OK|o.c.CANCEL,null,function(l){t.sumRequest(l,e,i,n,c,d)},o.c.CANCEL)):"N"==l?(c="IncludeInTotals",this.swtAlert.question("Include this account and all linked accounts in the totals?",null,o.c.OK|o.c.CANCEL,null,function(l){t.sumRequest(l,e,i,n,c,d)},o.c.CANCEL)):"P"==l?(o.c.yesLabel="Include",o.c.noLabel="Exclude","true"==a?this.swtAlert.question(r,null,o.c.YES|o.c.NO|o.c.CANCEL,null,function(l){t.sumRequest(l,e,i,n,c,d)},o.c.CANCEL):this.swtAlert.question(s,null,o.c.YES|o.c.NO|o.c.CANCEL,null,function(l){t.sumRequest(l,e,i,n,c,d)},o.c.CANCEL)):"C"==l&&"true"==a&&(o.c.yesLabel="Include",o.c.noLabel="Exclude",this.swtAlert.question(r,null,o.c.YES|o.c.NO|o.c.CANCEL,null,function(l){t.sumRequest(l,e,i,n,c,d)},o.c.CANCEL))},e.prototype.sumRequest=function(t,e,i,n,l,a){if(t.detail==o.c.YES)l="IncludeInTotals";else if(t.detail==o.c.NO)l="ExcludeFromTotals";else if(t.detail!=o.c.OK)return void this.autoRefreshAfterStop();this.logicUpdate.encodeURL=!1,this.actionPath="acctmonitornew.do?",this.actionMethod="method=updateMonitorSumFlag",this.requestParams=[],this.requestParams.operation=l,this.requestParams.accountId=n,this.requestParams["accountMonitorNew.entityId"]=e,this.requestParams["accountMonitorNew.currencyCode"]=i,this.requestParams.nocache=a,this.requestParams.method="updateMonitorSumFlag",this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams),this.sumButton.enabled=!1,this.sumButton.buttonMode=!1,this.updateData("no")},e.prototype.moveHandler=function(){var t=this;try{var e="",i=this.entityCombo.selectedLabel,n=this.accountGrid.dataProvider[this.accountGrid.selectedIndex].ccy,l=this.accountGrid.dataProvider[this.accountGrid.selectedIndex].account,a=this.accountGrid.dataProvider[this.accountGrid.selectedIndex].slickgrid_rowcontent.lorotopredictedflag.content,s=(new Date).getTime();clearInterval(this.interval),"Y"==a?(e="MoveToLoro",this.swtAlert.question("Move balance to Loro/Curr column?",null,o.c.OK|o.c.CANCEL,null,function(o){t.moveRequest(o,i,n,l,e,s)},o.c.CANCEL)):"N"==a&&(e="MoveToPredicted",this.swtAlert.question("Move balance to Predicted column?",null,o.c.OK|o.c.CANCEL,null,function(o){t.moveRequest(o,i,n,l,e,s)},o.c.CANCEL))}catch(r){console.log("error in move ",r)}},e.prototype.moveRequest=function(t,e,i,n,l,a){t.detail==o.c.OK?(this.requestParams=[],this.actionPath="acctmonitornew.do?",this.actionMethod="method=updateLoroToPredictedFlag",this.requestParams.operation=l,this.requestParams.accountId=n,this.requestParams["accountMonitorNew.entityId"]=e,this.requestParams["accountMonitorNew.currencyCode"]=i,this.requestParams.nocache=a,this.requestParams.method="updateLoroToPredictedFlag",this.logicUpdate.encodeURL=!1,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams),this.sumButton.enabled=!1,this.sumButton.buttonMode=!1,this.updateData("no")):this.autoRefreshAfterStop()},e.prototype.swpHandler=function(){var t=this.entityCombo.selectedLabel,e=this.ccyCombo.selectedLabel,i=null;this.typeCombo.selectedIndex>-1&&(i=this.typeCombo.selectedItem.value),this.swp(t,e,i)},e.prototype.swp=function(t,e,i){o.x.call("openManualsweep",t,e,i)},e.prototype.report=function(t){var e=[];e.push(o.x.call("getBundle","text","label-entity","Entity")+"="+this.entityCombo.selectedLabel),e.push("Ccy="+this.ccyCombo.selectedLabel),e.push(o.x.call("getBundle","text","label-accountType","Account Type")+"="+this.typeCombo.selectedLabel),e.push(o.x.call("getBundle","text","label-accountClass","Account Class")+"="+this.classCombo.selectedLabel),e.push("Date="+this.startDate.text),e.push(o.x.call("getBundle","text","label-startDayBalance","Start of Day Balance")+"="+(""!=this.sodText.text.toString()?this.sodText.text:"None")),e.push(o.x.call("getBundle","text","label-openUnexpected","Open Unexpected")+"="+(""!=this.unexpectedText.text.toString()?this.unexpectedText.text:"None")),e.push(o.x.call("getBundle","text","label-hideZeroBalances","Hide Zero Balance")+"="+(this.hideZero.selected?"On":"Off")),e.push(o.x.call("getBundle","text","label-currencyThresold","Currency Threshold")+"="+(this.currencyThreshold.selected?"On":"Off")),this.dataExport.convertData(this.lastRecievedJSON.accountmonitor.grid.metadata.columns,this.accountGrid,this.totalGrid.gridData,e,t,!0)},e.prototype.autoRefreshAfterStop=function(){var t=this,e=this.refreshRate;this.interval=setInterval(function(){t.dataRefresh()},1e3*e)},e.prototype.unexpectedHandler=function(){var t=this.entityCombo.selectedLabel.toString(),e=this.ccyCombo.selectedLabel.toString(),i=this.startDate.text,n=this.typeCombo.selectedValue,l=this.currencyThreshold.selected?"Y":"N";t.length>0&&"All"!=e&&o.x.call("showOpenUnexpectedMovs",t,e,"",i,n,l)},e.prototype.underlineText=function(){this.unexpectedText.setStyle("text-decoration","underline",this.unexpectedText.domElement)},e.prototype.removeUnderline=function(){this.unexpectedText.setStyle("text-decoration","none",this.unexpectedText.domElement)},e}(o.yb)),b=[{path:"",component:h}],p=(a.l.forChild(b),function(){return function(){}}()),m=i("pMnS"),g=i("RChO"),w=i("t6HQ"),y=i("WFGK"),f=i("5FqG"),C=i("Ip0R"),x=i("gIcY"),v=i("t/Na"),S=i("sE5F"),R=i("OzfB"),I=i("T7CS"),T=i("S7LP"),B=i("6aHO"),L=i("WzUx"),D=i("A7o+"),P=i("zCE2"),A=i("Jg5P"),G=i("3R0m"),k=i("hhbb"),N=i("5rxC"),O=i("Fzqc"),J=i("21Lb"),F=i("hUWP"),M=i("3pJQ"),E=i("V9q+"),_=i("VDKW"),Z=i("kXfT"),q=i("BGbe");i.d(e,"AccountMonitorModuleNgFactory",function(){return H}),i.d(e,"RenderType_AccountMonitor",function(){return U}),i.d(e,"View_AccountMonitor_0",function(){return W}),i.d(e,"View_AccountMonitor_Host_0",function(){return j}),i.d(e,"AccountMonitorNgFactory",function(){return Y});var H=n.Gb(p,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[m.a,g.a,w.a,y.a,f.Cb,f.Pb,f.r,f.rc,f.s,f.Ab,f.Bb,f.Db,f.qd,f.Hb,f.k,f.Ib,f.Nb,f.Ub,f.yb,f.Jb,f.v,f.A,f.e,f.c,f.g,f.d,f.Kb,f.f,f.ec,f.Wb,f.bc,f.ac,f.sc,f.fc,f.lc,f.jc,f.Eb,f.Fb,f.mc,f.Lb,f.nc,f.Mb,f.dc,f.Rb,f.b,f.ic,f.Yb,f.Sb,f.kc,f.y,f.Qb,f.cc,f.hc,f.pc,f.oc,f.xb,f.p,f.q,f.o,f.h,f.j,f.w,f.Zb,f.i,f.m,f.Vb,f.Ob,f.Gb,f.Xb,f.t,f.tc,f.zb,f.n,f.qc,f.a,f.z,f.rd,f.sd,f.x,f.td,f.gc,f.l,f.u,f.ud,f.Tb,Y]],[3,n.n],n.J]),n.Rb(4608,C.m,C.l,[n.F,[2,C.u]]),n.Rb(4608,x.c,x.c,[]),n.Rb(4608,x.p,x.p,[]),n.Rb(4608,v.j,v.p,[C.c,n.O,v.n]),n.Rb(4608,v.q,v.q,[v.j,v.o]),n.Rb(5120,v.a,function(t){return[t,new o.tb]},[v.q]),n.Rb(4608,v.m,v.m,[]),n.Rb(6144,v.k,null,[v.m]),n.Rb(4608,v.i,v.i,[v.k]),n.Rb(6144,v.b,null,[v.i]),n.Rb(4608,v.f,v.l,[v.b,n.B]),n.Rb(4608,v.c,v.c,[v.f]),n.Rb(4608,S.c,S.c,[]),n.Rb(4608,S.g,S.b,[]),n.Rb(5120,S.i,S.j,[]),n.Rb(4608,S.h,S.h,[S.c,S.g,S.i]),n.Rb(4608,S.f,S.a,[]),n.Rb(5120,S.d,S.k,[S.h,S.f]),n.Rb(5120,n.b,function(t,e){return[R.j(t,e)]},[C.c,n.O]),n.Rb(4608,I.a,I.a,[]),n.Rb(4608,T.a,T.a,[]),n.Rb(4608,B.a,B.a,[n.n,n.L,n.B,T.a,n.g]),n.Rb(4608,L.c,L.c,[n.n,n.g,n.B]),n.Rb(4608,L.e,L.e,[L.c]),n.Rb(4608,D.l,D.l,[]),n.Rb(4608,D.h,D.g,[]),n.Rb(4608,D.c,D.f,[]),n.Rb(4608,D.j,D.d,[]),n.Rb(4608,D.b,D.a,[]),n.Rb(4608,D.k,D.k,[D.l,D.h,D.c,D.j,D.b,D.m,D.n]),n.Rb(4608,L.i,L.i,[[2,D.k]]),n.Rb(4608,L.r,L.r,[L.L,[2,D.k],L.i]),n.Rb(4608,L.t,L.t,[]),n.Rb(4608,L.w,L.w,[]),n.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),n.Rb(1073742336,C.b,C.b,[]),n.Rb(1073742336,x.n,x.n,[]),n.Rb(1073742336,x.l,x.l,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,A.a,A.a,[]),n.Rb(1073742336,x.e,x.e,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,D.i,D.i,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,v.e,v.e,[]),n.Rb(1073742336,v.d,v.d,[]),n.Rb(1073742336,S.e,S.e,[]),n.Rb(1073742336,k.b,k.b,[]),n.Rb(1073742336,N.b,N.b,[]),n.Rb(1073742336,R.c,R.c,[]),n.Rb(1073742336,O.a,O.a,[]),n.Rb(1073742336,J.d,J.d,[]),n.Rb(1073742336,F.c,F.c,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,E.a,E.a,[[2,R.g],n.O]),n.Rb(1073742336,_.b,_.b,[]),n.Rb(1073742336,Z.a,Z.a,[]),n.Rb(1073742336,q.b,q.b,[]),n.Rb(1073742336,o.Tb,o.Tb,[]),n.Rb(1073742336,p,p,[]),n.Rb(256,v.n,"XSRF-TOKEN",[]),n.Rb(256,v.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,D.m,void 0,[]),n.Rb(256,D.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,a.i,function(){return[[{path:"",component:h}]]},[])])}),z=[[".canvasWithGreyBorder{border:1px solid #696969!important}"]],U=n.Hb({encapsulation:2,styles:z,data:{}});function W(t){return n.dc(0,[n.Zb(*********,1,{_container:0}),n.Zb(*********,2,{gridCanvas:0}),n.Zb(*********,3,{totalCanvas:0}),n.Zb(*********,4,{tabs:0}),n.Zb(*********,5,{displayContainerToday:0}),n.Zb(*********,6,{displayContainerTodayPlus:0}),n.Zb(*********,7,{displayContainerTodayPlusPlus:0}),n.Zb(*********,8,{displayContainerTodayPlusThree:0}),n.Zb(*********,9,{displayContainerTodayPlusFour:0}),n.Zb(*********,10,{displayContainerTodayPlusFive:0}),n.Zb(*********,11,{displayContainerTodayPlusSix:0}),n.Zb(*********,12,{displayContainerSelected:0}),n.Zb(*********,13,{alertImage:0}),n.Zb(*********,14,{globalCanvas:0}),n.Zb(*********,15,{totalsContainer:0}),n.Zb(*********,16,{loader:0}),n.Zb(*********,17,{groupCombo:0}),n.Zb(*********,18,{entityCombo:0}),n.Zb(*********,19,{ccyCombo:0}),n.Zb(*********,20,{typeCombo:0}),n.Zb(*********,21,{classCombo:0}),n.Zb(*********,22,{hideZero:0}),n.Zb(*********,23,{currencyThreshold:0}),n.Zb(*********,24,{currencyLabel:0}),n.Zb(*********,25,{currencyToday:0}),n.Zb(*********,26,{selectedCcy:0}),n.Zb(*********,27,{accountTypeLabel:0}),n.Zb(*********,28,{accoutClassLabel:0}),n.Zb(*********,29,{dateLabel:0}),n.Zb(*********,30,{startDate:0}),n.Zb(*********,31,{entityLabel:0}),n.Zb(*********,32,{selectedEntity:0}),n.Zb(*********,33,{alwaysSumLabel:0}),n.Zb(*********,34,{sumByCutOffLabel:0}),n.Zb(*********,35,{hideZeroLabel:0}),n.Zb(*********,36,{applyCurrencyLabel:0}),n.Zb(*********,37,{openLabel:0}),n.Zb(*********,38,{sodLabel:0}),n.Zb(*********,39,{lastRefTime:0}),n.Zb(*********,40,{lastRefText:0}),n.Zb(*********,41,{dataBuildingText:0}),n.Zb(*********,42,{lostConnectionText:0}),n.Zb(*********,43,{unexpectedText:0}),n.Zb(*********,44,{sodText:0}),n.Zb(*********,45,{breakdown:0}),n.Zb(*********,46,{radioC:0}),n.Zb(*********,47,{radioE:0}),n.Zb(*********,48,{optionsButton:0}),n.Zb(*********,49,{refreshButton:0}),n.Zb(*********,50,{sumButton:0}),n.Zb(*********,51,{manswpButton:0}),n.Zb(*********,52,{closeButton:0}),n.Zb(*********,53,{swtModule:0}),n.Zb(*********,54,{dataExport:0}),n.Zb(*********,55,{accountRadio:0}),n.Zb(*********,56,{movementRadio:0}),n.Zb(*********,57,{breakLabel:0}),(t()(),n.Jb(57,0,null,null,153,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,l=t.component;"creationComplete"===e&&(n=!1!==l.onLoad()&&n);return n},f.ad,f.hb)),n.Ib(58,4440064,null,0,o.yb,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(59,0,null,0,151,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,f.od,f.vb)),n.Ib(60,4440064,null,0,o.ec,[n.r,o.i,n.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),n.Jb(61,0,null,0,109,"SwtCanvas",[["height","120"],["minWidth","1100"],["width","100%"]],null,null,null,f.Nc,f.U)),n.Ib(62,4440064,null,0,o.db,[n.r,o.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),n.Jb(63,0,null,0,107,"HBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(64,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"],paddingRight:[3,"paddingRight"]},null),(t()(),n.Jb(65,0,null,0,31,"HBox",[["width","40%"]],null,null,null,f.Dc,f.K)),n.Ib(66,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(67,0,null,0,29,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,f.od,f.vb)),n.Ib(68,4440064,null,0,o.ec,[n.r,o.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(69,0,null,0,7,"HBox",[["height","25"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(70,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(71,0,null,0,1,"SwtLabel",[["id","entityLabel"],["width","100"]],null,null,null,f.Yc,f.fb)),n.Ib(72,4440064,[[31,4],["entityLabel",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),n.Jb(73,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var l=!0,o=t.component;"window:mousewheel"===e&&(l=!1!==n.Tb(t,74).mouseWeelEventHandler(i.target)&&l);"change"===e&&(l=!1!==o.entityChangeCombo()&&l);return l},f.Pc,f.W)),n.Ib(74,4440064,[[18,4],["entityCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(75,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingLeft","10"]],null,null,null,f.Yc,f.fb)),n.Ib(76,4440064,[[32,4],["selectedEntity",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(77,0,null,0,7,"HBox",[["height","25"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(78,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(79,0,null,0,1,"SwtLabel",[["id","currencyLabel"],["width","100"]],null,null,null,f.Yc,f.fb)),n.Ib(80,4440064,[[24,4],["currencyLabel",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),n.Jb(81,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","ccyCombo"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var l=!0,o=t.component;"window:mousewheel"===e&&(l=!1!==n.Tb(t,82).mouseWeelEventHandler(i.target)&&l);"change"===e&&(l=!1!==o.changeCombo(i)&&l);return l},f.Pc,f.W)),n.Ib(82,4440064,[[19,4],["ccyCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(83,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCcy"],["paddingLeft","10"]],null,null,null,f.Yc,f.fb)),n.Ib(84,4440064,[[26,4],["selectedCcy",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(85,0,null,0,5,"HBox",[["height","25"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(86,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(87,0,null,0,1,"SwtLabel",[["id","accountTypeLabel"],["width","100"]],null,null,null,f.Yc,f.fb)),n.Ib(88,4440064,[[27,4],["accountTypeLabel",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),n.Jb(89,0,null,0,1,"SwtComboBox",[["dataLabel","accounttype"],["id","typeCombo"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var l=!0,o=t.component;"window:mousewheel"===e&&(l=!1!==n.Tb(t,90).mouseWeelEventHandler(i.target)&&l);"change"===e&&(l=!1!==o.changeCombo(i)&&l);return l},f.Pc,f.W)),n.Ib(90,4440064,[[20,4],["typeCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(91,0,null,0,5,"HBox",[["height","25"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(92,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(93,0,null,0,1,"SwtLabel",[["id","accoutClassLabel"],["width","100"]],null,null,null,f.Yc,f.fb)),n.Ib(94,4440064,[[28,4],["accoutClassLabel",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),n.Jb(95,0,null,0,1,"SwtComboBox",[["dataLabel","accountclass"],["id","classCombo"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var l=!0,o=t.component;"window:mousewheel"===e&&(l=!1!==n.Tb(t,96).mouseWeelEventHandler(i.target)&&l);"change"===e&&(l=!1!==o.changeCombo(i)&&l);return l},f.Pc,f.W)),n.Ib(96,4440064,[[21,4],["classCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(97,0,null,0,24,"HBox",[["width","30%"]],null,null,null,f.Dc,f.K)),n.Ib(98,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(99,0,null,0,18,"HBox",[["height","70%"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(100,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(101,0,null,0,16,"fieldset",[],null,null,null,null,null)),(t()(),n.Jb(102,0,null,null,1,"legend",[],null,null,null,null,null)),(t()(),n.bc(103,null,[""," ",""])),(t()(),n.Jb(104,0,null,null,13,"VBox",[["verticalGap","0"],["width","100%"]],null,null,null,f.od,f.vb)),n.Ib(105,4440064,null,0,o.ec,[n.r,o.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"]},null),(t()(),n.Jb(106,0,null,0,5,"HBox",[["height","40%"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(107,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(108,0,null,0,1,"SwtLabel",[["width","50%"]],null,null,null,f.Yc,f.fb)),n.Ib(109,4440064,[[38,4],["sodLabel",4]],0,o.vb,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(110,0,null,0,1,"SwtTextInput",[["editable","false"],["textAlign","right"],["width","50%"]],null,null,null,f.kd,f.sb)),n.Ib(111,4440064,[[44,4],["sodText",4]],0,o.Rb,[n.r,o.i],{textAlign:[0,"textAlign"],width:[1,"width"],editable:[2,"editable"]},null),(t()(),n.Jb(112,0,null,0,5,"HBox",[["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(113,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(114,0,null,0,1,"SwtLabel",[["width","50%"]],null,null,null,f.Yc,f.fb)),n.Ib(115,4440064,[[37,4],["openLabel",4]],0,o.vb,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(116,0,null,0,1,"SwtTextInput",[["editable","false"],["textAlign","right"],["width","50%"]],null,[[null,"click"],[null,"mouseIn"],[null,"mouseout"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.unexpectedHandler()&&n);"mouseIn"===e&&(n=!1!==l.underlineText()&&n);"mouseout"===e&&(n=!1!==l.removeUnderline()&&n);return n},f.kd,f.sb)),n.Ib(117,4440064,[[43,4],["unexpectedText",4]],0,o.Rb,[n.r,o.i],{textAlign:[0,"textAlign"],width:[1,"width"],editable:[2,"editable"]},{onClick_:"click",mouseIn_:"mouseIn"}),(t()(),n.Jb(118,0,null,0,3,"HBox",[["height","70%"],["paddingTop","35"]],null,null,null,f.Dc,f.K)),n.Ib(119,4440064,null,0,o.C,[n.r,o.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(t()(),n.Jb(120,0,null,0,1,"SwtImage",[["height","19"],["id","alertImage"],["width","19"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.imageClickFunction(i)&&n);return n},f.Xc,f.eb)),n.Ib(121,4440064,[[13,4],["alertImage",4]],0,o.ub,[n.r],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},{onClick_:"click"}),(t()(),n.Jb(122,0,null,0,48,"HBox",[["width","35%"]],null,null,null,f.Dc,f.K)),n.Ib(123,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(124,0,null,0,46,"Grid",[["height","100%"],["width","100%"]],null,null,null,f.Cc,f.H)),n.Ib(125,4440064,null,0,o.z,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(126,0,null,0,9,"GridRow",[["width","100%"]],null,null,null,f.Bc,f.J)),n.Ib(127,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(128,0,null,0,7,"GridItem",[["width","100%"]],null,null,null,f.Ac,f.I)),n.Ib(129,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(130,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(131,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),n.Jb(132,0,null,0,1,"SwtLabel",[["id","dateLabel"],["paddingRight","57"]],null,null,null,f.Yc,f.fb)),n.Ib(133,4440064,[[29,4],["dateLabel",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],paddingRight:[1,"paddingRight"]},null),(t()(),n.Jb(134,0,null,0,1,"SwtDateField",[["id","startDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"],[null,"keyup.enter"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(n=!1!==l.validateDate()&&n);"keyup.enter"===e&&(n=!1!==l.validateDate()&&n);return n},f.Tc,f.ab)),n.Ib(135,4308992,[[30,4],["startDate",4]],0,o.lb,[n.r,o.i,n.T],{restrict:[0,"restrict"],id:[1,"id"],width:[2,"width"]},{changeEventOutPut:"change"}),(t()(),n.Jb(136,0,null,0,9,"GridRow",[["width","100%"]],null,null,null,f.Bc,f.J)),n.Ib(137,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(138,0,null,0,7,"GridItem",[["width","100%"]],null,null,null,f.Ac,f.I)),n.Ib(139,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(140,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(141,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),n.Jb(142,0,null,0,1,"SwtLabel",[["width","170"]],null,null,null,f.Yc,f.fb)),n.Ib(143,4440064,[[35,4],["hideZeroLabel",4]],0,o.vb,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(144,0,null,0,1,"SwtCheckBox",[],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(n=!1!==l.changeCombo(i)&&n);return n},f.Oc,f.V)),n.Ib(145,4440064,[[22,4],["hideZero",4]],0,o.eb,[n.r,o.i],null,{change_:"change"}),(t()(),n.Jb(146,0,null,0,9,"GridRow",[["width","100%"]],null,null,null,f.Bc,f.J)),n.Ib(147,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(148,0,null,0,7,"GridItem",[["width","100%"]],null,null,null,f.Ac,f.I)),n.Ib(149,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(150,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(151,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),n.Jb(152,0,null,0,1,"SwtLabel",[["width","170"]],null,null,null,f.Yc,f.fb)),n.Ib(153,4440064,[[36,4],["applyCurrencyLabel",4]],0,o.vb,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(154,0,null,0,1,"SwtCheckBox",[],null,[[null,"change"]],function(t,e,i){var n=!0,l=t.component;"change"===e&&(n=!1!==l.changeCombo(i)&&n);return n},f.Oc,f.V)),n.Ib(155,4440064,[[23,4],["currencyThreshold",4]],0,o.eb,[n.r,o.i],null,{change_:"change"}),(t()(),n.Jb(156,0,null,0,14,"GridRow",[["width","100%"]],null,null,null,f.Bc,f.J)),n.Ib(157,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(158,0,null,0,12,"GridItem",[["width","100%"]],null,null,null,f.Ac,f.I)),n.Ib(159,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(160,0,null,0,10,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(161,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),n.Jb(162,0,null,0,1,"SwtLabel",[["paddingTop","2"]],null,null,null,f.Yc,f.fb)),n.Ib(163,4440064,[[57,4],["breakLabel",4]],0,o.vb,[n.r,o.i],{paddingTop:[0,"paddingTop"]},null),(t()(),n.Jb(164,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","breakdown"]],null,null,null,f.ed,f.lb)),n.Ib(165,4440064,[[45,4],["breakdown",4]],1,o.Hb,[v.c,n.r,o.i],{id:[0,"id"],align:[1,"align"]},null),n.Zb(*********,58,{radioItems:1}),(t()(),n.Jb(167,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","accountRadio"],["selected","true"],["value","a"]],null,null,null,f.fd,f.mb)),n.Ib(168,4440064,[[58,4],[55,4],["accountRadio",4]],0,o.Ib,[n.r,o.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"],selected:[3,"selected"]},null),(t()(),n.Jb(169,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","movementRadio"],["value","m"]],null,null,null,f.fd,f.mb)),n.Ib(170,4440064,[[58,4],[56,4],["movementRadio",4]],0,o.Ib,[n.r,o.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),n.Jb(171,0,null,0,2,"SwtTabNavigator",[["borderBottom","false"],["height","3%"],["id","tabs"],["minWidth","1100"],["width","100%"]],null,[[null,"onChange"]],function(t,e,i){var n=!0,l=t.component;"onChange"===e&&(n=!1!==l.tabIndexchangeHandler()&&n);return n},f.id,f.pb)),n.Ib(172,4440064,[[4,4],["tabs",4]],1,o.Ob,[n.r,o.i,n.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"],borderBottom:[4,"borderBottom"]},{onChange_:"onChange"}),n.Zb(*********,59,{tabChildren:1}),(t()(),n.Jb(174,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","gridCanvas"],["minWidth","1100"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,f.Nc,f.U)),n.Ib(175,4440064,[[2,4],["gridCanvas",4]],0,o.db,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],minWidth:[4,"minWidth"],border:[5,"border"]},null),(t()(),n.Jb(176,0,null,0,1,"SwtCanvas",[["border","false"],["height","40"],["id","totalsContainer"],["minWidth","1100"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,f.Nc,f.U)),n.Ib(177,4440064,[[15,4],["totalsContainer",4]],0,o.db,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],minWidth:[4,"minWidth"],border:[5,"border"]},null),(t()(),n.Jb(178,0,null,0,32,"SwtCanvas",[["height","40"],["id","canvasButtons"],["marginBottom","0"],["minWidth","1100"],["width","100%"]],null,null,null,f.Nc,f.U)),n.Ib(179,4440064,null,0,o.db,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"],marginBottom:[4,"marginBottom"]},null),(t()(),n.Jb(180,0,null,0,30,"HBox",[["height","100%"],["width","100%"]],null,null,null,f.Dc,f.K)),n.Ib(181,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(182,0,null,0,11,"HBox",[["paddingLeft","5"],["width","50%"]],null,null,null,f.Dc,f.K)),n.Ib(183,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(184,0,null,0,1,"SwtButton",[["id","refreshButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.updateData("yes")&&n);return n},f.Mc,f.T)),n.Ib(185,4440064,[[49,4],["refreshButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(186,0,null,0,1,"SwtButton",[["id","optionsButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.optionsHandler()&&n);return n},f.Mc,f.T)),n.Ib(187,4440064,[[48,4],["optionsButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),n.Jb(188,0,null,0,1,"SwtButton",[["enabled","false"],["id","sumButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.sumHandler()&&n);return n},f.Mc,f.T)),n.Ib(189,4440064,[[50,4],["sumButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(190,0,null,0,1,"SwtButton",[["enabled","false"],["id","manswpButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.swpHandler()&&n);return n},f.Mc,f.T)),n.Ib(191,4440064,[[51,4],["manswpButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(192,0,null,0,1,"SwtButton",[["id","closeButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.closeHandler()&&n);return n},f.Mc,f.T)),n.Ib(193,4440064,[[52,4],["closeButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),n.Jb(194,0,null,0,16,"HBox",[["horizontalAlign","right"],["paddingTop","5"],["width","50%"]],null,null,null,f.Dc,f.K)),n.Ib(195,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(t()(),n.Jb(196,0,null,0,1,"SwtLabel",[["color","red"],["visible","false"]],null,null,null,f.Yc,f.fb)),n.Ib(197,4440064,[[41,4],["dataBuildingText",4]],0,o.vb,[n.r,o.i],{visible:[0,"visible"],color:[1,"color"]},null),(t()(),n.Jb(198,0,null,0,1,"SwtLabel",[["color","red"],["visible","false"]],null,null,null,f.Yc,f.fb)),n.Ib(199,4440064,[[42,4],["lostConnectionText",4]],0,o.vb,[n.r,o.i],{visible:[0,"visible"],color:[1,"color"]},null),(t()(),n.Jb(200,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,f.Yc,f.fb)),n.Ib(201,4440064,[[40,4],["lastRefText",4]],0,o.vb,[n.r,o.i],{fontWeight:[0,"fontWeight"]},null),(t()(),n.Jb(202,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,f.Yc,f.fb)),n.Ib(203,4440064,[[39,4],["lastRefTime",4]],0,o.vb,[n.r,o.i],{fontWeight:[0,"fontWeight"]},null),(t()(),n.Jb(204,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),n.Jb(205,0,null,null,1,"DataExport",[["id","dataExport"]],null,null,null,f.Sc,f.Z)),n.Ib(206,4440064,[[54,4],["dataExport",4]],0,o.kb,[o.i,n.r],{id:[0,"id"]},null),(t()(),n.Jb(207,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.doHelp()&&n);return n},f.Wc,f.db)),n.Ib(208,4440064,[["helpIcon",4]],0,o.rb,[n.r,o.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(209,0,null,0,1,"SwtLoadingImage",[],null,null,null,f.Zc,f.gb)),n.Ib(210,114688,[[16,4],["loader",4]],0,o.xb,[n.r],null,null)],function(t,e){t(e,58,0,"100%","100%");t(e,60,0,"vBox1","100%","100%","5","5","5","5");t(e,62,0,"100%","120","1100");t(e,64,0,"100%","100%","5","5");t(e,66,0,"40%");t(e,68,0,"0","100%","100%");t(e,70,0,"100%","25");t(e,72,0,"entityLabel","100");t(e,74,0,"entity","135","entityCombo");t(e,76,0,"selectedEntity","10","normal");t(e,78,0,"100%","25");t(e,80,0,"currencyLabel","100");t(e,82,0,"currency","135","ccyCombo");t(e,84,0,"selectedCcy","10","normal");t(e,86,0,"100%","25");t(e,88,0,"accountTypeLabel","100");t(e,90,0,"accounttype","135","typeCombo");t(e,92,0,"100%","25");t(e,94,0,"accoutClassLabel","100");t(e,96,0,"accountclass","135","classCombo");t(e,98,0,"30%");t(e,100,0,"100%","70%");t(e,105,0,"0","100%");t(e,107,0,"100%","40%");t(e,109,0,"50%");t(e,111,0,"right","50%","false");t(e,113,0,"100%");t(e,115,0,"50%");t(e,117,0,"right","50%","false");t(e,119,0,"70%","35");t(e,121,0,"alertImage","19","19");t(e,123,0,"35%");t(e,125,0,"100%","100%");t(e,127,0,"100%");t(e,129,0,"100%");t(e,131,0,"right","100%");t(e,133,0,"dateLabel","57");t(e,135,0,"0-9/","startDate","70");t(e,137,0,"100%");t(e,139,0,"100%");t(e,141,0,"right","100%");t(e,143,0,"170"),t(e,145,0);t(e,147,0,"100%");t(e,149,0,"100%");t(e,151,0,"right","100%");t(e,153,0,"170"),t(e,155,0);t(e,157,0,"100%");t(e,159,0,"100%");t(e,161,0,"right","100%");t(e,163,0,"2");t(e,165,0,"breakdown","horizontal");t(e,168,0,"accountRadio","breakdown","a","true");t(e,170,0,"movementRadio","breakdown","m");t(e,172,0,"tabs","100%","3%","1100","false");t(e,175,0,"gridCanvas","canvasWithGreyBorder","100%","100%","1100","false");t(e,177,0,"totalsContainer","canvasWithGreyBorder","100%","40","1100","false");t(e,179,0,"canvasButtons","100%","40","1100","0");t(e,181,0,"100%","100%");t(e,183,0,"50%","5");t(e,185,0,"refreshButton","70",!0);t(e,187,0,"optionsButton","70");t(e,189,0,"sumButton","70","false");t(e,191,0,"manswpButton","70","false");t(e,193,0,"closeButton","70");t(e,195,0,"right","50%","5");t(e,197,0,"false","red");t(e,199,0,"false","red");t(e,201,0,"normal");t(e,203,0,"normal");t(e,206,0,"dataExport");t(e,208,0,"helpIcon"),t(e,210,0)},function(t,e){var i=e.component;t(e,103,0,i.currencyDay,i.today)})}function j(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-account-monitor",[],null,null,null,W,U)),n.Ib(1,4440064,null,0,h,[o.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var Y=n.Fb("app-account-monitor",h,j,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);