{"_from": "@types/webpack-sources@^0.1.5", "_id": "@types/webpack-sources@0.1.12", "_inBundle": false, "_integrity": "sha512-+vRVqE3LzMLLVPgZHUeI8k1YmvgEky+MOir5fQhKvFxpB8uZ0CFnGqxkRAmf8jvNhUBQzhuGZpIMNWZDeEyDIA==", "_location": "/@types/webpack-sources", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/webpack-sources@^0.1.5", "name": "@types/webpack-sources", "escapedName": "@types%2fwebpack-sources", "scope": "@types", "rawSpec": "^0.1.5", "saveSpec": null, "fetchSpec": "^0.1.5"}, "_requiredBy": ["/license-webpack-plugin"], "_resolved": "https://registry.npmjs.org/@types/webpack-sources/-/webpack-sources-0.1.12.tgz", "_shasum": "9beb82c5dc5483c0fb947da1723f4044b07f6204", "_spec": "@types/webpack-sources@^0.1.5", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\license-webpack-plugin", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud"}, {"name": "<PERSON>", "url": "https://github.com/chrise<PERSON>tein"}], "dependencies": {"@types/node": "*", "@types/source-list-map": "*", "source-map": "^0.6.1"}, "deprecated": false, "description": "TypeScript definitions for webpack-sources", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/webpack-sources", "license": "MIT", "main": "", "name": "@types/webpack-sources", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/webpack-sources"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "0ed3e5b18481a19e85e21e14d924c2a7bd3b44416df3ddcd0c7c63365b95fe82", "version": "0.1.12"}