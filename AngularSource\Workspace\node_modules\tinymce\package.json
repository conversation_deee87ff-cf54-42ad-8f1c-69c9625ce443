{"_from": "tinymce@5.1.5", "_id": "tinymce@5.1.5", "_inBundle": false, "_integrity": "sha512-xl7rMhjxtDLWGXJF2ZNGv6Dxqy64HAQKSCvfcbfFuF+oJYRA1JqfNLfgS3yqG4D/V30adFWOZUv9Eng6l5BWOA==", "_location": "/tinymce", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "tinymce@5.1.5", "name": "<PERSON><PERSON><PERSON>", "escapedName": "<PERSON><PERSON><PERSON>", "rawSpec": "5.1.5", "saveSpec": null, "fetchSpec": "5.1.5"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/tinymce/-/tinymce-5.1.5.tgz", "_shasum": "08a4815bcefd33fb5f654b0b82b066f788a8ddf4", "_spec": "tinymce@5.1.5", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "Ephox Corporation"}, "bugs": {"url": "https://github.com/tinymce/tinymce/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Web based JavaScript HTML WYSIWYG editor control.", "homepage": "https://github.com/tinymce/tinymce-dist#readme", "keywords": ["editor", "wysiwyg", "<PERSON><PERSON><PERSON>", "richtext", "javascript", "html"], "license": "LGPL-2.1", "main": "tinymce.js", "name": "<PERSON><PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/tinymce/tinymce-dist.git"}, "version": "5.1.5"}