{"_from": "@types/q@^0.0.32", "_id": "@types/q@0.0.32", "_inBundle": false, "_integrity": "sha512-qYi3YV9inU/REEfxwVcGZzbS3KG/Xs90lv0Pr+lDtuVjBPGd1A+eciXzVSaRvLify132BfcvhvEjeVahrUl0Ug==", "_location": "/@types/q", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/q@^0.0.32", "name": "@types/q", "escapedName": "@types%2fq", "scope": "@types", "rawSpec": "^0.0.32", "saveSpec": null, "fetchSpec": "^0.0.32"}, "_requiredBy": ["/protractor"], "_resolved": "https://registry.npmjs.org/@types/q/-/q-0.0.32.tgz", "_shasum": "bd284e57c84f1325da702babfc82a5328190c0c5", "_spec": "@types/q@^0.0.32", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\protractor", "author": {"name": "<PERSON><PERSON>", "email": "https://github.com/bnemetchek"}, "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for Q", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/q", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typesPublisherContentHash": "6597b89d57d9ceec9ef1e6af32e2fd02f4582abaf309f8a76f6746ee1af830d5", "typings": "index.d.ts", "version": "0.0.32"}