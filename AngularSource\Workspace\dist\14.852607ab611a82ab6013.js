(window.webpackJsonp=window.webpackJsonp||[]).push([[14],{dj92:function(t,e,i){"use strict";i.r(e);var l=i("CcnG"),s=i("mrSG"),n=i("wd/R"),a=i.n(n),o=(i("tp8m"),i("447K")),r=i("MA5k"),d=i("GEQe"),h=i("+c5L"),c=i("g4+O"),u=i("ZYCi"),b=function(t){function e(e,i){var l=t.call(this,i,e)||this;return l.commonService=e,l.element=i,l.inputData=new o.G(l.commonService),l.widthData=new o.G(l.commonService),l.ordertData=new o.G(l.commonService),l.logicUpdate=new o.G(l.commonService),l.baseURL=o.Wb.getBaseURL(),l.jsonReader=new o.L,l.invalidComms=null,l.errorLocation=0,l.comboOpen=!1,l.comboChange=!1,l.firstLoad=!0,l.moduleId="PC",l.status=null,l.ccyFromPrevScreen=null,l.acctGrpFromPrevScreen=null,l.acctFromPrevScreen=null,l.account=null,l.entityFromPrevScreen=null,l.listEntitiesFromPrevScreen=null,l.valueDateFromPrevScreen=null,l.initialFilterFromPrevScreen=null,l.refFilterFromPrevScreen=null,l.archiveFromPrevScreen="",l.spreadOnlyFromPrevScreen="N",l.ccyThresholdFromPrevScreen="N",l.ccyMultiplierFromPrevScreen="N",l.timeFrameFromPrevScreen="E",l.inputSinceFromPrevScreen="",l.bVFromPrevScreen=!1,l.currencyCode=null,l.maxPage=0,l.lastNumber=0,l.isDisplayClicked=!1,l.isSpreadClicked=!1,l.fromSummary=!1,l.arrayOftabs=[],l.formatIso="yyyy-mm-dd",l.formatIsoTime="yyyy-mm-dd hh24:mi:ss",l.spreadId=null,l.selectedFilter=null,l.selectedSort=null,l.pageSize=1,l.screenName="",l.previousStatus="",l.statusChanged=!1,l.isDateChanged=!1,l.autoRefresh=null,l.interval=null,l.refusedStatus=[],l.count=2,l.isReleaseButtonClicked=!1,l.userFilter=null,l.order=null,l.ascDesc=null,l.selectedItemsListEntity=null,l.selectedItemsList=null,l.isBlockedStopped=null,l.arrayStatusPayments=[],l.blockedPaymentsId=[],l.columnsNewWidths="",l.columnsNewOrders="",l.columnDefinitionsTempArray=[],l.resetStepper=!1,l.menuAccessId=2,l.entityChanged=!1,l.swtAlert=new o.bb(e),l}return s.d(e,t),e.prototype.ngOnInit=function(){var t=this;if(instanceElement=this,this.commonGrid=this.gridCanvas.addChild(o.hb),this.commonGrid.paginationComponent=this.numstepper,this.commonGrid.onPaginationChanged=this.updataDataWithPagination.bind(this),this.commonGrid.onFilterChanged=this.updateData.bind(this),this.commonGrid.onSortChanged=this.updateData.bind(this),this.commonGrid.columnWidthChanged.subscribe(function(e){t.columnWidthChange(e)}),this.commonGrid.columnOrderChanged.subscribe(function(e){t.columnOrderChange(e)}),this.commonGrid.clientSideFilter=!1,this.commonGrid.clientSideSort=!1,this.commonGrid.allowMultipleSelection=!0,this.currencyLabel.text=o.Wb.getPredictMessage("dashboardDetails.currency.label",null),this.ccyCombo.toolTip=o.Wb.getPredictMessage("dashboardDetails.currency.tooltip",null),this.entityLabel.text=o.Wb.getPredictMessage("dashboardDetails.entity.label",null),this.entityCombo.toolTip=o.Wb.getPredictMessage("dashboardDetails.entity.tooltip",null),this.acagLabel.text=o.Wb.getPredictMessage("dashboardDetails.accountGroup.label",null),this.acctGrpCombo.toolTip=o.Wb.getPredictMessage("dashboardDetails.accountGroup.tooltip",null),this.accountLabel.text=o.Wb.getPredictMessage("dashboardDetails.account.label",null),this.accountCombo.toolTip=o.Wb.getPredictMessage("dashboardDetails.account.tooltip",null),this.statusLabel.text=o.Wb.getPredictMessage("dashboardDetails.status.label",null),this.statusCombo.toolTip=o.Wb.getPredictMessage("dashboardDetails.status.tooltip",null),this.blokedLabel.text=o.Wb.getPredictMessage("dashboardDetails.blocked.label",null),this.inputSinceLabel.text=o.Wb.getPredictMessage("dashboardDetails.inputSince.label",null),this.inputSince.toolTip=o.Wb.getPredictMessage("dashboardDetails.inputSince.tooltip",null),this.blokedCombo.toolTip=o.Wb.getPredictMessage("dashboardDetails.blocked.tooltip",null),this.dateLabel.text=o.Wb.getPredictMessage("dashboardDetails.date.label",null),this.startDate.toolTip=o.Wb.getPredictMessage("dashboardDetails.date.tooltip",null)+""+this.dateFormatUpper,this.applyCurrencyLabel.text=o.Wb.getPredictMessage("dashboardDetails.currencyThreshold.label",null),this.applyAbsoluteLabel.text=o.Wb.getPredictMessage("dashboardDetails.currencyMultiplier.label",null),this.timeFrameLabel.text=o.Wb.getPredictMessage("dashboardDetails.timeFrame.label",null),this.entityRadio.text=o.Wb.getPredictMessage("dashboardDetails.radioEntity.label",null),this.currencyRadio.text=o.Wb.getPredictMessage("dashboardDetails.radioCurrency.label",null),this.systemRadio.text=o.Wb.getPredictMessage("dashboardDetails.radiosystem.label",null),this.sodLabel.text=o.Wb.getPredictMessage("dashboardDetails.startDayBalance.label",null),this.confirmedLabel.text=o.Wb.getPredictMessage("dashboardDetails.confirmedCredits.label",null),this.creditLabel.text=o.Wb.getPredictMessage("dashboardDetails.creditLine.label",null),this.releasedPayLabel.text=o.Wb.getPredictMessage("dashboardDetails.releasedPayments.label",null),this.otherPaymentsLabel.text=o.Wb.getPredictMessage("dashboardDetails.otherPayments.label",null),this.excludeCLlabel.text=o.Wb.getPredictMessage("dashboardDetails.availableLiquidityEx.label",null),this.includeCLlabel.text=o.Wb.getPredictMessage("dashboardDetails.availableLiquidityInc.label",null),this.reservelabel.text=o.Wb.getPredictMessage("dashboardDetails.reserve.label",null),this.displayButton.label=o.Wb.getPredictMessage("dashboardDetails.buttonDisplay.label",null),this.displayButton.toolTip=o.Wb.getPredictMessage("dashboardDetails.buttonDisplay.tooltip",null),this.releaseButton.label=o.Wb.getPredictMessage("dashboardDetails.buttonRelease.label",null),this.releaseButton.toolTip=o.Wb.getPredictMessage("dashboardDetails.buttonRelease.tooltip",null),this.spreadButton.label=o.Wb.getPredictMessage("dashboardDetails.spreadDisplay.label",null),this.spreadButton.toolTip=o.Wb.getPredictMessage("dashboardDetails.spreadDisplay.tooltip",null),this.unStopButton.label=o.Wb.getPredictMessage("dashboardDetails.unStopButton.label",null),this.unStopButton.toolTip=o.Wb.getPredictMessage("dashboardDetails.unStopButton.tooltip",null),this.changeCatgButton.label=o.Wb.getPredictMessage("dashboardDetails.changeCatButton.label",null),this.changeCatgButton.toolTip=o.Wb.getPredictMessage("dashboardDetails.changeCatButton.tooltip",null),this.refreshButton.label=o.Wb.getPredictMessage("button.refresh",null),this.refreshButton.toolTip=o.Wb.getPredictMessage("tooltip.refresh",null),this.closeButton.label=o.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=o.Wb.getPredictMessage("tooltip.close",null),this.printIcon.toolTip=o.Wb.getPredictMessage("tooltip.print",null),o.v.subscribe(function(e){t.report(e)}),window.opener&&window.opener.instanceElement){var e=window.opener.instanceElement.getParamsFromParent?window.opener.instanceElement.getParamsFromParent():"";e&&(this.screenName=e[0].screenName?e[0].screenName:"grid",this.status=e[0].status?e[0].status:"W",this.ccyFromPrevScreen=e[0].currencyCode?e[0].currencyCode:"All",this.acctGrpFromPrevScreen=e[0].accountGroup?e[0].accountGroup:"All",this.acctFromPrevScreen=e[0].account?e[0].account:"All",this.entityFromPrevScreen=e[0].entity?e[0].entity:"All",this.listEntitiesFromPrevScreen=e[0].listEntity?e[0].listEntity:"",this.valueDateFromPrevScreen=e[0].valueDate?e[0].valueDate:"",this.initialFilterFromPrevScreen=e[0].initialFilter?e[0].initialFilter:"",this.refFilterFromPrevScreen=e[0].refFilter?e[0].refFilter:"",this.archiveFromPrevScreen=e[0].archive?e[0].archive:"",this.spreadOnlyFromPrevScreen=e[0].spreadOnly?e[0].spreadOnly:"N",this.ccyThresholdFromPrevScreen=e[0].ccyThreshold?e[0].ccyThreshold:"N",this.ccyMultiplierFromPrevScreen=e[0].ccyMultiplier?e[0].ccyMultiplier:"N",this.timeFrameFromPrevScreen=e[0].timeFrame?e[0].timeFrame:"E",this.inputSinceFromPrevScreen=e[0].inputSince?e[0].inputSince:"",this.bVFromPrevScreen=!!e[0].isBackValueClicked&&e[0].isBackValueClicked)}this.previousStatus=this.status,this.previousCcy=this.ccyFromPrevScreen,this.previousAcctGrp=this.acctGrpFromPrevScreen},e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.onLoad=function(){var t=this;this.applyAbsLabel=this.applyAbsoluteLabel.text,this.applyCurrencyCheck.selected="Y"==this.ccyThresholdFromPrevScreen,this.applyAbsoluteCheck.selected="Y"==this.ccyMultiplierFromPrevScreen,"C"==this.timeFrameFromPrevScreen?this.radioC.selected=!0:"S"==this.timeFrameFromPrevScreen?this.radioS.selected=!0:this.radioE.selected=!0,""!=this.listEntitiesFromPrevScreen&&(this.selectedEntity.text=this.listEntitiesFromPrevScreen,this.selectedItemsListEntity=this.listEntitiesFromPrevScreen),this.requestParams=[],this.bVFromPrevScreen&&(this.inputSinceHbox.visible=!0,this.inputSince.text=this.inputSinceFromPrevScreen,this.valueDateFromPrevScreen="",this.requestParams.blockedReason="BV"),this.actionMethod="method=getDashboardDetails",this.actionPath="dashboardPCM.do?",this.requestParams.method="getDashboardDetails",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},""!=this.screenName?(this.requestParams.currencyCode=this.ccyFromPrevScreen,this.requestParams.accountGroup=this.acctGrpFromPrevScreen,this.requestParams.account=this.acctFromPrevScreen,this.requestParams.entity=this.entityFromPrevScreen,this.requestParams.status=this.status,this.requestParams.date=this.valueDateFromPrevScreen):(this.requestParams.currencyCode="All",this.requestParams.accountGroup="All",this.requestParams.account="All",this.requestParams.entity="All",this.requestParams.status="W"),"search"==this.screenName&&(this.requestParams.initialFilter=this.initialFilterFromPrevScreen,this.requestParams.refFilter=this.refFilterFromPrevScreen,this.requestParams.archive=this.archiveFromPrevScreen),this.requestParams.applyCurrencyThreshold=this.ccyThresholdFromPrevScreen,this.requestParams.spreadOnly=this.spreadOnlyFromPrevScreen,this.requestParams.absoluteAmount=this.ccyMultiplierFromPrevScreen,this.requestParams.fromScreen=this.screenName,this.requestParams.timeFrame=this.timeFrameFromPrevScreen,this.requestParams.inputSince=this.inputSinceFromPrevScreen,this.inputData.cbFault=this.inputDataFault,this.inputData.encodeURL=!1,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.commonGrid.onRowClick=function(e){t.cellClickEventHandler(e)}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.dataExport.enabled=!1},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.dataExport.enabled=!0},e.prototype.inputDataResult=function(t){var e=this;if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON))if(this.jsonReader.getRequestReplyStatus()){if(!this.jsonReader.isDataBuilding()){if(this.menuAccessId=this.jsonReader.getScreenAttributes().menuaccess,this.sysDateFrmSession=this.jsonReader.getSingletons().sysDateFrmSession,this.jsonReader.getSingletons().multiplier?this.applyAbsoluteLabel.text=this.applyAbsLabel+" "+this.jsonReader.getSingletons().multiplier:this.applyAbsoluteLabel.text=this.applyAbsLabel,this.dateFormat=this.jsonReader.getScreenAttributes().dateformat,this.dateFormatUpper=this.dateFormat.toUpperCase(),this.startDate.toolTip=o.Wb.getPredictMessage("dashboardDetails.date.tooltip",null)+""+this.dateFormatUpper,this.startDate.formatString=this.dateFormat.toLowerCase(),this.fillComboData(),this.handleFieldsDisplay(),this.previousStatus!=this.statusCombo.selectedItem.value&&(this.columnsNewOrders="",this.columnsNewWidths=""),0==this.tabs.getTabChildren().length||this.entityChanged||["R","S","C","W","All"].indexOf(this.previousStatus)>-1&&"B"==this.statusCombo.selectedItem.value||["R","S","C","W","All"].indexOf(this.statusCombo.selectedItem.value)>-1&&"B"==this.previousStatus){if(this.tabs.getTabChildren().length>0)for(var i=0;i<this.arrayOftabs.length;i++)this.tabs.removeChild(this.arrayOftabs[i]);this.entityChanged=!1,this.currentDateTab=this.tabs.addChild(o.Xb),this.currentDateTabPlusOne=this.tabs.addChild(o.Xb),this.currentDateTabPlusTwo=this.tabs.addChild(o.Xb),this.currentDateTabPlusThree=this.tabs.addChild(o.Xb),this.currentDateTabPlusFour=this.tabs.addChild(o.Xb),this.currentDateTabPlusFive=this.tabs.addChild(o.Xb),this.currentDateTabPlusSix=this.tabs.addChild(o.Xb),this.currentDateTabPlusSeven=this.tabs.addChild(o.Xb),this.selectedDateTab=this.tabs.addChild(o.Xb),this.arrayOftabs=[this.currentDateTab,this.currentDateTabPlusOne,this.currentDateTabPlusTwo,this.currentDateTabPlusThree,this.currentDateTabPlusFour,this.currentDateTabPlusFive,this.currentDateTabPlusSix,this.currentDateTabPlusSeven,this.selectedDateTab];for(i=0;i<this.lastRecievedJSON.DashboardLevel2.tabs.row.length;i++)this.arrayOftabs[i].label=this.lastRecievedJSON.DashboardLevel2.tabs.row[i].dateLabel,this.arrayOftabs[i].businessday=this.lastRecievedJSON.DashboardLevel2.tabs.row[i].businessday,this.arrayOftabs[i].year=this.lastRecievedJSON.DashboardLevel2.tabs.row[i].content.substr(0,4),0==this.arrayOftabs[i].businessday?this.arrayOftabs[i].setTabHeaderStyle("color","darkgray"):this.arrayOftabs[i].setTabHeaderStyle("color","black");this.arrayOftabs[8].label=o.Wb.getPredictMessage("dashboard.selected.label",null)}setTimeout(function(){e.handleDate(),e.jsonReader.getSingletons().valueDate||(e.startDate.text="",e.tabs.selectedIndex=8)},0),this.previousStatus=this.statusCombo.selectedItem.value,this.previousCcy=this.ccyCombo.selectedItem.value,this.previousAcctGrp=this.acctGrpCombo.selectedItem.value;var l={columns:this.jsonReader.getColumnData()};this.commonGrid.CustomGrid(l),this.jsonReader.getGridData()&&this.jsonReader.getGridData().size?(this.commonGrid.gridData=this.jsonReader.getGridData(),this.commonGrid.setRowSize=this.jsonReader.getRowSize()):this.commonGrid.dataProvider=[],this.paySelectedIndex?(this.indexSelectedPay=this.commonGrid.gridData.findIndex(function(t){return t.payreq_id==e.paySelectedIndex}),this.indexSelectedPay>=0?this.commonGrid.selectedIndex=this.indexSelectedPay:(this.commonGrid.selectedIndex=-1,this.disableButtons())):this.commonGrid.selectedIndex=-1,this.jsonReader.getRowSize()>0?(this.pageSize=Number(this.jsonReader.getSingletons().pageSize),this.maxPage=this.jsonReader.getRowSize()/this.pageSize,this.maxPage<1?this.maxPage=1:this.maxPage=Math.ceil(this.maxPage),this.numstepper.processing=!1,this.numstepper.maximum=Number(this.maxPage),Number(this.maxPage)>0?this.numstepper.minimum=1:this.numstepper.minimum=0):(this.dataExport.enabled=!1,this.numstepper.value=0,this.numstepper.maximum=0),this.refreshRate=this.jsonReader.getSingletons().refresh,0==this.refreshRate&&(this.refreshRate=60);var s=this.refreshRate;clearInterval(this.interval),this.interval=setInterval(function(){e.updataDataWithPagination()},1e3*s)}}else this.swtAlert.error(o.Wb.getCommonMessages("alert.generic_exception"))},e.prototype.fillComboData=function(){this.ccyCombo.setComboData(this.jsonReader.getSelects(),!1),this.entityCombo.setComboData(this.jsonReader.getSelects(),!1),this.acctGrpCombo.setComboData(this.jsonReader.getSelects(),!1),this.accountCombo.setComboData(this.jsonReader.getSelects(),!1),this.statusCombo.setComboData(this.jsonReader.getSelects(),!0),this.blokedCombo.setComboData(this.jsonReader.getSelects(),!0),this.selectedCcy.text=this.ccyCombo.selectedItem.value,this.selectedAcctGrp.text=this.acctGrpCombo.selectedItem.value,this.selectedAccount.text=this.accountCombo.selectedItem.value,this.startDate.text=this.jsonReader.getSingletons().valueDate,this.inputSinceHbox.visible&&(this.inputSince.formatString=this.dateFormat.toLowerCase(),this.inputSince.text=this.jsonReader.getSingletons().inputSince,this.blokedCombo.selectedValue="BV"),-1!=this.jsonReader.getSingletons().selectedEntity.indexOf(",")?(this.entityMoreItemsButton.enabled=!0,this.selectedEntity.text=this.get2FirstItemsFromList(this.jsonReader.getSingletons().selectedEntity)):(this.entityMoreItemsButton.enabled=!1,this.selectedEntity.text=this.entityCombo.selectedItem.value)},e.prototype.getfilteredGridColumns=function(){var t="",e=[],i=this.commonGrid.getFilterColumns(),l=this.commonGrid.filteredGridColumns;try{for(var s=0;s<i.length;s++)e[s]=i[s].field;if(""!=l){var n=l.split("|");for(s=0;s<e.length;s++){if(""!=n[s])if("All"!=n[s]&&null!=n[s])"_"==e[s][e[s].length-1]&&(e[s]=e[s].slice(0,-1)),t="value_date"==e[s]||"release_date"==e[s]||"input_date"==e[s]||"stop_date"==e[s]||"cancel_date"==e[s]?t+e[s]+"=TO_DATE ('"+n[s]+"' , '"+this.formatIsoTime+"') and ":"required_release_time"==e[s]||"cutoff_time"==e[s]?t+"to_char("+e[s]+", 'hh24:mi')='"+n[s].substring(11,16)+"' and ":t+e[s]+"='"+n[s]+"' and "}}return t=t.substring(0,t.length-5)}catch(a){console.log("error",a)}},e.prototype.convertStringDateToIso=function(t){var e=a()(t,this.dateFormat.toUpperCase(),!0).utcOffset("+0100").toDate();return o.j.formatDate(e,"YYYY-MM-DD")},e.prototype.handleFieldsDisplay=function(){this.statusCombo.selectedItem&&this.ccyCombo.selectedItem&&("W"==this.statusCombo.selectedItem.value&&this.startDate.text==this.sysDateFrmSession&&"All"!=this.ccyCombo.selectedItem.value?(this.hboxTextInput.visible=!0,this.sodText.text=""!=this.jsonReader.getSingletons().sodBalance?this.jsonReader.getSingletons().sodBalance:"",this.confirmedText.text=this.jsonReader.getSingletons().confirmedCredit?this.jsonReader.getSingletons().confirmedCredit:"",this.creditText.text=this.jsonReader.getSingletons().creditLine?this.jsonReader.getSingletons().creditLine:" ",this.releasedPayText.text=this.jsonReader.getSingletons().releasePayment?this.jsonReader.getSingletons().releasePayment:"",this.otherPaymentsText.text=this.jsonReader.getSingletons().otherPayments?this.jsonReader.getSingletons().otherPayments:"",this.includeCLText.text=this.jsonReader.getSingletons().incCreditLine?this.jsonReader.getSingletons().incCreditLine:"",this.excludeCLText.text=this.jsonReader.getSingletons().exCreditLine?this.jsonReader.getSingletons().exCreditLine:"",this.reserveText.text=this.jsonReader.getSingletons().reserveBalanced?this.jsonReader.getSingletons().reserveBalanced:""):this.hboxTextInput.visible=!1,"B"==this.statusCombo.selectedItem.value?(this.blokedHbox.visible=!0,"BV"==this.blokedCombo.selectedItem.value?this.inputSinceHbox.visible=!0:this.inputSinceHbox.visible=!1):(this.blokedHbox.visible=!1,this.inputSinceHbox.visible=!1))},e.prototype.validateDate=function(t){this.validateDateField(t)&&this.updateData(null)},e.prototype.handleDate=function(){var t=!1,e=this.arrayOftabs[0].label+"/"+this.arrayOftabs[0].year,i=a()(this.sysDateFrmSession,this.dateFormat.toUpperCase()),l=a()(this.startDate.text,this.dateFormat.toUpperCase());if(l)for(var s=0;s<8;s++)if(i="B"==this.statusCombo.selectedValue?a()(e,this.dateFormat.toUpperCase()).add(s,"days"):a()(this.sysDateFrmSession,this.dateFormat.toUpperCase()).add(s,"days"),0==l.diff(i)){t=!0,this.tabs.selectedIndex=s;break}t||(this.tabs.selectedIndex=8)},e.prototype.validateDateField=function(t){try{var e=void 0,i=o.Wb.getPredictMessage("dashboardDetails.alert.dateFormat",null);if(t.text&&!(e=a()(t.text,this.dateFormat.toUpperCase(),!0)).isValid())return this.swtAlert.warning(i+this.dateFormat.toUpperCase()),!1;t.selectedDate=e.toDate()}catch(l){o.Wb.logError(l,o.Wb.SYSTEM_MODULE_ID,"PCDashboard"," validateDateField",this.errorLocation)}return!0},e.prototype.cellClickEventHandler=function(t){try{this.commonGrid.selectedIndex>=0?(this.enableDisableButtonsByStatus(),1==this.commonGrid.selectedIndices.length?(this.paySelectedIndex=this.commonGrid.dataProvider[this.commonGrid.selectedIndex].payreq_id,this.spreadButton.enabled=!0,this.displayButton.enabled=!0,this.printIcon.enabled=!0):this.commonGrid.selectedIndices.length>1&&(clearInterval(this.interval),this.paySelectedIndex=null,this.spreadButton.enabled=!1,this.displayButton.enabled=!1,this.changeCatgButton.enabled=!1,this.printIcon.enabled=!1)):(this.paySelectedIndex=null,this.spreadButton.enabled=!1,this.displayButton.enabled=!1,this.changeCatgButton.enabled=!1,this.printIcon.enabled=!1,this.releaseButton.enabled=!1,this.unStopButton.enabled=!1)}catch(e){o.Wb.logError(e,this.moduleId,"ClassName","cellClickEventHandler",this.errorLocation)}},e.prototype.columnWidthChange=function(t){var e=this;this.columnsNewWidths="";for(var i=this.commonGrid.gridObj.getColumns(),l=0;l<i.length;l++)"dummy"!=i[l].id&&(this.columnsNewWidths=this.columnsNewWidths+i[l].field+"="+i[l].width+",");this.columnsNewWidths=this.columnsNewWidths.substring(0,this.columnsNewWidths.length-1),this.requestParams=[],this.widthData.encodeURL=!1,this.actionMethod="method=saveColumnWidthBreakDownScreen",this.actionPath="dashboardPCM.do?",this.requestParams.method="saveColumnWidthBreakDownScreen",this.requestParams.width=this.columnsNewWidths,"<Multiple values>"==this.entityCombo.selectedLabel&&null!=this.selectedItemsListEntity?this.requestParams.entityid=this.selectedItemsListEntity:this.requestParams.entityid=this.entityCombo.selectedLabel,this.requestParams.status=this.statusCombo.selectedValue,this.widthData.cbStart=this.startOfComms.bind(this),this.widthData.cbStop=this.endOfComms.bind(this),this.widthData.cbResult=function(t){e.inputDataResultColumnsChange(t)},this.widthData.url=this.baseURL+this.actionPath+this.actionMethod,this.widthData.send(this.requestParams,null)},e.prototype.columnOrderChange=function(t){var e=this;this.columnsNewOrders="",this.columnDefinitionsTempArray=t;for(var i=0;i<this.columnDefinitionsTempArray.length;i++)"dummy"!=this.columnDefinitionsTempArray[i].id&&(this.columnsNewOrders=this.columnsNewOrders+this.columnDefinitionsTempArray[i].field+"="+this.columnDefinitionsTempArray[i].width+",");this.columnsNewOrders=this.columnsNewOrders.substring(0,this.columnsNewOrders.length-1),this.requestParams=[],this.ordertData.encodeURL=!1,this.actionMethod="method=saveColumnOrderBreakDownScreen",this.actionPath="dashboardPCM.do?",this.requestParams.method="saveColumnOrderBreakDownScreen",this.requestParams.order=this.columnsNewOrders,"<Multiple values>"==this.entityCombo.selectedLabel&&null!=this.selectedItemsListEntity?this.requestParams.entityid=this.selectedItemsListEntity:this.requestParams.entityid=this.entityCombo.selectedLabel,this.requestParams.status=this.statusCombo.selectedValue,this.ordertData.cbStart=this.startOfComms.bind(this),this.ordertData.cbStop=this.endOfComms.bind(this),this.ordertData.cbResult=function(t){e.inputDataResultColumnsChange(t)},this.ordertData.url=this.baseURL+this.actionPath+this.actionMethod,this.ordertData.send(this.requestParams,null)},e.prototype.inputDataResultColumnsChange=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),"Column width saved ok"==this.jsonReader.getRequestReplyMessage()||this.swtAlert.error(o.Wb.getCommonMessages("alert.generic_exception")))},e.prototype.updateData=function(t){var e=this;"keepSelected"!=t?this.paySelectedIndex=null:1==this.commonGrid.selectedIndices.length&&(this.paySelectedIndex=this.commonGrid.dataProvider[this.commonGrid.selectedIndex].payreq_id),this.userFilter=this.getfilteredGridColumns();for(var i=0;i<this.commonGrid.sorters.length;i++){"_"==this.commonGrid.sorters[i].columnId[this.commonGrid.sorters[i].columnId.length-1]&&(this.commonGrid.sorters[i].columnId=this.commonGrid.sorters[i].columnId.slice(0,-1)),this.order=this.commonGrid.sorters[i].columnId,this.ascDesc=this.commonGrid.sorters[i].direction?"Asc":"Desc"}this.requestParams=[],this.inputData.encodeURL=!1,this.actionMethod="method=getDashboardDetails",this.actionPath="dashboardPCM.do?",this.requestParams.method="getDashboardDetails",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.numstepper.value=1,e.inputDataResult(t),e.disableButtons()},this.requestParams.applyCurrencyThreshold=this.applyCurrencyCheck.selected?"Y":"N",this.requestParams.absoluteAmount=this.applyAbsoluteCheck.selected?"Y":"N",this.requestParams.spreadOnly=this.spreadOnlyFromPrevScreen,this.requestParams.currencyCode=this.ccyCombo.selectedLabel,this.requestParams.accountGroup=this.acctGrpCombo.selectedLabel,this.requestParams.account=this.accountCombo.selectedLabel,"<Multiple values>"==this.entityCombo.selectedLabel&&null!=this.selectedItemsListEntity?this.requestParams.entity=this.selectedItemsListEntity:this.requestParams.entity=this.entityCombo.selectedLabel,this.requestParams.status=this.statusCombo.selectedItem.value,this.requestParams.date=this.startDate.text,"B"==this.statusCombo.selectedItem.value&&(this.requestParams.blockedReason=this.blokedCombo.selectedItem.value),this.requestParams.fromScreen=this.screenName,this.requestParams.userFilter=this.userFilter,this.requestParams.order=this.order,this.requestParams.ascDesc=this.ascDesc,"search"==this.screenName&&(this.requestParams.initialFilter=this.initialFilterFromPrevScreen,this.requestParams.refFilter=this.refFilterFromPrevScreen,this.requestParams.archive=this.archiveFromPrevScreen),this.requestParams.timeFrame=this.getTimeFrame(),this.requestParams.inputSince=this.inputSinceHbox.visible?this.inputSince.text:"",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams,null),this.isDateChanged=!1},e.prototype.updataDataWithPagination=function(){var t,e,i=this;t=Number(this.numstepper.value-1)*this.pageSize+(1==this.numstepper.value?0:1),e=Number(this.numstepper.value)*this.pageSize,this.userFilter=this.getfilteredGridColumns();for(var l=0;l<this.commonGrid.sorters.length;l++){"_"==this.commonGrid.sorters[l].columnId[this.commonGrid.sorters[l].columnId.length-1]&&(this.commonGrid.sorters[l].columnId=this.commonGrid.sorters[l].columnId.slice(0,-1)),this.order=this.commonGrid.sorters[l].columnId,this.ascDesc=this.commonGrid.sorters[l].direction?"Asc":"Desc"}this.requestParams=[],this.inputData.encodeURL=!1,this.actionMethod="method=getDashboardDetails",this.actionPath="dashboardPCM.do?",this.requestParams.method="getDashboardDetails",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){i.inputDataResult(t),i.disableButtons()},this.requestParams.applyCurrencyThreshold=this.applyCurrencyCheck.selected?"Y":"N",this.requestParams.absoluteAmount=this.applyAbsoluteCheck.selected?"Y":"N",this.requestParams.spreadOnly=this.spreadOnlyFromPrevScreen,this.requestParams.currencyCode=this.ccyCombo.selectedLabel,this.requestParams.accountGroup=this.acctGrpCombo.selectedLabel,this.requestParams.account=this.accountCombo.selectedLabel,"<Multiple values>"==this.entityCombo.selectedLabel&&null!=this.selectedItemsListEntity?this.requestParams.entity=this.selectedItemsListEntity:this.requestParams.entity=this.entityCombo.selectedLabel,this.requestParams.status=this.statusCombo.selectedItem.value,this.requestParams.date=this.startDate.text,"B"==this.statusCombo.selectedItem.value&&(this.requestParams.blockedReason=this.blokedCombo.selectedItem.value),this.requestParams.fromScreen=this.screenName,this.requestParams.userFilter=this.userFilter,this.requestParams.order=this.order,this.requestParams.ascDesc=this.ascDesc,t<0?(this.requestParams.rowBegin=null,this.requestParams.rowEnd=null):(this.requestParams.rowBegin=t,this.requestParams.rowEnd=e),"search"==this.screenName&&(this.requestParams.initialFilter=this.initialFilterFromPrevScreen,this.requestParams.refFilter=this.refFilterFromPrevScreen,this.requestParams.archive=this.archiveFromPrevScreen),this.requestParams.timeFrame=this.getTimeFrame(),this.requestParams.inputSince=this.inputSinceHbox.visible?this.inputSince.text:"",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams,null),this.isDateChanged=!1},e.prototype.changeCombo=function(){this.ccyCombo.selectedItem&&this.previousCcy!=this.ccyCombo.selectedItem.value&&(this.acctGrpCombo.selectedLabel="All",this.accountCombo.selectedLabel="All"),this.previousAcctGrp!=this.acctGrpCombo.selectedItem.value&&(this.accountCombo.selectedLabel="All"),this.selectedCcy.text=this.ccyCombo.selectedItem?this.ccyCombo.selectedItem.value:"",this.selectedAcctGrp.text=this.acctGrpCombo.selectedItem.value,this.selectedAccount.text=this.accountCombo.selectedItem.value,this.selectedItemsList="<<Multiple Values>>"==this.entityCombo.selectedValue?this.selectedEntity.text:this.entityCombo.selectedLabel,this.handleFieldsDisplay(),this.updateData(null)},e.prototype.get2FirstItemsFromList=function(t){var e=(t.match(new RegExp(",","g"))||[]).length;return t&&-1!==t.indexOf(",")&&e>=2?t.split(",")[0].toString()+" ,"+t.split(",")[1].toString()+",...":t},e.prototype.changeComboEntity=function(t,e,i){try{"<<Multiple Values>>"==t.selectedValue?(e.enabled=!0,this.selectedItemsListEntity="",i.text=""):(e.enabled=!1,i.text=t.selectedValue,this.selectedItemsList=t.selectedLabel,this.selectedItemsListEntity=this.entityCombo.selectedItem?this.entityCombo.selectedItem.content:"",this.updateData(null),this.entityChanged=!0)}catch(l){console.log(l,this.moduleId,"PCDashboard","changeCombo")}},e.prototype.multipleListSelect=function(t,e){var i=this;try{this.win=o.Eb.createPopUp(this,r.a,{title:"Entity",operation:"in",dataSource:"fromDashboard",columnLabel:"entityList",columnCode:"entityList",viewOnly:!1}),this.win.enableResize=!1,this.win.id="listValuesPopup",this.win.width="500",this.win.height="500",this.win.showControls=!0,this.win.isModal=!0,this.win.onClose.subscribe(function(t){if(i.win.getChild().result){i.selectedItemsList=i.selectedItemsListEntity;var l=(i.selectedItemsList.match(new RegExp(",","g"))||[]).length;i.selectedItemsList&&-1!==i.selectedItemsList.indexOf(",")&&l>=2?e.text=i.selectedItemsList.split(",")[0].toString()+" ,"+i.selectedItemsList.split(",")[1].toString()+",...":e.text=i.selectedItemsList,i.entityChanged=!0,i.updateData(null)}}),this.win.display()}catch(l){console.log(l,this.moduleId,"Dashboard","multipleListSelect")}},e.prototype.previouStatus=function(){this.count%2==0&&(this.previousStatus=this.statusCombo.selectedItem.value),this.count++},e.prototype.previouCcy=function(){this.previousCcy=this.ccyCombo.selectedItem.value},e.prototype.previouAcctGrp=function(){this.previousAcctGrp=this.acctGrpCombo.selectedItem.value},e.prototype.closeHandler=function(){window.close()},e.prototype.displayPayment=function(t){this.isDisplayClicked=!0,o.x.call("paymentDisplay")},e.prototype.getParamsFromParent=function(){var t=[];return this.isSpreadClicked?(t=[{screenName:"view",spreadId:this.spreadId}],this.isSpreadClicked=!1):this.isDisplayClicked&&(t=[{payRequestId:this.commonGrid.selectedItem.payreq_id.content,timeFrame:this.getTimeFrame()}]),t},e.prototype.checkBeforeUnstop=function(t){var e=this;""==this.archiveFromPrevScreen&&(this.isReleaseButtonClicked=!1,this.requestParams=[],this.actionMethod="method=checkStopProcess",this.actionPath="dashboardPCM.do?",this.logicUpdate.cbResult=function(t){e.isUnStopProcessRunning(t)},this.requestParams.method="checkStopProcess",this.logicUpdate.cbStart=this.startOfComms.bind(this),this.logicUpdate.cbStop=this.endOfComms.bind(this),this.logicUpdate.encodeURL=!1,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams))},e.prototype.isUnStopProcessRunning=function(t){var e=o.Wb.getPredictMessage("dashboardDetails.stopProcessRun",null);o.c.okLabel=o.Wb.getPredictMessage("button.retry",null),o.c.cancelLabel=o.Wb.getPredictMessage("button.cancel",null);try{if(this.logicUpdate.isBusy())this.logicUpdate.cbStop();else{var i=t,l=new o.L;l.setInputJSON(i),"N"==l.getSingletons().isStopProcRun?this.unStopPay(event):this.swtAlert.confirm(e,"",o.c.OK|o.c.CANCEL,null,this.reCheckBeforeUnstop.bind(this))}o.c.okLabel=o.Wb.getPredictMessage("button.ok",null)}catch(s){}},e.prototype.reCheckBeforeUnstop=function(t){t.detail==o.c.OK?(clearInterval(this.interval),this.checkBeforeUnstop(t)):this.autoRefreshAfterStop()},e.prototype.checkBeforeRelease=function(t){var e=this;""==this.archiveFromPrevScreen&&(this.isReleaseButtonClicked=!0,this.requestParams=[],this.actionMethod="method=checkStopProcess",this.actionPath="dashboardPCM.do?",this.logicUpdate.cbResult=function(t){e.isStopProcessRunning(t)},this.requestParams.method="checkStopProcess",this.logicUpdate.cbStart=this.startOfComms.bind(this),this.logicUpdate.cbStop=this.endOfComms.bind(this),this.logicUpdate.encodeURL=!1,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams))},e.prototype.isStopProcessRunning=function(t){var e=o.Wb.getPredictMessage("dashboardDetails.stopProcessRun",null);o.c.okLabel=o.Wb.getPredictMessage("button.retry",null),o.c.cancelLabel=o.Wb.getPredictMessage("button.cancel",null);try{if(this.logicUpdate.isBusy())this.logicUpdate.cbStop();else{var i=t,l=new o.L;l.setInputJSON(i),"N"==l.getSingletons().isStopProcRun?this.releasePayment(event):this.swtAlert.confirm(e,"",o.c.OK|o.c.CANCEL,null,this.reCheckBeforeRelease.bind(this))}o.c.okLabel=o.Wb.getPredictMessage("button.ok",null)}catch(s){}},e.prototype.reCheckBeforeRelease=function(t){t.detail==o.c.OK?(clearInterval(this.interval),this.checkBeforeRelease(t)):this.autoRefreshAfterStop()},e.prototype.releasePayment=function(t){var e;clearInterval(this.interval),1==this.commonGrid.selectedItems.length?"B"==this.commonGrid.selectedItem.status.content||"S"==this.commonGrid.selectedItem.status.content?this.blockedStoppedRelease(this.commonGrid.selectedItem.payreq_id.content,!1):(e=o.Wb.getPredictMessage("dashboardDetails.alert.releaseSelectedPay",null),this.swtAlert.confirm(e,"",o.c.YES|o.c.CANCEL,null,this.confirmRelease.bind(this))):this.confirmReleaseMultiplePayments(t)},e.prototype.blockedStoppedRelease=function(t,e){var i=this;this.requestParams=[],this.actionMethod="method=checkBlockedStoppedPay",this.actionPath="dashboardPCM.do?",this.logicUpdate.cbResult=function(t){e?i.isBlockedStoppedResultFromMultipleSelection(t):i.isBlockedStoppedResult(t)},this.requestParams.paymentId=t?t.toString():"",this.requestParams.method="checkBlockedStoppedPay",this.logicUpdate.cbStart=this.startOfComms.bind(this),this.logicUpdate.cbStop=this.endOfComms.bind(this),this.logicUpdate.encodeURL=!1,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams)},e.prototype.secondConfirmRelease=function(t){var e=null;t.detail==o.c.YES?(e=o.Wb.getPredictMessage("dashboardDetails.alert.releaseBlockedPay",null),this.swtAlert.confirm(e,"",o.c.YES|o.c.CANCEL,null,this.confirmRelease.bind(this))):this.autoRefreshAfterStop()},e.prototype.confirmRelease=function(t){var e=this;try{if(t.detail==o.c.YES){this.requestParams=[];var i=[],l=[];this.isReleaseButtonClicked=!0,this.totalSelectedPayments=this.commonGrid.selectedItems.length;for(var s=0;s<this.totalSelectedPayments;s++)"S"!=this.commonGrid.selectedItems[s].status.content&&"W"!=this.commonGrid.selectedItems[s].status.content&&"B"!=this.commonGrid.selectedItems[s].status.content||(l.push(this.commonGrid.selectedItems[s].status.content),i.push(this.commonGrid.selectedItems[s].payreq_id.content));1==this.commonGrid.selectedItems.length&&(this.actionMethod="method=unStopReleasePayment",this.actionPath="dashboardPCM.do?",this.logicUpdate.cbResult=function(t){e.logicUpdateResult(t)},this.requestParams.paymentId=i.toString(),this.requestParams.previousStatus=l.toString(),this.requestParams.paymentAction="R",this.requestParams.method="unStopReleasePayment",this.logicUpdate.cbStart=this.startOfComms.bind(this),this.logicUpdate.cbStop=this.endOfComms.bind(this),this.logicUpdate.encodeURL=!1,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams))}else this.autoRefreshAfterStop()}catch(n){console.log("error",n)}},e.prototype.confirmReleaseMultiplePayments=function(t){try{this.requestParams=[],this.blockedPaymentsId=[],this.totalSelectedPayments=this.commonGrid.selectedItems.length;for(var e=0;e<this.totalSelectedPayments;e++)"B"==this.commonGrid.selectedItems[e].status.content&&this.blockedPaymentsId.push(this.commonGrid.selectedItems[e].payreq_id.content);this.blockedStoppedRelease(this.blockedPaymentsId,!0)}catch(i){console.log("error",i)}},e.prototype.unStopPay=function(t){var e="";e=1==this.commonGrid.selectedItems.length?o.Wb.getPredictMessage("dashboardDetails.alert.unstopPay",null):o.Wb.getPredictMessage("dashboardDetails.alert.unstopPays",null),this.swtAlert.confirm(e,"",o.c.YES|o.c.CANCEL,null,this.confirmUnstop.bind(this))},e.prototype.confirmUnstop=function(t){var e=this;if(t.detail==o.c.YES){clearInterval(this.interval);for(var i=[],l=[],s=0;s<this.commonGrid.selectedItems.length;s++)"S"==this.commonGrid.selectedItems[s].status.content&&(i.push(this.commonGrid.selectedItems[s].status.content),l.push(this.commonGrid.selectedItems[s].payreq_id.content));this.isReleaseButtonClicked=!1,this.actionMethod="method=unStopReleasePayment",this.actionPath="dashboardPCM.do?",this.logicUpdate.cbResult=function(t){e.logicUpdateResult(t)},this.requestParams.paymentId=l.toString(),this.requestParams.previousStatus=i.toString(),this.requestParams.paymentAction="U",this.requestParams.method="unStopReleasePayment",this.logicUpdate.cbStart=this.startOfComms.bind(this),this.logicUpdate.cbStop=this.endOfComms.bind(this),this.logicUpdate.encodeURL=!1,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams)}else this.autoRefreshAfterStop()},e.prototype.autoRefreshAfterStop=function(){var t=this,e=this.refreshRate;clearInterval(this.interval),this.interval=setInterval(function(){t.updataDataWithPagination()},1e3*e)},e.prototype.logicUpdateResult=function(t){try{var e=o.Wb.getPredictMessage("dashboardDetails.alert.inputEngineError",null),i=o.Wb.getPredictMessage("dashboardDetails.alert.error",null),l=o.Wb.getPredictMessage("dashboardDetails.title.errorPay",null);if(this.logicUpdate.isBusy())this.logicUpdate.cbStop();else{var s=t,n=new o.L;n.setInputJSON(s),"ERROR_PAYMENT_ENGINE"==n.getRequestReplyMessage()?this.swtAlert.error(e):"GENERIC_ERROR"==n.getRequestReplyMessage()?this.swtAlert.error(i):(t.ErrorsOfUpdate.grid&&n.getGridData().size>0&&(this.win=o.Eb.createPopUp(this,h.a,{title:l,rowsData:n.getGridData(),columnsData:n.getColumnData(),isReleaseAction:this.isReleaseButtonClicked}),this.win.width="1010",this.win.height="400",this.win.enableResize=!1,this.win.showControls=!0,this.win.display()),this.updateData(null))}}catch(a){}},e.prototype.isBlockedStoppedResult=function(t){var e,i="",l="",s=[],n=[],a=[],r=o.Wb.getPredictMessage("dashboardDetails.alert.releaseBlockedPay",null),d=o.Wb.getPredictMessage("dashboardDetails.alert.lockedPay",null),h=o.Wb.getPredictMessage("dashboardDetails.paymentStopped",null),c=o.Wb.getPredictMessage("dashboardDetails.paymentWasStopped",null),u=o.Wb.getPredictMessage("dashboardDetails.until",null),b=o.Wb.getPredictMessage("dashboardDetails.wishContinue",null);try{if(this.logicUpdate.isBusy())this.logicUpdate.cbStop();else{var m=t,p=new o.L;p.setInputJSON(m),e=p.getSingletons().payIdRule.replace("{","").replace("}","");var g=[];if(e){g.push(e.split("#")),g=g[0];for(var y=0;y<g.length;y++)s.push(g[y].split("|")[0]),n.push(g[y].split("|")[1]),a.push(g[y].split("|")[2]);for(var w=0;w<s.length;w++)"Y"==s[w]&&null!=n[w]?l=l+h+" "+n[w]+"<br/>":"P"==s[w]&&null!=n[w]&&null!=a[w]?l=l+c+" "+n[w]+" "+u+" "+a[w]+"<br/>":"N"==s[w]?(i=r,this.swtAlert.confirm(i,"",o.c.YES|o.c.CANCEL,null,this.confirmRelease.bind(this))):"L"==s[w]&&(i=d,this.swtAlert.warning(i));""!=l&&(l+=b,"S"==this.commonGrid.selectedItem.status.content?this.swtAlert.confirm(l,"",o.c.YES|o.c.CANCEL,null,this.confirmRelease.bind(this)):this.swtAlert.confirm(l,"",o.c.YES|o.c.CANCEL,null,this.secondConfirmRelease.bind(this)))}}}catch(C){}},e.prototype.isBlockedStoppedResultFromMultipleSelection=function(t){var e,i=[],l=0,s=o.Wb.getPredictMessage("dashboardDetails.title.releaseConfirmation",null);this.arrayStatusPayments=[];try{if(this.logicUpdate.isBusy())this.logicUpdate.cbStop();else{var n=t,a=new o.L;a.setInputJSON(n),e=a.getSingletons().payIdRule.replace("{","").replace("}","");var r=[];if(e){r.push(e.split(";")),r=r[0];for(var h=0;h<r.length;h++)i.push(r[h].substr(0,1))}for(h=0;h<this.commonGrid.selectedItems.length;h++)"B"==this.commonGrid.selectedItems[h].status.content?("Y"==i[l]?this.arrayStatusPayments.push({id:this.commonGrid.selectedItems[h].payreq_id.content,status:this.commonGrid.selectedItems[h].status.content,blockReason:this.commonGrid.selectedItems[h].block_reason_code?this.commonGrid.selectedItems[h].block_reason_code.content:"",alreadyStopped:"Stopped"}):"P"==i[l]?this.arrayStatusPayments.push({id:this.commonGrid.selectedItems[h].payreq_id.content,status:this.commonGrid.selectedItems[h].status.content,blockReason:this.commonGrid.selectedItems[h].block_reason_code?this.commonGrid.selectedItems[h].block_reason_code.content:"",alreadyStopped:"PrevStopped"}):"N"==i[l]&&this.arrayStatusPayments.push({id:this.commonGrid.selectedItems[h].payreq_id.content,status:this.commonGrid.selectedItems[h].status.content,blockReason:this.commonGrid.selectedItems[h].block_reason_code?this.commonGrid.selectedItems[h].block_reason_code.content:"",alreadyStopped:""}),l++):this.arrayStatusPayments.push({id:this.commonGrid.selectedItems[h].payreq_id.content,status:this.commonGrid.selectedItems[h].status.content,blockReason:"",alreadyStopped:""});this.win=o.Eb.createPopUp(this,d.a,{title:s,statusPayArray:this.arrayStatusPayments}),this.win.isModal=!0,this.win.width="430",this.win.height="460",this.win.enableResize=!1,this.win.showControl,this.win.display()}}catch(c){console.log("error ",c)}},e.prototype.spreadDisplay=function(t){var e=this;try{var i=o.Wb.getPredictMessage("dashboardDetails.alert.noSpread",null),l=o.Wb.getPredictMessage("dashboardDetails.alert.noAccount",null);this.isSpreadClicked=!0,this.commonGrid.selectedItem.acc_grp_id.content?(this.actionMethod="method=getSpreadId",this.actionPath="dashboardPCM.do?",this.inputData.cbResult=function(t){e.jsonReader.setInputJSON(t),e.spreadId=e.jsonReader.getSingletons().spreadId,e.spreadId?o.x.call("spreadDisplay"):e.swtAlert.warning(i,null,o.c.OK)},this.requestParams.accountGroupId=this.commonGrid.selectedItem.acc_grp_id.content,this.requestParams.method="getSpreadId",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)):this.swtAlert.warning(l)}catch(s){console.log("error",s)}},e.prototype.changeCategory=function(t){},e.prototype.refreshParent=function(t,e){var i=this;this.requestParams=[],this.actionMethod="method=updatePaymentCategory",this.actionPath="dashboardPCM.do?",this.inputData.cbResult=function(t){i.updateData(null)},this.requestParams.paymentId=e,this.requestParams.categoryId=t,this.requestParams.method="updateCategory",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.inputDataFault=function(t){this.swtAlert.error(o.Wb.getPredictMessage("dashboardDetails.genericError",null))},e.prototype.doHelp=function(){try{o.x.call("help")}catch(t){}},e.prototype.print=function(){o.x.call("printPage")},e.prototype.popupClosed=function(t){this.close()},e.prototype.enableDisableButtonsByStatus=function(){if(this.archiveFromPrevScreen)this.releaseButton.enabled=!1,this.unStopButton.enabled=!1,this.changeCatgButton.enabled=!1;else{if(0==this.menuAccessId){for(var t=0;t<this.commonGrid.selectedItems.length;t++)if(1==this.commonGrid.selectedItems.length)"S"==this.commonGrid.selectedItem.status.content||"S"==this.statusCombo.selectedItem.value?this.unStopButton.enabled=!0:this.unStopButton.enabled=!1;else{if("S"==this.commonGrid.selectedItems[t].status.content||"S"==this.statusCombo.selectedItem.value){this.unStopButton.enabled=!0;break}this.unStopButton.enabled=!1}for(t=0;t<this.commonGrid.selectedItems.length;t++)if(1==this.commonGrid.selectedItems.length)"R"==this.commonGrid.selectedItem.status.content||"C"==this.commonGrid.selectedItem.status.content?this.releaseButton.enabled=!1:this.releaseButton.enabled=!0;else{if("R"!=this.commonGrid.selectedItems[t].status.content&&"C"!=this.commonGrid.selectedItems[t].status.content){this.releaseButton.enabled=!0;break}this.releaseButton.enabled=!1}}else this.releaseButton.enabled=!1,this.unStopButton.enabled=!1;this.changeCatgButton.enabled=!1}},e.prototype.tabIndexchangeHandler=function(){try{this.statusChanged=!1,this.isDateChanged=!0;var t=void 0,e=this.arrayOftabs[0].label+"/"+this.arrayOftabs[0].year;(t="B"==this.statusCombo.selectedValue?new Date(o.j.parseDate(e,this.dateFormat.toUpperCase())):new Date(o.j.parseDate(this.sysDateFrmSession,this.dateFormat.toUpperCase()))).setHours(12,0,0),"Selected"!=this.tabs.selectedLabel&&(this.startDate.selectedDate=new Date(t.setDate(t.getDate()+this.tabs.selectedIndex))),this.updateData(null)}catch(i){console.log("error",i)}},e.prototype.enableDisableTextInput=function(t){this.sodText.enabled=t,this.confirmedText.enabled=t,this.creditText.enabled=t,this.releasedPayText.enabled=t,this.otherPaymentsText.enabled=t,this.excludeCLText.enabled=t,this.includeCLText.enabled=t,this.reserveText.enabled=t},e.prototype.disableButtons=function(){(null==this.paySelectedIndex||this.indexSelectedPay<0)&&(this.spreadButton.enabled=!1,this.displayButton.enabled=!1,this.changeCatgButton.enabled=!1,this.printIcon.enabled=!1,this.releaseButton.enabled=!1,this.unStopButton.enabled=!1)},e.prototype.report=function(t){var e,i,l,s,n,a,r,d,h,u,b,m,p,g=this,y="",w="",C="",S="",P="",f=o.Wb.getPredictMessage("exportPages.title",null);try{i=this.applyCurrencyCheck.selected?"Y":"N",l=this.applyAbsoluteCheck.selected?"Y":"N",s=this.ccyCombo.selectedLabel,n=this.acctGrpCombo.selectedLabel,a=this.accountCombo.selectedLabel,e="<Multiple values>"==this.entityCombo.selectedLabel&&null!=this.selectedItemsListEntity?this.selectedItemsListEntity:this.entityCombo.selectedLabel,m=Number(this.numstepper.value-1)*this.pageSize+(1==this.numstepper.value?0:1),p=Number(this.numstepper.value)*this.pageSize,r=this.statusCombo.selectedItem.value,"B"==this.statusCombo.selectedItem.value&&(y=this.blokedCombo.selectedItem.value),d=this.screenName,h=this.userFilter,u=this.order,b=this.ascDesc,"search"==this.screenName?(w=this.initialFilterFromPrevScreen,C=this.refFilterFromPrevScreen,S=this.archiveFromPrevScreen):(w="",C="",S=""),this.win=o.Eb.createPopUp(this,c.a,{}),this.win.enableResize=!1,this.win.title=f,this.win.width="220",this.win.height="180",this.win.showControls=!0,this.win.isModal=!0,this.win.onClose.subscribe(function(c){g.win.getChild().result&&(P="current"==g.win.getChild().result?"current":"all",o.x.call("report",e,g.selectedEntity.text,t,g.dateFormat,g.startDate.text,s,n,a,r,i,l,y,g.spreadOnlyFromPrevScreen,d,h,u,b,w,C,S,m,p,g.sodText.text,g.confirmedText.text,g.creditText.text,g.releasedPayText.text,g.otherPaymentsText.text,g.excludeCLText.text,g.includeCLText.text,g.reserveText.text,P,g.getTimeFrame(),g.inputSince.text))}),this.win.display()}catch(v){console.log("error",v),o.Wb.logError(v,this.moduleId,"Dashboard","report",this.errorLocation)}},e.prototype.getTimeFrame=function(){return this.radioC.selected?"C":this.radioE.selected?"E":"S"},e}(o.yb),m=[{path:"",component:b}],p=(u.l.forChild(m),function(){return function(){}}()),g=i("pMnS"),y=i("RChO"),w=i("t6HQ"),C=i("WFGK"),S=i("5FqG"),P=i("Ip0R"),f=i("gIcY"),v=i("t/Na"),x=i("sE5F"),I=i("OzfB"),D=i("T7CS"),L=i("S7LP"),R=i("6aHO"),B=i("WzUx"),k=i("A7o+"),F=i("zCE2"),T=i("Jg5P"),M=i("3R0m"),A=i("hhbb"),G=i("5rxC"),W=i("Fzqc"),q=i("21Lb"),N=i("hUWP"),J=i("3pJQ"),O=i("V9q+"),E=i("VDKW"),U=i("kXfT"),_=i("BGbe");i.d(e,"PCMBreakdownMonitorModuleNgFactory",function(){return Z}),i.d(e,"RenderType_PCMBreakdownMonitor",function(){return j}),i.d(e,"View_PCMBreakdownMonitor_0",function(){return Y}),i.d(e,"View_PCMBreakdownMonitor_Host_0",function(){return K}),i.d(e,"PCMBreakdownMonitorNgFactory",function(){return V});var Z=l.Gb(p,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[g.a,y.a,w.a,C.a,S.Cb,S.Pb,S.r,S.rc,S.s,S.Ab,S.Bb,S.Db,S.qd,S.Hb,S.k,S.Ib,S.Nb,S.Ub,S.yb,S.Jb,S.v,S.A,S.e,S.c,S.g,S.d,S.Kb,S.f,S.ec,S.Wb,S.bc,S.ac,S.sc,S.fc,S.lc,S.jc,S.Eb,S.Fb,S.mc,S.Lb,S.nc,S.Mb,S.dc,S.Rb,S.b,S.ic,S.Yb,S.Sb,S.kc,S.y,S.Qb,S.cc,S.hc,S.pc,S.oc,S.xb,S.p,S.q,S.o,S.h,S.j,S.w,S.Zb,S.i,S.m,S.Vb,S.Ob,S.Gb,S.Xb,S.t,S.tc,S.zb,S.n,S.qc,S.a,S.z,S.rd,S.sd,S.x,S.td,S.gc,S.l,S.u,S.ud,S.Tb,V]],[3,l.n],l.J]),l.Rb(4608,P.m,P.l,[l.F,[2,P.u]]),l.Rb(4608,f.c,f.c,[]),l.Rb(4608,f.p,f.p,[]),l.Rb(4608,v.j,v.p,[P.c,l.O,v.n]),l.Rb(4608,v.q,v.q,[v.j,v.o]),l.Rb(5120,v.a,function(t){return[t,new o.tb]},[v.q]),l.Rb(4608,v.m,v.m,[]),l.Rb(6144,v.k,null,[v.m]),l.Rb(4608,v.i,v.i,[v.k]),l.Rb(6144,v.b,null,[v.i]),l.Rb(4608,v.f,v.l,[v.b,l.B]),l.Rb(4608,v.c,v.c,[v.f]),l.Rb(4608,x.c,x.c,[]),l.Rb(4608,x.g,x.b,[]),l.Rb(5120,x.i,x.j,[]),l.Rb(4608,x.h,x.h,[x.c,x.g,x.i]),l.Rb(4608,x.f,x.a,[]),l.Rb(5120,x.d,x.k,[x.h,x.f]),l.Rb(5120,l.b,function(t,e){return[I.j(t,e)]},[P.c,l.O]),l.Rb(4608,D.a,D.a,[]),l.Rb(4608,L.a,L.a,[]),l.Rb(4608,R.a,R.a,[l.n,l.L,l.B,L.a,l.g]),l.Rb(4608,B.c,B.c,[l.n,l.g,l.B]),l.Rb(4608,B.e,B.e,[B.c]),l.Rb(4608,k.l,k.l,[]),l.Rb(4608,k.h,k.g,[]),l.Rb(4608,k.c,k.f,[]),l.Rb(4608,k.j,k.d,[]),l.Rb(4608,k.b,k.a,[]),l.Rb(4608,k.k,k.k,[k.l,k.h,k.c,k.j,k.b,k.m,k.n]),l.Rb(4608,B.i,B.i,[[2,k.k]]),l.Rb(4608,B.r,B.r,[B.L,[2,k.k],B.i]),l.Rb(4608,B.t,B.t,[]),l.Rb(4608,B.w,B.w,[]),l.Rb(1073742336,u.l,u.l,[[2,u.r],[2,u.k]]),l.Rb(1073742336,P.b,P.b,[]),l.Rb(1073742336,f.n,f.n,[]),l.Rb(1073742336,f.l,f.l,[]),l.Rb(1073742336,F.a,F.a,[]),l.Rb(1073742336,T.a,T.a,[]),l.Rb(1073742336,f.e,f.e,[]),l.Rb(1073742336,M.a,M.a,[]),l.Rb(1073742336,k.i,k.i,[]),l.Rb(1073742336,B.b,B.b,[]),l.Rb(1073742336,v.e,v.e,[]),l.Rb(1073742336,v.d,v.d,[]),l.Rb(1073742336,x.e,x.e,[]),l.Rb(1073742336,A.b,A.b,[]),l.Rb(1073742336,G.b,G.b,[]),l.Rb(1073742336,I.c,I.c,[]),l.Rb(1073742336,W.a,W.a,[]),l.Rb(1073742336,q.d,q.d,[]),l.Rb(1073742336,N.c,N.c,[]),l.Rb(1073742336,J.a,J.a,[]),l.Rb(1073742336,O.a,O.a,[[2,I.g],l.O]),l.Rb(1073742336,E.b,E.b,[]),l.Rb(1073742336,U.a,U.a,[]),l.Rb(1073742336,_.b,_.b,[]),l.Rb(1073742336,o.Tb,o.Tb,[]),l.Rb(1073742336,p,p,[]),l.Rb(256,v.n,"XSRF-TOKEN",[]),l.Rb(256,v.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,k.m,void 0,[]),l.Rb(256,k.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,u.i,function(){return[[{path:"",component:b}]]},[])])}),H=[[".bigLabel[_ngcontent-%COMP%]{width:160px!important}.labelForm[_ngcontent-%COMP%]{width:130px!important;height:19px!important;padding-left:5px!important}"]],j=l.Hb({encapsulation:0,styles:H,data:{}});function Y(t){return l.dc(0,[l.Zb(*********,1,{_container:0}),l.Zb(*********,2,{gridCanvas:0}),l.Zb(*********,3,{totalCanvas:0}),l.Zb(*********,4,{globalCanvas:0}),l.Zb(*********,5,{entityCombo:0}),l.Zb(*********,6,{ccyCombo:0}),l.Zb(*********,7,{statusCombo:0}),l.Zb(*********,8,{accountCombo:0}),l.Zb(*********,9,{blokedCombo:0}),l.Zb(*********,10,{acctGrpCombo:0}),l.Zb(*********,11,{applyCurrencyLabel:0}),l.Zb(*********,12,{loadingImage:0}),l.Zb(*********,13,{currencyLabel:0}),l.Zb(*********,14,{selectedCcy:0}),l.Zb(*********,15,{acagLabel:0}),l.Zb(*********,16,{accountLabel:0}),l.Zb(*********,17,{statusLabel:0}),l.Zb(*********,18,{dateLabel:0}),l.Zb(*********,19,{startDate:0}),l.Zb(*********,20,{inputSince:0}),l.Zb(*********,21,{entityLabel:0}),l.Zb(*********,22,{selectedEntity:0}),l.Zb(*********,23,{selectedAccount:0}),l.Zb(*********,24,{selectedAcctGrp:0}),l.Zb(*********,25,{blokedLabel:0}),l.Zb(*********,26,{inputSinceLabel:0}),l.Zb(*********,27,{applyAbsoluteLabel:0}),l.Zb(*********,28,{timeFrameLabel:0}),l.Zb(*********,29,{entityRadio:0}),l.Zb(*********,30,{currencyRadio:0}),l.Zb(*********,31,{systemRadio:0}),l.Zb(*********,32,{sodLabel:0}),l.Zb(*********,33,{confirmedLabel:0}),l.Zb(*********,34,{creditLabel:0}),l.Zb(*********,35,{releasedPayLabel:0}),l.Zb(*********,36,{otherPaymentsLabel:0}),l.Zb(*********,37,{excludeCLlabel:0}),l.Zb(*********,38,{includeCLlabel:0}),l.Zb(*********,39,{reservelabel:0}),l.Zb(*********,40,{sodText:0}),l.Zb(*********,41,{confirmedText:0}),l.Zb(*********,42,{creditText:0}),l.Zb(*********,43,{releasedPayText:0}),l.Zb(*********,44,{otherPaymentsText:0}),l.Zb(*********,45,{excludeCLText:0}),l.Zb(*********,46,{includeCLText:0}),l.Zb(*********,47,{reserveText:0}),l.Zb(*********,48,{applyCurrencyCheck:0}),l.Zb(*********,49,{applyAbsoluteCheck:0}),l.Zb(*********,50,{releaseButton:0}),l.Zb(*********,51,{refreshButton:0}),l.Zb(*********,52,{displayButton:0}),l.Zb(*********,53,{changeCatgButton:0}),l.Zb(*********,54,{spreadButton:0}),l.Zb(*********,55,{closeButton:0}),l.Zb(*********,56,{upButton:0}),l.Zb(*********,57,{downButton:0}),l.Zb(*********,58,{settingButton:0}),l.Zb(*********,59,{rejectButton:0}),l.Zb(*********,60,{unStopButton:0}),l.Zb(*********,61,{helpIcon:0}),l.Zb(*********,62,{printIcon:0}),l.Zb(*********,63,{highValueButton:0}),l.Zb(*********,64,{trimButton:0}),l.Zb(*********,65,{entityMoreItemsButton:0}),l.Zb(*********,66,{tabs:0}),l.Zb(*********,67,{blokedHbox:0}),l.Zb(*********,68,{inputSinceHbox:0}),l.Zb(*********,69,{hboxTextInput:0}),l.Zb(*********,70,{numstepper:0}),l.Zb(*********,71,{timeFrameRadioGroup:0}),l.Zb(*********,72,{radioC:0}),l.Zb(*********,73,{radioE:0}),l.Zb(*********,74,{radioS:0}),l.Zb(*********,75,{dataExport:0}),(t()(),l.Jb(75,0,null,null,215,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var l=!0,s=t.component;"creationComplete"===e&&(l=!1!==s.onLoad()&&l);return l},S.ad,S.hb)),l.Ib(76,4440064,null,0,o.yb,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(77,0,null,0,213,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,S.od,S.vb)),l.Ib(78,4440064,null,0,o.ec,[l.r,o.i,l.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),l.Jb(79,0,null,0,172,"SwtCanvas",[["height","230"],["minWidth","1200"],["width","100%"]],null,null,null,S.Nc,S.U)),l.Ib(80,4440064,null,0,o.db,[l.r,o.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),l.Jb(81,0,null,0,170,"HBox",[["height","100%"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(82,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(83,0,null,0,71,"HBox",[["width","50%"]],null,null,null,S.Dc,S.K)),l.Ib(84,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(85,0,null,0,69,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,S.od,S.vb)),l.Ib(86,4440064,null,0,o.ec,[l.r,o.i,l.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(87,0,null,0,9,"HBox",[["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(88,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(89,0,null,0,1,"SwtLabel",[["id","currencyLabel"],["width","100"]],null,null,null,S.Yc,S.fb)),l.Ib(90,4440064,[[13,4],["currencyLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(91,0,null,0,1,"SwtComboBox",[["dataLabel","currencyList"],["id","ccyCombo"],["width","200"]],null,[[null,"change"],[null,"click"],["window","mousewheel"]],function(t,e,i){var s=!0,n=t.component;"window:mousewheel"===e&&(s=!1!==l.Tb(t,92).mouseWeelEventHandler(i.target)&&s);"change"===e&&(s=!1!==n.changeCombo()&&s);"click"===e&&(s=!1!==n.previouCcy()&&s);return s},S.Pc,S.W)),l.Ib(92,4440064,[[6,4],["ccyCombo",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(93,0,null,0,1,"spacer",[["width","27"]],null,null,null,S.Kc,S.R)),l.Ib(94,4440064,null,0,o.Y,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(95,0,null,0,1,"SwtLabel",[["id","selectedCcy"]],null,null,null,S.Yc,S.fb)),l.Ib(96,4440064,[[14,4],["selectedCcy",4]],0,o.vb,[l.r,o.i],{id:[0,"id"]},null),(t()(),l.Jb(97,0,null,0,9,"HBox",[["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(98,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(99,0,null,0,1,"SwtLabel",[["id","entityLabel"],["width","100"]],null,null,null,S.Yc,S.fb)),l.Ib(100,4440064,[[21,4],["entityLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(101,0,null,0,1,"SwtComboBox",[["dataLabel","entityList"],["id","entityCombo"],["width","200"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var s=!0,n=t.component;"window:mousewheel"===e&&(s=!1!==l.Tb(t,102).mouseWeelEventHandler(i.target)&&s);"change"===e&&(s=!1!==n.changeComboEntity(l.Tb(t,102),l.Tb(t,104),l.Tb(t,106))&&s);return s},S.Pc,S.W)),l.Ib(102,4440064,[[5,4],["entityCombo",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(103,0,null,0,1,"SwtButton",[["buttonMode","false"],["enabled","false"],["id","entityMoreItemsButton"],["label","..."],["width","25"]],null,[[null,"click"]],function(t,e,i){var s=!0,n=t.component;"click"===e&&(s=!1!==n.multipleListSelect(l.Tb(t,102).id,l.Tb(t,106))&&s);return s},S.Mc,S.T)),l.Ib(104,4440064,[[65,4],["entityMoreItemsButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(105,0,null,0,1,"SwtLabel",[["id","selectedEntity"],["marginRight","2"],["textAlign","left"]],null,null,null,S.Yc,S.fb)),l.Ib(106,4440064,[[22,4],["selectedEntity",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],marginRight:[2,"marginRight"]},null),(t()(),l.Jb(107,0,null,0,9,"HBox",[["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(108,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(109,0,null,0,1,"SwtLabel",[["id","acagLabel"],["width","100"]],null,null,null,S.Yc,S.fb)),l.Ib(110,4440064,[[15,4],["acagLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(111,0,null,0,1,"SwtComboBox",[["dataLabel","AcctGrpList"],["id","acctGrpCombo"],["width","200"]],null,[[null,"change"],[null,"click"],["window","mousewheel"]],function(t,e,i){var s=!0,n=t.component;"window:mousewheel"===e&&(s=!1!==l.Tb(t,112).mouseWeelEventHandler(i.target)&&s);"change"===e&&(s=!1!==n.changeCombo()&&s);"click"===e&&(s=!1!==n.previouAcctGrp()&&s);return s},S.Pc,S.W)),l.Ib(112,4440064,[[10,4],["acctGrpCombo",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(113,0,null,0,1,"spacer",[["width","27"]],null,null,null,S.Kc,S.R)),l.Ib(114,4440064,null,0,o.Y,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(115,0,null,0,1,"SwtLabel",[["id","selectedAcctGrp"],["width","200"]],null,null,null,S.Yc,S.fb)),l.Ib(116,4440064,[[24,4],["selectedAcctGrp",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(117,0,null,0,9,"HBox",[["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(118,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(119,0,null,0,1,"SwtLabel",[["id","accountLabel"],["width","100"]],null,null,null,S.Yc,S.fb)),l.Ib(120,4440064,[[16,4],["accountLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(121,0,null,0,1,"SwtComboBox",[["dataLabel","AcctList"],["id","accountCombo"],["width","200"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var s=!0,n=t.component;"window:mousewheel"===e&&(s=!1!==l.Tb(t,122).mouseWeelEventHandler(i.target)&&s);"change"===e&&(s=!1!==n.changeCombo()&&s);return s},S.Pc,S.W)),l.Ib(122,4440064,[[8,4],["accountCombo",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(123,0,null,0,1,"spacer",[["width","27"]],null,null,null,S.Kc,S.R)),l.Ib(124,4440064,null,0,o.Y,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(125,0,null,0,1,"SwtLabel",[["id","selectedAccount"],["width","200"]],null,null,null,S.Yc,S.fb)),l.Ib(126,4440064,[[23,4],["selectedAccount",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(127,0,null,0,13,"HBox",[["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(128,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(129,0,null,0,5,"HBox",[],null,null,null,S.Dc,S.K)),l.Ib(130,4440064,null,0,o.C,[l.r,o.i],null,null),(t()(),l.Jb(131,0,null,0,1,"SwtLabel",[["id","statusLabel"],["width","100"]],null,null,null,S.Yc,S.fb)),l.Ib(132,4440064,[[17,4],["statusLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(133,0,null,0,1,"SwtComboBox",[["dataLabel","statusList"],["id","statusCombo"],["width","200"]],null,[[null,"change"],[null,"click"],["window","mousewheel"]],function(t,e,i){var s=!0,n=t.component;"window:mousewheel"===e&&(s=!1!==l.Tb(t,134).mouseWeelEventHandler(i.target)&&s);"change"===e&&(s=!1!==n.changeCombo()&&s);"click"===e&&(s=!1!==n.previouStatus()&&s);return s},S.Pc,S.W)),l.Ib(134,4440064,[[7,4],["statusCombo",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(135,0,null,0,5,"HBox",[["visible","false"]],null,null,null,S.Dc,S.K)),l.Ib(136,4440064,[[67,4],["blokedHbox",4]],0,o.C,[l.r,o.i],{visible:[0,"visible"]},null),(t()(),l.Jb(137,0,null,0,1,"SwtLabel",[["id","blokedLabel"],["width","70"]],null,null,null,S.Yc,S.fb)),l.Ib(138,4440064,[[25,4],["blokedLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(139,0,null,0,1,"SwtComboBox",[["dataLabel","blockedList"],["id","blokedCombo"],["width","190"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var s=!0,n=t.component;"window:mousewheel"===e&&(s=!1!==l.Tb(t,140).mouseWeelEventHandler(i.target)&&s);"change"===e&&(s=!1!==n.changeCombo()&&s);return s},S.Pc,S.W)),l.Ib(140,4440064,[[9,4],["blokedCombo",4]],0,o.gb,[l.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(141,0,null,0,13,"HBox",[["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(142,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(143,0,null,0,1,"SwtLabel",[["id","dateLabel"],["width","100"]],null,null,null,S.Yc,S.fb)),l.Ib(144,4440064,[[18,4],["dateLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(145,0,null,0,3,"HBox",[["width","200"]],null,null,null,S.Dc,S.K)),l.Ib(146,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(147,0,null,0,1,"SwtDateField",[["id","startDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"]],function(t,e,i){var s=!0,n=t.component;"change"===e&&(s=!1!==n.validateDate(l.Tb(t,148))&&s);return s},S.Tc,S.ab)),l.Ib(148,4308992,[[19,4],["startDate",4]],0,o.lb,[l.r,o.i,l.T],{restrict:[0,"restrict"],id:[1,"id"],editable:[2,"editable"],width:[3,"width"]},{changeEventOutPut:"change"}),(t()(),l.Jb(149,0,null,0,5,"HBox",[["visible","false"]],null,null,null,S.Dc,S.K)),l.Ib(150,4440064,[[68,4],["inputSinceHbox",4]],0,o.C,[l.r,o.i],{visible:[0,"visible"]},null),(t()(),l.Jb(151,0,null,0,1,"SwtLabel",[["marginLeft","5"],["width","75"]],null,null,null,S.Yc,S.fb)),l.Ib(152,4440064,[[26,4],["inputSinceLabel",4]],0,o.vb,[l.r,o.i],{width:[0,"width"],marginLeft:[1,"marginLeft"]},null),(t()(),l.Jb(153,0,null,0,1,"SwtDateField",[["id","inputSince"],["restrict","0-9/"],["width","70"]],null,[[null,"change"]],function(t,e,i){var s=!0,n=t.component;"change"===e&&(s=!1!==n.validateDate(l.Tb(t,154))&&s);return s},S.Tc,S.ab)),l.Ib(154,4308992,[[20,4],["inputSince",4]],0,o.lb,[l.r,o.i,l.T],{restrict:[0,"restrict"],id:[1,"id"],editable:[2,"editable"],width:[3,"width"]},{changeEventOutPut:"change"}),(t()(),l.Jb(155,0,null,0,44,"HBox",[["width","20%"]],null,null,null,S.Dc,S.K)),l.Ib(156,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(157,0,null,0,42,"VBox",[["height","100%"],["verticalGap","3"],["width","100%"]],null,null,null,S.od,S.vb)),l.Ib(158,4440064,null,0,o.ec,[l.r,o.i,l.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(159,0,null,0,5,"HBox",[["height","26"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(160,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(161,0,null,0,1,"SwtLabel",[["textAlign","right"],["width","170"]],null,null,null,S.Yc,S.fb)),l.Ib(162,4440064,[[11,4],["applyCurrencyLabel",4]],0,o.vb,[l.r,o.i],{textAlign:[0,"textAlign"],width:[1,"width"]},null),(t()(),l.Jb(163,0,null,0,1,"SwtCheckBox",[["paddingLeft","15"]],null,[[null,"change"]],function(t,e,i){var l=!0,s=t.component;"change"===e&&(l=!1!==s.updateData("keepSelected")&&l);return l},S.Oc,S.V)),l.Ib(164,4440064,[[48,4],["applyCurrencyCheck",4]],0,o.eb,[l.r,o.i],{paddingLeft:[0,"paddingLeft"]},{change_:"change"}),(t()(),l.Jb(165,0,null,0,5,"HBox",[["height","26"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(166,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(167,0,null,0,1,"SwtLabel",[["textAlign","right"],["width","170"]],null,null,null,S.Yc,S.fb)),l.Ib(168,4440064,[[27,4],["applyAbsoluteLabel",4]],0,o.vb,[l.r,o.i],{textAlign:[0,"textAlign"],width:[1,"width"]},null),(t()(),l.Jb(169,0,null,0,1,"SwtCheckBox",[["paddingLeft","15"]],null,[[null,"change"]],function(t,e,i){var l=!0,s=t.component;"change"===e&&(l=!1!==s.updateData("keepSelected")&&l);return l},S.Oc,S.V)),l.Ib(170,4440064,[[49,4],["applyAbsoluteCheck",4]],0,o.eb,[l.r,o.i],{paddingLeft:[0,"paddingLeft"]},{change_:"change"}),(t()(),l.Jb(171,0,null,0,3,"HBox",[["height","21"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(172,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(173,0,null,0,1,"SwtLabel",[["textAlign","right"],["width","170"]],null,null,null,S.Yc,S.fb)),l.Ib(174,4440064,[[28,4],["timeFrameLabel",4]],0,o.vb,[l.r,o.i],{textAlign:[0,"textAlign"],width:[1,"width"]},null),(t()(),l.Jb(175,0,null,0,22,"VBox",[["height","60"],["paddingLeft","65"],["width","100%"]],null,null,null,S.od,S.vb)),l.Ib(176,4440064,null,0,o.ec,[l.r,o.i,l.T],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),l.Jb(177,0,null,0,20,"SwtRadioButtonGroup",[["align","vertical"],["id","timeFrameRadioGroup"]],null,[[null,"change"]],function(t,e,i){var l=!0,s=t.component;"change"===e&&(l=!1!==s.updateData("keepSelected")&&l);return l},S.ed,S.lb)),l.Ib(178,4440064,[[71,4],["timeFrameRadioGroup",4]],1,o.Hb,[v.c,l.r,o.i],{id:[0,"id"],align:[1,"align"]},{change_:"change"}),l.Zb(603979776,76,{radioItems:1}),(t()(),l.Jb(180,0,null,0,5,"HBox",[],null,null,null,S.Dc,S.K)),l.Ib(181,4440064,null,0,o.C,[l.r,o.i],null,null),(t()(),l.Jb(182,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["textAlign","right"],["width","80"]],null,null,null,S.Yc,S.fb)),l.Ib(183,4440064,[[29,4],["entityRadio",4]],0,o.vb,[l.r,o.i],{textAlign:[0,"textAlign"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),l.Jb(184,0,null,0,1,"SwtRadioItem",[["groupName","timeFrameRadioGroup"],["id","radioE"],["selected","true"],["value","entity"]],null,null,null,S.fd,S.mb)),l.Ib(185,4440064,[[73,4],["radioE",4]],0,o.Ib,[l.r,o.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"],selected:[3,"selected"]},null),(t()(),l.Jb(186,0,null,0,5,"HBox",[],null,null,null,S.Dc,S.K)),l.Ib(187,4440064,null,0,o.C,[l.r,o.i],null,null),(t()(),l.Jb(188,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["textAlign","right"],["width","80"]],null,null,null,S.Yc,S.fb)),l.Ib(189,4440064,[[30,4],["currencyRadio",4]],0,o.vb,[l.r,o.i],{textAlign:[0,"textAlign"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),l.Jb(190,0,null,0,1,"SwtRadioItem",[["groupName","timeFrameRadioGroup"],["id","radioC"],["value","currency"]],null,null,null,S.fd,S.mb)),l.Ib(191,4440064,[[72,4],["radioC",4]],0,o.Ib,[l.r,o.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),l.Jb(192,0,null,0,5,"HBox",[],null,null,null,S.Dc,S.K)),l.Ib(193,4440064,null,0,o.C,[l.r,o.i],null,null),(t()(),l.Jb(194,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["textAlign","right"],["width","80"]],null,null,null,S.Yc,S.fb)),l.Ib(195,4440064,[[31,4],["systemRadio",4]],0,o.vb,[l.r,o.i],{textAlign:[0,"textAlign"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),l.Jb(196,0,null,0,1,"SwtRadioItem",[["groupName","timeFrameRadioGroup"],["id","radioS"],["value","system"]],null,null,null,S.fd,S.mb)),l.Ib(197,4440064,[[74,4],["radioS",4]],0,o.Ib,[l.r,o.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),l.Jb(198,0,null,0,1,"VBox",[],null,null,null,S.od,S.vb)),l.Ib(199,4440064,null,0,o.ec,[l.r,o.i,l.T],null,null),(t()(),l.Jb(200,0,null,0,51,"HBox",[["horizontalAlign","right"],["visible","false"],["width","35%"]],null,null,null,S.Dc,S.K)),l.Ib(201,4440064,[[69,4],["hboxTextInput",4]],0,o.C,[l.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],visible:[2,"visible"]},null),(t()(),l.Jb(202,0,null,0,49,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,S.od,S.vb)),l.Ib(203,4440064,null,0,o.ec,[l.r,o.i,l.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(204,0,null,0,5,"HBox",[["height","28"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(205,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(206,0,null,0,1,"SwtLabel",[["id","sodLabel"],["textAlign","right"],["width","65%"]],null,null,null,S.Yc,S.fb)),l.Ib(207,4440064,[[32,4],["sodLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),l.Jb(208,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","sodText"],["styleName","textInputPadding"],["textAlign","right"],["width","35%"]],null,null,null,S.kd,S.sb)),l.Ib(209,4440064,[[40,4],["sodText",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],styleName:[2,"styleName"],width:[3,"width"],enabled:[4,"enabled"]},null),(t()(),l.Jb(210,0,null,0,5,"HBox",[["height","28"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(211,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(212,0,null,0,1,"SwtLabel",[["id","confirmedLabel"],["textAlign","right"],["width","65%"]],null,null,null,S.Yc,S.fb)),l.Ib(213,4440064,[[33,4],["confirmedLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),l.Jb(214,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","confirmedText"],["styleName","textInputPadding"],["textAlign","right"],["width","35%"]],null,null,null,S.kd,S.sb)),l.Ib(215,4440064,[[41,4],["confirmedText",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],styleName:[2,"styleName"],width:[3,"width"],enabled:[4,"enabled"]},null),(t()(),l.Jb(216,0,null,0,5,"HBox",[["height","28"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(217,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(218,0,null,0,1,"SwtLabel",[["id","creditLabel"],["textAlign","right"],["width","65%"]],null,null,null,S.Yc,S.fb)),l.Ib(219,4440064,[[34,4],["creditLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),l.Jb(220,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","creditText"],["styleName","textInputPadding"],["textAlign","right"],["width","35%"]],null,null,null,S.kd,S.sb)),l.Ib(221,4440064,[[42,4],["creditText",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],styleName:[2,"styleName"],width:[3,"width"],enabled:[4,"enabled"]},null),(t()(),l.Jb(222,0,null,0,5,"HBox",[["height","28"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(223,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(224,0,null,0,1,"SwtLabel",[["id","releasedPayLabel"],["textAlign","right"],["width","65%"]],null,null,null,S.Yc,S.fb)),l.Ib(225,4440064,[[35,4],["releasedPayLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),l.Jb(226,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","releasedPayText"],["styleName","textInputPadding"],["textAlign","right"],["width","35%"]],null,null,null,S.kd,S.sb)),l.Ib(227,4440064,[[43,4],["releasedPayText",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],styleName:[2,"styleName"],width:[3,"width"],enabled:[4,"enabled"]},null),(t()(),l.Jb(228,0,null,0,5,"HBox",[["height","28"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(229,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(230,0,null,0,1,"SwtLabel",[["id","otherPaymentsLabel"],["textAlign","right"],["width","65%"]],null,null,null,S.Yc,S.fb)),l.Ib(231,4440064,[[36,4],["otherPaymentsLabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),l.Jb(232,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","otherPaymentsText"],["styleName","textInputPadding"],["textAlign","right"],["width","35%"]],null,null,null,S.kd,S.sb)),l.Ib(233,4440064,[[44,4],["otherPaymentsText",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],styleName:[2,"styleName"],width:[3,"width"],enabled:[4,"enabled"]},null),(t()(),l.Jb(234,0,null,0,5,"HBox",[["height","28"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(235,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(236,0,null,0,1,"SwtLabel",[["id","excludeCLlabel"],["textAlign","right"],["width","65%"]],null,null,null,S.Yc,S.fb)),l.Ib(237,4440064,[[37,4],["excludeCLlabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),l.Jb(238,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","excludeCLText"],["styleName","textInputPadding"],["textAlign","right"],["width","35%"]],null,null,null,S.kd,S.sb)),l.Ib(239,4440064,[[45,4],["excludeCLText",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],styleName:[2,"styleName"],width:[3,"width"],enabled:[4,"enabled"]},null),(t()(),l.Jb(240,0,null,0,5,"HBox",[["height","28"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(241,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(242,0,null,0,1,"SwtLabel",[["id","includeCLlabel"],["textAlign","right"],["width","65%"]],null,null,null,S.Yc,S.fb)),l.Ib(243,4440064,[[38,4],["includeCLlabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),l.Jb(244,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","includeCLText"],["styleName","textInputPadding"],["textAlign","right"],["width","35%"]],null,null,null,S.kd,S.sb)),l.Ib(245,4440064,[[46,4],["includeCLText",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],styleName:[2,"styleName"],width:[3,"width"],enabled:[4,"enabled"]},null),(t()(),l.Jb(246,0,null,0,5,"HBox",[["height","28"],["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(247,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(248,0,null,0,1,"SwtLabel",[["id","reservelabel"],["textAlign","right"],["width","65%"]],null,null,null,S.Yc,S.fb)),l.Ib(249,4440064,[[39,4],["reservelabel",4]],0,o.vb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),l.Jb(250,0,null,0,1,"SwtTextInput",[["enabled","false"],["id","reserveText"],["styleName","textInputPadding"],["textAlign","right"],["width","35%"]],null,null,null,S.kd,S.sb)),l.Ib(251,4440064,[[47,4],["reserveText",4]],0,o.Rb,[l.r,o.i],{id:[0,"id"],textAlign:[1,"textAlign"],styleName:[2,"styleName"],width:[3,"width"],enabled:[4,"enabled"]},null),(t()(),l.Jb(252,0,null,0,2,"SwtTabNavigator",[["borderBottom","false"],["height","2%"],["id","tabs"],["minWidth","1200"],["width","100%"]],null,[[null,"onChange"]],function(t,e,i){var l=!0,s=t.component;"onChange"===e&&(l=!1!==s.tabIndexchangeHandler()&&l);return l},S.id,S.pb)),l.Ib(253,4440064,[[66,4],["tabs",4]],1,o.Ob,[l.r,o.i,l.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"],borderBottom:[4,"borderBottom"]},{onChange_:"onChange"}),l.Zb(603979776,77,{tabChildren:1}),(t()(),l.Jb(255,0,null,0,5,"VBox",[["height","65%"],["minWidth","1200"],["paddingLeft","5"],["styleName","borderVBox"],["verticalGap","1"],["width","100%"]],null,null,null,S.od,S.vb)),l.Ib(256,4440064,null,0,o.ec,[l.r,o.i,l.T],{verticalGap:[0,"verticalGap"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],minWidth:[4,"minWidth"],paddingLeft:[5,"paddingLeft"]},null),(t()(),l.Jb(257,0,null,0,1,"SwtCommonGridPagination",[],null,null,null,S.Qc,S.Y)),l.Ib(258,2211840,[[70,4],["numstepper",4]],0,o.ib,[v.c,l.r],null,null),(t()(),l.Jb(259,0,null,0,1,"SwtCanvas",[["height","95%"],["id","gridCanvas"],["width","100%"]],null,null,null,S.Nc,S.U)),l.Ib(260,4440064,[[2,4],["gridCanvas",4]],0,o.db,[l.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(261,0,null,0,29,"SwtCanvas",[["height","5%"],["id","canvasButtons"],["minWidth","1200"],["width","100%"]],null,null,null,S.Nc,S.U)),l.Ib(262,4440064,null,0,o.db,[l.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"]},null),(t()(),l.Jb(263,0,null,0,27,"HBox",[["width","100%"]],null,null,null,S.Dc,S.K)),l.Ib(264,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"]},null),(t()(),l.Jb(265,0,null,0,15,"HBox",[["paddingLeft","5"],["width","80%"]],null,null,null,S.Dc,S.K)),l.Ib(266,4440064,null,0,o.C,[l.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(267,0,null,0,1,"SwtButton",[["enabled","false"],["id","displayButton"],["width","120"]],null,[[null,"click"]],function(t,e,i){var l=!0,s=t.component;"click"===e&&(l=!1!==s.displayPayment(i)&&l);return l},S.Mc,S.T)),l.Ib(268,4440064,[[52,4],["displayButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(269,0,null,0,1,"SwtButton",[["enabled","false"],["id","releaseButton"],["width","120"]],null,[[null,"click"]],function(t,e,i){var l=!0,s=t.component;"click"===e&&(l=!1!==s.checkBeforeRelease(i)&&l);return l},S.Mc,S.T)),l.Ib(270,4440064,[[50,4],["releaseButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(271,0,null,0,1,"SwtButton",[["enabled","false"],["id","spreadButton"],["width","120"]],null,[[null,"click"]],function(t,e,i){var l=!0,s=t.component;"click"===e&&(l=!1!==s.spreadDisplay(i)&&l);return l},S.Mc,S.T)),l.Ib(272,4440064,[[54,4],["spreadButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(273,0,null,0,1,"SwtButton",[["enabled","false"],["id","unStopButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var l=!0,s=t.component;"click"===e&&(l=!1!==s.checkBeforeUnstop(i)&&l);return l},S.Mc,S.T)),l.Ib(274,4440064,[[60,4],["unStopButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),l.Jb(275,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeCatgButton"],["width","120"]],null,[[null,"click"]],function(t,e,i){var l=!0,s=t.component;"click"===e&&(l=!1!==s.changeCategory(i)&&l);return l},S.Mc,S.T)),l.Ib(276,4440064,[[53,4],["changeCatgButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),l.Jb(277,0,null,0,1,"SwtButton",[["id","refreshButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var l=!0,s=t.component;"click"===e&&(l=!1!==s.updataDataWithPagination()&&l);return l},S.Mc,S.T)),l.Ib(278,4440064,[[51,4],["refreshButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(279,0,null,0,1,"SwtButton",[["id","closeButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var l=!0,s=t.component;"click"===e&&(l=!1!==s.closeHandler()&&l);return l},S.Mc,S.T)),l.Ib(280,4440064,[[55,4],["closeButton",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(281,0,null,0,9,"HBox",[["horizontalAlign","right"],["width","20%"]],null,null,null,S.Dc,S.K)),l.Ib(282,4440064,null,0,o.C,[l.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),l.Jb(283,0,null,0,1,"DataExport",[["id","dataExport"]],null,null,null,S.Sc,S.Z)),l.Ib(284,4440064,[[75,4],["dataExport",4]],0,o.kb,[o.i,l.r],{id:[0,"id"]},null),(t()(),l.Jb(285,0,null,0,1,"SwtButton",[["buttonMode","true"],["enabled","false"],["id","printIcon"],["styleName","printIcon"]],null,[[null,"click"]],function(t,e,i){var l=!0,s=t.component;"click"===e&&(l=!1!==s.print()&&l);return l},S.Mc,S.T)),l.Ib(286,4440064,[[62,4],["printIcon",4]],0,o.cb,[l.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(287,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var l=!0,s=t.component;"click"===e&&(l=!1!==s.doHelp()&&l);return l},S.Wc,S.db)),l.Ib(288,4440064,[[61,4],["helpIcon",4]],0,o.rb,[l.r,o.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),l.Jb(289,0,null,0,1,"SwtLoadingImage",[],null,null,null,S.Zc,S.gb)),l.Ib(290,114688,[[12,4],["loadingImage",4]],0,o.xb,[l.r],null,null)],function(t,e){t(e,76,0,"100%","100%");t(e,78,0,"vBox1","100%","100%","5","5","5","5");t(e,80,0,"100%","230","1200");t(e,82,0,"100%","100%");t(e,84,0,"50%");t(e,86,0,"0","100%","100%");t(e,88,0,"100%");t(e,90,0,"currencyLabel","100");t(e,92,0,"currencyList","200","ccyCombo");t(e,94,0,"27");t(e,96,0,"selectedCcy");t(e,98,0,"100%");t(e,100,0,"entityLabel","100");t(e,102,0,"entityList","200","entityCombo");t(e,104,0,"entityMoreItemsButton","25","false","...","false");t(e,106,0,"selectedEntity","left","2");t(e,108,0,"100%");t(e,110,0,"acagLabel","100");t(e,112,0,"AcctGrpList","200","acctGrpCombo");t(e,114,0,"27");t(e,116,0,"selectedAcctGrp","200");t(e,118,0,"100%");t(e,120,0,"accountLabel","100");t(e,122,0,"AcctList","200","accountCombo");t(e,124,0,"27");t(e,126,0,"selectedAccount","200");t(e,128,0,"100%"),t(e,130,0);t(e,132,0,"statusLabel","100");t(e,134,0,"statusList","200","statusCombo");t(e,136,0,"false");t(e,138,0,"blokedLabel","70");t(e,140,0,"blockedList","190","blokedCombo");t(e,142,0,"100%");t(e,144,0,"dateLabel","100");t(e,146,0,"200");t(e,148,0,"0-9/","startDate",!0,"70");t(e,150,0,"false");t(e,152,0,"75","5");t(e,154,0,"0-9/","inputSince",!0,"70");t(e,156,0,"20%");t(e,158,0,"3","100%","100%");t(e,160,0,"100%","26");t(e,162,0,"right","170");t(e,164,0,"15");t(e,166,0,"100%","26");t(e,168,0,"right","170");t(e,170,0,"15");t(e,172,0,"100%","21");t(e,174,0,"right","170");t(e,176,0,"100%","60","65");t(e,178,0,"timeFrameRadioGroup","vertical"),t(e,181,0);t(e,183,0,"right","80","normal");t(e,185,0,"radioE","timeFrameRadioGroup","entity","true"),t(e,187,0);t(e,189,0,"right","80","normal");t(e,191,0,"radioC","timeFrameRadioGroup","currency"),t(e,193,0);t(e,195,0,"right","80","normal");t(e,197,0,"radioS","timeFrameRadioGroup","system"),t(e,199,0);t(e,201,0,"right","35%","false");t(e,203,0,"0","100%","100%");t(e,205,0,"100%","28");t(e,207,0,"sodLabel","right","65%");t(e,209,0,"sodText","right","textInputPadding","35%","false");t(e,211,0,"100%","28");t(e,213,0,"confirmedLabel","right","65%");t(e,215,0,"confirmedText","right","textInputPadding","35%","false");t(e,217,0,"100%","28");t(e,219,0,"creditLabel","right","65%");t(e,221,0,"creditText","right","textInputPadding","35%","false");t(e,223,0,"100%","28");t(e,225,0,"releasedPayLabel","right","65%");t(e,227,0,"releasedPayText","right","textInputPadding","35%","false");t(e,229,0,"100%","28");t(e,231,0,"otherPaymentsLabel","right","65%");t(e,233,0,"otherPaymentsText","right","textInputPadding","35%","false");t(e,235,0,"100%","28");t(e,237,0,"excludeCLlabel","right","65%");t(e,239,0,"excludeCLText","right","textInputPadding","35%","false");t(e,241,0,"100%","28");t(e,243,0,"includeCLlabel","right","65%");t(e,245,0,"includeCLText","right","textInputPadding","35%","false");t(e,247,0,"100%","28");t(e,249,0,"reservelabel","right","65%");t(e,251,0,"reserveText","right","textInputPadding","35%","false");t(e,253,0,"tabs","100%","2%","1200","false");t(e,256,0,"1","borderVBox","100%","65%","1200","5"),t(e,258,0);t(e,260,0,"gridCanvas","100%","95%");t(e,262,0,"canvasButtons","100%","5%","1200");t(e,264,0,"100%");t(e,266,0,"80%","5");t(e,268,0,"displayButton","120","false",!0);t(e,270,0,"releaseButton","120","false",!0);t(e,272,0,"spreadButton","120","false",!0);t(e,274,0,"unStopButton","70","false");t(e,276,0,"changeCatgButton","120","false");t(e,278,0,"refreshButton","70",!0);t(e,280,0,"closeButton","70",!0);t(e,282,0,"right","20%");t(e,284,0,"dataExport");t(e,286,0,"printIcon","printIcon","false","true");t(e,288,0,"helpIcon"),t(e,290,0)},null)}function K(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-pcdashboard-details",[],null,null,null,Y,j)),l.Ib(1,4440064,null,0,b,[o.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var V=l.Fb("app-pcdashboard-details",b,K,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);