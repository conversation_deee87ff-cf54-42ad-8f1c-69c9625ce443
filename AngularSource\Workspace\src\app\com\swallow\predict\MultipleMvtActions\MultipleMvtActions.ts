import { Component, OnInit, ModuleWithProviders, NgModule, ViewChild, ElementRef } from '@angular/core';
import { ExternalInterface, SwtToolBoxModule, SwtModule, SwtCanvas, SwtLabel, SwtComboBox, SwtCheckBox, SwtFieldSet, SwtRadioButtonGroup, SwtRadioItem, SwtButton, SwtLoadingImage, SwtImage, CommonService, SwtAlert, SwtTextInput, SwtCommonGrid, SwtUtil, JSONReader, HTTPComms, SwtDateField, StringUtils, Alert, HBox, SwtCommonGridPagination, SwtPopUpManager, GridItem, SwtTextArea } from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
declare var instanceElement: any;
import * as XLSX from 'xlsx'
import { MultiMvtSummary } from './MultiMvtSummary/MultiMvtSummary';
import { ProcessWindow } from './ProcessWindow/ProcessWindow';
declare var require: any;
declare  function validateFormatTimeSecond(strField): any;
interface ActionConfig {
  action: string;
  requiredParams: string[];
  optionalParams?: { [key: string]: () => any };
}


@Component({
  selector: 'app-multiple-mvt-actions',
  templateUrl: './MultipleMvtActions.html',
  styleUrls: ['./MultipleMvtActions.css']
})
export class MultipleMvtActions extends SwtModule implements OnInit {

  @ViewChild('mvtGridContainer') mvtGridContainer: SwtCanvas;

  /***********SwtLabel***********/
  @ViewChild('dataSource') dataSource: SwtLabel;
  @ViewChild('fileName') fileName: SwtLabel;
  @ViewChild('total') total: SwtLabel;
  @ViewChild('selected') selected: SwtLabel;
  @ViewChild('bookLbl') bookLbl: SwtLabel;
  @ViewChild('ordInstLbl') ordInstLbl: SwtLabel;
  @ViewChild('critPayTypeLbl') critPayTypeLbl: SwtLabel;
  @ViewChild('counterPartyLbl') counterPartyLbl: SwtLabel;
  @ViewChild('expSettlLbl') expSettlLbl: SwtLabel;
  @ViewChild('actualSettlLbl') actualSettlLbl: SwtLabel;
  @ViewChild('noteLbl') noteLbl: SwtLabel;
  @ViewChild('mvtIdLocationLbl') mvtIdLocationLbl: SwtLabel;
  @ViewChild('updateBookCode') updateBookCode: GridItem;

  @ViewChild('noteLbl5') noteLbl5: SwtLabel;
  @ViewChild('noteLbl2') noteLbl2: SwtLabel;
  @ViewChild('noteLbl3') noteLbl3: SwtLabel;
  @ViewChild('noteLbl4') noteLbl4: SwtLabel;
  
  /***********SwtFieldSet***********/
  @ViewChild('dataDefFieldSet') dataDefFieldSet: SwtFieldSet;
  @ViewChild('mvtTotalFieldSet') mvtTotalFieldSet: SwtFieldSet;
  @ViewChild('MvtsFieldSet') MvtsFieldSet: SwtFieldSet;
  @ViewChild('actionFieldSet') actionFieldSet: SwtFieldSet;
  @ViewChild('dynamicContentPanel') dynamicContentPanel: SwtFieldSet;
  @ViewChild('noteFieldSet') noteFieldSet: SwtFieldSet;
  @ViewChild('predictFieldSet') predictFieldSet: SwtFieldSet;
  @ViewChild('externalFieldSet') externalFieldSet: SwtFieldSet;
  @ViewChild('ilmFieldSet') ilmFieldSet: SwtFieldSet;
  @ViewChild('internalSttlmFieldSet') internalSttlmFieldSet: SwtFieldSet;
  

  /***********SwtRadioButtonGroup***********/
  @ViewChild('mvtIdLocation') mvtIdLocation: SwtRadioButtonGroup;
  @ViewChild('mvtAction') mvtAction: SwtRadioButtonGroup;
  @ViewChild('predictStatus') predictStatus: SwtRadioButtonGroup;
  @ViewChild('externalStatus') externalStatus: SwtRadioButtonGroup;
  @ViewChild('ilmFcastStatus') ilmFcastStatus: SwtRadioButtonGroup;
  @ViewChild('internalSttlmStatus') internalSttlmStatus: SwtRadioButtonGroup;

   /***********SwtRadioItem***********/
   @ViewChild('colNameRadio') colNameRadio: SwtRadioItem;
   @ViewChild('colNumberRadio') colNumberRadio: SwtRadioItem;
   @ViewChild('addNoteRadio') addNoteRadio: SwtRadioItem;
   @ViewChild('updateStsRadio') updateStsRadio: SwtRadioItem;
   @ViewChild('notUpdateRadio') notUpdateRadio: SwtRadioItem;
   @ViewChild('includedRadio') includedRadio: SwtRadioItem;
   @ViewChild('excludedRadio') excludedRadio: SwtRadioItem;
   @ViewChild('cancelledRadio') cancelledRadio: SwtRadioItem;
   @ViewChild('notUpdateRadio1') notUpdateRadio1: SwtRadioItem;
   @ViewChild('includedRadio1') includedRadio1: SwtRadioItem;
   @ViewChild('excludedRadio1') excludedRadio1: SwtRadioItem;
   @ViewChild('notUpdateRadio2') notUpdateRadio2: SwtRadioItem;
   @ViewChild('includedRadio2') includedRadio2: SwtRadioItem;
   @ViewChild('excludedRadio2') excludedRadio2: SwtRadioItem;
   @ViewChild('notUpdateRadio3') notUpdateRadio3: SwtRadioItem;
   @ViewChild('yesRadio') yesRadio: SwtRadioItem;
   @ViewChild('noRadio') noRadio: SwtRadioItem;
   @ViewChild('unmatchRadio') unmatchRadio: SwtRadioItem;
   @ViewChild('reconcileRadio') reconcileRadio: SwtRadioItem;
   @ViewChild('updateOtherRadio') updateOtherRadio: SwtRadioItem;

  /***********SwtButton***********/
  @ViewChild('importButton') importButton: SwtButton;
  @ViewChild('bookButton') bookButton: SwtButton; 
  @ViewChild('processButton') processButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;

  /***********SwtComboBox***********/ 
  @ViewChild('dataSourceCombo') dataSourceCombo: SwtComboBox;
  @ViewChild('bookCombo') bookCombo: SwtComboBox;

  /***********SwtTextInput***********/ 
  @ViewChild('colNameTxt') colNameTxt: SwtTextInput;
  @ViewChild('colNumberTxt') colNumberTxt: SwtTextInput;
  @ViewChild('totalTxt') totalTxt: SwtTextInput;
  @ViewChild('selectedTxt') selectedTxt: SwtTextInput;
  @ViewChild('ordInstTxtInput') ordInstTxtInput: SwtTextInput;
  @ViewChild('critPayTypeTxtInput') critPayTypeTxtInput: SwtTextInput;
  @ViewChild('counterPartyTxtInput') counterPartyTxtInput: SwtTextInput;

  
  @ViewChild('actualSettlTimeField') actualSettlTimeField: SwtTextInput;
  @ViewChild('expSettlTimeField') expSettlTimeField: SwtTextInput;
  
  @ViewChild('bookCheckbox') bookCheckbox: SwtCheckBox;
  @ViewChild('ordInstCheckbox') ordInstCheckbox: SwtCheckBox;
  @ViewChild('critPayTypeCheckbox') critPayTypeCheckbox: SwtCheckBox;
  @ViewChild('counterPartyCheckbox') counterPartyCheckbox: SwtCheckBox;
  @ViewChild('expSettlCheckbox') expSettlCheckbox: SwtCheckBox;
  @ViewChild('actualSettlCheckbox') actualSettlCheckbox: SwtCheckBox;

  /***********SwtTextArea***********/ 
  @ViewChild('noteText') noteText: SwtTextArea;
  @ViewChild('noteText5') noteText5: SwtTextArea;
  @ViewChild('noteText2') noteText2: SwtTextArea;
  @ViewChild('noteText3') noteText3: SwtTextArea;
  @ViewChild('noteText4') noteText4: SwtTextArea;

  /***********SwtTextArea***********/ 
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  @ViewChild('uploadImage') uploadImage: SwtImage;

  /***********SwtDateField***********/ 
  @ViewChild('expSettlField') expSettlField: SwtDateField;
  @ViewChild('actualSettlField') actualSettlField: SwtDateField;

  /***Module****/
  @ViewChild('swtModule') swtModule: SwtModule;
  
  @ViewChild('pageBox') pageBox: HBox;
  @ViewChild('numstepper', { read: SwtCommonGridPagination }) numstepper: SwtCommonGridPagination;
  //Variable to store the last page no
  public lastNumber: Number = 0;
  private swtAlert: SwtAlert;
  private menuAccessId;
  /***variable to hold module id ***/
  public moduleId = "Predict";
  /**
   * Data Objects
   **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private requestParams = [];
  private _invalidComms: string;
  /**
   * Variable to hold the grid
   **/
  private mvtGrid: SwtCommonGrid;
  private displayedDate: string;
  private dateFormat: string;
  private pastedText;
  private excelData = [];
  private checkedData = [];
  private binarystrGlobal: string ="";
  private defaultColumns: string[] = [];
  private defaultHeading: string[] = [];
  private data = [];
  private uploadedFileName = "";
  private mvtIdLabelInFile;
  private mvtIdPositionInFile;
  private selectCounter = 0;
  private selectedMvtIdsList;
  private fromMenu;
  private preStatusEditStatus;
  private extBalStatus;
  private ilmFcastStatusEditStatus;
  private bookCodeEditStatus;
  private orederInstEditStatus;
  private critPayTypeEditStatus;
  private cpartyEditStatus;
  private expSettEditStatus;
  private actualSettEditStatus;
  private win: any;
  private nonNumericCount = 0; // Counter for non-numeric Movement IDs
  private deselectedMovementUN= [];
  private deselectedMovementRE= [];


// Store all grid data received from server
private allGridData: any = { size: 0, row: [] };
private pageSize: number = 100; // Default page size, adjust as needed
private currentPage: number = 1;
private filteredData: any = { size: 0, row: [] };
private columnMap: any = {}; // Map to store column index to property name mapping

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtAlert = new SwtAlert(commonService);
  }

  ngOnInit() {
    try {
    instanceElement = this;
    this.mvtGrid = <SwtCommonGrid>this.mvtGridContainer.addChild(SwtCommonGrid);
    this.mvtGrid.editable=true;
    this.mvtGrid.checkBoxHeaderActive = true;
    this.dataSource.text = SwtUtil.getPredictMessage('multipleMvtActions.label.dataSource', null);
    this.total.text = SwtUtil.getPredictMessage('multipleMvtActions.label.total', null);
    this.selected.text = SwtUtil.getPredictMessage('multipleMvtActions.label.selected', null);
    this.bookLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.bookLbl', null);
    this.ordInstLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.ordInstLbl', null);
    this.critPayTypeLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.critPayTypeLbl', null);
    this.counterPartyLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.counterPartyLbl', null);
    this.expSettlLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.expSettlLbl', null);
    this.actualSettlLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.actualSettlLbl', null);
    this.noteLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.noteLbl', null);
    this.noteLbl2.text = SwtUtil.getPredictMessage('multipleMvtActions.label.noteLbl', null);
    this.noteLbl3.text = SwtUtil.getPredictMessage('multipleMvtActions.label.noteLbl', null);
    this.noteLbl4.text = SwtUtil.getPredictMessage('multipleMvtActions.label.noteLbl', null);
    this.noteLbl5.text = SwtUtil.getPredictMessage('multipleMvtActions.label.noteLbl', null);

    this.mvtIdLocationLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.mvtIdLocationLbl', null);

    this.importButton.label = SwtUtil.getPredictMessage('multipleMvtActions.label.importButton', null);
    this.processButton.label = SwtUtil.getPredictMessage('multipleMvtActions.label.processButton', null);
    this.closeButton.label = SwtUtil.getPredictMessage('multipleMvtActions.label.closeButton', null);

    /*this.bookCheckbox.label = SwtUtil.getPredictMessage('multipleMvtActions.label.xButton', null);
    this.ordInstCheckbox.label = SwtUtil.getPredictMessage('multipleMvtActions.label.xButton', null);
    this.expSettlCheckbox.label = SwtUtil.getPredictMessage('multipleMvtActions.label.xButton', null);
    this.actualSettlCheckbox.label = SwtUtil.getPredictMessage('multipleMvtActions.label.xButton', null);
    this.critPayTypeCheckbox.label = SwtUtil.getPredictMessage('multipleMvtActions.label.xButton', null);
    this.counterPartyCheckbox.label = SwtUtil.getPredictMessage('multipleMvtActions.label.xButton', null);
*/
    this.colNameRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.colNameRadio', null);
    this.colNumberRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.colNumberRadio', null);
    this.addNoteRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.addNoteRadio', null);
    this.updateStsRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.updateStsRadio', null);
    this.notUpdateRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.notUpdateRadio', null);
    this.includedRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.includedRadio', null);
    this.excludedRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.excludedRadio', null);
    this.cancelledRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.cancelledRadio', null);
    this.unmatchRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.unmatchRadio', null);
    this.reconcileRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.reconcileRadio', null);
    this.updateOtherRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.updateOtherRadio', null);
    this.notUpdateRadio1.label = SwtUtil.getPredictMessage('multipleMvtActions.notUpdateRadio', null);
    this.includedRadio1.label = SwtUtil.getPredictMessage('multipleMvtActions.includedRadio', null);
    this.excludedRadio1.label = SwtUtil.getPredictMessage('multipleMvtActions.excludedRadio', null);
    this.notUpdateRadio2.label = SwtUtil.getPredictMessage('multipleMvtActions.notUpdateRadio', null);
    this.includedRadio2.label = SwtUtil.getPredictMessage('multipleMvtActions.includedRadio', null);
    this.excludedRadio2.label = SwtUtil.getPredictMessage('multipleMvtActions.excludedRadio', null);
    this.notUpdateRadio3.label = SwtUtil.getPredictMessage('multipleMvtActions.notUpdateRadio', null);
    this.yesRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.yesRadio', null);
    this.noRadio.label = SwtUtil.getPredictMessage('multipleMvtActions.noRadio', null);

    this.dataDefFieldSet.legendText = SwtUtil.getPredictMessage('multipleMvtActions.dataDefFieldSet', null);
    this.mvtTotalFieldSet.legendText = SwtUtil.getPredictMessage('multipleMvtActions.mvtTotalFieldSet', null);
    this.MvtsFieldSet.legendText = SwtUtil.getPredictMessage('multipleMvtActions.MvtsFieldSet', null);
    this.actionFieldSet.legendText = "Action";
    this.dynamicContentPanel.legendText = SwtUtil.getPredictMessage('multipleMvtActions.actionFieldSet', null);
    //this.noteFieldSet.legendText = SwtUtil.getPredictMessage('multipleMvtActions.noteFieldSet', null);
    this.predictFieldSet.legendText = SwtUtil.getPredictMessage('multipleMvtActions.predictFieldSet', null);
    this.externalFieldSet.legendText = SwtUtil.getPredictMessage('multipleMvtActions.externalFieldSet', null);
    this.ilmFieldSet.legendText = SwtUtil.getPredictMessage('multipleMvtActions.ilmFieldSet', null);
    this.internalSttlmFieldSet.legendText = SwtUtil.getPredictMessage('multipleMvtActions.internalSttlmFieldSet', null);

    this.uploadImage.toolTip= SwtUtil.getPredictMessage('tooltip.chooseFile', null);

    this.dataSourceCombo.toolTip = SwtUtil.getPredictMessage('multipleMvtActions.tooltip.dataSrcCombo', null);
    this.bookCombo.toolTip = SwtUtil.getPredictMessage('multipleMvtActions.tooltip.bookCombo', null);
  }catch (error) {
    console.log("🚀 ~ file: MultipleMvtActions.ts ~ line 268 ~ MultipleMvtActions ~ onLoad ~ error", error)
}
  }


  onLoad(){
    try {
      
    this.isAddNotePanelVisible = true;
    this.requestParams = [];
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    // this.selectedMvtIdsList ='1031315,'//ExternalInterface.call('eval', 'selectedMvtIdsList') ? ExternalInterface.call('eval', 'selectedMvtIdsList') : "";
    this.selectedMvtIdsList = ExternalInterface.call('eval', 'selectedMvtIdsList') ? ExternalInterface.call('eval', 'selectedMvtIdsList') : "";
    this.fromMenu= ExternalInterface.call('eval', 'fromMenu');
    // this.fromMenu= 'false';
    //let entityId= '';//'RABONL2U'//ExternalInterface.call('eval', 'entityId');
    let entityId= ExternalInterface.call('eval', 'entityId');
    if (this.menuAccessId) {
      if (this.menuAccessId !== "") {
        this.menuAccessId = Number(this.menuAccessId);
      }
    }
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "multipleMvtActions.do?";
    this.actionMethod = 'method=display';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['fromMenu'] = this.fromMenu;
    this.requestParams['selectedMvtIdsList'] = this.selectedMvtIdsList;
    this.requestParams['entityId'] = entityId;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
    
    this.enabledDisableFields();

    this.mvtGrid.ITEM_CLICK.subscribe((selectedCell) => {
      this.cellClick(selectedCell);
    });

    //Add the event listener to listen for checkbox change on a datagrid header
    this.mvtGrid.CheckChange = (selected) => {
      this.selectAll(selected);
    };
    if(this.fromMenu=="true"){
      this.connectGridEvents();
    }

    this.mvtGrid.columnOrderChanged.subscribe((event) => {
        //check first if mvt grid is empty or not to avoid clearing grid when is not
        /*if (this.mvtGrid.gridData.length > 0) {
          this.orderChangedRows = this.mvtGrid.gridData;
        }*/
        this.columnOrderChange(event);
    });

   } catch (error) {
      console.log("🚀 ~ file: MultipleMvtActions.ts ~ line 268 ~ MultipleMvtActions ~ onLoad ~ error", error)
   }

  }

  updateGridData() {

   let entityId= ExternalInterface.call('eval', 'entityId');

    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.inputDataResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = "multipleMvtActions.do?";
    this.actionMethod = 'method=display';
    this.requestParams['menuAccessId'] = this.menuAccessId;
    this.requestParams['fromMenu'] = this.fromMenu;
    this.requestParams['selectedMvtIdsList'] = this.selectedMvtIdsList;
    this.requestParams['entityId'] = entityId;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }


  sortfilterChangee(event) {
    console.log('sortedGridColumn' ,this.mvtGrid.sortedGridColumn);
    console.log(this.mvtGrid.filteredGridColumns);
  }

  updateHeaderCheckbox() {
    if(this.fromMenu !== "true"){
      var selectedXMLList = this.mvtGrid.gridData; // Get grid data
      var allSelected = selectedXMLList.every(row => row.slickgrid_rowcontent.select.content === 'Y');

      // Find the header checkbox dynamically based on its parent
      $('.slick-header-column')
          .find('.checkBoxHeader')
          .prop('checked', allSelected);
    }
}



  cellLogic(event: any, args) {
    
      console.log("cellLogic", event, args);  
  }



  /**
 * Refactored Multiple Movement Actions class
 * Handles movement selection, actions, and grid management
 */
private selectAll(selected: boolean): void {
  try {
    if(this.fromMenu !== "true"){
      const gridData = this.mvtGrid.gridData;
      for (let i = 0; i < gridData.length; i++) {
        if (this.isRowSelectable(gridData[i])) {
          gridData[i].select = selected ? 'Y' : 'N';
          gridData[i].slickgrid_rowcontent.select.content = gridData[i].select;
        }
      }
      
      this.updateGridState();
  }
  } catch (error) {
    SwtUtil.logError(error, SwtUtil.DUP_MODULE_ID, this.getQualifiedClassName(this), "selectAll", 0);
  }
}

/**
 * Update grid state including counter, button states and entity uniformity
 */
private updateGridState(): void {
  try {
  this.mvtGrid.refresh();
  this.updateCounter();
  this.checkEntitiesUniformity();
  this.updateHeaderCheckbox();

}catch (error) {
  console.log("🚀 ~ file: MultipleMvtActions.ts ~ line 268 ~ MultipleMvtActions ~ onLoad ~ error", error)
}
}

/**
 * Checks if a row can be selected based on its properties
 */
private isRowSelectable(row: any): boolean {
  // Check if row can be enabled/disabled for selection
  return this.enableDisableRow(row, "select");
}

/**
 * Handles column order changes in the grid
 */
columnOrderChange(event): void {
  const newOrderDataElmnt = [];
  const newOrderHeader = [];
  const columnDefinitions = event;
  
  for (let i = 0; i < columnDefinitions.length; i++) {
    const column = columnDefinitions[i];
    if (column.id !== "dummy" && column.id !== "select") {
      newOrderDataElmnt.push(column.field);
      newOrderHeader.push(column.name);
    }
  }
  
  this.defaultColumns = newOrderDataElmnt;
  this.defaultHeading = newOrderHeader;
}

/**
 * Updates selection counter and process button state
 */
updateCounter(): void {
  this.selectCounter = 0;
  const isFromMenu = this.fromMenu === "true";
  const dataSource = isFromMenu ? this.allGridData.row : this.mvtGrid.gridData;
  const selectField = isFromMenu ? "select.content" : "select";
  
  for (let i = 0; i < dataSource.length; i++) {
    const item = dataSource[i];
    const selectValue = isFromMenu ? item.select.content : item.select;
    
    if (selectValue === "Y") {
      this.selectCounter++;
    }
  }
  
  this.selectedTxt.text = this.selectCounter;
  this.processButton.enabled = isFromMenu 
    ? this.selectCounter > 0 && this.isNoteTextEmpty()
    : this.selectCounter > 0;
}

/**
 * Checks if entities are uniform across selected movements
 */
checkEntitiesUniformity(): void {
  const entitySet = new Set();
  const isFromMenu = this.fromMenu === "true";
  const dataSource = isFromMenu ? this.allGridData.row : this.mvtGrid.gridData;
  const selectField = isFromMenu ? "select.content" : "select";
  
  for (let i = 0; i < dataSource.length; i++) {
    const item = dataSource[i];
    const selectValue = isFromMenu ? item.select.content : item.select;
    const entityValue = isFromMenu ? item.entity.content : item.entity;
    
    if (selectValue === "Y") {
      entitySet.add(entityValue);
    }
  }
  
  this.updateBookCode.enabled = (entitySet.size === 1 || entitySet.size === 0);
}

/**
 * Handles cell click events in the grid
 */
cellClick(selectedCell): void {
  if (selectedCell.target.field === "select") {
    const rowIndex = selectedCell.rowIndex;
    const currentRecord = this.mvtGrid.gridData[rowIndex];
    
    this.mvtGrid.gridObj.invalidateRow(rowIndex);
    this.mvtGrid.gridObj.render();
    this.updateCounter();
    
    const isFromMenu = this.fromMenu === "true";
    this.processButton.enabled = isFromMenu
      ? this.selectCounter > 0 && this.isNoteTextEmpty() 
      : this.selectCounter > 0;
  }
  
  this.checkEntitiesUniformity();
  this.updateHeaderCheckbox();
}

/**
 * Processes input data results 
 */
inputDataResult(event): void {
  if (this.inputData.isBusy()) {
    this.inputData.cbStop();
    return;
  }
  if(this.selectedMvtIdsList !== null && this.selectedMvtIdsList !== "" )
    ExternalInterface.call('setSelectedMovementForLock',this.selectedMvtIdsList);
  
  this.lastRecievedJSON = event;
  this.jsonReader.setInputJSON(this.lastRecievedJSON);
  
  if (!this.jsonReader.getRequestReplyStatus()) {
    this.handleRequestReplyError();
    return;
  }
  
  
  if (this.lastRecievedJSON != this.prevRecievedJSON) {
    this.processNewData();
  }    //set row color
  this.mvtGrid.rowColorFunction = (dataContext, color) => {
    return this.selectInvalidRowColor(dataContext, color);
  };
  this.mvtGrid.enableDisableCells=(row, field) => {
    return this.enableDisableRow(row, field);
  };

  
  this.mvtAction.selectedValue="AN"
  this.showActionPanel();
  this.resetActionFields();
  this.finalizeDataProcessing();
  this.enableDisableTxtInput();
}

/**
 * Handles request reply errors
 */
private handleRequestReplyError(): void {
  if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
    this.swtAlert.error(
      this.jsonReader.getRequestReplyMessage() + "\n" + 
      this.jsonReader.getRequestReplyLocation(), 
      "Error"
    );
  }
}

/**
 * Processes new data from server
 */
private processNewData(): void {
  // Handle common singletons
  this.displayedDate = this.jsonReader.getSingletons().displayedDate;
  this.dateFormat = this.jsonReader.getSingletons().dateFormat;
  this.dataSourceCombo.setComboData(this.jsonReader.getSelects());
  
  if (this.fromMenu === "true") {
    this.dataSourceCombo.selectedLabel = "Excel";
  } else {
    this.configureNonMenuMode();
  }
  
  this.bookCombo.setComboData(this.jsonReader.getSelects());
  this.configureSettlementFields();
  
  if (!this.jsonReader.isDataBuilding()) {
    this.processGridMetadata();
  }
  
  this.prevRecievedJSON = this.lastRecievedJSON;
}

/**
 * Configure non-menu mode specific settings
 */
private configureNonMenuMode(): void {
  this.dataSourceCombo.selectedLabel = "Movement Summary";
  
  // Extract status values from singletons
  const singletons = this.jsonReader.getSingletons();
  this.preStatusEditStatus = singletons.preStatusEditStatus;
  this.extBalStatus = singletons.extBalStatus;
  this.ilmFcastStatusEditStatus = singletons.ilmFcastStatusEditStatus;
  this.bookCodeEditStatus = singletons.bookCodeEditStatus;
  this.orederInstEditStatus = singletons.orederInstEditStatus;
  this.critPayTypeEditStatus = singletons.critPayTypeEditStatus;
  this.cpartyEditStatus = singletons.cpartyEditStatus;
  this.expSettEditStatus = singletons.expSettEditStatus;
  this.actualSettEditStatus = singletons.actualSettEditStatus;
}

/**
 * Configure settlement fields with date format
 */
private configureSettlementFields(): void {
  const dateFormatLower = this.dateFormat.toLowerCase();
  
  this.expSettlField.formatString = dateFormatLower;
  this.expSettlField.text = this.displayedDate;
  
  this.actualSettlField.formatString = dateFormatLower;
  this.actualSettlField.text = this.displayedDate;
}

/**
 * Process grid metadata and setup columns
 */
private processGridMetadata(): void {
  const metadata = this.lastRecievedJSON.multiMvtActions.mvtGrid.metadata;
  
  // Process columns
  this.defaultColumns = [];
  this.defaultHeading = [];
  
  for (let i = 0; i < metadata.columns.column.length; i++) {
    const column = metadata.columns.column[i];
    
    // Configure checkbox columns
    if (column.type === "checkbox") {
      column.checkBoxVisibility = true;
    }
    
    // Add to default columns if not select column
    if (column.dataelement !== 'select') {
      this.defaultColumns.push(column.dataelement.trim());
      this.defaultHeading.push(column.heading.trim());
    }
  }
  
  // Setup grid with columns
  const gridConfig = { columns: metadata.columns };
  this.mvtGrid.CustomGrid(gridConfig);
  
  // Process grid rows
  this.processGridRows();
}

/**
 * Process grid rows data
 */
private processGridRows(): void {
  const gridRows = this.lastRecievedJSON.multiMvtActions.mvtGrid.rows;
  
  if (gridRows && gridRows.size > 0) {
    this.mvtGrid.gridData = gridRows;
    this.mvtGrid.setRowSize = this.jsonReader.getRowSize();
    this.totalTxt.text = gridRows.size.toString();
    this.selectedTxt.text = gridRows.size.toString();
  } else {
    this.mvtGrid.gridData = { size: 0, row: [] };
  }
}

/**
 * Finalize data processing with UI updates
 */
private finalizeDataProcessing(): void {
  this.uploadImage.source =this.baseURL + ExternalInterface.call('eval', 'uploadFileImage');
  this.uploadImage.id = "uploadImage";
  
  this.enableDisableTxtInput();
  this.enableAllRadioButtons(false);
  this.enableAllInputs(false);
  
  this.processButton.enabled = 
    this.selectedTxt.text && this.selectedTxt.text !== "0" ? true : false;
  
  this.updateCounter();
  this.updateHeaderCheckbox();
  this.ChangeSelectedRadioButton();
}

/**
 * Change selected radio button and update UI state
 */
ChangeSelectedRadioButton(): void {
  const action = this.getAction();
  
  // Handle deselected movements
  if (this.deselectedMovementRE.length > 0 && action !== "RECONCILE") {
    this.reselectMovements("RE");
  }
  
  if (this.deselectedMovementUN.length > 0 && action !== "UNMATCH") {
    this.reselectMovements("UN");
  }
  
  // Configure UI based on selected action
  this.configureUIForAction(action);
  
  // Enable/disable fields and update process button
  this.enabledDisableFields();
  this.enableDisableProcessBtn();
}

/**
 * Configure UI elements based on selected action
 */
private configureUIForAction(action: string): void {
  // Reset checkboxes for non-settlement actions
  if (action !== "UPDATE_OTHER_SETTLEMENT") {
    this.resetCheckboxes();
  }
  
  switch (action) {
    case "UPD_STATUS":
      this.enableAllRadioButtons(true);
      this.enableAllInputs(false);
      if (this.mvtAction.selectedValue === "US") {
        this.resetActionFields();
      }
      this.updateRowsSelectionStatus();
      break;
      
    case "UPDATE_OTHER_SETTLEMENT":
      this.enableAllRadioButtons(false);
      this.enableAllInputs(true);
      this.updateRowsSelectionStatus();
      break;
      
    case "UNMATCH":
      this.deselectMovements("UN");
      this.enableAllRadioButtons(false);
      this.enableAllInputs(false);
      this.resetActionFields();
      break;
      
    case "RECONCILE":
      this.updateRowsSelectionStatus();
      this.deselectMovements("RE");
      this.enableAllRadioButtons(false);
      this.enableAllInputs(false);
      this.resetActionFields();
      break;
      
    default:
      this.updateRowsSelectionStatus();
      this.enableAllRadioButtons(false);
      this.enableAllInputs(false);
      this.resetActionFields();
      break;
  }
  this.mvtGrid.refresh();
  this.updateGridState();
}

/**
 * Reset checkboxes to unselected state
 */
private resetCheckboxes(): void {
  this.bookCheckbox.selected = false;
  this.ordInstCheckbox.selected = false;
  this.expSettlCheckbox.selected = false;
  this.actualSettlCheckbox.selected = false;
  this.critPayTypeCheckbox.selected = false;
  this.counterPartyCheckbox.selected = false;
}

/**
 * Reset action fields to default values
 */
resetActionFields(): void {
  this.predictStatus.selectedValue = "D";
  this.externalStatus.selectedValue = "D";
  this.ilmFcastStatus.selectedValue = "D";
  this.internalSttlmStatus.selectedValue = "D";
  
  this.bookCombo.selectedLabel = "";
  this.ordInstTxtInput.text = "";
  this.critPayTypeTxtInput.text = "";
  this.counterPartyTxtInput.text = "";
  this.expSettlField.text = this.displayedDate;
  this.actualSettlField.text = this.displayedDate;
  this.actualSettlField.text = this.displayedDate;
  this.actualSettlTimeField.text = "00:00:00";
  this.expSettlTimeField.text = "00:00:00";
  
}

/**
 * Enable or disable all radio buttons based on current state
 */
enableAllRadioButtons(enable: boolean): void {
  const isFromMenu = this.fromMenu !== "true";
  const controls = [
    { control: this.notUpdateRadio, status: this.preStatusEditStatus },
    { control: this.includedRadio, status: this.preStatusEditStatus },
    { control: this.excludedRadio, status: this.preStatusEditStatus },
    { control: this.cancelledRadio, status: this.preStatusEditStatus },
    { control: this.notUpdateRadio1, status: this.extBalStatus },
    { control: this.includedRadio1, status: this.extBalStatus },
    { control: this.excludedRadio1, status: this.extBalStatus },
    { control: this.notUpdateRadio2, status: this.ilmFcastStatusEditStatus },
    { control: this.includedRadio2, status: this.ilmFcastStatusEditStatus },
    { control: this.excludedRadio2, status: this.ilmFcastStatusEditStatus },
    { control: this.notUpdateRadio3, status: this.preStatusEditStatus },
    { control: this.yesRadio, status: this.preStatusEditStatus },
    { control: this.noRadio, status: this.preStatusEditStatus }
  ];
  
  controls.forEach(item => {
    item.control.enabled = enable && isFromMenu ? 
      item.status === "true" : enable;
  });
  
}

/**
 * Enable or disable all input fields based on current state
 */
enableAllInputs(enable: boolean): void {
  const isFromMenu = this.fromMenu !== "true";
  const controls = [
    { control: this.bookCombo, status: this.bookCodeEditStatus },
    { control: this.ordInstTxtInput, status: this.orederInstEditStatus },
    { control: this.critPayTypeTxtInput, status: this.critPayTypeEditStatus },
    { control: this.counterPartyTxtInput, status: this.cpartyEditStatus },
    { control: this.expSettlField, status: this.expSettEditStatus },
    { control: this.expSettlTimeField, status: this.expSettEditStatus },
    { control: this.actualSettlField, status: this.actualSettEditStatus },
    { control: this.actualSettlTimeField, status: this.actualSettEditStatus },
    { control: this.bookCheckbox, status: this.bookCodeEditStatus },
    { control: this.ordInstCheckbox, status: this.orederInstEditStatus },
    { control: this.critPayTypeCheckbox, status: this.critPayTypeEditStatus },
    { control: this.counterPartyCheckbox, status: this.cpartyEditStatus },
    { control: this.expSettlCheckbox, status: this.expSettEditStatus },
    { control: this.actualSettlCheckbox, status: this.actualSettEditStatus }
  ];
  
  controls.forEach(item => {
    item.control.enabled = enable && isFromMenu ? 
      item.status === "true" : enable;
  });

}

/**
 * Deselect movements based on specific conditions
 */
deselectMovements(actionType: string): void {
  console.log("🚀 ~ file: MultipleMvtActions.ts ~ line 268 ~ MultipleMvtActions ~ onLoad ~ error", actionType)
  for (let i = 0; i < this.mvtGrid.dataProvider.length; i++) {
    const item = this.mvtGrid.dataProvider[i];

    if (actionType === "UN" && !item.matchId) {
      // Unmatched movements cannot be unmatchable
      this.deselectedMovementUN.push(item.movementId);
      this.updateRowSelection(item, false);
      this.markRowAsDisabledForUnmatch(item, true);
    }

    if (actionType === "RE") {
      console.log("🚀 ~ MultipleMvtActions ~ deselectMovements ~ actionType:", actionType)
      if (!item.matchId && item.status === "E") {
        // Reconciled movements should be deselected
        console.log("🚀 ~ MultipleMvtActions ~ deselectMovements ~ item.movementId:", item.movementId)
        this.deselectedMovementRE.push(item.movementId);
        this.updateRowSelection(item, false);
      } else if (item.matchId) {
        // Matched movements need to be first unmatched before reconciling
        this.markRowAsDisabledForReconcile(item, false); // Enable row for reconciliation
      }
    }
  }
  if(this.fromMenu === "true"){
    console.log("🚀 ~ MultipleMvtActions ~ deselectMovements ~ this.allGridData.row")
    for (let i = 0; i < this.allGridData.row.length; i++) {
      const item = this.allGridData.row[i];
      if (actionType === "UN" && !item.matchId.content) {
        // Unmatched movements cannot be unmatchable
        this.deselectedMovementUN.push(item.movementId.content);
        this.updateRowSelection(item, false);
        this.markRowAsDisabledForUnmatch(item, true);
      }
  
      if (actionType === "RE") {
        console.log("🚀 ~ MultipleMvtActions ~ deselectMovements ~ actionType:", actionType)
        console.log("🚀 ~ MultipleMvtActions ~ deselectMovements ~ item.matchId.content:", item.matchId.content, item.status)
        if (!item.matchId.content && item.status.content === "E") {
          // Reconciled movements should be deselected
          this.deselectedMovementRE.push(item.movementId.content);
          this.updateRowSelection(item, false);
        } else if (item.matchId.content) {
          // Matched movements need to be first unmatched before reconciling
          console.log('from ere')
          this.markRowAsDisabledForReconcile(item, false); // Enable row for reconciliation
        }
      }
    }
  }

  this.mvtGrid.refresh();
}
/**
 * Updates row selection status based on action type and disables rows accordingly
 */
updateRowsSelectionStatus(): void {
  console.log("🚀 ~ MultipleMvtActions ~ updateRowsSelectionStatus");
  
  const action = this.getAction();
  const isReconcileAction = action === "RECONCILE";
  const isUnmatchAction = action === "UNMATCH";
  
  // Process grid data provider
  if (this.mvtGrid && this.mvtGrid.dataProvider) {
    for (let i = 0; i < this.mvtGrid.dataProvider.length; i++) {
      const item = this.mvtGrid.dataProvider[i];
      
      if (item.slickgrid_rowcontent) {
        // Handle unmatch status
        if (!isUnmatchAction) {
          item.slickgrid_rowcontent.disableForUnmatch = item.slickgrid_rowcontent.disableForUnmatch || {};
          item.slickgrid_rowcontent.disableForUnmatch.content = "N";
          item.disableForUnmatch = "N";
        }
        
        // Handle reconcile status
        if (!isReconcileAction) {
          item.slickgrid_rowcontent.disableForReconcile = item.slickgrid_rowcontent.disableForReconcile || {};
          item.slickgrid_rowcontent.disableForReconcile.content = "N";
          item.disableForReconcile = "N";
        }
        
        // Update selection based on both flags
        const disableForUnmatchOk = item.slickgrid_rowcontent.disableForUnmatch && 
                                   item.slickgrid_rowcontent.disableForUnmatch.content === "N" || 
                                   !item.slickgrid_rowcontent.disableForUnmatch;
                                   
        const disableForReconcileOk = item.slickgrid_rowcontent.disableForReconcile && 
                                     item.slickgrid_rowcontent.disableForReconcile.content === "N" || 
                                     !item.slickgrid_rowcontent.disableForReconcile;
                                     
        const mvtAccessOk = item.slickgrid_rowcontent.mvtAccess && 
                           item.slickgrid_rowcontent.mvtAccess.content !== "N";
          
        const canSelect = disableForUnmatchOk && disableForReconcileOk && mvtAccessOk;
        this.updateRowSelection(item, canSelect);
      }
    }
  }
  
  // Process allGridData if fromMenu is true
  if (this.fromMenu === "true" && this.allGridData && this.allGridData.row) {
    for (let i = 0; i < this.allGridData.row.length; i++) {
      const item = this.allGridData.row[i];
      
      // Handle unmatch status
      if (!isUnmatchAction) {
        item.disableForUnmatch = item.disableForUnmatch || {};
        item.disableForUnmatch.content = "N";
      }
      
      console.log("🚀 ~ MultipleMvtActions ~ updateRowsSelectionStatus ~ isReconcileAction:", isReconcileAction)
      // Handle reconcile status
      if (!isReconcileAction) {
        item.disableForReconcile = item.disableForReconcile || {};
        item.disableForReconcile.content = "N";
        console.log('set disableForReconcile to N')
      }
      
      // Update selection based on both flags
      const disableForUnmatchOk = item.disableForUnmatch && 
                                 item.disableForUnmatch.content === "N" || 
                                 !item.disableForUnmatch;
                                 
      const disableForReconcileOk = item.disableForReconcile && 
                                   item.disableForReconcile.content === "N" || 
                                   !item.disableForReconcile;
                                   
      const mvtAccessOk = item.mvtAccess && 
                         item.mvtAccess.content !== "N";
        
      const canSelect = disableForUnmatchOk && disableForReconcileOk && mvtAccessOk;
        
      // For allGridData items, we use a different update approach
      if (canSelect) {
        item.select = item.select || {};
        item.select.content = "Y";
      } else {
        item.select = item.select || {};
        item.select.content = "N";
      }
      console.log("🚀 ~ MultipleMvtActions ~ updateRowsSelectionStatus ~ item:", item)
    }
  }
  
  // Handle deselected movements
  if (this.deselectedMovementRE && this.deselectedMovementRE.length > 0 && action !== "RECONCILE") {
    this.reselectMovements("RE");
  }
  
  if (this.deselectedMovementUN && this.deselectedMovementUN.length > 0 && action !== "UNMATCH") {
    this.reselectMovements("UN");
  }
}

/**
 * Updates the selection status of a single row
 */
private updateRowSelection(item: any, isSelected: boolean): void {
  if (item.slickgrid_rowcontent) {
    if (item.slickgrid_rowcontent.mvtAccess && item.slickgrid_rowcontent.mvtAccess.content !== "N") {
      item.slickgrid_rowcontent.select = item.slickgrid_rowcontent.select || {};
      item.slickgrid_rowcontent.select.content = isSelected ? "Y" : "N";
      item.select = isSelected ? "Y" : "N";
    } else {
      item.slickgrid_rowcontent.select = item.slickgrid_rowcontent.select || {};
      item.slickgrid_rowcontent.select.content = "N";
      item.select = "N";
    }
  } else {
    if (item.mvtAccess && item.mvtAccess.content !== "N") {
      item.select = item.select || {};
      item.select.content = isSelected ? "Y" : "N";
    } else {
      item.select = item.select || {};
      item.select.content = "N";
    }
  }
}

/**
 * Mark row as disabled for unmatch operation
 */
private markRowAsDisabledForUnmatch(item: any, value: boolean): void {
  if(item.slickgrid_rowcontent){
    item.slickgrid_rowcontent.disableForUnmatch = item.slickgrid_rowcontent.disableForUnmatch || {};
    item.slickgrid_rowcontent.disableForUnmatch.content = value ? "Y" : "N";
    item.disableForUnmatch = value ? "Y" : "N";
  }else{
    item.disableForUnmatch = item.disableForUnmatch || {};
    item.disableForUnmatch.content = {};
    item.disableForUnmatch.content = value ? "Y" : "N";
    // item.disableForUnmatch = value ? "Y" : "N";
  }


}

/**
 * Mark row as disabled for reconcile operation
 */
private markRowAsDisabledForReconcile(item: any, value: boolean): void {
console.log("🚀 ~ MultipleMvtActions ~ markRowAsDisabledForReconcile ~ markRowAsDisabledForReconcile:")

  if(item.slickgrid_rowcontent){
    item.slickgrid_rowcontent.disableForReconcile = item.slickgrid_rowcontent.disableForReconcile || {};
    item.slickgrid_rowcontent.disableForReconcile.content = value ? "Y" : "N";
    item.disableForReconcile = value ? "Y" : "N";
  }else{
    item.disableForReconcile = item.disableForReconcile || {};
    item.disableForReconcile.content = {};
    item.disableForReconcile.content = value ? "Y" : "N";
  }

}


/**
 * Reselect previously deselected movements
 */
reselectMovements(action: string): void {
  const movementList = action === "RE" ? this.deselectedMovementRE : this.deselectedMovementUN;
  
  for (let i = 0; i < this.mvtGrid.dataProvider.length; i++) {
    const item = this.mvtGrid.dataProvider[i];
    
    if (movementList.includes(item.movementId)) {
      this.updateRowSelection(item, true);
    }
  }
  if(this.fromMenu === "true"){
    console.log("🚀 ~ file: MultipleMvtActions.ts ~ line 268 ~ MultipleMvtActions ~ onLoad ~ error")
    for (let i = 0; i < this.allGridData.row.length; i++) {
      const item = this.allGridData.row[i];
      
      if (movementList.includes(item.movementId)) {
        this.updateRowSelection(item, true);
      }
    }
  }

  
  // Clear deselected movement lists
  this.deselectedMovementUN = [];
  this.deselectedMovementRE = [];
  
  this.mvtGrid.refresh();
}

/**
 * Enable or disable text input fields based on selection
 */
enableDisableTxtInput(): void {
  const isColumnNameSelected = this.colNameRadio.selected;
  
  this.colNameTxt.enabled = isColumnNameSelected;
  this.colNumberTxt.enabled = !isColumnNameSelected;
  
  if (isColumnNameSelected) {
    this.colNumberTxt.text = "";
  } else {
    this.colNameTxt.text = "";
  }
}

/**
 * Enable or disable fields based on current mode
 */
enabledDisableFields(): void {
  try{
    const isFromMenu = this.fromMenu === "true";
  
  // Handle note requirement
  if (isFromMenu) {
    const isAddNote = this.getAction() === "ADD_MOV_NOTE";
    this.noteText.required = isAddNote;
    this.noteLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.noteLbl', null) + 
    (isAddNote ? "*" : "");
    
    // Configure menu mode UI elements
    this.configureMenuModeUI(true);
  } else {
    // Configure non-menu mode UI elements
    this.noteText.required = false;
    this.dataSourceCombo.enabled = false;
    this.configureMenuModeUI(false);
    this.noteLbl.text = SwtUtil.getPredictMessage('multipleMvtActions.label.noteLbl', null);
  }
}catch (error) {
    console.log("🚀 ~ file: MultipleMvtActions.ts ~ line 268 ~ MultipleMvtActions ~ onLoad ~ error", error)
}
}

/**
 * Configure menu mode UI elements
 */
private configureMenuModeUI(enable: boolean): void {
  this.uploadImage.includeInLayout = enable;
  this.uploadImage.visible = enable;
  this.colNameTxt.enabled = enable;
  this.colNumberTxt.enabled = enable;
  this.colNameRadio.enabled = enable;
  this.colNumberRadio.enabled = enable;
  this.importButton.enabled = enable;
}

/**
 * Process file upload
 */
readUploadedFile(event): void {
  const target: DataTransfer = <DataTransfer>(event.target);
  
  if (target.files.length !== 1) {
    throw new Error('Cannot use multiple files');
  }
  
  this.uploadedFileName = event.target.files[0].name;
  this.fileName.text = this.uploadedFileName;
  
  const reader: FileReader = new FileReader();
  reader.readAsBinaryString(target.files[0]);
  reader.onload = (e: any) => {
    this.binarystrGlobal = e.target.result;
  };
}

/**
 * Reset input value on click
 */
onInputClick(event): void {
  event.target.value = '';
}

/**
 * Import data from uploaded file
 */
importData(): void {
  if (!this.colNameTxt && !this.colNumberTxt) {
    this.swtAlert.error(SwtUtil.getPredictMessage('alert.emptyMovementIdLocation', null));
    return;
  }
  
  const message = SwtUtil.getPredictMessage("multipleMvtActions.importFile?", null);
  Alert.okLabel = SwtUtil.getPredictMessage("button.ok", null);
  Alert.cancelLabel = SwtUtil.getPredictMessage("button.cancel", null);
  
  this.swtAlert.confirm(
    message, 
    "", 
    Alert.OK | Alert.CANCEL, 
    null, 
    this.confirmImport.bind(this)
  );
}

/**
 * Handle import confirmation
 */
confirmImport(event): void {
  if (event.detail !== Alert.OK) {
    return;
  }
  
  try {
    // Validate column selection
    if (this.validateColumnSelection()) {
      return;
    }
    
    // Process Excel file
    this.processExcelFile();
  } catch (error) {
    this.showImportError();
  }
}

/**
 * Validate column selection
 * @returns true if validation fails
 */
private validateColumnSelection(): boolean {
  if ((this.colNameRadio.selected && !this.colNameTxt.text) || 
      (this.colNumberRadio.selected && !this.colNumberTxt.text)) {
    this.swtAlert.error(
      SwtUtil.getPredictMessage('alert.movementIdNotFilled', null),
      SwtUtil.getPredictMessage('alert_header.error'),
      Alert.OK,
      null
    );
    return true;
  }
  return false;
}

/**
 * Process Excel file for import
 */
private processExcelFile(): void {
  this.nonNumericCount = 0;
  let columnIndex: number;
  
  // Read Excel file
  const wb: XLSX.WorkBook = XLSX.read(this.binarystrGlobal, { type: 'binary', raw: true });
  const wsname: string = wb.SheetNames[0];
  const ws: XLSX.WorkSheet = wb.Sheets[wsname];
  
  this.excelData = [];
  let rawExcelData: string[][] = XLSX.utils.sheet_to_json(ws, { header: 1, raw: true }) as string[][];
  
  // Process based on column selection method
  if (this.colNameRadio.selected) {
    if (!this.processColumnByName(rawExcelData)) {
      return;
    }
  } else if (this.colNumberRadio.selected) {
    if (!this.processColumnByNumber(rawExcelData)) {
      return;
    }
  }
  
  // Validate and process extracted data
  if (this.excelData.length > 0) {
    this.checkMvts(this.excelData);
  } else {
    this.swtAlert.error(
      SwtUtil.getPredictMessage('alert.invalidDataFound', null),
      SwtUtil.getPredictMessage('alert_header.error'),
      Alert.OK, 
      null
    );
  }
}

/**
 * Process Excel data by column name
 * @returns false if processing fails
 */
private processColumnByName(rawExcelData: string[][]): boolean {
  const columnName = this.colNameTxt.text;
  let headerRowIndex = -1;
  
  // Find header row containing the column name
  for (let i = 0; i < rawExcelData.length; i++) {
    if (rawExcelData[i].includes(columnName)) {
      headerRowIndex = i;
      break;
    }
  }
  
  if (headerRowIndex === -1) {
    this.swtAlert.error(
      SwtUtil.getPredictMessage('alert.movementIdNotInHeader', null),
      SwtUtil.getPredictMessage('alert_header.error'),
      Alert.OK, 
      null
    );
    return false;
  }
  
  const headers = rawExcelData[headerRowIndex];
  const dataRows = rawExcelData.slice(headerRowIndex + 1);
  const columnIndex = headers.indexOf(columnName);
  
  if (columnIndex === -1) {
    this.swtAlert.error(
      SwtUtil.getPredictMessage('alert.movementIdNotInHeader', null),
      SwtUtil.getPredictMessage('alert_header.error'),
      Alert.OK, 
      null
    );
    return false;
  }
  
  this.extractMovementIds(dataRows, columnIndex);
  return true;
}

/**
 * Process Excel data by column number
 * @returns false if processing fails
 */
private processColumnByNumber(rawExcelData: string[][]): boolean {
  const position = parseInt(this.colNumberTxt.text, 10);
  
  if (isNaN(position) || position < 1) {
    this.swtAlert.error(
      SwtUtil.getPredictMessage('alert.invalidColumnPosition', null),
      SwtUtil.getPredictMessage('alert_header.error'),
      Alert.OK, 
      null
    );
    return false;
  }
  
  const columnIndex = position - 1;
  this.extractMovementIds(rawExcelData, columnIndex);
  return true;
}

/**
 * Extract movement IDs from data rows
 */
private extractMovementIds(dataRows: string[][], columnIndex: number): void {
  this.excelData = dataRows.map(row => {
    const movementIdValue = row[columnIndex];
    const cleanedValue = String(movementIdValue).replace(/^['"]|['"]$/g, '').trim();
    const isNumeric = cleanedValue !== undefined && /^\d+$/.test(cleanedValue);
    
    if (!isNumeric) {
      this.nonNumericCount++;
    }
    
    return isNumeric ? cleanedValue : null;
  }).filter(value => value !== null);
}

/**
 * Show import error
 */
private showImportError(): void {
  this.swtAlert.error(
    SwtUtil.getPredictMessage('multiMvtActions.importFailed', null),
    SwtUtil.getPredictMessage('alert_header.error'),
    Alert.OK, 
    null
  );
}

/**
 * Check movements against server
 */
checkMvts(data): void {
  this.requestParams = [];
  this.setupCallbacks();
  
  this.actionPath = "multipleMvtActions.do?";
  this.actionMethod = 'method=checkMovements';
  
  this.requestParams['movementList'] = JSON.stringify(data);
  this.requestParams['mvtIdLabelInFile']  = this.mvtIdLabelInFile;
  this.requestParams['mvtIdPositionInFile']  = this.mvtIdPositionInFile;
  
  this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
  
  if (JSON.stringify(data).length > 0) {
    this.inputData.send(this.requestParams);
  }
}

/**
 * Setup input data callbacks
 */
private setupCallbacks(): void {
  this.inputData.cbStart = this.startOfComms.bind(this);
  this.inputData.cbStop = this.endOfComms.bind(this);
  this.inputData.cbResult = (event) => { this.populateData(event); };
  this.inputData.cbFault = this.inputDataFault.bind(this);
  this.inputData.encodeURL = false;
}

/**
 * Handle confirmation for reimporting
 */
confirmListener(event): void {
  if (event.detail === Alert.YES) {
    this.mvtGrid.gridData = { size: 0, row: [] };
    this.checkMvts(this.excelData);
  }
}

refreshMovementList(): void {
  this.mvtGrid.gridData = null;
  if(this.fromMenu=="true"){
    this.checkMvts(this.excelData);
  }else{
    this.updateGridData();
  }
}
/**
 * Populate data from server response
 */
populateData(event): void {
  try {

    // Checks the inputData and stops the communication
  if (this.inputData.isBusy()) {
    this.inputData.cbStop();
  } else {
    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);

    if (this.jsonReader.getRequestReplyStatus()) {
      if ((this.lastRecievedJSON != this.prevRecievedJSON)) { 
        let mvtNotFoundCount= this.jsonReader.getSingletons().mvtNotFoundCount;
        let viewAccessCount= this.jsonReader.getSingletons().viewAccessCount;
        let unlockedMvtCount= this.jsonReader.getSingletons().unlockedMvtCount;
        let successMvtCount= this.jsonReader.getSingletons().successMvtCount;
        let mvtNotFoundList= this.jsonReader.getSingletons().movementIdsNotFound;
        let viewAccessList= this.jsonReader.getSingletons().recordsWithViewAccess;
        let unlockedMvtList= this.jsonReader.getSingletons().unlockedMovements;
        let lockedMvtList= this.jsonReader.getSingletons().lockedMovements;
        this.bookCombo.setComboData(this.jsonReader.getSelects());
          //send the locked mvt List to the jsp side
          if(lockedMvtList){
             ExternalInterface.call('setSelectedMovementForLock',lockedMvtList);
            }
            if (!this.jsonReader.isDataBuilding()) {
              // Set item Render and build column map for filtering
              this.columnMap = {};
              for (var i = 0; i < this.lastRecievedJSON.multiMvtActions.mvtGrid.metadata.columns.column.length; i++) {
                /* Rendering check box for check box types */
                if (this.lastRecievedJSON.multiMvtActions.mvtGrid.metadata.columns.column[i]['type'] == "checkbox") {
                  this.lastRecievedJSON.multiMvtActions.mvtGrid.metadata.columns.column[i].checkBoxVisibility = true;
                }
                
                // Store column name and index for filtering
                const colName = this.lastRecievedJSON.multiMvtActions.mvtGrid.metadata.columns.column[i].dataelement;
                this.columnMap[i] = colName;
              }
            
              const obj = { columns: this.lastRecievedJSON.multiMvtActions.mvtGrid.metadata.columns };
              this.mvtGrid.CustomGrid(obj);
              
              var gridRows = this.lastRecievedJSON.multiMvtActions.mvtGrid.rows;
              if (gridRows && gridRows.size > 0) {
                // Store all data for client-side pagination
                this.allGridData = {...gridRows};
                this.filteredData = {...gridRows};
                
                // Initialize pagination component
                this.configureClientPagination();
                
                // Apply pagination to show only current page data
                this.applyPagination();
                
                //populate Total text input value
                this.totalTxt.text = gridRows.size;
                //populate selected text input value
                this.selectedTxt.text = successMvtCount;
                //enable process button if the grid is populated and at least one movement is selected 
                //for processing and Note Text has been specified
                this.processButton.enabled = (successMvtCount != "0" && this.noteText.text) ? true : false;
              } else {
                this.mvtGrid.gridData = { size: 0, row: [] };
                this.allGridData = { size: 0, row: [] };
                this.filteredData = { size: 0, row: [] };
                this.configureClientPagination();
              }

            
              this.prevRecievedJSON = this.lastRecievedJSON;
            }

    let summaryText= successMvtCount+ ' movements loaded successfully.\n';

    if (this.nonNumericCount > 0) {
      summaryText += this.nonNumericCount + ' movement IDs are ignored as they are not fully numeric.\n';
    }


    if (mvtNotFoundCount !="0") {
      summaryText += mvtNotFoundCount+ 'movement IDs could not be found: {'+ mvtNotFoundList.slice(0, -1) +'}.\n';
    }

    if (viewAccessCount !="0") {
      summaryText += viewAccessCount+ ' movements where your role does not allow updates : {'+ viewAccessList.slice(0, -1) +'}.\n';
    }

    if (unlockedMvtCount !="0") {
      summaryText += unlockedMvtCount+ ' movements could not be locked  : {'+ unlockedMvtList.slice(0, -1) +'}.\n';
    }

    //Display a summary dialogue on completing the import
    this.win =  SwtPopUpManager.createPopUp(this, MultiMvtSummary, {
      title: "Multiple Movement Import Summary",
      summary:summaryText,
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '500';
    this.win.height = '200';
    this.win.showControls = true;
    this.win.id = "ImportSummary";
    this.win.display();


      }

    //set row color
    this.mvtGrid.rowColorFunction = (dataContext, color) => {
      return this.selectInvalidRowColor(dataContext, color);
    };
    this.mvtGrid.enableDisableCells=(row, field) => {
      return this.enableDisableRow(row, field);
    };

    } else {
      if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
      }
    }
  }
  this.mvtAction.selectedValue="AN"
  this.showActionPanel();
  this.resetActionFields();
  this.updateCounter();
  this.ChangeSelectedRadioButton();
  this.resetActionFields();
  this.checkEntitiesUniformity();
  setTimeout(() => {
    this.updateHeaderCheckbox();
  }, 500);
    
  }catch(error){
    console.log("🚀 ~ file: MultipleMvtActions.ts ~ line 517 ~ MultipleMvtActions ~ populateData ~ error", error)
  }

}



/**
 * Enable/Disable rows based on the selected action
 */
private enableDisableRow(row: any, field: string): boolean {
  const action = this.getAction(); // Get selected action
  let isEnabled = true; // Default to enabled

  // If the field is not "select", normal access checks apply
  if (field !== "select") {
    return row.slickgrid_rowcontent.mvtAccess.content !== "N";
  }
  if(field === "select" && this.fromMenu === "true"){
    return false;
  }

  // If no specific action requires disabling, allow selection for accessible rows
  if (action !== "RECONCILE" && action !== "UNMATCH") {
    isEnabled = row.slickgrid_rowcontent.mvtAccess.content !== "N";
  } else if (action === "UNMATCH") {
    isEnabled = !!row.matchId; // Only matched rows are selectable
  } else if (action === "RECONCILE") {
    isEnabled = !!row.matchId || row.status === "L"; // Matched & outstanding movements are selectable
  }
  return isEnabled;
}


b64DecodeUnicode(str) {
  // Going backwards: from bytestream, to percent-encoding, to original string.
  return decodeURIComponent(atob(str).split('').map(function(c) {
      return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
  }).join(''));
}

private selectInvalidRowColor(data, color): any {
  // Variable to hold error location
  var errorLocation: number = 0;
  // Color
  var rColor: any;
  try {
    errorLocation = 10;
    if (data.slickgrid_rowcontent.select.content=="N") {
      errorLocation = 20;
      rColor = "#C0C0C0"; //0xDCDCDC not clear 
    }
  }
  catch (error) {
  }
  return rColor;
}

    updateOtherSettlDetails(field){
          if(field.enabled==true){
            field.enabled=false;
            field.text=null;
          }else{
            field.enabled=true;
            field.text="";
          }
    }




  getColHeading(dataelement) {
    var headerDesc = null;

    switch (dataelement) {
      case "movementId":
        headerDesc = this.mvtIdLabelInFile?this.mvtIdLabelInFile:"MovementID";
        break;
      case "entity":
        headerDesc = "Entity";
        break;
      case "ccy":
        headerDesc = "Ccy";
        break;
      case "vdate":
        headerDesc = "Vdate";
        break;
      case "account":
        headerDesc = "Account";
        break;
      case "amount":
        headerDesc = "Amount";
        break;
      case "sign":
        headerDesc = "Sign";
        break;
      case "pred":
        headerDesc = "Pred";
        break;
      case "ext":
        headerDesc = "Ext";
        break;
      case "ilmFcast":
        headerDesc = "ILM Fcast";
        break;
      case "status":
        headerDesc = "Match Status";
        break;
      case "ref1":
        headerDesc = "Ref1";
        break;
      case "ref2":
        headerDesc = "Ref2";
        break;
      case "extraRef":
        headerDesc = "ExtraRef";
        break;
      case "book":
        headerDesc = "Book";
        break;
      case "matchId":
        headerDesc = "MatchID";
        break;
      case "source":
        headerDesc = "Source";
        break;
      case "format":
        headerDesc = "Format";
        break;
      case "bookCode":
        headerDesc = "Book code";
        break;
      case "cparty":
        headerDesc = "Cparty";
        break;
      case "ordInst":
        headerDesc = "Ord Inst";
        break;
      case "expSettlement":
        headerDesc = "Exp Settlement";
        break;
      case "actSettlement":
        headerDesc = "Act Settlement";
        break;
      case "critPayType":
        headerDesc = "Crit Pay Type";
      default:
        break;
    }

    return headerDesc;
  }

  processHandler(){
    if(this.getAction() === "ADD_MOV_NOTE"  && this.noteText.text == "") {
      this.swtAlert.warning('Please fill all mandatory fields (marked with *)');
      return;
    }

    let message =  SwtUtil.getPredictMessage("multipleMvtActions.confirmProcess", null) +  this.selectedTxt.text +
    SwtUtil.getPredictMessage("multipleMvtActions.confirmProcess1", null) 
    Alert.okLabel = SwtUtil.getPredictMessage("button.ok", null);
    Alert.cancelLabel =  SwtUtil.getPredictMessage("button.cancel", null);
    this.swtAlert.confirm(message, "", Alert.OK |  Alert.CANCEL, null, this.confirmProcess.bind(this));  
  }
 
// Action configurations
private actionConfigurations: { [key: string]: ActionConfig } = {
  ADD_MOV_NOTE: {  // Changed to match DB action
    action: 'ADD_MOV_NOTE',
    requiredParams: ['noteText', 'movementsList']
  },
  UPD_STATUS: {  // Changed to match DB action
    action: 'UPD_STATUS',
    requiredParams: ['movementsList', 'noteText'],
    optionalParams: {
      // These will need to be formatted into JSON values
      predictStatus: () => this.predictStatus.selectedValue,
      externalStatus: () => this.externalStatus.selectedValue,
      ilmFcastStatus: () => this.ilmFcastStatus.selectedValue
    }
  },
  UNMATCH: {  // Changed to match DB action
    action: 'UNMATCH',
    requiredParams: ['movementsList', 'noteText']
  },
  RECONCILE: {  // Changed to match DB action
    action: 'RECONCILE',
    requiredParams: ['movementsList', 'noteText']
  },
  UPDATE_OTHER_SETTLEMENT: {  // This action isn't in the SP_MAIN, might need special handling
    action: 'UPDATE_OTHER_SETTLEMENT',
    requiredParams: ['movementsList', 'noteText'],
    optionalParams: {
      book: () => this.bookCombo.selectedLabel,
      orderingInst: () => this.ordInstTxtInput.text,
      critPayType: () => this.critPayTypeTxtInput.text,
      counterParty: () => this.counterPartyTxtInput.text,
      expSettlAsString: () => this.expSettlField.text,
      actSettlAsString: () => this.actualSettlField.text
    }
  }
};
// Method to get the selected action
private getAction(): string {
  if (this.addNoteRadio.selected) return 'ADD_MOV_NOTE';  // Changed to match DB action
  if (this.updateStsRadio.selected) return 'UPD_STATUS';  // Changed to match DB action
  if (this.unmatchRadio.selected) return 'UNMATCH';      // Changed to match DB action
  if (this.reconcileRadio.selected) return 'RECONCILE';  // Changed to match DB action
  if (this.updateOtherRadio.selected) return 'UPDATE_OTHER_SETTLEMENT';  // This might need special handling
  
  throw new Error('No action selected');
}

private getNoteTextValue(): string {
  const action = this.getAction();
  
  switch (action) {
    case 'ADD_MOV_NOTE':
      return this.noteText && this.noteText.text ? this.noteText.text : '';
    case 'UPD_STATUS':
      return this.noteText2 && this.noteText2.text ? this.noteText2.text : '';
    case 'UNMATCH':
      return this.noteText3 && this.noteText3.text ? this.noteText3.text : '';
    case 'RECONCILE':
      return this.noteText4 && this.noteText4.text ? this.noteText4.text : '';
    case 'UPDATE_OTHER_SETTLEMENT':
      return this.noteText5 && this.noteText5.text ? this.noteText5.text : '';
    default:
      return '';
  }
}
// Panel visibility properties
isAddNotePanelVisible: boolean = true;
isUpdateStatusPanelVisible: boolean = false;
isUpdateOtherPanelVisible: boolean = false;
isUnmatchPanelVisible: boolean = false;
isReconcilePanelVisible: boolean = false;

// Function to show/hide panels based on selected action
showActionPanel(): void {
  // Get the selected action value
  const selectedAction = this.mvtAction.selectedValue;
  
  // Hide all panels
  this.isAddNotePanelVisible = false;
  this.isUpdateStatusPanelVisible = false;
  this.isUpdateOtherPanelVisible = false;
  this.isUnmatchPanelVisible = false;
  this.isReconcilePanelVisible = false;
  
  // Show the appropriate panel based on selection
  switch(selectedAction) {
    case 'AN': // Add Note
      this.isAddNotePanelVisible = true;
      break;
    case 'US': // Update Status
      this.isUpdateStatusPanelVisible = true;
      break;
    case 'UN': // Unmatch
      this.isUnmatchPanelVisible = true;
      break;
    case 'RE': // Reconcile
      this.isReconcilePanelVisible = true;
      break;
    case 'UO': // Update Other
      this.isUpdateOtherPanelVisible = true;
      break;
    default:
      this.isAddNotePanelVisible = true; // Default to add note panel
  }
}

toggleField(checkbox: SwtCheckBox, field: any, pairedField?: any): void {
  if (checkbox.selected) {
    field.enabled = false;
    field.text = "";
    if (pairedField) {
      pairedField.enabled = false;
      pairedField.text = "";
    }
  } else {
    field.enabled = true;
    if(checkbox == this.expSettlCheckbox){
      this.expSettlField.text = this.displayedDate;
      pairedField.enabled = true;
      pairedField.text = "00:00:00";
    }
    if(checkbox == this.actualSettlCheckbox){
      this.actualSettlField.text = this.displayedDate;
      pairedField.enabled = true;
      pairedField.text = "00:00:00";
    }
    
  }
}

// Modify the request parameter generation function to send `NULL` when fields are disabled
// Prepare request parameters dynamically
private prepareRequestParams(action: string): { [key: string]: any } {
  const config = this.actionConfigurations[action];
  if (!config) {
    throw new Error('Invalid action: ' + action);
  }

  // Get selected movement IDs and join as comma-separated string
  const movementsList = this.getSelectedMvtIds();

  // Initialize request parameters
  const requestParams: { [key: string]: any } = {
    p_action: action,
    movementList: movementsList
  };
  
  // Note text handling - consistent across all actions
  requestParams.p_note_text = this.getNoteTextValue();
  
  // Handle user ID

  // Special handling for UPD_STATUS - prepare JSON values
  if (action === 'UPD_STATUS') {
    const jsonValues = [];
    
    // Add status fields if they have values
    // Add status fields if they have values
    if (this.predictStatus && this.predictStatus.selectedValue && this.predictStatus.selectedValue !== 'D') {
      jsonValues.push({
        column_name: 'PREDICT_STATUS',
        column_value: this.predictStatus.selectedValue,
        data_type : 'STRING'
      });
    }

    if (this.externalStatus && this.externalStatus.selectedValue && this.externalStatus.selectedValue !== 'D') {
      jsonValues.push({
        column_name: 'EXT_BAL_STATUS',
        column_value: this.externalStatus.selectedValue,
        data_type : 'STRING'
      });
    }

    if (this.ilmFcastStatus && this.ilmFcastStatus.selectedValue && this.ilmFcastStatus.selectedValue !== 'D') {
      jsonValues.push({
        column_name: 'ILM_FCAST_STATUS',
        column_value: this.ilmFcastStatus.selectedValue,
        data_type : 'STRING'
      });
    }
    
    requestParams.p_json_values = JSON.stringify(jsonValues);
  } else {
    // Default empty JSON array for other actions
    requestParams.p_json_values = '[]';
  }

  // Handle UPDATE_OTHER_SETTLEMENT specially
  if (action === 'UPDATE_OTHER_SETTLEMENT') {
    // This action isn't explicitly in the SP_MAIN procedure
    // It might need to be converted to UPD_STATUS with different columns
    const jsonValues = [];
    
     if (!this.bookCheckbox.selected && this.bookCombo.selectedLabel && this.updateBookCode.enabled) {
    jsonValues.push({ column_name: 'BOOKCODE', column_value: this.bookCombo.selectedLabel, data_type: 'STRING' });
  } else if (this.bookCheckbox.selected) {
    jsonValues.push({ column_name: 'BOOKCODE', column_value: null, data_type: 'STRING' });
  }

  if (!this.ordInstCheckbox.selected && this.ordInstTxtInput.text && this.ordInstTxtInput.enabled) {
    jsonValues.push({ column_name: 'ORDERING_INSTITUTION', column_value: this.ordInstTxtInput.text, data_type: 'STRING' });
  } else if (this.ordInstCheckbox.selected) {
    jsonValues.push({ column_name: 'ORDERING_INSTITUTION', column_value: null, data_type: 'STRING' });
  }

  if (!this.critPayTypeCheckbox.selected && this.critPayTypeTxtInput.text && this.critPayTypeTxtInput.enabled) {
    jsonValues.push({ column_name: 'CRITICAL_PAYMENT_TYPE', column_value: this.critPayTypeTxtInput.text, data_type: 'STRING' });
  } else if (this.critPayTypeCheckbox.selected) {
    jsonValues.push({ column_name: 'CRITICAL_PAYMENT_TYPE', column_value: null, data_type: 'STRING' });
  }

  if (!this.counterPartyCheckbox.selected && this.counterPartyTxtInput.text && this.counterPartyTxtInput.enabled) {
    jsonValues.push({ column_name: 'COUNTERPARTY_ID', column_value: this.counterPartyTxtInput.text, data_type: 'STRING' });
  } else if (this.counterPartyCheckbox.selected) {
    jsonValues.push({ column_name: 'COUNTERPARTY_ID', column_value: null, data_type: 'STRING' });
  }

  if (!this.expSettlCheckbox.selected && this.expSettlField.text && this.expSettlField.enabled) {
    jsonValues.push({ column_name: 'EXPECTED_SETTLEMENT_DATETIME', column_value: this.convertToISO(this.expSettlField.text, this.dateFormat,this.expSettlTimeField.text), data_type: 'DATE' });
  } else if (this.expSettlCheckbox.selected) {
    jsonValues.push({ column_name: 'EXPECTED_SETTLEMENT_DATETIME', column_value: null, data_type: 'DATE' });
  }

  if (!this.actualSettlCheckbox.selected && this.actualSettlField.text && this.actualSettlField.enabled) {
    jsonValues.push({ column_name: 'SETTLEMENT_DATETIME', column_value: this.convertToISO(this.actualSettlField.text, this.dateFormat, this.actualSettlTimeField.text), data_type: 'DATE' });
  } else if (this.actualSettlCheckbox.selected) {
    jsonValues.push({ column_name: 'SETTLEMENT_DATETIME', column_value: null, data_type: 'DATE' });
  }
    
    // Set as UPD_STATUS action with settlement fields
    requestParams.p_action = 'UPD_STATUS';
    requestParams.p_json_values = JSON.stringify(jsonValues);
  }

  return requestParams;
}

  validateTime(textInput) {
    let validTimeMessage = SwtUtil.getPredictMessage('alert.validTime', null);
    if(textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00:00";
    }
    if (textInput.text  && validateFormatTimeSecond(textInput) == false) {
      this.swtAlert.warning(validTimeMessage, null );
      textInput.text = "";
      return false;
    } else  {
      textInput.text = textInput.text.substring(0,8);
      return true;

    }
  }



convertToISO(dateStr: string, format: string, timeStr?: string): string | null {
  if (!dateStr) return null;

  let day, month, year;

  if (format === 'dd/MM/yyyy') {
    [day, month, year] = dateStr.split('/');
  } else if (format === 'MM/dd/yyyy') {
    [month, day, year] = dateStr.split('/');
  } else {
    return dateStr; // Unknown format, return as-is
  }

  const isoDate = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;

  // Validate and normalize time string
  let time = '00:00:00';
  if (timeStr && /^([01]\d|2[0-3]):([0-5]\d):([0-5]\d)$/.test(timeStr.trim())) {
    time = timeStr.trim();
  }

  return `${isoDate} ${time}`;
}

// Confirm process method
confirmProcess(event) {
  try {
    if (event.detail === Alert.OK) {
      // Get the selected action
      const action = this.getAction();
      
      // Prepare input data for communication
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => this.processPopup(event);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;

      // Set up URL and method
      this.actionPath = "multipleMvtActions.do?";
      this.actionMethod = 'method=processAction';
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
        
      // Dynamically prepare request parameters
      const requestParams = this.prepareRequestParams(action);
      
      // Send the request
      this.inputData.send(requestParams);
    }
  } catch (error) {
    this.swtAlert.error(
      SwtUtil.getPredictMessage('multiMvtActions.processFailed', null), 
      SwtUtil.getPredictMessage('alert_header.error'), 
      Alert.OK, 
      null
    );
  }
}

// Method to add or update action configurations dynamically
addOrUpdateActionConfig(action: string, config: ActionConfig) {
  this.actionConfigurations[action] = config;
}

private seq = null;
  processPopup(event){
    //Display a summary dialogue on completing the import
    
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON != this.prevRecievedJSON)) { 
          this.seq=this.jsonReader.getSingletons().seq;
        }
      }
    }
    let action= this.getAction();
    this.win =  SwtPopUpManager.createPopUp(this, ProcessWindow, {
      title: "Multiple Movement Action",
      movementList :this.getSelectedMvtIds(),
      actionSent : this.getAction(),
      requestParamsSent : this.prepareRequestParams(action),
      seq : this.seq
    });
    this.win.isModal = true;
    this.win.enableResize = false;
    this.win.width = '500';
    this.win.height = '250';
    this.win.showControls = false;
    this.win.id = "PorcessWindow";
    this.win.display();
  }

  getSelectedMovement(){ 
    // Variable to hold error location
    var errorLocation: number = 0;
    try {
    errorLocation = 10; 
    const selectedMap: { [key: string]: string } = {};
    for (let i = 0; i < this.mvtGrid.gridData.length; i++) {
      if (this.mvtGrid.gridData[i].select == "Y") {
        selectedMap[this.mvtGrid.gridData[i].entity] = this.mvtGrid.gridData[i].movementId;
      }
    }
    return Object.entries(selectedMap).map(([key, value]) => `${key}:${value}`).join(', '); 
  }catch (error) {
    SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "getSelectedMovement", errorLocation);
 }
}


getSelectedMvtIds(){ 
  // Variable to hold error location
  var errorLocation: number = 0;
  try {
  errorLocation = 10; 
  var selectedList = "";
  if(this.fromMenu=="true"){
    this.allGridData.row.forEach(row => {
      if (row['select'].content == "Y") {
        selectedList = selectedList + row['movementId'].content + ",";
      }
    });
  }else{
    for (let i = 0; i < this.mvtGrid.gridData.length; i++) {
      if (this.mvtGrid.gridData[i].select == "Y") {
        selectedList = selectedList + this.mvtGrid.gridData[i].movementId + ",";
      }
    }
  }
  return selectedList; 
}catch (error) {
  SwtUtil.logError(error, this.moduleId , this.commonService.getQualifiedClassName(this)  , "getSelectedMvtIds", errorLocation);
}
}


enableDisableProcessBtn(){

  if(this.selectedTxt.text && this.selectedTxt.text!="0" && this.isNoteTextEmpty()){
    this.processButton.enabled=true;
  }else{
    this.processButton.enabled=false;
  }
}

isNoteTextEmpty(){
  if(this.getAction()=== "ADD_MOV_NOTE" && this.noteText.text){
    return true;
  }else if(this.getAction()=== "ADD_MOV_NOTE" && !this.noteText.text){
    return false;
  } else {
    return true;
  }
}

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


	/**                                                                                                                  
	 * If a fault occurs with the connection with the server then display the lost connection label                      
	 * @param event:FaultEvent                                                                                           
	 **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }


  closeHandler(): void {
      ExternalInterface.call("close"); 
  }


  updateDataTypeInfo(){    
    
    if (this.mvtGrid.gridData.length > 0) {
      Alert.yesLabel = SwtUtil.getPredictMessage('alert.yes.label');
      Alert.noLabel = SwtUtil.getPredictMessage('alert.no.label');
      var message: string = StringUtils.substitute((SwtUtil.getPredictMessage('preAdviceInput.unsavedData', null)));
      this.swtAlert.confirm(message, SwtUtil.getPredictMessage('alert_header.confirm'), Alert.YES | Alert.NO, null, this.confirmResetFields.bind(this));
    }
  }

  confirmResetFields(){
    this.fileName.text = "";
    this.colNameTxt.text = "";
    this.colNumberTxt.text = "";
    this.totalTxt.text = "";
    this.selectedTxt.text = "";
    this.noteText.text="";
    this.mvtAction.selectedValue="AN"
    this.resetActionFields();
    this.mvtGrid.gridData = { size: 0, row: [] };
    this.mvtGrid.refresh();
  }
// Configure pagination component
private configureClientPagination(): void {
  // Get page size from settings if available
  if (this.jsonReader.getSingletons() && this.jsonReader.getSingletons().pageSize) {
    this.pageSize = Number(this.jsonReader.getSingletons().pageSize);
  }
  
  // Calculate max pages
  let maxPage = Math.ceil(this.filteredData.size / this.pageSize);
  if (maxPage < 1) maxPage = 1;
  
  // Configure numstepper
  this.numstepper.processing = false;
  this.numstepper.maximum = maxPage;
  this.numstepper.minimum = maxPage > 0 ? 1 : 0;
  this.numstepper.value = 1; // Start at first page
  this.currentPage = 1;
  
  // Set pagination component visibility
  if (maxPage > 1) {
    this.pageBox.visible = true;
  } else {
    this.pageBox.visible = false;
  }
  
  // Set pagination component on grid
  this.mvtGrid.paginationComponent = this.numstepper;
  
  // Set up pagination change handler
  this.numstepper.onPageChanged = () => {
    this.paginationChanged();
  };
}

// Handle pagination changes
private paginationChanged(): void {
  try {
    const newPage = this.numstepper.value;
    
    if (newPage > 0 && 
        newPage <= this.numstepper.maximum && 
        newPage !== this.currentPage) {
      
      this.currentPage = newPage;
      this.applyPagination();
   /*   setTimeout(() => {
        this.ChangeSelectedRadioButton();
      }, 0);*/
    }
    
  } catch (error) {
    SwtUtil.logError(error, this.moduleId, 'ClassName', 'paginationChanged', 0);
  }
}

// Apply pagination
private applyPagination(): void {
  try {
    // Start with filtered data (or all data if no filter applied)
    const dataToDisplay = {...this.filteredData};
    
    // Calculate start and end indices for current page
    const startIndex = (this.currentPage - 1) * this.pageSize;
    const endIndex = Math.min(startIndex + this.pageSize, dataToDisplay.size);
    
    // Create subset of data for current page
    const paginatedData = {
      size: endIndex - startIndex,
      row: []
    };
    
    // Extract only the rows for current page
    if (Array.isArray(dataToDisplay.row)) {
      paginatedData.row = dataToDisplay.row.slice(startIndex, endIndex);
    }
    
    // Update grid with paginated data
    this.mvtGrid.gridData = paginatedData;
    
  } catch (error) {
    SwtUtil.logError(error, this.moduleId, 'ClassName', 'applyPagination', 0);
  }
}

// Handle filtering with the actual filter format
public applyFilter(filterCriteria: string): void {
  try {
    // Reset to first page when filtering
    this.currentPage = 1;
    
    if (!filterCriteria || filterCriteria === '' || filterCriteria === '|') {
      // No filter, use all data
      this.filteredData = {...this.allGridData};
    } else {
      // Parse the filter criteria format: All|1118502|All|All|All|All|All|All|All|All|All|All||||||All|All|All|||||
      const filterValues = filterCriteria.split('|');
      const filteredRows = [];
      
      if (Array.isArray(this.allGridData.row)) {
        // Apply filters to each row
        this.allGridData.row.forEach(row => {
          let includeRow = true;
          
          // Check each filter value against corresponding column
          for (let i = 0; i < filterValues.length; i++) {
            // Skip "All" or empty filters
            if (!filterValues[i] || filterValues[i] === 'All') {
              continue;
            }
            
            // Get column name from the map
            const columnName = this.columnMap[i];
            if (!columnName) {
              continue;  // Skip if column not found
            }
            
            // Check if the row has this property
            if (!row[columnName]) {
              includeRow = false;
              break;
            }
            
            // Handle complex row structure (objects with content property)
            let cellValue = '';
            if (typeof row[columnName] === 'object' && row[columnName].content) {
              cellValue = row[columnName].content.toString().toLowerCase();
            } else if (typeof row[columnName] === 'string') {
              cellValue = row[columnName].toLowerCase();
            } else if (row[columnName] !== null && row[columnName] !== undefined) {
              cellValue = row[columnName].toString().toLowerCase();
            }
            
            // Compare filter value with cell value
            const filterValue = filterValues[i].toLowerCase();
            if (!cellValue.includes(filterValue)) {
              includeRow = false;
              break;
            }
          }
          
          if (includeRow) {
            filteredRows.push(row);
          }
        });
      }
      
      this.filteredData = {
        size: filteredRows.length,
        row: filteredRows
      };
    }
    
    // Check if we need to apply sort as well
    if (this.mvtGrid.sortedGridColumn) {
      this.applySort();
    }
    
    // Reconfigure pagination after filtering
    this.configureClientPagination();
    
    // Apply pagination to filtered data
    this.applyPagination();
    
  } catch (error) {
    SwtUtil.logError(error, this.moduleId, 'ClassName', 'applyFilter', 0);
  }
}

public applySort(): void {
  try {
    const columnIdFromGrid = this.mvtGrid.sortedGridColumnId;
    const direction = this.mvtGrid.sortDirection; // true for ascending, false for descending
    
    if (!columnIdFromGrid) {
      return; // No sorting needed
    }
    
    // Find the actual column name with correct case in the data
    let actualColumnName = '';
    
    // Try to find the correct case for the column name
    if (this.filteredData.row && this.filteredData.row.length > 0) {
      const firstRow = this.filteredData.row[0];
      const keys = Object.keys(firstRow);
      
      // Find the column name case-insensitively
      for (const key of keys) {
        if (key.toLowerCase() === columnIdFromGrid.toLowerCase()) {
          actualColumnName = key;
          break;
        }
      }
    }
    
    // If no matching column found, use the original (this preserves lowercase columnId)
    if (!actualColumnName) {
      actualColumnName = columnIdFromGrid;
    }
    
    // Debug log to see what we're sorting by
    
    // Determine column type based on column name (case insensitive)
    const columnLower = actualColumnName.toLowerCase();
    const isAmountColumn = columnLower === 'amount';
    const isDateColumn = ['vdate', 'actsettlement', 'expsettlement'].includes(columnLower);
    const isNumericIdColumn = ['movementid', 'matchid'].includes(columnLower);
    
    // Sort the filtered data
    if (Array.isArray(this.filteredData.row)) {
      this.filteredData.row.sort((a, b) => {
        let valueA, valueB;
      
        // Extract values
        if (a[actualColumnName] && typeof a[actualColumnName] === 'object' && a[actualColumnName].content !== undefined) {
          valueA = a[actualColumnName].content;
        } else {
          valueA = a[actualColumnName];
        }
      
        if (b[actualColumnName] && typeof b[actualColumnName] === 'object' && b[actualColumnName].content !== undefined) {
          valueB = b[actualColumnName].content;
        } else {
          valueB = b[actualColumnName];
        }
      
        // Ensure undefined/null/empty values are treated correctly
        const isEmptyA = valueA === null || valueA === undefined || valueA === '';
        const isEmptyB = valueB === null || valueB === undefined || valueB === '';
      
        // Always push empty values to the end, regardless of direction
        if (isEmptyA && isEmptyB) return 0;
        if (isEmptyA) return 1; // Empty values always go last
        if (isEmptyB) return -1; // Empty values always go last
      
        // Special handling based on column type
        if (isAmountColumn) {
          return this.compareAmounts(valueA, valueB, direction);
        } else if (isDateColumn) {
          return this.compareDates(valueA, valueB, direction);
        } else {
          // Default string comparison (case insensitive)
          if (typeof valueA === 'string') valueA = valueA.toLowerCase();
          if (typeof valueB === 'string') valueB = valueB.toLowerCase();
      
          if (valueA < valueB) return direction ? -1 : 1;
          if (valueA > valueB) return direction ? 1 : -1;
          return 0;
        }
      });
      
      // Debug log first few sorted items (compatible with older TypeScript)
      for (let i = 0; i < Math.min(5, this.filteredData.row.length); i++) {
        const row = this.filteredData.row[i];
        let value;
        if (row[actualColumnName] && typeof row[actualColumnName] === 'object' && row[actualColumnName].content !== undefined) {
          value = row[actualColumnName].content;
        } else {
          value = row[actualColumnName];
        }
        console.log("- " + actualColumnName + ": " + value);
      }
    }
    
    // Apply pagination after sorting
    this.applyPagination();
    
  } catch (error) {
    SwtUtil.logError(error, this.moduleId, 'ClassName', 'applySort', 0);
  }
}

// Helper function to compare amount values
compareAmounts(valueA: any, valueB: any, direction: boolean): number {
  // Convert to strings for processing
  valueA = String(valueA || '');
  valueB = String(valueB || '');
  
  // Check for empty values - already handled in main sort function
  if (!valueA && !valueB) return 0;
  if (!valueA) return 1; // Empty values go last
  if (!valueB) return -1; // Empty values go last
  
  // European format detected in your data: "13.280.000,00"
  // Convert format: replace dots, then replace comma with dot
  const numA = parseFloat(valueA.replace(/\./g, '').replace(',', '.'));
  const numB = parseFloat(valueB.replace(/\./g, '').replace(',', '.'));
  
  // Handle NaN values
  if (isNaN(numA) && isNaN(numB)) return 0;
  if (isNaN(numA)) return 1; // Empty values go last
  if (isNaN(numB)) return -1; // Empty values go last
  
  // Compare the numeric values
  return direction ? numA - numB : numB - numA;
}

// Helper function to compare date values (in DD/MM/YYYY format)
compareDates(valueA: any, valueB: any, direction: boolean): number {
  // Convert to strings for processing
  valueA = String(valueA || '');
  valueB = String(valueB || '');
  
  // Check for empty values - already handled in main sort function
  if (!valueA && !valueB) return 0;
  if (!valueA) return 1; // Empty values go last
  if (!valueB) return -1; // Empty values go last
  
  // Parse DD/MM/YYYY format
  const parseDate = function(dateStr: string): Date {
    if (!dateStr.match(/^\d{2}\/\d{2}\/\d{4}$/)) return new Date(0); // Invalid date
    
    const parts = dateStr.split('/');
    const day = parseInt(parts[0], 10);
    const month = parseInt(parts[1], 10) - 1; // JavaScript months are 0-based
    const year = parseInt(parts[2], 10);
    
    return new Date(year, month, day);
  };
  
  const dateA = parseDate(valueA);
  const dateB = parseDate(valueB);
  
  // Invalid dates are treated as empty and sorted accordingly
  const timeA = dateA.getTime();
  const timeB = dateB.getTime();
  
  if (isNaN(timeA) && isNaN(timeB)) return 0;
  if (isNaN(timeA)) return 1; // Empty values go last
  if (isNaN(timeB)) return -1; // Empty values go last
  
  // Compare valid dates
  return direction ? timeA - timeB : timeB - timeA;
}

// Helper function to parse various date formats
parseDate(dateStr: string): Date | null {
  if (!dateStr) return null;
  
  // Handle common formats
  // Format: DD/MM/YYYY
  if (/^\d{2}\/\d{2}\/\d{4}$/.test(dateStr)) {
    const [day, month, year] = dateStr.split('/').map(Number);
    return new Date(year, month - 1, day);
  }
  
  // Format: YYYY-MM-DD
  if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    return new Date(dateStr);
  }
  
  // Try standard date parsing
  const date = new Date(dateStr);
  return isNaN(date.getTime()) ? null : date;
}

// Helper function to compare numeric IDs
compareNumericIds(valueA: any, valueB: any, direction: boolean): number {
  // Check for empty values
  if ((!valueA || valueA === '0') && (!valueB || valueB === '0')) return 0;
  if (!valueA || valueA === '0') return 1; // Empty values go last
  if (!valueB || valueB === '0') return -1; // Empty values go last
  
  // Convert to strings, then to numbers for comparison
  const numA = parseInt(String(valueA), 10);
  const numB = parseInt(String(valueB), 10);
  
  // Handle NaN values
  if (isNaN(numA) && isNaN(numB)) return 0;
  if (isNaN(numA)) return 1; // Empty values go last
  if (isNaN(numB)) return -1; // Empty values go last
  
  // Compare the numeric values
  return direction ? numA - numB : numB - numA;
}
// Integrate with existing grid filter mechanism
public onGridFilterChanged(event: any): void {
  // Get the filter string from the grid
  const filterString = this.mvtGrid.getFilteredGridColumns();
  
  // Apply client-side filtering
  this.applyFilter(filterString);
  
  // Update any UI elements that show filtered counts
  if (this.filteredData && this.totalTxt) {
    // no need to update the counter
    //this.totalTxt.text = this.filteredData.size.toString();
  }
}

// Integrate with existing grid sort mechanism
public onGridSortChanged(event: any): void {
  
  // Apply client-side sorting using the grid's sort properties
  this.applySort();
}

// Connect these handlers to your grid
public connectGridEvents(): void {
  // Connect to grid filter change event
  this.mvtGrid.onFilterChanged = (event) => {
    this.onGridFilterChanged(event);
  };
  
  // Connect to grid sort change event
  this.mvtGrid.onSortChanged = (event) => {
    this.onGridSortChanged(event);
  };
}



}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: MultipleMvtActions }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [MultipleMvtActions],
  entryComponents: []
})
export class MultipleMvtActionsModule { }