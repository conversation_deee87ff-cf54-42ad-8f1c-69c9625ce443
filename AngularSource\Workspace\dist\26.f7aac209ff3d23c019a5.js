(window.webpackJsonp=window.webpackJsonp||[]).push([[26],{"bJ+q":function(e,t,l){"use strict";l.r(t);var i=l("CcnG"),n=l("mrSG"),o=l("447K"),a=l("ZYCi"),s=function(e){function t(t,l){var i=e.call(this,l,t)||this;return i.commonService=t,i.element=l,i.jsonReader=new o.L,i.inputData=new o.G(i.commonService),i.baseURL=o.Wb.getBaseURL(),i.actionMethod="",i.actionPath="",i.requestParams=[],i.screenName=null,i.helpURL=null,i.message=null,i.title=null,i.spreadId=null,i.spreadName=null,i.errorLocation=0,i.time=null,i.targetValue=null,i.processCategories=null,i.categories=null,i.processName=null,i.processId=null,i.timesList=null,i.namesList=null,i.maintEventId=null,i.selectedDataRow=null,i.moduleName="Spread Details",i.versionNumber="1.00.00",i.releaseDate="13 March 2019",i.moduleId="PCM",i.swtAlert=new o.bb(t),i}return n.d(t,e),t.prototype.onLoad=function(){var e=this;try{this.categoryGrid=this.categoryCanvasGrid.addChild(o.hb),"view"!=this.screenName?(this.processNameInput.setFocus(),this.okButton.enabled=!0,this.categoryGrid.ITEM_CLICK.subscribe(function(t){e.cellClickEventHandler(t)})):(this.okButton.visible=!1,this.okButton.includeInLayout=!1,this.enableDisableComponents(!1)),this.categoryGrid.rowColorFunction=function(t,l,i,n){return e.drawRowBackground(t,l,i,n)},this.requestParams=[],this.actionPath="spreadProfilesPCM.do?",this.requestParams.moduleId=this.moduleId,"add"!=this.screenName?(this.requestParams.spreadId=this.spreadId,this.requestParams.maintEventId=this.maintEventId,this.requestParams.processPointName=this.processName,this.requestParams.processId=this.processId,this.actionMethod="method=displayChangeOrViewProcess"):this.actionMethod="method=displayAddProcess",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),"add"==this.screenName?(this.message=o.Wb.getAMLMessages("spreadMaintenanceScreen.message.add_help_message"),this.title=o.Wb.getAMLMessages("spreadMaintenanceScreen.add_help_title")):"view"==this.screenName?(this.message=o.Wb.getAMLMessages("spreadMaintenanceScreen.message.view_help_message"),this.title=o.Wb.getAMLMessages("spreadMaintenanceScreen.view_help_title")):"change"==this.screenName&&(this.message=o.Wb.getAMLMessages("spreadMaintenanceScreen.message.change_help_message"),this.title=o.Wb.getAMLMessages("spreadMaintenanceScreen.change_help_title")),this.okButton.label="Ok",this.cancelButton.label="Cancel"}catch(t){o.Wb.logError(t,this.moduleId,"className","onLoad",this.errorLocation)}},t.prototype.drawRowBackground=function(e,t,l,i){var n;try{"Y"==e.slickgrid_rowcontent[i].isDeletedRow?n="#ff808a":"Y"==e.slickgrid_rowcontent[i].isNewRow?n="#c6efce":"Y"==e.slickgrid_rowcontent[i].isUpdatedRow&&(n="#ee82ee")}catch(o){console.log("error drawRowBackground ",o)}return n},t.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},t.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),"view"!=this.screenName&&this.enableDisableComponents(!0)},t.prototype.inputDataResult=function(e){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&this.jsonReader.getRequestReplyStatus()){if(this.helpURL=this.jsonReader.getSingletons().helpurl,!this.jsonReader.isDataBuilding())if(this.processCategoryComboBox.setComboData(this.jsonReader.getSelects(),!1),this.categoryGrid.CustomGrid(e.SpreadProcessPoints.grid.metadata),this.categoryGrid.gridData=e.SpreadProcessPoints.grid.rows,this.categoryGrid.setRowSize=e.SpreadProcessPoints.grid.rows.size,"add"!=this.screenName){if(this.processNameInput.text=this.processName,this.timeInput.text=this.time,this.targetInput.text=this.targetValue,this.processCategoryComboBox.selectedLabel=this.processCategories,this.selectedDataRow&&(this.selectedDataRow.slickgrid_rowcontent.processName.previousValue&&(this.processNameInput.toolTipPreviousValue=this.selectedDataRow.slickgrid_rowcontent.processName.previousValue),this.selectedDataRow.slickgrid_rowcontent.time.previousValue&&(this.timeInput.toolTipPreviousValue=this.selectedDataRow.slickgrid_rowcontent.time.previousValue),this.selectedDataRow.slickgrid_rowcontent.target.previousValue&&(this.targetInput.toolTipPreviousValue=this.selectedDataRow.slickgrid_rowcontent.target.previousValue),this.selectedDataRow.slickgrid_rowcontent.process.previousValue&&(this.processCategoryComboBox.toolTipPreviousValue=this.selectedDataRow.slickgrid_rowcontent.process.previousValue)),"view"!=this.screenName?"ALLEXCEPT"==this.processCategoryComboBox.selectedValue?this.informationLabel.text="Please select all categories which you do NOT wish to be processed":"ONLY"==this.processCategoryComboBox.selectedValue?this.informationLabel.text="Please select all categories which you wish to be processed":this.informationLabel.text="":"ALLEXCEPT"==this.processCategoryComboBox.selectedValue?this.informationLabel.text="The selected categories will NOT be processed":"ONLY"==this.processCategoryComboBox.selectedValue&&(this.informationLabel.text="The selected categories will be processed"),"All"!=this.categories){for(var t=this.categories.split(","),l=0;l<this.categoryGrid.dataProvider.length;l++)-1!=t.indexOf(this.categoryGrid.dataProvider[l].categoryId)?this.categoryGrid.dataProvider[l].Checked="Y":this.categoryGrid.dataProvider[l].Checked="N";this.categoryGrid.refresh()}else this.changeComboProcessCategory();"view"==this.screenName&&this.categoryGrid.enableColumn("Checked",!1)}else this.changeComboProcessCategory();this.prevRecievedJSON=this.lastRecievedJSON}}catch(i){console.log("error:   ",i),o.Wb.logError(i,this.moduleId,"className","inputDataResult",this.errorLocation)}},t.prototype.inputDataFault=function(e){this.swtAlert.error(e.fault.faultstring+"\n"+e.fault.faultCode+"\n"+e.fault.faultDetail)},t.prototype.enableDisableComponents=function(e){try{e?(this.processNameInput.enabled=!0,this.timeInput.enabled=!0,this.targetInput.enabled=!0,this.processCategoryComboBox.enabled=!0,this.selectAllCheckbox.enabled=!0,this.categoryGrid.enableColumn("Checked",!0)):(this.processNameInput.enabled=!1,this.timeInput.enabled=!1,this.targetInput.enabled=!1,this.processCategoryComboBox.enabled=!1,this.selectAllCheckbox.enabled=!1,this.categoryGrid.enableColumn("Checked",!1))}catch(t){o.Wb.logError(t,this.moduleId,"ClassName","disableComponents",this.errorLocation),console.log(t)}},t.prototype.keyDownEventHandler=function(e){try{var t=Object(o.ic.getFocus()).name;e.keyCode==o.N.ENTER&&"saveButton"==t&&this.save()}catch(l){console.log(l,this.moduleId,"className","keyDownEventHandler")}},t.prototype.cellClickEventHandler=function(e){try{if("Checked"==e.target.field)for(var t=0,l=0;l<this.categoryGrid.dataProvider.length;l++)"Y"==this.categoryGrid.dataProvider[l].Checked&&t++,t==this.categoryGrid.dataProvider.length?this.selectAllCheckbox.selected=!0:this.selectAllCheckbox.selected=!1}catch(i){o.Wb.logError(i,this.moduleId,"ClassName","cellClickEventHandler",this.errorLocation)}},t.prototype.changeComboProcessCategory=function(){if("ALLEXCEPT"==this.processCategoryComboBox.selectedValue||"ONLY"==this.processCategoryComboBox.selectedValue){this.categoryGrid.enableColumn("Checked",!0),this.selectAllCheckbox.enabled=!0,this.selectAllCheckbox.selected=!1;for(var e=0;e<this.categoryGrid.dataProvider.length;e++)this.categoryGrid.dataProvider[e].Checked="N";"ALLEXCEPT"==this.processCategoryComboBox.selectedValue?this.informationLabel.text="Please select all categories which you do NOT wish to be processed":this.informationLabel.text="Please select all categories which you wish to be processed"}else if("ALL"==this.processCategoryComboBox.selectedValue){this.categoryGrid.enableColumn("Checked",!1),this.selectAllCheckbox.enabled=!1,this.selectAllCheckbox.selected=!0;for(e=0;e<this.categoryGrid.dataProvider.length;e++)this.categoryGrid.dataProvider[e].Checked="Y";this.informationLabel.text=""}},t.prototype.selectDeselectAll=function(e){for(var t=0;t<this.categoryGrid.dataProvider.length;t++)this.selectAllCheckbox.selected?this.categoryGrid.dataProvider[t].Checked="Y":this.categoryGrid.dataProvider[t].Checked="N";this.categoryGrid.refresh()},t.prototype.save=function(){if(""!=this.processNameInput.text.trim()){for(var e,t,l=0,i=0;i<this.categoryGrid.dataProvider.length;i++)"Y"==this.categoryGrid.dataProvider[i].Checked&&l++;if(e=l>0,t=l==this.categoryGrid.dataProvider.length,this.timeInput.text&&this.targetInput.text)if(-1==this.timesList.indexOf(this.timeInput.text)&&-1==this.namesList.indexOf(this.processNameInput.text))if("ONLY"!=this.processCategoryComboBox.selectedValue||e)if("ALLEXCEPT"==this.processCategoryComboBox.selectedValue&&t)this.swtAlert.warning("Based on selected process category, you need to exclude at least one category");else{var n="",a="";if(this.selectAllCheckbox.selected)n="All",a="ONLY"==this.processCategoryComboBox.selectedValue?"All":this.processCategoryComboBox.selectedLabel;else if("ALLEXCEPT"!=this.processCategoryComboBox.selectedValue||e){a=this.processCategoryComboBox.selectedLabel;for(i=0;i<this.categoryGrid.dataProvider.length;i++)"Y"==this.categoryGrid.dataProvider[i].Checked&&(n=n+this.categoryGrid.dataProvider[i].categoryId+",");n=n.substring(0,n.length-1)}else a="All",n="All";this.result={time:this.timeInput.text,target:this.targetInput.text+"",process:a,categories:n,processName:this.processNameInput.text},o.Eb.getPopUpById("spreadDetails").close()}else this.swtAlert.warning("Based on selected process, you need to choose at least one category");else-1==this.namesList.indexOf(this.processNameInput.text)?this.swtAlert.warning("Cannot have two process points being defined with same time"):this.swtAlert.warning("Cannot have two process points being defined with same name");else this.swtAlert.warning("Please fill all mandatory fields (marked with *)")}else this.swtAlert.warning("Process Name can not be saved as just spaces")},t.prototype.validateTime=function(e){return e.text.endsWith(":")&&(e.text=e.text+"00"),e.text&&0==validateFormatTime(e)?(this.swtAlert.warning("Please enter a valid time",null),e.text="",!1):(e.text=e.text.substring(0,5),!0)},t.prototype.popupClosed=function(){this.close()},t.prototype.dispose=function(){try{this.requestParams=null,this.baseURL=null,this.actionMethod=null,this.actionPath=null,this.close()}catch(e){console.log(e,this.moduleId,"className","dispose")}},t.prototype.closeBtn=function(){try{o.Eb.getPopUpById("spreadDetails").close()}catch(e){o.Wb.logError(e,this.moduleId,"className","close",this.errorLocation)}},t.prototype.doHelp=function(){try{o.sb.open(this.baseURL+"help/aml/fr/index.html#page=groupes-des-regles.html"),o.sb.resizable(!0)}catch(e){o.Wb.logError(e,this.moduleId,"ClassName","doHelp",this.errorLocation)}},t.prototype.printPage=function(){try{this.actionMethod="type=pdf",this.actionMethod=this.actionMethod+"&action=EXPORT",this.actionMethod=this.actionMethod+"&currentModuleId="+this.moduleId,this.actionMethod=this.actionMethod+"&print=PAGE",o.x.call("getReports",this.actionPath+this.actionMethod)}catch(e){o.Wb.logError(e,this.moduleId,"className","printPage",this.errorLocation)}},t}(o.yb),r=[{path:"",component:s}],d=(a.l.forChild(r),function(){return function(){}}()),c=l("pMnS"),u=l("RChO"),h=l("t6HQ"),b=l("WFGK"),p=l("5FqG"),g=l("Ip0R"),m=l("gIcY"),w=l("t/Na"),C=l("sE5F"),y=l("OzfB"),f=l("T7CS"),I=l("S7LP"),R=l("6aHO"),v=l("WzUx"),x=l("A7o+"),k=l("zCE2"),L=l("Jg5P"),B=l("3R0m"),P=l("hhbb"),N=l("5rxC"),T=l("Fzqc"),A=l("21Lb"),D=l("hUWP"),S=l("3pJQ"),G=l("V9q+"),J=l("VDKW"),_=l("kXfT"),M=l("BGbe");l.d(t,"SpreadDetailsModuleNgFactory",function(){return O}),l.d(t,"RenderType_SpreadDetails",function(){return V}),l.d(t,"View_SpreadDetails_0",function(){return q}),l.d(t,"View_SpreadDetails_Host_0",function(){return H}),l.d(t,"SpreadDetailsNgFactory",function(){return W});var O=i.Gb(d,[],function(e){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[c.a,u.a,h.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,W]],[3,i.n],i.J]),i.Rb(4608,g.m,g.l,[i.F,[2,g.u]]),i.Rb(4608,m.c,m.c,[]),i.Rb(4608,m.p,m.p,[]),i.Rb(4608,w.j,w.p,[g.c,i.O,w.n]),i.Rb(4608,w.q,w.q,[w.j,w.o]),i.Rb(5120,w.a,function(e){return[e,new o.tb]},[w.q]),i.Rb(4608,w.m,w.m,[]),i.Rb(6144,w.k,null,[w.m]),i.Rb(4608,w.i,w.i,[w.k]),i.Rb(6144,w.b,null,[w.i]),i.Rb(4608,w.f,w.l,[w.b,i.B]),i.Rb(4608,w.c,w.c,[w.f]),i.Rb(4608,C.c,C.c,[]),i.Rb(4608,C.g,C.b,[]),i.Rb(5120,C.i,C.j,[]),i.Rb(4608,C.h,C.h,[C.c,C.g,C.i]),i.Rb(4608,C.f,C.a,[]),i.Rb(5120,C.d,C.k,[C.h,C.f]),i.Rb(5120,i.b,function(e,t){return[y.j(e,t)]},[g.c,i.O]),i.Rb(4608,f.a,f.a,[]),i.Rb(4608,I.a,I.a,[]),i.Rb(4608,R.a,R.a,[i.n,i.L,i.B,I.a,i.g]),i.Rb(4608,v.c,v.c,[i.n,i.g,i.B]),i.Rb(4608,v.e,v.e,[v.c]),i.Rb(4608,x.l,x.l,[]),i.Rb(4608,x.h,x.g,[]),i.Rb(4608,x.c,x.f,[]),i.Rb(4608,x.j,x.d,[]),i.Rb(4608,x.b,x.a,[]),i.Rb(4608,x.k,x.k,[x.l,x.h,x.c,x.j,x.b,x.m,x.n]),i.Rb(4608,v.i,v.i,[[2,x.k]]),i.Rb(4608,v.r,v.r,[v.L,[2,x.k],v.i]),i.Rb(4608,v.t,v.t,[]),i.Rb(4608,v.w,v.w,[]),i.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),i.Rb(1073742336,g.b,g.b,[]),i.Rb(1073742336,m.n,m.n,[]),i.Rb(1073742336,m.l,m.l,[]),i.Rb(1073742336,k.a,k.a,[]),i.Rb(1073742336,L.a,L.a,[]),i.Rb(1073742336,m.e,m.e,[]),i.Rb(1073742336,B.a,B.a,[]),i.Rb(1073742336,x.i,x.i,[]),i.Rb(1073742336,v.b,v.b,[]),i.Rb(1073742336,w.e,w.e,[]),i.Rb(1073742336,w.d,w.d,[]),i.Rb(1073742336,C.e,C.e,[]),i.Rb(1073742336,P.b,P.b,[]),i.Rb(1073742336,N.b,N.b,[]),i.Rb(1073742336,y.c,y.c,[]),i.Rb(1073742336,T.a,T.a,[]),i.Rb(1073742336,A.d,A.d,[]),i.Rb(1073742336,D.c,D.c,[]),i.Rb(1073742336,S.a,S.a,[]),i.Rb(1073742336,G.a,G.a,[[2,y.g],i.O]),i.Rb(1073742336,J.b,J.b,[]),i.Rb(1073742336,_.a,_.a,[]),i.Rb(1073742336,M.b,M.b,[]),i.Rb(1073742336,o.Tb,o.Tb,[]),i.Rb(1073742336,d,d,[]),i.Rb(256,w.n,"XSRF-TOKEN",[]),i.Rb(256,w.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,x.m,void 0,[]),i.Rb(256,x.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,a.i,function(){return[[{path:"",component:s}]]},[]),i.Rb(256,"spreadDetails",s,[])])}),E=[[""]],V=i.Hb({encapsulation:0,styles:E,data:{}});function q(e){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{processNameInput:0}),i.Zb(402653184,3,{timeInput:0}),i.Zb(402653184,4,{targetInput:0}),i.Zb(402653184,5,{processCategoryComboBox:0}),i.Zb(402653184,6,{selectAllCheckbox:0}),i.Zb(402653184,7,{loadingImage:0}),i.Zb(402653184,8,{okButton:0}),i.Zb(402653184,9,{cancelButton:0}),i.Zb(402653184,10,{categoryCanvasGrid:0}),i.Zb(402653184,11,{informationLabel:0}),(e()(),i.Jb(11,0,null,null,87,"SwtModule",[["height","100%"],["paddingLeft","10"],["width","600"]],null,[[null,"close"],[null,"creationComplete"]],function(e,t,l){var i=!0,n=e.component;"close"===t&&(i=!1!==n.popupClosed()&&i);"creationComplete"===t&&(i=!1!==n.onLoad()&&i);return i},p.ad,p.hb)),i.Ib(12,4440064,null,0,o.yb,[i.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},{creationComplete:"creationComplete"}),(e()(),i.Jb(13,0,null,0,85,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(14,4440064,null,0,o.ec,[i.r,o.i,i.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(e()(),i.Jb(15,0,null,0,65,"SwtCanvas",[["height","89%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(16,4440064,null,0,o.db,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),i.Jb(17,0,null,0,63,"VBox",[["height","100%"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(18,4440064,null,0,o.ec,[i.r,o.i,i.T],{width:[0,"width"],height:[1,"height"]},null),(e()(),i.Jb(19,0,null,0,49,"Grid",[["height","35%"],["width","100%"]],null,null,null,p.Cc,p.H)),i.Ib(20,4440064,null,0,o.z,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),i.Jb(21,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(22,4440064,null,0,o.B,[i.r,o.i],null,null),(e()(),i.Jb(23,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),i.Ib(24,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(25,0,null,0,1,"SwtLabel",[["text","Process Name*"]],null,null,null,p.Yc,p.fb)),i.Ib(26,4440064,null,0,o.vb,[i.r,o.i],{text:[0,"text"]},null),(e()(),i.Jb(27,0,null,0,3,"GridItem",[["width","80%"]],null,null,null,p.Ac,p.I)),i.Ib(28,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(29,0,null,0,1,"SwtTextInput",[["height","22"],["id","processNameInput"],["maxChars","30"],["required","true"],["toolTip","Process Name"],["width","200"]],null,null,null,p.kd,p.sb)),i.Ib(30,4440064,[[2,4],["processNameInput",4]],0,o.Rb,[i.r,o.i],{maxChars:[0,"maxChars"],id:[1,"id"],toolTip:[2,"toolTip"],width:[3,"width"],height:[4,"height"],required:[5,"required"]},null),(e()(),i.Jb(31,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(32,4440064,null,0,o.B,[i.r,o.i],null,null),(e()(),i.Jb(33,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),i.Ib(34,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(35,0,null,0,1,"SwtLabel",[["text","Time*"]],null,null,null,p.Yc,p.fb)),i.Ib(36,4440064,null,0,o.vb,[i.r,o.i],{text:[0,"text"]},null),(e()(),i.Jb(37,0,null,0,3,"GridItem",[["width","80%"]],null,null,null,p.Ac,p.I)),i.Ib(38,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(39,0,null,0,1,"SwtTextInput",[["height","22"],["id","timeInput"],["maxChars","5"],["pattern","^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"],["required","true"],["textAlign","center"],["toolTip","Time"],["width","50"]],null,[[null,"focusOut"]],function(e,t,l){var n=!0,o=e.component;"focusOut"===t&&(n=!1!==o.validateTime(i.Tb(e,40))&&n);return n},p.kd,p.sb)),i.Ib(40,4440064,[[3,4],["timeInput",4]],0,o.Rb,[i.r,o.i],{maxChars:[0,"maxChars"],id:[1,"id"],textAlign:[2,"textAlign"],toolTip:[3,"toolTip"],width:[4,"width"],height:[5,"height"],required:[6,"required"]},{onFocusOut_:"focusOut"}),(e()(),i.Jb(41,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(42,4440064,null,0,o.B,[i.r,o.i],null,null),(e()(),i.Jb(43,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),i.Ib(44,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(45,0,null,0,1,"SwtLabel",[["text","Target %*"]],null,null,null,p.Yc,p.fb)),i.Ib(46,4440064,null,0,o.vb,[i.r,o.i],{text:[0,"text"]},null),(e()(),i.Jb(47,0,null,0,3,"GridItem",[["width","80%"]],null,null,null,p.Ac,p.I)),i.Ib(48,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(49,0,null,0,1,"SwtStepper",[["id","targetInput"],["maximum","100"],["minimum","1"],["required","true"],["toolTip","Target"],["width","24"]],null,null,null,p.gd,p.nb)),i.Ib(50,4833280,[[4,4],["targetInput",4]],0,o.Mb,[i.r],{minimum:[0,"minimum"],maximum:[1,"maximum"],toolTip:[2,"toolTip"],width:[3,"width"],id:[4,"id"],required:[5,"required"]},null),(e()(),i.Jb(51,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(52,4440064,null,0,o.B,[i.r,o.i],null,null),(e()(),i.Jb(53,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),i.Ib(54,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(55,0,null,0,1,"SwtLabel",[["text","Process Category"]],null,null,null,p.Yc,p.fb)),i.Ib(56,4440064,null,0,o.vb,[i.r,o.i],{text:[0,"text"]},null),(e()(),i.Jb(57,0,null,0,3,"GridItem",[["width","80%"]],null,null,null,p.Ac,p.I)),i.Ib(58,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(59,0,null,0,1,"SwtComboBox",[["dataLabel","processCategoryList"],["id","processCategoryComboBox"],["toolTip","Select Process Category"],["width","150"]],null,[[null,"change"],["window","mousewheel"]],function(e,t,l){var n=!0,o=e.component;"window:mousewheel"===t&&(n=!1!==i.Tb(e,60).mouseWeelEventHandler(l.target)&&n);"change"===t&&(n=!1!==o.changeComboProcessCategory()&&n);return n},p.Pc,p.W)),i.Ib(60,4440064,[[5,4],["processCategoryComboBox",4]],0,o.gb,[i.r,o.i],{dataLabel:[0,"dataLabel"],toolTip:[1,"toolTip"],width:[2,"width"],id:[3,"id"]},{change_:"change"}),(e()(),i.Jb(61,0,null,0,7,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(62,4440064,null,0,o.B,[i.r,o.i],null,null),(e()(),i.Jb(63,0,null,0,1,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),i.Ib(64,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(65,0,null,0,3,"GridItem",[["width","80%"]],null,null,null,p.Ac,p.I)),i.Ib(66,4440064,null,0,o.A,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(67,0,null,0,1,"SwtLabel",[["id","informationLabel"]],null,null,null,p.Yc,p.fb)),i.Ib(68,4440064,[[11,4],["informationLabel",4]],0,o.vb,[i.r,o.i],{id:[0,"id"]},null),(e()(),i.Jb(69,0,null,0,5,"HBox",[["height","55%"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(70,4440064,null,0,o.C,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),i.Jb(71,0,null,0,3,"SwtPanel",[["height","100%"],["paddingTop","5"],["title","Categories"],["width","100%"]],null,null,null,p.dd,p.kb)),i.Ib(72,4440064,null,0,o.Cb,[i.r,o.i,i.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],title:[3,"title"]},null),(e()(),i.Jb(73,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","categoryCanvasGrid"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(74,4440064,[[10,4],["categoryCanvasGrid",4]],0,o.db,[i.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(e()(),i.Jb(75,0,null,0,5,"HBox",[["height","5%"],["paddingLeft","15"],["paddingTop","5"]],null,null,null,p.Dc,p.K)),i.Ib(76,4440064,null,0,o.C,[i.r,o.i],{height:[0,"height"],paddingTop:[1,"paddingTop"],paddingLeft:[2,"paddingLeft"]},null),(e()(),i.Jb(77,0,null,0,1,"SwtCheckBox",[["id","selectAll"],["paddingTop","5"],["selected","false"]],null,[[null,"change"]],function(e,t,l){var i=!0,n=e.component;"change"===t&&(i=!1!==n.selectDeselectAll(l)&&i);return i},p.Oc,p.V)),i.Ib(78,4440064,[[6,4],["selectAll",4]],0,o.eb,[i.r,o.i],{id:[0,"id"],paddingTop:[1,"paddingTop"],selected:[2,"selected"]},{change_:"change"}),(e()(),i.Jb(79,0,null,0,1,"SwtLabel",[["paddingRight","5"],["text","Select All"]],null,null,null,p.Yc,p.fb)),i.Ib(80,4440064,null,0,o.vb,[i.r,o.i],{paddingRight:[0,"paddingRight"],text:[1,"text"]},null),(e()(),i.Jb(81,0,null,0,17,"SwtCanvas",[["height","9%"],["id","canvasContainer"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(82,4440064,null,0,o.db,[i.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),i.Jb(83,0,null,0,7,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(84,4440064,null,0,o.C,[i.r,o.i],{width:[0,"width"]},null),(e()(),i.Jb(85,0,null,0,5,"HBox",[["height","100%"],["style","margin-top: 3px; margin-left: 5px"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(86,4440064,null,0,o.C,[i.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),i.Jb(87,0,null,0,1,"SwtButton",[["id","okButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,l){var i=!0,n=e.component;"click"===t&&(i=!1!==n.save()&&i);"keyDown"===t&&(i=!1!==n.keyDownEventHandler(l)&&i);return i},p.Mc,p.T)),i.Ib(88,4440064,[[8,4],["okButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),i.Jb(89,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,l){var i=!0,n=e.component;"click"===t&&(i=!1!==n.closeBtn()&&i);"keyDown"===t&&(i=!1!==n.keyDownEventHandler(l)&&i);return i},p.Mc,p.T)),i.Ib(90,4440064,[[9,4],["cancelButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),i.Jb(91,0,null,0,7,"HBox",[["horizontalAlign","right"],["marginTop","5"],["paddingRight","10"]],null,null,null,p.Dc,p.K)),i.Ib(92,4440064,null,0,o.C,[i.r,o.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"],marginTop:[2,"marginTop"]},null),(e()(),i.Jb(93,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),i.Ib(94,114688,[[7,4],["loadingImage",4]],0,o.xb,[i.r],null,null),(e()(),i.Jb(95,0,null,0,1,"SwtButton",[["id","printButton"],["styleName","printIcon"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,l){var i=!0,n=e.component;"click"===t&&(i=!1!==n.printPage()&&i);"keyDown"===t&&(i=!1!==n.keyDownEventHandler(l)&&i);return i},p.Mc,p.T)),i.Ib(96,4440064,[["printButton",4]],0,o.cb,[i.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),i.Jb(97,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","spread-profile"],["id","helpIcon"]],null,[[null,"click"]],function(e,t,l){var i=!0,n=e.component;"click"===t&&(i=!1!==n.doHelp()&&i);return i},p.Wc,p.db)),i.Ib(98,4440064,null,0,o.rb,[i.r,o.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"})],function(e,t){e(t,12,0,"600","100%","10");e(t,14,0,"100%","100%","5","5","5","5");e(t,16,0,"100%","89%");e(t,18,0,"100%","100%");e(t,20,0,"100%","35%"),e(t,22,0);e(t,24,0,"20%");e(t,26,0,"Process Name*");e(t,28,0,"80%");e(t,30,0,"30","processNameInput","Process Name","200","22","true"),e(t,32,0);e(t,34,0,"20%");e(t,36,0,"Time*");e(t,38,0,"80%");e(t,40,0,"5","timeInput","center","Time","50","22","true"),e(t,42,0);e(t,44,0,"20%");e(t,46,0,"Target %*");e(t,48,0,"80%");e(t,50,0,"1","100","Target","24","targetInput","true"),e(t,52,0);e(t,54,0,"20%");e(t,56,0,"Process Category");e(t,58,0,"80%");e(t,60,0,"processCategoryList","Select Process Category","150","processCategoryComboBox"),e(t,62,0);e(t,64,0,"20%");e(t,66,0,"80%");e(t,68,0,"informationLabel");e(t,70,0,"100%","55%");e(t,72,0,"100%","100%","5","Categories");e(t,74,0,"categoryCanvasGrid","100%","100%","false");e(t,76,0,"5%","5","15");e(t,78,0,"selectAll","5","false");e(t,80,0,"5","Select All");e(t,82,0,"canvasContainer","100%","9%");e(t,84,0,"100%");e(t,86,0,"100%","100%");e(t,88,0,"okButton","70");e(t,90,0,"cancelButton","70","true");e(t,92,0,"right","10","5"),e(t,94,0);e(t,96,0,"printButton","printIcon",!0);e(t,98,0,"helpIcon","true",!0,"spread-profile")},null)}function H(e){return i.dc(0,[(e()(),i.Jb(0,0,null,null,1,"app-spread-details",[],null,null,null,q,V)),i.Ib(1,4440064,null,0,s,[o.i,i.r],null,null)],function(e,t){e(t,1,0)},null)}var W=i.Fb("app-spread-details",s,H,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);