<SwtModule  #swtModule  (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width='100%' height='100%' paddingLeft="5" paddingRight="5" paddingBottom="5" paddingTop="5">
    <!-- Top Section - Data Definition and Movement Totals -->
    <HBox width='100%' height='80' minWidth="1000" >
    <SwtFieldSet id="dataDefFieldSet" #dataDefFieldSet style="height: 70px; width: 950px; color:blue;">
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow width="100%" height="20">
              <GridItem width="150">
                <SwtLabel id="dataSource" marginTop="3" #dataSource></SwtLabel>
              </GridItem>
              <GridItem width="160" paddingRight="10">
                <SwtComboBox id="dataSourceCombo" #dataSourceCombo width="150" 
                dataLabel="dataSourcesList" (change)="updateDataTypeInfo()"> </SwtComboBox>
              </GridItem>
              <GridItem width="40">
                <input type="file" style="display: none" #file (change)="readUploadedFile($event)" (click)="onInputClick($event)" 
                accept="{{this.dataSourceCombo.selectedLabel=='Excel'? '.xlsx, .xls' : '.csv'}}"/>
                <SwtImage id="uploadImage" #uploadImage width="23" (click)="file.click()" styleName="imageStyle"></SwtImage>
              </GridItem>
              <GridItem width="250">
                <SwtLabel id="fileName" #fileName fontWeight="normal"></SwtLabel>
              </GridItem>
          </GridRow>
      
          <GridRow width="100%" height="20" paddingTop="10">
            <GridItem marginTop="-1" width="150">
              <SwtLabel id="mvtIdLocationLbl" #mvtIdLocationLbl></SwtLabel>
            </GridItem>
          <SwtRadioButtonGroup #mvtIdLocation id="mvtIdLocation" (change) ="enableDisableTxtInput()"  align="horizontal" width="100%" height="100%">
            <SwtRadioItem value="Na"  groupName="mvtIdLocation" enabled ="true" selected="true" id="colNameRadio" #colNameRadio>
            </SwtRadioItem>
              <GridItem width="200">
                <SwtTextInput #colNameTxt  text=""  width="180"></SwtTextInput>
              </GridItem>
            <SwtRadioItem value="Nu"  groupName="mvtIdLocation" enabled ="true"  id="colNumberRadio" #colNumberRadio>
            </SwtRadioItem>
              <GridItem width="120">
                <SwtTextInput #colNumberTxt textAlign="right"  width="60"></SwtTextInput>
              </GridItem>            
          </SwtRadioButtonGroup>
         
          <SwtButton [buttonMode]="true" marginTop="-2"  id="importButton" #importButton (click)="importData()">
          </SwtButton>
        </GridRow>     
      </Grid>
    </SwtFieldSet>

    <SwtFieldSet id="mvtTotalFieldSet" #mvtTotalFieldSet style="padding-bottom: 5px; height: 75px; width: 160;color:blue;">
      <Grid width="100%" height="100%" paddingLeft="5">
        <GridRow width="100%" height="26" paddingTop="2">
              <HBox width="80" horizontalAlign="right" paddingRight="10">
                <SwtLabel id="total" #total></SwtLabel>
              </HBox>
              <HBox>
                <SwtTextInput #totalTxt textAlign="right" maxChars="8" width="60"></SwtTextInput>
              </HBox>
        </GridRow>

        <GridRow width="100%" height="26" paddingTop="3">
              <HBox width="80" horizontalAlign="right" paddingRight="10">
                <SwtLabel id="selected" #selected></SwtLabel>
              </HBox>
              <HBox>
                <SwtTextInput #selectedTxt textAlign="right" maxChars="8" width="60"></SwtTextInput>
              </HBox>
        </GridRow>
      </Grid>
    </SwtFieldSet>

     <HBox #pageBox horizontalAlign="right"  visible="false">
            <SwtCommonGridPagination  #numstepper></SwtCommonGridPagination>
          </HBox>
    </HBox>

    <!-- Middle Section - Movements Grid -->
    <SwtFieldSet id="MvtsFieldSet" #MvtsFieldSet minHeight="200" minWidth="1000" style="padding-bottom: 5px; height: 100%; width: 100%;color:blue;">
      <SwtCanvas #mvtGridContainer id="mvtGridContainer"  styleName="canvasWithGreyBorder" width="100%" height="100%"
      border="false"></SwtCanvas>
    </SwtFieldSet>

    <!-- Bottom Section - Actions and Details -->
    <HBox minWidth="1000">
      <!-- Left Side - Actions List -->
      <SwtFieldSet id="actionFieldSet" #actionFieldSet style="padding-bottom: 5px; width: 20%; color:blue;">
        <Grid width="100%" height="100%" paddingLeft="5">
          <GridRow width="100%" height="25%">
            <SwtRadioButtonGroup #mvtAction id="mvtAction" (change)="showActionPanel();ChangeSelectedRadioButton()" align="vertical">
              <SwtRadioItem value="AN" groupName="mvtAction" selected="true" id="addNoteRadio" #addNoteRadio></SwtRadioItem>
              <SwtRadioItem value="US" groupName="mvtAction" id="updateStsRadio" #updateStsRadio></SwtRadioItem>
              <SwtRadioItem value="UN" groupName="mvtAction" id="unmatchRadio" #unmatchRadio></SwtRadioItem>
              <SwtRadioItem value="RE" groupName="mvtAction" id="reconcileRadio" #reconcileRadio></SwtRadioItem> 
              <SwtRadioItem value="UO" groupName="mvtAction" id="updateOtherRadio" #updateOtherRadio></SwtRadioItem>    
            </SwtRadioButtonGroup>
          </GridRow>
        </Grid>
      </SwtFieldSet>

      <!-- Right Side - Dynamic Content Panel -->
      <SwtFieldSet id="dynamicContentPanel" height="250" #dynamicContentPanel style="padding-bottom: 5px; width: 80%; color:blue;">
        <!-- Add Note Panel -->
        <Grid id="addNotePanel" #addNotePanel width="100%" height="100%" paddingLeft="5" [includeInLayout]="isAddNotePanelVisible" [visible]="isAddNotePanelVisible">
          <GridRow width="150">
            <SwtLabel id="noteLbl" #noteLbl></SwtLabel>
          </GridRow>
          <SwtTextArea id='noteText' #noteText (change)="enableDisableProcessBtn()" height="50%" width="100%" maxChars="200" editable="true">
          </SwtTextArea>
        </Grid>

        <!-- Update Status Panel -->
        <Grid id="updateStatusPanel" #updateStatusPanel width="100%" height="100%" paddingLeft="5"  [includeInLayout]="isUpdateStatusPanelVisible" [visible]="isUpdateStatusPanelVisible">
          <GridRow width="100%" height="25%">
            <HBox paddingLeft="20">
              <SwtFieldSet style="color:blue;" id="predictFieldSet" #predictFieldSet>
                <SwtRadioButtonGroup #predictStatus id="predictStatus" align="vertical">
                  <SwtRadioItem value="D" groupName="predictStatus" selected="true" id="notUpdateRadio" #notUpdateRadio></SwtRadioItem>
                  <SwtRadioItem value="I" groupName="predictStatus" id="includedRadio" #includedRadio></SwtRadioItem>
                  <SwtRadioItem value="E" groupName="predictStatus" id="excludedRadio" #excludedRadio></SwtRadioItem>
                  <SwtRadioItem value="C" groupName="predictStatus" id="cancelledRadio" #cancelledRadio></SwtRadioItem>
                </SwtRadioButtonGroup>
              </SwtFieldSet>
              <SwtFieldSet style="color:blue;" id="externalFieldSet" #externalFieldSet>
                <SwtRadioButtonGroup #externalStatus id="externalStatus" align="vertical">
                  <SwtRadioItem value="D" groupName="externalStatus" selected="true" id="notUpdateRadio1" #notUpdateRadio1></SwtRadioItem>
                  <SwtRadioItem value="I" groupName="externalStatus" id="includedRadio1" #includedRadio1></SwtRadioItem>
                  <SwtRadioItem value="E" groupName="externalStatus" id="excludedRadio1" #excludedRadio1></SwtRadioItem>
                </SwtRadioButtonGroup>
              </SwtFieldSet>
              <SwtFieldSet style="color:blue;" id="ilmFieldSet" #ilmFieldSet>
                <SwtRadioButtonGroup #ilmFcastStatus id="ilmFcastStatus" align="vertical">
                  <SwtRadioItem value="D" groupName="ilmFcastStatus" selected="true" id="notUpdateRadio2" #notUpdateRadio2></SwtRadioItem>
                  <SwtRadioItem value="I" groupName="ilmFcastStatus" id="includedRadio2" #includedRadio2></SwtRadioItem>
                  <SwtRadioItem value="E" groupName="ilmFcastStatus" id="excludedRadio2" #excludedRadio2></SwtRadioItem>
                </SwtRadioButtonGroup>
              </SwtFieldSet>
              <SwtFieldSet style="color:blue;" id="internalSttlmFieldSet" #internalSttlmFieldSet>
                <SwtRadioButtonGroup #internalSttlmStatus id="internalSttlmStatus" align="vertical">
                  <SwtRadioItem value="D" groupName="internalSttlmStatus" selected="true" id="notUpdateRadio3" #notUpdateRadio3></SwtRadioItem>
                  <SwtRadioItem value="Y" groupName="internalSttlmStatus" id="yesRadio" #yesRadio></SwtRadioItem>
                  <SwtRadioItem value="N" groupName="internalSttlmStatus" id="noRadio" #noRadio></SwtRadioItem>
                </SwtRadioButtonGroup>
              </SwtFieldSet>
            </HBox>
          </GridRow>
          <GridRow paddingTop="30"  width="150">
            <SwtLabel id="noteLbl2" paddingTop="4"  #noteLbl2></SwtLabel>
          </GridRow>
          <SwtTextArea  id='noteText2' #noteText2 (change)="enableDisableProcessBtn()" height="50%" width="100%" maxChars="200" editable="true">
          </SwtTextArea>
        </Grid>


        <!-- Unmatch Panel (placeholder - you can add specific content) -->
        <Grid id="unmatchPanel" #unmatchPanel width="100%" height="100%" paddingLeft="5" [includeInLayout]="isUnmatchPanelVisible" [visible]="isUnmatchPanelVisible">
          <GridRow width="150">
            <SwtLabel id="noteLbl3" #noteLbl3></SwtLabel>
          </GridRow>
          <SwtTextArea id='noteText3' #noteText3 (change)="enableDisableProcessBtn()" height="50%" width="100%" maxChars="200" editable="true">
          </SwtTextArea>
        </Grid>
        
        <!-- Reconcile Panel (placeholder - you can add specific content) -->
        <Grid id="reconcilePanel" #reconcilePanel width="100%" height="100%" paddingLeft="5" [includeInLayout]="isReconcilePanelVisible" [visible]="isReconcilePanelVisible">
          <GridRow width="150">
            <SwtLabel id="noteLbl4" #noteLbl4></SwtLabel>
          </GridRow>
          <SwtTextArea id='noteText4' #noteText4 (change)="enableDisableProcessBtn()" height="50%" width="100%" maxChars="200" editable="true">
          </SwtTextArea>
        </Grid>


         <!-- Update Other Panel -->
         <Grid id="updateOtherPanel" #updateOtherPanel width="100%" height="100%" paddingLeft="5"  [includeInLayout]="isUpdateOtherPanelVisible" [visible]="isUpdateOtherPanelVisible">
          <GridRow width="100%" height="20">
            <GridItem width="420">
              <HBox width="50" horizontalAlign="right">
                <SwtLabel text="Enable"></SwtLabel>
              </HBox>
              <HBox width="120" horizontalAlign="right">
              </HBox>
              <HBox width="200">
              </HBox>
              <SwtLabel text="Null?"></SwtLabel>
            </GridItem>
            <GridItem width="440">
              <HBox width="50" horizontalAlign="right">
                <SwtLabel text="Enable"></SwtLabel>
              </HBox>
              <HBox width="140" horizontalAlign="right">
              </HBox>
              <HBox width="200">
              </HBox>
              <SwtLabel text="Null?"></SwtLabel>
            </GridItem>
          </GridRow>

          <GridRow width="100%" height="26">
            <GridItem #updateBookCode id="updateBookCode" width="420">
              <GridItem width="50">
                <SwtCheckBox id="bookEnableCheckbox" #bookEnableCheckbox styleName="checkbox" (change)="toggleFieldEnable(bookEnableCheckbox, bookCombo, bookCheckbox)"></SwtCheckBox>
              </GridItem>
              <GridItem  width="120">
                <SwtLabel id="bookLbl" #bookLbl></SwtLabel>
              </GridItem>
              <GridItem width="200">
                <SwtComboBox id="bookCombo" #bookCombo width="180" dataLabel="bookCodeList"></SwtComboBox>
              </GridItem>
              <GridItem>
                <SwtCheckBox id="bookCheckbox" #bookCheckbox styleName="checkbox" (change)="toggleField(bookCheckbox, bookCombo)"></SwtCheckBox>
              </GridItem>
            </GridItem>
            <GridItem>
              <GridItem width="50">
                <SwtCheckBox id="ordInstEnableCheckbox" #ordInstEnableCheckbox styleName="checkbox" (change)="toggleFieldEnable(ordInstEnableCheckbox, ordInstTxtInput, ordInstCheckbox)"></SwtCheckBox>
              </GridItem>
              <HBox width="140">
                <SwtLabel id="ordInstLbl" #ordInstLbl></SwtLabel>
              </HBox>
              <HBox width="200">
                <SwtTextInput #ordInstTxtInput id="ordInstTxtInput" editable="true" width="180"></SwtTextInput>
              </HBox>
              <GridItem>
                <SwtCheckBox id="ordInstCheckbox" #ordInstCheckbox styleName="checkbox" (change)="toggleField(ordInstCheckbox, ordInstTxtInput)"></SwtCheckBox>
              </GridItem>
            </GridItem>
          </GridRow>
                
          <GridRow width="100%" height="26">
            <GridItem width="420">
              <GridItem width="50">
                <SwtCheckBox id="critPayTypeEnableCheckbox" #critPayTypeEnableCheckbox styleName="checkbox" (change)="toggleFieldEnable(critPayTypeEnableCheckbox, critPayTypeTxtInput, critPayTypeCheckbox)"></SwtCheckBox>
              </GridItem>
              <GridItem width="120">
                <SwtLabel id="critPayTypeLbl" #critPayTypeLbl></SwtLabel>
              </GridItem>
              <GridItem width="200">
                <SwtTextInput #critPayTypeTxtInput id="critPayTypeTxtInput" editable="true" width="180"></SwtTextInput>
              </GridItem>
              <GridItem>
                <SwtCheckBox id="critPayTypeCheckbox" #critPayTypeCheckbox styleName="checkbox" (change)="toggleField(critPayTypeCheckbox, critPayTypeTxtInput)"></SwtCheckBox>
              </GridItem>
            </GridItem>
            <GridItem>
              <GridItem width="50">
                <SwtCheckBox id="counterPartyEnableCheckbox" #counterPartyEnableCheckbox styleName="checkbox" (change)="toggleFieldEnable(counterPartyEnableCheckbox, counterPartyTxtInput, counterPartyCheckbox)"></SwtCheckBox>
              </GridItem>
              <HBox width="140">
                <SwtLabel id="counterPartyLbl" #counterPartyLbl></SwtLabel>
              </HBox>
              <HBox width="200">
                <SwtTextInput #counterPartyTxtInput id="counterPartyTxtInput" editable="true" width="180"></SwtTextInput>
              </HBox>
              <GridItem>
                <SwtCheckBox id="counterPartyCheckbox" #counterPartyCheckbox styleName="checkbox" (change)="toggleField(counterPartyCheckbox, counterPartyTxtInput)"></SwtCheckBox>
              </GridItem>
            </GridItem>
          </GridRow>
                
          <GridRow width="100%" height="26">
            <GridItem width="420">
              <GridItem width="50">
                <SwtCheckBox id="expSettlEnableCheckbox" #expSettlEnableCheckbox styleName="checkbox" (change)="toggleFieldEnable(expSettlEnableCheckbox, expSettlField, expSettlCheckbox, expSettlTimeField)"></SwtCheckBox>
              </GridItem>
              <GridItem width="120">
                <SwtLabel id="expSettlLbl" #expSettlLbl></SwtLabel>
              </GridItem>
              <GridItem width="200">
                <HBox>
                  <SwtDateField id="expSettlField" #expSettlField width="70"></SwtDateField>
                  <SwtTextInput id="expSettlTimeField" #expSettlTimeField width="70" maxChars="8"
                  (focusOut)="validateTime(expSettlTimeField)"></SwtTextInput>
                </HBox>
              </GridItem>
              <GridItem>
                <SwtCheckBox id="expSettlCheckbox" #expSettlCheckbox styleName="checkbox" (change)="toggleField(expSettlCheckbox, expSettlField, expSettlTimeField)"></SwtCheckBox>
              </GridItem>
            </GridItem>
            <GridItem>
              <GridItem width="50">
                <SwtCheckBox id="actualSettlEnableCheckbox" #actualSettlEnableCheckbox styleName="checkbox" (change)="toggleFieldEnable(actualSettlEnableCheckbox, actualSettlField, actualSettlCheckbox, actualSettlTimeField)"></SwtCheckBox>
              </GridItem>
              <HBox width="140" paddingRight="5">
                <SwtLabel id="actualSettlLbl" #actualSettlLbl></SwtLabel>
              </HBox>
              <HBox width="200">
                <HBox>
                  <SwtDateField id="actualSettlField" #actualSettlField width="70"></SwtDateField>
                  <SwtTextInput id="actualSettlTimeField" #actualSettlTimeField width="70" maxChars="8"
                  (focusOut)="validateTime(actualSettlTimeField)"></SwtTextInput>
                </HBox>
              </HBox>
              <GridItem>
                <SwtCheckBox id="actualSettlCheckbox" #actualSettlCheckbox styleName="checkbox" (change)="toggleField(actualSettlCheckbox, actualSettlField, actualSettlTimeField)"></SwtCheckBox>
              </GridItem>
            </GridItem>
          </GridRow>
          <GridRow width="150">
            <SwtLabel id="noteLbl5" #noteLbl5></SwtLabel>
          </GridRow>
          <SwtTextArea id='noteText5' #noteText5 (change)="enableDisableProcessBtn()" height="50%" width="100%" maxChars="200" editable="true">
          </SwtTextArea>
        </Grid>

      </SwtFieldSet>
    </HBox>

    <!-- Bottom Buttons -->
    <SwtCanvas width="100%" height="35" minWidth="1000">
      <HBox width="100%" top="1">
        <HBox paddingLeft="5" width="80%">
          <SwtButton [buttonMode]="true" id="processButton" #processButton (click)="processHandler()" enabled="false">
          </SwtButton>
        </HBox>
        <HBox width="20%" horizontalAlign="right" paddingRight="10">
          <SwtButton [buttonMode]="true" id="closeButton" #closeButton (click)="closeHandler()">
          </SwtButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>