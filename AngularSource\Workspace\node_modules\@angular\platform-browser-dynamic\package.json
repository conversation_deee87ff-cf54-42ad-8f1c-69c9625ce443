{"_from": "@angular/platform-browser-dynamic@7.2.4", "_id": "@angular/platform-browser-dynamic@7.2.4", "_inBundle": false, "_integrity": "sha512-J/xWlmaYOPUoCHZ5TiIRiyYa4uRMtCz3aGdBfY8k/NWtNo8SCYaS3aut7Sk4RS5rK8aAVi+aYFlY5YOrlW+Hbg==", "_location": "/@angular/platform-browser-dynamic", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/platform-browser-dynamic@7.2.4", "name": "@angular/platform-browser-dynamic", "escapedName": "@angular%2fplatform-browser-dynamic", "scope": "@angular", "rawSpec": "7.2.4", "saveSpec": null, "fetchSpec": "7.2.4"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@angular/platform-browser-dynamic/-/platform-browser-dynamic-7.2.4.tgz", "_shasum": "24dce1bb0d9dab541b3b1b3eda3084a732f11b64", "_spec": "@angular/platform-browser-dynamic@7.2.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "description": "Angular - library for using Angular in a web browser with JIT compilation", "es2015": "./fesm2015/platform-browser-dynamic.js", "esm2015": "./esm2015/platform-browser-dynamic.js", "esm5": "./esm5/platform-browser-dynamic.js", "fesm2015": "./fesm2015/platform-browser-dynamic.js", "fesm5": "./fesm5/platform-browser-dynamic.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/platform-browser-dynamic.umd.js", "module": "./fesm5/platform-browser-dynamic.js", "name": "@angular/platform-browser-dynamic", "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/http", "@angular/platform-server", "@angular/platform-webworker", "@angular/platform-webworker-dynamic", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/service-worker"]}, "peerDependencies": {"@angular/core": "7.2.4", "@angular/common": "7.2.4", "@angular/compiler": "7.2.4", "@angular/platform-browser": "7.2.4"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "sideEffects": false, "typings": "./platform-browser-dynamic.d.ts", "version": "7.2.4"}