{"$schema": "../../node_modules/ng-packagr/package.schema.json", "_from": "@ctrl/ngx-codemirror@^1.3.9", "_id": "@ctrl/ngx-codemirror@1.3.10", "_inBundle": false, "_integrity": "sha512-BvjSab31w3qA5pqVzxY9TkpNWORJIATssxAuYJbJLimHIoMCTnAgMaL3WRkfa7yFsAP4Sr4J9ybTHwHd2N+9Xw==", "_location": "/@ctrl/ngx-codemirror", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@ctrl/ngx-codemirror@^1.3.9", "name": "@ctrl/ngx-codemirror", "escapedName": "@ctrl%2fngx-codemirror", "scope": "@ctrl", "rawSpec": "^1.3.9", "saveSpec": null, "fetchSpec": "^1.3.9"}, "_requiredBy": ["/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@ctrl/ngx-codemirror/-/ngx-codemirror-1.3.10.tgz", "_shasum": "2809eb2a0a756e77e601123644dbba4d8215a7c0", "_spec": "@ctrl/ngx-codemirror@^1.3.9", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "bugs": {"url": "https://github.com/typectrl/ngx-codemirror/issues"}, "bundleDependencies": false, "dependencies": {"@types/codemirror": "0.0.72", "tslib": "^1.9.0"}, "deprecated": false, "description": "CodeMirror wrapper for Angular", "es2015": "fesm2015/ctrl-ngx-codemirror.js", "esm2015": "esm2015/ctrl-ngx-codemirror.js", "esm5": "esm5/ctrl-ngx-codemirror.js", "fesm2015": "fesm2015/ctrl-ngx-codemirror.js", "fesm5": "fesm5/ctrl-ngx-codemirror.js", "homepage": "https://github.com/typectrl/ngx-codemirror", "keywords": ["ngx", "angular", "wrapper", "codemirror"], "license": "MIT", "main": "bundles/ctrl-ngx-codemirror.umd.js", "metadata": "ctrl-ngx-codemirror.metadata.json", "module": "fesm5/ctrl-ngx-codemirror.js", "name": "@ctrl/ngx-codemirror", "peerDependencies": {"@angular/core": "^5.2.0 || ^6.0.0 || ^7.0.0", "@angular/common": "^5.2.0 || ^6.0.0 || ^7.0.0", "@angular/forms": "^5.2.0 || ^6.0.0 || ^7.0.0", "codemirror": "^5.39.0"}, "repository": {"type": "git", "url": "git+https://github.com/typectrl/ngx-codemirror.git"}, "sideEffects": false, "typings": "ctrl-ngx-codemirror.d.ts", "version": "1.3.10"}