(window.webpackJsonp=window.webpackJsonp||[]).push([[41],{KuCf:function(t,e,l){"use strict";l.r(e);var n=l("CcnG"),i=l("mrSG"),o=l("447K"),a=l("ZYCi"),c=function(t){function e(e,l){var n=t.call(this,l,e)||this;return n.commonService=e,n.element=l,n.baseURL=o.Wb.getBaseURL(),n.actionPath="",n.actionMethod="",n.inputData=new o.G(n.commonService),n.saveData=new o.G(n.commonService),n.jsonReader=new o.L,n.comboOpen=!1,n.comboChange=!1,n.requestParams=new Array,n.menuAccessIdParent=0,n.ccyList=null,n.itemId="",n.screenName="",n.hostId=null,n.entityId=null,n.accountId=null,n.currencyId=null,n.specifiedAccountId=null,n.specifiedEntityId=null,n.csrfId=null,n.parentMethodName=null,n._closeWindow=!1,n.versionNumber="1",n.ASCII_RESTRICT_PATTERN="A-Za-z0-9d_ !\"#$%&'()*+,-./:;<=>?@[\\]^`{|}~",n.ACCOUNT_NAME_RESTRICT_PATTERN="a-zA-Z0-9d .,:;#()*?[]%>_+=^|\\+/",n.logger=new o.R("Account Specific Sweep Format Add",n.commonService.httpclient),n.swtAlert=new o.bb(e),n}return i.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this},e.prototype.onLoad=function(){var t=this;try{this.requestParams=new Array,this.menuAccessIdParent=o.x.call("eval","menuAccessIdParent"),this.screenName=o.x.call("eval","screenName"),this.hostId=o.x.call("eval","hostId"),this.entityId=o.x.call("eval","entityId"),this.currencyId=o.x.call("eval","currencyId"),this.accountId=o.x.call("eval","accountId"),this.parentMethodName=o.x.call("eval","parentMethodName"),this.csrfId=o.x.call("eval","id"),this.specifiedAccountId=o.x.call("eval","specifiedAccountId"),this.specifiedEntityId=o.x.call("eval","specifiedEntityId"),1!=this.menuAccessIdParent&&"viewScreen"!=this.screenName||(this.okButton.enabled=!1),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="accountSpecificSweepFormat.do?",this.actionMethod="method=displayAccountSpecificSweepFormat",this.actionMethod+="&loadFlex=true",null!=this.screenName&&(this.actionMethod+="&screenName="+this.screenName,this.actionMethod+="&entityId="+this.entityId,this.actionMethod+="&currencyId="+this.currencyId,this.actionMethod+="&accountId="+this.accountId,this.actionMethod+="&specifiedAccountId="+this.specifiedAccountId,this.actionMethod+="&specifiedEntityId="+this.specifiedEntityId,this.actionMethod+="&id="+this.csrfId,this.actionMethod+="&parentMethodName="+this.parentMethodName),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveData.cbResult=function(e){t.saveDataResult(e)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1,this.inputData.send(this.requestParams)}catch(e){this.logger.error("Method [onLoad]",e)}},e.prototype.saveDataResult=function(t){try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.lastRecievedJSON.request_reply.status_ok?o.x.call("closeWindow"):"errors.DataIntegrityViolationExceptioninAdd"==this.jsonReader.getRequestReplyMessage()?this.swtAlert.show(o.x.call("getBundle","alert","label-recordExistsAlert","Record already exists"),o.x.call("getBundle","text","alert-warning","Warning")):this.swtAlert.show(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error",o.c.OK,this,this.errorHandler.bind(this)))}catch(e){console.log(e),this.logger.error("Method [saveDataResult]",e)}},e.prototype.errorHandler=function(t){t.detail==o.c.OK&&this._closeWindow&&(this._closeWindow=!1)},e.prototype.inputDataResult=function(t){try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastRecievedJSON!=this.prevRecievedJSON&&(this.entityCombo.enabled=!1,this.ccyCombo.enabled=!1,this.accountCombo.enabled=!1,null!=this.screenName&&"viewScreen"==this.screenName&&this.disableInterface(),null!=this.screenName&&"changeScreen"==this.screenName&&(this.specificAccountCombo.enabled=!1,this.specificEntityCombo.enabled=!1),this.entityCombo.setComboData(this.jsonReader.getSelects(),!1),this.selectedEntity.text=this.entityCombo.selectedValue,this.ccyCombo.setComboData(this.jsonReader.getSelects(),!1),this.accountCombo.setComboData(this.jsonReader.getSelects(),!1),null!=this.parentMethodName&&"add"==this.parentMethodName?(this.accountCombo.selectedIndex=-1,this.selectedAccount.text=""):this.selectedAccount.text=this.accountCombo.selectedValue,this.selectedCcy.text=this.ccyCombo.selectedValue,null!=this.screenName&&"addScreen"==this.screenName?(this.specificAccountCombo.setComboDataAndForceSelected(this.jsonReader.getSelects(),!1,""),this.specificEntityCombo.setComboDataAndForceSelected(this.jsonReader.getSelects(),!1,this.entityId)):(this.specificAccountCombo.setComboData(this.jsonReader.getSelects(),!1),this.specificEntityCombo.setComboData(this.jsonReader.getSelects(),!1),this.specificEntityCombo.selectedLabel=this.specifiedEntityId,this.specificAccountCombo.selectedLabel=this.specifiedAccountId),this.selectedSpecificAccount.text=this.specificAccountCombo.selectedValue,this.selectedSpecificEntity.text=this.specificEntityCombo.selectedValue,this.newInternalCrFormatTextField.setComboData(this.jsonReader.getSelects(),!1),this.newInternalCrFormatTextField.selectedIndex=this.comboboxIndexForValue(this.newInternalCrFormatTextField,this.jsonReader.getSingletons().newInternalCrFormat),this.newInternalDrFormatTextField.setComboData(this.jsonReader.getSelects(),!1),this.newInternalDrFormatTextField.selectedIndex=this.comboboxIndexForValue(this.newInternalDrFormatTextField,this.jsonReader.getSingletons().newInternalDrFormat),this.newExternalCrFormatTextField.setComboData(this.jsonReader.getSelects(),!1),this.newExternalCrFormatTextField.selectedIndex=this.comboboxIndexForValue(this.newInternalCrFormatTextField,this.jsonReader.getSingletons().newExternalCrFormat),this.newExternalDrFormatTextField.setComboData(this.jsonReader.getSelects(),!1),this.newExternalDrFormatTextField.selectedIndex=this.comboboxIndexForValue(this.newInternalCrFormatTextField,this.jsonReader.getSingletons().newExternalDrFormat),this.newExternalCrFormatIntTextField.setComboData(this.jsonReader.getSelects(),!1),this.newExternalCrFormatIntTextField.selectedIndex=this.comboboxIndexForValue(this.newExternalCrFormatIntTextField,this.jsonReader.getSingletons().newExternalCrFormatInt),this.newExternalDrFormatIntTextField.setComboData(this.jsonReader.getSelects(),!1),this.newExternalDrFormatIntTextField.selectedIndex=this.comboboxIndexForValue(this.newExternalDrFormatIntTextField,this.jsonReader.getSingletons().newExternalDrFormatINt)))}catch(e){this.logger.error("Method [inputDataResult]",e)}},e.prototype.comboboxIndexForValue=function(t,e){for(var l=0;l<t.dataProvider.length;l++)if(t.dataProvider[l].content==e)return l;return 0},e.prototype.disableInterface=function(){this.entityCombo.enabled=!1,this.ccyCombo.enabled=!1,this.accountCombo.enabled=!1,this.specificAccountCombo.enabled=!1,this.specificEntityCombo.enabled=!1,this.newInternalCrFormatTextField.enabled=!1,this.newInternalDrFormatTextField.enabled=!1,this.newExternalCrFormatTextField.enabled=!1,this.newExternalDrFormatTextField.enabled=!1,this.newExternalCrFormatIntTextField.enabled=!1,this.newExternalDrFormatIntTextField.enabled=!1},e.prototype.enableInterface=function(){this.specificAccountCombo.enabled=!0,this.specificEntityCombo.enabled=!0,this.newInternalCrFormatTextField.enabled=!0,this.newInternalDrFormatTextField.enabled=!0,this.newExternalCrFormatTextField.enabled=!0,this.newExternalDrFormatTextField.enabled=!0,this.newExternalCrFormatIntTextField.enabled=!0,this.newExternalDrFormatIntTextField.enabled=!0},e.prototype.inputDataFault=function(t){this.invalidComms=t.fault.faultString+""+t.fault.faultCode+t.fault.faultDetail,this.swtAlert.show(this.invalidComms)},e.prototype.closeHandler=function(t){o.x.call("close")},e.prototype.startOfComms=function(){this.disableInterface()},e.prototype.endOfComms=function(){this.enableInterface()},e.prototype.isExistingDataMethod=function(t,e,l){return null==e&&(e=""),1==o.x.call("checkExistingDataMethod",t.content,e.content,l.content,this.csrfId,this.parentMethodName)},e.prototype.confirmChange=function(t){t.detail!=o.c.CANCEL&&this.saveAccountSpecificSweepFormat()},e.prototype.saveAccountSpecificSweepFormat=function(){var t=this;try{this.saveData.cbStart=this.startOfComms.bind(this),this.saveData.cbStop=this.endOfComms.bind(this),this.saveData.cbResult=function(e){t.saveDataResult(e)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1,this.actionPath="accountSpecificSweepFormat.do?",this.actionMethod="method=saveAccountSpecificSweepFormat",this.saveData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.screenName=this.screenName,this.requestParams.entityId=this.entityCombo.selectedLabel,this.requestParams.accountId=this.accountCombo.selectedLabel,this.requestParams.id=this.csrfId,this.requestParams.specifiedAccountId=this.specificAccountCombo.selectedLabel,this.requestParams.specifiedEntityId=this.specificEntityCombo.selectedLabel,this.requestParams.newInternalCrFormat=this.newInternalCrFormatTextField.selectedLabel,this.requestParams.newInternalDrFormat=this.newInternalDrFormatTextField.selectedLabel,this.requestParams.newExternalCrFormat=this.newExternalCrFormatTextField.selectedLabel,this.requestParams.newExternalDrFormat=this.newExternalDrFormatTextField.selectedLabel,this.requestParams.newExternalCrFormatInt=this.newExternalCrFormatIntTextField.selectedLabel,this.requestParams.newExternalDrFormatINt=this.newExternalDrFormatIntTextField.selectedLabel,this.requestParams.specifiedAccountName=this.specificAccountCombo.selectedValue,this.requestParams.specifiedEntityName=this.specificEntityCombo.selectedValue,this.requestParams.parentMethodName=this.parentMethodName,this.saveData.send(this.requestParams)}catch(e){this.logger.error("Method [saveAccountSpecificSweepFormat]",e)}},e.prototype.saveHandler=function(t){try{this.specificAccountCombo.selectedLabel&&this.specificEntityCombo.selectedLabel&&-1!=this.specificAccountCombo.selectedIndex&&-1!=this.specificEntityCombo.selectedIndex?"addScreen"==this.screenName&&this.isExistingDataMethod(this.entityCombo.selectedItem,this.accountCombo.selectedItem,this.specificAccountCombo.selectedItem)?this.swtAlert.show(o.x.call("getBundle","alert","label-warningExistAccountData","Data exists for this account. If you change the definition of the account you must ensure that the existing data is consistent with the new settings"),o.x.call("getBundle","alert","alert-warning","Warning"),o.c.OK|o.c.CANCEL,null,this.confirmChange.bind(this),null,0):this.saveAccountSpecificSweepFormat():this.specificEntityCombo.selectedLabel?this.swtAlert.show(o.x.call("getBundle","alert","label-specAcctIdRequired","Specific Acount ID is required"),o.x.call("getBundle","alert","alert-warning","Warning")):this.swtAlert.show(o.x.call("getBundle","alert","label-specEntityIdRequired","Specific Entity ID is required"),o.x.call("getBundle","alert","alert-warning","Warning"))}catch(e){this.logger.error("Method [saveHandler]",e)}},e.prototype.openedCombo=function(t){this.comboOpen=!0,this.inputData.isBusy()&&(this.inputData.cancel(),"SwtComboBox"==t.currentTarget&&(t.currentTarget.interruptComms=!0))},e.prototype.closedCombo=function(t){this.comboOpen=!1,"SwtComboBox"==t.currentTarget&&(t.currentTarget.interruptComms=!0)},e.prototype.changeCombo=function(t){this.comboOpen=!0,this.selectedSpecificAccount.text=this.specificAccountCombo.selectedValue,this.selectedSpecificEntity.text=this.specificEntityCombo.selectedValue},e.prototype.updateComboData=function(t){var e=this;setTimeout(function(){e.requestParams=[],e.comboOpen=!0,e.selectedSpecificAccount.text=e.specificAccountCombo.selectedValue,e.selectedSpecificEntity.text=e.specificEntityCombo.selectedValue,e.inputData.cbStart=e.startOfComms.bind(e),e.inputData.cbStop=e.endOfComms.bind(e),e.inputData.cbResult=function(t){e.refreshComboList(t)},e.inputData.cbFault=e.inputDataFault.bind(e),e.inputData.encodeURL=!1,e.actionPath="accountSpecificSweepFormat.do?",e.actionMethod="method=getUpdatedLists",e.requestParams.accountId=e.accountCombo.selectedLabel,e.requestParams.specifiedEntityId=e.specificEntityCombo.selectedLabel,e.requestParams.currencyId=e.ccyCombo.selectedLabel,e.inputData.url=e.baseURL+e.actionPath+e.actionMethod,e.inputData.send(e.requestParams)},500)},e.prototype.refreshComboList=function(t){try{this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()?this.jsonReader.isDataBuilding()||(this.specificAccountCombo.setComboData(this.jsonReader.getSelects()),this.specificAccountCombo.selectedLabel="",this.selectedSpecificAccount.text=""):this.swtAlert.show(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error",o.c.OK,this,this.errorHandler.bind(this))}catch(e){this.logger.error("Method [refreshComboList]",e)}},e.prototype.comboBoxfocusOutHandler=function(t){},e.prototype.doHelp=function(){o.x.call("help")},e}(o.yb),r=[{path:"",component:c}],d=(a.l.forChild(r),function(){return function(){}}()),s=l("pMnS"),u=l("RChO"),h=l("t6HQ"),b=l("WFGK"),m=l("5FqG"),w=l("Ip0R"),p=l("gIcY"),f=l("t/Na"),I=l("sE5F"),x=l("OzfB"),g=l("T7CS"),y=l("S7LP"),C=l("6aHO"),F=l("WzUx"),R=l("A7o+"),A=l("zCE2"),S=l("Jg5P"),D=l("3R0m"),v=l("hhbb"),E=l("5rxC"),L=l("Fzqc"),T=l("21Lb"),B=l("hUWP"),N=l("3pJQ"),J=l("V9q+"),_=l("VDKW"),P=l("kXfT"),M=l("BGbe");l.d(e,"AccountSpecificSweepFormatAddModuleNgFactory",function(){return W}),l.d(e,"RenderType_AccountSpecificSweepFormatAdd",function(){return G}),l.d(e,"View_AccountSpecificSweepFormatAdd_0",function(){return O}),l.d(e,"View_AccountSpecificSweepFormatAdd_Host_0",function(){return k}),l.d(e,"AccountSpecificSweepFormatAddNgFactory",function(){return q});var W=n.Gb(d,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[s.a,u.a,h.a,b.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,q]],[3,n.n],n.J]),n.Rb(4608,w.m,w.l,[n.F,[2,w.u]]),n.Rb(4608,p.c,p.c,[]),n.Rb(4608,p.p,p.p,[]),n.Rb(4608,f.j,f.p,[w.c,n.O,f.n]),n.Rb(4608,f.q,f.q,[f.j,f.o]),n.Rb(5120,f.a,function(t){return[t,new o.tb]},[f.q]),n.Rb(4608,f.m,f.m,[]),n.Rb(6144,f.k,null,[f.m]),n.Rb(4608,f.i,f.i,[f.k]),n.Rb(6144,f.b,null,[f.i]),n.Rb(4608,f.f,f.l,[f.b,n.B]),n.Rb(4608,f.c,f.c,[f.f]),n.Rb(4608,I.c,I.c,[]),n.Rb(4608,I.g,I.b,[]),n.Rb(5120,I.i,I.j,[]),n.Rb(4608,I.h,I.h,[I.c,I.g,I.i]),n.Rb(4608,I.f,I.a,[]),n.Rb(5120,I.d,I.k,[I.h,I.f]),n.Rb(5120,n.b,function(t,e){return[x.j(t,e)]},[w.c,n.O]),n.Rb(4608,g.a,g.a,[]),n.Rb(4608,y.a,y.a,[]),n.Rb(4608,C.a,C.a,[n.n,n.L,n.B,y.a,n.g]),n.Rb(4608,F.c,F.c,[n.n,n.g,n.B]),n.Rb(4608,F.e,F.e,[F.c]),n.Rb(4608,R.l,R.l,[]),n.Rb(4608,R.h,R.g,[]),n.Rb(4608,R.c,R.f,[]),n.Rb(4608,R.j,R.d,[]),n.Rb(4608,R.b,R.a,[]),n.Rb(4608,R.k,R.k,[R.l,R.h,R.c,R.j,R.b,R.m,R.n]),n.Rb(4608,F.i,F.i,[[2,R.k]]),n.Rb(4608,F.r,F.r,[F.L,[2,R.k],F.i]),n.Rb(4608,F.t,F.t,[]),n.Rb(4608,F.w,F.w,[]),n.Rb(**********,a.l,a.l,[[2,a.r],[2,a.k]]),n.Rb(**********,w.b,w.b,[]),n.Rb(**********,p.n,p.n,[]),n.Rb(**********,p.l,p.l,[]),n.Rb(**********,A.a,A.a,[]),n.Rb(**********,S.a,S.a,[]),n.Rb(**********,p.e,p.e,[]),n.Rb(**********,D.a,D.a,[]),n.Rb(**********,R.i,R.i,[]),n.Rb(**********,F.b,F.b,[]),n.Rb(**********,f.e,f.e,[]),n.Rb(**********,f.d,f.d,[]),n.Rb(**********,I.e,I.e,[]),n.Rb(**********,v.b,v.b,[]),n.Rb(**********,E.b,E.b,[]),n.Rb(**********,x.c,x.c,[]),n.Rb(**********,L.a,L.a,[]),n.Rb(**********,T.d,T.d,[]),n.Rb(**********,B.c,B.c,[]),n.Rb(**********,N.a,N.a,[]),n.Rb(**********,J.a,J.a,[[2,x.g],n.O]),n.Rb(**********,_.b,_.b,[]),n.Rb(**********,P.a,P.a,[]),n.Rb(**********,M.b,M.b,[]),n.Rb(**********,o.Tb,o.Tb,[]),n.Rb(**********,d,d,[]),n.Rb(256,f.n,"XSRF-TOKEN",[]),n.Rb(256,f.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,R.m,void 0,[]),n.Rb(256,R.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,a.i,function(){return[[{path:"",component:c}]]},[])])}),G=n.Hb({encapsulation:2,styles:[],data:{}});function O(t){return n.dc(0,[n.Zb(*********,1,{_container:0}),n.Zb(*********,2,{okButton:0}),n.Zb(*********,3,{entityCombo:0}),n.Zb(*********,4,{ccyCombo:0}),n.Zb(*********,5,{accountCombo:0}),n.Zb(*********,6,{specificAccountCombo:0}),n.Zb(*********,7,{specificEntityCombo:0}),n.Zb(*********,8,{newInternalCrFormatTextField:0}),n.Zb(*********,9,{newInternalDrFormatTextField:0}),n.Zb(*********,10,{newExternalCrFormatTextField:0}),n.Zb(*********,11,{newExternalDrFormatTextField:0}),n.Zb(*********,12,{newExternalCrFormatIntTextField:0}),n.Zb(*********,13,{newExternalDrFormatIntTextField:0}),n.Zb(*********,14,{selectedEntity:0}),n.Zb(*********,15,{selectedAccount:0}),n.Zb(*********,16,{selectedCcy:0}),n.Zb(*********,17,{selectedSpecificAccount:0}),n.Zb(*********,18,{selectedSpecificEntity:0}),(t()(),n.Jb(18,0,null,null,145,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,l){var n=!0,i=t.component;"creationComplete"===e&&(n=!1!==i.onLoad()&&n);return n},m.ad,m.hb)),n.Ib(19,4440064,null,0,o.yb,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(20,0,null,0,143,"VBox",[["height","100%"],["id","vbParent"],["paddingBottom","18"],["paddingLeft","17"],["paddingRight","17"],["paddingTop","20"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(21,4440064,null,0,o.ec,[n.r,o.i,n.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),n.Jb(22,0,null,0,127,"SwtCanvas",[["height","260"],["id","attAccContainer"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(23,4440064,null,0,o.db,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(24,0,null,0,125,"VBox",[["height","100%"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(25,4440064,null,0,o.ec,[n.r,o.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(26,0,null,0,71,"Grid",[["id","grid1"],["marginTop","3"],["verticalGap","0"],["width","100%"]],null,null,null,m.Cc,m.H)),n.Ib(27,4440064,null,0,o.z,[n.r,o.i],{id:[0,"id"],verticalGap:[1,"verticalGap"],width:[2,"width"],marginTop:[3,"marginTop"]},null),(t()(),n.Jb(28,0,null,0,13,"GridRow",[["id","gridRow1"],["width","100%"]],null,null,null,m.Bc,m.J)),n.Ib(29,4440064,null,0,o.B,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),n.Jb(30,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(31,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(32,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.entity"]],null,null,null,m.Yc,m.fb)),n.Ib(33,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"]},null),(t()(),n.Jb(34,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),n.Ib(35,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(36,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"],["width","120"]],null,[[null,"change"],[null,"focusout"],[null,"close"],[null,"open"],["window","mousewheel"]],function(t,e,l){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,37).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==o.changeCombo(l)&&i);"focusout"===e&&(i=!1!==o.comboBoxfocusOutHandler(l)&&i);"close"===e&&(i=!1!==o.closedCombo(l)&&i);"open"===e&&(i=!1!==o.openedCombo(l)&&i);return i},m.Pc,m.W)),n.Ib(37,4440064,[[3,4],["entityCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",focusout_:"focusout",change_:"change"}),(t()(),n.Jb(38,0,null,0,3,"GridItem",[["width","35%"]],null,null,null,m.Ac,m.I)),n.Ib(39,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(40,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["styleName","labelLeft"],["text",""]],null,null,null,m.Yc,m.fb)),n.Ib(41,4440064,[[14,4],["selectedEntity",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],text:[2,"text"],fontWeight:[3,"fontWeight"]},null),(t()(),n.Jb(42,0,null,0,13,"GridRow",[["width","100%"]],null,null,null,m.Bc,m.J)),n.Ib(43,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(44,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(45,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(46,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.currency"]],null,null,null,m.Yc,m.fb)),n.Ib(47,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"]},null),(t()(),n.Jb(48,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),n.Ib(49,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(50,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","ccyCombo"],["width","120"]],null,[[null,"change"],[null,"close"],[null,"focusout"],[null,"open"],["window","mousewheel"]],function(t,e,l){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,51).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==o.changeCombo(l)&&i);"close"===e&&(i=!1!==o.closedCombo(l)&&i);"focusout"===e&&(i=!1!==o.comboBoxfocusOutHandler(l)&&i);"open"===e&&(i=!1!==o.openedCombo(l)&&i);return i},m.Pc,m.W)),n.Ib(51,4440064,[[4,4],["ccyCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",focusout_:"focusout",change_:"change"}),(t()(),n.Jb(52,0,null,0,3,"GridItem",[["width","35%"]],null,null,null,m.Ac,m.I)),n.Ib(53,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(54,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCcy"],["styleName","labelLeft"],["text",""]],null,null,null,m.Yc,m.fb)),n.Ib(55,4440064,[[16,4],["selectedCcy",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],text:[2,"text"],fontWeight:[3,"fontWeight"]},null),(t()(),n.Jb(56,0,null,0,13,"GridRow",[["width","100%"]],null,null,null,m.Bc,m.J)),n.Ib(57,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(58,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(59,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(60,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.accountId"],["width","165"]],null,null,null,m.Yc,m.fb)),n.Ib(61,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"],width:[2,"width"]},null),(t()(),n.Jb(62,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),n.Ib(63,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(64,0,null,0,1,"SwtComboBox",[["dataLabel","accounts"],["id","accountCombo"],["width","230"]],null,[[null,"change"],[null,"focusout"],[null,"close"],[null,"open"],["window","mousewheel"]],function(t,e,l){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,65).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==o.changeCombo(l)&&i);"focusout"===e&&(i=!1!==o.comboBoxfocusOutHandler(l)&&i);"close"===e&&(i=!1!==o.closedCombo(l)&&i);"open"===e&&(i=!1!==o.openedCombo(l)&&i);return i},m.Pc,m.W)),n.Ib(65,4440064,[[5,4],["accountCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",focusout_:"focusout",change_:"change"}),(t()(),n.Jb(66,0,null,0,3,"GridItem",[["width","35%"]],null,null,null,m.Ac,m.I)),n.Ib(67,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(68,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedAccount"],["styleName","labelLeft"],["text",""]],null,null,null,m.Yc,m.fb)),n.Ib(69,4440064,[[15,4],["selectedAccount",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],text:[2,"text"],fontWeight:[3,"fontWeight"]},null),(t()(),n.Jb(70,0,null,0,13,"GridRow",[["width","100%"]],null,null,null,m.Bc,m.J)),n.Ib(71,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(72,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(73,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(74,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.specEntityId"]],null,null,null,m.Yc,m.fb)),n.Ib(75,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"]},null),(t()(),n.Jb(76,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),n.Ib(77,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(78,0,null,0,1,"SwtComboBox",[["dataLabel","specEntities"],["id","specificEntityCombo"],["prompt","Entity ID"],["required","true"],["width","120"]],null,[[null,"change"],[null,"focusout"],[null,"close"],[null,"open"],["window","mousewheel"]],function(t,e,l){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,79).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==o.updateComboData(l)&&i);"focusout"===e&&(i=!1!==o.comboBoxfocusOutHandler(l)&&i);"close"===e&&(i=!1!==o.closedCombo(l)&&i);"open"===e&&(i=!1!==o.openedCombo(l)&&i);return i},m.Pc,m.W)),n.Ib(79,4440064,[[7,4],["specificEntityCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],prompt:[1,"prompt"],width:[2,"width"],id:[3,"id"],required:[4,"required"]},{open_:"open",close_:"close",focusout_:"focusout",change_:"change"}),(t()(),n.Jb(80,0,null,0,3,"GridItem",[["width","35%"]],null,null,null,m.Ac,m.I)),n.Ib(81,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(82,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedSpecificEntity"],["styleName","labelLeft"],["text",""]],null,null,null,m.Yc,m.fb)),n.Ib(83,4440064,[[18,4],["selectedSpecificEntity",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],text:[2,"text"],fontWeight:[3,"fontWeight"]},null),(t()(),n.Jb(84,0,null,0,13,"GridRow",[["width","100%"]],null,null,null,m.Bc,m.J)),n.Ib(85,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(86,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(87,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(88,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.specAccountId"]],null,null,null,m.Yc,m.fb)),n.Ib(89,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"]},null),(t()(),n.Jb(90,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,m.Ac,m.I)),n.Ib(91,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(92,0,null,0,1,"SwtComboBox",[["dataLabel","specAccounts"],["id","specificAccountCombo"],["prompt","Select Specific Account ID"],["required","true"],["width","230"]],null,[[null,"change"],[null,"focusout"],[null,"close"],[null,"open"],["window","mousewheel"]],function(t,e,l){var i=!0,o=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,93).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==o.changeCombo(l)&&i);"focusout"===e&&(i=!1!==o.comboBoxfocusOutHandler(l)&&i);"close"===e&&(i=!1!==o.closedCombo(l)&&i);"open"===e&&(i=!1!==o.openedCombo(l)&&i);return i},m.Pc,m.W)),n.Ib(93,4440064,[[6,4],["specificAccountCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],prompt:[1,"prompt"],width:[2,"width"],id:[3,"id"],required:[4,"required"]},{open_:"open",close_:"close",focusout_:"focusout",change_:"change"}),(t()(),n.Jb(94,0,null,0,3,"GridItem",[["width","35%"]],null,null,null,m.Ac,m.I)),n.Ib(95,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(96,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedSpecificAccount"],["styleName","labelLeft"],["text",""]],null,null,null,m.Yc,m.fb)),n.Ib(97,4440064,[[17,4],["selectedSpecificAccount",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],text:[2,"text"],fontWeight:[3,"fontWeight"]},null),(t()(),n.Jb(98,0,null,0,7,"HBox",[["height","20"]],null,null,null,m.Dc,m.K)),n.Ib(99,4440064,null,0,o.C,[n.r,o.i],{height:[0,"height"]},null),(t()(),n.Jb(100,0,null,0,1,"spacer",[["width","170"]],null,null,null,m.Kc,m.R)),n.Ib(101,4440064,null,0,o.Y,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(102,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.credit"],["width","160"]],null,null,null,m.Yc,m.fb)),n.Ib(103,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"],width:[2,"width"]},null),(t()(),n.Jb(104,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.debit"]],null,null,null,m.Yc,m.fb)),n.Ib(105,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"]},null),(t()(),n.Jb(106,0,null,0,43,"Grid",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,m.Cc,m.H)),n.Ib(107,4440064,null,0,o.z,[n.r,o.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(108,0,null,0,13,"GridRow",[["width","100%"]],null,null,null,m.Bc,m.J)),n.Ib(109,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(110,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(111,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(112,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.internal"],["width","165"]],null,null,null,m.Yc,m.fb)),n.Ib(113,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"],width:[2,"width"]},null),(t()(),n.Jb(114,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(115,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(116,0,null,0,1,"SwtComboBox",[["dataLabel","formats"],["id","newInternalCrFormatTextField"],["shiftUp","100"],["width","140"]],null,[["window","mousewheel"]],function(t,e,l){var i=!0;"window:mousewheel"===e&&(i=!1!==n.Tb(t,117).mouseWeelEventHandler(l.target)&&i);return i},m.Pc,m.W)),n.Ib(117,4440064,[[8,4],["newInternalCrFormatTextField",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],shiftUp:[3,"shiftUp"]},null),(t()(),n.Jb(118,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(119,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(120,0,null,0,1,"SwtComboBox",[["dataLabel","formats"],["id","newInternalDrFormatTextField"],["shiftUp","100"],["width","140"]],null,[["window","mousewheel"]],function(t,e,l){var i=!0;"window:mousewheel"===e&&(i=!1!==n.Tb(t,121).mouseWeelEventHandler(l.target)&&i);return i},m.Pc,m.W)),n.Ib(121,4440064,[[9,4],["newInternalDrFormatTextField",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],shiftUp:[3,"shiftUp"]},null),(t()(),n.Jb(122,0,null,0,13,"GridRow",[["width","100%"]],null,null,null,m.Bc,m.J)),n.Ib(123,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(124,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(125,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(126,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.external"],["width","165"]],null,null,null,m.Yc,m.fb)),n.Ib(127,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"],width:[2,"width"]},null),(t()(),n.Jb(128,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(129,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(130,0,null,0,1,"SwtComboBox",[["dataLabel","formats"],["id","newExternalCrFormatTextField"],["shiftUp","100"],["width","140"]],null,[["window","mousewheel"]],function(t,e,l){var i=!0;"window:mousewheel"===e&&(i=!1!==n.Tb(t,131).mouseWeelEventHandler(l.target)&&i);return i},m.Pc,m.W)),n.Ib(131,4440064,[[10,4],["newExternalCrFormatTextField",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],shiftUp:[3,"shiftUp"]},null),(t()(),n.Jb(132,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(133,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(134,0,null,0,1,"SwtComboBox",[["dataLabel","formats"],["id","newExternalDrFormatTextField"],["shiftUp","100"],["width","140"]],null,[["window","mousewheel"]],function(t,e,l){var i=!0;"window:mousewheel"===e&&(i=!1!==n.Tb(t,135).mouseWeelEventHandler(l.target)&&i);return i},m.Pc,m.W)),n.Ib(135,4440064,[[11,4],["newExternalDrFormatTextField",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],shiftUp:[3,"shiftUp"]},null),(t()(),n.Jb(136,0,null,0,13,"GridRow",[["width","100%"]],null,null,null,m.Bc,m.J)),n.Ib(137,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(138,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(139,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(140,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["textDictionaryId","label.accountspecificsweepformat.text.extViaIntermediary"],["width","165"]],null,null,null,m.Yc,m.fb)),n.Ib(141,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"],styleName:[1,"styleName"],width:[2,"width"]},null),(t()(),n.Jb(142,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,m.Ac,m.I)),n.Ib(143,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(144,0,null,0,1,"SwtComboBox",[["dataLabel","formats"],["id","newExternalCrFormatIntTextField"],["shiftUp","100"],["width","140"]],null,[["window","mousewheel"]],function(t,e,l){var i=!0;"window:mousewheel"===e&&(i=!1!==n.Tb(t,145).mouseWeelEventHandler(l.target)&&i);return i},m.Pc,m.W)),n.Ib(145,4440064,[[12,4],["newExternalCrFormatIntTextField",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],shiftUp:[3,"shiftUp"]},null),(t()(),n.Jb(146,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(147,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(148,0,null,0,1,"SwtComboBox",[["dataLabel","formats"],["id","newExternalDrFormatIntTextField"],["shiftUp","100"],["width","140"]],null,[["window","mousewheel"]],function(t,e,l){var i=!0;"window:mousewheel"===e&&(i=!1!==n.Tb(t,149).mouseWeelEventHandler(l.target)&&i);return i},m.Pc,m.W)),n.Ib(149,4440064,[[13,4],["newExternalDrFormatIntTextField",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],shiftUp:[3,"shiftUp"]},null),(t()(),n.Jb(150,0,null,0,13,"SwtCanvas",[["height","35"],["id","swtButtonBar"],["width","100%"],["y","475"]],null,null,null,m.Nc,m.U)),n.Ib(151,4440064,null,0,o.db,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(152,0,null,0,11,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(153,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(154,0,null,0,5,"HBox",[["height","100%"],["horizontalAlign","left"],["paddingLeft","2"],["width","90%"]],null,null,null,m.Dc,m.K)),n.Ib(155,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),n.Jb(156,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","okButton"],["label","Ok"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.saveHandler(l)&&n);return n},m.Mc,m.T)),n.Ib(157,4440064,[[2,4],[2,4],["okButton",4],["okButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],label:[1,"label"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(158,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"],["label","Cancel"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.closeHandler(l)&&n);return n},m.Mc,m.T)),n.Ib(159,4440064,[["cancelButton",4],["cancelButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],label:[1,"label"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(160,0,null,0,3,"HBox",[["height","100%"],["horizontalAlign","right"],["right","30"],["top","3"],["verticalAlign","middle"]],null,null,null,m.Dc,m.K)),n.Ib(161,4440064,null,0,o.C,[n.r,o.i],{right:[0,"right"],top:[1,"top"],horizontalAlign:[2,"horizontalAlign"],verticalAlign:[3,"verticalAlign"],height:[4,"height"]},null),(t()(),n.Jb(162,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","spread-profile"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.doHelp()&&n);return n},m.Wc,m.db)),n.Ib(163,4440064,null,0,o.rb,[n.r,o.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"})],function(t,e){t(e,19,0,"100%","100%");t(e,21,0,"vbParent","100%","100%","20","18","17","17");t(e,23,0,"attAccContainer","100%","260");t(e,25,0,"100%","100%");t(e,27,0,"grid1","0","100%","3");t(e,29,0,"gridRow1","100%");t(e,31,0,"25%");t(e,33,0,"label.accountspecificsweepformat.text.entity","labelBold");t(e,35,0,"40%");t(e,37,0,"entity","120","entityCombo");t(e,39,0,"35%");t(e,41,0,"selectedEntity","labelLeft","","normal");t(e,43,0,"100%");t(e,45,0,"25%");t(e,47,0,"label.accountspecificsweepformat.text.currency","labelBold");t(e,49,0,"40%");t(e,51,0,"currency","120","ccyCombo");t(e,53,0,"35%");t(e,55,0,"selectedCcy","labelLeft","","normal");t(e,57,0,"100%");t(e,59,0,"25%");t(e,61,0,"label.accountspecificsweepformat.text.accountId","labelBold","165");t(e,63,0,"40%");t(e,65,0,"accounts","230","accountCombo");t(e,67,0,"35%");t(e,69,0,"selectedAccount","labelLeft","","normal");t(e,71,0,"100%");t(e,73,0,"25%");t(e,75,0,"label.accountspecificsweepformat.text.specEntityId","labelBold");t(e,77,0,"40%");t(e,79,0,"specEntities","Entity ID","120","specificEntityCombo","true");t(e,81,0,"35%");t(e,83,0,"selectedSpecificEntity","labelLeft","","normal");t(e,85,0,"100%");t(e,87,0,"25%");t(e,89,0,"label.accountspecificsweepformat.text.specAccountId","labelBold");t(e,91,0,"40%");t(e,93,0,"specAccounts","Select Specific Account ID","230","specificAccountCombo","true");t(e,95,0,"35%");t(e,97,0,"selectedSpecificAccount","labelLeft","","normal");t(e,99,0,"20");t(e,101,0,"170");t(e,103,0,"label.accountspecificsweepformat.text.credit","labelBold","160");t(e,105,0,"label.accountspecificsweepformat.text.debit","labelBold");t(e,107,0,"0","100%","100%");t(e,109,0,"100%");t(e,111,0,"25%");t(e,113,0,"label.accountspecificsweepformat.text.internal","labelBold","165");t(e,115,0,"25%");t(e,117,0,"formats","140","newInternalCrFormatTextField","100");t(e,119,0,"50%");t(e,121,0,"formats","140","newInternalDrFormatTextField","100");t(e,123,0,"100%");t(e,125,0,"25%");t(e,127,0,"label.accountspecificsweepformat.text.external","labelBold","165");t(e,129,0,"25%");t(e,131,0,"formats","140","newExternalCrFormatTextField","100");t(e,133,0,"50%");t(e,135,0,"formats","140","newExternalDrFormatTextField","100");t(e,137,0,"100%");t(e,139,0,"25%");t(e,141,0,"label.accountspecificsweepformat.text.extViaIntermediary","labelBold","165");t(e,143,0,"25%");t(e,145,0,"formats","140","newExternalCrFormatIntTextField","100");t(e,147,0,"50%");t(e,149,0,"formats","140","newExternalDrFormatIntTextField","100");t(e,151,0,"swtButtonBar","100%","35");t(e,153,0,"100%");t(e,155,0,"left","90%","100%","2");t(e,157,0,"okButton","Ok","true");t(e,159,0,"cancelButton","Cancel","true");t(e,161,0,"30","3","right","middle","100%");t(e,163,0,"helpIcon","true",!0,"spread-profile")},null)}function k(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-accountspecificsweepformatadd",[],null,null,null,O,G)),n.Ib(1,4440064,null,0,c,[o.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var q=n.Fb("app-accountspecificsweepformatadd",c,k,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);