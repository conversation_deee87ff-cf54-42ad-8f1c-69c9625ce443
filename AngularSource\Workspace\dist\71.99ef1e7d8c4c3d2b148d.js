(window.webpackJsonp=window.webpackJsonp||[]).push([[71],{BcLV:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),o=i("mrSG"),a=i("447K"),l=i("ZYCi"),s=i("+k9y"),d=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.jsonReader=new a.L,n.actionMethod="",n.actionPath="",n.inputData=new a.G(n.commonService),n.sendData=new a.G(n.commonService),n.deleteData=new a.G(n.commonService),n.updateRefreshRate=new a.G(n.commonService),n.baseURL=a.Wb.getBaseURL(),n.requestParams=[],n.saveFlag=!0,n.deletestring="",n.listOfSumEntitiesFromDB=[],n.menuAccessIdParent=0,n.swtAlert=new a.bb(e),n}return o.d(e,t),e.prototype.ngOnInit=function(){this.btnOk.label=a.Wb.getPredictMessage("label.personalEntityList.button.ok",null),this.btnOk.toolTip=a.Wb.getPredictMessage("tooltip.personalEntityList.button.ok",null),this.addSumButton.label=a.Wb.getPredictMessage("label.personalEntityList.button.addsum",null),this.addSumButton.toolTip=a.Wb.getPredictMessage("tooltip.personalEntityList.button.addsum",null),this.changeButton.label=a.Wb.getPredictMessage("label.personalEntityList.button.modifysum",null),this.changeButton.toolTip=a.Wb.getPredictMessage("tooltip.personalEntityList.button.modifysum",null),this.deleteButton.label=a.Wb.getPredictMessage("label.personalEntityList.button.deletesum",null),this.deleteButton.toolTip=a.Wb.getPredictMessage("tooltip.personalEntityList.button.deletesum",null),this.btnCancel.label=a.Wb.getPredictMessage("label.personalEntityList.button.cancel",null),this.btnCancel.toolTip=a.Wb.getPredictMessage("tooltip.personalEntityList.button.cancel",null),this.lostConnectionText.text=a.Wb.getPredictMessage("screen.connectionError",null)},e.prototype.onLoad=function(){var t=this;this.configGrid=this.dataGridContainer.addChild(a.hb),this.configGrid.uniqueColumn="entityid",this.configGrid.editable=!0,this.initializeMenus(),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.sendData.cbStart=this.startOfComms.bind(this),this.sendData.cbStop=this.endOfComms.bind(this),this.sendData.cbResult=function(e){t.sendDataResult(e)},this.sendData.cbFault=this.sendDataFault.bind(this),this.sendData.encodeURL=!1,this.deleteData.cbStart=this.startOfComms.bind(this),this.deleteData.cbStop=this.endOfComms.bind(this),this.deleteData.cbResult=function(e){t.deleteDataResult(e)},this.deleteData.cbFault=this.sendDataFault.bind(this),this.deleteData.encodeURL=!1,this.configGrid.ITEM_CLICK.subscribe(function(e){t.cellLogic(e)}),this.menuAccessIdParent=a.x.call("eval","menuAccessIdParent"),this.actionPath="entityMonitor.do?",this.actionMethod="method=displayPersonalEntityList",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.sendData.url=this.baseURL+this.actionPath+"method=savePersonalEntityList",this.deleteData.url=this.baseURL+this.actionPath+"method=deletePersonalSumEntityList",this.inputData.send(this.requestParams)},e.prototype.cellLogic=function(t){-1!=this.configGrid.selectedIndex?("display"==t.target.field&&(this.btnCancel.enabled=!0),"Y"==this.configGrid.dataProvider[this.configGrid.selectedIndex].slickgrid_rowcontent.entityid.aggregate?(this.changeButton.enabled=!0,this.deleteButton.enabled=!0):(this.changeButton.enabled=!1,this.deleteButton.enabled=!1)):(this.changeButton.enabled=!1,this.deleteButton.enabled=!1)},e.prototype.initializeMenus=function(){new a.n("Show JSON").MenuItemSelect=this.showGridJSON.bind(this)},e.prototype.showGridJSON=function(t){this.showJSONPopup=a.Eb.createPopUp(this,a.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.height="400",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.display()},e.prototype.sendDataFault=function(t){this.lostConnectionText.visible=!0,this.swtAlert.error("Error")},e.prototype.inputDataResult=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else{if(this.btnCancel.enabled=!1,this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.lostConnectionText.visible=!1,this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON){var e={columns:this.jsonReader.getColumnData()};this.configGrid.CustomGrid(e),this.configGrid.gridData=this.jsonReader.getGridData(),this.configGrid.setRowSize=this.jsonReader.getRowSize(),this.prevRecievedJSON=this.lastRecievedJSON,this.listOfSumEntitiesFromDB=this.getListOfSumEntitiesFromDB()}this.saveFlag||a.x.call("closeWindow")}this.prevRecievedJSON=this.lastRecievedJSON}},e.prototype.updateData=function(){this.inputData.send(this.requestParams)},e.prototype.inputDataFault=function(t){this.lostConnectionText.visible=!0,this.swtAlert.error("Error")},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.saveHandle=function(){this.btnOk.enabled=!1,this.btnOk.buttonMode=!1;for(var t=[],e=[],i=[],n=this.configGrid.changes.getValues(),o=0;o<n.length;o++){var l=n[o].crud_operation.split(">");if("D"==l)i.entityId=n[o].crud_data.entityid;else{for(var s=0;s<l.length;s++){var d=l[s].substring(2,l[s].length-1);n[o].crud_data[d]&&(i[n[o].crud_data.entityid+"_"+d]=n[o].crud_data[d])}t.push(a.t.encode64(n[o].crud_data.entityid.toString())),n[o].crud_data.entityname.includes("Aggregation")?(i[n[o].crud_data.entityid+"_display"]=n[o].crud_data.slickgrid_rowcontent.display.content,i[n[o].crud_data.entityid+"_displaydays"]=n[o].crud_data.slickgrid_rowcontent.displaydays.content,i[n[o].crud_data.entityid+"_priority"]=n[o].crud_data.slickgrid_rowcontent.priority.content,e.push("Y")):e.push("N")}}t.length>0&&(i.update=t.toString(),i.aggregate=e.toString()),n.length>0?this.sendData.send(i):a.x.call("closeWindow")},e.prototype.sumWindowAdd=function(){this.win=a.Eb.createPopUp(this,s.a,{title:"Add Sum Entity",cameFrom:"Add"}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="500",this.win.height="435",this.win.showControls=!0,this.win.id="addWindow",this.win.onClose.subscribe(function(t){}),this.win.display()},e.prototype.sumWindowChange=function(){var t=null,e=null,i=null,n=null;-1!=this.configGrid.selectedIndex&&(t=this.configGrid.dataProvider[this.configGrid.selectedIndex].entityid,e=this.configGrid.dataProvider[this.configGrid.selectedIndex].entityname,i=this.configGrid.dataProvider[this.configGrid.selectedIndex].notSaved,n=this.configGrid.dataProvider[this.configGrid.selectedIndex].entitiesList),this.win=a.Eb.createPopUp(this,s.a,{title:"Change Sum Entity",entityId:t,entityName:e,entityNotSaved:i,entitiesList:n,cameFrom:"Change"}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="500",this.win.height="435",this.win.showControls=!0,this.win.id="addWindow",this.win.onClose.subscribe(function(t){}),this.win.display()},e.prototype.sumDelete=function(){var t=a.Wb.getPredictMessage("alert.personalEntityList.deletesum",null);this.swtAlert.question(t,"Confirm Deletion",a.c.YES|a.c.NO,null,this.confirmSumDelete.bind(this))},e.prototype.confirmSumDelete=function(t){if(t.detail==a.c.YES){var e=this.configGrid.dataProvider[this.configGrid.selectedIndex].entityid;this.requestParams=[],this.requestParams.entityId=e,this.deleteData.send(this.requestParams),this.configGrid.removeSelected(),this.configGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1}},e.prototype.entityExists=function(t){for(var e=0;e<this.configGrid.dataProvider.length;e++)if(this.configGrid.dataProvider[e].entityid==t)return!0;return!1},e.prototype.sendDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&(this.saveFlag=!1,this.updateData()))},e.prototype.deleteDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():this.jsonReader.getRequestReplyStatus()||this.swtAlert.error("Error occurred, Please contact your System Administrator: \n"+this.jsonReader.getRequestReplyMessage(),"Error")},e.prototype.cancelHandle=function(){a.x.call("close")},e.prototype.addChangeSumEntityToGrid=function(t,e,i){this.changeButton.enabled=!1,this.deleteButton.enabled=!1;this.jsonReader.getRowSize();for(var n=!1,o=0;o<this.configGrid.dataProvider.length;o++)this.configGrid.dataProvider[o].entityid==t&&(this.configGrid.dataProvider[o].entityname=e,this.configGrid.dataProvider[o].notSaved="true",this.configGrid.dataProvider[o].entitiesList=i,n=!0,this.configGrid.refresh());if(!n){var a={notSaved:{content:"true"},entitiesList:{content:String(i)},entityid:{content:String(t),aggregate:"Y"},entityname:{content:String(e)},display:{content:"N",selected:"false"},priority:{content:500},displaydays:{content:1}};this.configGrid.appendRow(a),this.configGrid.dataProvider[this.configGrid.dataProvider.length-1].slickgrid_rowcontent.entityid.aggregate="Y",this.configGrid.dataProvider[this.configGrid.dataProvider.length-1].slickgrid_rowcontent.display.selected="false"}this.changeButton.enabled=!0,this.deleteButton.enabled=!0},e.prototype.getListOfSumEntitiesFromDB=function(){for(var t=[],e=0;e<this.configGrid.dataProvider.length;e++)"Y"==this.configGrid.dataProvider[e].entityid.aggregate&&t.push(this.configGrid.dataProvider[e].entityid);return t},e.prototype.doHelp=function(){a.x.call("help")},e}(a.yb),r=[{path:"",component:d}],c=(l.l.forChild(r),function(){return function(){}}()),u=i("pMnS"),h=i("RChO"),b=i("t6HQ"),g=i("WFGK"),p=i("5FqG"),m=i("Ip0R"),f=i("gIcY"),R=i("t/Na"),y=i("sE5F"),w=i("OzfB"),v=i("T7CS"),S=i("S7LP"),C=i("6aHO"),D=i("WzUx"),k=i("A7o+"),P=i("zCE2"),G=i("Jg5P"),O=i("3R0m"),B=i("hhbb"),_=i("5rxC"),L=i("Fzqc"),I=i("21Lb"),E=i("hUWP"),J=i("3pJQ"),x=i("V9q+"),N=i("VDKW"),M=i("kXfT"),T=i("BGbe");i.d(e,"PersonalEntityListModuleNgFactory",function(){return F}),i.d(e,"RenderType_PersonalEntityList",function(){return A}),i.d(e,"View_PersonalEntityList_0",function(){return z}),i.d(e,"View_PersonalEntityList_Host_0",function(){return q}),i.d(e,"PersonalEntityListNgFactory",function(){return j});var F=n.Gb(c,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[u.a,h.a,b.a,g.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,j]],[3,n.n],n.J]),n.Rb(4608,m.m,m.l,[n.F,[2,m.u]]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.p,f.p,[]),n.Rb(4608,R.j,R.p,[m.c,n.O,R.n]),n.Rb(4608,R.q,R.q,[R.j,R.o]),n.Rb(5120,R.a,function(t){return[t,new a.tb]},[R.q]),n.Rb(4608,R.m,R.m,[]),n.Rb(6144,R.k,null,[R.m]),n.Rb(4608,R.i,R.i,[R.k]),n.Rb(6144,R.b,null,[R.i]),n.Rb(4608,R.f,R.l,[R.b,n.B]),n.Rb(4608,R.c,R.c,[R.f]),n.Rb(4608,y.c,y.c,[]),n.Rb(4608,y.g,y.b,[]),n.Rb(5120,y.i,y.j,[]),n.Rb(4608,y.h,y.h,[y.c,y.g,y.i]),n.Rb(4608,y.f,y.a,[]),n.Rb(5120,y.d,y.k,[y.h,y.f]),n.Rb(5120,n.b,function(t,e){return[w.j(t,e)]},[m.c,n.O]),n.Rb(4608,v.a,v.a,[]),n.Rb(4608,S.a,S.a,[]),n.Rb(4608,C.a,C.a,[n.n,n.L,n.B,S.a,n.g]),n.Rb(4608,D.c,D.c,[n.n,n.g,n.B]),n.Rb(4608,D.e,D.e,[D.c]),n.Rb(4608,k.l,k.l,[]),n.Rb(4608,k.h,k.g,[]),n.Rb(4608,k.c,k.f,[]),n.Rb(4608,k.j,k.d,[]),n.Rb(4608,k.b,k.a,[]),n.Rb(4608,k.k,k.k,[k.l,k.h,k.c,k.j,k.b,k.m,k.n]),n.Rb(4608,D.i,D.i,[[2,k.k]]),n.Rb(4608,D.r,D.r,[D.L,[2,k.k],D.i]),n.Rb(4608,D.t,D.t,[]),n.Rb(4608,D.w,D.w,[]),n.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),n.Rb(1073742336,m.b,m.b,[]),n.Rb(1073742336,f.n,f.n,[]),n.Rb(1073742336,f.l,f.l,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,O.a,O.a,[]),n.Rb(1073742336,k.i,k.i,[]),n.Rb(1073742336,D.b,D.b,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,R.d,R.d,[]),n.Rb(1073742336,y.e,y.e,[]),n.Rb(1073742336,B.b,B.b,[]),n.Rb(1073742336,_.b,_.b,[]),n.Rb(1073742336,w.c,w.c,[]),n.Rb(1073742336,L.a,L.a,[]),n.Rb(1073742336,I.d,I.d,[]),n.Rb(1073742336,E.c,E.c,[]),n.Rb(1073742336,J.a,J.a,[]),n.Rb(1073742336,x.a,x.a,[[2,w.g],n.O]),n.Rb(1073742336,N.b,N.b,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,T.b,T.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,c,c,[]),n.Rb(256,R.n,"XSRF-TOKEN",[]),n.Rb(256,R.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,k.m,void 0,[]),n.Rb(256,k.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,l.i,function(){return[[{path:"",component:d}]]},[])])}),W=[[""]],A=n.Hb({encapsulation:0,styles:W,data:{}});function z(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{dataGridContainer:0}),n.Zb(402653184,3,{cvSaveOrCancel:0}),n.Zb(402653184,4,{btnOk:0}),n.Zb(402653184,5,{addSumButton:0}),n.Zb(402653184,6,{changeButton:0}),n.Zb(402653184,7,{deleteButton:0}),n.Zb(402653184,8,{btnCancel:0}),n.Zb(402653184,9,{loadingImage:0}),n.Zb(402653184,10,{lostConnectionText:0}),(t()(),n.Jb(10,0,null,null,29,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,o=t.component;"creationComplete"===e&&(n=!1!==o.onLoad()&&n);return n},p.ad,p.hb)),n.Ib(11,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(12,0,null,0,27,"VBox",[["height","100%"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(13,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(14,0,null,0,1,"SwtCanvas",[["height","90%"],["id","dataGridContainer"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(15,4440064,[[2,4],["dataGridContainer",4]],0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(16,0,null,0,23,"SwtCanvas",[["height","7%"],["id","cvSaveOrCancel"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(17,4440064,[[3,4],["cvSaveOrCancel",4]],0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(18,0,null,0,21,"HBox",[["height","100%"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(19,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(20,0,null,0,11,"HBox",[["height","100%"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(21,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(22,0,null,0,1,"SwtButton",[["id","btnOk"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.saveHandle()&&n);return n},p.Mc,p.T)),n.Ib(23,4440064,[[4,4],["btnOk",4]],0,a.cb,[n.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(24,0,null,0,1,"SwtButton",[["id","addSumButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.sumWindowAdd()&&n);return n},p.Mc,p.T)),n.Ib(25,4440064,[[5,4],["addSumButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(26,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.sumWindowChange()&&n);return n},p.Mc,p.T)),n.Ib(27,4440064,[[6,4],["changeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(28,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.sumDelete()&&n);return n},p.Mc,p.T)),n.Ib(29,4440064,[[7,4],["deleteButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(30,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnCancel"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.cancelHandle()&&n);return n},p.Mc,p.T)),n.Ib(31,4440064,[[8,4],["btnCancel",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(32,0,null,0,7,"HBox",[["horizontalAlign","right"]],null,null,null,p.Dc,p.K)),n.Ib(33,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"]},null),(t()(),n.Jb(34,0,null,0,1,"SwtLabel",[["styleName","red"],["visible","false"]],null,null,null,p.Yc,p.fb)),n.Ib(35,4440064,[[10,4],["lostConnectionText",4]],0,a.vb,[n.r,a.i],{styleName:[0,"styleName"],visible:[1,"visible"]},null),(t()(),n.Jb(36,0,null,0,1,"SwtHelpButton",[],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.doHelp()&&n);return n},p.Wc,p.db)),n.Ib(37,4440064,null,0,a.rb,[n.r,a.i],null,{onClick_:"click"}),(t()(),n.Jb(38,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),n.Ib(39,114688,[[9,4],["loadingImage",4]],0,a.xb,[n.r],null,null)],function(t,e){t(e,11,0,"100%","100%");t(e,13,0,"100%","100%");t(e,15,0,"dataGridContainer","100%","90%");t(e,17,0,"cvSaveOrCancel","100%","7%");t(e,19,0,"100%","100%");t(e,21,0,"100%","100%");t(e,23,0,"btnOk");t(e,25,0,"addSumButton");t(e,27,0,"changeButton","false");t(e,29,0,"deleteButton","false");t(e,31,0,"btnCancel","true");t(e,33,0,"right");t(e,35,0,"red","false"),t(e,37,0),t(e,39,0)},null)}function q(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-personalentitylist",[],null,null,null,z,A)),n.Ib(1,4440064,null,0,d,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var j=n.Fb("app-personalentitylist",d,q,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);