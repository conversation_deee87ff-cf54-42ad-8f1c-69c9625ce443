(window.webpackJsonp=window.webpackJsonp||[]).push([[69],{ItJ6:function(t,l,e){"use strict";e.r(l);var n=e("CcnG"),i=e("mrSG"),o=e("447K"),u=e("ZYCi"),r=function(t){function l(l,e){var n=t.call(this,e,l)||this;return n.commonService=l,n.element=e,n.baseURL=o.Wb.getBaseURL(),n.actionPath="",n.actionMethod="",n.inputData=new o.G(n.commonService),n.saveData=new o.G(n.commonService),n.jsonReader=new o.L,n.comboOpen=!1,n.comboChange=!1,n.requestParams=[],n.menuAccessIdParent=0,n.fontValue="",n.fontLabel="",n.fontRequest="",n.tempFontSize="",n.refreshRate=10,n.itemId="",n.updateFontSize=new o.G(n.commonService),n.swtAlert=new o.bb(l),n}return i.d(l,t),l.prototype.ngOnInit=function(){this.cbUsePeList.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.usePersonalEntityList",null),this.btnEntity.label=o.Wb.getPredictMessage("label.entityMonitorOptions.entity",null),this.btnEntity.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.entity",null),this.cbHideWeekends.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.hideWeekends",null),this.reportingCcy.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.reportingCcy",null),this.ccyMultiplier.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.useCurrencyMultiplier",null),this.cbPersonalCcyList.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.usePersonalCcyList",null),this.textRefreshRate.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.rate",null),this.fontNormal.label=o.Wb.getPredictMessage("label.entityMonitorOptions.fontnormal",null),this.fontNormal.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.fontnormal",null),this.fontSmall.label=o.Wb.getPredictMessage("label.entityMonitorOptions.fontsmall",null),this.fontSmall.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.fontsmall",null),this.btnSave.label=o.Wb.getPredictMessage("label.entityMonitorOptions.save",null),this.btnSave.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.save",null),this.btnCancel.label=o.Wb.getPredictMessage("label.entityMonitorOptions.cancel",null),this.btnCancel.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.cancel",null),this.btnCurrency.label=o.Wb.getPredictMessage("label.entityMonitorOptions.currency",null),this.btnCurrency.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitorOptions.currency",null)},l.prototype.onLoad=function(){var t=this;this.requestParams=[],this.menuAccessIdParent=o.x.call("eval","menuAccessIdParent"),this.initializeMenus(),this.inputData.cbResult=this.inputDataResult.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="entityMonitor.do?",this.actionMethod="method=displayEntityMonitorOptions",this.actionMethod=this.actionMethod+"&selectedEntityId="+o.x.call("eval","selectedEntityId"),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveData.cbResult=function(l){t.saveDataResult(l)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1,this.inputData.send(this.requestParams)},l.prototype.saveDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&(o.x.call("unloadCloseWindow"),o.x.call("closeWindow")))},l.prototype.initializeMenus=function(){new o.n("Show JSON").MenuItemSelect=this.showGridJSON.bind(this)},l.prototype.showGridJSON=function(t){this.showJSONPopup=o.Eb.createPopUp(this,o.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.height="400",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.display()},l.prototype.inputDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastRecievedJSON!=this.prevRecievedJSON&&("N"==this.lastRecievedJSON.monitoroptions.options.usepersonalentitylist?this.cbUsePeList.selected=!1:this.cbUsePeList.selected=!0,"N"==this.lastRecievedJSON.monitoroptions.options.hideweekend?this.cbHideWeekends.selected=!1:this.cbHideWeekends.selected=!0,"N"==this.lastRecievedJSON.monitoroptions.options.usepersonalcurrency?this.cbPersonalCcyList.selected=!1:this.cbPersonalCcyList.selected=!0,this.reportingCcy.setComboData(this.jsonReader.getSelects()),this.ccyMultiplier.setComboData(this.jsonReader.getSelects()),""!=o.Z.trim(this.reportingCcy.text)&&(this.lblcurrencyname.text=this.reportingCcy.selectedValue),""!=o.Z.trim(this.ccyMultiplier.text)&&(this.lblccymultiplier.text=this.ccyMultiplier.selectedValue),this.refreshRate=parseInt(this.lastRecievedJSON.monitoroptions.refresh),this.textRefreshRate.text=this.refreshRate.toString(),"Normal"==this.jsonReader.getScreenAttributes().currfontsize?(this.selectedFont=0,this.fontNormal.selected=!0):"Small"==this.jsonReader.getScreenAttributes().currfontsize&&(this.selectedFont=1,this.fontSmall.selected=!0)))},l.prototype.inputDataFault=function(t){this.swtAlert.error("")},l.prototype.changeCombo=function(t){this.lblcurrencyname.text=this.reportingCcy.selectedValue?this.reportingCcy.selectedValue:"",this.lblccymultiplier.text=this.ccyMultiplier.selectedValue?this.ccyMultiplier.selectedValue:"",this.comboChange=!0},l.prototype.updateData=function(){var t=this;this.btnSave.enabled=!1,this.requestParams=[],this.actionMethod="method=saveEntityMonitorOptions";var l="",e="",n="",i="",u="";if(l=this.cbUsePeList.selected?"Y":"N",e=this.cbHideWeekends.selected?"Y":"N",n=this.cbPersonalCcyList.selected?"Y":"N",this.reportingCcy.selectedValue&&(i=this.reportingCcy.selectedItem.content),this.ccyMultiplier.selectedValue&&(u=this.ccyMultiplier.selectedItem.content),isNaN(parseInt(this.textRefreshRate.text)))this.swtAlert.error(o.Wb.getPredictMessage("alert.entityMonitorOptions.notANumber",null),"Error",o.c.OK,null,function(l){t.textRefreshRate.setFocus(),t.btnSave.enabled=!0});else{var r=!1;if(this.refreshRate=parseInt(this.textRefreshRate.text),this.refreshRate<5&&(this.refreshRate=5,r=!0),this.itemId=o.x.call("eval","itemId"),this.textRefreshRate.text=this.refreshRate.toString(),this.fontValue=this.fontSize.selectedValue.toString(),"N"==this.fontValue?(this.selectedFont=0,this.fontLabel=this.fontNormal.label,this.fontRequest=o.x.call("getUpdateFontSize",this.fontLabel,this.itemId)):"S"==this.fontValue&&(this.selectedFont=1,this.fontLabel=this.fontSmall.label,this.fontRequest=o.x.call("getUpdateFontSize",this.fontLabel,this.itemId)),null!=this.fontRequest&&""!=this.fontRequest){var a;a=this.updateFontSize.url,this.updateFontSize.url=this.baseURL+this.fontRequest,this.updateFontSize.send(),this.updateFontSize.url=a}this.requestParams["entityMonitorOptions.personalEntityList"]=l,this.requestParams["entityMonitorOptions.hideWeekends"]=e,this.requestParams["entityMonitorOptions.personalCurrencyList"]=n,this.requestParams["entityMonitorOptions.reportCurrency"]=i,this.requestParams["entityMonitorOptions.useCurrencyMultiplier"]=u,this.requestParams["entityMonitorOptions.propertyValue"]=this.refreshRate.toString(),this.saveData.url=this.baseURL+this.actionPath+this.actionMethod,r?this.swtAlert.warning("Refresh rate selected was below minimum.\nSet to 5 seconds.","Warning",o.c.OK,null,function(l){t.saveData.send(t.requestParams)}):this.saveData.send(this.requestParams)}},l.prototype.send=function(t){this.saveData.send(this.requestParams)},l.prototype.closeHandler=function(){o.x.call("close")},l.prototype.openEntity=function(){o.x.call("openPersonalEntityWindow")},l.prototype.openCurrency=function(){o.x.call("openPersonalCurrencyWindow")},l.prototype.doHelp=function(){o.x.call("help")},l}(o.yb),a=[{path:"",component:r}],s=(u.l.forChild(a),function(){return function(){}}()),c=e("pMnS"),b=e("RChO"),d=e("t6HQ"),h=e("WFGK"),p=e("5FqG"),m=e("Ip0R"),y=e("gIcY"),g=e("t/Na"),R=e("sE5F"),f=e("OzfB"),w=e("T7CS"),I=e("S7LP"),S=e("6aHO"),C=e("WzUx"),M=e("A7o+"),v=e("zCE2"),O=e("Jg5P"),J=e("3R0m"),x=e("hhbb"),L=e("5rxC"),P=e("Fzqc"),k=e("21Lb"),D=e("hUWP"),N=e("3pJQ"),W=e("V9q+"),A=e("VDKW"),B=e("kXfT"),T=e("BGbe");e.d(l,"EntityMonitorOptionsModuleNgFactory",function(){return G}),e.d(l,"RenderType_EntityMonitorOptions",function(){return E}),e.d(l,"View_EntityMonitorOptions_0",function(){return _}),e.d(l,"View_EntityMonitorOptions_Host_0",function(){return F}),e.d(l,"EntityMonitorOptionsNgFactory",function(){return q});var G=n.Gb(s,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[c.a,b.a,d.a,h.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,q]],[3,n.n],n.J]),n.Rb(4608,m.m,m.l,[n.F,[2,m.u]]),n.Rb(4608,y.c,y.c,[]),n.Rb(4608,y.p,y.p,[]),n.Rb(4608,g.j,g.p,[m.c,n.O,g.n]),n.Rb(4608,g.q,g.q,[g.j,g.o]),n.Rb(5120,g.a,function(t){return[t,new o.tb]},[g.q]),n.Rb(4608,g.m,g.m,[]),n.Rb(6144,g.k,null,[g.m]),n.Rb(4608,g.i,g.i,[g.k]),n.Rb(6144,g.b,null,[g.i]),n.Rb(4608,g.f,g.l,[g.b,n.B]),n.Rb(4608,g.c,g.c,[g.f]),n.Rb(4608,R.c,R.c,[]),n.Rb(4608,R.g,R.b,[]),n.Rb(5120,R.i,R.j,[]),n.Rb(4608,R.h,R.h,[R.c,R.g,R.i]),n.Rb(4608,R.f,R.a,[]),n.Rb(5120,R.d,R.k,[R.h,R.f]),n.Rb(5120,n.b,function(t,l){return[f.j(t,l)]},[m.c,n.O]),n.Rb(4608,w.a,w.a,[]),n.Rb(4608,I.a,I.a,[]),n.Rb(4608,S.a,S.a,[n.n,n.L,n.B,I.a,n.g]),n.Rb(4608,C.c,C.c,[n.n,n.g,n.B]),n.Rb(4608,C.e,C.e,[C.c]),n.Rb(4608,M.l,M.l,[]),n.Rb(4608,M.h,M.g,[]),n.Rb(4608,M.c,M.f,[]),n.Rb(4608,M.j,M.d,[]),n.Rb(4608,M.b,M.a,[]),n.Rb(4608,M.k,M.k,[M.l,M.h,M.c,M.j,M.b,M.m,M.n]),n.Rb(4608,C.i,C.i,[[2,M.k]]),n.Rb(4608,C.r,C.r,[C.L,[2,M.k],C.i]),n.Rb(4608,C.t,C.t,[]),n.Rb(4608,C.w,C.w,[]),n.Rb(1073742336,u.l,u.l,[[2,u.r],[2,u.k]]),n.Rb(1073742336,m.b,m.b,[]),n.Rb(1073742336,y.n,y.n,[]),n.Rb(1073742336,y.l,y.l,[]),n.Rb(1073742336,v.a,v.a,[]),n.Rb(1073742336,O.a,O.a,[]),n.Rb(1073742336,y.e,y.e,[]),n.Rb(1073742336,J.a,J.a,[]),n.Rb(1073742336,M.i,M.i,[]),n.Rb(1073742336,C.b,C.b,[]),n.Rb(1073742336,g.e,g.e,[]),n.Rb(1073742336,g.d,g.d,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,x.b,x.b,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,f.c,f.c,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,k.d,k.d,[]),n.Rb(1073742336,D.c,D.c,[]),n.Rb(1073742336,N.a,N.a,[]),n.Rb(1073742336,W.a,W.a,[[2,f.g],n.O]),n.Rb(1073742336,A.b,A.b,[]),n.Rb(1073742336,B.a,B.a,[]),n.Rb(1073742336,T.b,T.b,[]),n.Rb(1073742336,o.Tb,o.Tb,[]),n.Rb(1073742336,s,s,[]),n.Rb(256,g.n,"XSRF-TOKEN",[]),n.Rb(256,g.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,M.m,void 0,[]),n.Rb(256,M.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,u.i,function(){return[[{path:"",component:r}]]},[])])}),z=[[""]],E=n.Hb({encapsulation:0,styles:z,data:{}});function _(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{cbUsePeList:0}),n.Zb(402653184,3,{btnEntity:0}),n.Zb(402653184,4,{cbHideWeekends:0}),n.Zb(402653184,5,{reportingCcy:0}),n.Zb(402653184,6,{lblcurrencyname:0}),n.Zb(402653184,7,{lblccymultiplier:0}),n.Zb(402653184,8,{ccyMultiplier:0}),n.Zb(402653184,9,{cbPersonalCcyList:0}),n.Zb(402653184,10,{btnCurrency:0}),n.Zb(402653184,11,{textRefreshRate:0}),n.Zb(402653184,12,{fontSize:0}),n.Zb(402653184,13,{fontNormal:0}),n.Zb(402653184,14,{fontSmall:0}),n.Zb(402653184,15,{btnSave:0}),n.Zb(402653184,16,{btnCancel:0}),(t()(),n.Jb(16,0,null,null,105,"SwtModule",[["height","265"],["width","100%"]],null,[[null,"creationComplete"]],function(t,l,e){var n=!0,i=t.component;"creationComplete"===l&&(n=!1!==i.onLoad()&&n);return n},p.ad,p.hb)),n.Ib(17,4440064,null,0,o.yb,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(18,0,null,0,103,"VBox",[["height","100%"],["paddingBottom","10"],["paddingLeft","10"],["paddingRight","10"],["paddingTop","10"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(19,4440064,null,0,o.ec,[n.r,o.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),n.Jb(20,0,null,0,87,"SwtCanvas",[["height","200"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(21,4440064,null,0,o.db,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(22,0,null,0,85,"Grid",[["height","100%"],["paddingLeft","5"],["width","100%"]],null,null,null,p.Cc,p.H)),n.Ib(23,4440064,null,0,o.z,[n.r,o.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),n.Jb(24,0,null,0,11,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(25,4440064,null,0,o.B,[n.r,o.i],null,null),(t()(),n.Jb(26,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,p.Ac,p.I)),n.Ib(27,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(28,0,null,0,1,"SwtLabel",[["textDictionaryId","label.entityMonitorOptions.usePersonalEntityList"]],null,null,null,p.Yc,p.fb)),n.Ib(29,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),n.Jb(30,0,null,0,5,"GridItem",[],null,null,null,p.Ac,p.I)),n.Ib(31,4440064,null,0,o.A,[n.r,o.i],null,null),(t()(),n.Jb(32,0,null,0,1,"SwtCheckBox",[["id","cbUsePeList"]],null,null,null,p.Oc,p.V)),n.Ib(33,4440064,[[2,4],["cbUsePeList",4]],0,o.eb,[n.r,o.i],{id:[0,"id"]},null),(t()(),n.Jb(34,0,null,0,1,"SwtButton",[["id","btnEntity"],["marginTop","0"],["width","70"]],null,[[null,"click"]],function(t,l,e){var n=!0,i=t.component;"click"===l&&(n=!1!==i.openEntity()&&n);return n},p.Mc,p.T)),n.Ib(35,4440064,[[3,4],["btnEntity",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],marginTop:[2,"marginTop"]},{onClick_:"click"}),(t()(),n.Jb(36,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(37,4440064,null,0,o.B,[n.r,o.i],null,null),(t()(),n.Jb(38,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,p.Ac,p.I)),n.Ib(39,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(40,0,null,0,1,"SwtLabel",[["textDictionaryId","label.entityMonitorOptions.hideWeekends"]],null,null,null,p.Yc,p.fb)),n.Ib(41,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),n.Jb(42,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),n.Ib(43,4440064,null,0,o.A,[n.r,o.i],null,null),(t()(),n.Jb(44,0,null,0,1,"SwtCheckBox",[["id","cbHideWeekends"]],null,null,null,p.Oc,p.V)),n.Ib(45,4440064,[[4,4],["cbHideWeekends",4]],0,o.eb,[n.r,o.i],{id:[0,"id"]},null),(t()(),n.Jb(46,0,null,0,11,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(47,4440064,null,0,o.B,[n.r,o.i],null,null),(t()(),n.Jb(48,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,p.Ac,p.I)),n.Ib(49,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(50,0,null,0,1,"SwtLabel",[["textDictionaryId","label.entityMonitorOptions.reportingCcy"]],null,null,null,p.Yc,p.fb)),n.Ib(51,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),n.Jb(52,0,null,0,5,"GridItem",[],null,null,null,p.Ac,p.I)),n.Ib(53,4440064,null,0,o.A,[n.r,o.i],null,null),(t()(),n.Jb(54,0,null,0,1,"SwtComboBox",[["dataLabel","ccylist"],["id","reportingCcy"],["width","130"]],null,[[null,"change"],["window","mousewheel"]],function(t,l,e){var i=!0,o=t.component;"window:mousewheel"===l&&(i=!1!==n.Tb(t,55).mouseWeelEventHandler(e.target)&&i);"change"===l&&(i=!1!==o.changeCombo(e)&&i);return i},p.Pc,p.W)),n.Ib(55,4440064,[[5,4],["reportingCcy",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(56,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","lblcurrencyname"],["paddingLeft","10"]],null,null,null,p.Yc,p.fb)),n.Ib(57,4440064,[[6,4],["lblcurrencyname",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(58,0,null,0,11,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(59,4440064,null,0,o.B,[n.r,o.i],null,null),(t()(),n.Jb(60,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,p.Ac,p.I)),n.Ib(61,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(62,0,null,0,1,"SwtLabel",[["textDictionaryId","label.entityMonitorOptions.useCurrencyMultiplier"]],null,null,null,p.Yc,p.fb)),n.Ib(63,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),n.Jb(64,0,null,0,5,"GridItem",[],null,null,null,p.Ac,p.I)),n.Ib(65,4440064,null,0,o.A,[n.r,o.i],null,null),(t()(),n.Jb(66,0,null,0,1,"SwtComboBox",[["dataLabel","ccymultiplier"],["id","ccyMultiplier"],["width","130"]],null,[[null,"change"],["window","mousewheel"]],function(t,l,e){var i=!0,o=t.component;"window:mousewheel"===l&&(i=!1!==n.Tb(t,67).mouseWeelEventHandler(e.target)&&i);"change"===l&&(i=!1!==o.changeCombo(e)&&i);return i},p.Pc,p.W)),n.Ib(67,4440064,[[8,4],["ccyMultiplier",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(68,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["paddingLeft","10"]],null,null,null,p.Yc,p.fb)),n.Ib(69,4440064,[[7,4],["lblccymultiplier",4]],0,o.vb,[n.r,o.i],{paddingLeft:[0,"paddingLeft"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(70,0,null,0,11,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(71,4440064,null,0,o.B,[n.r,o.i],null,null),(t()(),n.Jb(72,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,p.Ac,p.I)),n.Ib(73,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(74,0,null,0,1,"SwtLabel",[["textDictionaryId","label.entityMonitorOptions.usePersonalCcyList"]],null,null,null,p.Yc,p.fb)),n.Ib(75,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),n.Jb(76,0,null,0,5,"GridItem",[],null,null,null,p.Ac,p.I)),n.Ib(77,4440064,null,0,o.A,[n.r,o.i],null,null),(t()(),n.Jb(78,0,null,0,1,"SwtCheckBox",[["id","cbPersonalCcyList"]],null,null,null,p.Oc,p.V)),n.Ib(79,4440064,[[9,4],["cbPersonalCcyList",4]],0,o.eb,[n.r,o.i],{id:[0,"id"]},null),(t()(),n.Jb(80,0,null,0,1,"SwtButton",[["id","btnCurrency"],["marginTop","0"],["width","70"]],null,[[null,"click"]],function(t,l,e){var n=!0,i=t.component;"click"===l&&(n=!1!==i.openCurrency()&&n);return n},p.Mc,p.T)),n.Ib(81,4440064,[[10,4],["btnCurrency",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],marginTop:[2,"marginTop"]},{onClick_:"click"}),(t()(),n.Jb(82,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(83,4440064,null,0,o.B,[n.r,o.i],null,null),(t()(),n.Jb(84,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,p.Ac,p.I)),n.Ib(85,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(86,0,null,0,1,"SwtLabel",[["textDictionaryId","label.entityMonitorOptions.rate"]],null,null,null,p.Yc,p.fb)),n.Ib(87,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),n.Jb(88,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),n.Ib(89,4440064,null,0,o.A,[n.r,o.i],null,null),(t()(),n.Jb(90,0,null,0,1,"SwtNumericInput",[["id","textRefreshRate"],["maxChars","3"],["width","45"]],null,null,null,p.cd,p.jb)),n.Ib(91,4440064,[[11,4],["textRefreshRate",4]],0,o.Ab,[n.r,o.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(t()(),n.Jb(92,0,null,0,15,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(93,4440064,null,0,o.B,[n.r,o.i],null,null),(t()(),n.Jb(94,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,p.Ac,p.I)),n.Ib(95,4440064,null,0,o.A,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(96,0,null,0,1,"SwtLabel",[["textDictionaryId","label.entityMonitorOptions.font"]],null,null,null,p.Yc,p.fb)),n.Ib(97,4440064,null,0,o.vb,[n.r,o.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),n.Jb(98,0,null,0,9,"GridItem",[],null,null,null,p.Ac,p.I)),n.Ib(99,4440064,null,0,o.A,[n.r,o.i],null,null),(t()(),n.Jb(100,0,null,0,7,"SwtRadioButtonGroup",[["align","horizontal"],["id","fontSize"]],null,null,null,p.ed,p.lb)),n.Ib(101,4440064,[[12,4],["fontSize",4]],1,o.Hb,[g.c,n.r,o.i],{id:[0,"id"],align:[1,"align"]},null),n.Zb(603979776,17,{radioItems:1}),(t()(),n.Jb(103,0,null,0,1,"SwtRadioItem",[["groupName","fontSize"],["id","fontNormal"],["selected","true"],["value","N"]],null,null,null,p.fd,p.mb)),n.Ib(104,4440064,[[17,4],[13,4],["fontNormal",4]],0,o.Ib,[n.r,o.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"],selected:[3,"selected"]},null),(t()(),n.Jb(105,0,null,0,2,"SwtRadioItem",[["groupName","fontSize"],["id","fontSmall"],["value","S"]],null,null,null,p.fd,p.mb)),n.Ib(106,4440064,[[17,4],[14,4],["fontSmall",4]],0,o.Ib,[n.r,o.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),n.bc(-1,null,[" >"])),(t()(),n.Jb(108,0,null,0,13,"SwtCanvas",[["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(109,4440064,null,0,o.db,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(110,0,null,0,11,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(111,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(112,0,null,0,5,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(113,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(114,0,null,0,1,"SwtButton",[["id","btnSave"],["width","70"]],null,[[null,"click"]],function(t,l,e){var n=!0,i=t.component;"click"===l&&(n=!1!==i.updateData()&&n);return n},p.Mc,p.T)),n.Ib(115,4440064,[[15,4],["btnSave",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),n.Jb(116,0,null,0,1,"SwtButton",[["id","btnCancel"],["width","70"]],null,[[null,"click"]],function(t,l,e){var n=!0,i=t.component;"click"===l&&(n=!1!==i.closeHandler()&&n);return n},p.Mc,p.T)),n.Ib(117,4440064,[[16,4],["btnCancel",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),n.Jb(118,0,null,0,3,"HBox",[["horizontalAlign","right"]],null,null,null,p.Dc,p.K)),n.Ib(119,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"]},null),(t()(),n.Jb(120,0,null,0,1,"SwtHelpButton",[],null,[[null,"click"]],function(t,l,e){var n=!0,i=t.component;"click"===l&&(n=!1!==i.doHelp()&&n);return n},p.Wc,p.db)),n.Ib(121,4440064,null,0,o.rb,[n.r,o.i],null,{onClick_:"click"})],function(t,l){t(l,17,0,"100%","265");t(l,19,0,"0","100%","100%","10","10","10","10");t(l,21,0,"100%","200");t(l,23,0,"100%","100%","5"),t(l,25,0);t(l,27,0,"40%");t(l,29,0,"label.entityMonitorOptions.usePersonalEntityList"),t(l,31,0);t(l,33,0,"cbUsePeList");t(l,35,0,"btnEntity","70","0"),t(l,37,0);t(l,39,0,"40%");t(l,41,0,"label.entityMonitorOptions.hideWeekends"),t(l,43,0);t(l,45,0,"cbHideWeekends"),t(l,47,0);t(l,49,0,"40%");t(l,51,0,"label.entityMonitorOptions.reportingCcy"),t(l,53,0);t(l,55,0,"ccylist","130","reportingCcy");t(l,57,0,"lblcurrencyname","10","normal"),t(l,59,0);t(l,61,0,"40%");t(l,63,0,"label.entityMonitorOptions.useCurrencyMultiplier"),t(l,65,0);t(l,67,0,"ccymultiplier","130","ccyMultiplier");t(l,69,0,"10","normal"),t(l,71,0);t(l,73,0,"40%");t(l,75,0,"label.entityMonitorOptions.usePersonalCcyList"),t(l,77,0);t(l,79,0,"cbPersonalCcyList");t(l,81,0,"btnCurrency","70","0"),t(l,83,0);t(l,85,0,"40%");t(l,87,0,"label.entityMonitorOptions.rate"),t(l,89,0);t(l,91,0,"3","textRefreshRate","45"),t(l,93,0);t(l,95,0,"40%");t(l,97,0,"label.entityMonitorOptions.font"),t(l,99,0);t(l,101,0,"fontSize","horizontal");t(l,104,0,"fontNormal","fontSize","N","true");t(l,106,0,"fontSmall","fontSize","S");t(l,109,0,"100%");t(l,111,0,"100%");t(l,113,0,"100%");t(l,115,0,"btnSave","70");t(l,117,0,"btnCancel","70");t(l,119,0,"right"),t(l,121,0)},null)}function F(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-entitymonitoroptions",[],null,null,null,_,E)),n.Ib(1,4440064,null,0,r,[o.i,n.r],null,null)],function(t,l){t(l,1,0)},null)}var q=n.Fb("app-entitymonitoroptions",r,F,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);