{"_from": "slickgrid@^2.4.32", "_id": "slickgrid@2.4.45", "_inBundle": false, "_integrity": "sha512-WvygGTaLU9LnMWZSxqW1agwq8IcDGeCZ289vP1E07eh3ffyLm5TGQsYHXvTwWCHtriZBw+L9RFK/h7TsIMcoLQ==", "_location": "/angular-slickgrid/slickgrid", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "slickgrid@^2.4.32", "name": "slickgrid", "escapedName": "slickgrid", "rawSpec": "^2.4.32", "saveSpec": null, "fetchSpec": "^2.4.32"}, "_requiredBy": ["/angular-slickgrid"], "_resolved": "https://registry.npmjs.org/slickgrid/-/slickgrid-2.4.45.tgz", "_shasum": "98012820ad2c782b3e51b66d5b793c0ad710e87c", "_spec": "slickgrid@^2.4.32", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\angular-slickgrid", "author": {"name": "<PERSON>", "email": "micha<PERSON>.<EMAIL>"}, "bugs": {"url": "https://github.com/6pac/SlickGrid/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"jquery": ">=1.8.0", "jquery-ui": ">=1.8.0"}, "deprecated": false, "description": "A lightning fast JavaScript grid/spreadsheet", "devDependencies": {"cypress": "^8.5.0", "eslint": "^7.32.0", "http-server": "^13.0.2"}, "directories": {"example": "examples", "test": "tests"}, "homepage": "https://github.com/6pac/SlickGrid#readme", "keywords": ["slickgrid", "grid"], "license": "MIT", "main": "slick.core.js", "name": "slickgrid", "repository": {"type": "git", "url": "git+https://github.com/6pac/SlickGrid.git"}, "scripts": {"cypress": "npx cypress open", "cypress:ci": "npx cypress run --reporter xunit --reporter-options output=testresult.xml", "serve": "http-server ./ -p 8080 -a localhost -s"}, "version": "2.4.45"}