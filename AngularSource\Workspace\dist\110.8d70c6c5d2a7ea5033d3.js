(window.webpackJsonp=window.webpackJsonp||[]).push([[110],{wEAA:function(t,e,l){"use strict";l.r(e);var i=l("CcnG"),n=l("mrSG"),a=l("JU2O"),s=l("447K"),o=l("ZYCi"),u=l("J8bQ"),r=l("pAEI"),h=l("jvDV"),d=l("EVdn"),c=(l("elGS"),l("R1Kr"),function(t){function e(e,l){var i=t.call(this,l,e)||this;return i.commonService=e,i.element=l,i.jsonReader=new s.L,i.summaryXMLReader=new s.L,i.screenVersion=new s.V(i.commonService),i.posLvlData=null,i.inputData=new s.G(i.commonService),i.detailsData=new s.G(i.commonService),i.summaryData=new s.G(i.commonService),i.updateRefreshRate=new s.G(i.commonService),i.saveSettingsData=new s.G(i.commonService),i.sendData=new s.G(i.commonService),i.baseURL=s.Wb.getBaseURL(),i.actionMethod="",i.scenarioSummaryActionMethod="",i.actionPath="",i.scenarioSummaryActionPath="",i.requestParams=[],i.moduleId="Predict",i.errorLocation=0,i.SUMMARY_REFRESH="summaryRefresh",i.WORKFLOW_REFRESH="workFlowRefresh",i.colorsBlind=[65280,255,16777215,8454143,16711680,16750125,10987431,16777107,11493729,0],i.colorsNonBlind=[15693393,15321142,7320415,10596047,16711935,65535,10066278,10053375,16777088,16751052],i.colors=i.colorsNonBlind,i.colorBlindMode=!1,i.pieColorArr=[],i.autoRefresh=null,i.refreshRate=10,i.lastApplyThresholdSetting="",i.comboOpen=!1,i.comboChange=!1,i.tabData=[],i.tabDataCategory=[],i.showJsonPopup=null,i.screenName=s.Wb.getPredictMessage("label.workflowMonitor",null),i.versionNumber="1.0.0026",i.releaseDate="30 May 2019",i.indexSelected=0,i.existingEntityId=null,i.firstLoad=!0,i.lastSelectedIndex=-1,i.fisrtTablastSelectedId=null,i.secondTablastSelectedId=null,i.lastSelectedId="",i.firstTabTreeOpenedItems=[],i.firstTabTreeClosedItems=[],i.secondTabTreeOpenedItems=[],i.secondTabTreeClosedItems=[],i.previousSelectedTabIndex=-1,i.scenarioId="",i.scenarioRows=[],i.facilityGui="",i.sqlParams="",i.scenarioTitle="",i.lastSelectedNode="",i.statusChanged=!1,i.formatIso="yyyy-mm-dd",i.formatIsoTime="yyyy-mm-dd hh24:mi:ss",i.previousScrollPosition=0,i.lastSelectedValue=null,i.logger=new s.R("Work Flow Monitor",i.commonService.httpclient),i.swtAlert=new s.bb(e),window.Main=i,i}return n.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.summary=this.mainGroup.addChild(r.a),this.summary.width="100%",this.summary.height="100%",this.summary.summaryGrid.clientSideSort=!1,this.summary.summaryGrid.clientSideFilter=!1,this.summary.expandFirstLoad=!1,this.summary.summaryGrid.clientSideSort=!1,this.closeButton.toolTip=s.Wb.getPredictMessage("tooltip.close",null),this.closeButton.label=s.Wb.getPredictMessage("button.close",null),this.refreshButton.toolTip=s.Wb.getPredictMessage("tooltip.refreshWindow",null),this.refreshButton.label=s.Wb.getPredictMessage("button.Refresh",null),this.optionsButton.toolTip=s.Wb.getPredictMessage("button.option",null),this.optionsButton.label=s.Wb.getPredictMessage("button.option",null),this.rateButton.toolTip=s.Wb.getPredictMessage("tooltip.rateButton",null),this.rateButton.label=s.Wb.getPredictMessage("accountmonitorbutton.Rate",null),this.lblComboEntity.text=s.Wb.getPredictMessage("scenarioSummary.Entity",null),this.lastRefTimeLabel.text=s.Wb.getPredictMessage("screen.lastRefresh",null),this.entityCombo.toolTip=s.Wb.getPredictMessage("tooltip.selectEntityid",null),this.lostConnectionText.text=s.Wb.getPredictMessage("screen.connectionError",null),this.pdf.toolTip=s.Wb.getPredictMessage("label.exportPDF",null),this.resolvedOnLbl.text=s.Wb.getPredictMessage("scenarioSummary.resolvedOn",null),this.resolveButton.toolTip=s.Wb.getPredictMessage("tooltip.resolveButton",null),this.resolveButton.label=s.Wb.getPredictMessage("button.genericdisplaymonitor.resolve",null),this.reActiveButton.toolTip=s.Wb.getPredictMessage("tooltip.reActivateButton",null),this.reActiveButton.label=s.Wb.getPredictMessage("button.genericdisplaymonitor.reActivate",null),this.goToButton.toolTip=s.Wb.getPredictMessage("tooltip.goTo",null),this.goToButton.label=s.Wb.getPredictMessage("button.genericdisplaymonitor.goTo",null),this.detailsButton.toolTip=s.Wb.getPredictMessage("tooltip.details",null),this.detailsButton.label=s.Wb.getPredictMessage("button.genericdisplaymonitor.details",null),this.statusLabel.text=s.Wb.getPredictMessage("scenarioSummary.status",null),this.all.label=s.Wb.getPredictMessage("scenarioSummary.all",null),this.active.label=s.Wb.getPredictMessage("scenarioSummary.active",null),this.resolved.label=s.Wb.getPredictMessage("scenarioSummary.resolved",null),this.pending.label=s.Wb.getPredictMessage("scenarioSummary.pending",null),this.overdue.label=s.Wb.getPredictMessage("scenarioSummary.overdue",null),this.allOpen.label=s.Wb.getPredictMessage("scenarioSummary.allOpen",null),this.resolvedOnDate.toolTip=s.Wb.getPredictMessage("tooltip.resolvedOnDate",null)},e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.onLoad=function(){var t=this;this.initializeMenus(),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="workflowmonitor.do?",this.actionMethod="method=data",this.summary.summaryGrid.onRowClick=function(e){"Y"==t.summary.rcdScenInstance&&t.handleButtonsStatus()},this.summary.tree.ITEM_CLICK.subscribe(function(e){t.summaryTreeEventHandler(e)}),this.summary.summaryGrid.ITEM_CLICK.subscribe(function(e){t.cellLogic(e)}),this.summary.summaryGrid.columnWidthChanged.subscribe(function(e){t.updateWidths(e)}),this.treeDivider.DIVIDER_BUTTON_CLICK.subscribe(function(e){t.saveDividerStatusListener(e)}),s.E.subscribe(function(e){var l=""+t.summary.divBox.widthLeft;t.currDividerPosition=Number(l.substr(0,l.length-1)),t.updateWidths(e)}),this.summary.summaryGrid.onFilterChanged=this.doUpdateSortFilter.bind(this),this.summary.summaryGrid.onSortChanged=this.doUpdateSortFilter.bind(this),this.requestParams=[],this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.saveDividerStatusListener=function(t){this.saveSettings()},e.prototype.saveSettings=function(){this.requestParams=[],this.saveSettingsData.cbStart=this.startOfComms.bind(this),this.saveSettingsData.cbStop=this.endOfComms.bind(this),this.saveSettingsData.cbResult=function(t){},this.saveSettingsData.cbFault=this.inputDataFault.bind(this),this.saveSettingsData.encodeURL=!1,this.actionPath="workflowmonitor.do?",this.actionMethod="method=saveUserSettings",this.requestParams.isClosed="0"==this.treeDivider.widthLeft,this.requestParams.applyThreshold=this.lastApplyThresholdSetting,this.saveSettingsData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveSettingsData.send(this.requestParams)},e.prototype.updateWidths=function(t){for(var e=this,l=[],i=this.summary.summaryGrid.gridObj.getColumns().slice(0),n=i.length,a=0;a<n;a++)"expand"==i[a].id?i[a].width=5:"dummy"!=i[a].id&&null!=i[a].field&&l.push(i[a].field+"="+i[a].width);l.push("divider="+this.currDividerPosition),this.requestParams=[],this.sendData.encodeURL=!1,this.requestParams.width=l.join(","),this.actionPath="workflowmonitor.do?",this.actionMethod="method=saveColumnWidth&",this.sendData.cbResult=function(t){e.inputDataResultColumnsChange(t)},this.sendData.url=this.baseURL+this.actionPath+this.actionMethod,this.sendData.send(this.requestParams)},e.prototype.inputDataResultColumnsChange=function(t){if(this.sendData.isBusy())this.sendData.cbStop();else{this.lastReceivedWidthJSON=t;var e=new s.L;e.setInputJSON(this.lastReceivedWidthJSON),"Column width saved ok"!==e.getRequestReplyMessage()&&this.swtAlert.error(s.Wb.getPredictMessage("error.contactAdmin",null)+"\n"+e.getRequestReplyMessage())}},e.prototype.loadSummary=function(){var t=this,e="",l=null,i=null;e=this.extractParentFilterDataFromNode(this.summary.tree.selectedItem,e),this.previousScrollPosition=d(".customTreeWarapper")[0].scrollTop;var n=this.currencyThreshold.selected;this.requestParams.currThreshold=n,this.requestParams.entityId=this.entityCombo.selectedItem.content,this.requestParams.selectedCurrencyGroup=this.ccyCombo.selectedItem.content,this.requestParams.dividerPosition=this.summary.getDividerPosition(),this.requestParams.selectedTab=this.tabCategoryList.getSelectedTab().id,this.requestParams.isScenarioAlertable=""==this.summary.getIsScenarioAlertable()?"N":this.summary.getIsScenarioAlertable(),this.requestParams.fromWorkFlow="true",this.requestParams.callerMethod="___",this.firstLoad?this.requestParams.firstLoad="true":(this.requestParams.firstLoad="false",this.summary.saveTreeOpenState(),0===this.previousSelectedTabIndex?(this.firstTabTreeOpenedItems=this.summary.treeOpenedItems.concat(),this.firstTabTreeClosedItems=this.summary.treeClosedItems.concat()):(this.secondTabTreeOpenedItems=this.summary.treeOpenedItems.concat(),this.secondTabTreeClosedItems=this.summary.treeClosedItems.concat())),this.requestParams.facilityId=this.facilityGui,this.requestParams.facilityGuiId="WORKFLOW_MONITOR",this.requestParams.status=this.status.selectedValue,this.requestParams.sqlParams=this.sqlParams,this.requestParams.scenarioId=this.scenarioId,this.requestParams.selectedTreeItemQuery=e,this.requestParams.statusChanged=this.statusChanged,this.summary.tree.selectedItem&&"Y"==this.summary.tree.selectedItem.record_scenario_instances?(this.requestParams.selectedSort=this.summary.summaryGrid.sortedGridColumnId+"|"+(this.summary.summaryGrid.sortDirection?"DESC":"ASC"),l=this.getfilteredGridColumnsForInstanceGrid()):(i=this.summary.getSortedGridColumn(),this.requestParams.selectedSort=i,l=this.getfilteredGridColumns()),this.requestParams.selectedFilter=l,this.summaryData.cbResult=function(e){t.summaryDataResult(e)},this.summaryData.cbFault=this.inputDataFault.bind(this),this.summaryData.encodeURL=!1,this.scenarioSummaryActionPath="scenarioSummary.do?",this.scenarioSummaryActionMethod="method=getScenarioSummaryDetails",this.summaryData.url=this.baseURL+this.scenarioSummaryActionPath+this.scenarioSummaryActionMethod,this.summaryData.send(this.requestParams),null!=this.summary.tree.selectedItem&&this.summary.tree.selectedItem.data&&(this.lastSelectedValue=this.summary.tree.selectedItem.id)},e.prototype.updateData=function(t){this.requestParams=[],this.requestParams.resolvedOnDate=this.resolvedOnDate.text,this.requestParams.selectedScenario=this.summary.getSelectedItemID(),this.requestParams.isScenarioAlertable=""==this.summary.getIsScenarioAlertable()?"N":this.summary.getIsScenarioAlertable();var e=this.ccyCombo.selectedItem.content,l=this.entityCombo.selectedItem.content;this.summary.tree.selectedItem&&(0===this.previousSelectedTabIndex?this.fisrtTablastSelectedId=this.summary.tree.selectedItem.id:this.secondTablastSelectedId=this.summary.tree.selectedItem.id),this.summary.saveTreeOpenState(),0===this.previousSelectedTabIndex?(this.firstTabTreeOpenedItems=this.summary.treeOpenedItems.concat(),this.firstTabTreeClosedItems=this.summary.treeClosedItems.concat()):(this.secondTabTreeOpenedItems=this.summary.treeOpenedItems.concat(),this.secondTabTreeClosedItems=this.summary.treeClosedItems.concat()),t===this.WORKFLOW_REFRESH?(null!==e&&""!==e&&(this.requestParams.entityid=l),null!==l&&""!==e&&(this.requestParams.currencygrpid=e),this.requestParams.applycurrencythreshold=!0===this.currencyThreshold.selected?"Y":"N",-1!==this.tabList.selectedIndex&&(this.requestParams.selectedDate=this.tabList.getTabChildren()[this.tabList.selectedIndex].tabName),this.requestParams.existingEntityId=this.existingEntityId,this.requestParams.selectedSort=this.summary.getSortedGridColumn(),this.indexSelected=this.tabList.selectedIndex,-1!==this.tabCategoryList.selectedIndex&&(this.requestParams.selectedCategory=this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName),this.actionPath="workflowmonitor.do?",this.actionMethod="method=data",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)):(this.requestParams.selectedSort=this.summary.getSortedGridColumn(),this.loadSummary())},e.prototype.inputDataResult=function(t){try{this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.lostConnectionText.visible=!1,this.pdf.enabled=!0,""!==this.jsonReader.getScreenAttributes().displayedDate&&(this.displayedDate=this.jsonReader.getScreenAttributes().displayedDate),""!==this.jsonReader.getScreenAttributes().dateFormat&&(this.dateFormat=this.jsonReader.getScreenAttributes().dateFormat,this.resolvedOnDate.formatString=this.dateFormat.toLowerCase()),""!==this.jsonReader.getScreenAttributes().currencyFormat&&(this.currencyFormat=this.jsonReader.getScreenAttributes().currencyFormat);var e=this.jsonReader.getScreenAttributes().lastRefTime;if(this.lastRefTime.text=e.replace(/\\u0028/g,"(").replace(/\\u0029/g,")"),this.lastApplyThresholdSetting=this.jsonReader.getScreenAttributes().isThresholdToBeApplied,this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!==this.prevRecievedJSON){if(this.resolvedOnDate.text=this.displayedDate,this.screenOption=this.jsonReader.getScreenAttributes().isClosed,1==this.screenOption&&(this.treeDivider.widthLeft="0"),this.entityCombo.setComboData(this.jsonReader.getSelects()),this.selectedEntity.text=this.entityCombo.selectedItem.value,this.ccyCombo.setComboData(this.jsonReader.getSelects()),this.ccyCombo.enabled=!("All"==this.entityCombo.selectedLabel),this.selectedCcy.text=this.ccyCombo.selectedItem.value,this.currencyThreshold.selected=!0===Boolean(this.jsonReader.getScreenAttributes().currencythreshold),this.existingEntityId=this.jsonReader.getScreenAttributes().existingEntityId,1===this.ccyCombo.dataProvider.length&&"All"!==this.entityCombo.selectedItem.value&&(null!==this.autoRefresh&&this.autoRefresh.stop(),this.comboOpen=!0,this.swtAlert.error(s.Wb.getPredictMessage("alert.currencyAccess",null),"Error")),this.jsonReader.isDataBuilding()?this.dataBuildingText.visible=!0:(this.dataBuildingText.visible=!1,(this.posLvlData||this.posLvlData!==this.deepCopy(this.lastRecievedJSON.workflowmonitor.positions.position))&&(this.posLvlData=this.deepCopy(this.lastRecievedJSON.workflowmonitor.positions.position)),this.labelExc0txt.text=this.posLvlData[0].level,this.labelExc1txt.text=this.posLvlData[1].level,this.labelExc2txt.text=this.posLvlData[2].level,this.labelExc3txt.text=this.posLvlData[3].level,this.labelExc4txt.text=this.posLvlData[4].level,this.labelExc5txt.text=this.posLvlData[5].level,this.labelExc6txt.text=this.posLvlData[6].level,this.labelExc7txt.text=this.posLvlData[7].level,this.labelExc8txt.text=this.posLvlData[8].level,this.valueInc0Btn.label=this.posLvlData[0].included,this.valueExc0Btn.label=this.posLvlData[0].excluded,this.valueInc1Btn.label=this.posLvlData[1].included,this.valueExc1Btn.label=this.posLvlData[1].excluded,this.valueInc2Btn.label=this.posLvlData[2].included,this.valueExc2Btn.label=this.posLvlData[2].excluded,this.valueInc3Btn.label=this.posLvlData[3].included,this.valueExc3Btn.label=this.posLvlData[3].excluded,this.valueInc4Btn.label=this.posLvlData[4].included,this.valueExc4Btn.label=this.posLvlData[4].excluded,this.valueInc5Btn.label=this.posLvlData[5].included,this.valueExc5Btn.label=this.posLvlData[5].excluded,this.valueInc6Btn.label=this.posLvlData[6].included,this.valueExc6Btn.label=this.posLvlData[6].excluded,this.valueInc7Btn.label=this.posLvlData[7].included,this.valueExc7Btn.label=this.posLvlData[7].excluded,this.valueInc8Btn.label=this.posLvlData[8].included,this.valueExc8Btn.label=this.posLvlData[8].excluded,this.incMovementsEnableDisable(),this.excMovementsEnableDisable(),this.labelExcTotalValue.text=this.lastRecievedJSON.workflowmonitor.positions.excludedtotal,this.labelIncTotalValue.text=this.lastRecievedJSON.workflowmonitor.positions.includedtotal,this.lastRecievedJSON.workflowmonitor.systemsummary.system&&"loggedonusers"==this.lastRecievedJSON.workflowmonitor.systemsummary.system.id&&(this.logonBtn.label=""==String(this.lastRecievedJSON.workflowmonitor.systemsummary.system.content)?"0":String(this.lastRecievedJSON.workflowmonitor.systemsummary.system.content),"0"===this.logonBtn.label?this.logonBtn.enabled=!1:this.logonBtn.enabled=!0)),null==this.autoRefresh&&(this.refreshRate=parseInt(this.jsonReader.getRefreshRate(),0),this.refreshRate<5&&(this.refreshRate=30),this.autoRefresh=new s.cc(1e3*this.refreshRate,0),this.autoRefresh.addEventListener("timer",this.dataRefresh.bind(this))),this.tabData=[],0===this.tabList.getTabChildren().length&&this.lastRecievedJSON.workflowmonitor.tabs.predictdate)for(var l=0;l<this.lastRecievedJSON.workflowmonitor.tabs.predictdate.length;l++)this.tabData[l]=this.tabList.addChild(s.Xb),this.tabData[l].id=l,this.tabData[l].label=this.lastRecievedJSON.workflowmonitor.tabs.predictdate[l].content.substring(0,5),this.tabData[l].tabName=this.lastRecievedJSON.workflowmonitor.tabs.predictdate[l].content,this.tabData[l].businessday=this.lastRecievedJSON.workflowmonitor.tabs.predictdate[l].businessday,0==this.tabList.getChildAt(l).businessday?this.tabList.getChildAt(l).setTabHeaderStyle("color","darkgray"):this.tabList.getChildAt(l).setTabHeaderStyle("color","black");var i=Number(this.lastRecievedJSON.workflowmonitor.tabs.selectedIndex.index);if(this.tabList.selectedIndex=i,this.prevRecievedJSON=this.lastRecievedJSON,this.firstLoad&&(this.tabDataCategory=[],0===this.tabCategoryList.getTabChildren().length&&this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab))for(l=0;l<2;l++)this.tabDataCategory[l]=this.tabCategoryList.addChild(s.Xb),this.tabDataCategory[l].id=this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab[l].tabId,this.tabDataCategory[l].label=this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab[l].tabName,this.tabDataCategory[l].tabName=this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab[l].tabName,this.tabDataCategory[l].count=this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab[l].count;this.tabCategoryList.selectedIndex=Number(this.lastRecievedJSON.workflowmonitor.tabsCategory.selectedIndex.index)}this.loadSummary()}else this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error");null!==this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())}catch(n){console.log("\ud83d\ude80 ~ file: WorkFlowMonitor.ts ~ line 694 ~ WorkFlowMonitor ~ inputDataResult ~ e",n)}},e.prototype.deepCopy=function(t){var e,l=[];for(e in t)l[e]=t[e];return l},e.prototype.summaryDataResult=function(t){var e=this;try{var l=!1;this.summaryData.isBusy()?this.summaryData.cbStop():(this.lastRecievedSummaryJSON=t,this.summaryXMLReader.setInputJSON(this.lastRecievedSummaryJSON),this.summaryXMLReader.getRequestReplyStatus()?this.lastRecievedSummaryJSON!==this.prevRecievedSummaryJSON&&(this.optionIdTypes=this.summaryXMLReader.getSelects().select.find(function(t){return"otherIdTypeCombo"==t.id}).option,this.summary.dataProvider(this.lastRecievedSummaryJSON),this.summaryXMLReader.getSingletons().scenarioTotalForTab1&&(this.tabCategoryList.getChildAt(0).label=this.tabDataCategory[0].tabName+"("+this.summaryXMLReader.getSingletons().scenarioTotalForTab1+")"),this.summaryXMLReader.getSingletons().scenarioTotalForTab2&&(this.tabCategoryList.getChildAt(1).label=this.tabDataCategory[1].tabName+"("+this.summaryXMLReader.getSingletons().scenarioTotalForTab2+")"),0===this.tabCategoryList.selectedIndex?(this.lastSelectedId=this.fisrtTablastSelectedId,this.summary.treeOpenedItems=this.firstTabTreeOpenedItems.concat(),this.summary.treeClosedItems=this.firstTabTreeClosedItems.concat()):(this.lastSelectedId=this.secondTablastSelectedId,this.summary.treeOpenedItems=this.secondTabTreeOpenedItems.concat(),this.summary.treeClosedItems=this.secondTabTreeClosedItems.concat()),0===this.summary.treeOpenedItems.length&&(this.summary.tree.openItems=[]),0===this.summary.treeClosedItems.length&&(this.summary.tree.closeItems=[]),this.summary.setBaseURL(this.baseURL),this.summary.setActionPath(this.actionPath),this.lastRecievedSummaryJSON.scenarioinstancedetails.tree.root&&this.lastRecievedSummaryJSON.scenarioinstancedetails.tree.root.node&&this.lastRecievedSummaryJSON.scenarioinstancedetails.tree.root.node[0].node&&(this.firstLoad?(this.summary.tree.expandAll(s.o.LEVEL_2_STR),this.firstLoad=!1,l=!0):(this.summary.openTreeItems(),this.summary.tree.selectNodeByAttribute("id",this.lastSelectedNode?this.lastSelectedNode:"N_"+this.scenarioId),setTimeout(function(){d(".customTreeWarapper")[0].scrollTop=e.previousScrollPosition},200))),!1===this.mainGroup.visible&&(this.mainGroup.visible=!0)):this.swtAlert.show(this.summaryXMLReader.getRequestReplyMessage()+"\n"+this.summaryXMLReader.getRequestReplyLocation(),"Error"),this.prevRecievedSummaryJSON=this.lastRecievedSummaryJSON),this.refreshTreeItemRender(),this.handleButtonsStatus(),this.previousSelectedTabIndex=this.tabCategoryList.selectedIndex,l&&this.summary.tree&&this.summary.tree.selectedItem&&setTimeout(function(){e.requestParams.refreshGridOnly="true",e.scenarioId=e.getScenarioId(e.summary.tree.selectedItem,e.summary.tree.selectedItem.level),e.loadSummary()},1e3)}catch(i){}},e.prototype.summaryTreeEventHandler=function(t){var e=!1;this.summary.summaryGrid.selectedIndex=-1;var l=this.summary.tree.selectedItem.id;l!=this.lastSelectedValue&&(e=!0),this.lastSelectedValue=l,this.lastSelectedNode=this.summary.tree.selectedItem.id;var i=t.level,n=this.getScenarioId(t,i);e&&(this.scenarioId=n,this.requestParams.refreshGridOnly="true",this.summary.summaryGrid.resetFilter(),this.summary.summaryGrid.filteredGridColumns="",this.summary.resetGridData(),this.loadSummary())},e.prototype.extractParentFilterDataFromNode=function(t,e){if(null!=t&&null!=t&&null!=t.treeLevelName){if("category_id"==t.treeLevelName||"CATEGORY_ID"==t.treeLevelName)e+=" AND P_SCENARIO."+t.treeLevelName+" = '"+t.treeLevelValue+"'";else e+=new RegExp(/^\d{4}-([0]\d|1[0-2])-([0-2]\d|3[01])$/).test(t.treeLevelValue)?"  AND PSI."+t.treeLevelName+" =  TO_DATE('"+t.treeLevelValue+"','YYYY-MM-DD')":" AND PSI."+t.treeLevelName+" = '"+t.treeLevelValue+"'";e=this.extractParentFilterDataFromNode(t.parentData,e)}return e},e.prototype.getfilteredGridColumnsForInstanceGrid=function(){var t="",e=[],l=this.summary.summaryGrid.getFilterColumns(),i=this.summary.summaryGrid.filteredGridColumns,n="dd/MM/yyyy"==this.dateFormat?"dd/mm/yyyy hh24:mi:ss":"mm/dd/yyyy hh24:mi:ss";try{for(var a=0;a<l.length;a++)e[a]=l[a].field;if(""!=i){var s=i.split("|");for(a=0;a<e.length;a++){if(""!=s[a]){if("All"!=s[a]&&null!=s[a])if("_"==e[a][e[a].length-1]&&(e[a]=e[a].slice(0,-1)),"_id"==e[a])t=t+"ID='"+s[a]+"' and ";else if("scenarioId"==e[a])t=t+"SCENARIO_ID='"+s[a]+"' and ";else if("uniqueIdentifier"==e[a])t=t+"UNIQUE_IDENTIFIER='"+s[a]+"' and ";else if("status"==e[a]){var o="";switch(s[a]){case"Active":o="A";break;case"Resolved":o="R";break;case"Pending":o="P";break;case"Overdue":o="O";break;default:o="A"}t=t+"STATUS='"+o+"' and "}else if("raisedDatetime"==e[a])t=t+"RAISED_DATETIME=TO_DATE ('"+s[a]+"' , '"+n+"') and ";else if("lastRaisedDatetime"==e[a])t=t+"LAST_RAISED_DATETIME=TO_DATE ('"+s[a]+"' , '"+n+"') and ";else if("resolvedDatetime"==e[a])t=t+"RESOLVED_DATETIME=TO_DATE ('"+s[a]+"' , '"+n+"') and ";else if("resolvedByUser"==e[a])t=t+"RESOLVED_BY_USER='"+s[a]+"' and ";else if("eventsLaunchStatus"==e[a]){var u="";switch(s[a]){case"No events to launch":u="N";break;case"Waiting to Launch":u="W";break;case"Launched":u="L";break;case"Failed":u="F";break;default:u="W"}t=t+"EVENTS_LAUNCH_STATUS='"+u+"' and "}else if("hostId"==e[a])t=t+"HOST_ID='"+s[a]+"' and ";else if("entityId"==e[a])t=t+"ENTITY_ID='"+s[a]+"' and ";else if("currencyCode"==e[a])t=t+"CURRENCY_CODE='"+s[a]+"' and ";else if("accountId"==e[a])t=t+"ACCOUNT_ID='"+s[a]+"' and ";else if("amount"==e[a]){var r=void 0;null!=s[a]&&null!=s[a]&&(r="currencyPat2"==this.currencyFormat?Number(s[a].replace(/\./g,"").replace(/,/g,".")):Number(s[a].replace(/,/g,""))),t=t+"AMOUNT='"+r+"' and "}else"sign"==e[a]?t=t+"SIGN='"+s[a]+"' and ":"overThreshold"==e[a]?t=t+"OVER_THRESHOLD='"+s[a]+"' and ":"movementId"==e[a]?t=t+"MOVEMENT_ID='"+s[a]+"' and ":"matchId"==e[a]?t=t+"MATCH_ID='"+s[a]+"' and ":"sweepId"==e[a]?t=t+"SWEEP_ID='"+s[a]+"' and ":"paymentId"==e[a]?t=t+"PAYMENT_ID='"+s[a]+"' and ":"valueDate"==e[a]?t=t+"VALUE_DATE=TO_DATE ('"+s[a]+"' , '"+n+"') and ":"otherId"==e[a]?t=t+"OTHER_ID='"+s[a]+"' and ":"otherIdType"==e[a]&&(t=t+"OTHER_ID_TYPE='"+s[a]+"' and ")}else"_"==e[a][e[a].length-1]&&(e[a]=e[a].slice(0,-1)),"scenarioId"==e[a]?t+="SCENARIO_ID is null and ":"uniqueIdentifier"==e[a]?t+="UNIQUE_IDENTIFIER is null and ":"status"==e[a]?t+="STATUS is null and ":"raisedDatetime"==e[a]?t+="RAISED_DATETIME is null and ":"lastRaisedDatetime"==e[a]?t+="LAST_RAISED_DATETIME is null and ":"resolvedDatetime"==e[a]?t+="RESOLVED_DATETIME is null and ":"resolvedByUser"==e[a]?t+="RESOLVED_BY_USER is null and ":"eventsLaunchStatus"==e[a]?t+="EVENTS_LAUNCH_STATUS is null and ":"hostId"==e[a]?t+="HOST_ID is null and ":"entityId"==e[a]?t+="ENTITY_ID is null and ":"currencyCode"==e[a]?t+="CURRENCY_CODE is null and ":"accountId"==e[a]?t+="ACCOUNT_ID is null and ":"amount"==e[a]?t+="AMOUNT is null and ":"sign"==e[a]?t+="SIGN is null and ":"overThreshold"==e[a]?t+="OVER_THRESHOLD is null and ":"movementId"==e[a]?t+="MOVEMENT_ID is null and ":"matchId"==e[a]?t+="MATCH_ID is null and ":"sweepId"==e[a]?t+="SWEEP_ID is null and ":"paymentId"==e[a]?t+="PAYMENT_ID is null and ":"valueDate"==e[a]?t+="VALUE_DATE is null and ":"otherId"==e[a]?t+="OTHER_ID is null and ":"otherIdType"==e[a]&&(t+="OTHER_ID_TYPE is null and ")}}return t=t.substring(0,t.length-5)}catch(h){console.log("error",h)}},e.prototype.getScenarioId=function(t,e){var l=null;switch(e){case"Level1":l=null;break;case"Level2":l=t.data.treeLevelValue;break;case"Level3":l=t.data.parentData.treeLevelValue;break;case"Level4":l=t.data.parentData.parentData.treeLevelValue;break;case"Level5":l=t.data.parentData.parentData.parentData.treeLevelValue;break;case"Level6":l=t.data.parentData.parentData.parentData.parentData.treeLevelValue;break;case"Level7":l=t.data.parentData.parentData.parentData.parentData.parentData.treeLevelValue}return l},e.prototype.cellClickEventHandler=function(t){var e=t.target.field,l=this.summary.setClickable(t.target.data,null,null),i=t.target.data.slickgrid_rowcontent[e]?t.target.data.slickgrid_rowcontent[e].content:null;if(l&&i>0){var n=t.target.data.slickgrid_rowcontent.entity.content,a=t.target.data.slickgrid_rowcontent.ccy.content,o=t.target.data.slickgrid_rowcontent[e].content,u=this.summary.facilityId,r=this.summary.facilityName,h=this.summary.useGeneric,d=this.summary.scenarioTitle,c=!0===this.currencyThreshold.selected?"Y":"N",b=this.summary.selectedscenario;s.x.call("openFacility",d,h,u,r,b,n,a,c,o,this.ccyCombo.selectedItem.content)}},e.prototype.cellLogic=function(t){if(this.summary.summaryGrid.selectedIndex>-1){var e=t.target.field,l=t.target.data;if("Y"==this.summary.rcdScenInstance&&(this.attributeJSON=t.target.data.slickgrid_rowcontent.attributesXml.code,this.attributeJSON&&this.isValidJSON(this.attributeJSON))){var i=JSON.parse(this.attributeJSON);i=JSON.stringify(i,function(t,e){return""!==t&&"rowset"!==t&&"row"!==t&&"object"==typeof e?Object.entries(e).reduce(function(t,e){var l=e[0],i=e[1];return t[l]=JSON.stringify(i),t},{}):e},4),this.attributeJSON=this.htmlEntities(i.replace(/\\"/g,'"').replace(/"{/g,"{").replace(/}"/g,"}"))}(l.slickgrid_rowcontent[e]?l.slickgrid_rowcontent[e].clickable:null)&&("Y"==this.summary.rcdScenInstance?this.clickLink("fromLink"):this.openFacility(l,e))}},e.prototype.htmlEntities=function(t){try{return String(t).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/ /g,"&nbsp;")}catch(e){console.log("error",e,t)}},e.prototype.clickLink=function(t){var e=this;this.requestParams=[],this.detailsData.cbStart=this.startOfComms.bind(this),this.detailsData.cbStop=this.endOfComms.bind(this),this.detailsData.cbFault=this.inputDataFault.bind(this),this.detailsData.encodeURL=!1,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getInstanceXml",this.requestParams.instanceId=this.summary.summaryGrid.selectedItem.id.content,this.detailsData.url=this.baseURL+this.actionPath+this.actionMethod,this.detailsData.cbResult=function(t){e.getInstanceDetails(t)},this.detailsData.send(this.requestParams),this.source=t},e.prototype.getInstanceDetails=function(t){if(this.detailsData.isBusy())this.detailsData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastRecievedJSON!==this.prevRecievedJSON){var e=this.jsonReader.getSingletons().instanceXml.replace(/#/g,">");if(e&&this.isValidJSON(e)){var l=JSON.parse(e);l=JSON.stringify(l,function(t,e){return""!==t&&"rowset"!==t&&"row"!==t&&"object"==typeof e?Object.entries(e).reduce(function(t,e){var l=e[0],i=e[1];return t[l]=JSON.stringify(i),t},{}):e},4),this.attributeJSON=this.htmlEntities(l.replace(/\\"/g,'"').replace(/"{/g,"{").replace(/}"/g,"}"))}else this.attributeJSON=e;"fromLink"==this.source?(this.win=s.Eb.createPopUp(this,h.a,{title:"Scenario Instance Attributes",attributeXmlText:this.attributeJSON}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="450",this.win.height="500",this.win.showControls=!0,this.win.id="AttributeXML",this.win.display()):this.openInstDetails(e)}},e.prototype.incMovement=function(t){var e=t,l="",i="";10===e?(e=0,l="R",i="E"):(l="I",i=" ");var n="outstandingmovement.do?method=flexWorkflow&entityId="+this.entityCombo.selectedItem.content.toString()+"&currGrp="+this.ccyCombo.selectedItem.content.toString()+"&valueDate="+s.x.call("eval","systemDate")+"&roleId="+s.x.call("eval","roleId")+"&totalCount=0&posLvlId="+e+"&predictStatus="+l+"&matchStatus="+i+"&tabIndicator="+this.tabList.selectedIndex+"&applyCurrencyThreshold="+(this.currencyThreshold.selected?"Y":"N");s.x.call("openJavaWindow",n,50,190,1280,685)},e.prototype.getfilteredGridColumns=function(){try{var t="",e=[];if(""===this.summary.summaryGrid.filteredGridColumns)return t="";for(var l=this.summary.summaryGrid.getFilterColumns(),i=this.summary.summaryGrid.filteredGridColumns,n=0;n<l.length;n++)e[n]=l[n].field;if(""!=i){var a=i.split("|");for(n=0;n<e.length-1;n++)t=t+a[n]+"|"}else t=this.summary.summaryGrid.getFilteredGridColumns();return t.slice(4,t.length)}catch(s){}},e.prototype.doUpdateSortFilter=function(){var t=null,e=null;try{var l=this.ccyCombo.selectedItem.content,i=this.entityCombo.selectedItem.content;e=this.getfilteredGridColumns(),t=this.summary.getSortedGridColumn(),this.requestParams=[],this.firstLoad=!1,this.requestParams.isScenarioAlertable=""==this.summary.getIsScenarioAlertable()?"N":this.summary.getIsScenarioAlertable(),this.requestParams.selectedScenario=this.summary.getSelectedItemID(),"Y"==this.summary.rcdScenInstance?(this.requestParams.selectedSort=this.summary.summaryGrid.sortedGridColumnId+"|"+(this.summary.summaryGrid.sortDirection?"DESC":"ASC"),e=this.getfilteredGridColumnsForInstanceGrid()):(t=this.summary.getSortedGridColumn(),this.requestParams.selectedSort=t,e=this.getfilteredGridColumns()),this.requestParams.selectedFilter=e,null!==l&&""!==l&&(this.requestParams.entityid=i),null!==i&&""!==l&&(this.requestParams.currencygrpid=l),this.requestParams.applycurrencythreshold=!0===this.currencyThreshold.selected?"Y":"N",-1!==this.tabList.selectedIndex&&(this.requestParams.selectedDate=this.tabList.getTabChildren()[this.tabList.selectedIndex].tabName),this.requestParams.existingEntityId=this.existingEntityId,this.summary.saveTreeOpenState(),this.loadSummary()}catch(n){}},e.prototype.closeHandler=function(){s.x.call("close")},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.releaseDate),this.screenVersion.svContextMenu.customItems=[];var t=new s.n("Expand All Groups");t.MenuItemSelect=this.contextMenuAction.bind(this,"expandAll"),this.screenVersion.svContextMenu.customItems.push(t);var e=new s.n("Expand To Level 1");e.MenuItemSelect=this.contextMenuAction.bind(this,"expandLevel1"),this.screenVersion.svContextMenu.customItems.push(e);var l=new s.n("Expand To Level 2");l.MenuItemSelect=this.contextMenuAction.bind(this,"expandLevel2"),this.screenVersion.svContextMenu.customItems.push(l);var i=new s.n("Collapse All Groups");i.MenuItemSelect=this.contextMenuAction.bind(this,"collapseAll"),this.screenVersion.svContextMenu.customItems.push(i),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.contextMenuAction=function(t){"expandAll"==t?this.summary.tree.expandAll():"expandLevel1"==t?(this.summary.tree.collapseAll(),this.summary.tree.expandAll("1")):"expandLevel2"==t?(this.summary.tree.collapseAll(),this.summary.tree.expandAll("2")):"collapseAll"==t&&this.summary.tree.collapseAll(),this.summary.saveTreeOpenState()},e.prototype.showJSONSelect=function(t){this.showJsonPopup=s.Eb.createPopUp(this,s.M,{jsonData:this.lastRecievedJSON}),this.showJsonPopup.width="700",this.showJsonPopup.title="Workflow JSON",this.showJsonPopup.height="500",this.showJsonPopup.enableResize=!1,this.showJsonPopup.showControls=!0,this.showJsonPopup.display()},e.prototype.showSummaryJSONSelect=function(t){null!==this.lastRecievedSummaryJSON?(this.showJsonPopup=s.Eb.createPopUp(this,s.M,{jsonData:this.lastRecievedSummaryJSON}),this.showJsonPopup.width="700",this.showJsonPopup.title="Summary Details JSON",this.showJsonPopup.height="500",this.showJsonPopup.enableResize=!1,this.showJsonPopup.showControls=!0,this.showJsonPopup.display()):this.swtAlert.warning("No data to display","Warning - Summary Details")},e.prototype.changeColours=function(t){this.colorBlindMode?(this.colors=this.colorsNonBlind,this.colorBlindMode=!1):(this.colors=this.colorsBlind,this.colorBlindMode=!0)},e.prototype.export=function(t){var e=[];e.push("Entity="+this.entityCombo.selectedItem.content),e.push("Ccy="+this.ccyCombo.selectedItem.content),e.push("Selected Tab="+this.tabList.getTabChildren()[this.tabList.selectedIndex].tabName),e.push("Currency Threshold="+(this.currencyThreshold.selected?"On":"Off"));for(var l="<rows>",i="<data>",n=0;n<this.posLvlData.length;n++)""!=this.posLvlData[n].level&&(isNaN(parseInt(this.posLvlData[n].excluded,10))||(l+="<row><name>Outstanding "+this.posLvlData[n].level+"</name><value>"+parseInt(this.posLvlData[n].excluded,10)+"</value></row>"));for(n=0;n<this.posLvlData.length;n++)""!=this.posLvlData[n].level&&(isNaN(parseInt(this.posLvlData[n].included,10))||(l+="<row><name>Included "+this.posLvlData[n].level+"</name><value>"+parseInt(this.posLvlData[n].included,10)+"</value></row>"));l=(l+="</rows>").replace("+",""),i+=this.removeLineBreaks(l),i=(i=(i=(i=(i=(i=(i+=this.removeLineBreaks('<columns><column heading="Name" type="str" dataelement="name" /><column heading="Value" type="num" dataelement="value" /></columns>')).split("\\").join("BACKSLASH_REPLACE")).split("'").join("\\\\'")).split("&amp;").join("\\&")).split("%").join("PERCENTAGE_REPLACE")).split("&lt;").join("\\<")).split("&gt;").join("\\>");var a="<filters>";if(null!=e)for(var o=0;o<e.length;o++)a+='<filter id="'+e[o].split("=")[0]+'">'+e[o].split("=")[1]+"</filter>";i+=a+="</filters>",i+="</data>",s.x.call("exportData",t,i),s.x.call("eval","document.getElementById('exportDataForm').action='flexdataexport.do?method="+t+"';"),s.x.call("eval","document.getElementById('exportData').value='"+i+"';"),s.x.call("eval","document.getElementById('exportDataForm').submit();")},e.prototype.removeLineBreaks=function(t){var e,l="";e=t.split("\n");for(var i=0;i<e.length;i++)l+=e[i];return l},e.prototype.excOutStandings=function(t,e){var l="outstandingmovement.do?method=displayOpenMovementCountByCurrency&entityId="+this.entityCombo.selectedItem.content.toString()+"&currGrp="+this.ccyCombo.selectedItem.content.toString()+"&valueDate="+s.x.call("eval","testDate")+"&selectedTabIndex="+(this.tabCategoryList.selectedIndex+1)+"&applyCurrencyThreshold="+(this.currencyThreshold.selected?"Y":"N")+"&selectedValueDate="+this.tabList.getTabChildren()[this.tabList.selectedIndex].tabName+"&workflow=W";s.x.call("openJavaWindow",l,50,190,1280,635)},e.prototype.getMenuAccessID=function(t){return String(s.x.call("getMenuAccessId",t))},e.prototype.loggedOnClick=function(){var t=this.getMenuAccessID("User Status"),e="userStatus.do?&menuAccessId="+t;if("2"!==t)s.x.call("openJavaWindow",e,50,190,942,530)},e.prototype.helpClick=function(){s.x.call("openHelpWindow('stuff')")},e.prototype.refreshTreeItemRender=function(){},e.prototype.dataRefresh=function(t){new Array([this.WORKFLOW_REFRESH]);this.comboOpen||this.updateData(this.WORKFLOW_REFRESH),this.autoRefresh.stop()},e.prototype.changeCombo=function(t){this.comboChange=!0,this.updateData(this.WORKFLOW_REFRESH)},e.prototype.rateHandler=function(){try{this.refreshRatePopup=s.Eb.createPopUp(this,u.a,{title:"Auto-refresh Rate",refreshText:this.refreshRate}),this.refreshRatePopup.width="340",this.refreshRatePopup.height="150",this.refreshRatePopup.id="myRatePopUp",this.refreshRatePopup.enableResize=!1,this.refreshRatePopup.showControls=!0,this.refreshRatePopup.display()}catch(t){s.Wb.logError(t,this.moduleId,"WorkFlow Monitor","rateHandler",this.errorLocation)}},e.prototype.saveRefreshRate=function(t){if(""==t||null==t)this.swtAlert.error(s.Wb.getPredictMessage("accountMonitor.alert.label.notANumber",null),"Error");else{var e=!1;this.refreshRate=Number(t),this.refreshRate<5&&(this.refreshRate=5,e=!0),e&&this.swtAlert.warning(s.Wb.getPredictMessage("alert.refreshRateSelectedMonimum",null),"Warning"),this.autoRefresh&&this.autoRefresh.delay(1e3*this.refreshRate);var l=s.x.call("getUpdateRefreshRequest",this.refreshRate);null!==l&&""!==l&&(this.updateRefreshRate.encodeURL=!1,this.updateRefreshRate.url=this.baseURL+l,this.updateRefreshRate.send())}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.disableInterface()},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableInterface()},e.prototype.inputDataFault=function(t){this.lostConnectionText.visible=!0,null!==this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())},e.prototype.disableInterface=function(){this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1,this.summary.disableTree()},e.prototype.enableInterface=function(){this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0,this.summary.enableTree()},e.prototype.comboFocusOutHandler=function(t){t.currentTarget.name===this.entityCombo.id?0===s.Z.trim(this.entityCombo.selectedItem.content).length&&this.entityCombo.setComboData(this.jsonReader.getSelects()):0===s.Z.trim(this.ccyCombo.selectedItem.content).length&&this.ccyCombo.setComboData(this.jsonReader.getSelects())},e.prototype.incMovementsEnableDisable=function(){"All"===this.entityCombo.selectedItem.content?(this.valueInc0Btn.enabled=!1,this.valueInc1Btn.enabled=!1,this.valueInc2Btn.enabled=!1,this.valueInc3Btn.enabled=!1,this.valueInc4Btn.enabled=!1,this.valueInc5Btn.enabled=!1,this.valueInc6Btn.enabled=!1,this.valueInc7Btn.enabled=!1,this.valueInc8Btn.enabled=!1,this.valueInc0Btn.buttonMode=!1,this.valueInc1Btn.buttonMode=!1,this.valueInc2Btn.buttonMode=!1,this.valueInc3Btn.buttonMode=!1,this.valueInc4Btn.buttonMode=!1,this.valueInc5Btn.buttonMode=!1,this.valueInc6Btn.buttonMode=!1,this.valueInc7Btn.buttonMode=!1,this.valueInc8Btn.buttonMode=!1):(""!=this.valueInc0Btn.label&&"0"!=this.valueInc0Btn.label?(this.valueInc0Btn.enabled=!0,this.valueInc0Btn.buttonMode=!0):(this.valueInc0Btn.enabled=!1,this.valueInc0Btn.buttonMode=!1),""!=this.valueInc1Btn.label&&"0"!=this.valueInc1Btn.label?(this.valueInc1Btn.enabled=!0,this.valueInc1Btn.buttonMode=!0):(this.valueInc1Btn.enabled=!1,this.valueInc1Btn.buttonMode=!1),""!=this.valueInc2Btn.label&&"0"!=this.valueInc2Btn.label?(this.valueInc2Btn.enabled=!0,this.valueInc2Btn.buttonMode=!0):(this.valueInc2Btn.enabled=!1,this.valueInc2Btn.buttonMode=!1),""!=this.valueInc3Btn.label&&"0"!=this.valueInc3Btn.label?(this.valueInc3Btn.enabled=!0,this.valueInc3Btn.buttonMode=!0):(this.valueInc3Btn.enabled=!1,this.valueInc3Btn.buttonMode=!1),""!=this.valueInc4Btn.label&&"0"!=this.valueInc4Btn.label?(this.valueInc4Btn.enabled=!0,this.valueInc4Btn.buttonMode=!0):(this.valueInc4Btn.enabled=!1,this.valueInc4Btn.buttonMode=!1),""!=this.valueInc5Btn.label&&"0"!=this.valueInc5Btn.label?(this.valueInc5Btn.enabled=!0,this.valueInc5Btn.buttonMode=!0):(this.valueInc5Btn.enabled=!1,this.valueInc5Btn.buttonMode=!1),""!=this.valueInc6Btn.label&&"0"!=this.valueInc6Btn.label?(this.valueInc6Btn.enabled=!0,this.valueInc6Btn.buttonMode=!0):(this.valueInc6Btn.enabled=!1,this.valueInc6Btn.buttonMode=!1),""!=this.valueInc7Btn.label&&"0"!=this.valueInc7Btn.label?(this.valueInc7Btn.enabled=!0,this.valueInc7Btn.buttonMode=!0):(this.valueInc7Btn.enabled=!1,this.valueInc7Btn.buttonMode=!1),""!=this.valueInc8Btn.label&&"0"!=this.valueInc8Btn.label?(this.valueInc8Btn.enabled=!0,this.valueInc8Btn.buttonMode=!0):(this.valueInc8Btn.enabled=!1,this.valueInc8Btn.buttonMode=!1))},e.prototype.excMovementsEnableDisable=function(){"All"==this.entityCombo.selectedItem.value?(this.valueExc0Btn.enabled=!1,this.valueExc1Btn.enabled=!1,this.valueExc2Btn.enabled=!1,this.valueExc3Btn.enabled=!1,this.valueExc4Btn.enabled=!1,this.valueExc5Btn.enabled=!1,this.valueExc6Btn.enabled=!1,this.valueExc7Btn.enabled=!1,this.valueExc8Btn.enabled=!1,this.valueExc0Btn.buttonMode=!1,this.valueExc1Btn.buttonMode=!1,this.valueExc2Btn.buttonMode=!1,this.valueExc3Btn.buttonMode=!1,this.valueExc4Btn.buttonMode=!1,this.valueExc5Btn.buttonMode=!1,this.valueExc6Btn.buttonMode=!1,this.valueExc7Btn.buttonMode=!1,this.valueExc8Btn.buttonMode=!1):(""!=this.valueExc0Btn.label&&"0"!=this.valueExc0Btn.label?(this.valueExc0Btn.enabled=!0,this.valueExc0Btn.buttonMode=!0):(this.valueExc0Btn.enabled=!1,this.valueExc0Btn.buttonMode=!1),""!=this.valueExc1Btn.label&&"0"!=this.valueExc1Btn.label?(this.valueExc1Btn.enabled=!0,this.valueExc1Btn.buttonMode=!0):(this.valueExc1Btn.enabled=!1,this.valueExc1Btn.buttonMode=!1),""!=this.valueExc2Btn.label&&"0"!=this.valueExc2Btn.label?(this.valueExc2Btn.enabled=!0,this.valueExc2Btn.buttonMode=!0):(this.valueExc2Btn.enabled=!1,this.valueExc2Btn.buttonMode=!1),""!=this.valueExc3Btn.label&&"0"!=this.valueExc3Btn.label?(this.valueExc3Btn.enabled=!0,this.valueExc3Btn.buttonMode=!0):(this.valueExc3Btn.enabled=!1,this.valueExc3Btn.buttonMode=!1),""!=this.valueExc4Btn.label&&"0"!=this.valueExc4Btn.label?(this.valueExc4Btn.enabled=!0,this.valueExc4Btn.buttonMode=!0):(this.valueExc4Btn.enabled=!1,this.valueExc4Btn.buttonMode=!1),""!=this.valueExc5Btn.label&&"0"!=this.valueExc5Btn.label?(this.valueExc5Btn.enabled=!0,this.valueExc5Btn.buttonMode=!0):(this.valueExc5Btn.enabled=!1,this.valueExc5Btn.buttonMode=!1),""!=this.valueExc6Btn.label&&"0"!=this.valueExc6Btn.label?(this.valueExc6Btn.enabled=!0,this.valueExc6Btn.buttonMode=!0):(this.valueExc6Btn.enabled=!1,this.valueExc6Btn.buttonMode=!1),""!=this.valueExc7Btn.label&&"0"!=this.valueExc7Btn.label?(this.valueExc7Btn.enabled=!0,this.valueExc7Btn.buttonMode=!0):(this.valueExc7Btn.enabled=!1,this.valueExc7Btn.buttonMode=!1),""!=this.valueExc8Btn.label&&"0"!=this.valueExc8Btn.label?(this.valueExc8Btn.enabled=!0,this.valueExc8Btn.buttonMode=!0):(this.valueExc8Btn.enabled=!1,this.valueExc8Btn.buttonMode=!1))},e.prototype.doHelp=function(){try{s.x.call("help")}catch(t){s.Wb.logError(t,this.moduleId,"workFlowMonitor","doHelp",this.errorLocation)}},e.prototype.handleButtonsStatus=function(){this.summary.summaryGrid.selectedIndex>-1&&this.summary.summaryGrid.selectedItem&&"Y"==this.summary.rcdScenInstance?this.checkUserAccess():this.disableButtons()},e.prototype.openInstDetails=function(t){var e=[];e.push({scenarioId:this.checkColumnsValue("scenarioId"),instanceId:this.checkColumnsValue("id"),status:this.checkColumnsValue("status"),eventStatus:this.checkColumnsValue("eventsLaunchStatus"),firstRaisedDate:this.checkColumnsValue("raisedDatetime"),lastRaisedDate:this.checkColumnsValue("lastRaisedDatetime"),resolvedDate:this.checkColumnsValue("resolvedDatetime"),resolvedUser:this.checkColumnsValue("resolvedByUser"),uniqueIdent:this.checkColumnsValue("uniqueIdentifier"),hostId:this.checkColumnsValue("hostId"),entityId:this.checkColumnsValue("entityId"),ccyCode:this.checkColumnsValue("currencyCode"),acctId:this.checkColumnsValue("accountId"),valueDate:this.checkColumnsValue("valueDate"),amount:this.checkColumnsValue("amount"),sign:this.checkColumnsValue("sign"),mvtId:this.checkColumnsValue("movementId"),matchId:this.checkColumnsValue("matchId"),sweepId:this.checkColumnsValue("sweepId"),payment:this.checkColumnsValue("paymentId"),otherId:this.checkColumnsValue("otherId"),otherIdTypesList:this.optionIdTypes,scenOtherIdType:this.checkColumnsValue("otherIdType"),firstRaisedUser:this.checkColumnsValue("raisedUser"),lastRaisedUser:this.checkColumnsValue("lastRaisedUser"),attributesXml:t,currBox:!0===this.currencyThreshold.selected?"Y":"N",scenarioTitle:this.summary.scenarioTitle,useGeneric:this.summary.useGeneric,facilityId:this.summary.facilityId,facilityName:this.summary.facilityName}),s.x.call("openInstDetails","openInstanceDetails",s.Z.encode64(JSON.stringify(e)))},e.prototype.checkColumnsValue=function(t){var e;return this.summary.summaryGrid.selectedItem&&(e=this.summary.summaryGrid.selectedItem[t]),e?e.content:""},e.prototype.openFacility=function(t,e){var l=t.slickgrid_rowcontent.entity.content,i=t.slickgrid_rowcontent.ccy.content,n=t.slickgrid_rowcontent[e].content,a=this.summary.facilityId,o=this.summary.facilityName,u=this.summary.useGeneric,r=this.summary.scenarioTitle,h=!0===this.currencyThreshold.selected?"Y":"N",d=this.summary.selectedscenario;s.x.call("openFacility",r,u,a,o,d,l,i,h,n,this.ccyCombo.selectedItem.content)},e.prototype.goTo=function(t){var e=this.summary.summaryGrid.selectedItem.hostId.content,l=this.summary.summaryGrid.selectedItem.entityId.content,i=this.summary.summaryGrid.selectedItem.matchId.content,n=this.summary.summaryGrid.selectedItem.currencyCode.content,a=this.summary.summaryGrid.selectedItem.movementId.content,o=this.summary.summaryGrid.selectedItem.sweepId.content,u=this.summary.summaryGrid.selectedItem.otherId.content;s.x.call("goTo",this.summary.facilityId,e,l,i,n,a,o,u)},e.prototype.changeStatus=function(){"All"==this.status.selectedValue||"R"==this.status.selectedValue?this.resolvedOnDate.enabled=!0:this.resolvedOnDate.enabled=!1,this.statusChanged=!0,this.summary.tree.selectedIndex=-1,this.summary.summaryGrid&&(this.summary.summaryGrid.resetFilter(),this.summary.summaryGrid.filteredGridColumns=""),this.updateData("workFlowRefresh"),this.statusChanged=!1},e.prototype.updateStatus=function(t){var e=this;this.summaryData.cbStart=this.startOfComms.bind(this),this.summaryData.cbStop=this.endOfComms.bind(this),this.summaryData.cbResult=function(t){e.requestParams.refreshGridOnly="false",e.loadSummary()},this.summaryData.cbFault=this.inputDataFault.bind(this),this.summaryData.encodeURL=!1,this.requestParams.id=this.summary.summaryGrid.selectedItem.id.content,this.requestParams.oldStatus=this.summary.summaryGrid.selectedItem.status.content,this.requestParams.newStatus=t,this.actionPath="scenarioSummary.do?",this.actionMethod="method=updateScenInstanceStatus",this.summaryData.encodeURL=!1,this.summaryData.url=this.baseURL+this.actionPath+this.actionMethod,this.summaryData.send(this.requestParams)},e.prototype.checkUserAccess=function(){var t=this;this.summary.summaryGrid.selectedIndex>-1&&(this.summaryData.cbStart=this.startOfComms.bind(this),this.summaryData.cbStop=this.endOfComms.bind(this),this.summaryData.cbResult=function(e){t.getUserAccess(e)},this.summaryData.cbFault=this.inputDataFault.bind(this),this.summaryData.encodeURL=!1,this.requestParams.instanceId=this.summary.summaryGrid.selectedItem.id.content,this.requestParams.scenarioId=this.summary.summaryGrid.selectedItem.scenarioId.content,this.requestParams.entityId=this.summary.summaryGrid.selectedItem.entityId.content,this.requestParams.ccyCode=this.summary.summaryGrid.selectedItem.currencyCode.content,this.requestParams.hostId=this.summary.summaryGrid.selectedItem.hostId.content,this.requestParams.fromWorkflow="true",this.actionPath="scenarioSummary.do?",this.actionMethod="method=checkUserInstAccess",this.summaryData.encodeURL=!1,this.summaryData.url=this.baseURL+this.actionPath+this.actionMethod,this.summaryData.send(this.requestParams))},e.prototype.getUserAccess=function(t){"true"==t.scenarioDetails.singletons.hasAccess?this.handleRctRslvButtons():(this.resolveButton.enabled=!1,this.resolveButton.buttonMode=!1,this.reActiveButton.enabled=!1,this.reActiveButton.buttonMode=!1),this.enableButtons()},e.prototype.handleRctRslvButtons=function(){var t=this.summary.summaryGrid.selectedItem&&this.summary.summaryGrid.selectedItem.status?this.summary.summaryGrid.selectedItem.status.content:"";t&&("Resolved"==t?(this.resolveButton.enabled=!1,this.resolveButton.buttonMode=!1,this.reActiveButton.enabled=!0,this.reActiveButton.buttonMode=!0):"Pending"==t||"Active"==t?(this.resolveButton.enabled=!0,this.resolveButton.buttonMode=!0,this.reActiveButton.enabled=!1,this.reActiveButton.buttonMode=!1):(this.resolveButton.enabled=!0,this.resolveButton.buttonMode=!0,this.reActiveButton.enabled=!0,this.reActiveButton.buttonMode=!0))},e.prototype.enableButtons=function(){this.detailsButton.enabled=!0,this.detailsButton.buttonMode=!0,this.summary.facilityId&&"None"!=this.summary.facilityId?(this.goToButton.enabled=!0,this.goToButton.buttonMode=!0):(this.goToButton.enabled=!1,this.goToButton.buttonMode=!1)},e.prototype.disableButtons=function(){this.resolveButton.enabled=!1,this.resolveButton.buttonMode=!1,this.reActiveButton.enabled=!1,this.reActiveButton.buttonMode=!1,this.detailsButton.enabled=!1,this.detailsButton.buttonMode=!1,this.goToButton.enabled=!1,this.goToButton.buttonMode=!1},e.prototype.isValidJSON=function(t){try{return t=JSON.parse(t),!0}catch(e){return!1}},e.prototype.optionsHandler=function(){try{this.win=s.Eb.createPopUp(this,a.a,{title:"Options",lastValue:this.lastApplyThresholdSetting}),this.win.width="400",this.win.height="200",this.win.id="myOptionsPopUp",this.win.enableResize=!1,this.win.showControls=!0,this.win.isModal=!0,this.win.onClose.subscribe(function(){},function(t){console.log(t)}),this.win.display()}catch(t){s.Wb.logError(t,this.moduleId,"Dashboard","optionsHandler",this.errorLocation)}},e.prototype.saveApplyCcyThreshold=function(t){this.lastApplyThresholdSetting=t,this.saveSettings()},e}(s.yb)),b=[{path:"",component:c}],m=(o.l.forChild(b),function(){return function(){}}()),g=l("pMnS"),p=l("RChO"),v=l("t6HQ"),I=l("WFGK"),y=l("5FqG"),f=l("Ip0R"),w=l("gIcY"),x=l("t/Na"),B=l("sE5F"),A=l("OzfB"),S=l("T7CS"),C=l("S7LP"),R=l("6aHO"),L=l("WzUx"),T=l("A7o+"),E=l("zCE2"),D=l("Jg5P"),J=l("3R0m"),M=l("hhbb"),k=l("5rxC"),P=l("Fzqc"),O=l("21Lb"),G=l("hUWP"),N=l("3pJQ"),W=l("V9q+"),_=l("VDKW"),F=l("kXfT"),z=l("BGbe");l.d(e,"WorkFlowMonitorModuleNgFactory",function(){return q}),l.d(e,"RenderType_WorkFlowMonitor",function(){return H}),l.d(e,"View_WorkFlowMonitor_0",function(){return Z}),l.d(e,"View_WorkFlowMonitor_Host_0",function(){return U}),l.d(e,"WorkFlowMonitorNgFactory",function(){return Y});var q=i.Gb(m,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[g.a,p.a,v.a,I.a,y.Cb,y.Pb,y.r,y.rc,y.s,y.Ab,y.Bb,y.Db,y.qd,y.Hb,y.k,y.Ib,y.Nb,y.Ub,y.yb,y.Jb,y.v,y.A,y.e,y.c,y.g,y.d,y.Kb,y.f,y.ec,y.Wb,y.bc,y.ac,y.sc,y.fc,y.lc,y.jc,y.Eb,y.Fb,y.mc,y.Lb,y.nc,y.Mb,y.dc,y.Rb,y.b,y.ic,y.Yb,y.Sb,y.kc,y.y,y.Qb,y.cc,y.hc,y.pc,y.oc,y.xb,y.p,y.q,y.o,y.h,y.j,y.w,y.Zb,y.i,y.m,y.Vb,y.Ob,y.Gb,y.Xb,y.t,y.tc,y.zb,y.n,y.qc,y.a,y.z,y.rd,y.sd,y.x,y.td,y.gc,y.l,y.u,y.ud,y.Tb,Y]],[3,i.n],i.J]),i.Rb(4608,f.m,f.l,[i.F,[2,f.u]]),i.Rb(4608,w.c,w.c,[]),i.Rb(4608,w.p,w.p,[]),i.Rb(4608,x.j,x.p,[f.c,i.O,x.n]),i.Rb(4608,x.q,x.q,[x.j,x.o]),i.Rb(5120,x.a,function(t){return[t,new s.tb]},[x.q]),i.Rb(4608,x.m,x.m,[]),i.Rb(6144,x.k,null,[x.m]),i.Rb(4608,x.i,x.i,[x.k]),i.Rb(6144,x.b,null,[x.i]),i.Rb(4608,x.f,x.l,[x.b,i.B]),i.Rb(4608,x.c,x.c,[x.f]),i.Rb(4608,B.c,B.c,[]),i.Rb(4608,B.g,B.b,[]),i.Rb(5120,B.i,B.j,[]),i.Rb(4608,B.h,B.h,[B.c,B.g,B.i]),i.Rb(4608,B.f,B.a,[]),i.Rb(5120,B.d,B.k,[B.h,B.f]),i.Rb(5120,i.b,function(t,e){return[A.j(t,e)]},[f.c,i.O]),i.Rb(4608,S.a,S.a,[]),i.Rb(4608,C.a,C.a,[]),i.Rb(4608,R.a,R.a,[i.n,i.L,i.B,C.a,i.g]),i.Rb(4608,L.c,L.c,[i.n,i.g,i.B]),i.Rb(4608,L.e,L.e,[L.c]),i.Rb(4608,T.l,T.l,[]),i.Rb(4608,T.h,T.g,[]),i.Rb(4608,T.c,T.f,[]),i.Rb(4608,T.j,T.d,[]),i.Rb(4608,T.b,T.a,[]),i.Rb(4608,T.k,T.k,[T.l,T.h,T.c,T.j,T.b,T.m,T.n]),i.Rb(4608,L.i,L.i,[[2,T.k]]),i.Rb(4608,L.r,L.r,[L.L,[2,T.k],L.i]),i.Rb(4608,L.t,L.t,[]),i.Rb(4608,L.w,L.w,[]),i.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),i.Rb(1073742336,f.b,f.b,[]),i.Rb(1073742336,w.n,w.n,[]),i.Rb(1073742336,w.l,w.l,[]),i.Rb(1073742336,E.a,E.a,[]),i.Rb(1073742336,D.a,D.a,[]),i.Rb(1073742336,w.e,w.e,[]),i.Rb(1073742336,J.a,J.a,[]),i.Rb(1073742336,T.i,T.i,[]),i.Rb(1073742336,L.b,L.b,[]),i.Rb(1073742336,x.e,x.e,[]),i.Rb(1073742336,x.d,x.d,[]),i.Rb(1073742336,B.e,B.e,[]),i.Rb(1073742336,M.b,M.b,[]),i.Rb(1073742336,k.b,k.b,[]),i.Rb(1073742336,A.c,A.c,[]),i.Rb(1073742336,P.a,P.a,[]),i.Rb(1073742336,O.d,O.d,[]),i.Rb(1073742336,G.c,G.c,[]),i.Rb(1073742336,N.a,N.a,[]),i.Rb(1073742336,W.a,W.a,[[2,A.g],i.O]),i.Rb(1073742336,_.b,_.b,[]),i.Rb(1073742336,F.a,F.a,[]),i.Rb(1073742336,z.b,z.b,[]),i.Rb(1073742336,s.Tb,s.Tb,[]),i.Rb(1073742336,m,m,[]),i.Rb(256,x.n,"XSRF-TOKEN",[]),i.Rb(256,x.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,T.m,void 0,[]),i.Rb(256,T.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,o.i,function(){return[[{path:"",component:c}]]},[])])}),V=[[".box-style[_ngcontent-%COMP%]{width:18px;height:18px;text-align:center;border:1px solid #000;font-weight:700;padding-top:1px}.line[_ngcontent-%COMP%]{width:100%;text-align:right;height:1px;margin:5px 0;display:block;clear:both;border-color:#7f7f7f!important;border-top:1px solid #7f7f7f}"]],H=i.Hb({encapsulation:0,styles:V,data:{}});function Z(t){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{lblComboEntity:0}),i.Zb(402653184,3,{selectedEntity:0}),i.Zb(402653184,4,{labelExc3txtcurrLabel:0}),i.Zb(402653184,5,{lblComboCcy:0}),i.Zb(402653184,6,{selectedCcy:0}),i.Zb(402653184,7,{currLabel:0}),i.Zb(402653184,8,{labelExc0txt:0}),i.Zb(402653184,9,{labelExc1txt:0}),i.Zb(402653184,10,{labelExc2txt:0}),i.Zb(402653184,11,{labelExc3txt:0}),i.Zb(402653184,12,{labelExc4txt:0}),i.Zb(402653184,13,{labelExc5txt:0}),i.Zb(402653184,14,{labelExc6txt:0}),i.Zb(402653184,15,{labelExc7txt:0}),i.Zb(402653184,16,{labelExc8txt:0}),i.Zb(402653184,17,{labelExcTotalTxt:0}),i.Zb(402653184,18,{labelIncTotalValue:0}),i.Zb(402653184,19,{labelExcTotalValue:0}),i.Zb(402653184,20,{statusLabel:0}),i.Zb(402653184,21,{resolvedOnLbl:0}),i.Zb(402653184,22,{valueInc0Btn:0}),i.Zb(402653184,23,{valueExc0Btn:0}),i.Zb(402653184,24,{valueInc1Btn:0}),i.Zb(402653184,25,{valueExc1Btn:0}),i.Zb(402653184,26,{valueInc2Btn:0}),i.Zb(402653184,27,{valueExc2Btn:0}),i.Zb(402653184,28,{valueInc3Btn:0}),i.Zb(402653184,29,{valueExc3Btn:0}),i.Zb(402653184,30,{valueInc4Btn:0}),i.Zb(402653184,31,{valueExc4Btn:0}),i.Zb(402653184,32,{valueInc5Btn:0}),i.Zb(402653184,33,{valueExc5Btn:0}),i.Zb(402653184,34,{valueInc6Btn:0}),i.Zb(402653184,35,{valueExc6Btn:0}),i.Zb(402653184,36,{valueInc7Btn:0}),i.Zb(402653184,37,{valueExc7Btn:0}),i.Zb(402653184,38,{valueInc8Btn:0}),i.Zb(402653184,39,{valueExc8Btn:0}),i.Zb(402653184,40,{entityCombo:0}),i.Zb(402653184,41,{ccyCombo:0}),i.Zb(402653184,42,{currencyThreshold:0}),i.Zb(402653184,43,{loadingImage:0}),i.Zb(402653184,44,{outTxt:0}),i.Zb(402653184,45,{excTxt:0}),i.Zb(402653184,46,{mvtTxt:0}),i.Zb(402653184,47,{incTxt:0}),i.Zb(402653184,48,{lostConnectionText:0}),i.Zb(402653184,49,{lastRefTime:0}),i.Zb(402653184,50,{lastRefTimeLabel:0}),i.Zb(402653184,51,{dataBuildingText:0}),i.Zb(402653184,52,{mainGroup:0}),i.Zb(402653184,53,{mainCanvas:0}),i.Zb(402653184,54,{controlContainer:0}),i.Zb(402653184,55,{tabCategoryList:0}),i.Zb(402653184,56,{tabList:0}),i.Zb(402653184,57,{displayContainerPredict:0}),i.Zb(402653184,58,{displayContainerPCM:0}),i.Zb(402653184,59,{resolveButton:0}),i.Zb(402653184,60,{reActiveButton:0}),i.Zb(402653184,61,{goToButton:0}),i.Zb(402653184,62,{detailsButton:0}),i.Zb(402653184,63,{refreshButton:0}),i.Zb(402653184,64,{closeButton:0}),i.Zb(402653184,65,{optionsButton:0}),i.Zb(402653184,66,{rateButton:0}),i.Zb(402653184,67,{helpIcon:0}),i.Zb(402653184,68,{logonBtn:0}),i.Zb(402653184,69,{pdf:0}),i.Zb(402653184,70,{treeDivider:0}),i.Zb(402653184,71,{status:0}),i.Zb(402653184,72,{all:0}),i.Zb(402653184,73,{active:0}),i.Zb(402653184,74,{resolved:0}),i.Zb(402653184,75,{pending:0}),i.Zb(402653184,76,{overdue:0}),i.Zb(402653184,77,{allOpen:0}),i.Zb(402653184,78,{resolvedOnDate:0}),(t()(),i.Jb(78,0,null,null,394,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,l){var i=!0,n=t.component;"creationComplete"===e&&(i=!1!==n.onLoad()&&i);return i},y.ad,y.hb)),i.Ib(79,4440064,null,0,s.yb,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(80,0,null,0,392,"VBox",[["height","100%"],["paddingBottom","0"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["verticalGap","1"],["width","100%"]],null,null,null,y.od,y.vb)),i.Ib(81,4440064,null,0,s.ec,[i.r,s.i,i.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),i.Jb(82,0,null,0,93,"SwtCanvas",[["height","80"],["minWidth","1100"],["width","100%"]],null,null,null,y.Nc,y.U)),i.Ib(83,4440064,[["filterContainer",4]],0,s.db,[i.r,s.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),i.Jb(84,0,null,0,91,"HBox",[["height","100%"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(85,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(86,0,null,0,31,"HBox",[["height","100%"],["minWidth","300"],["paddingBottom","5"],["paddingTop","5"],["width","35%"]],null,null,null,y.Dc,y.K)),i.Ib(87,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"]},null),(t()(),i.Jb(88,0,null,0,29,"Grid",[["height","100%"],["width","100%"]],null,null,null,y.Cc,y.H)),i.Ib(89,4440064,null,0,s.z,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(90,0,null,0,13,"GridRow",[["height","100%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(91,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(92,0,null,0,3,"GridItem",[["height","100%"],["width","130"]],null,null,null,y.Ac,y.I)),i.Ib(93,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(94,0,null,0,1,"SwtLabel",[["paddingLeft","5"],["styleName","labelBold"],["text","Entity"]],null,null,null,y.Yc,y.fb)),i.Ib(95,4440064,[[2,4],["lblComboEntity",4]],0,s.vb,[i.r,s.i],{styleName:[0,"styleName"],paddingLeft:[1,"paddingLeft"],text:[2,"text"]},null),(t()(),i.Jb(96,0,null,0,3,"GridItem",[["height","100%"],["width","135"]],null,null,null,y.Ac,y.I)),i.Ib(97,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(98,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"],["toolTip","Entity"],["width","135"]],null,[[null,"change"],[null,"focusOut"],["window","mousewheel"]],function(t,e,l){var n=!0,a=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,99).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==a.changeCombo(l)&&n);"focusOut"===e&&(n=!1!==a.comboFocusOutHandler(l)&&n);return n},y.Pc,y.W)),i.Ib(99,4440064,[[40,4],["entityCombo",4]],0,s.gb,[i.r,s.i],{dataLabel:[0,"dataLabel"],toolTip:[1,"toolTip"],width:[2,"width"],id:[3,"id"]},{change_:"change"}),(t()(),i.Jb(100,0,null,0,3,"GridItem",[["height","100%"],["width","43%"]],null,null,null,y.Ac,y.I)),i.Ib(101,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(102,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingLeft","10"],["styleName","labelLeft"]],null,null,null,y.Yc,y.fb)),i.Ib(103,4440064,[[3,4],["selectedEntity",4]],0,s.vb,[i.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],paddingLeft:[2,"paddingLeft"],fontWeight:[3,"fontWeight"]},null),(t()(),i.Jb(104,0,null,0,13,"GridRow",[["height","100%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(105,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(106,0,null,0,3,"GridItem",[["height","100%"],["width","130"]],null,null,null,y.Ac,y.I)),i.Ib(107,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(108,0,null,0,1,"SwtLabel",[["paddingLeft","5"],["styleName","labelBold"],["text","Currency Group"]],null,null,null,y.Yc,y.fb)),i.Ib(109,4440064,[[5,4],["lblComboCcy",4]],0,s.vb,[i.r,s.i],{styleName:[0,"styleName"],paddingLeft:[1,"paddingLeft"],text:[2,"text"]},null),(t()(),i.Jb(110,0,null,0,3,"GridItem",[["height","100%"],["width","135"]],null,null,null,y.Ac,y.I)),i.Ib(111,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(112,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","ccyCombo"],["toolTip","Select currency code"],["width","135"]],null,[[null,"change"],[null,"focusout"],["window","mousewheel"]],function(t,e,l){var n=!0,a=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,113).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==a.changeCombo(l)&&n);"focusout"===e&&(n=!1!==a.comboFocusOutHandler(l)&&n);return n},y.Pc,y.W)),i.Ib(113,4440064,[[41,4],["ccyCombo",4]],0,s.gb,[i.r,s.i],{dataLabel:[0,"dataLabel"],toolTip:[1,"toolTip"],width:[2,"width"],id:[3,"id"]},{focusout_:"focusout",change_:"change"}),(t()(),i.Jb(114,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","43%"]],null,null,null,y.Ac,y.I)),i.Ib(115,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(116,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCcy"],["styleName","labelRight"]],null,null,null,y.Yc,y.fb)),i.Ib(117,4440064,[[6,4],["selectedCcy",4]],0,s.vb,[i.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(118,0,null,0,57,"HBox",[["height","100%"],["width","65%"]],null,null,null,y.Dc,y.K)),i.Ib(119,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(120,0,null,0,55,"Grid",[["height","100%"],["verticalGap","2"],["width","100%"]],null,null,null,y.Cc,y.H)),i.Ib(121,4440064,null,0,s.z,[i.r,s.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(122,0,null,0,38,"GridRow",[["height","72%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(123,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(124,0,null,0,36,"HBox",[["horizontalAlign","right"],["paddingRight","7"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(125,4440064,null,0,s.C,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingRight:[2,"paddingRight"]},null),(t()(),i.Jb(126,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),i.Ib(127,4440064,null,0,s.A,[i.r,s.i],null,null),(t()(),i.Jb(128,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","statusLabel"],["width","60"]],null,null,null,y.Yc,y.fb)),i.Ib(129,4440064,[[20,4],["statusLabel",4]],0,s.vb,[i.r,s.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(130,0,null,0,22,"GridItem",[],null,null,null,y.Ac,y.I)),i.Ib(131,4440064,null,0,s.A,[i.r,s.i],null,null),(t()(),i.Jb(132,0,null,0,20,"SwtRadioButtonGroup",[["align","horizontal"],["id","status"],["width","100%"]],null,[[null,"change"]],function(t,e,l){var i=!0,n=t.component;"change"===e&&(i=!1!==n.changeStatus()&&i);return i},y.ed,y.lb)),i.Ib(133,4440064,[[71,4],["status",4]],1,s.Hb,[x.c,i.r,s.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),i.Zb(603979776,79,{radioItems:1}),(t()(),i.Jb(135,0,null,0,17,"VBox",[["height","100%"],["width","100%"]],null,null,null,y.od,y.vb)),i.Ib(136,4440064,null,0,s.ec,[i.r,s.i,i.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(137,0,null,0,7,"HBox",[["height","50%"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(138,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(139,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","all"],["value","All"],["width","75"]],null,null,null,y.fd,y.mb)),i.Ib(140,4440064,[[72,4],["all",4]],0,s.Ib,[i.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(141,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","allOpen"],["selected","true"],["value",""],["width","75"]],null,null,null,y.fd,y.mb)),i.Ib(142,4440064,[[77,4],["allOpen",4]],0,s.Ib,[i.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),i.Jb(143,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","active"],["value","A"],["width","75"]],null,null,null,y.fd,y.mb)),i.Ib(144,4440064,[[73,4],["active",4]],0,s.Ib,[i.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(145,0,null,0,7,"HBox",[["height","50%"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(146,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(147,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","pending"],["value","P"],["width","75"]],null,null,null,y.fd,y.mb)),i.Ib(148,4440064,[[75,4],["pending",4]],0,s.Ib,[i.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(149,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","overdue"],["value","O"],["width","75"]],null,null,null,y.fd,y.mb)),i.Ib(150,4440064,[[76,4],["overdue",4]],0,s.Ib,[i.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(151,0,null,0,1,"SwtRadioItem",[["groupName","status"],["id","resolved"],["value","R"],["width","75"]],null,null,null,y.fd,y.mb)),i.Ib(152,4440064,[[74,4],["resolved",4]],0,s.Ib,[i.r,s.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(153,0,null,0,3,"GridItem",[["paddingTop","16"]],null,null,null,y.Ac,y.I)),i.Ib(154,4440064,null,0,s.A,[i.r,s.i],{paddingTop:[0,"paddingTop"]},null),(t()(),i.Jb(155,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","resolvedOnLbl"],["width","85"]],null,null,null,y.Yc,y.fb)),i.Ib(156,4440064,[[21,4],["resolvedOnLbl",4]],0,s.vb,[i.r,s.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(157,0,null,0,3,"GridItem",[["paddingTop","16"]],null,null,null,y.Ac,y.I)),i.Ib(158,4440064,null,0,s.A,[i.r,s.i],{paddingTop:[0,"paddingTop"]},null),(t()(),i.Jb(159,0,null,0,1,"SwtDateField",[["enabled","false"],["id","resolvedOnDate"],["width","70"]],null,[[null,"change"]],function(t,e,l){var i=!0,n=t.component;"change"===e&&(i=!1!==n.updateData("tree")&&i);return i},y.Tc,y.ab)),i.Ib(160,4308992,[[78,4],["resolvedOnDate",4]],0,s.lb,[i.r,s.i,i.T],{id:[0,"id"],enabled:[1,"enabled"],width:[2,"width"]},{changeEventOutPut:"change"}),(t()(),i.Jb(161,0,null,0,14,"GridRow",[["height","28%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(162,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(163,0,null,0,4,"GridItem",[["width","250"]],null,null,null,y.Ac,y.I)),i.Ib(164,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"]},null),(t()(),i.Jb(165,0,null,0,2,"SwtTabNavigator",[["borderBottom","false"],["height","100%"],["id","tabCategoryList"],["width","100%"]],null,[[null,"onChange"]],function(t,e,l){var i=!0,n=t.component;"onChange"===e&&(i=!1!==n.updateData("workFlowRefresh")&&i);return i},y.id,y.pb)),i.Ib(166,4440064,[[55,4],["tabCategoryList",4]],1,s.Ob,[i.r,s.i,i.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],borderBottom:[3,"borderBottom"]},{onChange_:"onChange"}),i.Zb(603979776,80,{tabChildren:1}),(t()(),i.Jb(168,0,null,0,7,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(169,4440064,null,0,s.C,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),i.Jb(170,0,null,0,5,"GridItem",[["horizontalAlign","right"],["paddingBottom","5"],["verticalAlign","bottom"]],null,null,null,y.Ac,y.I)),i.Ib(171,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],verticalAlign:[1,"verticalAlign"],paddingBottom:[2,"paddingBottom"]},null),(t()(),i.Jb(172,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["text","Apply Currency Threshold"],["width","170"]],null,null,null,y.Yc,y.fb)),i.Ib(173,4440064,[[4,4],["labelExc3txtcurrLabel",4]],0,s.vb,[i.r,s.i],{width:[0,"width"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(174,0,null,0,1,"SwtCheckBox",[["id","currencyThreshold"],["toolTip","Apply Currency Threshold"]],null,[[null,"change"]],function(t,e,l){var i=!0,n=t.component;"change"===e&&(i=!1!==n.changeCombo(l)&&i);return i},y.Oc,y.V)),i.Ib(175,4440064,[[42,4],["currencyThreshold",4]],0,s.eb,[i.r,s.i],{id:[0,"id"],toolTip:[1,"toolTip"]},{change_:"change"}),(t()(),i.Jb(176,0,null,0,266,"SwtCanvas",[["border","false"],["height","80%"],["minHeight","450"],["minWidth","1100"],["style","border: 1px solid gray"],["width","100%"]],null,null,null,y.Nc,y.U)),i.Ib(177,4440064,[[53,4],["mainCanvas",4]],0,s.db,[i.r,s.i],{width:[0,"width"],height:[1,"height"],minHeight:[2,"minHeight"],minWidth:[3,"minWidth"],border:[4,"border"]},null),(t()(),i.Jb(178,0,null,0,264,"HBox",[["height","100%"],["id","mainHGroup"],["paddingBottom","5"],["paddingTop","5"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(179,4440064,null,0,s.C,[i.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"]},null),(t()(),i.Jb(180,0,null,0,262,"HDividedBox",[["dividersAnimation","W"],["extendedDividedBox","true"],["height","100%"],["id","treeDivider"],["liveDrag","true"],["minHeight","5"],["styleName","ExtendedDivider"],["width","100%"]],null,null,null,y.Ec,y.L)),i.Ib(181,4440064,[[70,4],["treeDivider",4]],0,s.D,[i.r,s.i,i.B,i.n],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],minHeight:[4,"minHeight"],dividersAnimation:[5,"dividersAnimation"],extendedDividedBox:[6,"extendedDividedBox"],liveDrag:[7,"liveDrag"]},null),(t()(),i.Jb(182,0,null,0,242,"VBox",[["class","left"],["height","100%"],["id","vbox"],["width","30%"]],null,null,null,y.od,y.vb)),i.Ib(183,4440064,[["vbox",4]],0,s.ec,[i.r,s.i,i.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(184,0,null,0,221,"Grid",[["height","89%"],["paddingLeft","5"],["verticalGap","0"],["width","100%"]],null,null,null,y.Cc,y.H)),i.Ib(185,4440064,null,0,s.z,[i.r,s.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),i.Jb(186,0,null,0,6,"GridRow",[["height","5%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(187,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(188,0,null,0,4,"GridItem",[["height","100%"],["width","100%"]],null,null,null,y.Ac,y.I)),i.Ib(189,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(190,0,null,0,2,"SwtTabNavigator",[["borderBottom","false"],["height","100%"],["id","tabList"],["width","100%"]],null,[[null,"onChange"]],function(t,e,l){var i=!0,n=t.component;"onChange"===e&&(i=!1!==n.updateData("workFlowRefresh")&&i);return i},y.id,y.pb)),i.Ib(191,4440064,[[56,4],["tabList",4]],1,s.Ob,[i.r,s.i,i.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],borderBottom:[3,"borderBottom"]},{onChange_:"onChange"}),i.Zb(603979776,81,{tabChildren:1}),(t()(),i.Jb(193,0,null,0,212,"GridRow",[["height","95%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(194,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(195,0,null,0,210,"GridItem",[["height","100%"],["width","100%"]],null,null,null,y.Ac,y.I)),i.Ib(196,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(197,0,null,0,208,"SwtCanvas",[["height","100%"],["width","100%"]],null,null,null,y.Nc,y.U)),i.Ib(198,4440064,null,0,s.db,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(199,0,null,0,206,"Grid",[["height","100%"],["id","flowGrid"],["verticalGap","0"],["width","100%"]],null,null,null,y.Cc,y.H)),i.Ib(200,4440064,null,0,s.z,[i.r,s.i],{id:[0,"id"],verticalGap:[1,"verticalGap"],width:[2,"width"],height:[3,"height"]},null),(t()(),i.Jb(201,0,null,0,204,"GridRow",[["height","100%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(202,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(203,0,null,0,202,"GridItem",[["height","100%"],["width","100%"]],null,null,null,y.Ac,y.I)),i.Ib(204,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(205,0,null,0,200,"HBox",[["height","100%"],["paddingLeft","3"],["paddingRight","10"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(206,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"],paddingRight:[3,"paddingRight"]},null),(t()(),i.Jb(207,0,null,0,198,"VBox",[["height","100%"],["width","100%"]],null,null,null,y.od,y.vb)),i.Ib(208,4440064,null,0,s.ec,[i.r,s.i,i.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(209,0,null,0,13,"HBox",[["height","40"],["horizontalAlign","right"],["id","titleGroup"],["paddingRight","3"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(210,4440064,null,0,s.C,[i.r,s.i],{id:[0,"id"],horizontalAlign:[1,"horizontalAlign"],width:[2,"width"],height:[3,"height"],paddingRight:[4,"paddingRight"]},null),(t()(),i.Jb(211,0,null,0,5,"VBox",[["height","100%"],["id","includedVbox"],["paddingLeft","{gridIncTotal.x+gridIncTotal.width-90}"],["verticalGap","0"]],null,null,null,y.od,y.vb)),i.Ib(212,4440064,null,0,s.ec,[i.r,s.i,i.T],{id:[0,"id"],verticalGap:[1,"verticalGap"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),i.Jb(213,0,null,0,1,"SwtText",[["fontWeight","bold"],["paddingTop","5"],["text","Included"],["textAlign","right"],["width","{mvtTxt.width}"]],null,null,null,y.ld,y.qb)),i.Ib(214,4440064,[[47,4],["incTxt",4]],0,s.Pb,[i.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"],text:[3,"text"],fontWeight:[4,"fontWeight"]},null),(t()(),i.Jb(215,0,null,0,1,"SwtText",[["fontWeight","bold"],["id","mvtTxt"],["paddingTop","5"],["text","Movements"],["textAlign","left"]],null,null,null,y.ld,y.qb)),i.Ib(216,4440064,[[46,4],["mvtTxt",4]],0,s.Pb,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],paddingTop:[2,"paddingTop"],text:[3,"text"],fontWeight:[4,"fontWeight"]},null),(t()(),i.Jb(217,0,null,0,5,"VBox",[["height","100%"],["id","excludedVBox"],["paddingLeft","5"],["verticalGap","0"]],null,null,null,y.od,y.vb)),i.Ib(218,4440064,null,0,s.ec,[i.r,s.i,i.T],{id:[0,"id"],verticalGap:[1,"verticalGap"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),i.Jb(219,0,null,0,1,"SwtText",[["fontWeight","bold"],["paddingTop","5"],["text","Excluded"],["textAlign","right"],["width","{outTxt.width}"]],null,null,null,y.ld,y.qb)),i.Ib(220,4440064,[[45,4],["excTxt",4]],0,s.Pb,[i.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"],text:[3,"text"],fontWeight:[4,"fontWeight"]},null),(t()(),i.Jb(221,0,null,0,1,"SwtText",[["fontWeight","bold"],["paddingTop","5"],["text","Outstandings"],["textAlign","right"]],null,null,null,y.ld,y.qb)),i.Ib(222,4440064,[[44,4],["outTxt",4]],0,s.Pb,[i.r,s.i],{textAlign:[0,"textAlign"],paddingTop:[1,"paddingTop"],text:[2,"text"],fontWeight:[3,"fontWeight"]},null),(t()(),i.Jb(223,0,null,0,0,"hr",[["class","line"]],null,null,null,null,null)),(t()(),i.Jb(224,0,null,0,181,"HBox",[["height","90%"],["id","linkGroup"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(225,4440064,null,0,s.C,[i.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(226,0,null,0,179,"Grid",[["height","100%"],["horizontalGap","3"],["verticalGap","4%"],["width","100%"]],null,null,null,y.Cc,y.H)),i.Ib(227,4440064,null,0,s.z,[i.r,s.i],{horizontalGap:[0,"horizontalGap"],verticalGap:[1,"verticalGap"],width:[2,"width"],height:[3,"height"]},null),(t()(),i.Jb(228,0,null,0,17,"GridRow",[["height","6%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(229,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(230,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["paddingTop","1"]],null,null,null,y.Ac,y.I)),i.Ib(231,4440064,null,0,s.A,[i.r,s.i],{height:[0,"height"],paddingTop:[1,"paddingTop"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(232,0,null,0,1,"div",[["class","box-style"],["style","background-color: #ef7651"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["1"])),(t()(),i.Jb(234,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","40%"]],null,null,null,y.Ac,y.I)),i.Ib(235,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(236,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),i.Ib(237,4440064,[[8,4],["labelExc0txt",4]],0,s.vb,[i.r,s.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(238,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(239,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(240,0,null,0,1,"LinkButton",[["enabled","{(entityCombo.id=='All'||valueInc0Btn.label=='')?false:true}"],["id","valueInc0Btn"],["textAlign","right"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.incMovement(n.posLvlData[0].num)&&i);return i},y.Ic,y.P)),i.Ib(241,4440064,[[22,4],["valueInc0Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(242,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(243,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(244,0,null,0,1,"LinkButton",[["buttonMode","{valueExc0Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueExc0Btn.label=='')?false:true}"],["id","valueExc0Btn"],["textAlign","right"],["width","90"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.excOutStandings(1,n.posLvlData[0].excluded)&&i);return i},y.Ic,y.P)),i.Ib(245,4440064,[[23,4],["valueExc0Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(246,0,null,0,17,"GridRow",[["height","6%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(247,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(248,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"]],null,null,null,y.Ac,y.I)),i.Ib(249,4440064,null,0,s.A,[i.r,s.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(250,0,null,0,1,"div",[["class","box-style"],["style","background-color: #E9C836"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["2"])),(t()(),i.Jb(252,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","40%"]],null,null,null,y.Ac,y.I)),i.Ib(253,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(254,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","labelExc1txt"]],null,null,null,y.Yc,y.fb)),i.Ib(255,4440064,[[9,4],["labelExc1txt",4]],0,s.vb,[i.r,s.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(256,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(257,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(258,0,null,0,1,"LinkButton",[["buttonMode","{valueInc1Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueInc1Btn.label=='')?false:true}"],["id","valueInc1Btn"],["textAlign","right"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.incMovement(n.posLvlData[1].num)&&i);return i},y.Ic,y.P)),i.Ib(259,4440064,[[24,4],["valueInc1Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(260,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(261,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(262,0,null,0,1,"LinkButton",[["buttonMode","{valueExc1Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueExc1Btn.label=='')?false:true}"],["id","valueExc1Btn"],["textAlign","right"],["width","90"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.excOutStandings(2,n.posLvlData[1].excluded)&&i);return i},y.Ic,y.P)),i.Ib(263,4440064,[[25,4],["valueExc1Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(264,0,null,0,17,"GridRow",[["height","6%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(265,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(266,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"]],null,null,null,y.Ac,y.I)),i.Ib(267,4440064,null,0,s.A,[i.r,s.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(268,0,null,0,1,"div",[["class","box-style"],["style","background-color: #6FB35F"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["3"])),(t()(),i.Jb(270,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","40%"]],null,null,null,y.Ac,y.I)),i.Ib(271,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(272,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","labelExc2txt"]],null,null,null,y.Yc,y.fb)),i.Ib(273,4440064,[[10,4],["labelExc2txt",4]],0,s.vb,[i.r,s.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(274,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(275,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(276,0,null,0,1,"LinkButton",[["buttonMode","{valueInc2Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueInc2Btn.label=='')?false:true}"],["id","valueInc2Btn"],["textAlign","right"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.incMovement(n.posLvlData[2].num)&&i);return i},y.Ic,y.P)),i.Ib(277,4440064,[[26,4],["valueInc2Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(278,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(279,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(280,0,null,0,1,"LinkButton",[["buttonMode","{valueExc2Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueExc2Btn.label=='')?false:true}"],["id","valueExc2Btn"],["textAlign","right"],["width","90"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.excOutStandings(3,n.posLvlData[2].excluded)&&i);return i},y.Ic,y.P)),i.Ib(281,4440064,[[27,4],["valueExc2Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(282,0,null,0,17,"GridRow",[["height","6%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(283,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(284,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"]],null,null,null,y.Ac,y.I)),i.Ib(285,4440064,null,0,s.A,[i.r,s.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(286,0,null,0,1,"div",[["class","box-style"],["style","background-color: #A1AECF"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["4"])),(t()(),i.Jb(288,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","40%"]],null,null,null,y.Ac,y.I)),i.Ib(289,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(290,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),i.Ib(291,4440064,[[11,4],["labelExc3txt",4]],0,s.vb,[i.r,s.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(292,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(293,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(294,0,null,0,1,"LinkButton",[["buttonMode","{valueInc3Btn.enabled}"],["enabled","{(entityCombo.id=='All'|| valueInc3Btn.label=='')?false:true}"],["id","valueInc3Btn"],["textAlign","right"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.incMovement(n.posLvlData[3].num)&&i);return i},y.Ic,y.P)),i.Ib(295,4440064,[[28,4],["valueInc3Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(296,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(297,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(298,0,null,0,1,"LinkButton",[["buttonMode","{valueExc3Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueExc3Btn.label=='')?false:true}"],["id","valueExc3Btn"],["textAlign","right"],["width","90"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.excOutStandings(4,n.posLvlData[3].excluded)&&i);return i},y.Ic,y.P)),i.Ib(299,4440064,[[29,4],["valueExc3Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(300,0,null,0,17,"GridRow",[["height","6%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(301,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(302,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"]],null,null,null,y.Ac,y.I)),i.Ib(303,4440064,null,0,s.A,[i.r,s.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(304,0,null,0,1,"div",[["class","box-style"],["style","background-color: #FF00FF"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["5"])),(t()(),i.Jb(306,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","40%"]],null,null,null,y.Ac,y.I)),i.Ib(307,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(308,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),i.Ib(309,4440064,[[12,4],["labelExc4txt",4]],0,s.vb,[i.r,s.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(310,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(311,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(312,0,null,0,1,"LinkButton",[["buttonMode","{valueInc4Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueInc4Btn.label=='')?false:true}"],["id","valueInc4Btn"],["textAlign","right"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.incMovement(n.posLvlData[4].num)&&i);return i},y.Ic,y.P)),i.Ib(313,4440064,[[30,4],["valueInc4Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(314,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(315,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(316,0,null,0,1,"LinkButton",[["buttonMode","{valueExc4Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueExc4Btn.label=='')?false:true}"],["id","valueExc4Btn"],["textAlign","right"],["width","90"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.excOutStandings(5,n.posLvlData[4].excluded)&&i);return i},y.Ic,y.P)),i.Ib(317,4440064,[[31,4],["valueExc4Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(318,0,null,0,17,"GridRow",[["height","6%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(319,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(320,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"]],null,null,null,y.Ac,y.I)),i.Ib(321,4440064,null,0,s.A,[i.r,s.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(322,0,null,0,1,"div",[["class","box-style"],["style","background-color: #00FFFF"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["6"])),(t()(),i.Jb(324,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","40%"]],null,null,null,y.Ac,y.I)),i.Ib(325,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(326,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),i.Ib(327,4440064,[[13,4],["labelExc5txt",4]],0,s.vb,[i.r,s.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(328,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(329,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(330,0,null,0,1,"LinkButton",[["buttonMode","{valueInc5Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueInc5Btn.label=='')?false:true}"],["textAlign","right"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.incMovement(n.posLvlData[5].num)&&i);return i},y.Ic,y.P)),i.Ib(331,4440064,[[32,4],["valueInc5Btn",4]],0,s.P,[i.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(332,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(333,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(334,0,null,0,1,"LinkButton",[["buttonMode","{valueExc5Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueExc5Btn.label=='')?false:true}"],["textAlign","right"],["width","90"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.excOutStandings(6,n.posLvlData[5].excluded)&&i);return i},y.Ic,y.P)),i.Ib(335,4440064,[[33,4],["valueExc5Btn",4]],0,s.P,[i.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(336,0,null,0,17,"GridRow",[["height","6%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(337,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(338,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"]],null,null,null,y.Ac,y.I)),i.Ib(339,4440064,null,0,s.A,[i.r,s.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(340,0,null,0,1,"div",[["class","box-style"],["style","background-color: #999966"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["7"])),(t()(),i.Jb(342,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","40%"]],null,null,null,y.Ac,y.I)),i.Ib(343,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(344,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),i.Ib(345,4440064,[[14,4],["labelExc6txt",4]],0,s.vb,[i.r,s.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(346,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(347,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(348,0,null,0,1,"LinkButton",[["buttonMode","{valueInc6Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueInc6Btn.label=='')?false:true}"],["textAlign","right"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.incMovement(n.posLvlData[6].num)&&i);return i},y.Ic,y.P)),i.Ib(349,4440064,[[34,4],["valueInc6Btn",4]],0,s.P,[i.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(350,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(351,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(352,0,null,0,1,"LinkButton",[["buttonMode","{valueExc6Btn.enabled}"],["enabled","{(entityCombo.id =='All'||valueExc6Btn.label=='') ? false:true}"],["textAlign","right"],["width","90"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.excOutStandings(7,n.posLvlData[6].excluded)&&i);return i},y.Ic,y.P)),i.Ib(353,4440064,[[35,4],["valueExc6Btn",4]],0,s.P,[i.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(354,0,null,0,17,"GridRow",[["height","6%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(355,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(356,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"]],null,null,null,y.Ac,y.I)),i.Ib(357,4440064,null,0,s.A,[i.r,s.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(358,0,null,0,1,"div",[["class","box-style"],["style","background-color: #9966FF"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["8"])),(t()(),i.Jb(360,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","40%"]],null,null,null,y.Ac,y.I)),i.Ib(361,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(362,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),i.Ib(363,4440064,[[15,4],["labelExc7txt",4]],0,s.vb,[i.r,s.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(364,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(365,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(366,0,null,0,1,"LinkButton",[["buttonMode","{valueInc7Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueInc7Btn.label=='')?false:true}"],["textAlign","right"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.incMovement(n.posLvlData[7].num)&&i);return i},y.Ic,y.P)),i.Ib(367,4440064,[[36,4],["valueInc7Btn",4]],0,s.P,[i.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(368,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(369,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(370,0,null,0,1,"LinkButton",[["buttonMode","{valueExc7Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueExc7Btn.label=='')?false:true}"],["textAlign","right"],["width","90"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.excOutStandings(8,n.posLvlData[7].excluded)&&i);return i},y.Ic,y.P)),i.Ib(371,4440064,[[37,4],["valueExc7Btn",4]],0,s.P,[i.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(372,0,null,0,17,"GridRow",[["height","6%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(373,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(374,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"]],null,null,null,y.Ac,y.I)),i.Ib(375,4440064,null,0,s.A,[i.r,s.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(376,0,null,0,1,"div",[["class","box-style"],["style","background-color: #ffff80"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["9"])),(t()(),i.Jb(378,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","10"],["width","40%"]],null,null,null,y.Ac,y.I)),i.Ib(379,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(380,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),i.Ib(381,4440064,[[16,4],["labelExc8txt",4]],0,s.vb,[i.r,s.i],{fontWeight:[0,"fontWeight"]},null),(t()(),i.Jb(382,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(383,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(384,0,null,0,1,"LinkButton",[["buttonMode","{valueInc8Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueInc8Btn.label=='')?false:true}"],["textAlign","right"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.incMovement(n.posLvlData[8].num)&&i);return i},y.Ic,y.P)),i.Ib(385,4440064,[[38,4],["valueInc8Btn",4]],0,s.P,[i.r,s.i],{textAlign:[0,"textAlign"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(386,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"]],null,null,null,y.Ac,y.I)),i.Ib(387,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],height:[1,"height"]},null),(t()(),i.Jb(388,0,null,0,1,"LinkButton",[["buttonMode","{valueExc8Btn.enabled}"],["enabled","{(entityCombo.id=='All'||valueExc8Btn.label=='')?false:true}"],["id","valueExc8Btn"],["textAlign","right"],["width","90"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.excOutStandings(9,n.posLvlData[8].excluded)&&i);return i},y.Ic,y.P)),i.Ib(389,4440064,[[39,4],["valueExc8Btn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(390,0,null,0,15,"GridRow",[["height","10%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(391,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(392,0,null,0,1,"GridItem",[["paddingLeft","10"]],null,null,null,y.Ac,y.I)),i.Ib(393,4440064,null,0,s.A,[i.r,s.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(394,0,null,0,3,"GridItem",[["height","100%"],["paddingLeft","26"],["width","65%"]],null,null,null,y.Ac,y.I)),i.Ib(395,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(396,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["text","Total"]],null,null,null,y.Yc,y.fb)),i.Ib(397,4440064,[[17,4],["labelExcTotalTxt",4]],0,s.vb,[i.r,s.i],{text:[0,"text"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(398,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"],["id","gridIncTotal"]],null,null,null,y.Ac,y.I)),i.Ib(399,4440064,[["gridIncTotal",4]],0,s.A,[i.r,s.i],{id:[0,"id"],horizontalAlign:[1,"horizontalAlign"],height:[2,"height"]},null),(t()(),i.Jb(400,0,null,0,1,"SwtLabel",[["fontSize","11"],["fontWeight","bold"],["styleName","labelRight"],["text","0"],["textAlign","right"]],null,null,null,y.Yc,y.fb)),i.Ib(401,4440064,[[18,4],["labelIncTotalValue",4]],0,s.vb,[i.r,s.i],{textAlign:[0,"textAlign"],styleName:[1,"styleName"],text:[2,"text"],fontSize:[3,"fontSize"],fontWeight:[4,"fontWeight"]},null),(t()(),i.Jb(402,0,null,0,3,"GridItem",[["height","100%"],["horizontalAlign","right"],["id","gridTotal"]],null,null,null,y.Ac,y.I)),i.Ib(403,4440064,[["gridTotal",4]],0,s.A,[i.r,s.i],{id:[0,"id"],horizontalAlign:[1,"horizontalAlign"],height:[2,"height"]},null),(t()(),i.Jb(404,0,null,0,1,"SwtLabel",[["fontSize","11"],["fontWeight","bold"],["styleName","labelRight"],["text","0"],["textAlign","right"],["width","88"]],null,null,null,y.Yc,y.fb)),i.Ib(405,4440064,[[19,4],["labelExcTotalValue",4]],0,s.vb,[i.r,s.i],{textAlign:[0,"textAlign"],styleName:[1,"styleName"],width:[2,"width"],text:[3,"text"],fontSize:[4,"fontSize"],fontWeight:[5,"fontWeight"]},null),(t()(),i.Jb(406,0,null,0,18,"VBox",[["height","10%"],["minHeight","40"],["paddingLeft","5"],["width","100%"]],null,null,null,y.od,y.vb)),i.Ib(407,4440064,null,0,s.ec,[i.r,s.i,i.T],{width:[0,"width"],height:[1,"height"],minHeight:[2,"minHeight"],paddingLeft:[3,"paddingLeft"]},null),(t()(),i.Jb(408,0,null,0,16,"fieldset",[["style","height:100%"]],null,null,null,null,null)),(t()(),i.Jb(409,0,null,null,1,"legend",[["style","background-color: transparent"]],null,null,null,null,null)),(t()(),i.bc(-1,null,["System"])),(t()(),i.Jb(411,0,null,null,13,"Grid",[["height","80%"],["verticalGap","0"],["width","100%"]],null,null,null,y.Cc,y.H)),i.Ib(412,4440064,null,0,s.z,[i.r,s.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(413,0,null,0,11,"GridRow",[["height","100%"],["width","100%"]],null,null,null,y.Bc,y.J)),i.Ib(414,4440064,null,0,s.B,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(415,0,null,0,1,"GridItem",[["width","11%"]],null,null,null,y.Ac,y.I)),i.Ib(416,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"]},null),(t()(),i.Jb(417,0,null,0,3,"GridItem",[["paddingTop","5"],["width","34%"]],null,null,null,y.Ac,y.I)),i.Ib(418,4440064,null,0,s.A,[i.r,s.i],{width:[0,"width"],paddingTop:[1,"paddingTop"]},null),(t()(),i.Jb(419,0,null,0,1,"SwtLabel",[["fontSize","11"],["fontWeight","normal"],["text","Logged on"]],null,null,null,y.Yc,y.fb)),i.Ib(420,4440064,null,0,s.vb,[i.r,s.i],{text:[0,"text"],fontSize:[1,"fontSize"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(421,0,null,0,3,"GridItem",[["horizontalAlign","right"],["paddingLeft","10"],["paddingTop","5"],["width","10%"]],null,null,null,y.Ac,y.I)),i.Ib(422,4440064,null,0,s.A,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"]},null),(t()(),i.Jb(423,0,null,0,1,"LinkButton",[["buttonMode","{logonBtn.enabled}"],["id","logonBtn"],["textAlign","right"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.loggedOnClick()&&i);return i},y.Ic,y.P)),i.Ib(424,4440064,[[68,4],["logonBtn",4]],0,s.P,[i.r,s.i],{id:[0,"id"],textAlign:[1,"textAlign"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(425,0,null,1,17,"SwtCanvas",[["class","right"],["height","100%"],["id","mainGr"],["width","70%"]],null,null,null,y.Nc,y.U)),i.Ib(426,4440064,[["mainGr",4]],0,s.db,[i.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(427,0,null,0,15,"VBox",[["height","100%"],["width","100%"]],null,null,null,y.od,y.vb)),i.Ib(428,4440064,null,0,s.ec,[i.r,s.i,i.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(429,0,null,0,1,"HBox",[["height","95%"],["id","mainGroup"],["visible","false"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(430,4440064,[[52,4],["mainGroup",4]],0,s.C,[i.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],visible:[3,"visible"]},null),(t()(),i.Jb(431,0,null,0,11,"HBox",[["height","5%"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(432,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(433,0,null,0,9,"HBox",[["paddingLeft","5"],["width","60%"]],null,null,null,y.Dc,y.K)),i.Ib(434,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(435,0,null,0,1,"SwtButton",[["enabled","false"],["id","detailsButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.clickLink("frDetailsBtn")&&i);return i},y.Mc,y.T)),i.Ib(436,4440064,[[62,4],["detailsButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(437,0,null,0,1,"SwtButton",[["enabled","false"],["id","goToButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.goTo(l)&&i);return i},y.Mc,y.T)),i.Ib(438,4440064,[[61,4],["goToButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(439,0,null,0,1,"SwtButton",[["enabled","false"],["id","reActiveButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.updateStatus("A")&&i);return i},y.Mc,y.T)),i.Ib(440,4440064,[[60,4],["reActiveButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(441,0,null,0,1,"SwtButton",[["enabled","false"],["id","resolveButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.updateStatus("R")&&i);return i},y.Mc,y.T)),i.Ib(442,4440064,[[59,4],["resolveButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),i.Jb(443,0,null,0,29,"SwtCanvas",[["height","35"],["minWidth","1100"],["width","100%"]],null,null,null,y.Nc,y.U)),i.Ib(444,4440064,[[54,4],["controlContainer",4]],0,s.db,[i.r,s.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),i.Jb(445,0,null,0,27,"HBox",[["height","100%"],["width","100%"]],null,null,null,y.Dc,y.K)),i.Ib(446,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(447,0,null,0,9,"HBox",[["height","100%"],["paddingLeft","8"],["width","50%"]],null,null,null,y.Dc,y.K)),i.Ib(448,4440064,null,0,s.C,[i.r,s.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(449,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.updateData("workFlowRefresh")&&i);return i},y.Mc,y.T)),i.Ib(450,4440064,[[63,4],["refreshButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(451,0,null,0,1,"SwtButton",[["id","rateButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.rateHandler()&&i);return i},y.Mc,y.T)),i.Ib(452,4440064,[[66,4],["rateButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(453,0,null,0,1,"SwtButton",[["id","optionsButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.optionsHandler()&&i);return i},y.Mc,y.T)),i.Ib(454,4440064,[[65,4],["optionsButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(455,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.closeHandler()&&i);return i},y.Mc,y.T)),i.Ib(456,4440064,[[64,4],["closeButton",4]],0,s.cb,[i.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(457,0,null,0,15,"HBox",[["height","100%"],["horizontalAlign","right"],["verticalAlign","middle"],["width","50%"]],null,null,null,y.Dc,y.K)),i.Ib(458,4440064,[["HgroupContent",4]],0,s.C,[i.r,s.i],{horizontalAlign:[0,"horizontalAlign"],verticalAlign:[1,"verticalAlign"],width:[2,"width"],height:[3,"height"]},null),(t()(),i.Jb(459,0,null,0,1,"SwtText",[["fontWeight","bold"],["height","16"],["id","dataBuildingText"],["right","155"],["styleName","redText"],["text","DATA BUILD IN PROGRESS"],["visible","false"]],null,null,null,y.ld,y.qb)),i.Ib(460,4440064,[[51,4],["dataBuildingText",4]],0,s.Pb,[i.r,s.i],{id:[0,"id"],right:[1,"right"],styleName:[2,"styleName"],height:[3,"height"],visible:[4,"visible"],text:[5,"text"],fontWeight:[6,"fontWeight"]},null),(t()(),i.Jb(461,0,null,0,1,"SwtText",[["fontWeight","bold"],["height","16"],["id","lostConnectionText"],["right","45"],["styleName","redText"],["text","CONNECTION ERROR"],["visible","false"]],null,null,null,y.ld,y.qb)),i.Ib(462,4440064,[[48,4],["lostConnectionText",4]],0,s.Pb,[i.r,s.i],{id:[0,"id"],right:[1,"right"],styleName:[2,"styleName"],height:[3,"height"],visible:[4,"visible"],text:[5,"text"],fontWeight:[6,"fontWeight"]},null),(t()(),i.Jb(463,0,null,0,1,"SwtText",[["fontWeight","normal"],["height","16"],["id","lastRefTimeLabel"]],null,null,null,y.ld,y.qb)),i.Ib(464,4440064,[[50,4],["lastRefTimeLabel",4]],0,s.Pb,[i.r,s.i],{id:[0,"id"],height:[1,"height"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(465,0,null,0,1,"SwtText",[["fontWeight","normal"],["height","16"],["id","lastRefTime"],["right","65"],["width","120"]],null,null,null,y.ld,y.qb)),i.Ib(466,4440064,[[49,4],["lastRefTime",4]],0,s.Pb,[i.r,s.i],{id:[0,"id"],right:[1,"right"],width:[2,"width"],height:[3,"height"],fontWeight:[4,"fontWeight"]},null),(t()(),i.Jb(467,0,null,0,1,"SwtButton",[["buttonMode","true"],["enabled","true"],["id","pdf"],["style","margin-top: 3px;"],["styleName","pdfIcon"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.export("pdf")&&i);return i},y.Mc,y.T)),i.Ib(468,4440064,[[69,4],["pdf",4]],0,s.cb,[i.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(469,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.doHelp()&&i);return i},y.Wc,y.db)),i.Ib(470,4440064,[[67,4],["helpIcon",4]],0,s.rb,[i.r,s.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(471,0,null,0,1,"SwtLoadingImage",[],null,null,null,y.Zc,y.gb)),i.Ib(472,114688,[[43,4],["loadingImage",4]],0,s.xb,[i.r],null,null)],function(t,e){t(e,79,0,"100%","100%");t(e,81,0,"1","100%","100%","5","0","5","5");t(e,83,0,"100%","80","1100");t(e,85,0,"100%","100%");t(e,87,0,"35%","100%","300","5","5");t(e,89,0,"100%","100%");t(e,91,0,"100%","100%");t(e,93,0,"130","100%");t(e,95,0,"labelBold","5","Entity");t(e,97,0,"135","100%");t(e,99,0,"entity","Entity","135","entityCombo");t(e,101,0,"43%","100%");t(e,103,0,"selectedEntity","labelLeft","10","normal");t(e,105,0,"100%","100%");t(e,107,0,"130","100%");t(e,109,0,"labelBold","5","Currency Group");t(e,111,0,"135","100%");t(e,113,0,"currency","Select currency code","135","ccyCombo");t(e,115,0,"43%","100%","10");t(e,117,0,"selectedCcy","labelRight","normal");t(e,119,0,"65%","100%");t(e,121,0,"2","100%","100%");t(e,123,0,"100%","72%");t(e,125,0,"right","100%","7"),t(e,127,0);t(e,129,0,"statusLabel","60","bold"),t(e,131,0);t(e,133,0,"status","100%","horizontal");t(e,136,0,"100%","100%");t(e,138,0,"100%","50%");t(e,140,0,"all","75","status","All");t(e,142,0,"allOpen","75","status","","true");t(e,144,0,"active","75","status","A");t(e,146,0,"100%","50%");t(e,148,0,"pending","75","status","P");t(e,150,0,"overdue","75","status","O");t(e,152,0,"resolved","75","status","R");t(e,154,0,"16");t(e,156,0,"resolvedOnLbl","85","normal");t(e,158,0,"16");t(e,160,0,"resolvedOnDate","false","70");t(e,162,0,"100%","28%");t(e,164,0,"250");t(e,166,0,"tabCategoryList","100%","100%","false");t(e,169,0,"right","100%");t(e,171,0,"right","bottom","5");t(e,173,0,"170","Apply Currency Threshold","bold");t(e,175,0,"currencyThreshold","Apply Currency Threshold");t(e,177,0,"100%","80%","450","1100","false");t(e,179,0,"mainHGroup","100%","100%","5","5");t(e,181,0,"treeDivider","ExtendedDivider","100%","100%","5","W","true","true");t(e,183,0,"vbox","30%","100%");t(e,185,0,"0","100%","89%","5");t(e,187,0,"100%","5%");t(e,189,0,"100%","100%");t(e,191,0,"tabList","100%","100%","false");t(e,194,0,"100%","95%");t(e,196,0,"100%","100%");t(e,198,0,"100%","100%");t(e,200,0,"flowGrid","0","100%","100%");t(e,202,0,"100%","100%");t(e,204,0,"100%","100%");t(e,206,0,"100%","100%","3","10");t(e,208,0,"100%","100%");t(e,210,0,"titleGroup","right","100%","40","3");t(e,212,0,"includedVbox","0","100%","{gridIncTotal.x+gridIncTotal.width-90}");t(e,214,0,"right","{mvtTxt.width}","5","Included","bold");t(e,216,0,"mvtTxt","left","5","Movements","bold");t(e,218,0,"excludedVBox","0","100%","5");t(e,220,0,"right","{outTxt.width}","5","Excluded","bold");t(e,222,0,"right","5","Outstandings","bold");t(e,225,0,"linkGroup","100%","90%");t(e,227,0,"3","4%","100%","100%");t(e,229,0,"100%","6%");t(e,231,0,"100%","1","10");t(e,235,0,"40%","100%","10");t(e,237,0,"normal");t(e,239,0,"right","100%");t(e,241,0,"valueInc0Btn","right","60","{(entityCombo.id=='All'||valueInc0Btn.label=='')?false:true}",i.Lb(1,"",i.Tb(e,241).enabled,""));t(e,243,0,"right","100%");t(e,245,0,"valueExc0Btn","right","90","{(entityCombo.id=='All'||valueExc0Btn.label=='')?false:true}","{valueExc0Btn.enabled}");t(e,247,0,"100%","6%");t(e,249,0,"100%","10");t(e,253,0,"40%","100%","10");t(e,255,0,"labelExc1txt","normal");t(e,257,0,"right","100%");t(e,259,0,"valueInc1Btn","right","60","{(entityCombo.id=='All'||valueInc1Btn.label=='')?false:true}","{valueInc1Btn.enabled}");t(e,261,0,"right","100%");t(e,263,0,"valueExc1Btn","right","90","{(entityCombo.id=='All'||valueExc1Btn.label=='')?false:true}","{valueExc1Btn.enabled}");t(e,265,0,"100%","6%");t(e,267,0,"100%","10");t(e,271,0,"40%","100%","10");t(e,273,0,"labelExc2txt","normal");t(e,275,0,"right","100%");t(e,277,0,"valueInc2Btn","right","60","{(entityCombo.id=='All'||valueInc2Btn.label=='')?false:true}","{valueInc2Btn.enabled}");t(e,279,0,"right","100%");t(e,281,0,"valueExc2Btn","right","90","{(entityCombo.id=='All'||valueExc2Btn.label=='')?false:true}","{valueExc2Btn.enabled}");t(e,283,0,"100%","6%");t(e,285,0,"100%","10");t(e,289,0,"40%","100%","10");t(e,291,0,"normal");t(e,293,0,"right","100%");t(e,295,0,"valueInc3Btn","right","60","{(entityCombo.id=='All'|| valueInc3Btn.label=='')?false:true}","{valueInc3Btn.enabled}");t(e,297,0,"right","100%");t(e,299,0,"valueExc3Btn","right","90","{(entityCombo.id=='All'||valueExc3Btn.label=='')?false:true}","{valueExc3Btn.enabled}");t(e,301,0,"100%","6%");t(e,303,0,"100%","10");t(e,307,0,"40%","100%","10");t(e,309,0,"normal");t(e,311,0,"right","100%");t(e,313,0,"valueInc4Btn","right","60","{(entityCombo.id=='All'||valueInc4Btn.label=='')?false:true}","{valueInc4Btn.enabled}");t(e,315,0,"right","100%");t(e,317,0,"valueExc4Btn","right","90","{(entityCombo.id=='All'||valueExc4Btn.label=='')?false:true}","{valueExc4Btn.enabled}");t(e,319,0,"100%","6%");t(e,321,0,"100%","10");t(e,325,0,"40%","100%","10");t(e,327,0,"normal");t(e,329,0,"right","100%");t(e,331,0,"right","60","{(entityCombo.id=='All'||valueInc5Btn.label=='')?false:true}","{valueInc5Btn.enabled}");t(e,333,0,"right","100%");t(e,335,0,"right","90","{(entityCombo.id=='All'||valueExc5Btn.label=='')?false:true}","{valueExc5Btn.enabled}");t(e,337,0,"100%","6%");t(e,339,0,"100%","10");t(e,343,0,"40%","100%","10");t(e,345,0,"normal");t(e,347,0,"right","100%");t(e,349,0,"right","60","{(entityCombo.id=='All'||valueInc6Btn.label=='')?false:true}","{valueInc6Btn.enabled}");t(e,351,0,"right","100%");t(e,353,0,"right","90","{(entityCombo.id =='All'||valueExc6Btn.label=='') ? false:true}","{valueExc6Btn.enabled}");t(e,355,0,"100%","6%");t(e,357,0,"100%","10");t(e,361,0,"40%","100%","10");t(e,363,0,"normal");t(e,365,0,"right","100%");t(e,367,0,"right","60","{(entityCombo.id=='All'||valueInc7Btn.label=='')?false:true}","{valueInc7Btn.enabled}");t(e,369,0,"right","100%");t(e,371,0,"right","90","{(entityCombo.id=='All'||valueExc7Btn.label=='')?false:true}","{valueExc7Btn.enabled}");t(e,373,0,"100%","6%");t(e,375,0,"100%","10");t(e,379,0,"40%","100%","10");t(e,381,0,"normal");t(e,383,0,"right","100%");t(e,385,0,"right","60","{(entityCombo.id=='All'||valueInc8Btn.label=='')?false:true}","{valueInc8Btn.enabled}");t(e,387,0,"right","100%");t(e,389,0,"valueExc8Btn","right","90","{(entityCombo.id=='All'||valueExc8Btn.label=='')?false:true}","{valueExc8Btn.enabled}");t(e,391,0,"100%","10%");t(e,393,0,"10");t(e,395,0,"65%","100%","26");t(e,397,0,"Total","normal");t(e,399,0,"gridIncTotal","right","100%");t(e,401,0,"right","labelRight","0","11","bold");t(e,403,0,"gridTotal","right","100%");t(e,405,0,"right","labelRight","88","0","11","bold");t(e,407,0,"100%","10%","40","5");t(e,412,0,"0","100%","80%");t(e,414,0,"100%","100%");t(e,416,0,"11%");t(e,418,0,"34%","5");t(e,420,0,"Logged on","11","normal");t(e,422,0,"right","10%","5","10");t(e,424,0,"logonBtn","right","{logonBtn.enabled}");t(e,426,0,"mainGr","70%","100%");t(e,428,0,"100%","100%");t(e,430,0,"mainGroup","100%","95%","false");t(e,432,0,"100%","5%");t(e,434,0,"60%","5");t(e,436,0,"detailsButton","false");t(e,438,0,"goToButton","false");t(e,440,0,"reActiveButton","false");t(e,442,0,"resolveButton","false");t(e,444,0,"100%","35","1100");t(e,446,0,"100%","100%");t(e,448,0,"50%","100%","8");t(e,450,0,"refreshButton");t(e,452,0,"rateButton");t(e,454,0,"optionsButton");t(e,456,0,"closeButton");t(e,458,0,"right","middle","50%","100%");t(e,460,0,"dataBuildingText","155","redText","16","false","DATA BUILD IN PROGRESS","bold");t(e,462,0,"lostConnectionText","45","redText","16","false","CONNECTION ERROR","bold");t(e,464,0,"lastRefTimeLabel","16","normal");t(e,466,0,"lastRefTime","65","120","16","normal");t(e,468,0,"pdf","pdfIcon","true","true");t(e,470,0,"helpIcon"),t(e,472,0)},null)}function U(t){return i.dc(0,[(t()(),i.Jb(0,0,null,null,1,"app-work-flow-monitor",[],null,null,null,Z,H)),i.Ib(1,4440064,null,0,c,[s.i,i.r],null,null)],function(t,e){t(e,1,0)},null)}var Y=i.Fb("app-work-flow-monitor",c,U,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);