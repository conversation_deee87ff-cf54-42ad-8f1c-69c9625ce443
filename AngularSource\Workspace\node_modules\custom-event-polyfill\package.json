{"_from": "custom-event-polyfill@1.0.6", "_id": "custom-event-polyfill@1.0.6", "_inBundle": false, "_integrity": "sha512-3FxpFlzGcHrDykwWu+xWVXZ8PfykM/9/bI3zXb953sh+AjInZWcQmrnmvPoZgiqNjmbtTm10PWvYqvRW527x6g==", "_location": "/custom-event-polyfill", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "custom-event-polyfill@1.0.6", "name": "custom-event-polyfill", "escapedName": "custom-event-polyfill", "rawSpec": "1.0.6", "saveSpec": null, "fetchSpec": "1.0.6"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/custom-event-polyfill/-/custom-event-polyfill-1.0.6.tgz", "_shasum": "6b026e81cd9f7bc896bd6b016a427407bb068db1", "_spec": "custom-event-polyfill@1.0.6", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/kumarharsh/custom-event-polyfill/issues"}, "bundleDependencies": false, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "contributors": [{"name": "Mozilla Developer Network"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "http://www.savvi.io"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://www.feth.com"}], "deprecated": false, "description": "A polyfill for creating CustomEvents on IE9+ if the native implementation is missing.", "devDependencies": {"@commitlint/cli": "^6.2.0", "@commitlint/config-conventional": "^6.1.3", "@semantic-release/changelog": "^2.0.2", "@semantic-release/git": "^5.0.0", "eslint": "4.19.1", "eslint-plugin-prettier": "2.6.0", "husky": "^0.14.3", "jasmine-core": "^3.1.0", "karma": "^2.0.2", "karma-jasmine": "^1.1.2", "karma-sauce-launcher": "^1.2.0", "prettier": "1.12.1", "semantic-release": "^15.4.1"}, "files": ["polyfill.js"], "homepage": "https://github.com/kumarharsh/custom-event-polyfill#readme", "keywords": ["polyfill", "custom-event", "CustomEvent", "Web API Interface"], "license": "MIT", "main": "polyfill.js", "name": "custom-event-polyfill", "repository": {"type": "git", "url": "git+ssh://**************/kumarharsh/custom-event-polyfill.git"}, "scripts": {"commitmsg": "commitlint -e $GIT_PARAMS", "test": "karma start", "test-ci": "karma start ./karma.conf-ci.js"}, "version": "1.0.6"}