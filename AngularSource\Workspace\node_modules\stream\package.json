{"_from": "stream@0.0.2", "_id": "stream@0.0.2", "_inBundle": false, "_integrity": "sha512-gCq3NDI2P35B2n6t76YJuOp7d6cN/C7Rt0577l91wllh0sY9ZBuw9KaSGqH/b0hzn3CWWJbpbW0W0WvQ1H/Q7g==", "_location": "/stream", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "stream@0.0.2", "name": "stream", "escapedName": "stream", "rawSpec": "0.0.2", "saveSpec": null, "fetchSpec": "0.0.2"}, "_requiredBy": ["/swt-tool-box"], "_resolved": "https://registry.npmjs.org/stream/-/stream-0.0.2.tgz", "_shasum": "7f5363f057f6592c5595f00bc80a27f5cec1f0ef", "_spec": "stream@0.0.2", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browser": {"emitter": "emitter-component"}, "bugs": {"url": "https://github.com/juliangruber/stream/issues"}, "bundleDependencies": false, "dependencies": {"emitter-component": "^1.1.1"}, "deprecated": false, "description": "Node.js streams in the browser", "devDependencies": {"expect.js": "*", "mocha": "*"}, "homepage": "https://github.com/juliangruber/stream#readme", "keywords": ["stream"], "license": "MIT", "main": "index.js", "name": "stream", "repository": {"type": "git", "url": "git://github.com/juliangruber/stream.git"}, "version": "0.0.2"}