{"_from": "xlsx@0.16.8", "_id": "xlsx@0.16.8", "_inBundle": false, "_integrity": "sha512-qWub4YCn0xLEGHI7WWhk6IJ73MDu7sPSJQImxN6/LiI8wsHi0hUhICEDbyqBT+jgFgORZxrii0HvhNSwBNAPoQ==", "_location": "/xlsx", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "xlsx@0.16.8", "name": "xlsx", "escapedName": "xlsx", "rawSpec": "0.16.8", "saveSpec": null, "fetchSpec": "0.16.8"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/xlsx/-/xlsx-0.16.8.tgz", "_shasum": "5546de9b0ba15169b36770d4e43b24790d3ff1b8", "_spec": "xlsx@0.16.8", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "author": {"name": "sheetjs"}, "bin": {"xlsx": "bin/xlsx.njs"}, "browser": {"buffer": false, "crypto": false, "stream": false, "process": false, "fs": false}, "bugs": {"url": "https://github.com/SheetJS/js-xlsx/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": "xlsx.js"}}, "dependencies": {"adler-32": "~1.2.0", "cfb": "^1.1.4", "codepage": "~1.14.0", "commander": "~2.17.1", "crc-32": "~1.2.0", "exit-on-epipe": "~1.0.1", "ssf": "~0.11.2", "wmf": "~1.0.1", "word": "~0.3.0"}, "deprecated": false, "description": "SheetJS Spreadsheet data parser and writer", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.5.9", "blanket": "~1.2.3", "dtslint": "^0.1.2", "jsdom": "~11.1.0", "mocha": "~2.5.3", "typescript": "2.2.0"}, "engines": {"node": ">=0.8"}, "homepage": "https://sheetjs.com/", "jsdelivr": "dist/xlsx.min.js", "keywords": ["excel", "xls", "xlsx", "xlsb", "xlsm", "ods", "csv", "dbf", "dif", "sylk", "office", "spreadsheet"], "license": "Apache-2.0", "main": "./xlsx", "name": "xlsx", "repository": {"type": "git", "url": "git://github.com/SheetJS/sheetjs.git"}, "scripts": {"build": "make", "dtslint": "dtslint types", "lint": "make fullint", "pretest": "git submodule init && git submodule update", "test": "make travis"}, "types": "types", "unpkg": "dist/xlsx.min.js", "version": "0.16.8"}