(window.webpackJsonp=window.webpackJsonp||[]).push([[67],{HCCY:function(e,t,i){"use strict";i.r(t);var n=i("CcnG"),l=i("mrSG"),r=i("ZYCi"),a=i("447K"),o=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return l.d(t,e),t.prototype.GenericJSONReader=function(){},t.prototype.getPages=function(){return this.getInputJSON().value.pages},t.prototype.getMaxPages=function(){return this.getInputJSON().value.pages.maxPage},t.prototype.getCurrent_Page=function(){return this.getInputJSON().value.pages.currentPage},t.prototype.getSelectedFilter=function(){return this.getInputJSON().value.selectedFilter},t.prototype.getInitialInputScreen=function(){return this.getInputJSON().value.initialinputscreen},t.prototype.getSelectedSort=function(){return this.getInputJSON().value.selectedSort},t.prototype.getFontSize=function(){return this.getInputJSON().value.currfontsize},t}(a.L),s=i("EVdn"),h=function(e){function t(t,i){var n=e.call(this,i,t)||this;return n.commonService=t,n.element=i,n.testJquery=s,n.jsonReader=new o,n.inputData=new a.G(n.commonService),n.baseURL="",n.actionMethod="",n.actionPath="",n.requestParams=[],n.invalidComms="",n.baseQuery="",n.scenarioFacilityId="",n.scenarioId="",n.refColumns="",n.facilityRefColumns="",n.refParams="",n.facilityRefParams="",n.fromSummaryScreen="",n.additionalParams="",n.filter="",n.selectedCurrencyGroup="",n.applyCurrencyThreshold="",n.screenName="Generic Display",n.versionNumber="1.0",n.releaseDate="31 January 2020",n.columnNamesArr=[],n.lastselectedIndex=null,n.currentFontSize="",n.columnsWidth="",n.columnsOrder="",n.hostID="",n.entityID="",n.currencyCodeKey="",n.movementIdKey="",n.matchIdKey="",n.sweepIdKey="",n.previousVerticalScrollBarPosition=0,n.previousHorizontalScrollBarPosition=0,n.lastSortedIndex=-1,n.screenVersion=new a.V(n.commonService),n.prevSortColumn="",n.applySort=!1,n.prevColumnSort=new Object,n.logger=new a.R("Generic Display",n.commonService.httpclient),n.swtAlert=new a.bb(t),n}return l.d(t,e),t.prototype.ngOnInit=function(){instanceElement=this,this.refreshButton.toolTip=a.x.call("getBundle","tip","button-refresh","Refresh window"),this.refreshButton.label=a.x.call("getBundle","text","button-refresh","Refresh"),this.goToButton.toolTip=a.x.call("getBundle","tip","button-goto-tooltip","Go to"),this.goToButton.label=a.x.call("getBundle","text","button-goto","Go to"),this.closeButton.toolTip=a.x.call("getBundle","tip","button-close","Close window"),this.closeButton.label=a.x.call("getBundle","text","button-close","Close")},t.prototype.ngOnDestroy=function(){instanceElement=null},t.prototype.onLoad=function(){var e=this,t=0;try{t=10,this.logger.info("method [onLoad] - START "),this.gGrid=this.dataGridContainer.addChild(a.hb),this.gGrid.clientSideSort=!1,this.gGrid.clientSideFilter=!1,this.gGrid.onSortChanged=this.rememberSortedColumn.bind(this),this.gGrid.onFilterChanged=this.filterCall.bind(this),this.gGrid.addEmptyNotEmptyFilter=!0,this.gGrid.onPaginationChanged=this.numPager.bind(this),this.gGrid.paginationComponent=this.numStepper,this.numStepper.enabled=!1,this.genLogic=new a.h,this.baseURL=a.Wb.getBaseURL(),this.initializeMenus(),this.gGrid.ITEM_CLICK.subscribe(function(t){e.cellLogic(t)}),a.v.subscribe(function(t){e.export(t.type,t.startPage,t.noOfPages)}),this.exportContainer.exportCancelFunction=this.exportCancel.bind(this),this.genLogic.testDate=a.x.call("eval","testDate"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.refreshButton.click=function(e){},this.goToButton.click=function(e){},this.goToButton.enabled=!1,this.closeButton.click=function(t){e.closeHandler(t)},this.refreshButton.visible=!0,this.closeButton.visible=!0,this.goToButton.visible=!0,this.scenarioIdAndTitle.text=a.x.call("eval","scenarioID")+" - "+a.x.call("eval","scenarioTitle"),this.fromSummaryScreen=a.x.call("eval","fromSummaryScreen"),"true"==this.fromSummaryScreen?(this.scenarioId=a.x.call("eval","scenarioID"),this.filter=a.x.call("eval","filter"),this.selectedCurrencyGroup=a.x.call("eval","selectedCurrencyGroup"),this.applyCurrencyThreshold=a.x.call("eval","applyCurrencyThreshold"),this.scenarioFacilityId=a.x.call("eval","facilityID"),this.refColumns=a.x.call("eval","refColumns"),this.facilityRefColumns=a.x.call("eval","facilityRefColumns"),this.refParams=a.x.call("eval","refParams"),this.facilityRefParams=a.x.call("eval","facilityRefParams"),this.actionPath="genericdisplay.do?",this.actionMethod="method=refreshGenericDisplayData",this.actionMethod+="&scenarioID="+this.scenarioId,this.actionMethod+="&filter="+this.filter,this.actionMethod+="&selectedCurrencyGroup="+this.selectedCurrencyGroup,this.actionMethod+="&applyCurrencyThreshold="+this.applyCurrencyThreshold,this.actionMethod+="&facilityId="+this.scenarioFacilityId,this.actionMethod+="&refColumns="+this.refColumns,this.actionMethod+="&facilityRefColumns="+this.facilityRefColumns,this.actionMethod+="&refParams="+this.refParams,this.actionMethod+="&facilityRefParams="+this.facilityRefParams,this.actionMethod+="&fromSummaryScreen="+this.fromSummaryScreen):(this.baseQuery=a.x.call("eval","basequery"),this.baseQuery=a.t.encode64(this.baseQuery),this.scenarioFacilityId=a.x.call("eval","facilityID"),this.refColumns=a.x.call("eval","refColumns"),this.facilityRefColumns=a.x.call("eval","facilityRefColumns"),this.refParams=a.x.call("eval","refParams"),this.facilityRefParams=a.x.call("eval","facilityRefParams"),this.actionPath="genericdisplay.do?",this.actionMethod="method=getGenericDisplayData",this.actionMethod+="&facilityId="+this.scenarioFacilityId,this.actionMethod+="&refColumns="+this.refColumns,this.actionMethod+="&facilityRefColumns="+this.facilityRefColumns,this.actionMethod+="&refParams="+this.refParams,this.actionMethod+="&facilityRefParams="+this.facilityRefParams),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.basequery=this.baseQuery,this.inputData.send(this.requestParams),this.logger.info("method [onLoad] - END ")}catch(i){a.Wb.logError(i,"Predict","GenericDisplay.ts","onLoad",t),this.logger.error("GenericDisplay - method [onLoad] - error ",i)}},t.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,"Generic Display",this.versionNumber,this.releaseDate);var e=null;(e=new a.n(a.x.call("getBundle","text","label-showXML","Show JSON"))).MenuItemSelect=this.showXMLSelect.bind(this),this.screenVersion.svContextMenu.customItems.push(e),this.contextMenu=this.screenVersion.svContextMenu},t.prototype.showXMLSelect=function(e){this.showXMLPopup=a.Eb.createPopUp(this,a.M,{jsonData:this.lastRecievedJSON}),this.showXMLPopup.width="700",this.showXMLPopup.title="Last Received JSON",this.showXMLPopup.height="350",this.showXMLPopup.enableResize=!1,this.showXMLPopup.showControls=!0,this.showXMLPopup.display()},t.prototype.cellLogic=function(e){this.logger.info("method [cellLogic] - START ");var t=0;try{t=10;var i=a.x.call("eval","facilityID");t=20,t=30,1==this.gGrid.selectedIndices.length?(t=40,i!=a.x.call("eval","NoneFacility")&&i!=a.x.call("eval","msdFacilityId")&&i!=a.x.call("eval","genericFacilityId")&&i!=a.x.call("eval","inputExceptionFacilityId")&&i!=a.x.call("eval","matchDisplayManyFacilityId")?this.goToButton.enabled=!0:this.goToButton.enabled=!1):this.goToButton.enabled=!1,this.logger.info("method [cellLogic] - END ")}catch(n){a.Wb.logError(n,"Predict","GenericDisplay.ts","cellLogic",t),this.logger.error("GenericDisplay - method [cellLogic] - error ",n)}},t.prototype.keyDownPager=function(e){this.logger.info("method [keyDownPager] - START ");var t=0;try{if(t=10,e.charCode==a.N.ENTER)if(this.jsonReader.getCurrent_Page()!=this.numStepper.value&&this.numStepper.value<=this.jsonReader.getMaxPages()){var i="&method=refreshGenericDisplayData";"true"==this.fromSummaryScreen?(i+="&scenarioID="+a.x.call("eval","scenarioID"),i+="&filter="+a.x.call("eval","filter"),i+="&selectedCurrencyGroup="+a.x.call("eval","selectedCurrencyGroup"),i+="&applyCurrencyThreshold="+a.x.call("eval","applyCurrencyThreshold"),i+="&fromSummaryScreen="+this.fromSummaryScreen,i+="&refColumns="+this.refColumns,i+="&facilityRefColumns="+this.facilityRefColumns,i+="&refParams="+this.refParams,i+="&facilityRefParams="+this.facilityRefParams):i+="&basequery="+this.baseQuery,i+="&selectedSort="+this.jsonReader.getSelectedSort()+"&selectedFilter="+this.jsonReader.getSelectedFilter()+"&maxPage="+this.jsonReader.getMaxPages()+"&totalCount="+this.jsonReader.getRowSize()+"&pageNoValue="+this.numStepper.value+"&currentPage="+this.jsonReader.getCurrent_Page()+"&columnsWidth="+this.columnsWidth+"&columnsOrder="+this.columnsOrder+"&refColumns="+this.refColumns+"&facilityRefColumns="+this.facilityRefColumns+"&refParams="+this.refParams+"&facilityRefParams="+this.facilityRefParams,this.inputData.url=this.baseURL+this.actionPath+i,this.inputData.send(this.requestParams)}else isNaN(this.numStepper.value)&&(this.numStepper.value=Number(this.jsonReader.getCurrent_Page()),this.swtAlert.show(a.x.call("getBundle","text","label-validPageNumber","Please enter a valid page number"),a.x.call("getBundle","text","alert-error","Error")));this.previousHorizontalScrollBarPosition=0,this.previousVerticalScrollBarPosition=0,this.logger.info("method [keyDownPager] - END ")}catch(n){a.Wb.logError(n,"Predict","GenericDisplay.ts","keyDownPager",t),this.logger.error("GenericDisplay - method [keyDownPager] - error ",n)}},t.prototype.numPager=function(e){this.logger.info("method [numPager] - START ");try{if(this.jsonReader.getCurrent_Page()!=this.numStepper.value){if(isNaN(this.numStepper.value))this.numStepper.value=Number(this.jsonReader.getCurrent_Page()),this.swtAlert.show(a.x.call("getBundle","text","label-validPageNumber","Please enter a valid page number"),a.x.call("getBundle","text","alert-error","Error"));else{var t="&method=refreshGenericDisplayData";"true"==this.fromSummaryScreen?(t+="&scenarioID="+a.x.call("eval","scenarioID"),t+="&filter="+a.x.call("eval","filter"),t+="&selectedCurrencyGroup="+a.x.call("eval","selectedCurrencyGroup"),t+="&applyCurrencyThreshold="+a.x.call("eval","applyCurrencyThreshold"),t+="&fromSummaryScreen="+this.fromSummaryScreen,t+="&refColumns="+this.refColumns,t+="&facilityRefColumns="+this.facilityRefColumns,t+="&refParams="+this.refParams,t+="&facilityRefParams="+this.facilityRefParams):t+="&basequery="+this.baseQuery,t+="&selectedSort="+this.jsonReader.getSelectedSort()+"&selectedFilter="+this.jsonReader.getSelectedFilter()+"&maxPage="+this.jsonReader.getMaxPages()+"&totalCount="+this.jsonReader.getRowSize()+"&pageNoValue="+this.numStepper.value+"&currentPage="+this.jsonReader.getCurrent_Page()+"&columnsWidth="+this.columnsWidth+"&columnsOrder="+this.columnsOrder+"&refColumns="+this.refColumns+"&facilityRefColumns="+this.facilityRefColumns+"&refParams="+this.refParams+"&facilityRefParams="+this.facilityRefParams,this.inputData.url=this.baseURL+this.actionPath+t,this.inputData.send(this.requestParams)}this.previousHorizontalScrollBarPosition=0,this.previousVerticalScrollBarPosition=0}this.logger.info("method [numPager] - END ")}catch(i){a.Wb.logError(i,"Predict","GenericDisplay.ts","numPager",0),this.logger.error("GenericDisplay - method [numPager] - error ",i)}},t.prototype.inputDataResult=function(e){this.logger.info("method [inputDataResult] - START ");var t=0,i=!1,n=a.x.call("eval","facilityID");try{if(this.inputData.isBusy())this.inputData.cbStop();else if(t=10,this.lastRecievedJSON=e,t=20,this.jsonReader.setInputJSON(this.lastRecievedJSON),t=30,t=40,this.lastRefTimeLabel.visible=!0,this.lastRefTime.visible=!0,this.lastRefTimeLabel.text=a.Wb.getPredictMessage("screen.lastRefresh",null),this.lastRefTimeLabel.color="black",this.lastRefTime.text=this.jsonReader.getScreenAttributes().lastRefTime,0!=this.jsonReader.getRowSize()?this.exportContainer.enabled=!0:this.exportContainer.enabled=!1,this.jsonReader.getRequestReplyStatus()){if(t=50,JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)){if(!this.jsonReader.isDataBuilding()){t=60,this.exportContainer.maxPages=a.x.call("eval","exportMaxPages"),this.exportContainer.totalPages=this.jsonReader.getMaxPages(),this.exportContainer.currentPage=this.jsonReader.getCurrent_Page(),null!=this.gGrid&&-1!=this.gGrid.selectedIndex&&(this.lastselectedIndex=this.gGrid.dataProvider[this.gGrid.selectedIndex]),null!=this.gGrid&&-1!=this.gGrid.sortColumnIndex&&(this.lastSortedIndex=this.gGrid.sortColumnIndex);var l=e.genericgrid.grid.metadata;window.opener&&window.opener.instanceElement3&&window.opener.instanceElement3.sendColumns(l),this.gGrid.CustomGrid(l),this.gGrid.saveColumnOrder=!0,this.gGrid.saveWidths=!0,this.gGrid.uniqueColumn="generic",this.gGrid.gridData=this.jsonReader.getGridData(),this.gGrid.setRowSize=this.jsonReader.getRowSize(),this.additionalParams=this.jsonReader.getScreenAttributes().scenarioRefParams;var r=a.t.decode64(this.jsonReader.getSelectedFilter());this.numStepper.value=Number(this.jsonReader.getCurrent_Page()),r.split("|");for(var o=a.t.decode64(this.jsonReader.getSelectedSort()),s=o.substr(0,o.indexOf("|")),h=[],d=[],u=[],c=this.jsonReader.getColumnData().column,g=function(e){var t=c[e].dataelement,i=m.jsonReader.getColumnData().column.find(function(e){return e.dataelement==t}),n=i.columnNumber,l=i.dataelement,r=i.heading;h[t]=n,d[t]=l,u[t]=r},m=this,p=0;p<c.length;p++)g(p);this.columnNamesArr=d;for(var b=0;b<h.length;b++)'"'+u[b]+'"'==s&&(this.gGrid.sortColumnIndex=b);var f=this.jsonReader.getPages();f.page&&f.page.length>0?(this.page.text="            "+a.Wb.getPredictMessage("genericDisplayMonitor.labelPage",null),this.page.fontWeight="bold",this.numStepper.minimum=1,this.numStepper.maximum=this.jsonReader.getMaxPages(),this.numStepper.enabled=!0):(this.page.text="            "+a.Wb.getPredictMessage("genericDisplayMonitor.labelPage",null),this.page.fontWeight="bold",this.numStepper.minimum=1,this.numStepper.maximum=1,this.numStepper.enabled=!1)}this.currentFontSize=this.jsonReader.getFontSize(),"Normal"==this.currentFontSize?(this.gGrid.styleName="dataGridNormal",this.gGrid.rowHeight=18):"Small"==this.currentFontSize&&(this.gGrid.styleName="dataGridSmall",this.gGrid.rowHeight=15)}if(null==this.initReceivedJSON&&null!=this.lastRecievedJSON&&(this.initReceivedJSON=this.lastRecievedJSON),null!=this.lastselectedIndex){for(p=0;p<this.gGrid.dataProvider.length;p++)this.gGrid.dataProvider[p]==this.lastselectedIndex&&(this.gGrid.selectedIndex=p,i=!0,n!=a.x.call("eval","NoneFacility")&&n!=a.x.call("eval","msdFacilityId")&&n!=a.x.call("eval","genericFacilityId")&&n!=a.x.call("eval","matchDisplayManyFacilityId")?this.goToButton.enabled=!0:this.goToButton.enabled=!1);i||(this.gGrid.selectedIndex=-1,this.goToButton.enabled=!1)}this.resetScrollBarPostions()}else this.swtAlert.show(a.x.call("getBundle","text","alert-contactAdmin","Error occurred, Please contact your System Administrator:")+"\n"+this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),a.x.call("getBundle","text","alert-error","Error"))}catch(y){a.Wb.logError(y,"Predict","GenericDisplay.ts","inputDataResult",t),this.logger.error("GenericDisplay - method [inputDataResult] - error ",y)}},t.prototype.pageDataResult=function(e){this.logger.info("method [pageDataResult] - START ");var t=0,i=!1,n=a.x.call("eval","facilityID");try{if(this.inputData.isBusy())this.inputData.cbStop();else if(t=10,this.lastRecievedJSON=e,t=20,this.jsonReader.setInputJSON(this.lastRecievedJSON),t=30,t=40,this.lastRefTimeLabel.visible=!0,this.lastRefTime.visible=!0,this.lastRefTimeLabel.text=a.Wb.getPredictMessage("screen.lastRefresh",null),this.lastRefTimeLabel.color="black",this.lastRefTime.text=this.jsonReader.getScreenAttributes().lastRefTime,0!=this.jsonReader.getRowSize()?this.exportContainer.enabled=!0:this.exportContainer.enabled=!1,this.jsonReader.getRequestReplyStatus()){if(t=50,JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&!this.jsonReader.isDataBuilding()){t=60,null!=this.gGrid&&-1!=this.gGrid.selectedIndex&&(this.lastselectedIndex=this.gGrid.dataProvider[this.gGrid.selectedIndex]),null!=this.gGrid&&-1!=this.gGrid.sortColumnIndex&&(this.lastSortedIndex=this.gGrid.sortColumnIndex),this.gGrid.gridData=this.jsonReader.getGridData(),this.gGrid.setRowSize=this.jsonReader.getRowSize(),this.additionalParams=this.jsonReader.getScreenAttributes().scenarioRefParams;this.numStepper.value=Number(this.jsonReader.getCurrent_Page()),this.jsonReader.getPages().page.length>0&&(this.page.text="            "+a.Wb.getPredictMessage("genericDisplayMonitor.labelPage",null),this.page.fontWeight="bold",this.numStepper.minimum=1,this.numStepper.maximum=this.jsonReader.getMaxPages(),this.numStepper.enabled=!0)}if(null==this.initReceivedJSON&&null!=this.lastRecievedJSON&&(this.initReceivedJSON=this.lastRecievedJSON),null!=this.lastselectedIndex){for(var l=0;l<this.gGrid.dataProvider.length;l++)this.gGrid.dataProvider[l]==this.lastselectedIndex&&(this.gGrid.selectedIndex=l,i=!0,n!=a.x.call("eval","NoneFacility")&&n!=a.x.call("eval","msdFacilityId")&&n!=a.x.call("eval","genericFacilityId")&&n!=a.x.call("eval","matchDisplayManyFacilityId")?this.goToButton.enabled=!0:this.goToButton.enabled=!1);i||(this.gGrid.selectedIndex=-1,this.goToButton.enabled=!1)}this.resetScrollBarPostions()}else this.swtAlert.show(a.x.call("getBundle","text","alert-contactAdmin","Error occurred, Please contact your System Administrator:")+"\n"+this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),a.x.call("getBundle","text","alert-error","Error"))}catch(r){a.Wb.logError(r,"Predict","GenericDisplay.ts","pageDataResult",t),this.logger.error("GenericDisplay - method [pageDataResult] - error ",r)}},t.prototype.resetScrollBarPostions=function(){},t.prototype.dataRefresh=function(e,t){void 0===t&&(t=!1),this.actionPath="genericdisplay.do?",this.actionMethod="method=refreshGenericDisplayData","true"==this.fromSummaryScreen?(this.actionMethod+="&scenarioID="+a.x.call("eval","scenarioID"),this.actionMethod+="&filter="+a.x.call("eval","filter"),this.actionMethod+="&selectedCurrencyGroup="+a.x.call("eval","selectedCurrencyGroup"),this.actionMethod+="&applyCurrencyThreshold="+a.x.call("eval","applyCurrencyThreshold"),this.actionMethod+="&fromSummaryScreen="+this.fromSummaryScreen):this.actionMethod+="&basequery="+this.baseQuery,this.actionMethod+="&currentPage="+this.jsonReader.getCurrent_Page(),this.actionMethod+="&selectedSort="+this.jsonReader.getSelectedSort(),this.actionMethod+="&selectedFilter="+this.jsonReader.getSelectedFilter(),this.actionMethod+="&maxPage="+this.jsonReader.getMaxPages(),this.actionMethod+="&totalCount="+this.jsonReader.getRowSize(),this.actionMethod+="&columnsWidth="+this.columnsWidth,this.actionMethod+="&columnsOrder="+this.columnsOrder,this.actionMethod+="&cancelExport="+t,this.actionMethod+="&facilityId="+this.scenarioFacilityId,this.actionMethod+="&refColumns="+this.refColumns,this.actionMethod+="&facilityRefColumns="+this.facilityRefColumns,this.actionMethod+="&refParams="+this.refParams,this.actionMethod+="&facilityRefParams="+this.facilityRefParams,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},t.prototype.inputDataFault=function(e){this.lastRefTime.visible=!1,this.lastRefTimeLabel.text=a.x.call("getBundle","text","label-connectionError","CONNECTION ERROR"),this.lastRefTimeLabel.color="red",this.invalidComms=e.fault.faultString+"\n"+e.fault.faultCode+"\n"+e.fault.faultDetail},t.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.disableInterface()},t.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableInterface()},t.prototype.disableInterface=function(){this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1},t.prototype.enableInterface=function(){this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0},t.prototype.closeHandler=function(e){a.x.call("close")},t.prototype.export=function(e,t,i){var n=this.jsonReader.getSelectedFilter().replace(/&/g,"amp;").replace(/\+/g,"plus;"),l=this.jsonReader.getSelectedSort();a.x.call("onExport",this.baseQuery,n,l,e,i,t,this.filter,this.fromSummaryScreen,this.selectedCurrencyGroup,this.applyCurrencyThreshold)},t.prototype.closePopup=function(){this.exportContainer.closeCancelPopup()},t.prototype.downloadError=function(){this.swtAlert.show(a.x.call("getBundle","text","alert-errorServerSide","SERVER SIDE ERROR"),a.x.call("getBundle","text","alert-error","Error"))},t.prototype.exportCancel=function(e){this.dataRefresh(e,!0)},t.prototype.helphandler=function(){a.x.call("help")},t.prototype.goTo=function(e){var t,i,n,l,r,o,s=this.jsonReader.getColumnData();t=s.column.find(function(e){return"HOST_ID"==e.heading}).dataelement,this.hostID=this.gGrid.dataProvider[this.gGrid.selectedIndex][t],null!=s.column.find(function(e){return"ENTITY_ID"==e.heading})&&(i=s.column.find(function(e){return"ENTITY_ID"==e.heading}).dataelement,this.entityID=this.gGrid.dataProvider[this.gGrid.selectedIndex][i]),null!=s.column.find(function(e){return"CURRENCY_CODE"==e.heading})&&(n=s.column.find(function(e){return"CURRENCY_CODE"==e.heading}).dataelement,this.currencyCodeKey=this.gGrid.dataProvider[this.gGrid.selectedIndex][n]),null!=s.column.find(function(e){return"MOVEMENT_ID"==e.heading})&&(l=s.column.find(function(e){return"MOVEMENT_ID"==e.heading}).dataelement,this.movementIdKey=this.gGrid.dataProvider[this.gGrid.selectedIndex][l]),null!=s.column.find(function(e){return"MATCH_ID"==e.heading})&&(r=s.column.find(function(e){return"MATCH_ID"==e.heading}).dataelement,this.matchIdKey=this.gGrid.dataProvider[this.gGrid.selectedIndex][r]),null!=s.column.find(function(e){return"SWEEP_ID"==e.heading})&&(o=s.column.find(function(e){return"SWEEP_ID"==e.heading}).dataelement,this.sweepIdKey=this.gGrid.dataProvider[this.gGrid.selectedIndex][o]),a.x.call("goTo",this.hostID,this.entityID,this.matchIdKey,this.currencyCodeKey,this.movementIdKey,this.sweepIdKey,this.additionalParams)},t.prototype.dataRefreshFilterAndSort=function(){for(var e,t="",i=(this.jsonReader.getScreenAttributes().access,[]),n=[],l=[],r=[],o=0;o<this.jsonReader.getColumnData().column.length;o++){var s=(e=this.jsonReader.getColumnData().column[o]).columnNumber,h=e.heading,d=e.type,u=e.dataelement;i[h]=s,n[h]=h,l[h]=d,r[u]={num:s,name:h,type:d}}var c=a.t.decode64(this.jsonReader.getSelectedSort()),g="";if(this.applySort)for(var m in n)m==this.gGrid.sortedGridColumn&&(g+=m,g=this.gGrid.sortColumnIndex==this.lastSortedIndex&&c.indexOf("|DESC|")>0?'"'+g+'"|ASC|':'"'+g+'"|DESC|');else g=c;var p,b="",f=!1,y="";for(var R in b="",r)void 0!==this.gGrid.getCurrentFilter()[R]?(y=this.gGrid.getCurrentFilter()[R])?"string"==typeof y&&(b+='"'+r[R].name+'"$#$'+y.replace(/&/g,"amp;").replace(/\+/g,"plus;")+"$#$"+r[R].type+"|"):y="(EMPTY)":(y=null,b+="All|");for(var C=0;C<this.columnNamesArr.length;C++){f=!1;for(var S=0;S<this.gGrid.currentFilterColumns.length;S++){for(p in this.gGrid.currentFilterColumns[S])if(this.gGrid.FilteredColumn=p,y=this.gGrid.currentFilterColumns[S][p],this.columnNamesArr[C]==this.gGrid.FilteredColumn){f=!0;break}if(f)break}f?(y.split("$#$").length>1&&(y=y.split("$#$")[1]),null!=y&&0!=y.length||(y="(EMPTY)"),b+='"'+n[C]+'"$#$'+y.replace(/&/g,"amp;").replace(/\+/g,"plus;")+"$#$"+l[C]+"|"):b+="All|"}b=a.t.encode64(b),g=a.t.encode64(g),t+="method=refreshGenericDisplayData",null!=this.fromSummaryScreen&&"true"==this.fromSummaryScreen?(t+="&scenarioID="+this.scenarioId,t+="&filter="+this.filter,t+="&selectedCurrencyGroup="+this.selectedCurrencyGroup,t+="&applyCurrencyThreshold="+this.applyCurrencyThreshold,t+="&fromSummaryScreen="+this.fromSummaryScreen):t+="&basequery="+this.baseQuery,t+="&maxPage="+this.jsonReader.getRowSize(),t+="&currentPage=1",t+="&selectedSort="+g,t+="&selectedFilter="+b.substring(0,b.length-1),t+="&totalCount="+this.jsonReader.getRowSize(),t+="&columnsWidth="+this.columnsWidth,t+="&columnsOrder="+this.columnsOrder,t+="&refColumns="+this.refColumns,t+="&facilityRefColumns="+this.facilityRefColumns,t+="&refParams="+this.refParams,t+="&facilityRefParams="+this.facilityRefParams,this.inputData.url=this.baseURL+"genericdisplay.do?"+t;this.inputData.send([])},t.prototype.rememberSortedColumn=function(){-1!=this.gGrid.selectedIndex&&1==this.gGrid.selectable&&(""==this.gGrid.uniqueColumn?this.prevSelectedItem.id=null:this.prevSelectedItem.id=this.gGrid.selectedItem[this.gGrid.uniqueColumn]),this.gGrid.columns[this.gGrid.sortColumnIndex].sortDescending=!this.gGrid.columns[this.gGrid.sortColumnIndex].sortDescending,this.prevColumnSort.ColumnName=this.gGrid.columns[this.gGrid.sortColumnIndex].name,this.prevColumnSort.Direction=this.gGrid.columns[this.gGrid.sortColumnIndex].sortDescending,this.prevColumnSort.Numeric="num"==this.gGrid.columns[this.gGrid.sortColumnIndex].columnType,this.prevColumnSort.PrevCol=this.gGrid.sortColumnIndex,this.gGrid.sortedGridColumn=this.gGrid.columns[this.gGrid.sortColumnIndex].name,0!=this.gGrid.columns[this.gGrid.sortColumnIndex].sortable&&(this.applySort=!0,this.dataRefreshFilterAndSort())},t.prototype.filterCall=function(e){this.dataRefreshFilterAndSort()},t.prototype.doHelp=function(){try{a.x.call("help")}catch(e){a.Wb.logError(e,"Predict","GenericDisplay","doHelp",0)}},t}(a.yb),d=[{path:"",component:h}],u=(r.l.forChild(d),function(){return function(){}}()),c=i("pMnS"),g=i("RChO"),m=i("t6HQ"),p=i("WFGK"),b=i("5FqG"),f=i("Ip0R"),y=i("gIcY"),R=i("t/Na"),C=i("sE5F"),S=i("OzfB"),v=i("T7CS"),x=i("S7LP"),I=i("6aHO"),P=i("WzUx"),D=i("A7o+"),G=i("zCE2"),w=i("Jg5P"),T=i("3R0m"),N=i("hhbb"),B=i("5rxC"),M=i("Fzqc"),L=i("21Lb"),O=i("hUWP"),j=i("3pJQ"),J=i("V9q+"),F=i("VDKW"),E=i("kXfT"),A=i("BGbe");i.d(t,"GenericDisplayModuleNgFactory",function(){return k}),i.d(t,"RenderType_GenericDisplay",function(){return W}),i.d(t,"View_GenericDisplay_0",function(){return z}),i.d(t,"View_GenericDisplay_Host_0",function(){return H}),i.d(t,"GenericDisplayNgFactory",function(){return q});var k=n.Gb(u,[],function(e){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[c.a,g.a,m.a,p.a,b.Cb,b.Pb,b.r,b.rc,b.s,b.Ab,b.Bb,b.Db,b.qd,b.Hb,b.k,b.Ib,b.Nb,b.Ub,b.yb,b.Jb,b.v,b.A,b.e,b.c,b.g,b.d,b.Kb,b.f,b.ec,b.Wb,b.bc,b.ac,b.sc,b.fc,b.lc,b.jc,b.Eb,b.Fb,b.mc,b.Lb,b.nc,b.Mb,b.dc,b.Rb,b.b,b.ic,b.Yb,b.Sb,b.kc,b.y,b.Qb,b.cc,b.hc,b.pc,b.oc,b.xb,b.p,b.q,b.o,b.h,b.j,b.w,b.Zb,b.i,b.m,b.Vb,b.Ob,b.Gb,b.Xb,b.t,b.tc,b.zb,b.n,b.qc,b.a,b.z,b.rd,b.sd,b.x,b.td,b.gc,b.l,b.u,b.ud,b.Tb,q]],[3,n.n],n.J]),n.Rb(4608,f.m,f.l,[n.F,[2,f.u]]),n.Rb(4608,y.c,y.c,[]),n.Rb(4608,y.p,y.p,[]),n.Rb(4608,R.j,R.p,[f.c,n.O,R.n]),n.Rb(4608,R.q,R.q,[R.j,R.o]),n.Rb(5120,R.a,function(e){return[e,new a.tb]},[R.q]),n.Rb(4608,R.m,R.m,[]),n.Rb(6144,R.k,null,[R.m]),n.Rb(4608,R.i,R.i,[R.k]),n.Rb(6144,R.b,null,[R.i]),n.Rb(4608,R.f,R.l,[R.b,n.B]),n.Rb(4608,R.c,R.c,[R.f]),n.Rb(4608,C.c,C.c,[]),n.Rb(4608,C.g,C.b,[]),n.Rb(5120,C.i,C.j,[]),n.Rb(4608,C.h,C.h,[C.c,C.g,C.i]),n.Rb(4608,C.f,C.a,[]),n.Rb(5120,C.d,C.k,[C.h,C.f]),n.Rb(5120,n.b,function(e,t){return[S.j(e,t)]},[f.c,n.O]),n.Rb(4608,v.a,v.a,[]),n.Rb(4608,x.a,x.a,[]),n.Rb(4608,I.a,I.a,[n.n,n.L,n.B,x.a,n.g]),n.Rb(4608,P.c,P.c,[n.n,n.g,n.B]),n.Rb(4608,P.e,P.e,[P.c]),n.Rb(4608,D.l,D.l,[]),n.Rb(4608,D.h,D.g,[]),n.Rb(4608,D.c,D.f,[]),n.Rb(4608,D.j,D.d,[]),n.Rb(4608,D.b,D.a,[]),n.Rb(4608,D.k,D.k,[D.l,D.h,D.c,D.j,D.b,D.m,D.n]),n.Rb(4608,P.i,P.i,[[2,D.k]]),n.Rb(4608,P.r,P.r,[P.L,[2,D.k],P.i]),n.Rb(4608,P.t,P.t,[]),n.Rb(4608,P.w,P.w,[]),n.Rb(1073742336,r.l,r.l,[[2,r.r],[2,r.k]]),n.Rb(1073742336,f.b,f.b,[]),n.Rb(1073742336,y.n,y.n,[]),n.Rb(1073742336,y.l,y.l,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,w.a,w.a,[]),n.Rb(1073742336,y.e,y.e,[]),n.Rb(1073742336,T.a,T.a,[]),n.Rb(1073742336,D.i,D.i,[]),n.Rb(1073742336,P.b,P.b,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,R.d,R.d,[]),n.Rb(1073742336,C.e,C.e,[]),n.Rb(1073742336,N.b,N.b,[]),n.Rb(1073742336,B.b,B.b,[]),n.Rb(1073742336,S.c,S.c,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,L.d,L.d,[]),n.Rb(1073742336,O.c,O.c,[]),n.Rb(1073742336,j.a,j.a,[]),n.Rb(1073742336,J.a,J.a,[[2,S.g],n.O]),n.Rb(1073742336,F.b,F.b,[]),n.Rb(1073742336,E.a,E.a,[]),n.Rb(1073742336,A.b,A.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,u,u,[]),n.Rb(256,R.n,"XSRF-TOKEN",[]),n.Rb(256,R.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,D.m,void 0,[]),n.Rb(256,D.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,r.i,function(){return[[{path:"",component:h}]]},[])])}),_=[[""]],W=n.Hb({encapsulation:0,styles:_,data:{}});function z(e){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{loadingImage:0}),n.Zb(402653184,3,{dataGridContainer:0}),n.Zb(402653184,4,{exportContainer:0}),n.Zb(402653184,5,{buttonBox:0}),n.Zb(402653184,6,{paginationData:0}),n.Zb(402653184,7,{numStepper:0}),n.Zb(402653184,8,{lastRefTime:0}),n.Zb(402653184,9,{lastRefTimeLabel:0}),n.Zb(402653184,10,{scenarioIdAndTitle:0}),n.Zb(402653184,11,{page:0}),n.Zb(402653184,12,{refreshButton:0}),n.Zb(402653184,13,{goToButton:0}),n.Zb(402653184,14,{closeButton:0}),(e()(),n.Jb(14,0,null,null,61,"SwtModule",[["height","100%"],["paddingBottom","10"],["paddingTop","10"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,i){var n=!0,l=e.component;"creationComplete"===t&&(n=!1!==l.onLoad()&&n);return n},b.ad,b.hb)),n.Ib(15,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"]},{creationComplete:"creationComplete"}),(e()(),n.Jb(16,0,null,0,59,"VBox",[["height","100%"],["id","allContainer"],["minWidth","800"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["styleName","vgroupAllContainer"],["width","100%"]],null,null,null,b.od,b.vb)),n.Ib(17,4440064,[["allContainer",4]],0,a.ec,[n.r,a.i,n.T],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],minWidth:[4,"minWidth"],paddingTop:[5,"paddingTop"],paddingBottom:[6,"paddingBottom"],paddingLeft:[7,"paddingLeft"],paddingRight:[8,"paddingRight"]},null),(e()(),n.Jb(18,0,null,0,57,"SwtCanvas",[["height","100%"],["id","movementSummary"],["width","100%"]],null,null,null,b.Nc,b.U)),n.Ib(19,4440064,[["movementSummary",4]],0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),n.Jb(20,0,null,0,55,"VBox",[["height","100%"],["id","Container"],["styleName","vgroupContainer"],["width","100%"]],null,null,null,b.od,b.vb)),n.Ib(21,4440064,[["Container",4]],0,a.ec,[n.r,a.i,n.T],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"]},null),(e()(),n.Jb(22,0,null,0,27,"SwtCanvas",[["height","40"],["width","100%"]],null,null,null,b.Nc,b.U)),n.Ib(23,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),n.Jb(24,0,null,0,25,"HBox",[["height","24"],["id","filterContainer"],["styleName","hgroupFilterContainer"],["width","100%"],["x","0"],["y","17"]],null,null,null,b.Dc,b.K)),n.Ib(25,4440064,[["filterContainer",4]],0,a.C,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"]},null),(e()(),n.Jb(26,0,null,0,13,"HBox",[["height","100%"],["styleName","hgroupGrid"],["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(27,4440064,null,0,a.C,[n.r,a.i],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"]},null),(e()(),n.Jb(28,0,null,0,11,"HBox",[["height","100%"],["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(29,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),n.Jb(30,0,null,0,5,"HBox",[["height","100%"],["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(31,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),n.Jb(32,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["paddingLeft","15"],["paddingTop","6"],["text","Scenario ID"]],null,null,null,b.Yc,b.fb)),n.Ib(33,4440064,null,0,a.vb,[n.r,a.i],{paddingTop:[0,"paddingTop"],paddingLeft:[1,"paddingLeft"],text:[2,"text"],fontWeight:[3,"fontWeight"]},null),(e()(),n.Jb(34,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","scenarioIdAndTitle"],["paddingLeft","15"],["paddingTop","6"]],null,null,null,b.Yc,b.fb)),n.Ib(35,4440064,[[10,4],["scenarioIdAndTitle",4]],0,a.vb,[n.r,a.i],{id:[0,"id"],paddingTop:[1,"paddingTop"],paddingLeft:[2,"paddingLeft"],fontWeight:[3,"fontWeight"]},null),(e()(),n.Jb(36,0,null,0,3,"HBox",[["height","100%"],["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(37,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),n.Jb(38,0,null,0,1,"SwtLabel",[["id","selectedEntity"],["paddingTop","6"],["styleName","labelLeft"]],null,null,null,b.Yc,b.fb)),n.Ib(39,4440064,[["selectedEntity",4]],0,a.vb,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],paddingTop:[2,"paddingTop"]},null),(e()(),n.Jb(40,0,null,0,7,"HBox",[["styleName","hgroupCenterData"]],null,null,null,b.Dc,b.K)),n.Ib(41,4440064,null,0,a.C,[n.r,a.i],{styleName:[0,"styleName"]},null),(e()(),n.Jb(42,0,null,0,5,"HBox",[["horizontalAlign","right"],["id","paginationData"],["width","280"]],null,null,null,b.Dc,b.K)),n.Ib(43,4440064,[[6,4],["paginationData",4]],0,a.C,[n.r,a.i],{id:[0,"id"],horizontalAlign:[1,"horizontalAlign"],width:[2,"width"]},null),(e()(),n.Jb(44,0,null,0,1,"SwtLabel",[["horizontalAlign","button"],["id","page"]],null,null,null,b.Yc,b.fb)),n.Ib(45,4440064,[[11,4],["page",4]],0,a.vb,[n.r,a.i],{id:[0,"id"],horizontalAlign:[1,"horizontalAlign"]},null),(e()(),n.Jb(46,0,null,0,1,"SwtCommonGridPagination",[["id","numStepper"]],null,null,null,b.Qc,b.Y)),n.Ib(47,2211840,[[7,4],["numStepper",4]],0,a.ib,[R.c,n.r],null,null),(e()(),n.Jb(48,0,null,0,1,"HBox",[],null,null,null,b.Dc,b.K)),n.Ib(49,4440064,null,0,a.C,[n.r,a.i],null,null),(e()(),n.Jb(50,0,null,0,1,"SwtCanvas",[["height","85%"],["id","dataGridContainer"],["width","100%"]],null,null,null,b.Nc,b.U)),n.Ib(51,4440064,[[3,4],["dataGridContainer",4]],0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),n.Jb(52,0,null,0,23,"SwtCanvas",[["height","35"],["width","100%"]],null,null,null,b.Nc,b.U)),n.Ib(53,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),n.Jb(54,0,null,0,21,"HBox",[["height","100%"],["id","controlContainer"],["styleName","hgroupFilterContainer"],["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(55,4440064,null,0,a.C,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"]},null),(e()(),n.Jb(56,0,null,0,7,"HBox",[["id","buttonBox"],["paddingLeft","8"],["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(57,4440064,[[5,4],["buttonBox",4]],0,a.C,[n.r,a.i],{id:[0,"id"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(e()(),n.Jb(58,0,null,0,1,"SwtButton",[["id","refreshButton"],["visible","false"]],null,[[null,"click"]],function(e,t,i){var n=!0,l=e.component;"click"===t&&(n=!1!==l.dataRefresh(i)&&n);return n},b.Mc,b.T)),n.Ib(59,4440064,[[12,4],["refreshButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],visible:[1,"visible"]},{onClick_:"click"}),(e()(),n.Jb(60,0,null,0,1,"SwtButton",[["id","closeButton"],["visible","false"]],null,[[null,"click"]],function(e,t,i){var n=!0,l=e.component;"click"===t&&(n=!1!==l.closeHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(61,4440064,[[14,4],["closeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],visible:[1,"visible"]},{onClick_:"click"}),(e()(),n.Jb(62,0,null,0,1,"SwtButton",[["id","goToButton"],["styleName","flexButton"],["visible","false"]],null,[[null,"click"]],function(e,t,i){var n=!0,l=e.component;"click"===t&&(n=!1!==l.goTo(i)&&n);return n},b.Mc,b.T)),n.Ib(63,4440064,[[13,4],["goToButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],visible:[2,"visible"]},{onClick_:"click"}),(e()(),n.Jb(64,0,null,0,11,"HBox",[["horizontalAlign","right"],["width","400"]],null,null,null,b.Dc,b.K)),n.Ib(65,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(e()(),n.Jb(66,0,null,0,1,"SwtText",[["fontWeight","bold"],["height","25"],["id","lastRefTimeLabel"],["paddingTop","0"],["visible","true"]],null,null,null,b.ld,b.qb)),n.Ib(67,4440064,[[9,4],["lastRefTimeLabel",4]],0,a.Pb,[n.r,a.i],{id:[0,"id"],height:[1,"height"],visible:[2,"visible"],paddingTop:[3,"paddingTop"],fontWeight:[4,"fontWeight"]},null),(e()(),n.Jb(68,0,null,0,1,"SwtText",[["height","25"],["id","lastRefTime"],["paddingTop","0"],["styleName","textLastRefTime"],["visible","true"]],null,null,null,b.ld,b.qb)),n.Ib(69,4440064,[[8,4],["lastRefTime",4]],0,a.Pb,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],height:[2,"height"],visible:[3,"visible"],paddingTop:[4,"paddingTop"]},null),(e()(),n.Jb(70,0,null,0,1,"DataExportMultiPage",[["id","exportContainer"],["right","45"],["tabIndex","6"],["verticalCenter","-1"]],null,null,null,b.yc,b.F)),n.Ib(71,4440064,[[4,4],["exportContainer",4]],0,a.q,[a.i,n.r],{id:[0,"id"],right:[1,"right"]},null),(e()(),n.Jb(72,0,null,0,1,"SwtHelpButton",[["buttonMode","true"],["enabled","true"],["id","helpButton"],["right","5"],["styleName","helpIcon"],["tabIndex","20"]],null,[[null,"click"]],function(e,t,i){var n=!0,l=e.component;"click"===t&&(n=!1!==l.doHelp()&&n);return n},b.Wc,b.db)),n.Ib(73,4440064,[["helpButton",4]],0,a.rb,[n.r,a.i],{id:[0,"id"],right:[1,"right"],styleName:[2,"styleName"],enabled:[3,"enabled"],tabIndex:[4,"tabIndex"],buttonMode:[5,"buttonMode"]},{onClick_:"click"}),(e()(),n.Jb(74,0,null,0,1,"SwtLoadingImage",[],null,null,null,b.Zc,b.gb)),n.Ib(75,114688,[[2,4],["loadingImage",4]],0,a.xb,[n.r],null,null)],function(e,t){e(t,15,0,"100%","100%","10","10");e(t,17,0,"allContainer","vgroupAllContainer","100%","100%","800","5","5","5","5");e(t,19,0,"movementSummary","100%","100%");e(t,21,0,"Container","vgroupContainer","100%","100%");e(t,23,0,"100%","40");e(t,25,0,"filterContainer","hgroupFilterContainer","100%","24");e(t,27,0,"hgroupGrid","100%","100%");e(t,29,0,"100%","100%");e(t,31,0,"100%","100%");e(t,33,0,"6","15","Scenario ID","bold");e(t,35,0,"scenarioIdAndTitle","6","15","normal");e(t,37,0,"100%","100%");e(t,39,0,"selectedEntity","labelLeft","6");e(t,41,0,"hgroupCenterData");e(t,43,0,"paginationData","right","280");e(t,45,0,"page","button"),e(t,47,0),e(t,49,0);e(t,51,0,"dataGridContainer","100%","85%");e(t,53,0,"100%","35");e(t,55,0,"controlContainer","hgroupFilterContainer","100%","100%");e(t,57,0,"buttonBox","100%","8");e(t,59,0,"refreshButton","false");e(t,61,0,"closeButton","false");e(t,63,0,"goToButton","flexButton","false");e(t,65,0,"right","400");e(t,67,0,"lastRefTimeLabel","25","true","0","bold");e(t,69,0,"lastRefTime","textLastRefTime","25","true","0");e(t,71,0,"exportContainer","45");e(t,73,0,"helpButton","5","helpIcon","true","20","true"),e(t,75,0)},null)}function H(e){return n.dc(0,[(e()(),n.Jb(0,0,null,null,1,"app-generic-display",[],null,null,null,z,W)),n.Ib(1,4440064,null,0,h,[a.i,n.r],null,null)],function(e,t){e(t,1,0)},null)}var q=n.Fb("app-generic-display",h,H,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);