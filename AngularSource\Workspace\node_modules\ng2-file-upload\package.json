{"_from": "ng2-file-upload@^1.3.0", "_id": "ng2-file-upload@1.4.0", "_inBundle": false, "_integrity": "sha512-3J/KPU/tyh/ad6TFeUbrxX+SihUj0iOEt2Zsg4EX7mB3GFiQscXOfcUOxCkBtPWWWaqt3azrYbVGzsYa3/7NzQ==", "_location": "/ng2-file-upload", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "ng2-file-upload@^1.3.0", "name": "ng2-file-upload", "escapedName": "ng2-file-upload", "rawSpec": "^1.3.0", "saveSpec": null, "fetchSpec": "^1.3.0"}, "_requiredBy": ["/swt-tool-box"], "_resolved": "https://registry.npmjs.org/ng2-file-upload/-/ng2-file-upload-1.4.0.tgz", "_shasum": "8dea28d573234c52af474ad2a4001b335271e5c4", "_spec": "ng2-file-upload@^1.3.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "bundleDependencies": false, "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "es2015": "fesm2015/ng2-file-upload.js", "esm2015": "esm2015/ng2-file-upload.js", "esm5": "esm5/ng2-file-upload.js", "fesm2015": "fesm2015/ng2-file-upload.js", "fesm5": "fesm5/ng2-file-upload.js", "main": "bundles/ng2-file-upload.umd.js", "metadata": "ng2-file-upload.metadata.json", "module": "fesm5/ng2-file-upload.js", "name": "ng2-file-upload", "peerDependencies": {"@angular/core": "*", "@angular/common": "*"}, "sideEffects": false, "typings": "ng2-file-upload.d.ts", "version": "1.4.0"}