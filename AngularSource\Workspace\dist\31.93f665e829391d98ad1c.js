(window.webpackJsonp=window.webpackJsonp||[]).push([[31],{y1Vb:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),a=i("mrSG"),l=(i("tp8m"),i("447K")),o=i("ZYCi"),s=i("wd/R"),c=i.n(s),u=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.inputData=new l.G(n.commonService),n.baseURL=l.Wb.getBaseURL(),n.requestParams=[],n.invalidComms="",n.jsonReader=new l.L,n.errorLocation=0,n.moduleId="Predict",n.screenVersion=new l.V(n.commonService),n.entityId=null,n.currencyCode=null,n.accountId=null,n.attributeId=null,n.dateFrom=null,n.dateTo=null,n.parentScreen=null,n.screenNameForPopup="Account Attributes Maintenance Screen",n.releaseDate="27 February 2019",n.versionNumber="1.0",n.selectedEffectiveDate=null,n.menuAccessId=0,n.menuAccess="",n.attributeChanged=!1,n.dateFormat=null,n.attributeSelected=!1,n.swtAlert=new l.bb(e),window.Main=n,n}return a.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.accountAttributesMaintenanceGrid=this.gridCanvas.addChild(l.hb),this.accountAttributesMaintenanceGrid.uniqueColumn="sequenceKey",this.addButton.label=l.Wb.getPredictMessage("button.add",null),this.changeButton.label=l.Wb.getPredictMessage("button.change",null),this.viewButton.label=l.Wb.getPredictMessage("button.view",null),this.deleteButton.label=l.Wb.getPredictMessage("button.delete",null),this.closeButton.label=l.Wb.getPredictMessage("button.close",null),this.goButton.label=l.Wb.getPredictMessage("button.go",null),this.goButton.toolTip=l.Wb.getPredictMessage("button.go",null),this.addButton.setFocus(),this.currencyLabel.text=l.Wb.getPredictMessage("label.accountattribute.currency",null),this.ccyCombo.toolTip=l.Wb.getPredictMessage("label.accountattribute.currency",null),this.entityLabel.text=l.Wb.getPredictMessage("label.accountattribute.entity",null),this.entityCombo.toolTip=l.Wb.getPredictMessage("tip.accountattribute.entity",null),this.accountLabel.text=l.Wb.getPredictMessage("label.accountattribute.accountId",null),this.accountCombo.toolTip=l.Wb.getPredictMessage("tip.accountattribute.account",null),this.attributeLabel.text=l.Wb.getPredictMessage("label.accountattribute.attribute",null),this.attributeCombo.toolTip=l.Wb.getPredictMessage("tip.accountattribute.attribute",null),this.startDateLabel.text=l.Wb.getPredictMessage("label.accountattribute.startdate",null),this.startDate.toolTip=l.Wb.getPredictMessage("tip.accountattribute.startdate",null),this.endDateLabel.text=l.Wb.getPredictMessage("label.accountattribute.enddate",null),this.endDate.toolTip=l.Wb.getPredictMessage("tip.accountattribute.enddate",null)},e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.onLoad=function(){var t=this;this.initializeMenus(),this.menuAccess=l.x.call("eval","menuAccessId"),this.menuAccess&&""!==this.menuAccess&&(this.menuAccessId=Number(this.menuAccess)),this.dateFormat=l.x.call("eval","dateFormat"),this.testDate=l.x.call("eval","dbDate"),this.entityId=l.x.call("eval","entityId"),this.currencyCode=l.x.call("eval","currencyCode"),this.accountId=l.x.call("eval","accountId"),this.attributeId=l.x.call("eval","attributeId"),this.selectedEffectiveDate=l.x.call("eval","effectiveDate"),this.dateFrom=l.x.call("eval","dateFrom"),this.dateTo=l.x.call("eval","dateTo"),this.parentScreen=l.x.call("eval","parentScreen"),this.attributeId?(this.attributeSelected=!0,this.startDate.selectedDate=null,this.endDate.selectedDate=null):(this.startDate.formatString=this.dateFormat,this.startDate.selectedDate=new Date(l.j.parseDate(this.testDate,this.dateFormat.toUpperCase())),this.endDate.formatString=this.dateFormat,this.endDate.selectedDate=new Date(l.j.parseDate(this.testDate,this.dateFormat.toUpperCase()))),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="accountAttribute.do?",this.actionMethod="method=displayAccountAttributes",this.requestParams=[],this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.requestParams.accountId=this.accountId,this.requestParams.attributeId=this.attributeId,this.requestParams.dateFrom=this.dateFrom,this.requestParams.dateTo=this.dateTo,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.accountAttributesMaintenanceGrid.onRowClick=function(e){t.cellLogic(e)},this.addButton.enabled=0==this.menuAccessId},e.prototype.inputDataResult=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.jsonReader.getRequestReplyStatus()){if(JSON.stringify(this.lastReceivedJSON)!==JSON.stringify(this.prevRecievedJSON)){if(this.accessIndex=parseInt(this.jsonReader.getScreenAttributes().accessInd,10),this.entityCombo.setComboData(this.jsonReader.getSelects(),!1),this.entityCombo.enabled="accountAttributeLastvalue"!=this.parentScreen,this.selectedEntity.text=this.entityCombo.selectedItem.value,this.ccyCombo.setComboData(this.jsonReader.getSelects(),!1),this.ccyCombo.enabled="accountAttributeLastvalue"!=this.parentScreen,this.selectedCcy.text=this.ccyCombo.selectedItem.value,this.accountCombo.setComboData(this.jsonReader.getSelects(),!1),this.accountCombo.enabled="accountAttributeLastvalue"!=this.parentScreen,this.selectedAccount.text=this.accountCombo.selectedItem.value,this.attributeSelected?this.attributeCombo.setComboData(this.jsonReader.getSelects(),!1):this.attributeCombo.setComboDataAndForceSelected(this.jsonReader.getSelects(),!1,""),this.attributeCombo.enabled="accountAttributeLastvalue"!=this.parentScreen,this.selectedAttribute.text=this.attributeCombo.selectedItem?this.attributeCombo.selectedItem.value:"",this.endDateLabel.visible=!0,this.endDate.visible=!0,null!=this.jsonReader.getSingletons().fromDate&&""!=l.Z.trim(this.jsonReader.getSingletons().fromDate)&&(this.testDate=String(this.jsonReader.getSingletons().fromDate),this.startDate.selectedDate=new Date(l.j.parseDate(this.testDate,this.dateFormat.toUpperCase()))),!this.jsonReader.isDataBuilding()){this.accountAttributesMaintenanceGrid.colWidthURL(this.baseURL+"accountAttribute.do?&screenName=accountAttributeValue"),this.accountAttributesMaintenanceGrid.colOrderURL(this.baseURL+"accountAttribute.do?&screenName=accountAttributeValue"),this.accountAttributesMaintenanceGrid.saveWidths=!0,this.accountAttributesMaintenanceGrid.saveColumnOrder=!0;var e={columns:this.jsonReader.getColumnData()};this.accountAttributesMaintenanceGrid.CustomGrid(e),this.accountAttributesMaintenanceGrid.doubleClickEnabled=!0,this.jsonReader.getGridData()?this.jsonReader.getGridData().size>0?(this.accountAttributesMaintenanceGrid.gridData=this.jsonReader.getGridData(),this.accountAttributesMaintenanceGrid.setRowSize=this.jsonReader.getRowSize(),this.accountAttributesMaintenanceGrid.allowMultipleSelection=!0):(this.accountAttributesMaintenanceGrid.dataProvider=null,this.accountAttributesMaintenanceGrid.selectedIndex=-1,this.disableOrEnableButtons(!1)):this.accountAttributesMaintenanceGrid.dataProvider=null,"accountAttributeLastvalue"!=this.parentScreen?(this.startDate.selectedDate=new Date(l.j.parseDate(this.testDate,this.dateFormat.toUpperCase())),this.endDate.selectedDate=new Date(l.j.parseDate(this.testDate,this.dateFormat.toUpperCase()))):(this.CheckEffectiveDateRaquiredValue(!0),this.populateGrid()),""===l.Z.trim(this.ccyCombo.selectedLabel)?(this.swtAlert.error(l.Wb.getPredictMessage("alert.currencyAccess",null),l.Wb.getPredictMessage("screen.error",null)),this.addButton.enabled=!1):0==this.attributeCombo.dataProvider.length||0!=this.accessIndex?this.addButton.enabled=!1:this.addButton.enabled=0==this.menuAccessId}this.prevRecievedJSON=this.lastReceivedJSON}}else this.swtAlert.error(l.Wb.getPredictMessage("label.errorContactSystemAdmin",null)+" \n"+this.jsonReader.getRequestReplyMessage(),l.Wb.getPredictMessage("screen.error",null))},e.prototype.inputDataGridResult=function(t){try{this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.jsonReader.getRequestReplyStatus()?JSON.stringify(this.lastReceivedJSON)!==JSON.stringify(this.prevRecievedJSON)&&(this.jsonReader.isDataBuilding()||(this.accountAttributesMaintenanceGrid.gridData=this.jsonReader.getGridData(),this.accountAttributesMaintenanceGrid.setRowSize=this.accountAttributesMaintenanceGrid.gridData.length),this.accountAttributesMaintenanceGrid.selectedIndex=-1,this.disableOrEnableButtons(!1),this.prevRecievedJSON=this.lastReceivedJSON):this.swtAlert.error(l.Wb.getPredictMessage("label.errorContactSystemAdmin",null)+" \n"+this.jsonReader.getRequestReplyMessage(),l.Wb.getPredictMessage("screen.error",null))}catch(e){console.log(e,this.moduleId,"AccountAttributeMaintenance","inputDataGridResult")}},e.prototype.inputDataFault=function(t){this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail},e.prototype.disableOrEnableButtons=function(t){t?(this.enableChangeButton(0===this.menuAccessId&&0==this.accessIndex),this.enableDeleteButton(0===this.menuAccessId&&0==this.accessIndex),this.enableViewButton(this.menuAccessId<2)):(this.enableChangeButton(!1),this.enableDeleteButton(!1),this.enableViewButton(!1))},e.prototype.cellLogic=function(t){try{this.accountAttributesMaintenanceGrid.selectedIndex>-1?this.disableOrEnableButtons(!0):this.disableOrEnableButtons(!1)}catch(e){l.Wb.logError(e,this.moduleId,"AccountAttributeMaintenance","cellClickEventHandler",this.errorLocation)}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.addChangeClickHandler=function(t){var e=null;e="Add"==t?"add":"Change"==t?"change":"view",this.entityId=this.entityCombo.selectedItem.content,this.currencyCode=this.ccyCombo.selectedItem.content,this.accountId=this.accountCombo.selectedItem.content,this.attributeId=this.attributeCombo.selectedItem?this.attributeCombo.selectedItem.content:"",this.dateFrom=this.startDate.text,this.dateTo=this.endDate.text;var i=this.accountAttributesMaintenanceGrid.gridData;this.actionMethod="accountAttributesAddMaintenance",this.actionMethod=this.actionMethod+"&entityId="+this.entityId,this.actionMethod=this.actionMethod+"&currencyCode="+this.currencyCode,this.actionMethod=this.actionMethod+"&accountId="+this.accountId,this.actionMethod=this.actionMethod+"&attributeId="+this.attributeId,this.actionMethod=this.actionMethod+"&dateFrom="+this.dateFrom,this.actionMethod=this.actionMethod+"&dateTo="+this.dateTo,"add"!==e?(this.actionMethod=this.actionMethod+"&selectedAccountId="+(i.length>0?this.accountAttributesMaintenanceGrid.selectedItem.accountId.content:""),this.actionMethod=this.actionMethod+"&effectivedateTime="+(i.length>0?this.accountAttributesMaintenanceGrid.selectedItem.effectivedateTime.content:""),this.actionMethod=this.actionMethod+"&value="+(i.length>0?this.accountAttributesMaintenanceGrid.selectedItem.value.content:""),this.actionMethod=this.actionMethod+"&seqKey="+(i.length>0?this.accountAttributesMaintenanceGrid.selectedItem.sequenceKey.content:"")):null!=this.startDate.selectedDate&&(this.actionMethod=this.actionMethod+"&effectivedateTime="+this.startDate.text),this.actionMethod=this.actionMethod+"&methodName="+e,this.attributeCombo.selectedItem?l.x.call("openChildWindow",this.actionMethod):this.swtAlert.warning(l.Wb.getPredictMessage("alert.warning.emptyAcctAttribute",null))},e.prototype.deleteHandler=function(){this.swtAlert.confirm(l.Wb.getPredictMessage("alert.columndelete",null),l.Wb.getPredictMessage("button.confirm",null),l.c.YES|l.c.NO,null,this.removeRecord.bind(this),null)},e.prototype.removeRecord=function(t){t.detail==l.c.YES&&(this.entityId=this.entityCombo.selectedItem.content,this.currencyCode=this.ccyCombo.selectedItem.content,this.accountId=this.accountCombo.selectedItem.content,this.attributeId=this.attributeCombo.selectedItem.content,this.dateFrom=null!=this.startDate.selectedDate?this.startDate.text:null,this.dateTo=null!=this.endDate.selectedDate?this.endDate.text:null,this.actionMethod="method=deleteAccountAttribute",this.requestParams.sequenceId=this.accountAttributesMaintenanceGrid.selectedItem.sequenceKey.content,this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.requestParams.accountId=this.accountId,this.requestParams.selectedAccountId=this.accountAttributesMaintenanceGrid.selectedItem.accountId.content,this.requestParams.effectivedateTime=this.accountAttributesMaintenanceGrid.selectedItem.effectivedateTime.content,this.requestParams.attributeId=this.attributeId,this.requestParams.dateFrom=this.dateFrom,this.requestParams.dateTo=this.dateTo,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenNameForPopup,this.versionNumber,this.releaseDate);var t=new l.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=l.Eb.createPopUp(this,l.M,{jsonData:this.lastReceivedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.height="400",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.display()},e.prototype.changeCombo=function(t){"accountCombo"!=t.target.id?"entityCombo"==t.target.id?(this.selectedEntity.text=this.entityCombo.selectedItem.value,this.updateData(!0)):(this.selectedCcy.text=this.ccyCombo.selectedItem.value,this.updateData()):this.selectedAccount.text=this.accountCombo.selectedItem.value},e.prototype.refreshParent=function(t,e,i,n){var a=this;this.entityId=t,this.currencyCode=e,this.accountId=i,this.attributeId=n,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){a.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="accountAttribute.do?",this.actionMethod="method=displayAccountAttributes",this.requestParams=[],this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.requestParams.accountId=this.accountId,this.requestParams.attributeId=this.attributeId,this.requestParams.resetDate=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.updateData=function(t){var e=this;void 0===t&&(t=!1),0!=this.menuAccessId&&(this.addButton.enabled=!1),this.entityId=this.entityCombo.selectedItem.content,this.currencyCode=this.ccyCombo.selectedItem.content,this.accountId=this.accountCombo.selectedItem.content,this.attributeId=this.attributeCombo.selectedItem?this.attributeCombo.selectedItem.content:"",""!==this.attributeId&&(this.attributeSelected=!0),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="accountAttribute.do?",this.actionMethod="method=displayAccountAttributes",this.requestParams=[],this.requestParams.entityId=this.entityId,this.requestParams.currencyCode=this.currencyCode,this.requestParams.accountId=this.accountId,this.requestParams.attributeId=this.attributeId,this.requestParams.resetDate=t,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.accountAttributesMaintenanceGrid.selectedIndex=-1},e.prototype.validateDateFieldValue=function(){this.validateDateField(this.startDate)||(this.startDate.text="")},e.prototype.validateDateField=function(t){try{var e=void 0,i=l.Wb.getPredictMessage("alert.validDate",null);if(t.text&&!(e=c()(t.text,this.dateFormat.toUpperCase(),!0)).isValid())return this.swtAlert.warning(i+"("+this.dateFormat.toUpperCase()+")"),!1;t.selectedDate=e.toDate()}catch(n){l.Wb.logError(n,l.Wb.SYSTEM_MODULE_ID,"AccountAttributeMaintenance","validateDateField",this.errorLocation)}return!0},e.prototype.checkDateRangeValue=function(){var t=!0;return this.startDate&&this.endDate&&!this.checkDates()&&(this.swtAlert.warning(l.Wb.getPredictMessage("alert.validation.dateRange",null),l.Wb.getPredictMessage("screen.alert.warning",null)),t=!1),t},e.prototype.checkDates=function(){try{var t=void 0,e=void 0;return this.startDate.text&&(t=c()(this.startDate.text,this.dateFormat.toUpperCase(),!0)),this.endDate.text&&(e=c()(this.endDate.text,this.dateFormat.toUpperCase(),!0)),!(!t&&e)&&!(t&&e&&e.isBefore(t))}catch(i){l.Wb.logError(i,this.moduleId,"AccountAttributeMaintenance","checkDates",this.errorLocation)}},e.prototype.CheckEffectiveDateRaquiredValue=function(t){void 0===t&&(t=!1),this.selectedAttribute.text=this.attributeCombo.selectedItem.value,0==l.x.call("isEffectiveDateRequired",this.attributeCombo.selectedItem.content)?(this.startDate.selectedDate=null,this.endDate.selectedDate=null,this.startDate.enabled=!1,this.endDate.enabled=!1):(this.startDate.enabled=!0,this.endDate.enabled=!0,t||null!=this.startDate.selectedDate||null!=this.endDate.selectedDate||(this.startDate.selectedDate=new Date(l.j.parseDate(this.testDate,this.dateFormat.toUpperCase())),this.endDate.selectedDate=new Date(l.j.parseDate(this.testDate,this.dateFormat.toUpperCase())))),this.attributeChanged=!0},e.prototype.populateGrid=function(){var t=this;try{var e=this.entityCombo.selectedItem.content,i=this.ccyCombo.selectedItem.content,n=this.accountCombo.selectedItem.content,a=this.attributeCombo.selectedItem?this.attributeCombo.selectedItem.content:"",o=this.startDate.text,s=this.endDate.text;if(!this.attributeCombo.selectedItem)return void this.swtAlert.warning(l.Wb.getPredictMessage("alert.warning.emptyAcctAttribute",null));this.checkDateRangeValue()&&(this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataGridResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="accountAttribute.do?",this.actionMethod="method=listAccountAttributes",this.requestParams=[],this.requestParams.entityId=e,this.requestParams.currencyCode=i,this.requestParams.accountId=n,this.requestParams.attributeId=a,this.requestParams.dateFrom=o,this.requestParams.dateTo=s,this.requestParams.parentScreen=this.parentScreen,this.requestParams.selectedEffectiveDate=this.selectedEffectiveDate,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))}catch(c){console.log(c,"AccountAttributeMaintenance","populateGrid")}},e.prototype.enableChangeButton=function(t){this.changeButton.enabled=t,this.changeButton.buttonMode=t},e.prototype.enableViewButton=function(t){this.viewButton.enabled=t,this.viewButton.buttonMode=t},e.prototype.enableDeleteButton=function(t){this.deleteButton.enabled=t,this.deleteButton.buttonMode=t},e.prototype.popupClosedEventHandler=function(t){try{this.accountAttributesMaintenanceGrid.selectable=!0,this.accountAttributesMaintenanceGrid.doubleClickEnabled=!0,1===this.accountAttributesMaintenanceGrid.selectedIndices.length&&this.accountAttributesMaintenanceGrid.selectable&&this.disableOrEnableButtons(!0)}catch(e){l.Wb.logError(e,this.moduleId,"AttributeMaintenance","popupClosedEventHandler",this.errorLocation)}},e.prototype.doHelp=function(){try{l.x.call("help")}catch(t){l.Wb.logError(t,this.moduleId,"AttributeMaintenance","doHelp",this.errorLocation)}},e.prototype.closeHeader=function(t){try{this.dispose()}catch(e){l.Wb.logError(e,l.Wb.SYSTEM_MODULE_ID,"AttributeMaintenance","closeCurrentTab",this.errorLocation)}},e.prototype.dispose=function(){try{this.accountAttributesMaintenanceGrid=null,this.requestParams=null,this.inputData=null,this.jsonReader=null,this.menuAccessId=null,this.lastReceivedJSON=null,this.prevRecievedJSON=null,l.x.call("close")}catch(t){l.Wb.logError(t,this.moduleId,"AttributeMaintenance","dispose",this.errorLocation)}},e}(l.yb),r=[{path:"",component:u}],d=(o.l.forChild(r),function(){return function(){}}()),h=i("pMnS"),b=i("RChO"),m=i("t6HQ"),g=i("WFGK"),p=i("5FqG"),I=i("Ip0R"),w=i("gIcY"),D=i("t/Na"),C=i("sE5F"),f=i("OzfB"),y=i("T7CS"),A=i("S7LP"),v=i("6aHO"),R=i("WzUx"),M=i("A7o+"),S=i("zCE2"),P=i("Jg5P"),B=i("3R0m"),L=i("hhbb"),J=i("5rxC"),W=i("Fzqc"),x=i("21Lb"),G=i("hUWP"),k=i("3pJQ"),O=i("V9q+"),q=i("VDKW"),T=i("kXfT"),F=i("BGbe");i.d(e,"AccountAttributeMaintenanceModuleNgFactory",function(){return E}),i.d(e,"RenderType_AccountAttributeMaintenance",function(){return _}),i.d(e,"View_AccountAttributeMaintenance_0",function(){return j}),i.d(e,"View_AccountAttributeMaintenance_Host_0",function(){return U}),i.d(e,"AccountAttributeMaintenanceNgFactory",function(){return V});var E=n.Gb(d,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[h.a,b.a,m.a,g.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,V]],[3,n.n],n.J]),n.Rb(4608,I.m,I.l,[n.F,[2,I.u]]),n.Rb(4608,w.c,w.c,[]),n.Rb(4608,w.p,w.p,[]),n.Rb(4608,D.j,D.p,[I.c,n.O,D.n]),n.Rb(4608,D.q,D.q,[D.j,D.o]),n.Rb(5120,D.a,function(t){return[t,new l.tb]},[D.q]),n.Rb(4608,D.m,D.m,[]),n.Rb(6144,D.k,null,[D.m]),n.Rb(4608,D.i,D.i,[D.k]),n.Rb(6144,D.b,null,[D.i]),n.Rb(4608,D.f,D.l,[D.b,n.B]),n.Rb(4608,D.c,D.c,[D.f]),n.Rb(4608,C.c,C.c,[]),n.Rb(4608,C.g,C.b,[]),n.Rb(5120,C.i,C.j,[]),n.Rb(4608,C.h,C.h,[C.c,C.g,C.i]),n.Rb(4608,C.f,C.a,[]),n.Rb(5120,C.d,C.k,[C.h,C.f]),n.Rb(5120,n.b,function(t,e){return[f.j(t,e)]},[I.c,n.O]),n.Rb(4608,y.a,y.a,[]),n.Rb(4608,A.a,A.a,[]),n.Rb(4608,v.a,v.a,[n.n,n.L,n.B,A.a,n.g]),n.Rb(4608,R.c,R.c,[n.n,n.g,n.B]),n.Rb(4608,R.e,R.e,[R.c]),n.Rb(4608,M.l,M.l,[]),n.Rb(4608,M.h,M.g,[]),n.Rb(4608,M.c,M.f,[]),n.Rb(4608,M.j,M.d,[]),n.Rb(4608,M.b,M.a,[]),n.Rb(4608,M.k,M.k,[M.l,M.h,M.c,M.j,M.b,M.m,M.n]),n.Rb(4608,R.i,R.i,[[2,M.k]]),n.Rb(4608,R.r,R.r,[R.L,[2,M.k],R.i]),n.Rb(4608,R.t,R.t,[]),n.Rb(4608,R.w,R.w,[]),n.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),n.Rb(1073742336,I.b,I.b,[]),n.Rb(1073742336,w.n,w.n,[]),n.Rb(1073742336,w.l,w.l,[]),n.Rb(1073742336,S.a,S.a,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,B.a,B.a,[]),n.Rb(1073742336,M.i,M.i,[]),n.Rb(1073742336,R.b,R.b,[]),n.Rb(1073742336,D.e,D.e,[]),n.Rb(1073742336,D.d,D.d,[]),n.Rb(1073742336,C.e,C.e,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,J.b,J.b,[]),n.Rb(1073742336,f.c,f.c,[]),n.Rb(1073742336,W.a,W.a,[]),n.Rb(1073742336,x.d,x.d,[]),n.Rb(1073742336,G.c,G.c,[]),n.Rb(1073742336,k.a,k.a,[]),n.Rb(1073742336,O.a,O.a,[[2,f.g],n.O]),n.Rb(1073742336,q.b,q.b,[]),n.Rb(1073742336,T.a,T.a,[]),n.Rb(1073742336,F.b,F.b,[]),n.Rb(1073742336,l.Tb,l.Tb,[]),n.Rb(1073742336,d,d,[]),n.Rb(256,D.n,"XSRF-TOKEN",[]),n.Rb(256,D.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,M.m,void 0,[]),n.Rb(256,M.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,o.i,function(){return[[{path:"",component:u}]]},[])])}),N=[[""]],_=n.Hb({encapsulation:0,styles:N,data:{}});function j(t){return n.dc(0,[n.Zb(*********,1,{_container:0}),n.Zb(*********,2,{gridCanvas:0}),n.Zb(*********,3,{entityCombo:0}),n.Zb(*********,4,{ccyCombo:0}),n.Zb(*********,5,{attributeCombo:0}),n.Zb(*********,6,{accountCombo:0}),n.Zb(*********,7,{loadingImage:0}),n.Zb(*********,8,{currencyLabel:0}),n.Zb(*********,9,{selectedCcy:0}),n.Zb(*********,10,{accountLabel:0}),n.Zb(*********,11,{attributeLabel:0}),n.Zb(*********,12,{endDateLabel:0}),n.Zb(*********,13,{startDateLabel:0}),n.Zb(*********,14,{entityLabel:0}),n.Zb(*********,15,{selectedEntity:0}),n.Zb(*********,16,{selectedAccount:0}),n.Zb(*********,17,{selectedAttribute:0}),n.Zb(*********,18,{startDate:0}),n.Zb(*********,19,{endDate:0}),n.Zb(*********,20,{addButton:0}),n.Zb(*********,21,{changeButton:0}),n.Zb(*********,22,{viewButton:0}),n.Zb(*********,23,{exportButton:0}),n.Zb(*********,24,{printButton:0}),n.Zb(*********,25,{deleteButton:0}),n.Zb(*********,26,{closeButton:0}),n.Zb(*********,27,{goButton:0}),n.Zb(*********,28,{helpIcon:0}),(t()(),n.Jb(28,0,null,null,111,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,a=t.component;"creationComplete"===e&&(n=!1!==a.onLoad()&&n);return n},p.ad,p.hb)),n.Ib(29,4440064,null,0,l.yb,[n.r,l.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(30,0,null,0,109,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(31,4440064,null,0,l.ec,[n.r,l.i,n.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),n.Jb(32,0,null,0,83,"SwtCanvas",[["height","33%"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(33,4440064,null,0,l.db,[n.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(34,0,null,0,81,"VBox",[["height","100%"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(35,4440064,null,0,l.ec,[n.r,l.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(36,0,null,0,79,"Grid",[],null,null,null,p.Cc,p.H)),n.Ib(37,4440064,null,0,l.z,[n.r,l.i],null,null),(t()(),n.Jb(38,0,null,0,13,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(39,4440064,null,0,l.B,[n.r,l.i],null,null),(t()(),n.Jb(40,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,p.Ac,p.I)),n.Ib(41,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(42,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","entityLabel"]],null,null,null,p.Yc,p.fb)),n.Ib(43,4440064,[[14,4],["entityLabel",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(44,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),n.Ib(45,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(46,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,l=t.component;"window:mousewheel"===e&&(a=!1!==n.Tb(t,47).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==l.changeCombo(i)&&a);return a},p.Pc,p.W)),n.Ib(47,4440064,[[3,4],["entityCombo",4]],0,l.gb,[n.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(48,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),n.Ib(49,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(50,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["textAlign","left"]],null,null,null,p.Yc,p.fb)),n.Ib(51,4440064,[[15,4],["selectedEntity",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],textAlign:[1,"textAlign"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(52,0,null,0,13,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(53,4440064,null,0,l.B,[n.r,l.i],null,null),(t()(),n.Jb(54,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,p.Ac,p.I)),n.Ib(55,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(56,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","currencyLabel"]],null,null,null,p.Yc,p.fb)),n.Ib(57,4440064,[[8,4],["currencyLabel",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(58,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),n.Ib(59,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(60,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","ccyCombo"],["width","85"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,l=t.component;"window:mousewheel"===e&&(a=!1!==n.Tb(t,61).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==l.changeCombo(i)&&a);return a},p.Pc,p.W)),n.Ib(61,4440064,[[4,4],["ccyCombo",4]],0,l.gb,[n.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(62,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),n.Ib(63,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(64,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCcy"]],null,null,null,p.Yc,p.fb)),n.Ib(65,4440064,[[9,4],["selectedCcy",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(66,0,null,0,13,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(67,4440064,null,0,l.B,[n.r,l.i],null,null),(t()(),n.Jb(68,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,p.Ac,p.I)),n.Ib(69,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(70,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","accountLabel"]],null,null,null,p.Yc,p.fb)),n.Ib(71,4440064,[[10,4],["accountLabel",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(72,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),n.Ib(73,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(74,0,null,0,1,"SwtComboBox",[["dataLabel","accounts"],["id","accountCombo"],["width","300"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,l=t.component;"window:mousewheel"===e&&(a=!1!==n.Tb(t,75).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==l.changeCombo(i)&&a);return a},p.Pc,p.W)),n.Ib(75,4440064,[[6,4],["accountCombo",4]],0,l.gb,[n.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(76,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),n.Ib(77,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(78,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedAccount"]],null,null,null,p.Yc,p.fb)),n.Ib(79,4440064,[[16,4],["selectedAccount",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(80,0,null,0,13,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(81,4440064,null,0,l.B,[n.r,l.i],null,null),(t()(),n.Jb(82,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,p.Ac,p.I)),n.Ib(83,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(84,0,null,0,1,"SwtLabel",[["id","attributeLabel"]],null,null,null,p.Yc,p.fb)),n.Ib(85,4440064,[[11,4],["attributeLabel",4]],0,l.vb,[n.r,l.i],{id:[0,"id"]},null),(t()(),n.Jb(86,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),n.Ib(87,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(88,0,null,0,1,"SwtComboBox",[["dataLabel","attributes"],["id","attributeCombo"],["prompt","Please select ..."],["width","300"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,l=t.component;"window:mousewheel"===e&&(a=!1!==n.Tb(t,89).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==l.CheckEffectiveDateRaquiredValue()&&a);return a},p.Pc,p.W)),n.Ib(89,4440064,[[5,4],["attributeCombo",4]],0,l.gb,[n.r,l.i],{dataLabel:[0,"dataLabel"],prompt:[1,"prompt"],width:[2,"width"],id:[3,"id"]},{change_:"change"}),(t()(),n.Jb(90,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),n.Ib(91,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(92,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedAttribute"]],null,null,null,p.Yc,p.fb)),n.Ib(93,4440064,[[17,4],["selectedAttribute",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(94,0,null,0,21,"GridRow",[],null,null,null,p.Bc,p.J)),n.Ib(95,4440064,null,0,l.B,[n.r,l.i],null,null),(t()(),n.Jb(96,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,p.Ac,p.I)),n.Ib(97,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(98,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","startDateLabel"]],null,null,null,p.Yc,p.fb)),n.Ib(99,4440064,[[13,4],["startDateLabel",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(100,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,p.Ac,p.I)),n.Ib(101,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(102,0,null,0,1,"SwtDateField",[["id","startDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"]],function(t,e,i){var n=!0,a=t.component;"change"===e&&(n=!1!==a.validateDateFieldValue()&&n);return n},p.Tc,p.ab)),n.Ib(103,4308992,[[18,4],["startDate",4]],0,l.lb,[n.r,l.i,n.T],{restrict:[0,"restrict"],id:[1,"id"],editable:[2,"editable"],width:[3,"width"]},{changeEventOutPut:"change"}),(t()(),n.Jb(104,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,p.Ac,p.I)),n.Ib(105,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(106,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","endDateLabel"]],null,null,null,p.Yc,p.fb)),n.Ib(107,4440064,[[12,4],["endDateLabel",4]],0,l.vb,[n.r,l.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(108,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,p.Ac,p.I)),n.Ib(109,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(110,0,null,0,1,"SwtDateField",[["id","endDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"]],function(t,e,i){var n=!0,a=t.component;"change"===e&&(n=!1!==a.validateDateFieldValue()&&n);return n},p.Tc,p.ab)),n.Ib(111,4308992,[[19,4],["endDate",4]],0,l.lb,[n.r,l.i,n.T],{restrict:[0,"restrict"],id:[1,"id"],editable:[2,"editable"],width:[3,"width"]},{changeEventOutPut:"change"}),(t()(),n.Jb(112,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,p.Ac,p.I)),n.Ib(113,4440064,null,0,l.A,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(114,0,null,0,1,"SwtButton",[["id","goButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.populateGrid()&&n);return n},p.Mc,p.T)),n.Ib(115,4440064,[[27,4],["goButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(116,0,null,0,1,"SwtCanvas",[["border","false"],["height","60%"],["id","gridCanvas"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(117,4440064,[[2,4],["gridCanvas",4]],0,l.db,[n.r,l.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(t()(),n.Jb(118,0,null,0,21,"SwtCanvas",[["height","7%"],["id","canvasButtons"]],null,null,null,p.Nc,p.U)),n.Ib(119,4440064,null,0,l.db,[n.r,l.i],{id:[0,"id"],height:[1,"height"]},null),(t()(),n.Jb(120,0,null,0,19,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(121,4440064,null,0,l.C,[n.r,l.i],{width:[0,"width"]},null),(t()(),n.Jb(122,0,null,0,11,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(123,4440064,null,0,l.C,[n.r,l.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(124,0,null,0,1,"SwtButton",[["id","addButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.addChangeClickHandler("Add")&&n);return n},p.Mc,p.T)),n.Ib(125,4440064,[[20,4],["addButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(126,0,null,0,1,"SwtButton",[["id","changeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.addChangeClickHandler("Change")&&n);return n},p.Mc,p.T)),n.Ib(127,4440064,[[21,4],["changeButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(128,0,null,0,1,"SwtButton",[["id","viewButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.addChangeClickHandler("View")&&n);return n},p.Mc,p.T)),n.Ib(129,4440064,[[22,4],["viewButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(130,0,null,0,1,"SwtButton",[["id","deleteButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.deleteHandler()&&n);return n},p.Mc,p.T)),n.Ib(131,4440064,[[25,4],["deleteButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(132,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.closeHeader(i)&&n);return n},p.Mc,p.T)),n.Ib(133,4440064,[[26,4],["closeButton",4]],0,l.cb,[n.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(134,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","5"],["paddingTop","5"]],null,null,null,p.Dc,p.K)),n.Ib(135,4440064,null,0,l.C,[n.r,l.i],{horizontalAlign:[0,"horizontalAlign"],paddingTop:[1,"paddingTop"],paddingRight:[2,"paddingRight"]},null),(t()(),n.Jb(136,0,null,0,1,"SwtLoadingImage",[["id","loadingImage"]],null,null,null,p.Zc,p.gb)),n.Ib(137,114688,[[7,4],["loadingImage",4]],0,l.xb,[n.r],null,null),(t()(),n.Jb(138,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doHelp()&&n);return n},p.Wc,p.db)),n.Ib(139,4440064,[[28,4],["helpIcon",4]],0,l.rb,[n.r,l.i],{id:[0,"id"]},{onClick_:"click"})],function(t,e){t(e,29,0,"100%","100%");t(e,31,0,"vBox1","100%","100%","5","5","5","5");t(e,33,0,"100%","33%");t(e,35,0,"100%","100%"),t(e,37,0),t(e,39,0);t(e,41,0,"10%");t(e,43,0,"entityLabel","bold");t(e,45,0,"50%");t(e,47,0,"entity","135","entityCombo");t(e,49,0,"20%");t(e,51,0,"selectedEntity","left","normal"),t(e,53,0);t(e,55,0,"10%");t(e,57,0,"currencyLabel","bold");t(e,59,0,"50%");t(e,61,0,"currency","85","ccyCombo");t(e,63,0,"20%");t(e,65,0,"selectedCcy","normal"),t(e,67,0);t(e,69,0,"10%");t(e,71,0,"accountLabel","bold");t(e,73,0,"50%");t(e,75,0,"accounts","300","accountCombo");t(e,77,0,"20%");t(e,79,0,"selectedAccount","normal"),t(e,81,0);t(e,83,0,"10%");t(e,85,0,"attributeLabel");t(e,87,0,"50%");t(e,89,0,"attributes","Please select ...","300","attributeCombo");t(e,91,0,"20%");t(e,93,0,"selectedAttribute","normal"),t(e,95,0);t(e,97,0,"10%");t(e,99,0,"startDateLabel","bold");t(e,101,0,"15%");t(e,103,0,"0-9/","startDate",!0,"70");t(e,105,0,"10%");t(e,107,0,"endDateLabel","bold");t(e,109,0,"25%");t(e,111,0,"0-9/","endDate",!0,"70");t(e,113,0,"20%");t(e,115,0,"goButton",!0);t(e,117,0,"gridCanvas","100%","60%","false");t(e,119,0,"canvasButtons","7%");t(e,121,0,"100%");t(e,123,0,"100%","5");t(e,125,0,"addButton",!0);t(e,127,0,"changeButton",!0);t(e,129,0,"viewButton",!0);t(e,131,0,"deleteButton",!0);t(e,133,0,"closeButton",!0);t(e,135,0,"right","5","5"),t(e,137,0);t(e,139,0,"helpIcon")},null)}function U(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-account-attribute",[],null,null,null,j,_)),n.Ib(1,4440064,null,0,u,[l.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var V=n.Fb("app-account-attribute",u,U,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);