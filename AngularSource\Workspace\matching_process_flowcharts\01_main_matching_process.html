<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Main Matching Process Flowchart</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .description {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Main Matching Process Flowchart</h1>
        
        <div class="mermaid">
flowchart TD
    A[Start Matching Process] --> B[Initialize Parameters]
    B --> C[Get Entity Configuration]
    C --> D[Set Position Level Thresholds]
    D --> E[Set Currency-specific Days Ahead]
    E --> F[Start Position Level Loop<br/>CNTR1: 1 to 11]
    
    F --> G{Determine Source Position Level<br/>Based on CNTR1}
    G -->|CNTR1=1| H1[Position = MAX_INTERNAL_POS]
    G -->|CNTR1=2| H2[Position = 9 or 8]
    G -->|CNTR1=3| H3[Position = 8 or 7]
    G -->|CNTR1=4| H4[Position = 7 or 6]
    G -->|CNTR1=5| H5[Position = 6 or 5]
    G -->|CNTR1=6| H6[Position = 5 or 4]
    G -->|CNTR1=7| H7[Position = 4 or 3]
    G -->|CNTR1=8| H8[Position = 3 or 2 or 1]
    G -->|CNTR1=9| H9[Position = MAX_INTERNAL_POS<br/>Stage = 2]
    G -->|CNTR1=10| H10[Position = PRE_ADVICE_POS]
    G -->|CNTR1=11| H11[Position = MAX_POS_OTHER_STAGE<br/>Stage = 2]
    
    H1 --> I[Check Match Quality Exists<br/>for Position Level]
    H2 --> I
    H3 --> I
    H4 --> I
    H5 --> I
    H6 --> I
    H7 --> I
    H8 --> I
    H9 --> I
    H10 --> I
    H11 --> I
    
    I --> J{Quality Config<br/>Exists?}
    J -->|No| K[Skip to Next Position]
    J -->|Yes| L[Open Source Cursor<br/>CR_INS_SOURCE]
    
    K --> F
    
    L --> M[Fetch Source Movement]
    M --> N{Source Found?}
    N -->|No| O[Close Source Cursor]
    O --> F
    
    N -->|Yes| P[Initialize Variables]
    P --> Q[Check Scheduler Status]
    Q --> R{Matching Enabled?}
    R -->|No - Disabled| S[Clean Up & Exit]
    R -->|Yes| T[Lock Source Movement]
    
    T --> U[Update Match Stage & Date]
    U --> V{Source Match Status = 'L'?}
    V -->|No| W[Skip Source]
    V -->|Yes| X[Set Currency Code for Quality]
    
    X --> Y[Determine Target Position Check]
    Y --> Z{Target Position Check Type}
    Z -->|'N'| AA[No Target Processing]
    Z -->|'H'| BB[Higher Position Targets]
    Z -->|'P'| CC[Pre-advice Targets]
    Z -->|Other| DD[Standard Targets]
    
    BB --> EE[Open CR_INSERT_TARGET_SUPP_PASS]
    CC --> FF[Open CR_INS_TARGET_PREADVICE]
    DD --> GG{Force Reference Only?}
    GG -->|Yes| HH[Open CR_INS_TARGET_REF_PASS]
    GG -->|No| II[Open CR_INS_TARGET_VD_AMT_PASS]
    
    EE --> JJ[Target Processing Loop]
    FF --> JJ
    HH --> JJ
    II --> JJ
    
    JJ --> KK[Fetch Target Movement]
    KK --> LL{Target Found?}
    LL -->|No| MM[Close Target Cursor]
    LL -->|Yes| NN[Calculate Quality Flag]
    
    NN --> OO{Quality > 0?}
    OO -->|No| PP[Skip Target]
    OO -->|Yes| QQ[Check Movement Already Exists]
    
    QQ --> RR{Already Exists?}
    RR -->|Yes| PP
    RR -->|No| SS[Determine Insert Flag]
    
    SS --> TT{Source Internal &<br/>Target External?}
    TT -->|Yes| UU[Set Insert Flag = Y]
    TT -->|No| VV{Both Internal?}
    
    VV -->|Yes| WW{Cross Reference<br/>Exists?}
    WW -->|Yes| UU
    WW -->|No| XX{Target = Pre-advice &<br/>Check = 'B'?}
    XX -->|Yes| YY[Check Higher Position Quality]
    XX -->|No| ZZ[Skip Insert]
    
    YY --> AAA{Higher Quality<br/>Exists?}
    AAA -->|Yes| UU
    AAA -->|No| ZZ
    
    VV -->|No| BBB{Source External?}
    BBB -->|Yes| CCC[External Target Processing]
    BBB -->|No| UU
    
    CCC --> DDD[Reference Validation]
    DDD --> EEE{References Match?}
    EEE -->|Yes| UU
    EEE -->|No| FFF[Break Match if Needed]
    
    UU --> GGG[Insert Target Movement]
    GGG --> HHH[Add Cross References]
    HHH --> III[Update Quality Counters]
    
    ZZ --> PP
    FFF --> PP
    PP --> JJ
    III --> JJ
    
    MM --> JJJ[Process Amount Total Logic]
    JJJ --> KKK[Inner Target Processing]
    KKK --> LLL[Update Match Actions]
    LLL --> MMM[Check Match Completion]
    
    MMM --> NNN{Match Complete?}
    NNN -->|Yes| OOO[Finalize Match]
    NNN -->|No| PPP[Continue Processing]
    
    OOO --> QQQ[Update Match Status]
    QQQ --> RRR[Clean Up Targets]
    RRR --> SSS[Remove Locks]
    
    PPP --> W
    SSS --> W
    W --> M
    
    AA --> TTT[End Position Processing]
    TTT --> F
    
    S --> UUU[End Process]
    
    style A fill:#e1f5fe
    style UUU fill:#ffebee
    style R fill:#fff3e0
    style OO fill:#f3e5f5
    style WW fill:#e8f5e8
    style NNN fill:#fff8e1
        </div>
        
        <div class="description">
            <h3>Description:</h3>
            <p>This flowchart shows the main matching process flow, including:</p>
            <ul>
                <li><strong>Position Level Processing:</strong> Iterates through 11 different position level configurations</li>
                <li><strong>Source Selection:</strong> Finds outstanding movements to match based on various criteria</li>
                <li><strong>Target Processing:</strong> Identifies potential matching targets using different cursor strategies</li>
                <li><strong>Quality Assessment:</strong> Calculates match quality based on multiple factors</li>
                <li><strong>Match Completion:</strong> Finalizes matches and updates system status</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>
