{"_from": "@angular/compiler@7.2.4", "_id": "@angular/compiler@7.2.4", "_inBundle": false, "_integrity": "sha512-+zyMzPCL45ePEV9nrnYJvhAVgp2Y19bDaq0f0YdZAqAjgDqHzXGGR6wX8GueyJWmUYWx5vwK6Apla4HwDrYA1w==", "_location": "/@angular/compiler", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/compiler@7.2.4", "name": "@angular/compiler", "escapedName": "@angular%2fcompiler", "scope": "@angular", "rawSpec": "7.2.4", "saveSpec": null, "fetchSpec": "7.2.4"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@angular/compiler/-/compiler-7.2.4.tgz", "_shasum": "133eb97fc3169ec9ff84f134eb9e3497fa37537e", "_spec": "@angular/compiler@7.2.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "description": "Angular - the compiler library", "es2015": "./fesm2015/compiler.js", "esm2015": "./esm2015/compiler.js", "esm5": "./esm5/compiler.js", "fesm2015": "./fesm2015/compiler.js", "fesm5": "./fesm5/compiler.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/compiler.umd.js", "module": "./fesm5/compiler.js", "name": "@angular/compiler", "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/http", "@angular/platform-server", "@angular/platform-webworker", "@angular/platform-webworker-dynamic", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/service-worker"]}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "sideEffects": true, "typings": "./compiler.d.ts", "version": "7.2.4"}