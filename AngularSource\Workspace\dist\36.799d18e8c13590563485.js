(window.webpackJsonp=window.webpackJsonp||[]).push([[36],{EDs9:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),a=i("mrSG"),o=i("447K"),l=i("ZYCi"),u=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.jsonReader=new o.L,n.inputData=new o.G(n.commonService),n.baseURL=o.Wb.getBaseURL(),n.actionMethod="",n.actionPath="",n.requestParams=[],n.invalidComms="",n.menuAccess="2",n.menuAccessId=2,n.versionNumber="1.0",n.errorLocation=0,n.moduleId="Predict",n.swtAlert=new o.bb(e),window.Main=n,n}return a.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.addButton.label=o.Wb.getPredictMessage("button.add",null),this.addButton.toolTip=o.Wb.getPredictMessage("button.add",null),this.changeButton.label=o.Wb.getPredictMessage("button.change",null),this.changeButton.toolTip=o.Wb.getPredictMessage("inputconfig.tooltip.change",null),this.deleteButton.label=o.Wb.getPredictMessage("button.delete",null),this.deleteButton.toolTip=o.Wb.getPredictMessage("button.delete",null),this.closeButton.label=o.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=o.Wb.getPredictMessage("tooltip.close",null),this.restrictionLabel.text=o.Wb.getPredictMessage("restriction.id",null),this.restrictionTxtInput.toolTip=o.Wb.getPredictMessage("tooltip.restriction",null),this.functionalGrpLabel.text=o.Wb.getPredictMessage("functGrp.id",null),this.functionalGrpCombo.toolTip=o.Wb.getPredictMessage("tooltip.functGrp",null),this.addButton.setFocus()},e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.onLoad=function(){var t=this;try{this.attributeUsageSummaryGrid=this.canvasGrid.addChild(o.hb),this.attributeUsageSummaryGrid.onRowClick=function(e){t.cellLogic(e)},this.attributeUsageSummaryGrid.uniqueColumn="attribute_id",this.menuAccess=o.x.call("eval","menuAccessId"),this.menuAccess&&""!==this.menuAccess&&(this.menuAccessId=Number(this.menuAccess)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionMethod="method=displayAttributeUsageSummary",this.actionPath="accountAttribute.do?",this.requestParams=[],this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){console.log(e,this.moduleId,"AttributeUsage","onLoad")}},e.prototype.inputDataResult=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),JSON.stringify(this.lastReceivedJSON)!==JSON.stringify(this.prevReceivedJSON))if(this.jsonReader.getRequestReplyStatus()){if(this.functionalGrpCombo.setComboData(this.jsonReader.getSelects(),!1),this.setRestrictionText(this.jsonReader.getScreenAttributes().restriction),this.selectedFunctGrp.text=this.functionalGrpCombo.selectedItem.value,!this.jsonReader.isDataBuilding()){0==this.attributeUsageSummaryGrid.columnDefinitions.length&&(this.attributeUsageSummaryGrid.CustomGrid(this.jsonReader.getColumnData()),this.attributeUsageSummaryGrid.colWidthURL(this.baseURL+"accountAttribute.do?&screenName=accountAttributeUsage"),this.attributeUsageSummaryGrid.colOrderURL(this.baseURL+"accountAttribute.do?&screenName=accountAttributeUsage"),this.attributeUsageSummaryGrid.saveWidths=!0,this.attributeUsageSummaryGrid.saveColumnOrder=!0),this.disableOrEnableButtons(!1),this.enableAddButton(0==this.menuAccessId);var e={columns:this.jsonReader.getColumnData()};null!==this.attributeUsageSummaryGrid&&void 0!==this.attributeUsageSummaryGrid||(this.attributeUsageSummaryGrid.componentID=this.jsonReader.getSingletons().screenid),this.attributeUsageSummaryGrid.doubleClickEnabled=!0,this.attributeUsageSummaryGrid.CustomGrid(e),this.jsonReader.getGridData().size>0?(this.attributeUsageSummaryGrid.gridData=this.jsonReader.getGridData(),this.attributeUsageSummaryGrid.setRowSize=this.jsonReader.getRowSize(),this.attributeUsageSummaryGrid.doubleClickEnabled=!0):(this.attributeUsageSummaryGrid.dataProvider=null,this.attributeUsageSummaryGrid.selectedIndex=-1)}this.prevReceivedJSON=this.lastReceivedJSON}else this.swtAlert.error(o.Wb.getPredictMessage("genericDisplayMonitor.contactAdmin",null)+this.jsonReader.getRequestReplyMessage(),o.Wb.getPredictMessage("screen.error",null))}catch(i){o.Wb.logError(i,this.moduleId,"AttributeUsage","inputDataResult",this.errorLocation)}},e.prototype.startOfComms=function(){try{this.loadingImage.setVisible(!0)}catch(t){o.Wb.logError(t,this.moduleId,"AttributeUsage","startOfComms",this.errorLocation)}},e.prototype.endOfComms=function(){try{this.loadingImage.setVisible(!1)}catch(t){o.Wb.logError(t,this.moduleId,"AttributeUsage","endOfComms",this.errorLocation)}},e.prototype.inputDataFault=function(t){try{this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail}catch(e){o.Wb.logError(e,this.moduleId,"AttributeUsage","inputDataFault",this.errorLocation)}},e.prototype.addChangeClickHandler=function(t){var e=this.functionalGrpCombo.selectedItem.content,i=o.x.call("checkExistingAccountAttributeDefintionList");if("Add"==t)i?o.x.call("openChildWindow","add","",e):this.swtAlert.error(o.Wb.getPredictMessage("attributeusagesummary.attributeNotFound",null));else{var n=this.attributeUsageSummaryGrid.selectedItem.attribute_id.content;o.x.call("openChildWindow","change",n,e)}},e.prototype.setRestrictionText=function(t){this.restrictionTxtInput.text="T"==t?o.Wb.getPredictMessage("type.text",null):"N"==t?o.Wb.getPredictMessage("type.numeric",null):"D"==t?o.Wb.getPredictMessage("type.date",null):""},e.prototype.refreshdetails=function(){var t=this;0!==this.menuAccessId&&(this.addButton.enabled=!1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataRefresh(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="accountAttribute.do?",this.actionMethod="method=displayAttributeUsageSummary",this.attributeUsageSummaryGrid.onRowClick=function(e){t.cellLogic(e)},this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.inputDataRefresh=function(t){this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),this.jsonReader.getRequestReplyStatus()&&(this.attributeUsageSummaryGrid.gridData=this.jsonReader.getGridData(),this.attributeUsageSummaryGrid.setRowSize=this.attributeUsageSummaryGrid.gridData.length,this.attributeUsageSummaryGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1)},e.prototype.deleteHandler=function(t){try{this.swtAlert.confirm(o.Wb.getPredictMessage("alert.columndelete",null),o.Wb.getPredictMessage("alert.deletion.confirm",null),o.c.YES|o.c.NO,null,this.removeRecord.bind(this),null)}catch(e){o.Wb.logError(e,this.moduleId,"AttributeUsage","deleteAcctAttributeHDR",this.errorLocation)}},e.prototype.removeRecord=function(t){var e=this;try{t.detail===o.c.YES&&(this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataRefresh(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.requestParams=[],this.requestParams.functionalGrp=this.functionalGrpCombo.selectedItem.content,this.requestParams.attributeId=this.attributeUsageSummaryGrid.selectedItem.attribute_id.content,this.requestParams.restriction=this.jsonReader.getScreenAttributes().restriction,this.actionMethod="method=deleteAttributeUsageAttribute",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))}catch(i){o.Wb.logError(i,this.moduleId,"AttributeUsage","deleteCurrency",this.errorLocation)}},e.prototype.updateData=function(){var t=this;this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault,this.inputData.encodeURL=!1,this.requestParams=[],this.requestParams.functionalGrp=this.functionalGrpCombo.selectedItem.content,this.actionMethod="method=displayAttributeUsageSummary",this.actionPath="accountAttribute.do?",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.attributeUsageSummaryGrid.refresh(),this.attributeUsageSummaryGrid.selectedIndex=-1,this.disableOrEnableButtons(!1)},e.prototype.doHelp=function(){try{o.x.call("help")}catch(t){o.Wb.logError(t,this.moduleId,"AttributeUsage","doHelp",this.errorLocation)}},e.prototype.functGrpCombo=function(t){this.updateData()},e.prototype.cellLogic=function(t){try{0!=this.menuAccessId?(this.addButton.enabled=!1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1):(this.attributeUsageSummaryGrid.selectedIndex>-1&&this.attributeUsageSummaryGrid.selectable?this.disableOrEnableButtons(!0):this.disableOrEnableButtons(!1),t.stopPropagation())}catch(e){o.Wb.logError(e,this.moduleId,"AttributeUsage","cellClickEventHandler",this.errorLocation)}},e.prototype.closeHeader=function(t){try{this.dispose()}catch(e){o.Wb.logError(e,o.Wb.SYSTEM_MODULE_ID,"AttributeUsage","closeHeader",this.errorLocation)}},e.prototype.dispose=function(){try{this.attributeUsageSummaryGrid=null,this.requestParams=null,this.inputData=null,this.jsonReader=null,this.menuAccess=null,this.lastReceivedJSON=null,this.prevReceivedJSON=null,o.x.call("close")}catch(t){o.Wb.logError(t,this.moduleId,"AttributeUsage","dispose",this.errorLocation)}},e.prototype.disableOrEnableButtons=function(t){t?(this.enableChangeButton(0==this.menuAccessId),this.enableDeleteButton(0==this.menuAccessId)):(this.enableChangeButton(!1),this.enableDeleteButton(!1))},e.prototype.enableAddButton=function(t){this.addButton.enabled=t,this.addButton.buttonMode=t},e.prototype.enableChangeButton=function(t){this.changeButton.enabled=t,this.changeButton.buttonMode=t},e.prototype.enableDeleteButton=function(t){this.deleteButton.enabled=t,this.deleteButton.buttonMode=t},e}(o.yb),s=[{path:"",component:u}],r=(l.l.forChild(s),function(){return function(){}}()),d=i("pMnS"),c=i("RChO"),h=i("t6HQ"),b=i("WFGK"),g=i("5FqG"),p=i("Ip0R"),m=i("gIcY"),R=i("t/Na"),f=i("sE5F"),y=i("OzfB"),S=i("T7CS"),C=i("S7LP"),w=i("6aHO"),D=i("WzUx"),B=i("A7o+"),I=i("zCE2"),G=i("Jg5P"),v=i("3R0m"),U=i("hhbb"),L=i("5rxC"),A=i("Fzqc"),k=i("21Lb"),M=i("hUWP"),x=i("3pJQ"),T=i("V9q+"),O=i("VDKW"),P=i("kXfT"),W=i("BGbe");i.d(e,"AttributeUsageModuleNgFactory",function(){return J}),i.d(e,"RenderType_AttributeUsage",function(){return E}),i.d(e,"View_AttributeUsage_0",function(){return F}),i.d(e,"View_AttributeUsage_Host_0",function(){return N}),i.d(e,"AttributeUsageNgFactory",function(){return H});var J=n.Gb(r,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[d.a,c.a,h.a,b.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,H]],[3,n.n],n.J]),n.Rb(4608,p.m,p.l,[n.F,[2,p.u]]),n.Rb(4608,m.c,m.c,[]),n.Rb(4608,m.p,m.p,[]),n.Rb(4608,R.j,R.p,[p.c,n.O,R.n]),n.Rb(4608,R.q,R.q,[R.j,R.o]),n.Rb(5120,R.a,function(t){return[t,new o.tb]},[R.q]),n.Rb(4608,R.m,R.m,[]),n.Rb(6144,R.k,null,[R.m]),n.Rb(4608,R.i,R.i,[R.k]),n.Rb(6144,R.b,null,[R.i]),n.Rb(4608,R.f,R.l,[R.b,n.B]),n.Rb(4608,R.c,R.c,[R.f]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.g,f.b,[]),n.Rb(5120,f.i,f.j,[]),n.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),n.Rb(4608,f.f,f.a,[]),n.Rb(5120,f.d,f.k,[f.h,f.f]),n.Rb(5120,n.b,function(t,e){return[y.j(t,e)]},[p.c,n.O]),n.Rb(4608,S.a,S.a,[]),n.Rb(4608,C.a,C.a,[]),n.Rb(4608,w.a,w.a,[n.n,n.L,n.B,C.a,n.g]),n.Rb(4608,D.c,D.c,[n.n,n.g,n.B]),n.Rb(4608,D.e,D.e,[D.c]),n.Rb(4608,B.l,B.l,[]),n.Rb(4608,B.h,B.g,[]),n.Rb(4608,B.c,B.f,[]),n.Rb(4608,B.j,B.d,[]),n.Rb(4608,B.b,B.a,[]),n.Rb(4608,B.k,B.k,[B.l,B.h,B.c,B.j,B.b,B.m,B.n]),n.Rb(4608,D.i,D.i,[[2,B.k]]),n.Rb(4608,D.r,D.r,[D.L,[2,B.k],D.i]),n.Rb(4608,D.t,D.t,[]),n.Rb(4608,D.w,D.w,[]),n.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),n.Rb(1073742336,p.b,p.b,[]),n.Rb(1073742336,m.n,m.n,[]),n.Rb(1073742336,m.l,m.l,[]),n.Rb(1073742336,I.a,I.a,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,m.e,m.e,[]),n.Rb(1073742336,v.a,v.a,[]),n.Rb(1073742336,B.i,B.i,[]),n.Rb(1073742336,D.b,D.b,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,R.d,R.d,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,U.b,U.b,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,y.c,y.c,[]),n.Rb(1073742336,A.a,A.a,[]),n.Rb(1073742336,k.d,k.d,[]),n.Rb(1073742336,M.c,M.c,[]),n.Rb(1073742336,x.a,x.a,[]),n.Rb(1073742336,T.a,T.a,[[2,y.g],n.O]),n.Rb(1073742336,O.b,O.b,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,W.b,W.b,[]),n.Rb(1073742336,o.Tb,o.Tb,[]),n.Rb(1073742336,r,r,[]),n.Rb(256,R.n,"XSRF-TOKEN",[]),n.Rb(256,R.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,B.m,void 0,[]),n.Rb(256,B.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,l.i,function(){return[[{path:"",component:u}]]},[])])}),_=[[""]],E=n.Hb({encapsulation:0,styles:_,data:{}});function F(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{canvasGrid:0}),n.Zb(402653184,3,{loadingImage:0}),n.Zb(402653184,4,{restrictionTxtInput:0}),n.Zb(402653184,5,{functionalGrpCombo:0}),n.Zb(402653184,6,{restrictionLabel:0}),n.Zb(402653184,7,{functionalGrpLabel:0}),n.Zb(402653184,8,{selectedFunctGrp:0}),n.Zb(402653184,9,{addButton:0}),n.Zb(402653184,10,{changeButton:0}),n.Zb(402653184,11,{deleteButton:0}),n.Zb(402653184,12,{closeButton:0}),n.Zb(402653184,13,{helpIcon:0}),(t()(),n.Jb(13,0,null,null,43,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,a=t.component;"creationComplete"===e&&(n=!1!==a.onLoad()&&n);return n},g.ad,g.hb)),n.Ib(14,4440064,null,0,o.yb,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(15,0,null,0,41,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(16,4440064,null,0,o.ec,[n.r,o.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(17,0,null,0,17,"SwtCanvas",[["height","18%"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(18,4440064,null,0,o.db,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(19,0,null,0,15,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(20,4440064,null,0,o.ec,[n.r,o.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(21,0,null,0,7,"HBox",[["height","50%"]],null,null,null,g.Dc,g.K)),n.Ib(22,4440064,null,0,o.C,[n.r,o.i],{height:[0,"height"]},null),(t()(),n.Jb(23,0,null,0,1,"SwtLabel",[["text","Functional Group"],["width","115"]],null,null,null,g.Yc,g.fb)),n.Ib(24,4440064,[[7,4],["functionalGrpLabel",4]],0,o.vb,[n.r,o.i],{width:[0,"width"],text:[1,"text"]},null),(t()(),n.Jb(25,0,null,0,1,"SwtComboBox",[["dataLabel","functionalGrpList"],["id","functionalGrpCombo"],["toolTip","Select a functional group"],["width","227"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,o=t.component;"window:mousewheel"===e&&(a=!1!==n.Tb(t,26).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==o.functGrpCombo(i)&&a);return a},g.Pc,g.W)),n.Ib(26,4440064,[[5,4],["functionalGrpCombo",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],toolTip:[1,"toolTip"],width:[2,"width"],id:[3,"id"]},{change_:"change"}),(t()(),n.Jb(27,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedFunctGrp"],["paddingLeft","20"],["paddingTop","5"],["text",""]],null,null,null,g.Yc,g.fb)),n.Ib(28,4440064,[[8,4],["selectedFunctGrp",4]],0,o.vb,[n.r,o.i],{id:[0,"id"],paddingTop:[1,"paddingTop"],paddingLeft:[2,"paddingLeft"],text:[3,"text"],fontWeight:[4,"fontWeight"]},null),(t()(),n.Jb(29,0,null,0,5,"HBox",[["height","50%"]],null,null,null,g.Dc,g.K)),n.Ib(30,4440064,null,0,o.C,[n.r,o.i],{height:[0,"height"]},null),(t()(),n.Jb(31,0,null,0,1,"SwtLabel",[["height","28"],["width","115"]],null,null,null,g.Yc,g.fb)),n.Ib(32,4440064,[[6,4],["restrictionLabel",4]],0,o.vb,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(33,0,null,0,1,"SwtTextInput",[["editable","false"],["id","restrictionTxtInput"],["width","75"]],null,null,null,g.kd,g.sb)),n.Ib(34,4440064,[[4,4],["restrictionTxtInput",4]],0,o.Rb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],editable:[2,"editable"]},null),(t()(),n.Jb(35,0,null,0,1,"SwtCanvas",[["height","72%"],["id","canvasGrid"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(36,4440064,[[2,4],["canvasGrid",4]],0,o.db,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(37,0,null,0,19,"SwtCanvas",[["height","10%"],["id","canvasButtons"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(38,4440064,null,0,o.db,[n.r,o.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(39,0,null,0,17,"HBox",[["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(40,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(41,0,null,0,9,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(42,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(43,0,null,0,1,"SwtButton",[["id","addButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.addChangeClickHandler("Add")&&n);return n},g.Mc,g.T)),n.Ib(44,4440064,[[9,4],["addButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(45,0,null,0,1,"SwtButton",[["id","changeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.addChangeClickHandler("Change")&&n);return n},g.Mc,g.T)),n.Ib(46,4440064,[[10,4],["changeButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(47,0,null,0,1,"SwtButton",[["id","deleteButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.deleteHandler("View")&&n);return n},g.Mc,g.T)),n.Ib(48,4440064,[[11,4],["deleteButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(49,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.closeHeader(i)&&n);return n},g.Mc,g.T)),n.Ib(50,4440064,[[12,4],["closeButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(51,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","5"],["paddingTop","2"]],null,null,null,g.Dc,g.K)),n.Ib(52,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],paddingTop:[1,"paddingTop"],paddingRight:[2,"paddingRight"]},null),(t()(),n.Jb(53,0,null,0,1,"SwtLoadingImage",[],null,null,null,g.Zc,g.gb)),n.Ib(54,114688,[[3,4],["loadingImage",4]],0,o.xb,[n.r],null,null),(t()(),n.Jb(55,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","groups-of-rules"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doHelp()&&n);return n},g.Wc,g.db)),n.Ib(56,4440064,null,0,o.rb,[n.r,o.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"})],function(t,e){t(e,14,0,"100%","100%");t(e,16,0,"100%","100%","5","5","5","5");t(e,18,0,"100%","18%");t(e,20,0,"100%","100%");t(e,22,0,"50%");t(e,24,0,"115","Functional Group");t(e,26,0,"functionalGrpList","Select a functional group","227","functionalGrpCombo");t(e,28,0,"selectedFunctGrp","5","20","","normal");t(e,30,0,"50%");t(e,32,0,"115","28");t(e,34,0,"restrictionTxtInput","75","false");t(e,36,0,"canvasGrid","100%","72%");t(e,38,0,"canvasButtons","100%","10%");t(e,40,0,"100%");t(e,42,0,"100%","5");t(e,44,0,"addButton",!0);t(e,46,0,"changeButton",!0);t(e,48,0,"deleteButton",!0);t(e,50,0,"closeButton",!0);t(e,52,0,"right","2","5"),t(e,54,0);t(e,56,0,"helpIcon","true",!0,"groups-of-rules")},null)}function N(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-attribute-usage",[],null,null,null,F,E)),n.Ib(1,4440064,null,0,u,[o.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var H=n.Fb("app-attribute-usage",u,N,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);