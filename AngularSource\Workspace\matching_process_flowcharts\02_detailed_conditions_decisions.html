<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Detailed Matching Process - Conditions & Decisions</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.0/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 100%;
            overflow-x: auto;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .diagram-container {
            width: 100%;
            height: 800px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: auto;
            background: white;
            position: relative;
        }
        .mermaid {
            min-width: 1200px;
            min-height: 800px;
            transform-origin: top left;
        }
        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .zoom-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 5px 10px;
            margin: 0 2px;
            border-radius: 3px;
            cursor: pointer;
        }
        .zoom-btn:hover {
            background: #1e7e34;
        }
        .description {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #28a745;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Detailed Matching Process - Key Conditions & Decision Points</h1>

        <div class="diagram-container">
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomIn()">Zoom In</button>
                <button class="zoom-btn" onclick="zoomOut()">Zoom Out</button>
                <button class="zoom-btn" onclick="resetZoom()">Reset</button>
            </div>
            <div class="mermaid" id="diagram">
flowchart TD
    A[SP_MATCHING_PROCEDURE Start] --> B[Initialize Configuration]
    B --> B1[Get PRE_ADVICE_POS from S_ENTITY]
    B1 --> B2[Get POS_LEVEL_THRESHOLD<br/>Default: 6]
    B2 --> B3[Get DEBUG mode<br/>Default: N]
    B3 --> B4[Get PREADVICE_SEARCH_ALWAYS<br/>Default: N]
    B4 --> B5[Get PREADVICE_PREDICT_STRATEGY<br/>Default: 1]
    B5 --> B6[Get ACC_LINKING_EXEMPTION_LEVEL<br/>Default: 7]
    B6 --> B7[Get ENFORCE_REFERENCE_SELECTION<br/>Default: N]
    B7 --> B8[Get PREADVICE_SEARCH_POSITIONS<br/>Default: 6,4]
    B8 --> B9[Set Days Ahead:<br/>EUR=0, Others=7]
    
    B9 --> C[Position Loop: CNTR1 = 1 to 11]
    
    C --> D{CNTR1 Value}
    D -->|1| E1[Source Pos = MAX_INTERNAL_POS<br/>Stage = 1]
    D -->|2| E2[Source Pos = 9 or 8<br/>Stage = 1]
    D -->|3| E3[Source Pos = 8 or 7<br/>Stage = 1]
    D -->|4| E4[Source Pos = 7 or 6<br/>Stage = 1]
    D -->|5| E5[Source Pos = 6 or 5<br/>Stage = 1]
    D -->|6| E6[Source Pos = 5 or 4<br/>Stage = 1]
    D -->|7| E7[Source Pos = 4 or 3<br/>Stage = 1]
    D -->|8| E8[Source Pos = 3/2/1<br/>Stage = 1]
    D -->|9| E9[Source Pos = MAX_INTERNAL_POS<br/>Stage = 2]
    D -->|10| E10[Source Pos = PRE_ADVICE_POS<br/>Stage = 1]
    D -->|11| E11[Source Pos = MAX_POS_OTHER_STAGE<br/>Stage = 2]
    
    E1 --> F[Check P_MATCH_QUALITY exists]
    E2 --> F
    E3 --> F
    E4 --> F
    E5 --> F
    E6 --> F
    E7 --> F
    E8 --> F
    E9 --> F
    E10 --> F
    E11 --> F
    
    F --> G{Quality Config<br/>Found?}
    G -->|No| H[Next Position]
    G -->|Yes| I[Open CR_INS_SOURCE Cursor]
    
    H --> C
    
    I --> J[Source Selection Criteria:<br/>• HOST/ENTITY/CURRENCY match<br/>• MATCH_STATUS = 'L'<br/>• PREDICT_STATUS = 'E' or 'I'<br/>• TO_MATCH_DATE ≤ Input Date<br/>• TO_MATCH_STAGE = Current Stage<br/>• AMOUNT ≥ Filter Threshold<br/>• VALUE_DATE conditions<br/>• POSITION_LEVEL = Current Level]
    
    J --> K[Fetch Source Movement]
    K --> L{Source Found?}
    L -->|No| M[Close Cursor → Next Position]
    L -->|Yes| N[Initialize Source Variables]
    
    M --> C
    
    N --> O[Check S_SCHEDULER Status]
    O --> P{Job Status?}
    P -->|Disabled 'D'| Q[Clean Up & Exit with Status 'D']
    P -->|Enabled 'E'| R[Lock Source Movement]
    
    R --> S[Update TO_MATCH_STAGE + 1<br/>Update TO_MATCH_DATE]
    S --> T{Source MATCH_STATUS = 'L'?}
    T -->|No| U[Skip to Next Source]
    T -->|Yes| V[Set Currency Code for Quality]
    
    V --> W[Determine Target Position Check]
    W --> X{Target Check Logic}
    X -->|Source ≤ MAX_INTERNAL_POS<br/>& Pre-advice Search| Y1[Check = 'P' - Pre-advice]
    X -->|Source > MAX_INTERNAL_POS<br/>& Supplement Flag = 'Y'| Y2[Check = 'H' - Higher Position]
    X -->|Source ≤ MAX_INTERNAL_POS<br/>& Normal| Y3[Check = 'B' - Both Positions]
    X -->|Source > MAX_INTERNAL_POS<br/>& Normal| Y4[Check = 'L' - Lower Positions]
    X -->|No Targets| Y5[Check = 'N' - No Processing]
    
    Y1 --> Z1[Open CR_INS_TARGET_PREADVICE]
    Y2 --> Z2[Open CR_INSERT_TARGET_SUPP_PASS]
    Y3 --> Z3{Force Reference Only?}
    Y4 --> Z3
    Y5 --> AA[Skip Target Processing]
    
    Z3 -->|Yes| Z4[Open CR_INS_TARGET_REF_PASS]
    Z3 -->|No| Z5[Open CR_INS_TARGET_VD_AMT_PASS]
    
    Z1 --> BB[Target Processing Loop]
    Z2 --> BB
    Z4 --> BB
    Z5 --> BB
    
    BB --> CC[Fetch Target Movement]
    CC --> DD{Target Found?}
    DD -->|No| EE[Process Amount Total Logic]
    DD -->|Yes| FF[Calculate Quality using<br/>FN_UPDATE_P_B_MATQUAL_POSLEVEL]
    
    FF --> GG{Quality > 0?}
    GG -->|No| HH[Skip Target]
    GG -->|Yes| II[Check if Movement Already Exists<br/>in P_B_TARGET_MOVEMENTS]
    
    II --> JJ{Already Exists?}
    JJ -->|Yes| HH
    JJ -->|No| KK[Determine Insert Flag Logic]
    
    KK --> LL{Source Internal &<br/>Target External?}
    LL -->|Yes| MM[Insert Flag = Y]
    LL -->|No| NN{Both Internal?}
    
    NN -->|Yes| OO{Cross Reference Check}
    OO -->|FNISCROSSREFEXISTS = 'Y'| MM
    OO -->|No Cross Ref| PP{Target ≠ Pre-advice<br/>& Check = 'B'?}
    PP -->|Yes| QQ[Check Higher Position Quality]
    PP -->|No| RR[Insert Flag = N]
    
    QQ --> SS{Higher Quality<br/>Count > 0?}
    SS -->|Yes| MM
    SS -->|No| RR
    
    NN -->|No| TT{Source External?}
    TT -->|Yes| UU[External Target Processing<br/>Reference Validation]
    TT -->|No| MM
    
    UU --> VV{References Match?}
    VV -->|Yes| MM
    VV -->|No| WW[Break Match Logic]
    
    MM --> XX[Insert into P_B_TARGET_MOVEMENTS<br/>Insert into P_B_TARGET_CROSS_REF<br/>Insert into P_MOVEMENT_LOCK]
    XX --> YY[Update Quality Counters by Position]
    
    RR --> HH
    WW --> HH
    HH --> BB
    YY --> BB
    
    EE --> ZZ[SP_MATCHING_AMOUNT_TOTAL]
    ZZ --> AAA[Inner Target Processing<br/>Quality-based Selection]
    AAA --> BBB[SP_UPDATE_MATCH_ACTIONS]
    BBB --> CCC[Check Match Completion]
    
    CCC --> DDD{Match Complete?}
    DDD -->|Yes| EEE[Update Match Status<br/>Clean Up Targets<br/>Remove Locks]
    DDD -->|No| FFF[Continue to Next Source]
    
    EEE --> FFF
    FFF --> U
    U --> K
    
    AA --> GGG[End Position Processing]
    GGG --> C
    
    Q --> HHH[End Process]
    
    style A fill:#e1f5fe
    style HHH fill:#ffebee
    style P fill:#fff3e0
    style GG fill:#f3e5f5
    style OO fill:#e8f5e8
    style DDD fill:#fff8e1
    style MM fill:#e8f5e8
    style RR fill:#ffebee
            </div>
        </div>
        
        <div class="description">
            <h3>Description:</h3>
            <p>This detailed flowchart focuses on configuration parameters and key decision points:</p>
            <ul>
                <li><strong>Configuration Parameters:</strong> Shows all configurable parameters with their default values</li>
                <li><strong>Position Level Logic:</strong> Details how each CNTR1 value determines the source position level</li>
                <li><strong>Target Check Logic:</strong> Complex logic for determining target processing strategy</li>
                <li><strong>Cross-Reference Validation:</strong> Critical decision point for internal vs external movements</li>
                <li><strong>Insert Flag Logic:</strong> Determines whether targets should be included in matching</li>
            </ul>
        </div>
    </div>

    <script>
        let currentZoom = 1;

        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true
            }
        });

        function zoomIn() {
            currentZoom += 0.2;
            applyZoom();
        }

        function zoomOut() {
            currentZoom = Math.max(0.2, currentZoom - 0.2);
            applyZoom();
        }

        function resetZoom() {
            currentZoom = 1;
            applyZoom();
        }

        function applyZoom() {
            const diagram = document.getElementById('diagram');
            diagram.style.transform = `scale(${currentZoom})`;
        }
    </script>
</body>
</html>
