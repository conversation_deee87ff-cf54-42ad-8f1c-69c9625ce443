{"_from": "@types/node@8.9.4", "_id": "@types/node@8.9.4", "_inBundle": false, "_integrity": "sha512-dSvD36qnQs78G1BPsrZFdPpvLgMW/dnvr5+nTW2csMs5TiP9MOXrjUbnMZOEwnIuBklXtn7b6TPA2Cuq07bDHA==", "_location": "/@types/node", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/node@8.9.4", "name": "@types/node", "escapedName": "@types%2fnode", "scope": "@types", "rawSpec": "8.9.4", "saveSpec": null, "fetchSpec": "8.9.4"}, "_requiredBy": ["#DEV:/", "/@types/webpack-sources"], "_resolved": "https://registry.npmjs.org/@types/node/-/node-8.9.4.tgz", "_shasum": "dfd327582a06c114eb6e0441fa3d6fab35edad48", "_spec": "@types/node@8.9.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "Microsoft TypeScript", "url": "http://typescriptlang.org"}, {"name": "DefinitelyTyped", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/parambirs"}, {"name": "<PERSON>", "url": "https://github.com/tellnes"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/WilcoBakker"}, {"name": "<PERSON>", "url": "https://github.com/octo-sniffle"}, {"name": "Chigozirim C.", "url": "https://github.com/smac89"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Flarna"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/mwiktorczyk"}, {"name": "wwwy3y3", "url": "https://github.com/wwwy3y3"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/DeividasBakanas"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/kjin"}, {"name": "Alvis <PERSON>", "url": "https://github.com/alvis"}, {"name": "<PERSON>", "url": "https://github.com/OliverJAsh"}, {"name": "<PERSON>", "url": "https://github.com/eps1lon"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>-<PERSON>-CK"}, {"name": "<PERSON>", "url": "https://github.com/jkomyno"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/hoo29"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for Node.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/node", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.1", "typesPublisherContentHash": "c57e8e80888027a0dcee1e3158dc571f6a16b8488c41d19cb72f78e3d52ddb45", "version": "8.9.4"}