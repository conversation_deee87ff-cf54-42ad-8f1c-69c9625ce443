{"_from": "@types/sizzle@*", "_id": "@types/sizzle@2.3.9", "_inBundle": false, "_integrity": "sha512-xzLEyKB50yqCUPUJkIsrVvoWNfFUbIZI+RspLWt8u+tIW/BetMBZtgV2LY/2o+tYH8dRvQ+eoPf3NdhQCcLE2w==", "_location": "/@types/sizzle", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/sizzle@*", "name": "@types/sizzle", "escapedName": "@types%2fsizzle", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/jquery", "/angular-slickgrid/@types/jquery"], "_resolved": "https://registry.npmjs.org/@types/sizzle/-/sizzle-2.3.9.tgz", "_shasum": "d4597dbd4618264c414d7429363e3f50acb66ea2", "_spec": "@types/sizzle@*", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\@types\\jquery", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/leonard-thieu"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for sizzle", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/sizzle", "license": "MIT", "main": "", "name": "@types/sizzle", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/sizzle"}, "scripts": {}, "typeScriptVersion": "4.8", "types": "index.d.ts", "typesPublisherContentHash": "0695816c2a1aaa2ea7eb8dc8c445f1aa526abd767d88970c1c4da9db0cf34bbe", "version": "2.3.9"}