{"_from": "moment-mini@^2.24.0", "_id": "moment-mini@2.29.4", "_inBundle": false, "_integrity": "sha512-uhXpYwHFeiTbY9KSgPPRoo1nt8OxNVdMVoTBYHfSEKeRkIkwGpO+gERmhuhBtzfaeOyTkykSrm2+noJBgqt3Hg==", "_location": "/moment-mini", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "moment-mini@^2.24.0", "name": "moment-mini", "escapedName": "moment-mini", "rawSpec": "^2.24.0", "saveSpec": null, "fetchSpec": "^2.24.0"}, "_requiredBy": ["/angular-slickgrid", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/moment-mini/-/moment-mini-2.29.4.tgz", "_shasum": "cbbcdc58ce1b267506f28ea6668dbe060a32758f", "_spec": "moment-mini@^2.24.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\angular-slickgrid", "author": {"name": "<PERSON>, <PERSON><PERSON><PERSON>, Moment.js contributors"}, "bugs": {"url": "https://github.com/ksloan/moment-mini/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Parse, validate, manipulate, and display dates", "homepage": "https://github.com/ksloan/moment-mini#readme", "keywords": ["moment", "date", "time", "parse", "format", "validate", "i18n", "l10n", "ender"], "license": "MIT", "main": "moment.min.js", "name": "moment-mini", "repository": {"type": "git", "url": "git+https://github.com/ksloan/moment-mini.git"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "typings": "moment.d.ts", "version": "2.29.4"}