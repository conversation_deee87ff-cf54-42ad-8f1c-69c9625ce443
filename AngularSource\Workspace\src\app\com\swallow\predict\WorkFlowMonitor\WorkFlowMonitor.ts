import { WorkFlowOptionsPopUp } from './WorkFlowOptions/WorkFlowOptionsPopUp';
import {Component, ElementRef, NgModule, OnDestroy, OnInit, ViewChild} from '@angular/core';
import {
  CommonService,
  ExternalInterface,
  HTTPComms,
  JSONReader,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtLoadingImage,
  SwtModule,
  SwtToolBoxModule,
  SwtLabel, SwtComboBox, SwtCheckBox, SwtText,
  SwtTabNavigator, Tab, SwtSummary, Logger, Timer, StringUtils,
  SwtPopUpManager, SwtUtil, ContextMenuItem, ScreenVersion, LinkButton, HBox, JSONViewer, HDividedEndResizeEvent, CustomTree, HDividedBox, VBox, HashMap, SwtRadioButtonGroup, SwtRadioItem, SwtDateField
} from 'swt-tool-box';
import {RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";
import {RatePopUp} from '../RatePopUp/RatePopUp';
import { CustomSummary } from '../AlertInstanceSummary/CustomSummary/CustomSummary';
import { AttributeXML } from '../AlertInstanceSummary/AttributeXML/AttributeXML';
const $ = require('jquery');
declare var instanceElement: any;
declare var require: any;
var parser = require('fast-xml-parser');
var prettyData = require('pretty-data');
@Component({
  selector: 'app-work-flow-monitor',
  templateUrl: './WorkFlowMonitor.html',
  styleUrls: ['./WorkFlowMonitor.css']
})
export class WorkFlowMonitor extends SwtModule implements OnInit, OnDestroy  {

  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Work Flow Monitor', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(commonService);
    window["Main"] = this;
  }


  @ViewChild('lblComboEntity') lblComboEntity: SwtLabel;
  @ViewChild('selectedEntity') selectedEntity: SwtLabel;
  @ViewChild('labelExc3txtcurrLabel') labelExc3txtcurrLabel: SwtLabel;
  @ViewChild('lblComboCcy') lblComboCcy: SwtLabel;
  @ViewChild('selectedCcy') selectedCcy: SwtLabel;
  @ViewChild('currLabel') currLabel: SwtLabel;
  @ViewChild('labelExc0txt') labelExc0txt: SwtLabel;
  @ViewChild('labelExc1txt') labelExc1txt: SwtLabel;
  @ViewChild('labelExc2txt') labelExc2txt: SwtLabel;
  @ViewChild('labelExc3txt') labelExc3txt: SwtLabel;
  @ViewChild('labelExc4txt') labelExc4txt: SwtLabel;
  @ViewChild('labelExc5txt') labelExc5txt: SwtLabel;
  @ViewChild('labelExc6txt') labelExc6txt: SwtLabel;
  @ViewChild('labelExc7txt') labelExc7txt: SwtLabel;
  @ViewChild('labelExc8txt') labelExc8txt: SwtLabel;
  @ViewChild('labelExcTotalTxt') labelExcTotalTxt: SwtLabel;
  @ViewChild('labelIncTotalValue') labelIncTotalValue: SwtLabel;
  @ViewChild('labelExcTotalValue') labelExcTotalValue: SwtLabel;
  @ViewChild('statusLabel') statusLabel: SwtLabel;
  @ViewChild('resolvedOnLbl') resolvedOnLbl: SwtLabel;

  @ViewChild('valueInc0Btn') valueInc0Btn: LinkButton;
  @ViewChild('valueExc0Btn') valueExc0Btn: LinkButton;
  @ViewChild('valueInc1Btn') valueInc1Btn: LinkButton;
  @ViewChild('valueExc1Btn') valueExc1Btn: LinkButton;
  @ViewChild('valueInc2Btn') valueInc2Btn: LinkButton;
  @ViewChild('valueExc2Btn') valueExc2Btn: LinkButton;
  @ViewChild('valueInc3Btn') valueInc3Btn: LinkButton;
  @ViewChild('valueExc3Btn') valueExc3Btn: LinkButton;
  @ViewChild('valueInc4Btn') valueInc4Btn: LinkButton;
  @ViewChild('valueExc4Btn') valueExc4Btn: LinkButton;
  @ViewChild('valueInc5Btn') valueInc5Btn: LinkButton;
  @ViewChild('valueExc5Btn') valueExc5Btn: LinkButton;
  @ViewChild('valueInc6Btn') valueInc6Btn: LinkButton;
  @ViewChild('valueExc6Btn') valueExc6Btn: LinkButton;
  @ViewChild('valueInc7Btn') valueInc7Btn: LinkButton;
  @ViewChild('valueExc7Btn') valueExc7Btn: LinkButton;
  @ViewChild('valueInc8Btn') valueInc8Btn: LinkButton;
  @ViewChild('valueExc8Btn') valueExc8Btn: LinkButton;

  @ViewChild('entityCombo') entityCombo: SwtComboBox;
  @ViewChild('ccyCombo') ccyCombo: SwtComboBox;
  @ViewChild('currencyThreshold') currencyThreshold: SwtCheckBox;
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  @ViewChild('outTxt') outTxt: SwtText;
  @ViewChild('excTxt') excTxt: SwtText;
  @ViewChild('mvtTxt') mvtTxt: SwtText;
  @ViewChild('incTxt') incTxt: SwtText;
  @ViewChild('lostConnectionText') lostConnectionText: SwtText;
  @ViewChild('lastRefTime') lastRefTime: SwtText;
  @ViewChild('lastRefTimeLabel') lastRefTimeLabel: SwtText;
  @ViewChild('dataBuildingText') dataBuildingText: SwtText;

  /*********SwtCanvas*********/
  @ViewChild('mainGroup') mainGroup: HBox;
  @ViewChild('mainCanvas') mainCanvas: SwtCanvas;
  @ViewChild('controlContainer') controlContainer: SwtCanvas;
  /*********Tab*********/
  @ViewChild('tabCategoryList') tabCategoryList: SwtTabNavigator;
  @ViewChild('tabList') tabList: SwtTabNavigator;
  @ViewChild('displayContainerPredict') displayContainerPredict: Tab;
  @ViewChild('displayContainerPCM') displayContainerPCM: Tab;
  /*********SwtSummary*********/
  // @ViewChild('summary') summary: SwtSummary;
  /*********SwtButton*********/
  //Mantis 5028 new buttons added --start
  @ViewChild('resolveButton') resolveButton: SwtButton;
  @ViewChild('reActiveButton') reActiveButton: SwtButton;
  @ViewChild('goToButton') goToButton: SwtButton;  
  @ViewChild('detailsButton') detailsButton: SwtButton;  
  //Mantis 5028 new buttons added --end
  @ViewChild('refreshButton') refreshButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  @ViewChild('optionsButton') optionsButton: SwtButton;
  
  @ViewChild('rateButton') rateButton: SwtButton;
  @ViewChild('helpIcon') helpIcon: SwtButton;
  @ViewChild('logonBtn') logonBtn: LinkButton;
  @ViewChild('pdf') pdf: SwtButton;
  /*********HDividedBox*********/
  @ViewChild("treeDivider") treeDivider: HDividedBox;
  /***********SwtRadioButtonGroup***********/
  @ViewChild('status') status: SwtRadioButtonGroup;
  @ViewChild('all') all: SwtRadioItem;
  @ViewChild('active') active: SwtRadioItem;
  @ViewChild('resolved') resolved: SwtRadioItem;
  @ViewChild('pending') pending: SwtRadioItem;
  @ViewChild('overdue') overdue: SwtRadioItem;
  @ViewChild('allOpen') allOpen: SwtRadioItem;
  /***********SwtDateField***********/
  @ViewChild('resolvedOnDate') resolvedOnDate: SwtDateField;
  private swtAlert: SwtAlert;
  private logger: Logger;

  /**
   * Data Objects
   **/
  private  jsonReader: JSONReader = new JSONReader();
  private  summaryXMLReader: JSONReader =new JSONReader();
  private  lastRecievedJSON;
  private  prevRecievedJSON;
  private  lastRecievedSummaryJSON;
  private  prevRecievedSummaryJSON;
  public lastReceivedWidthJSON;
  public screenVersion  = new ScreenVersion(this.commonService) ;
  public showJSONPopup: any;


  // XMLList to hold data result
  public  posLvlData  = null;

  /**
   * Communication Objects
   **/
    // WorkFlow http comm object
  private  inputData: HTTPComms= new HTTPComms(this.commonService);
  private  detailsData: HTTPComms= new HTTPComms(this.commonService);
  private  summaryData: HTTPComms= new HTTPComms(this.commonService);
  private  updateRefreshRate: HTTPComms=new HTTPComms(this.commonService);
  private  saveSettingsData: HTTPComms=new HTTPComms(this.commonService);
  public sendData: HTTPComms = new HTTPComms(this.commonService);
  private  baseURL = SwtUtil.getBaseURL();
  private  actionMethod="";
  private  scenarioSummaryActionMethod="";
  private  actionPath="";
  private  scenarioSummaryActionPath="";
  private  requestParams=[];
  public   moduleId = 'Predict';
  private errorLocation = 0;
  private optionIdTypes;
  /**
   * Refresh Strings
   **/
  private  SUMMARY_REFRESH='summaryRefresh';
  private  WORKFLOW_REFRESH='workFlowRefresh';
  public refreshRatePopup: any;

  private  colorsBlind =[0x00FF00, 0x0000FF, 0xFFFFFF, 0x80FFFF, 0xFF0000, 0xFF962D, 0xA7A7A7, 0xFFFF93, 0xAF6161, 0x000000];
  private  colorsNonBlind =[0xEF7651, 0xE9C836, 0x6FB35F, 0xA1AECF, 0xFF00FF, 0x00FFFF, 0x999966, 0x9966FF, 0xffff80, 0xff99cc];

  private  colors= this.colorsNonBlind;
  private  colorBlindMode=false;
  public  pieColorArr=[];

  /**
   * Timer Objects
   **/
  private  autoRefresh: Timer = null;
  private  refreshRate=10;
  private lastApplyThresholdSetting = '';


  /**
   * Logic Objects
   **/
  private  comboOpen=false;
  private  comboChange=false;
  public  tabData=[];
  public  tabDataCategory=[];
  private currDividerPosition: number;
  /**
   * Popup Objects
   **/
  private showJsonPopup = null;


  /* - START -- Screen Name and Version Number ---- */
  private  screenName = SwtUtil.getPredictMessage('label.workflowMonitor', null) ;
  private  versionNumber="1.0.0026";
  private releaseDate = '30 May 2019';
  /* - END -- Screen Name and Version Number ---- */



  private  indexSelected=0;
  private  existingEntityId: string=null;
  private  firstLoad = true;
  private  lastSelectedIndex = -1;
  private  fisrtTablastSelectedId: string = null;
  private  secondTablastSelectedId: string= null;
  private  lastSelectedId = "" ;
  private  firstTabTreeOpenedItems = [];
  private  firstTabTreeClosedItems = [];
  private  secondTabTreeOpenedItems = [];
  private  secondTabTreeClosedItems = [];
  private  previousSelectedTabIndex = -1;
  private screenOption;
  private scenarioId="";
  private scenarioRows=[];
  private win: any;
  private attributeJSON;
  private facilityGui="";
  private sqlParams = "";
  public  scenarioTitle = '';
  private  lastSelectedNode = "" ;
  private summary:CustomSummary;
  private source;
  private statusChanged = false;
  private dateFormat: string;
  private displayedDate;
  private formatIso : string = "yyyy-mm-dd";
  private formatIsoTime : string = "yyyy-mm-dd hh24:mi:ss";
  private currencyFormat: string;

  ngOnInit() {
    instanceElement = this;
    this.summary = <CustomSummary>this.mainGroup.addChild(CustomSummary);
    this.summary.width="100%";
    this.summary.height="100%";
    this.summary.summaryGrid.clientSideSort = false;
    this.summary.summaryGrid.clientSideFilter = false;
    this.summary.expandFirstLoad=false;
    this.summary.summaryGrid.clientSideSort = false;
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.refreshButton.toolTip = SwtUtil.getPredictMessage('tooltip.refreshWindow', null);
    this.refreshButton.label = SwtUtil.getPredictMessage('button.Refresh', null);

    this.optionsButton.toolTip = SwtUtil.getPredictMessage('button.option', null);
    this.optionsButton.label = SwtUtil.getPredictMessage('button.option', null);


    this.rateButton.toolTip = SwtUtil.getPredictMessage('tooltip.rateButton', null);
    this.rateButton.label = SwtUtil.getPredictMessage('accountmonitorbutton.Rate', null);
    this.lblComboEntity.text = SwtUtil.getPredictMessage('scenarioSummary.Entity', null);
    this.lastRefTimeLabel.text = SwtUtil.getPredictMessage('screen.lastRefresh', null);
    this.entityCombo.toolTip = SwtUtil.getPredictMessage('tooltip.selectEntityid', null);
    this.lostConnectionText.text = SwtUtil.getPredictMessage('screen.connectionError', null);
    this.pdf.toolTip = SwtUtil.getPredictMessage('label.exportPDF', null);
    this.resolvedOnLbl.text= SwtUtil.getPredictMessage('scenarioSummary.resolvedOn', null); 
    //Mantis 5028 new buttons added 
    this.resolveButton.toolTip = SwtUtil.getPredictMessage('tooltip.resolveButton', null);
    this.resolveButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.resolve', null);
    this.reActiveButton.toolTip = SwtUtil.getPredictMessage('tooltip.reActivateButton', null);
    this.reActiveButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.reActivate', null);
    this.goToButton.toolTip = SwtUtil.getPredictMessage('tooltip.goTo', null);
    this.goToButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.goTo', null);
    this.detailsButton.toolTip = SwtUtil.getPredictMessage('tooltip.details', null);
    this.detailsButton.label = SwtUtil.getPredictMessage('button.genericdisplaymonitor.details', null);
    this.statusLabel.text = SwtUtil.getPredictMessage('scenarioSummary.status', null);
    this.all.label = SwtUtil.getPredictMessage('scenarioSummary.all', null);
    this.active.label = SwtUtil.getPredictMessage('scenarioSummary.active', null);
    this.resolved.label = SwtUtil.getPredictMessage('scenarioSummary.resolved', null);
    this.pending.label = SwtUtil.getPredictMessage('scenarioSummary.pending', null);
    this.overdue.label = SwtUtil.getPredictMessage('scenarioSummary.overdue', null);
    this.allOpen.label = SwtUtil.getPredictMessage('scenarioSummary.allOpen', null);
    //label section
    this.resolvedOnDate.toolTip = SwtUtil.getPredictMessage('tooltip.resolvedOnDate', null);
  }

  ngOnDestroy(): any {
    instanceElement = null;
  }

  /**
   * onLoad
   * Upon completion of loading into the flash player this method is called
   **/
  onLoad(): void {
    this.initializeMenus();
    this.inputData.cbStart= this.startOfComms.bind(this);
    this.inputData.cbStop= this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.inputDataResult(data);
    };
    this.inputData.cbFault=this.inputDataFault.bind(this);
    this.inputData.encodeURL=false;

    this.actionPath="workflowmonitor.do?";
    this.actionMethod="method=data";

    //update buttons status
    this.summary.summaryGrid.onRowClick = (event) => {
      if (this.summary.rcdScenInstance == "Y") {
        this.handleButtonsStatus();
      }
    };
    this.summary.tree.ITEM_CLICK.subscribe(( item ) => {
      this.summaryTreeEventHandler( item );
    });

    this.summary.summaryGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      //this.cellClickEventHandler(selectedRowData);
      this.cellLogic(selectedRowData);
    });

    this.summary.summaryGrid.columnWidthChanged.subscribe((event) => {
      this.updateWidths(event);
    });

    this.treeDivider.DIVIDER_BUTTON_CLICK.subscribe((event) => {
      this.saveDividerStatusListener(event);
    });

    HDividedEndResizeEvent.subscribe((event) => {
      const widthLeft = ''+this.summary.divBox.widthLeft;
      this.currDividerPosition = Number(widthLeft.substr(0,widthLeft.length-1));
      this.updateWidths(event);
    });
    this.summary.summaryGrid.onFilterChanged = this.doUpdateSortFilter.bind(this);
    this.summary.summaryGrid.onSortChanged = this.doUpdateSortFilter.bind(this);
    this.requestParams=[];
    this.inputData.url=this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  saveDividerStatusListener(event) {
    // Save the divider status in the database if it is closed or opened
    this.saveSettings();
  }

  saveSettings() {
    this.requestParams = [];
    this.saveSettingsData.cbStart = this.startOfComms.bind(this);
    this.saveSettingsData.cbStop = this.endOfComms.bind(this);
    this.saveSettingsData.cbResult = (event) => {
    };
    this.saveSettingsData.cbFault = this.inputDataFault.bind(this);
    this.saveSettingsData.encodeURL = false;
    this.actionPath = 'workflowmonitor.do?';
    this.actionMethod = 'method=saveUserSettings';
    this.requestParams['isClosed'] = this.treeDivider.widthLeft == '0';
    this.requestParams['applyThreshold'] = this.lastApplyThresholdSetting;
    this.saveSettingsData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.saveSettingsData.send(this.requestParams);
  }

  updateWidths(event): void {
    let columnWidth=[];
    let cols = this.summary.summaryGrid.gridObj.getColumns().slice(0);
    let columnCount = cols.length;
    for (let i=0; i< columnCount; i++) {
      if(cols[i].id == "expand") {
        cols[i].width = 5;
      } else if(cols[i].id != "dummy") {
        if (cols[i].field != null ) {
          columnWidth.push(cols[i].field + "=" + cols[i].width);
        }
      }
    }
    // add divider value
    columnWidth.push("divider=" + this.currDividerPosition);
    this.requestParams=[];
    this.sendData.encodeURL = false;
    this.requestParams["width"]=columnWidth.join(",");
    this.actionPath = 'workflowmonitor.do?';
    this.actionMethod = 'method=saveColumnWidth&';
    this.sendData.cbResult = (event) => {
      this.inputDataResultColumnsChange(event);
    };
    this.sendData.url = this.baseURL + this.actionPath +this.actionMethod;
    this.sendData.send(this.requestParams);
  }


  inputDataResultColumnsChange(event): void {
    let index: any;
    if (this.sendData.isBusy()) {
      this.sendData.cbStop();
    } else {
      this.lastReceivedWidthJSON = event;
      let jsonResponse: JSONReader = new JSONReader();
      jsonResponse.setInputJSON(this.lastReceivedWidthJSON);
      if (jsonResponse.getRequestReplyMessage() !== "Column width saved ok") {
        this.swtAlert.error(SwtUtil.getPredictMessage('error.contactAdmin', null) + '\n' + jsonResponse.getRequestReplyMessage());
      }
    }
  }
private previousScrollPosition = 0;
  /**
   * loadSummary
   *
   * This function is called to load data relevent to alerts
   **/
  loadSummary(): void {
    let selectedTreeItemQuery = "";
    let selectedFilter = null;
    let selectedSort = null;
    selectedTreeItemQuery = this.extractParentFilterDataFromNode(this.summary.tree.selectedItem,selectedTreeItemQuery);
    this.previousScrollPosition = $('.customTreeWarapper')[0].scrollTop;


    const currThreshold: boolean = this.currencyThreshold.selected;
    this.requestParams["currThreshold"]= currThreshold;
    this.requestParams["entityId"]= this.entityCombo.selectedItem.content;
    this.requestParams["selectedCurrencyGroup"]= this.ccyCombo.selectedItem.content;
    this.requestParams["dividerPosition"]= this.summary.getDividerPosition();
    this.requestParams["selectedTab"] = this.tabCategoryList.getSelectedTab().id;
    this.requestParams["isScenarioAlertable"]= (this.summary.getIsScenarioAlertable() == "" ) ? 'N': this.summary.getIsScenarioAlertable();
    this.requestParams["fromWorkFlow"]="true";
    this.requestParams["callerMethod"]="___";
    if( this.firstLoad) {
      this.requestParams["firstLoad"]="true";
    } else {
      this.requestParams["firstLoad"]="false";
      this.summary.saveTreeOpenState();
      if (this.previousSelectedTabIndex === 0 ) {
        this.firstTabTreeOpenedItems = this.summary.treeOpenedItems.concat();
        this.firstTabTreeClosedItems = this.summary.treeClosedItems.concat();
      } else {
        this.secondTabTreeOpenedItems = this.summary.treeOpenedItems.concat();
        this.secondTabTreeClosedItems = this.summary.treeClosedItems.concat();
      }
    }
    this.requestParams["facilityId"] =this.facilityGui;
    this.requestParams["facilityGuiId"]="WORKFLOW_MONITOR";
    this.requestParams["status"] = this.status.selectedValue;
    this.requestParams["sqlParams"] =this.sqlParams;
    this.requestParams["scenarioId"] =this.scenarioId;
    this.requestParams['selectedTreeItemQuery'] = selectedTreeItemQuery;
    this.requestParams['statusChanged'] = this.statusChanged;
    

    if (this.summary.tree.selectedItem && this.summary.tree.selectedItem['record_scenario_instances'] == "Y") {
      this.requestParams["selectedSort"] = this.summary.summaryGrid.sortedGridColumnId+"|"+ (this.summary.summaryGrid.sortDirection ? "DESC" : "ASC");
      selectedFilter = this.getfilteredGridColumnsForInstanceGrid();
    }else{
      selectedSort = this.summary.getSortedGridColumn();
      this.requestParams["selectedSort"] = selectedSort;
      selectedFilter = this.getfilteredGridColumns();
    }
    this.requestParams["selectedFilter"] = selectedFilter;
    

    this.summaryData.cbResult = (data) => {
      this.summaryDataResult(data);
    };
    this.summaryData.cbFault= this.inputDataFault.bind(this);
    this.summaryData.encodeURL=false;
    this.scenarioSummaryActionPath="scenarioSummary.do?";
    //this.scenarioSummaryActionMethod="method=summaryData"; 
    this.scenarioSummaryActionMethod="method=getScenarioSummaryDetails"; 
    this.summaryData.url=this.baseURL + this.scenarioSummaryActionPath + this.scenarioSummaryActionMethod;
    this.summaryData.send(this.requestParams);
    if(this.summary.tree.selectedItem != null && this.summary.tree.selectedItem.data)
     this.lastSelectedValue = this.summary.tree.selectedItem.id

  }

  /**
   * updateData
   * @param refresh:String
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  updateData(refresh: string): void {
    this.requestParams=[];
    this.requestParams["resolvedOnDate"]= this.resolvedOnDate.text;
    this.requestParams["selectedScenario"]= this.summary.getSelectedItemID();
    this.requestParams["isScenarioAlertable"]= (this.summary.getIsScenarioAlertable() == "" ) ? 'N': this.summary.getIsScenarioAlertable();
    let currGrp: string=  this.ccyCombo.selectedItem.content;
    let entityId: string=  this.entityCombo.selectedItem.content;
    if(this.summary.tree.selectedItem) {
      if (this.previousSelectedTabIndex === 0 ) {
        this.fisrtTablastSelectedId = this.summary.tree.selectedItem.id;
      } else {
        this.secondTablastSelectedId = this.summary.tree.selectedItem.id;
      }
    }
    // this.summary.tree.selectedIndex = -1 ;
    this.summary.saveTreeOpenState();
    if (this.previousSelectedTabIndex === 0 ) {
      this.firstTabTreeOpenedItems = this.summary.treeOpenedItems.concat();
      this.firstTabTreeClosedItems = this.summary.treeClosedItems.concat();
    } else {
      this.secondTabTreeOpenedItems = this.summary.treeOpenedItems.concat();
      this.secondTabTreeClosedItems = this.summary.treeClosedItems.concat();
    }
    if ( refresh ===  this.WORKFLOW_REFRESH) {
      if (currGrp !== null && currGrp !== "") {
        this.requestParams["entityid"]=entityId;
      }
      if (entityId !== null && currGrp !== "") {
        this.requestParams["currencygrpid"]=currGrp;
      }
      this.requestParams["applycurrencythreshold"]=  this.currencyThreshold.selected === true ? "Y" : "N";

      if( this.tabList.selectedIndex !==-1) {
        this.requestParams["selectedDate"] = this.tabList.getTabChildren()[this.tabList.selectedIndex].tabName;
      }
      this.requestParams["existingEntityId"]= this.existingEntityId;
      this.requestParams["selectedSort"]= this.summary.getSortedGridColumn();
      this.indexSelected= this.tabList.selectedIndex;

      if (this.tabCategoryList.selectedIndex !== -1) {
        this.requestParams["selectedCategory"] = this.tabDataCategory[this.tabCategoryList.selectedIndex].tabName;
      }
      this.actionPath="workflowmonitor.do?";
      this.actionMethod="method=data";
      this.inputData.url= this.baseURL +  this.actionPath +  this.actionMethod;
      this.inputData.send( this.requestParams);
    } else {
      this.requestParams["selectedSort"]= this.summary.getSortedGridColumn();
      this.loadSummary();
    }
  }

  /**
   * inputDataResult
   * @param event:ResultEvent
   * Communication Result Methods
   **/
  inputDataResult(event): void {
    try{
    this.lastRecievedJSON=(event);
    this.jsonReader.setInputJSON( this.lastRecievedJSON);
    this.lostConnectionText.visible=false;
    this.pdf.enabled = true;
    if (this.jsonReader.getScreenAttributes().displayedDate !== '') {
      this.displayedDate = this.jsonReader.getScreenAttributes()["displayedDate"];
  }
  if (this.jsonReader.getScreenAttributes().dateFormat !== '') {
      //resolvedOnDate
      this.dateFormat = this.jsonReader.getScreenAttributes()["dateFormat"];
      this.resolvedOnDate.formatString = this.dateFormat.toLowerCase();
  }
  if (this.jsonReader.getScreenAttributes().currencyFormat !== '') {
    //resolvedOnDate
    this.currencyFormat = this.jsonReader.getScreenAttributes()["currencyFormat"];
}

    const lastRef = this.jsonReader.getScreenAttributes()["lastRefTime"];
    this.lastRefTime.text = lastRef.replace(/\\u0028/g, '(').replace(/\\u0029/g, ')');

    this.lastApplyThresholdSetting = this.jsonReader.getScreenAttributes()["isThresholdToBeApplied"];
    if (this.jsonReader.getRequestReplyStatus()) {
      if ((this.lastRecievedJSON !== this.prevRecievedJSON)) {
        this.resolvedOnDate.text=this.displayedDate;
        this.screenOption=this.jsonReader.getScreenAttributes()["isClosed"];
        if (this.screenOption==true){
          this.treeDivider.widthLeft = '0';  
        }
        this.entityCombo.setComboData(this.jsonReader.getSelects());
        this.selectedEntity.text =this.entityCombo.selectedItem.value;
        this.ccyCombo.setComboData(this.jsonReader.getSelects());
        this.ccyCombo.enabled= !(this.entityCombo.selectedLabel == 'All');
        this. selectedCcy.text=this.ccyCombo.selectedItem.value;
        this.currencyThreshold.selected = Boolean(this.jsonReader.getScreenAttributes()["currencythreshold"]) === true;
        this.existingEntityId=this.jsonReader.getScreenAttributes()["existingEntityId"];
        if (this.ccyCombo.dataProvider.length === 1 && this.entityCombo.selectedItem.value !== "All") {
          if (this.autoRefresh !== null) {
              this.autoRefresh.stop();
          }
          this.comboOpen=true;
          this.swtAlert.error(SwtUtil.getPredictMessage('alert.currencyAccess', null),  'Error');
        }

        if (!this.jsonReader.isDataBuilding()) {
          this.dataBuildingText.visible=false;
          if ( this.posLvlData   || this.posLvlData  !== this.deepCopy(this.lastRecievedJSON.workflowmonitor.positions.position)) {
            this.posLvlData  = this.deepCopy(this.lastRecievedJSON.workflowmonitor.positions.position);
          }
          /************Labels********/
          this.labelExc0txt.text  = this.posLvlData[0].level;
          this.labelExc1txt.text  = this.posLvlData[1].level;
          this.labelExc2txt.text  = this.posLvlData[2].level;
          this.labelExc3txt.text  = this.posLvlData[3].level;
          this.labelExc4txt.text  = this.posLvlData[4].level;
          this.labelExc5txt.text  = this.posLvlData[5].level;
          this.labelExc6txt.text  = this.posLvlData[6].level;
          this.labelExc7txt.text  = this.posLvlData[7].level;
          this.labelExc8txt.text  = this.posLvlData[8].level;
          /**********LinkButtons labels*********/
          this.valueInc0Btn.label = this.posLvlData[0].included;
          this.valueExc0Btn.label = this.posLvlData[0].excluded;
          this.valueInc1Btn.label = this.posLvlData[1].included;
          this.valueExc1Btn.label = this.posLvlData[1].excluded;
          this.valueInc2Btn.label = this.posLvlData[2].included;
          this.valueExc2Btn.label = this.posLvlData[2].excluded;
          this.valueInc3Btn.label = this.posLvlData[3].included;
          this.valueExc3Btn.label = this.posLvlData[3].excluded;
          this.valueInc4Btn.label = this.posLvlData[4].included;
          this.valueExc4Btn.label = this.posLvlData[4].excluded;
          this.valueInc5Btn.label = this.posLvlData[5].included;
          this.valueExc5Btn.label = this.posLvlData[5].excluded;
          this.valueInc6Btn.label = this.posLvlData[6].included;
          this.valueExc6Btn.label = this.posLvlData[6].excluded;
          this.valueInc7Btn.label = this.posLvlData[7].included;
          this.valueExc7Btn.label = this.posLvlData[7].excluded;
          this.valueInc8Btn.label = this.posLvlData[8].included;
          this.valueExc8Btn.label = this.posLvlData[8].excluded;

          this.incMovementsEnableDisable();
          this.excMovementsEnableDisable();

          this.labelExcTotalValue.text = this.lastRecievedJSON.workflowmonitor.positions.excludedtotal;
          this.labelIncTotalValue.text = this.lastRecievedJSON.workflowmonitor.positions.includedtotal;
          if(this.lastRecievedJSON.workflowmonitor.systemsummary.system) {
            if(this.lastRecievedJSON.workflowmonitor.systemsummary.system.id == "loggedonusers") {
              this.logonBtn.label = String(this.lastRecievedJSON.workflowmonitor.systemsummary.system.content) == "" ? "0" : String(this.lastRecievedJSON.workflowmonitor.systemsummary.system.content);
              this.logonBtn.label === "0" ? (this.logonBtn.enabled = false) : (this.logonBtn.enabled = true);
            }
          }

        } else {
          this.dataBuildingText.visible=true;
        }
        // if main Timer is not initialized
        if (this.autoRefresh == null) {
          this.refreshRate=parseInt(this.jsonReader.getRefreshRate(),0);
          if (this.refreshRate < 5) {
            this.refreshRate=30;
          }
          // initilaize main timer
          this.autoRefresh = new Timer((this.refreshRate * 1000), 0);
          this.autoRefresh.addEventListener("timer", this.dataRefresh.bind(this));
        }
        
        this.tabData=[];
        if (this.tabList.getTabChildren().length === 0) {

          if(this.lastRecievedJSON.workflowmonitor.tabs.predictdate) {
            for (let i = 0; i < this.lastRecievedJSON.workflowmonitor.tabs.predictdate.length; i++) {
              this.tabData[i] = this.tabList.addChild(Tab) as Tab;
              this.tabData[i].id = i;
              this.tabData[i].label = this.lastRecievedJSON.workflowmonitor.tabs.predictdate[i].content.substring(0,5);
              this.tabData[i].tabName = this.lastRecievedJSON.workflowmonitor.tabs.predictdate[i].content;
              this.tabData[i].businessday = this.lastRecievedJSON.workflowmonitor.tabs.predictdate[i].businessday;
              if(  this.tabList.getChildAt(i).businessday == false) {
                this.tabList.getChildAt(i).setTabHeaderStyle("color","darkgray");
              } else
                this.tabList.getChildAt(i).setTabHeaderStyle("color","black");

            }
          }
        }

        const index = Number(this.lastRecievedJSON.workflowmonitor.tabs.selectedIndex.index);
        this.tabList.selectedIndex = index;
        this.prevRecievedJSON = this.lastRecievedJSON;

        if (this.firstLoad) {
          this.tabDataCategory = [];

          if (this.tabCategoryList.getTabChildren().length === 0) {
            if (this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab) {
              for (let i = 0; i < 2; i++) {
                this.tabDataCategory[i] = this.tabCategoryList.addChild(Tab) as Tab;
                this.tabDataCategory[i].id = this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab[i].tabId;
                this.tabDataCategory[i].label = this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab[i].tabName;
                this.tabDataCategory[i].tabName = this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab[i].tabName;
                this.tabDataCategory[i].count = this.lastRecievedJSON.workflowmonitor.tabsCategory.displaytab[i].count;
              }
            }
          }

        }
        this.tabCategoryList.selectedIndex = Number(this.lastRecievedJSON.workflowmonitor.tabsCategory.selectedIndex.index);
      }
      this.loadSummary();
    } else {
      // alert user
      this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(),  'Error');
    }
    if ( this.autoRefresh !== null) {
      if (! this.autoRefresh.running) {
        this.autoRefresh.start();
      }
    }

  }catch(e){
    console.log("🚀 ~ file: WorkFlowMonitor.ts ~ line 694 ~ WorkFlowMonitor ~ inputDataResult ~ e", e)
    
  }
  }


  deepCopy( mainObj ) {
    const objCopy = []; // objCopy will store a copy of the mainObj
    let key;

    for ( key in mainObj ) {
      objCopy[key] = mainObj[key]; // copies each property to the objCopy object
    }
    return objCopy;
  }

  /**
   * summaryDataResult
   * @param event:ResultEvent
   * This method is called when result event occurs.
   **/
  summaryDataResult(event): void {
    try {
      
    let isFirstLoad = false;
    if (this.summaryData.isBusy()) {
      this.summaryData.cbStop();
    } else {
      this.lastRecievedSummaryJSON=(event);
      this.summaryXMLReader.setInputJSON( this.lastRecievedSummaryJSON);
      if (this.summaryXMLReader.getRequestReplyStatus()) {
        if (this.lastRecievedSummaryJSON !== this.prevRecievedSummaryJSON) {
          this.optionIdTypes = this.summaryXMLReader.getSelects()['select'].find(x => x.id == "otherIdTypeCombo").option;
          this.summary.dataProvider(this.lastRecievedSummaryJSON);
          if (this.summaryXMLReader.getSingletons().scenarioTotalForTab1) {
            this.tabCategoryList.getChildAt(0).label = this.tabDataCategory[0].tabName + '(' + this.summaryXMLReader.getSingletons().scenarioTotalForTab1 + ')';
          }
          if (this.summaryXMLReader.getSingletons().scenarioTotalForTab2) {
            this.tabCategoryList.getChildAt(1).label = this.tabDataCategory[1].tabName + '(' + this.summaryXMLReader.getSingletons().scenarioTotalForTab2 + ')';
          }

          if (this.tabCategoryList.selectedIndex === 0 ) {
            this.lastSelectedId = this.fisrtTablastSelectedId;
            this.summary.treeOpenedItems = this.firstTabTreeOpenedItems.concat();
            this.summary.treeClosedItems = this.firstTabTreeClosedItems.concat();
          } else {
            this.lastSelectedId = this.secondTablastSelectedId;
            this.summary.treeOpenedItems = this.secondTabTreeOpenedItems.concat();
            this.summary.treeClosedItems = this.secondTabTreeClosedItems.concat();
          }
          if (this.summary.treeOpenedItems.length === 0) {
            this.summary.tree.openItems = [];
          }

          if(this.summary.treeClosedItems.length === 0) {
            this.summary.tree.closeItems = [];
          }
          this.summary.setBaseURL(this.baseURL);
          this.summary.setActionPath(this.actionPath);
          if(this.lastRecievedSummaryJSON.scenarioinstancedetails.tree.root && this.lastRecievedSummaryJSON.scenarioinstancedetails.tree.root.node && this.lastRecievedSummaryJSON.scenarioinstancedetails.tree.root.node[0].node)
            // this.summary.tree.selectedItem=this.lastRecievedSummaryJSON.scenarioinstancedetails.tree.root.node[0].node[0];
            // this.summary.tree.selectedIndex = 1;
            // zz.put('levelAsInt','2')
            // this.summary.tree.selectNodeByAttribute('levelAsInt','2',true)
              // this.summary.tree.selectNodeByAttribute("id", "N_SCEN5");
          //this.customSummary.dataProvider(this.lastRecievedSummaryJSON);
          if (this.firstLoad) {
            this.summary.tree.expandAll(CustomTree.LEVEL_2_STR);
            // this.summary.tree.selectNodeByAttribute("id", "N_SCEN5");
            //this.customSummary.setFirstSubNode(this.lastRecievedSummaryJSON);
            this.firstLoad = false;
            isFirstLoad = true;
          } else {
            this.summary.openTreeItems();
            // this.summary.tree.expandAll(CustomTree.LEVEL_2_STR);
            this.summary.tree.selectNodeByAttribute("id", this.lastSelectedNode?this.lastSelectedNode:"N_"+this.scenarioId);
            setTimeout(() => {
              // this.summary.tree.selectedIndex =  this.summary.tree.selectedIndex;
              $('.customTreeWarapper')[0].scrollTop = this.previousScrollPosition;
            }, 200);
          }

          if ( this.mainGroup.visible === false) {
            this.mainGroup.visible=true;
          }
        }
      } else {
        this.swtAlert.show( this.summaryXMLReader.getRequestReplyMessage() + "\n" +  this.summaryXMLReader.getRequestReplyLocation(),  'Error');
      }
      this.prevRecievedSummaryJSON = this.lastRecievedSummaryJSON;
    }
    this.refreshTreeItemRender();
    this.handleButtonsStatus();
    this.previousSelectedTabIndex = this.tabCategoryList.selectedIndex;

    if(isFirstLoad && this.summary.tree && this.summary.tree.selectedItem){

      setTimeout(() => {
        this.requestParams["refreshGridOnly"] = 'true';
        this.scenarioId=this.getScenarioId(this.summary.tree.selectedItem,this.summary.tree.selectedItem.level);
        this.loadSummary();
      }, 1000);
    }

  } catch (error) {
      
  }
  }

  private lastSelectedValue = null
  /**
   * summaryTreeEventHandler
   * @param event
   * This method is called when Event occurs on the summary tree
   **/
  summaryTreeEventHandler(event): void {
    let refreshNeeded = false;
    this.summary.summaryGrid.selectedIndex=-1;

    let newSelectedNode = this.summary.tree.selectedItem.id;
    if(newSelectedNode != this.lastSelectedValue)
       refreshNeeded = true;
    
    this.lastSelectedValue = newSelectedNode;
    this.lastSelectedNode=this.summary.tree.selectedItem.id;
    
    let level = event.level;
    let newScenarioId = this.getScenarioId(event, level);
    if (refreshNeeded) {
      this.scenarioId = newScenarioId;
      this.requestParams["refreshGridOnly"] = 'true';
      this.summary.summaryGrid.resetFilter();
      this.summary.summaryGrid.filteredGridColumns="";
      this.summary.resetGridData();
      this.loadSummary();
    }
  }
  extractParentFilterDataFromNode(parentData,sql){
    if(parentData != null && parentData != undefined && parentData.treeLevelName!=null ){
      if(parentData.treeLevelName == 'category_id' || parentData.treeLevelName == 'CATEGORY_ID'){
        sql+=" AND P_SCENARIO."+parentData.treeLevelName+" = '"+parentData.treeLevelValue+"'"
      }else{
        let isDate = new RegExp(/^\d{4}-([0]\d|1[0-2])-([0-2]\d|3[01])$/).test(parentData.treeLevelValue);
        if(isDate)
          sql+="  AND PSI."+parentData.treeLevelName+" =  TO_DATE('"+parentData.treeLevelValue+"','YYYY-MM-DD')";
        else
          sql+=" AND PSI."+parentData.treeLevelName+" = '"+parentData.treeLevelValue+"'"
      }
      sql = this.extractParentFilterDataFromNode(parentData.parentData,sql);
    }


    return sql;
  }


  /**
   * filteredGridColumns
   *
   *
   * This function is used to get the filtered grid columns
   */
   private  getfilteredGridColumnsForInstanceGrid():string
   {
 
     var selectedFilter: string = "";
     var filteredColumnsFields=[];
     var gridColumns=this.summary.summaryGrid.getFilterColumns();
     var filteredColumns=this.summary.summaryGrid.filteredGridColumns;
     let databaseDateFormat = "dd/MM/yyyy" == this.dateFormat?"dd/mm/yyyy hh24:mi:ss":"mm/dd/yyyy hh24:mi:ss" ;
 
     try
     {
       // Iterate for gridColumns
       for (var i=0; i < gridColumns.length; i++)
       {
         filteredColumnsFields[i] = gridColumns[i].field;
       }
       if(filteredColumns != ''){
         let filterdValues = filteredColumns.split('|');
         for (var i=0; i < filteredColumnsFields.length; i++)
         {
     if(filterdValues[i] != "") {
       if(filterdValues[i] != "All" && filterdValues[i] != undefined) {
         var underscoreExist: string = filteredColumnsFields[i][filteredColumnsFields[i].length -1] ;
         if (underscoreExist == "_" ) {
           filteredColumnsFields[i] = filteredColumnsFields[i].slice(0, -1);
         }
         if( filteredColumnsFields[i] == "_id" ){
          selectedFilter= selectedFilter + "ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
        }else if( filteredColumnsFields[i] == "scenarioId" ){
           selectedFilter= selectedFilter + "SCENARIO_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "uniqueIdentifier" ){
           selectedFilter= selectedFilter + "UNIQUE_IDENTIFIER"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "status" ){
 
           let statusCode = '';
           switch (filterdValues[i]) {
             case 'Active':
               statusCode = "A";
               break;
             case 'Resolved':
               statusCode = "R";
               break;
             case 'Pending':
               statusCode = "P";
               break;
             case 'Overdue':
               statusCode = "O";
               break;
               default:
                 statusCode = "A";
           }
 
 
 
           selectedFilter= selectedFilter + "STATUS"+ "=" +"'"+ statusCode+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "raisedDatetime" ){
           // selectedFilter= selectedFilter + "RAISED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
           selectedFilter= selectedFilter  +  "RAISED_DATETIME"+ "="  + "TO_DATE ('"+filterdValues[i]  +"' , '"+databaseDateFormat+ "')" + " and " ;
         }else  if( filteredColumnsFields[i] == "lastRaisedDatetime" ){
           // selectedFilter= selectedFilter + "LAST_RAISED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
           selectedFilter= selectedFilter  +  "LAST_RAISED_DATETIME"+ "="  + "TO_DATE ('"+filterdValues[i]  +"' , '"+databaseDateFormat+ "')" + " and " ;
         }else  if( filteredColumnsFields[i] == "resolvedDatetime" ){
           // selectedFilter= selectedFilter + "RESOLVED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
           selectedFilter= selectedFilter  +  "RESOLVED_DATETIME"+ "="  + "TO_DATE ('"+filterdValues[i]  +"' , '"+databaseDateFormat+ "')" + " and " ;
         }else  if( filteredColumnsFields[i] == "resolvedByUser" ){
           selectedFilter= selectedFilter + "RESOLVED_BY_USER"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "eventsLaunchStatus" ){
           let eventLunchStatusCode = '';
           switch (filterdValues[i]) {
             case 'No events to launch':
               eventLunchStatusCode = "N";
               break;
             case 'Waiting to Launch':
               eventLunchStatusCode = "W";
               break;
             case 'Launched':
               eventLunchStatusCode = "L";
               break;
             case 'Failed':
               eventLunchStatusCode = "F";
                 // expected output: "Mangoes and papayas are $2.79 a pound."
               break;
               default:
                 eventLunchStatusCode = "W";
           }
           selectedFilter= selectedFilter + "EVENTS_LAUNCH_STATUS"+ "=" +"'"+ eventLunchStatusCode+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "hostId" ){
           selectedFilter= selectedFilter + "HOST_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "entityId" ){
           selectedFilter= selectedFilter + "ENTITY_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "currencyCode" ){
           selectedFilter= selectedFilter + "CURRENCY_CODE"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "accountId" ){
           selectedFilter= selectedFilter + "ACCOUNT_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "amount" ){
           let amount;
           if(filterdValues[i] != null && filterdValues[i] != undefined) {
             if (this.currencyFormat == "currencyPat2") {
               amount = Number(filterdValues[i].replace(/\./g, '').replace(/,/g, '.'));
             }else {
               amount = Number(filterdValues[i].replace(/,/g, ''));
             }
             
           }
 
           selectedFilter= selectedFilter + "AMOUNT"+ "=" +"'"+ amount+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "sign" ){
           selectedFilter= selectedFilter + "SIGN"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "overThreshold" ){
           selectedFilter= selectedFilter + "OVER_THRESHOLD"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "movementId" ){
           selectedFilter= selectedFilter + "MOVEMENT_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "matchId" ){
           selectedFilter= selectedFilter + "MATCH_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "sweepId" ){
           selectedFilter= selectedFilter + "SWEEP_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "paymentId" ){
           selectedFilter= selectedFilter + "PAYMENT_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "valueDate" ){
           // selectedFilter= selectedFilter + "VALUE_DATE"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
           selectedFilter= selectedFilter  +  "VALUE_DATE"+ "="  + "TO_DATE ('"+filterdValues[i]  +"' , '"+databaseDateFormat+ "')" + " and " ;
         }else  if( filteredColumnsFields[i] == "otherId" ){
           selectedFilter= selectedFilter + "OTHER_ID"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }else  if( filteredColumnsFields[i] == "otherIdType" ){
           selectedFilter= selectedFilter + "OTHER_ID_TYPE"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
         }
 
 
       }
     }
     else {
       // selectedFilter= selectedFilter + filteredColumnsFields[i] + " is null" +" and ";
 
 
       var underscoreExist: string = filteredColumnsFields[i][filteredColumnsFields[i].length -1] ;
         if (underscoreExist == "_" ) {
           filteredColumnsFields[i] = filteredColumnsFields[i].slice(0, -1);
         }
         if( filteredColumnsFields[i] == "scenarioId" ){
           selectedFilter= selectedFilter + "SCENARIO_ID"+  " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "uniqueIdentifier" ){
           selectedFilter= selectedFilter + "UNIQUE_IDENTIFIER" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "status" ){
 
           selectedFilter= selectedFilter + "STATUS" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "raisedDatetime" ){
           // selectedFilter= selectedFilter + "RAISED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
           selectedFilter= selectedFilter  +  "RAISED_DATETIME" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "lastRaisedDatetime" ){
           // selectedFilter= selectedFilter + "LAST_RAISED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
           selectedFilter= selectedFilter  +  "LAST_RAISED_DATETIME" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "resolvedDatetime" ){
           // selectedFilter= selectedFilter + "RESOLVED_DATETIME"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
           selectedFilter= selectedFilter  +  "RESOLVED_DATETIME" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "resolvedByUser" ){
           selectedFilter= selectedFilter + "RESOLVED_BY_USER" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "eventsLaunchStatus" ){
           selectedFilter= selectedFilter + "EVENTS_LAUNCH_STATUS" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "hostId" ){
           selectedFilter= selectedFilter + "HOST_ID" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "entityId" ){
           selectedFilter= selectedFilter + "ENTITY_ID" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "currencyCode" ){
           selectedFilter= selectedFilter + "CURRENCY_CODE" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "accountId" ){
           selectedFilter= selectedFilter + "ACCOUNT_ID" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "amount" ){
 
           selectedFilter= selectedFilter + "AMOUNT" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "sign" ){
           selectedFilter= selectedFilter + "SIGN" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "overThreshold" ){
           selectedFilter= selectedFilter + "OVER_THRESHOLD" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "movementId" ){
           selectedFilter= selectedFilter + "MOVEMENT_ID" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "matchId" ){
           selectedFilter= selectedFilter + "MATCH_ID" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "sweepId" ){
           selectedFilter= selectedFilter + "SWEEP_ID" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "paymentId" ){
           selectedFilter= selectedFilter + "PAYMENT_ID" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "valueDate" ){
           // selectedFilter= selectedFilter + "VALUE_DATE"+ "=" +"'"+ filterdValues[i]+ "'" +" and ";
           selectedFilter= selectedFilter  +  "VALUE_DATE" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "otherId" ){
           selectedFilter= selectedFilter + "OTHER_ID" + " is null" +" and ";
         }else  if( filteredColumnsFields[i] == "otherIdType" ){
           selectedFilter= selectedFilter + "OTHER_ID_TYPE" + " is null" +" and ";
         }
 
 
     }
 
 
     
         }
       }
 
       selectedFilter = selectedFilter.substring(0, selectedFilter.length - 5);
       return selectedFilter;
     }
     catch (error)
     {
       console.log('error', error)
     }
   }



  getScenarioId(event, level) {
    var scenarioId = null;

    switch (level) {
      case "Level1":
        scenarioId = null;
        break;
      case "Level2":
        scenarioId = event.data.treeLevelValue;
        break;
      case "Level3":
        scenarioId = event.data.parentData.treeLevelValue;
        break;
      case "Level4":
        scenarioId = event.data.parentData.parentData.treeLevelValue;
        break;
      case "Level5":
        scenarioId = event.data.parentData.parentData.parentData.treeLevelValue;
        break;
      case "Level6":
          scenarioId = event.data.parentData.parentData.parentData.parentData.treeLevelValue;
          break;
      case "Level7":
          scenarioId = event.data.parentData.parentData.parentData.parentData.parentData.treeLevelValue;
        break;
      default:
        break;
    }
    return scenarioId;
  }

  /**
   * cellClickEventHandler
   *
   * @param e:CellEvent
   *
   * This method is called when clicking summary grid cell
   **/
  cellClickEventHandler(event): void {
    let fieldName = event.target.field;
    const isClickable = this.summary.setClickable( event.target.data, null,null);
    const value = (event.target.data.slickgrid_rowcontent[fieldName] ) ? event.target.data.slickgrid_rowcontent[fieldName].content : null;
    if (isClickable && value > 0) {
      const entityId: string = event.target.data.slickgrid_rowcontent['entity'].content;
      const currencyId: string = event.target.data.slickgrid_rowcontent['ccy'].content;
      const count: string = event.target.data.slickgrid_rowcontent[fieldName].content;
      const facilityId: string = this.summary.facilityId;
      const facilityName: string = this.summary.facilityName;
      const useGeneric: string = this.summary.useGeneric;
      const scenarioTitle: string = this.summary.scenarioTitle;
      const applyCurrencyThreshold: string = this.currencyThreshold.selected === true ? 'Y' : 'N';
      const scenarioId: string = this.summary.selectedscenario;
      ExternalInterface.call("openFacility", scenarioTitle, useGeneric, facilityId,facilityName, scenarioId, entityId, currencyId, applyCurrencyThreshold, count,this.ccyCombo.selectedItem.content);
    }
  }

  cellLogic(selectedRowData): void {
    if(this.summary.summaryGrid.selectedIndex>-1){
      let fieldName = selectedRowData.target.field;
      let data = selectedRowData.target.data;
      if (this.summary.rcdScenInstance == "Y") {
        this.attributeJSON = selectedRowData.target.data.slickgrid_rowcontent.attributesXml.code;

        if(this.attributeJSON && this.isValidJSON(this.attributeJSON)){
        let jsonData=  JSON.parse( this.attributeJSON);
        jsonData = JSON.stringify(jsonData, (key, value) => {
          if (key !== "" && key !== "rowset" && key !== "row" && typeof value === "object") {
            return Object.entries(value).reduce((acc, [k, v]) => {
              acc[k] = JSON.stringify(v);
              return acc;
            }, {});
          }
          return value;
        }, 4);
        this.attributeJSON =this.htmlEntities(jsonData.replace(/\\"/g, '"').replace(/"{/g, '{').replace(/}"/g, '}'));
        }
      }
    let isClickable = (data.slickgrid_rowcontent[fieldName]) ? data.slickgrid_rowcontent[fieldName].clickable : null;
    if (isClickable) {
      if(this.summary.rcdScenInstance=="Y")
      this.clickLink('fromLink');
      else
      this.openFacility(data,fieldName);
    }
  }
  }


  htmlEntities(str) {
    try {
      return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
      replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
    } catch (e) {
      console.log('error', e, str)
    }
  }
  
  /*clickLink(): void {
    try {
      this.win =  SwtPopUpManager.createPopUp(this, AttributeXML, {
        title: "Scenario Instance Attributes",
        attributeXmlText:this.attributeXML,
      });
      this.win.isModal = true;
      this.win.enableResize = false;
      this.win.width = '400';
      this.win.height = '500';
      this.win.showControls = true;
      this.win.id = "AttributeXML";
      this.win.display();

    }
    catch (error) {
    }
  }*/

  clickLink(src): void {
    this.requestParams = [];
    this.detailsData.cbStart = this.startOfComms.bind(this);
    this.detailsData.cbStop = this.endOfComms.bind(this);
    this.detailsData.cbFault = this.inputDataFault.bind(this);
    this.detailsData.encodeURL = false;
    // Define the action to send the request
    this.actionPath = "scenarioSummary.do?";
    // Define method the request to access
    this.actionMethod = 'method=getInstanceXml';
    this.requestParams['instanceId'] = this.summary.summaryGrid.selectedItem.id.content;
    this.detailsData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.detailsData.cbResult = (event) => {
      this.getInstanceDetails(event);
    };
    this.detailsData.send(this.requestParams);
    this.source=src;
}

getInstanceDetails(event){
  if (this.detailsData.isBusy()) {
    this.detailsData.cbStop();
  } else {
    this.lastRecievedJSON = event;
    this.jsonReader.setInputJSON(this.lastRecievedJSON);

    if (this.jsonReader.getRequestReplyStatus()) {
      if (this.lastRecievedJSON !== this.prevRecievedJSON) {
        let json= (this.jsonReader.getSingletons().instanceXml).replace(/#/g, ">");
          if(json && this.isValidJSON(json)){
          let jsonData=  JSON.parse(json);
          jsonData = JSON.stringify(jsonData, (key, value) => {
            if (key !== "" && key !== "rowset" && key !== "row" && typeof value === "object") {
              return Object.entries(value).reduce((acc, [k, v]) => {
                acc[k] = JSON.stringify(v);
                return acc;
              }, {});
            }
            return value;
          }, 4);
          this.attributeJSON =this.htmlEntities(jsonData.replace(/\\"/g, '"').replace(/"{/g, '{').replace(/}"/g, '}'));
          }else{
          this.attributeJSON =json;
          }
        if(this.source=='fromLink'){
        this.win =  SwtPopUpManager.createPopUp(this, AttributeXML, {
          title: "Scenario Instance Attributes",
          attributeXmlText:this.attributeJSON,
        });
        this.win.isModal = true;
        this.win.enableResize = false;
        this.win.width = '450';
        this.win.height = '500';
        this.win.showControls = true;
        this.win.id = "AttributeXML";
        this.win.display();
      }else{
        this.openInstDetails(json);
      }
      }
    }
  }
  }

  /**
   * incMovement
   *
   * @param posLvlId:number
   *
   * This Method is called when we click on included movement link
   **/
  public incMovement(posLvlId: number): void {
    // set position level id
    let posId: number = posLvlId;
    let predictStatus="";
    let matchStatus="";
    if (posId === 10) {
      posId=0;
      predictStatus='R';
      matchStatus='E';
    } else {
      predictStatus='I';
      matchStatus=' ';
    }
    let jsfunction="openJavaWindow";
    let param: string="outstandingmovement.do?method=flexWorkflow" + "&entityId=" + this.entityCombo.selectedItem.content.toString() + "&currGrp=" + this.ccyCombo.selectedItem.content.toString() + "&valueDate=" + (ExternalInterface.call('eval', 'systemDate')) + "&roleId=" + (ExternalInterface.call('eval', 'roleId')) + "&totalCount=0" + "&posLvlId=" + posId + "&predictStatus=" + predictStatus + "&matchStatus=" + matchStatus + "&tabIndicator=" + this.tabList.selectedIndex + "&applyCurrencyThreshold=" + ((this.currencyThreshold.selected) ? "Y" : "N");
    // set window properties
    let left = 50;
    let top = 190;
    let width = 1280;
    let height = 685;
    let m: string=ExternalInterface.call(jsfunction, param, left, top, width, height);
  }

  /**
   * getGridFilter
   *
   * Method is used to make the border design for grid.
   *
   **/
  //  getGridFilter():spark.filters.DropShadowFilter {
  // //    let distance:Number=2;
  // //    let angle:Number=90;
  // //    let color:Number=0xFBFBFB;
  // //    let alpha:Number=5;
  // //    let blurX:Number=1;
  // //    let blurY:Number=1;
  // //    let strength:Number=0.5;
  // //    let quality:Number; ////BitmapFilterQuality.LOW;
  // //    let inner:Boolean=false;
  // //    let knockout:Boolean=false;
  // //
  // //    return new DropShadowFilter(distance, angle, color, alpha, blurX, blurY, strength, quality, inner, knockout);
  //  }


  /**
   * filteredGridColumns
   * This function is used to get the filtered grid columns
   */
  private  getfilteredGridColumns(): string {
    try {
      let selectedFilter='';
      let filteredColumnsFields=[];
      if (this.summary.summaryGrid.filteredGridColumns !== '') {
        let gridColumns=this.summary.summaryGrid.getFilterColumns();
        let filteredColumns=this.summary.summaryGrid.filteredGridColumns;
        for (let i=0; i < gridColumns.length; i++) {
          filteredColumnsFields[i] = gridColumns[i].field;
        }
        if(filteredColumns != '') {
          let filterdValues = filteredColumns.split('|');
          for (let i=0; i < filteredColumnsFields.length-1; i++) {
            selectedFilter=selectedFilter+filterdValues[i]+'|';
          }
        } else {
          selectedFilter = this.summary.summaryGrid.getFilteredGridColumns();
        }
      } else {
        selectedFilter = "";
        return selectedFilter;
      }
      return selectedFilter.slice(4,selectedFilter.length);
    } catch (error) {
      // SwtUtil.logError(error, SwtUtil.INPUT_MODULE_ID, this.getQualifiedClassName(this)  , "getfilteredGridColumns", this.errorLocation);
    }
  }




  /**
   * doUpdateSortFilter()
   *
   * @param event:Event
   *
   * Method called when update filter and/or sort.
   **/
  private  doUpdateSortFilter(): void {
    let selectedSort: string = null;
    let selectedFilter: string =null;
    try {
      let currGrp: string=  this.ccyCombo.selectedItem.content;
      let entityId: string=  this.entityCombo.selectedItem.content;
      selectedFilter = this.getfilteredGridColumns();
      selectedSort = this.summary.getSortedGridColumn();
      this.requestParams=[];
      this.firstLoad = false;
      this.requestParams["isScenarioAlertable"]= (this.summary.getIsScenarioAlertable() == "" ) ? 'N': this.summary.getIsScenarioAlertable();
      this.requestParams["selectedScenario"]= this.summary.getSelectedItemID();
      if (this.summary.rcdScenInstance == "Y") {
        this.requestParams["selectedSort"] = this.summary.summaryGrid.sortedGridColumnId+"|"+ (this.summary.summaryGrid.sortDirection ? "DESC" : "ASC");
        selectedFilter = this.getfilteredGridColumnsForInstanceGrid();
      }else{
        selectedSort = this.summary.getSortedGridColumn();
        this.requestParams["selectedSort"] = selectedSort;
        selectedFilter = this.getfilteredGridColumns();
      }
      this.requestParams["selectedFilter"] = selectedFilter;
      if (currGrp !== null && currGrp !== "") {
        this.requestParams["entityid"]=entityId;
      }
      if (entityId !== null && currGrp !== "") {
        this.requestParams["currencygrpid"]=currGrp;
      }
      this.requestParams["applycurrencythreshold"]=  this.currencyThreshold.selected === true ? "Y" : "N";

      if( this.tabList.selectedIndex !==-1) {
        this.requestParams["selectedDate"] = this.tabList.getTabChildren()[this.tabList.selectedIndex].tabName;
      }
      this.requestParams["existingEntityId"]= this.existingEntityId;
      this.summary.saveTreeOpenState();
      this.loadSummary();

    } catch (error) {

    }
  }




  /**
   * closeHandler
   *
   * close the window from the close button
   **/
  closeHandler(): void {
    ExternalInterface.call("close");
  }

  initializeMenus(): void {
    // this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
    // let screenItem: ContextMenuItem = new ContextMenuItem('Show screen details JSON');
    // screenItem.MenuItemSelect = this.showXMLSelect.bind(this);
    // this.screenVersion.svContextMenu.customItems.push(screenItem);
    // let scenarioItem: ContextMenuItem = new ContextMenuItem('Show summary details JSON');
    // scenarioItem.MenuItemSelect = this.showSummaryXMLSelect.bind(this);
    // this.screenVersion.svContextMenu.customItems.push(scenarioItem);
    // this.contextMenu = this.screenVersion.svContextMenu;

    this.screenVersion.loadScreenVersion(this, this.screenName, this.versionNumber, this.releaseDate);
    this.screenVersion.svContextMenu.customItems = [];
    let screenItem: ContextMenuItem = new ContextMenuItem('Expand All Groups');
    screenItem.MenuItemSelect = this.contextMenuAction.bind(this,'expandAll');
    this.screenVersion.svContextMenu.customItems.push(screenItem);
    let screenItem2: ContextMenuItem = new ContextMenuItem('Expand To Level 1');
    screenItem2.MenuItemSelect = this.contextMenuAction.bind(this,'expandLevel1');
    this.screenVersion.svContextMenu.customItems.push(screenItem2);
    let screenItem3: ContextMenuItem = new ContextMenuItem('Expand To Level 2');
    screenItem3.MenuItemSelect = this.contextMenuAction.bind(this,'expandLevel2');
    this.screenVersion.svContextMenu.customItems.push(screenItem3);
    let scenarioItem: ContextMenuItem = new ContextMenuItem('Collapse All Groups');
    scenarioItem.MenuItemSelect = this.contextMenuAction.bind(this,'collapseAll');
    this.screenVersion.svContextMenu.customItems.push(scenarioItem);
    this.contextMenu = this.screenVersion.svContextMenu;
  }


  contextMenuAction(action){
    if('expandAll' == action){
      this.summary.tree.expandAll();
    }else if('expandLevel1' == action){
      this.summary.tree.collapseAll();
      this.summary.tree.expandAll('1');
    }else if('expandLevel2' == action){
      this.summary.tree.collapseAll();
      this.summary.tree.expandAll('2');
    }else if('collapseAll' == action){
      this.summary.tree.collapseAll();
    }
    this.summary.saveTreeOpenState();

    
  }




  /**
   * showXMLSelect
   * @param event:ContextMenuEvent
   * This method is called to open WF ShowXML popup
   **/
  showJSONSelect(event): void {
    this.showJsonPopup = SwtPopUpManager.createPopUp(this,
      JSONViewer,
      {
        jsonData: this.lastRecievedJSON,
      });
    this.showJsonPopup.width = "700";
    this.showJsonPopup.title = "Workflow JSON"; // SwtUtil.getPredictMessage('workflowmonitor.title.xml', null);
    this.showJsonPopup.height = "500";
    this.showJsonPopup.enableResize = false;
    this.showJsonPopup.showControls = true;
    this.showJsonPopup.display();
  }

  /**
   * This function is used to show XML screen
   * @param event:ContextMenuEvent
   * This method is called to open Show Scenario Summary XML popup
   **/
  showSummaryJSONSelect(event): void {
    if (this.lastRecievedSummaryJSON !== null) {
      this.showJsonPopup = SwtPopUpManager.createPopUp(this,
        JSONViewer,
        {
          jsonData: this.lastRecievedSummaryJSON,
        });
      this.showJsonPopup.width = "700";
      this.showJsonPopup.title = "Summary Details JSON";// SwtUtil.getPredictMessage('scenarioSummary.title.xml', null);
      this.showJsonPopup.height = "500";
      this.showJsonPopup.enableResize = false;
      this.showJsonPopup.showControls = true;
      this.showJsonPopup.display();
    } else {
      this.swtAlert.warning('No data to display', 'Warning - Summary Details');
    }
  }


  /**
   * changeColours
   * @param event:ContextMenuEvent
   * This method is called to change the colors
   **/
  changeColours(event): void {
    // check if colors are set to nonBlind mode
    if (!this.colorBlindMode) {
      this.colors = this.colorsBlind;
      this.colorBlindMode=true;
    } else {
      this.colors = this.colorsNonBlind;
      this.colorBlindMode = false;
    }
  }


  public export(type: string): void {
    let selects: any = [];
    selects.push("Entity=" + this.entityCombo.selectedItem.content);
    selects.push("Ccy=" + this.ccyCombo.selectedItem.content);
    selects.push("Selected Tab=" + this.tabList.getTabChildren()[this.tabList.selectedIndex].tabName);
    selects.push("Currency Threshold=" + ((this.currencyThreshold.selected) ? "On" : "Off"));
    let columns ="<columns><column heading=\"Name\" type=\"str\" dataelement=\"name\" /><column heading=\"Value\" type=\"num\" dataelement=\"value\" /></columns>";
    let rows ="<rows>";
    let str = "<data>";


    for(let i=0; i < this.posLvlData.length; i++ ) {
      if (this.posLvlData[i].level == "") {
        continue;
      } else {
        if (isNaN(parseInt(this.posLvlData[i].excluded,10))) {
          continue;
        } else {
          rows+="<row>" + "<name>Outstanding " + this.posLvlData[i].level + "</name>" + "<value>" + parseInt(this.posLvlData[i].excluded,10) + "</value>" + "</row>";
        }
      }
    }

    for(let i=0;i < this.posLvlData.length; i++ ) {
      if (this.posLvlData[i].level == "") {
        continue;
      } else {
        if (isNaN(parseInt(this.posLvlData[i].included,10))) {
          continue;
        } else {
          rows+="<row>" + "<name>Included " + this.posLvlData[i].level + "</name>" + "<value>" + parseInt(this.posLvlData[i].included, 10) + "</value>" + "</row>";

        }
      }
    }
    rows+="</rows>";
    rows = rows.replace('+', '');
    str += this.removeLineBreaks(rows);
    str += this.removeLineBreaks(columns);
    str = str.split("\\").join("BACKSLASH_REPLACE");
    str = str.split("'").join("\\\\'");
    str = str.split("&amp;").join("\\&");
    str = str.split("%").join("PERCENTAGE_REPLACE");
    str = str.split("&lt;").join("\\<");
    str = str.split("&gt;").join("\\>");
    // str = str.split("+").join("PLUSSYMBOL_REPLACE");
    let filters = "<filters>";
    if (selects != null) {
      for (let k = 0; k < selects.length; k++) {
        filters += "<filter id=\"" + selects[k].split("=")[0] + "\">" + selects[k].split("=")[1] + "</filter>";
      }
    }
    filters += "</filters>";
    str += filters;
    str += "</data>";
    ExternalInterface.call('exportData', type, str);
    ExternalInterface.call('eval', 'document.getElementById(\'exportDataForm\').action=\'flexdataexport.do?method=' + type + '\';');
    ExternalInterface.call('eval', 'document.getElementById(\'exportData\').value=\'' + str + '\';');
    ExternalInterface.call('eval', 'document.getElementById(\'exportDataForm\').submit();');
  }


  removeLineBreaks(input: string): string {

    let rtn = "";
    let postSplit = [];
    postSplit = input.split("\n");
    for (let i = 0; i < postSplit.length; i++) {
      rtn += postSplit[i];
    }
    return rtn;
  }

  /**
   * excOutStandings
   *
   * @param posLvlId:number, posValue: string
   *
   * This Method is called when we click on excluded outsading movement link
   **/
  public excOutStandings(posLvlId: number, posValue: string): void {

    // let nFor:NumberBase = new NumberBase();
    let f ="openJavaWindow";
    let param: string="outstandingmovement.do?method=displayOpenMovementCountByCurrency" + "&entityId=" + this.entityCombo.selectedItem.content.toString() + "&currGrp=" + this.ccyCombo.selectedItem.content.toString() + "&valueDate=" + ExternalInterface.call('eval', 'testDate') + "&selectedTabIndex=" + (this.tabCategoryList.selectedIndex + 1) + "&applyCurrencyThreshold=" + ((this.currencyThreshold.selected) ? "Y" : "N") + "&selectedValueDate=" + this.tabList.getTabChildren()[this.tabList.selectedIndex].tabName + "&workflow=" + "W";
    let left=50;
    let top=190;
    let width=1280;
    let height=635;
    let m: string = ExternalInterface.call(f, param, left, top, width, height);

  }

  public getMenuAccessID(param: string): string {
    let menuAccessFunc = "getMenuAccessId" ;
    let menuAccessId: string = String(ExternalInterface.call(menuAccessFunc, param));
    return menuAccessId;
  }

  public loggedOnClick(): void {

    let funcParam ="User Status";

    let menuAccessId: string=this.getMenuAccessID(funcParam);
    let f ="openJavaWindow";
    let param = "userStatus.do?&menuAccessId=" + menuAccessId;
    let left = 50;
    let top = 190;
    let width = 942;
    let height = 530;
    if (menuAccessId !== "2") {
      let m = ExternalInterface.call(f, param, left, top, width, height);
    }
  }
  /**
   * helpClick
   * This method is called to open help popup Screen
   **/
  helpClick(): void {
    let m = ExternalInterface.call('openHelpWindow(\'stuff\')');
  }


  /**
   * drawLine
   *
   * Method is used to draw a line between included movements
   * 		and excluded outstandings.
   **/
  //  drawLine():void
  //  {
  //    //remove the line when window resize
  //    if (horizontalLine !== null)
  //      mainCanvas.removeElement(horizontalLine);
  //    //create the instance
  //    horizontalLine=new UIComponent();
  //    //set the line style
  //    horizontalLine.graphics.lineStyle(1, 0x959595, 2);
  //    //draw the line
  //    horizontalLine.graphics.moveTo(flowGrid.x + 10, mvtTxt.y + 50);
  //    horizontalLine.graphics.lineTo(flowGrid.x + flowGrid.width - 20, mvtTxt.y + 50);
  //    //add the line to canvas
  //    mainCanvas.addElement(horizontalLine);
  //  }


  /**
   * refreshTreeItemRender
   * This method is called to refresh tree itemRenders if any update occurs on the summary tree
   **/
  refreshTreeItemRender(): void {
    // this.summary.tree.invalidateList();
  }

  /**
   * Timing result methods
   **/
  dataRefresh(event): void {
    let args = new Array([this.WORKFLOW_REFRESH]);
    if (!this.comboOpen) {
      this.updateData(this.WORKFLOW_REFRESH);
    }
    this.autoRefresh.stop();
    args=null;
  }


  /**
   * changeCombo
   * @param e:Event
   * When there is a change in the in one of the combo's
   **/
  changeCombo(e): void {
    this.comboChange=true;
    this.updateData(this.WORKFLOW_REFRESH);
  }


  rateHandler(): void {
    try {
      // clearInterval(this.interval);
      this.refreshRatePopup= SwtPopUpManager.createPopUp(this,
        RatePopUp,
        {
          title: "Auto-refresh Rate", // childTitle,
          refreshText:this.refreshRate
        });
      this.refreshRatePopup.width = "340";
      this.refreshRatePopup.height = "150";
      this.refreshRatePopup.id = "myRatePopUp";
      this.refreshRatePopup.enableResize = false;
      this.refreshRatePopup.showControls = true;
      this.refreshRatePopup.display();
    } catch (error) {
      SwtUtil.logError(error, this.moduleId,"WorkFlow Monitor"  , "rateHandler", this.errorLocation);
    }
  }


  /**
   * submitRate
   *
   * @param event:Event
   *
   * This method is called to submit the refresh rate
   */
  saveRefreshRate(res): void {
    if (res == '' || res == null) {
      this.swtAlert.error(SwtUtil.getPredictMessage('accountMonitor.alert.label.notANumber', null), 'Error');
    } else {
      let selectedRateBelowMinimum=false;
      this.refreshRate = Number(res);
      // this.refreshRate = parseInt(this.refreshRatePopup.refresh.text,10);
      if (this.refreshRate < 5) {
        this.refreshRate = 5;
        selectedRateBelowMinimum=true;
      }
      if (selectedRateBelowMinimum) {

        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.refreshRateSelectedMonimum', null), 'Warning');
      }
      // this.autoRefresh.delay(this.refreshRate * 1000);
      if (this.autoRefresh) {
        this.autoRefresh.delay(this.refreshRate * 1000);
      }
      let request: string = ExternalInterface.call("getUpdateRefreshRequest", this.refreshRate);
      if (request !== null && request !== "") {
        this.updateRefreshRate.encodeURL = false;
        this.updateRefreshRate.url = this.baseURL + request;
        this.updateRefreshRate.send();
      }
      // this.updateData("no");

    }
  }

  /**
   * startOfComms
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms(): void {
    this.loadingImage.setVisible(true);
    this.disableInterface();
  }

  /**
   * endOfComms
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  endOfComms(): void {
    // hide image
    this.loadingImage.setVisible(false);
    // enable buttons and control
    this.enableInterface();
  }

  /**
   * inputDataFault
   * @param event:FaultEvent
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  inputDataFault(event): void {
    this.lostConnectionText.visible=true;
    if (this.autoRefresh !== null) {
      if (!this.autoRefresh.running) {
        this.autoRefresh.start();
      }
    }
  }

  /**
   * disableInterface
   * Disable interface, turn off certain UI elements when a request is made to the server
   **/
  disableInterface(): void {
    // disable refresh Button
    this.refreshButton.enabled=false;
    this.refreshButton.buttonMode=false;
    /* remove selection events on the summary tree  */
    this.summary.disableTree();
    //this.summary.tree.removeEventListener("treeItemClick",   this.summaryTreeEventHandler,false);
  }

  /**
   * enableInterface
   *
   * This method is called to enable buttons
   **/
  enableInterface(): void {
    this.refreshButton.enabled=true;
    this.refreshButton.buttonMode=true;
    this.summary.enableTree();
    //this.summary.tree.addEventListener("treeItemClick",  this.summaryTreeEventHandler, false, 0, true);
  }

  /**
   * comboFocusOutHandler
   * @param e:Event
   * This method is called on FocusOut Event occurs on ComboBox
   **/
  comboFocusOutHandler(e): void {
    if (e.currentTarget.name ===  this.entityCombo.id) {
      // Check if entity combo textInput is empty
      if (StringUtils.trim( this.entityCombo.selectedItem.content).length === 0) {
        // reload dataProvider
        this.entityCombo.setComboData( this.jsonReader.getSelects());
      }
    } else {
      // Check if ccy combo textInput is empty
      if (StringUtils.trim( this.ccyCombo.selectedItem.content).length === 0) {
        // reload dataProvider
        this.ccyCombo.setComboData( this.jsonReader.getSelects());
      }
    }
  }


  /**
   * incMovementsEnableDisable
   * This method is called in order to enable or disable included movements
   * link buttons according to counts values
   **/
  incMovementsEnableDisable(): void {
    if (this.entityCombo.selectedItem.content === "All") {
      this.valueInc0Btn.enabled=false;
      this.valueInc1Btn.enabled=false;
      this.valueInc2Btn.enabled=false;
      this.valueInc3Btn.enabled=false;
      this.valueInc4Btn.enabled=false;
      this.valueInc5Btn.enabled=false;
      this.valueInc6Btn.enabled=false;
      this.valueInc7Btn.enabled=false;
      this.valueInc8Btn.enabled=false;

      this.valueInc0Btn.buttonMode=false;
      this.valueInc1Btn.buttonMode=false;
      this.valueInc2Btn.buttonMode=false;
      this.valueInc3Btn.buttonMode=false;
      this.valueInc4Btn.buttonMode=false;
      this.valueInc5Btn.buttonMode=false;
      this.valueInc6Btn.buttonMode=false;
      this.valueInc7Btn.buttonMode=false;
      this.valueInc8Btn.buttonMode=false;

    } else {
      if (this.valueInc0Btn.label != "" && this.valueInc0Btn.label != "0") {
        this.valueInc0Btn.enabled=true;
        this.valueInc0Btn.buttonMode=true;
      } else {
        this.valueInc0Btn.enabled=false;
        this.valueInc0Btn.buttonMode=false;
      }
      if (this.valueInc1Btn.label != "" && this.valueInc1Btn.label != "0") {
        this.valueInc1Btn.enabled=true;
        this.valueInc1Btn.buttonMode=true;
      } else {
        this.valueInc1Btn.enabled=false;
        this.valueInc1Btn.buttonMode=false;
      }
      if (this.valueInc2Btn.label != "" && this.valueInc2Btn.label != "0") {
        this.valueInc2Btn.enabled=true;
        this.valueInc2Btn.buttonMode=true;
      } else {
        this.valueInc2Btn.enabled=false;
        this.valueInc2Btn.buttonMode=false;
      }
      if (this.valueInc3Btn.label != "" && this.valueInc3Btn.label != "0") {
        this.valueInc3Btn.enabled=true;
        this.valueInc3Btn.buttonMode=true;
      } else {
        this.valueInc3Btn.enabled=false;
        this.valueInc3Btn.buttonMode=false;
      }
      if (this.valueInc4Btn.label != "" && this.valueInc4Btn.label != "0") {
        this.valueInc4Btn.enabled=true;
        this.valueInc4Btn.buttonMode=true;
      } else {
        this.valueInc4Btn.enabled=false;
        this.valueInc4Btn.buttonMode=false;
      }
      if (this.valueInc5Btn.label != "" && this.valueInc5Btn.label != "0") {
        this.valueInc5Btn.enabled=true;
        this.valueInc5Btn.buttonMode=true;
      } else {
        this.valueInc5Btn.enabled=false;
        this.valueInc5Btn.buttonMode=false;
      }
      if (this.valueInc6Btn.label != "" && this.valueInc6Btn.label != "0") {
        this.valueInc6Btn.enabled=true;
        this.valueInc6Btn.buttonMode=true;
      } else {
        this.valueInc6Btn.enabled=false;
        this.valueInc6Btn.buttonMode=false;
      }
      if (this.valueInc7Btn.label != "" && this.valueInc7Btn.label != "0") {
        this.valueInc7Btn.enabled=true;
        this.valueInc7Btn.buttonMode=true;
      } else {
        this.valueInc7Btn.enabled=false;
        this.valueInc7Btn.buttonMode=false;
      }
      if (this.valueInc8Btn.label != "" && this.valueInc8Btn.label != "0") {
        this.valueInc8Btn.enabled=true;
        this.valueInc8Btn.buttonMode=true;
      } else {
        this.valueInc8Btn.enabled=false;
        this.valueInc8Btn.buttonMode=false;
      }

    }
  }




  /**
   * excMovementsEnableDisable
   * This method is called in order to enable or disable excluded outstanding
   * link buttons according to counts values
   **/
  excMovementsEnableDisable(): void {
    if (this.entityCombo.selectedItem.value == "All") {
      this.valueExc0Btn.enabled=false;
      this.valueExc1Btn.enabled=false;
      this.valueExc2Btn.enabled=false;
      this.valueExc3Btn.enabled=false;
      this.valueExc4Btn.enabled=false;
      this.valueExc5Btn.enabled=false;
      this.valueExc6Btn.enabled=false;
      this.valueExc7Btn.enabled=false;
      this.valueExc8Btn.enabled=false;
      this.valueExc0Btn.buttonMode=false;
      this.valueExc1Btn.buttonMode=false;
      this.valueExc2Btn.buttonMode=false;
      this.valueExc3Btn.buttonMode=false;
      this.valueExc4Btn.buttonMode=false;
      this.valueExc5Btn.buttonMode=false;
      this.valueExc6Btn.buttonMode=false;
      this.valueExc7Btn.buttonMode=false;
      this.valueExc8Btn.buttonMode=false;
    } else {
      if (this.valueExc0Btn.label != "" && this.valueExc0Btn.label != "0") {
        this.valueExc0Btn.enabled=true;
        this.valueExc0Btn.buttonMode=true;
      } else {
        this.valueExc0Btn.enabled=false;
        this.valueExc0Btn.buttonMode=false;
      }
      if (this.valueExc1Btn.label != "" && this.valueExc1Btn.label != "0") {
        this.valueExc1Btn.enabled=true;
        this.valueExc1Btn.buttonMode=true;
      } else {
        this.valueExc1Btn.enabled=false;
        this.valueExc1Btn.buttonMode=false;
      }
      if (this.valueExc2Btn.label != "" && this.valueExc2Btn.label != "0") {
        this.valueExc2Btn.enabled=true;
        this.valueExc2Btn.buttonMode=true;
      } else {
        this.valueExc2Btn.enabled=false;
        this.valueExc2Btn.buttonMode=false;
      }
      if (this.valueExc3Btn.label != "" && this.valueExc3Btn.label != "0") {
        this.valueExc3Btn.enabled=true;
        this.valueExc3Btn.buttonMode=true;
      } else {
        this.valueExc3Btn.enabled=false;
        this.valueExc3Btn.buttonMode=false;
      }
      if (this.valueExc4Btn.label != "" && this.valueExc4Btn.label != "0") {
        this.valueExc4Btn.enabled=true;
        this.valueExc4Btn.buttonMode=true;
      } else {
        this.valueExc4Btn.enabled=false;
        this.valueExc4Btn.buttonMode=false;
      }
      if (this.valueExc5Btn.label != "" && this.valueExc5Btn.label != "0") {
        this.valueExc5Btn.enabled=true;
        this.valueExc5Btn.buttonMode=true;
      } else {
        this.valueExc5Btn.enabled=false;
        this.valueExc5Btn.buttonMode=false;

      }
      if (this.valueExc6Btn.label != "" && this.valueExc6Btn.label != "0") {
        this.valueExc6Btn.enabled=true;
        this.valueExc6Btn.buttonMode=true;
      } else {
        this.valueExc6Btn.enabled=false;
        this.valueExc6Btn.buttonMode=false;
      }
      if (this.valueExc7Btn.label != "" && this.valueExc7Btn.label != "0") {
        this.valueExc7Btn.enabled=true;
        this.valueExc7Btn.buttonMode=true;
      } else {
        this.valueExc7Btn.enabled=false;
        this.valueExc7Btn.buttonMode=false;

      }
      if (this.valueExc8Btn.label != "" && this.valueExc8Btn.label != "0") {
        this.valueExc8Btn.enabled=true;
        this.valueExc8Btn.buttonMode=true;
      } else {
        this.valueExc8Btn.enabled=false;
        this.valueExc8Btn.buttonMode=false;

      }

    }

  }

  /**
   * Function called when help button is clicked.
   */
  doHelp(): void {
    try {
      ExternalInterface.call("help");
    } catch (e) {
      SwtUtil.logError(e,   this.moduleId, 'workFlowMonitor', 'doHelp',   this.errorLocation);
    }
  }

  handleButtonsStatus() {
    if (this.summary.summaryGrid.selectedIndex > -1 && this.summary.summaryGrid.selectedItem) {
      if (this.summary.rcdScenInstance == "Y") {
        this.checkUserAccess();
      } else {
        this.disableButtons();
      }

    } else {
      this.disableButtons();
    }
  }
  
  //Mantis 5028 new methods to be checked after integrating custom summary
  openInstDetails(xml) {
    let params=[];   
    params.push ({scenarioId :this.checkColumnsValue("scenarioId"),
    instanceId :this.checkColumnsValue("id"),
    status : this.checkColumnsValue("status"),
    eventStatus : this.checkColumnsValue("eventsLaunchStatus"),
    firstRaisedDate : this.checkColumnsValue("raisedDatetime"),
    lastRaisedDate : this.checkColumnsValue("lastRaisedDatetime"),
    resolvedDate : this.checkColumnsValue("resolvedDatetime"),
    resolvedUser : this.checkColumnsValue("resolvedByUser"),
    uniqueIdent : this.checkColumnsValue("uniqueIdentifier"),
    hostId : this.checkColumnsValue("hostId"),
    entityId : this.checkColumnsValue("entityId"),
    ccyCode : this.checkColumnsValue("currencyCode"),
    acctId : this.checkColumnsValue("accountId"),
    valueDate : this.checkColumnsValue("valueDate"),
    amount : this.checkColumnsValue("amount"),
    sign : this.checkColumnsValue("sign"),
    mvtId : this.checkColumnsValue("movementId"),
    matchId : this.checkColumnsValue("matchId"),
    sweepId : this.checkColumnsValue("sweepId"),
    payment : this.checkColumnsValue("paymentId"),
    otherId : this.checkColumnsValue("otherId"),
    otherIdTypesList : this.optionIdTypes,
    scenOtherIdType : this.checkColumnsValue("otherIdType"),
    firstRaisedUser : this.checkColumnsValue("raisedUser"),
    lastRaisedUser : this.checkColumnsValue("lastRaisedUser"),
    attributesXml : xml,
    currBox : this.currencyThreshold.selected === true ? 'Y' : 'N',
    scenarioTitle : this.summary.scenarioTitle,
    useGeneric : this.summary.useGeneric,
    facilityId : this.summary.facilityId,
    facilityName : this.summary.facilityName});
    //Mantis 6163
    ExternalInterface.call("openInstDetails", "openInstanceDetails" , StringUtils.encode64(JSON.stringify(params))); 
  }

  checkColumnsValue(dataElement){
    let columnVal;
    let column;
    if(this.summary.summaryGrid.selectedItem){
      column= this.summary.summaryGrid.selectedItem[dataElement];
    }
    if(column){
      columnVal=column.content;
    }else{
      columnVal="";
    }
   return columnVal;
  }

  openFacility(data, fieldName): void {
    const entityId: string = data.slickgrid_rowcontent['entity'].content;
    const currencyId: string = data.slickgrid_rowcontent['ccy'].content;
    const count: string = data.slickgrid_rowcontent[fieldName].content;
    const facilityId: string = this.summary.facilityId;
    const facilityName: string = this.summary.facilityName;
    const useGeneric: string = this.summary.useGeneric;
    const scenarioTitle: string = this.summary.scenarioTitle;
    const applyCurrencyThreshold: string = this.currencyThreshold.selected === true ? 'Y' : 'N';
    const scenarioId: string = this.summary.selectedscenario;
    
    ExternalInterface.call('openFacility', scenarioTitle, useGeneric, facilityId, facilityName, scenarioId, entityId, currencyId, applyCurrencyThreshold, count, this.ccyCombo.selectedItem.content);
  }

  goTo(event): void {
    let hostId = this.summary.summaryGrid.selectedItem.hostId.content;
    let entityId = this.summary.summaryGrid.selectedItem.entityId.content;
    let matchId = this.summary.summaryGrid.selectedItem.matchId.content;
    let currencyId = this.summary.summaryGrid.selectedItem.currencyCode.content;
    let mvtId = this.summary.summaryGrid.selectedItem.movementId.content;
    let sweepId = this.summary.summaryGrid.selectedItem.sweepId.content;
    let additionalParams = this.summary.summaryGrid.selectedItem.otherId.content;
    ExternalInterface.call("goTo", this.summary.facilityId, hostId, entityId, matchId, currencyId, mvtId, sweepId, additionalParams);

  }
 
  changeStatus() {
    //enable resolvedOnDate only when all or resolved are selected
    if(this.status.selectedValue=="All" || this.status.selectedValue=="R"){
      this.resolvedOnDate.enabled=true;
    }else{
      this.resolvedOnDate.enabled=false; 
    }
    this.statusChanged = true; 
    this.summary.tree.selectedIndex = -1;
    if(this.summary.summaryGrid){
      this.summary.summaryGrid.resetFilter();
      this.summary.summaryGrid.filteredGridColumns="";
    }
    this.updateData('workFlowRefresh')
    this.statusChanged = false;
  }


  updateStatus(status) {
    // Initialize communication objects
    this.summaryData.cbStart = this.startOfComms.bind(this);
    this.summaryData.cbStop = this.endOfComms.bind(this);
    this.summaryData.cbResult = (data) => {
      this.requestParams["refreshGridOnly"] = 'false';
      this.loadSummary();
    };
    this.summaryData.cbFault = this.inputDataFault.bind(this);
    this.summaryData.encodeURL = false;
    this.requestParams["id"] = this.summary.summaryGrid.selectedItem.id.content;
    this.requestParams["oldStatus"] = this.summary.summaryGrid.selectedItem.status.content;
    this.requestParams["newStatus"] = status;
    this.actionPath = 'scenarioSummary.do?';
    this.actionMethod = 'method=updateScenInstanceStatus';
    this.summaryData.encodeURL = false;
    this.summaryData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.summaryData.send(this.requestParams);
  }

    checkUserAccess(){
      if(this.summary.summaryGrid.selectedIndex>-1){
      // Initialize communication objects
      this.summaryData.cbStart = this.startOfComms.bind(this);
      this.summaryData.cbStop = this.endOfComms.bind(this);
      this.summaryData.cbResult = (data) => {
        this.getUserAccess(data);
      };
      this.summaryData.cbFault = this.inputDataFault.bind(this);
      this.summaryData.encodeURL = false;
      this.requestParams["instanceId"] =  this.summary.summaryGrid.selectedItem.id.content;
      this.requestParams["scenarioId"] = this.summary.summaryGrid.selectedItem.scenarioId.content;
      this.requestParams["entityId"] =  this.summary.summaryGrid.selectedItem.entityId.content;
      this.requestParams["ccyCode"] = this.summary.summaryGrid.selectedItem.currencyCode.content;
      this.requestParams["hostId"] = this.summary.summaryGrid.selectedItem.hostId.content;
      this.requestParams["fromWorkflow"] = "true";
      this.actionPath = 'scenarioSummary.do?';
      this.actionMethod = 'method=checkUserInstAccess';
      this.summaryData.encodeURL=false;
      this.summaryData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.summaryData.send(this.requestParams);
      }
    }
  
    getUserAccess(event) {
      let hasAccess = event.scenarioDetails.singletons.hasAccess;
      if (hasAccess == 'true') {
        this.handleRctRslvButtons();
      }else {
        this.resolveButton.enabled = false;
        this.resolveButton.buttonMode = false;
        this.reActiveButton.enabled = false;
        this.reActiveButton.buttonMode = false;
      }
      this.enableButtons();
    
    }

  handleRctRslvButtons() {
    let status = (this.summary.summaryGrid.selectedItem && this.summary.summaryGrid.selectedItem.status)?this.summary.summaryGrid.selectedItem.status.content:"";
    if(status){
    if (status == "Resolved") {
      this.resolveButton.enabled = false;
      this.resolveButton.buttonMode = false;
      this.reActiveButton.enabled = true;
      this.reActiveButton.buttonMode = true;
    } else if (status == "Pending" || status == "Active") {
      this.resolveButton.enabled = true;
      this.resolveButton.buttonMode = true;
      this.reActiveButton.enabled = false;
      this.reActiveButton.buttonMode = false;
    } else {
      this.resolveButton.enabled = true;
      this.resolveButton.buttonMode = true;
      this.reActiveButton.enabled = true;
      this.reActiveButton.buttonMode = true;
    }
  }
  }

  enableButtons() {
    this.detailsButton.enabled = true;
    this.detailsButton.buttonMode = true;
    if (this.summary.facilityId && this.summary.facilityId != "None") {
    this.goToButton.enabled = true;
    this.goToButton.buttonMode = true;
    } else {
      this.goToButton.enabled = false;
      this.goToButton.buttonMode = false;
    }
  }

  disableButtons() {
    this.resolveButton.enabled = false;
    this.resolveButton.buttonMode = false;
    this.reActiveButton.enabled = false;
    this.reActiveButton.buttonMode = false;
    this.detailsButton.enabled = false;
    this.detailsButton.buttonMode = false;
    this.goToButton.enabled = false;
    this.goToButton.buttonMode = false;
  }

  isValidJSON(jsonStr: string): boolean {
  try {
    jsonStr = JSON.parse(jsonStr);
    return true;
  } catch (e) {
    return false;
  }
}
    /**
   * This function serves when the rate button is clicked it opens the popup withthe
   * autorefresh rate set.
   **/
     optionsHandler(): void {
      try {
        this.win= SwtPopUpManager.createPopUp(this,
          WorkFlowOptionsPopUp,
          {
            title: "Options", // childTitle,
            lastValue:this.lastApplyThresholdSetting
          });
        this.win.width = "400";
        this.win.height = "200";
        this.win.id = "myOptionsPopUp";
        this.win.enableResize = false;
        this.win.showControls = true;
        this.win.isModal = true;
        this.win.onClose.subscribe(() => {
          // this.autoRefreshAfterStop();
        }, error => {
          console.log(error);
        });
        this.win.display();
      } catch (error) {
        SwtUtil.logError(error, this.moduleId,"Dashboard"  , "optionsHandler", this.errorLocation);
      }
    }


    saveApplyCcyThreshold(ccyThresholdValue){
      this.lastApplyThresholdSetting = ccyThresholdValue;
      this.saveSettings();


    }
    

}

// Define lazy loading routes
const routes: Routes = [
  { path: '', component: WorkFlowMonitor }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
// Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [WorkFlowMonitor],
  entryComponents: []
})
export class WorkFlowMonitorModule {}
