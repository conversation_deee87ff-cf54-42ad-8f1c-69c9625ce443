(window.webpackJsonp=window.webpackJsonp||[]).push([[68],{"3CAa":function(t,e,i){"use strict";i.r(e);var o=i("CcnG"),n=i("mrSG"),l=i("447K"),a=i("ZYCi"),r=i("wd/R"),s=i.n(r),u=i("ik3b"),d=i("6blF"),h=(i("0GgQ"),function(t){function e(e,i){var o=t.call(this,i,e)||this;return o.commonService=e,o.element=i,o.jsonReader=new l.L,o.inputData=new l.G(o.commonService),o.alertingData=new l.G(o.commonService),o.cancelExport=new l.G(o.commonService),o.lockMovements=new l.G(o.commonService),o.filterConfData=new l.G(o.commonService),o.updateRefreshRate=new l.G(o.commonService),o.baseURL=l.Wb.getBaseURL(),o.actionMethod="",o.actionPath="",o.requestParams=[],o.refreshStatus="N",o.selectIndex=-1,o.ccyCurrency="",o.columnLength=0,o.tempFontSize="",o.screenName="Entity Monitor - SMART Predict",o.columnChangeFlag=!1,o.controlBarHideFlag=!1,o.buttonBarHideFlag=!1,o.comboChange=!1,o.updateDateFlag=!1,o.refreshRate=10,o.screenVersion=new l.V(o.commonService),o.versionNumber="1.1.0008",o.comboOpen=!1,o.widthData=new l.G(o.commonService),o.currentFontSize="",o.tooltipEntityId=null,o.tooltipCurrencyCode=null,o.tooltipFacilityId=null,o.tooltipSelectedDate=null,o.selectedNodeId=null,o.treeLevelValue=null,o.lastSelectedTooltipParams=null,o.eventsCreated=!1,o.customTooltip=null,o.swtAlert=new l.bb(e),window.Main=o,o}return n.d(e,t),e.ngOnDestroy=function(){instanceElement=null,window.Main=null},e.prototype.ngOnInit=function(){instanceElement=this,this.entityMonitorGrid=this.cvGridContainer.addChild(l.pb),this.totalsGrid=this.cvTotalsContainer.addChild(l.qb),this.entityMonitorGrid.lockedColumnCount=2,this.entityMonitorGrid.uniqueColumn="ccy",this.totalsGrid.lockedColumnCount=2,this.entityMonitorGrid.hideHorizontalScrollBar=!0,this.entityMonitorGrid.listenHorizontalScrollEvent=!0,this.totalsGrid.fireHorizontalScrollEvent=!0,this.entityMonitorGrid.forceSameColumnSize=!0,this.entityMonitorGrid.forceSameColumnException=["ccy"],this.entityMonitorGrid.addPreHeaderBackGroundColor=!0,this.totalsGrid.selectable=!1,this.currGrpCombo.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.selectCcyGroup",null),this.startDate.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.date",null),this.accountRadio.label=l.Wb.getPredictMessage("label.entityMonitor.accountId",null),this.accountRadio.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.accountMonitor",null),this.movementRadio.label=l.Wb.getPredictMessage("label.entityMonitor.movementId",null),this.movementRadio.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.mvmntBrkdown",null),this.bookRadio.label=l.Wb.getPredictMessage("label.entityMonitor.bookCode",null),this.bookRadio.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.bookBrkdown",null),this.groupRadio.label=l.Wb.getPredictMessage("label.entityMonitor.group",null),this.groupRadio.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.viewGroupMonitor",null),this.metagroupRadio.label=l.Wb.getPredictMessage("label.entityMonitor.metagroup",null),this.metagroupRadio.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.viewMetagroupMonitor",null),this.refreshButton.label=l.Wb.getPredictMessage("button.entityMonitor.refresh",null),this.refreshButton.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.refreshWindow",null),this.optionsButton.label=l.Wb.getPredictMessage("button.entityMonitor.option",null),this.optionsButton.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.option",null),this.closeButton.label=l.Wb.getPredictMessage("button.entityMonitor.close",null),this.closeButton.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.close",null),this.chkEntityOffset.label=l.Wb.getPredictMessage("label.entityMonitor.entityOffset",null),this.chkEntityOffset.toolTip=l.Wb.getPredictMessage("tooltip.entityMonitor.entityOffset",null),this.dataBuildingText.text=l.Wb.getPredictMessage("screen.buildInProgress",null),this.lostConnectionText.text=l.Wb.getPredictMessage("screen.connectionError",null),this.lastRefText.text=l.Wb.getPredictMessage("screen.lastRefresh",null)},e.prototype.getParamsFromParent=function(){return{sqlParams:this.lastSelectedTooltipParams,facilityId:this.tooltipFacilityId,selectedNodeId:this.selectedNodeId,treeLevelValue:this.treeLevelValue,tooltipCurrencyCode:this.tooltipCurrencyCode,tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate}},e.prototype.createTooltip=function(){var t=this;this.customTooltip&&this.customTooltip.close&&this.removeTooltip();try{this.customTooltip=l.Eb.createPopUp(parent,l.u,{}),window.innerHeight<this.positionY+450&&(this.positionY=120),this.customTooltip.setWindowXY(this.positionX+20,this.positionY),this.customTooltip.enableResize=!1,this.customTooltip.width="410",this.customTooltip.height="450",this.customTooltip.enableResize=!1,this.customTooltip.title="Alert Summary Tooltip",this.customTooltip.showControls=!0,this.customTooltip.showHeader=!1,this.customTooltip.parentDocument=this,this.customTooltip.processBox=this,this.customTooltip.display(),setTimeout(function(){t.eventsCreated||t.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe(function(e){t.lastSelectedTooltipParams=e.noode.data,l.x.call("openAlertInstanceSummary","openAlertInstSummary",t.selectedNodeId,t.treeLevelValue)})},0),setTimeout(function(){t.eventsCreated||t.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe(function(e){t.getScenarioFacility(e.noode.data.scenario_id),t.lastSelectedTooltipParams=e.noode.data,t.hostId=e.hostId,t.entityId=t.lastSelectedTooltipParams.ENTITY,t.currencyId=t.lastSelectedTooltipParams.CCY})},0),setTimeout(function(){t.eventsCreated||t.customTooltip.getChild().ITEM_CLICK.subscribe(function(e){t.selectedNodeId=e.noode.data.id,t.treeLevelValue=e.noode.data.treeLevelValue,t.customTooltip.getChild().linkToSpecificButton.enabled=!1})},0)}catch(e){console.log("SwtCommonGrid -> createTooltip -> error",e)}},e.prototype.getScenarioFacility=function(t){var e=this;this.requestParams=[],this.alertingData.cbStart=this.startOfComms.bind(this),this.alertingData.cbStop=this.endOfComms.bind(this),this.alertingData.cbFault=this.inputDataFault.bind(this),this.alertingData.encodeURL=!1,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getScenarioFacility",this.requestParams.scenarioId=t,this.alertingData.url=this.baseURL+this.actionPath+this.actionMethod,this.alertingData.cbResult=function(t){e.openGoToScreen(t)},this.alertingData.send(this.requestParams)},e.prototype.openGoToScreen=function(t){if(t&&t.ScenarioSummary&&t.ScenarioSummary.scenarioFacility){var e=t.ScenarioSummary.scenarioFacility,i=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.entity_id?this.lastSelectedTooltipParams.entity_id:this.tooltipEntityId,o=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.currency_code?this.lastSelectedTooltipParams.currency_code:this.tooltipCurrencyCode,n=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.match_id?this.lastSelectedTooltipParams.match_id:null,a=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.movement_id?this.lastSelectedTooltipParams.movement_id:null,r=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.sweep_id?this.lastSelectedTooltipParams.sweep_id:null;l.x.call("goTo",e,this.hostId,i,n,o,a,r,"")}},e.prototype.removeTooltip=function(){null!=this.customTooltip&&this.customTooltip.close()},e.prototype.itemClickFunction=function(t){var e=this;null==t.target||null==t.target.field||"alerting"!=t.target.field||"C"!=t.target.data.alerting&&"Y"!=t.target.data.alerting?this.removeTooltip():(this.tooltipCurrencyCode=t.target.data.ccy,this.tooltipEntityId="All",this.tooltipFacilityId="ENTITY_MONITOR_CCY_ROW",this.tooltipSelectedDate=null,setTimeout(function(){e.createTooltip()},100))},e.prototype.onLoad=function(){var t=this;this.systemDate=l.x.call("eval","dbDate"),this.testDate=this.systemDate,this.entityMonitorGrid.columnWidthChanged.subscribe(function(e){t.columnWidthChange(e)}),this.optionsButton.enabled=!1,this.dateFormat=l.x.call("eval","dateFormat"),this.initializeMenus(),this.chkEntityOffset.selected&&(this.startDate.enabled=!1),this.startDate.formatString=this.dateFormat,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="entityMonitor.do?",this.actionMethod="method=unspecified",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.entityMonitorGrid.ITEM_CLICK.subscribe(function(e){t.itemClickFunction(e),t.onGridCellClick(e)}),d.a.fromEvent(document.body,"click").subscribe(function(e){t.positionX=e.clientX,t.positionY=e.clientY}),l.v.subscribe(function(e){t.export(e)})},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,"");var t=new l.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=l.Eb.createPopUp(this,l.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="400",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.display()},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.disableInterface(),this.startDate.enabled=!1},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableInterface(),this.startDate.enabled=!0},e.prototype.disableInterface=function(){this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1},e.prototype.enableInterface=function(){this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0},e.prototype.inputDataFault=function(t){this.lostConnectionText.visible=!0,null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())},e.prototype.inputDataResult=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else{if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.dataExport.enabled=!0,this.dataBuildingText.visible=!1,this.lastRefTime.text=this.jsonReader.getScreenAttributes().lastRefTime,this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON)if(this.optionsButton.enabled||(this.optionsButton.enabled=!0),this.chkEntityOffset.selected?this.startDate.enabled=!1:this.startDate.enabled=!0,this.startDate.showToday=!1,this.startDate.text=this.jsonReader.getScreenAttributes().currentdate,this.currGrpCombo.setComboData(this.jsonReader.getSelects()),this.selectedGroup.text=this.currGrpCombo.selectedValue,this.currentFontSize=this.jsonReader.getScreenAttributes().currfontsize,this.jsonReader.isDataBuilding())this.dataBuildingText.visible=!0;else{var e={columns:this.jsonReader.getColumnData()},i={columns:this.lastRecievedJSON.entitymonitor.grid.metadataTotal.columns};this.entityMonitorGrid.CustomGrid(e),this.totalsGrid.CustomGrid(i);for(var o=0;o<this.entityMonitorGrid.columnDefinitions.length;o++){var n=this.entityMonitorGrid.columnDefinitions[o];if("alerting"==n.field){var a="./"+l.x.call("eval","alertOrangeImage"),r="./"+l.x.call("eval","alertRedImage");"Normal"==this.currentFontSize?n.properties={enabled:!1,columnName:"alerting",imageEnabled:a,imageCritEnabled:r,imageDisabled:"",_toolTipFlag:!0,style:" display: block; margin-left: auto; margin-right: auto;"}:n.properties={enabled:!1,columnName:"alerting",imageEnabled:a,imageCritEnabled:r,imageDisabled:"",_toolTipFlag:!0,style:"height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;"},this.entityMonitorGrid.columnDefinitions[o].editor=null,this.entityMonitorGrid.columnDefinitions[o].formatter=u.a}}this.jsonReader.getRowSize()>=1?(this.entityMonitorGrid.gridData=this.jsonReader.getGridData(),this.entityMonitorGrid.setRowSize=this.jsonReader.getRowSize(),this.totalsGrid.gridData=this.jsonReader.getTotalsData(),this.dataExport.enabled=!0):this.dataExport.enabled=!1,"Normal"==this.currentFontSize?(this.entityMonitorGrid.styleName="dataGridNormal",this.entityMonitorGrid.rowHeight=18,this.totalsGrid.styleName="dataGridNormal",this.totalsGrid.rowHeight=18):"Small"==this.currentFontSize&&(this.entityMonitorGrid.styleName="dataGridSmall",this.entityMonitorGrid.rowHeight=15,this.totalsGrid.styleName="dataGridSmall",this.totalsGrid.rowHeight=15),this.refreshRate=parseInt(this.jsonReader.getRefreshRate()),null==this.autoRefresh?(this.autoRefresh=new l.cc(1e3*this.refreshRate,0),this.autoRefresh.addEventListener("timer",this.dataRefresh.bind(this))):this.autoRefresh.delay(1e3*this.refreshRate),this.prevRecievedJSON=this.lastRecievedJSON}this.systemDate=this.jsonReader.getScreenAttributes().sysDate}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error");null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())}},e.prototype.dataRefresh=function(t){this.refreshStatus="Y",this.comboOpen||this.updateData("yes"),this.autoRefresh.stop()},e.prototype.startAutoRefresh=function(t){var e=this;setTimeout(function(){e.updateData("no"),"Y"==t&&(e.updateData("no"),null!=e.autoRefresh&&e.autoRefresh.start())},0)},e.prototype.onPaymentSuccess=function(t){var e=this;setTimeout(function(){e.updateData("no"),t.detail&&"Y"==t.detail&&(e.updateData("no"),null!=e.autoRefresh&&e.autoRefresh.start())},0)},e.prototype.onGridCellClick=function(t){try{if(this.selectIndex=this.entityMonitorGrid.selectedIndex,-1!=this.entityMonitorGrid.selectedIndex){var e=t.target.field,i="T"==t.target.name,o=t.target.data.slickgrid_rowcontent[e]?t.target.data.slickgrid_rowcontent[e].date:"",n=t.target.data.ccy?t.target.data.ccy:"",l=t.target.columnGroup;""==(t.target.data.slickgrid_rowcontent[e]?t.target.data.slickgrid_rowcontent[e].content:"")||i||"ccy"==e||"alerting"==e||""==e||this.clickLink(l,n,o,this.breakdown.selectedValue.toString())}else this.selectIndex=-1,this.updateData("no")}catch(a){console.log("errorrrr",a)}},e.prototype.clickLink=function(t,e,i,o){l.x.call("clickLink",t,e,i,o)},e.prototype.showHideControlBar=function(t){this.controlBarHideFlag?(this.swtControlBar.visible=!0,this.imgShowHideControlBar.styleName="minusIcon",this.imgShowHideControlBar.toolTip="Hide Button Bar"):(this.swtControlBar.visible=!1,this.swtControlBar.includeInLayout=!1,this.imgShowHideControlBar.styleName="plusIcon",this.imgShowHideControlBar.toolTip="Show Button Bar",this.vboxCanvas.height="100%",this.entityMonitorGrid.resizeGrid()),this.controlBarHideFlag=!this.controlBarHideFlag},e.prototype.showHideButtonBar=function(t){this.buttonBarHideFlag?(this.swtButtonBar.visible=!0,this.imgShowHideButtonBar.styleName="minusIcon",this.imgShowHideButtonBar.toolTip="Hide Button Bar"):(this.swtButtonBar.visible=!1,this.swtButtonBar.includeInLayout=!1,this.imgShowHideButtonBar.styleName="plusIcon",this.imgShowHideButtonBar.toolTip="Show Button Bar",this.vboxCanvas.height="100%",this.entityMonitorGrid.resizeGrid()),this.buttonBarHideFlag=!this.buttonBarHideFlag},e.prototype.openedCombo=function(t){this.comboOpen=!0,this.inputData.isBusy()&&(this.enableInterface(),this.inputData.cancel())},e.prototype.closedCombo=function(t){this.comboOpen=!1},e.prototype.changeCombo=function(t){this.comboChange=!0,this.updateData("no")},e.prototype.updateData=function(t){this.requestParams=[],"yes"==t?(this.refreshStatus="Y",this.requestParams.currencyCode=this.ccyCurrency):(this.ccyCurrency="",this.refreshStatus="N");var e=this.currGrpCombo.selectedItem.content;this.requestParams["entityMonitor.currentDateAsString"]=this.startDate.text,this.requestParams["entityMonitor.currGrp"]=e,this.requestParams.systemDate=this.systemDate,this.requestParams["entityMonitor.entityOffsetTime"]=this.chkEntityOffset.selected,this.requestParams.autoRefresh=t,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.send(this.requestParams)},e.prototype.export=function(t){var e=[];e.push("Ccy="+this.currGrpCombo.selectedLabel),e.push("Date="+this.startDate.text),this.dataExport.convertData(this.lastRecievedJSON.entitymonitor.grid.metadata.columns,this.entityMonitorGrid,this.totalsGrid.gridData,e,t,!0)},e.prototype.updateTotalGrid=function(t){if(this.requestParams=[],this.startDate.selectedDate){this.requestParams["entityMonitor.currentDateAsString"]=this.startDate.text;var e=this.currGrpCombo.selectedLabel;this.requestParams["entityMonitor.currGrp"]=e,this.requestParams.systemDate=this.systemDate,this.requestParams["entityMonitor.entityOffsetTime"]=this.chkEntityOffset.selected,this.refreshStatus="Y",this.ccyCurrency=t,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.requestParams.currencyCode=t,this.inputData.send(this.requestParams)}},e.prototype.optionClick=function(){this.refreshStatus="N",null!=this.autoRefresh&&(this.autoRefresh.stop(),this.autoRefresh=null),l.x.call("openEntityOptionsWindow")},e.prototype.closeHandler=function(){l.x.call("close")},e.prototype.validateStartDate=function(t){var e=this;try{var i=void 0,o=l.Wb.getPredictMessage("alert.enterValidDate",null);if(!t.text)return this.swtAlert.error(o,null,null,null,function(){e.setFocusDateField(t)}),!1;if(!(i=s()(t.text,this.dateFormat.toUpperCase(),!0)).isValid())return this.swtAlert.error(o,null,null,null,function(){e.setFocusDateField(t)}),!1;t.selectedDate=i.toDate()}catch(n){console.log("error in validateDateField",n)}return!0},e.prototype.setFocusDateField=function(t){t.setFocus(),t.text=this.jsonReader.getScreenAttributes().currentdate},e.prototype.validateDate=function(){this.validateStartDate(this.startDate)&&this.updateDataFromDate()},e.prototype.entityOffsetTime=function(){this.chkEntityOffset.selected?this.startDate.enabled=!1:this.startDate.enabled=!0,this.updateData("no")},e.prototype.updateDataFromDate=function(){if(this.requestParams=[],this.startDate.selectedDate){this.requestParams["entityMonitor.currentDateAsString"]=this.startDate.text;var t=this.currGrpCombo.selectedItem.content;this.requestParams["entityMonitor.currGrp"]=t,this.requestParams["entityMonitor.entityOffsetTime"]=this.chkEntityOffset.selected,this.updateDateFlag=!0,this.inputData.send(this.requestParams)}},e.prototype.doHelp=function(){l.x.call("help")},e.prototype.columnWidthChange=function(t){this.requestParams=[],this.widthData.encodeURL=!1,this.actionMethod="method=saveColumnWidth",this.actionPath="entityMonitor.do?",this.requestParams.method="saveColumnWidth";for(var e=this.entityMonitorGrid.gridObj.getColumns(),i=0;i<e.length;i++)if("dummy"!=e[i].id&&e[i].width!=e[i].previousWidth){var o=e[i].width;return o<20&&(o=20),o>200&&(o=200),this.requestParams.width=o,this.widthData.url=this.baseURL+this.actionPath+this.actionMethod,this.widthData.send(this.requestParams,null),void this.totalsGrid.setRefreshColumnWidths(this.entityMonitorGrid.gridObj.getColumns())}},e}(l.yb)),c=[{path:"",component:h}],b=(a.l.forChild(c),function(){return function(){}}()),p=i("pMnS"),m=i("RChO"),g=i("t6HQ"),f=i("WFGK"),y=i("5FqG"),w=i("Ip0R"),R=i("gIcY"),v=i("t/Na"),C=i("sE5F"),S=i("OzfB"),I=i("T7CS"),T=i("S7LP"),D=i("6aHO"),B=i("WzUx"),M=i("A7o+"),G=i("zCE2"),k=i("Jg5P"),P=i("3R0m"),x=i("hhbb"),N=i("5rxC"),J=i("Fzqc"),O=i("21Lb"),L=i("hUWP"),W=i("3pJQ"),H=i("V9q+"),F=i("VDKW"),E=i("kXfT"),_=i("BGbe");i.d(e,"EntityMonitorModuleNgFactory",function(){return A}),i.d(e,"RenderType_EntityMonitor",function(){return q}),i.d(e,"View_EntityMonitor_0",function(){return Z}),i.d(e,"View_EntityMonitor_Host_0",function(){return j}),i.d(e,"EntityMonitorNgFactory",function(){return V});var A=o.Gb(b,[],function(t){return o.Qb([o.Rb(512,o.n,o.vb,[[8,[p.a,m.a,g.a,f.a,y.Cb,y.Pb,y.r,y.rc,y.s,y.Ab,y.Bb,y.Db,y.qd,y.Hb,y.k,y.Ib,y.Nb,y.Ub,y.yb,y.Jb,y.v,y.A,y.e,y.c,y.g,y.d,y.Kb,y.f,y.ec,y.Wb,y.bc,y.ac,y.sc,y.fc,y.lc,y.jc,y.Eb,y.Fb,y.mc,y.Lb,y.nc,y.Mb,y.dc,y.Rb,y.b,y.ic,y.Yb,y.Sb,y.kc,y.y,y.Qb,y.cc,y.hc,y.pc,y.oc,y.xb,y.p,y.q,y.o,y.h,y.j,y.w,y.Zb,y.i,y.m,y.Vb,y.Ob,y.Gb,y.Xb,y.t,y.tc,y.zb,y.n,y.qc,y.a,y.z,y.rd,y.sd,y.x,y.td,y.gc,y.l,y.u,y.ud,y.Tb,V]],[3,o.n],o.J]),o.Rb(4608,w.m,w.l,[o.F,[2,w.u]]),o.Rb(4608,R.c,R.c,[]),o.Rb(4608,R.p,R.p,[]),o.Rb(4608,v.j,v.p,[w.c,o.O,v.n]),o.Rb(4608,v.q,v.q,[v.j,v.o]),o.Rb(5120,v.a,function(t){return[t,new l.tb]},[v.q]),o.Rb(4608,v.m,v.m,[]),o.Rb(6144,v.k,null,[v.m]),o.Rb(4608,v.i,v.i,[v.k]),o.Rb(6144,v.b,null,[v.i]),o.Rb(4608,v.f,v.l,[v.b,o.B]),o.Rb(4608,v.c,v.c,[v.f]),o.Rb(4608,C.c,C.c,[]),o.Rb(4608,C.g,C.b,[]),o.Rb(5120,C.i,C.j,[]),o.Rb(4608,C.h,C.h,[C.c,C.g,C.i]),o.Rb(4608,C.f,C.a,[]),o.Rb(5120,C.d,C.k,[C.h,C.f]),o.Rb(5120,o.b,function(t,e){return[S.j(t,e)]},[w.c,o.O]),o.Rb(4608,I.a,I.a,[]),o.Rb(4608,T.a,T.a,[]),o.Rb(4608,D.a,D.a,[o.n,o.L,o.B,T.a,o.g]),o.Rb(4608,B.c,B.c,[o.n,o.g,o.B]),o.Rb(4608,B.e,B.e,[B.c]),o.Rb(4608,M.l,M.l,[]),o.Rb(4608,M.h,M.g,[]),o.Rb(4608,M.c,M.f,[]),o.Rb(4608,M.j,M.d,[]),o.Rb(4608,M.b,M.a,[]),o.Rb(4608,M.k,M.k,[M.l,M.h,M.c,M.j,M.b,M.m,M.n]),o.Rb(4608,B.i,B.i,[[2,M.k]]),o.Rb(4608,B.r,B.r,[B.L,[2,M.k],B.i]),o.Rb(4608,B.t,B.t,[]),o.Rb(4608,B.w,B.w,[]),o.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),o.Rb(1073742336,w.b,w.b,[]),o.Rb(1073742336,R.n,R.n,[]),o.Rb(1073742336,R.l,R.l,[]),o.Rb(1073742336,G.a,G.a,[]),o.Rb(1073742336,k.a,k.a,[]),o.Rb(1073742336,R.e,R.e,[]),o.Rb(1073742336,P.a,P.a,[]),o.Rb(1073742336,M.i,M.i,[]),o.Rb(1073742336,B.b,B.b,[]),o.Rb(1073742336,v.e,v.e,[]),o.Rb(1073742336,v.d,v.d,[]),o.Rb(1073742336,C.e,C.e,[]),o.Rb(1073742336,x.b,x.b,[]),o.Rb(1073742336,N.b,N.b,[]),o.Rb(1073742336,S.c,S.c,[]),o.Rb(1073742336,J.a,J.a,[]),o.Rb(1073742336,O.d,O.d,[]),o.Rb(1073742336,L.c,L.c,[]),o.Rb(1073742336,W.a,W.a,[]),o.Rb(1073742336,H.a,H.a,[[2,S.g],o.O]),o.Rb(1073742336,F.b,F.b,[]),o.Rb(1073742336,E.a,E.a,[]),o.Rb(1073742336,_.b,_.b,[]),o.Rb(1073742336,l.Tb,l.Tb,[]),o.Rb(1073742336,b,b,[]),o.Rb(256,v.n,"XSRF-TOKEN",[]),o.Rb(256,v.o,"X-XSRF-TOKEN",[]),o.Rb(256,"config",{},[]),o.Rb(256,M.m,void 0,[]),o.Rb(256,M.n,void 0,[]),o.Rb(256,"popperDefaults",{},[]),o.Rb(1024,a.i,function(){return[[{path:"",component:h}]]},[])])}),z=[[".canvasWithGreyBorder{border:1px solid #696969!important}"]],q=o.Hb({encapsulation:2,styles:z,data:{}});function Z(t){return o.dc(0,[o.Zb(*********,1,{_container:0}),o.Zb(*********,2,{imgShowHideControlBar:0}),o.Zb(*********,3,{imgShowHideButtonBar:0}),o.Zb(*********,4,{refreshButton:0}),o.Zb(*********,5,{optionsButton:0}),o.Zb(*********,6,{closeButton:0}),o.Zb(*********,7,{swtControlBar:0}),o.Zb(*********,8,{swtControlBarHbox:0}),o.Zb(*********,9,{cvGridContainer:0}),o.Zb(*********,10,{cvTotalsContainer:0}),o.Zb(*********,11,{swtButtonBar:0}),o.Zb(*********,12,{ccyLabel:0}),o.Zb(*********,13,{currGrpCombo:0}),o.Zb(*********,14,{selectedGroup:0}),o.Zb(*********,15,{dateLabel:0}),o.Zb(*********,16,{startDate:0}),o.Zb(*********,17,{chkEntityOffset:0}),o.Zb(*********,18,{breakdown:0}),o.Zb(*********,19,{accountRadio:0}),o.Zb(*********,20,{movementRadio:0}),o.Zb(*********,21,{bookRadio:0}),o.Zb(*********,22,{groupRadio:0}),o.Zb(*********,23,{metagroupRadio:0}),o.Zb(*********,24,{loadingImage:0}),o.Zb(*********,25,{helpIcon:0}),o.Zb(*********,26,{dataExport:0}),o.Zb(*********,27,{lastRefText:0}),o.Zb(*********,28,{dataBuildingText:0}),o.Zb(*********,29,{lostConnectionText:0}),o.Zb(*********,30,{lastRefTime:0}),o.Zb(*********,31,{vboxCanvas:0}),(t()(),o.Jb(31,0,null,null,104,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var o=!0,n=t.component;"creationComplete"===e&&(o=!1!==n.onLoad()&&o);return o},y.ad,y.hb)),o.Ib(32,4440064,null,0,l.yb,[o.r,l.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),o.Jb(33,0,null,0,102,"VBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["verticalGap","0"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(34,4440064,null,0,l.ec,[o.r,l.i,o.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(t()(),o.Jb(35,0,null,0,7,"HBox",[["height","11"],["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(36,4440064,null,0,l.C,[o.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(37,0,null,0,1,"HBox",[["width","99%"]],null,null,null,y.Dc,y.K)),o.Ib(38,4440064,null,0,l.C,[o.r,l.i],{width:[0,"width"]},null),(t()(),o.Jb(39,0,null,0,3,"HBox",[["horizontalAlign","right"],["width","1%"]],null,null,null,y.Dc,y.K)),o.Ib(40,4440064,null,0,l.C,[o.r,l.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),o.Jb(41,0,null,0,1,"SwtButton",[["id","imgShowHideControlBar"],["styleName","minusIcon"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.showHideControlBar(i)&&o);return o},y.Mc,y.T)),o.Ib(42,4440064,[[2,4],["imgShowHideControlBar",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],styleName:[1,"styleName"]},{onClick_:"click"}),(t()(),o.Jb(43,0,null,0,49,"SwtCanvas",[["height","60"],["marginTop","-5"],["minWidth","1000"],["width","99%"]],null,null,null,y.Nc,y.U)),o.Ib(44,4440064,[[7,4],["swtControlBar",4]],0,l.db,[o.r,l.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],marginTop:[3,"marginTop"]},null),(t()(),o.Jb(45,0,null,0,29,"Grid",[["height","100%"],["paddingLeft","10"],["width","50%"]],null,null,null,y.Cc,y.H)),o.Ib(46,4440064,null,0,l.z,[o.r,l.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),o.Jb(47,0,null,0,13,"GridRow",[["height","50%"]],null,null,null,y.Bc,y.J)),o.Ib(48,4440064,null,0,l.B,[o.r,l.i],{height:[0,"height"]},null),(t()(),o.Jb(49,0,null,0,3,"GridItem",[["width","110"]],null,null,null,y.Ac,y.I)),o.Ib(50,4440064,null,0,l.A,[o.r,l.i],{width:[0,"width"]},null),(t()(),o.Jb(51,0,null,0,1,"SwtLabel",[["textDictionaryId","label.entityMonitor.currency.group"]],null,null,null,y.Yc,y.fb)),o.Ib(52,4440064,[[12,4],["ccyLabel",4]],0,l.vb,[o.r,l.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),o.Jb(53,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(54,4440064,null,0,l.A,[o.r,l.i],null,null),(t()(),o.Jb(55,0,null,0,1,"SwtComboBox",[["dataLabel","currencygroup"],["id","currGrpCombo"],["width","130"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var n=!0,l=t.component;"window:mousewheel"===e&&(n=!1!==o.Tb(t,56).mouseWeelEventHandler(i.target)&&n);"change"===e&&(n=!1!==l.changeCombo(i)&&n);return n},y.Pc,y.W)),o.Ib(56,4440064,[[13,4],["currGrpCombo",4]],0,l.gb,[o.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),o.Jb(57,0,null,0,3,"GridItem",[["paddingLeft","3"]],null,null,null,y.Ac,y.I)),o.Ib(58,4440064,null,0,l.A,[o.r,l.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),o.Jb(59,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),o.Ib(60,4440064,[[14,4],["selectedGroup",4]],0,l.vb,[o.r,l.i],{fontWeight:[0,"fontWeight"]},null),(t()(),o.Jb(61,0,null,0,13,"GridRow",[["height","50%"],["paddingTop","2"]],null,null,null,y.Bc,y.J)),o.Ib(62,4440064,null,0,l.B,[o.r,l.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(t()(),o.Jb(63,0,null,0,3,"GridItem",[["width","110"]],null,null,null,y.Ac,y.I)),o.Ib(64,4440064,null,0,l.A,[o.r,l.i],{width:[0,"width"]},null),(t()(),o.Jb(65,0,null,0,1,"SwtLabel",[["textDictionaryId","label.entityMonitor.date"]],null,null,null,y.Yc,y.fb)),o.Ib(66,4440064,[[15,4],["dateLabel",4]],0,l.vb,[o.r,l.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),o.Jb(67,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(68,4440064,null,0,l.A,[o.r,l.i],null,null),(t()(),o.Jb(69,0,null,0,1,"SwtDateField",[["width","70"]],null,[[null,"change"]],function(t,e,i){var o=!0,n=t.component;"change"===e&&(o=!1!==n.validateDate()&&o);return o},y.Tc,y.ab)),o.Ib(70,4308992,[[16,4],["startDate",4]],0,l.lb,[o.r,l.i,o.T],{width:[0,"width"]},{changeEventOutPut:"change"}),(t()(),o.Jb(71,0,null,0,3,"GridItem",[["paddingLeft","35"]],null,null,null,y.Ac,y.I)),o.Ib(72,4440064,null,0,l.A,[o.r,l.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),o.Jb(73,0,null,0,1,"SwtCheckBox",[["selected","true"]],null,[[null,"change"]],function(t,e,i){var o=!0,n=t.component;"change"===e&&(o=!1!==n.entityOffsetTime()&&o);return o},y.Oc,y.V)),o.Ib(74,4440064,[[17,4],["chkEntityOffset",4]],0,l.eb,[o.r,l.i],{selected:[0,"selected"]},{change_:"change"}),(t()(),o.Jb(75,0,null,0,17,"HBox",[["horizontalAlign","right"],["width","50%"]],null,null,null,y.Dc,y.K)),o.Ib(76,4440064,null,0,l.C,[o.r,l.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),o.Jb(77,0,null,0,15,"fieldset",[],null,null,null,null,null)),(t()(),o.Jb(78,0,null,null,1,"legend",[],null,null,null,null,null)),(t()(),o.bc(-1,null,["Breakdown"])),(t()(),o.Jb(80,0,null,null,12,"SwtRadioButtonGroup",[["align","horizontal"],["id","breakdown"]],null,null,null,y.ed,y.lb)),o.Ib(81,4440064,[[18,4],["breakdown",4]],1,l.Hb,[v.c,o.r,l.i],{id:[0,"id"],align:[1,"align"]},null),o.Zb(*********,32,{radioItems:1}),(t()(),o.Jb(83,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","accountRadio"],["selected","true"],["value","A"]],null,null,null,y.fd,y.mb)),o.Ib(84,4440064,[[32,4],[19,4],["accountRadio",4]],0,l.Ib,[o.r,l.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"],selected:[3,"selected"]},null),(t()(),o.Jb(85,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","movementRadio"],["value","M"]],null,null,null,y.fd,y.mb)),o.Ib(86,4440064,[[32,4],[20,4],["movementRadio",4]],0,l.Ib,[o.r,l.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),o.Jb(87,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","bookRadio"],["value","B"]],null,null,null,y.fd,y.mb)),o.Ib(88,4440064,[[32,4],[21,4],["bookRadio",4]],0,l.Ib,[o.r,l.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),o.Jb(89,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","groupRadio"],["value","G"]],null,null,null,y.fd,y.mb)),o.Ib(90,4440064,[[32,4],[22,4],["groupRadio",4]],0,l.Ib,[o.r,l.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),o.Jb(91,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","metagroupRadio"],["value","MG"]],null,null,null,y.fd,y.mb)),o.Ib(92,4440064,[[32,4],[23,4],["metagroupRadio",4]],0,l.Ib,[o.r,l.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),o.Jb(93,0,null,0,5,"VBox",[["height","100%"],["minWidth","1000"],["verticalGap","0"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(94,4440064,[[31,4],["vboxCanvas",4]],0,l.ec,[o.r,l.i,o.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"]},null),(t()(),o.Jb(95,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,y.Nc,y.U)),o.Ib(96,4440064,[[9,4],["cvGridContainer",4]],0,l.db,[o.r,l.i],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(t()(),o.Jb(97,0,null,0,1,"SwtCanvas",[["border","false"],["height","40"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,y.Nc,y.U)),o.Ib(98,4440064,[[10,4],["cvTotalsContainer",4]],0,l.db,[o.r,l.i],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(t()(),o.Jb(99,0,null,0,7,"HBox",[["height","11"],["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(100,4440064,null,0,l.C,[o.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(101,0,null,0,1,"HBox",[["width","99%"]],null,null,null,y.Dc,y.K)),o.Ib(102,4440064,null,0,l.C,[o.r,l.i],{width:[0,"width"]},null),(t()(),o.Jb(103,0,null,0,3,"HBox",[["horizontalAlign","right"],["width","1%"]],null,null,null,y.Dc,y.K)),o.Ib(104,4440064,null,0,l.C,[o.r,l.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),o.Jb(105,0,null,0,1,"SwtButton",[["id","imgShowHideButtonBar"],["styleName","minusIcon"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.showHideButtonBar(i)&&o);return o},y.Mc,y.T)),o.Ib(106,4440064,[[3,4],["imgShowHideButtonBar",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],styleName:[1,"styleName"]},{onClick_:"click"}),(t()(),o.Jb(107,0,null,0,28,"SwtCanvas",[["height","33"],["marginBottom","0"],["marginTop","-5"],["minWidth","1000"],["width","99%"]],null,null,null,y.Nc,y.U)),o.Ib(108,4440064,[[11,4],["swtButtonBar",4]],0,l.db,[o.r,l.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],marginTop:[3,"marginTop"],marginBottom:[4,"marginBottom"]},null),(t()(),o.Jb(109,0,null,0,26,"HBox",[["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(110,4440064,null,0,l.C,[o.r,l.i],{width:[0,"width"]},null),(t()(),o.Jb(111,0,null,0,7,"HBox",[["width","60%"]],null,null,null,y.Dc,y.K)),o.Ib(112,4440064,null,0,l.C,[o.r,l.i],{width:[0,"width"]},null),(t()(),o.Jb(113,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","refreshButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.updateData("yes")&&o);return o},y.Mc,y.T)),o.Ib(114,4440064,[[4,4],["refreshButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),o.Jb(115,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","optionsButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.optionClick()&&o);return o},y.Mc,y.T)),o.Ib(116,4440064,[[5,4],["optionsButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),o.Jb(117,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.closeHandler()&&o);return o},y.Mc,y.T)),o.Ib(118,4440064,[[6,4],["closeButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),o.Jb(119,0,null,0,16,"HBox",[["horizontalAlign","right"]],null,null,null,y.Dc,y.K)),o.Ib(120,4440064,null,0,l.C,[o.r,l.i],{horizontalAlign:[0,"horizontalAlign"]},null),(t()(),o.Jb(121,0,null,0,1,"SwtLabel",[["color","red"],["visible","false"]],null,null,null,y.Yc,y.fb)),o.Ib(122,4440064,[[28,4],["dataBuildingText",4]],0,l.vb,[o.r,l.i],{visible:[0,"visible"],color:[1,"color"]},null),(t()(),o.Jb(123,0,null,0,1,"SwtLabel",[["color","red"],["visible","false"]],null,null,null,y.Yc,y.fb)),o.Ib(124,4440064,[[29,4],["lostConnectionText",4]],0,l.vb,[o.r,l.i],{visible:[0,"visible"],color:[1,"color"]},null),(t()(),o.Jb(125,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),o.Ib(126,4440064,[[27,4],["lastRefText",4]],0,l.vb,[o.r,l.i],{fontWeight:[0,"fontWeight"]},null),(t()(),o.Jb(127,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,y.Yc,y.fb)),o.Ib(128,4440064,[[30,4],["lastRefTime",4]],0,l.vb,[o.r,l.i],{fontWeight:[0,"fontWeight"]},null),(t()(),o.Jb(129,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),o.Jb(130,0,null,null,1,"DataExport",[["id","dataExport"]],null,null,null,y.Sc,y.Z)),o.Ib(131,4440064,[[26,4],["dataExport",4]],0,l.kb,[l.i,o.r],{id:[0,"id"]},null),(t()(),o.Jb(132,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doHelp()&&o);return o},y.Wc,y.db)),o.Ib(133,4440064,[[25,4],["helpIcon",4]],0,l.rb,[o.r,l.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),o.Jb(134,0,null,0,1,"SwtLoadingImage",[],null,null,null,y.Zc,y.gb)),o.Ib(135,114688,[[24,4],["loadingImage",4]],0,l.xb,[o.r],null,null)],function(t,e){t(e,32,0,"100%","100%");t(e,34,0,"0","100%","100%","5","5");t(e,36,0,"100%","11");t(e,38,0,"99%");t(e,40,0,"right","1%");t(e,42,0,"imgShowHideControlBar","minusIcon");t(e,44,0,"99%","60","1000","-5");t(e,46,0,"50%","100%","10");t(e,48,0,"50%");t(e,50,0,"110");t(e,52,0,"label.entityMonitor.currency.group"),t(e,54,0);t(e,56,0,"currencygroup","130","currGrpCombo");t(e,58,0,"3");t(e,60,0,"normal");t(e,62,0,"50%","2");t(e,64,0,"110");t(e,66,0,"label.entityMonitor.date"),t(e,68,0);t(e,70,0,"70");t(e,72,0,"35");t(e,74,0,"true");t(e,76,0,"right","50%");t(e,81,0,"breakdown","horizontal");t(e,84,0,"accountRadio","breakdown","A","true");t(e,86,0,"movementRadio","breakdown","M");t(e,88,0,"bookRadio","breakdown","B");t(e,90,0,"groupRadio","breakdown","G");t(e,92,0,"metagroupRadio","breakdown","MG");t(e,94,0,"0","100%","100%","1000");t(e,96,0,"canvasWithGreyBorder","100%","100%","false");t(e,98,0,"canvasWithGreyBorder","100%","40","false");t(e,100,0,"100%","11");t(e,102,0,"99%");t(e,104,0,"right","1%");t(e,106,0,"imgShowHideButtonBar","minusIcon");t(e,108,0,"99%","33","1000","-5","0");t(e,110,0,"100%");t(e,112,0,"60%");t(e,114,0,"refreshButton","true");t(e,116,0,"optionsButton","true");t(e,118,0,"closeButton","true");t(e,120,0,"right");t(e,122,0,"false","red");t(e,124,0,"false","red");t(e,126,0,"normal");t(e,128,0,"normal");t(e,131,0,"dataExport");t(e,133,0,"helpIcon"),t(e,135,0)},null)}function j(t){return o.dc(0,[(t()(),o.Jb(0,0,null,null,1,"app-entity-monitor",[],null,[["window","entityMonitor.refresh"]],function(t,e,i){var n=!0;"window:entityMonitor.refresh"===e&&(n=!1!==o.Tb(t,1).onPaymentSuccess(i)&&n);return n},Z,q)),o.Ib(1,4440064,null,0,h,[l.i,o.r],null,null)],function(t,e){t(e,1,0)},null)}var V=o.Fb("app-entity-monitor",h,j,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);