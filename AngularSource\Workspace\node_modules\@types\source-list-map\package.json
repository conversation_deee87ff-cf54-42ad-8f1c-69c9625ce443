{"_from": "@types/source-list-map@*", "_id": "@types/source-list-map@0.1.6", "_inBundle": false, "_integrity": "sha512-5JcVt1u5HDmlXkwOD2nslZVllBBc7HDuOICfiZah2Z0is8M8g+ddAEawbmd3VjedfDHBzxCaXLs07QEmb7y54g==", "_location": "/@types/source-list-map", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/source-list-map@*", "name": "@types/source-list-map", "escapedName": "@types%2fsource-list-map", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/webpack-sources"], "_resolved": "https://registry.npmjs.org/@types/source-list-map/-/source-list-map-0.1.6.tgz", "_shasum": "164e169dd061795b50b83c19e4d3be09f8d3a454", "_spec": "@types/source-list-map@*", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\@types\\webpack-sources", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "e-cloud", "url": "https://github.com/e-cloud"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for source-list-map", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/source-list-map", "license": "MIT", "main": "", "name": "@types/source-list-map", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/source-list-map"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "07d3ed18f56b2577ea692c7caa363a48937da1181a8af2b18a95e2447c75c614", "version": "0.1.6"}