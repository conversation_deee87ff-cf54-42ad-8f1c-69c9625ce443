import { Component, OnInit, ViewChild, ElementRef, ModuleWithProviders, NgModule, ViewEncapsulation, Output, Input, AfterViewInit } from '@angular/core';
import { SwtLoadingImage, SwtCanvas, SwtButton, JSONReader, HTTPComms, SwtUtil, SwtAlert, Logger, CommonService, SwtCommonGrid, ExternalInterface, SwtToolBoxModule, SwtTextArea, GridRow, Encryptor, SwtModule, SwtLabel, SwtTextInput, VDividedBox } from 'swt-tool-box';
import { Routes, RouterModule } from '@angular/router';
import { DiffEditorModel, MonacoEditorModule } from 'ngx-monaco-editor';
import { CommonModule } from '@angular/common';
@Component({
  selector: 'app-maintenance-log-view',
  templateUrl: './MaintenanceLogView.html',
  encapsulation: ViewEncapsulation.None,
  styleUrls: ['./MaintenanceLogView.css']
})
export class MaintenanceLogView  extends SwtModule implements OnInit, AfterViewInit {

  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;

  /***********SwtCanvas***********/
  @ViewChild('viewLogGridContainer') viewLogGridContainer: SwtCanvas;

    /***********SwtButton***********/
  @ViewChild('closeButton') closeButton: SwtButton;

    /***********SwtLabel***********/
    @ViewChild('facilityLbl') facilityLbl: SwtLabel;
    @ViewChild('dateLbl') dateLbl: SwtLabel;
    @ViewChild('ipAddressLbl') ipAddressLbl: SwtLabel;
    @ViewChild('userLbl') userLbl: SwtLabel;
    @ViewChild('recordRefLbl') recordRefLbl: SwtLabel;
    @ViewChild('actionLbl') actionLbl: SwtLabel;
    @ViewChild('fullDetailsLbl') fullDetailsLbl: SwtLabel;
    @ViewChild('oldValLbl') oldValLbl: SwtLabel;
    @ViewChild('newValLbl') newValLbl: SwtLabel;

    @ViewChild('facilityVal') facilityVal: SwtLabel;
    @ViewChild('dateVal') dateVal: SwtLabel;
    @ViewChild('ipAddressVal') ipAddressVal: SwtLabel;
    @ViewChild('userIdVal') userIdVal: SwtLabel;
    @ViewChild('userNameVal') userNameVal: SwtLabel;
    @ViewChild('recordRefVal') recordRefVal: SwtLabel;
    @ViewChild('actionVal') actionVal: SwtLabel;
    @ViewChild('vDivider') vDivider: VDividedBox;


    
  /**
  * Data Objects
  **/
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public initReceivedJSON;
  /**
    * Communication Objects
    **/
  public inputData = new HTTPComms(this.commonService);
  public baseURL: string = SwtUtil.getBaseURL();
  private actionMethod: string = "";
  private actionPath: string = "";
  private swtAlert: SwtAlert;
  private _invalidComms: string;
  private menuAccessId;
  private requestParams = [];
  private viewLogGrid;
  private logger: Logger = null;
  private fromValue;
  private toValue;
  text1 = '';
  text2 = '';
  isCompared = false;

  @Output()
  selectedLang = 'plaintext';
  @Output()
  selectedTheme = 'vs';

  @Input()
  languages = [
    'bat',
    'c',
    'coffeescript',
    'cpp',
    'csharp',
    'csp',
    'css',
    'dockerfile',
    'fsharp',
    'go',
    'handlebars',
    'html',
    'ini',
    'java',
    'javascript',
    'json',
    'less',
    'lua',
    'markdown',
    'msdax',
    'mysql',
    'objective-c',
    'pgsql',
    'php',
    'plaintext',
    'postiats',
    'powershell',
    'pug',
    'python',
    'r',
    'razor',
    'redis',
    'redshift',
    'ruby',
    'rust',
    'sb',
    'scss',
    'sol',
    'sql',
    'st',
    'swift',
    'typescript',
    'vb',
    'xml',
    'yaml'
  ];

  @Input()
  themes = [
    {
      value: 'vs',
      name: 'Visual Studio'
    },
    {
      value: 'vs-dark',
      name: 'Visual Studio Dark'
    },
    {
      value: 'hc-black',
      name: 'High Contrast Dark'
    }
  ];

  // input
  inputOptions = {
    theme: 'vs',
    language: 'plaintext',
    minimap: {
      enabled: false
    },
    scrollbar: {
      // Subtle shadows to the left & top. Defaults to true.
      useShadows: false,
      // Render vertical arrows. Defaults to false.
      verticalHasArrows: false,
      // Render horizontal arrows. Defaults to false.
      horizontalHasArrows: false,
      // Render vertical scrollbar.
      // Accepted values: 'auto', 'visible', 'hidden'.
      // Defaults to 'auto'
      vertical: 'hidden',
      // Render horizontal scrollbar.
      // Accepted values: 'auto', 'visible', 'hidden'.
      // Defaults to 'auto'
      horizontal: 'hidden'
    }
  };
  // compare, output
  diffOptions = {
    theme: 'vs',
    language: 'plaintext',
    readOnly: true,
    renderSideBySide: true
  };
  originalModel: DiffEditorModel = {
    code: '',
    language: 'plaintext'
  };

  modifiedModel: DiffEditorModel = {
    code: '',
    language: 'plaintext'
  };
  
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Maintenance Log View Maintenance', this.commonService.httpclient);
  }

  ngAfterViewInit(): void {
    if ((window as any).monaco) {
      (window as any).monaco.editor.setTheme('vs');
    }
  }

  ngOnInit() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.viewLogGrid = <SwtCommonGrid>this.viewLogGridContainer.addChild(SwtCommonGrid);
      errorLocation = 10;
      this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
      this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.close', null);
      this.facilityLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.facility', null);
      this.dateLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.date', null);
      this.ipAddressLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.ipAddress', null);
      this.userLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.user', null);
      this.recordRefLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.recordRef', null);
      this.actionLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.action', null);
      this.fullDetailsLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.fullDetails', null);
      this.oldValLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.oldVal', null);
      this.newValLbl.text = SwtUtil.getPredictMessage('maintenanceLogView.newVal', null);

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [ngOnInit] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'MaintenanceLogView.ts', "ngOnInit", errorLocation);
    }
  }


  onLoad() {
    this.requestParams = [];
    //Variable for errorLocation
    let errorLocation = 0;
    try {

      var logDate = ExternalInterface.call('eval', 'logDate');
      errorLocation = 10;
      var userId = ExternalInterface.call('eval', 'userId');
      errorLocation = 20;
      var ipAddress = ExternalInterface.call('eval', 'ipAddress');
      errorLocation = 30;
      var tableName = ExternalInterface.call('eval', 'tableName');
      errorLocation = 40;
      var reference = ExternalInterface.call('eval', 'reference');
      errorLocation = 50;
      var action = ExternalInterface.call('eval', 'action');
      errorLocation = 60;
      this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
      errorLocation = 70;
      if (this.menuAccessId) {
        if (this.menuAccessId !== "") {
          this.menuAccessId = Number(this.menuAccessId);
        }
      }
      errorLocation = 80;
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      errorLocation = 90;
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.actionPath = "maintenancelog.do?";
      this.actionMethod = 'method=displayViewLog';
      this.requestParams['menuAccessId'] = this.menuAccessId;
      this.requestParams['userId'] = userId;
      this.requestParams['logDate'] = logDate;
      this.requestParams['ipAddress'] = ipAddress;
      this.requestParams['tableName'] = tableName;
      this.requestParams['reference'] = reference;
      errorLocation = 100;
      this.requestParams['action'] = action;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 110;
      this.inputData.send(this.requestParams);

      this.viewLogGrid.onRowClick = (event) => {
        errorLocation = 120;
        this.cellClickEventHandler(event);
      };


      this.vDivider.DIVIDER_DRAG_COMPLETE.subscribe((event) => {
        window.dispatchEvent(new Event('resize'));
      });


    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onLoad] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'MaintenanceLogView.ts', "onLoad", errorLocation);
    }

  }

  cellClickEventHandler(event) {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      if (this.viewLogGrid.selectedIndex >= 0) {
        errorLocation = 10;
        this.fromValue = this.viewLogGrid.selectedItem.changedFrom.content;
        errorLocation = 20;
        this.toValue = this.viewLogGrid.selectedItem.changedTo.content;
        errorLocation = 30;
        this.onCompare();
      } else {
        errorLocation = 40;
        this.fromValue = "";
        this.toValue = "";
        this.onCompare();
      }

    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [cellClickEventHandler] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'MaintenanceLogView.ts', "cellClickEventHandler", errorLocation);
    }
  }

  onCompare() {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      this.originalModel = Object.assign({}, this.originalModel, {
        code: this.fromValue
      });
      errorLocation = 10;
      this.modifiedModel = Object.assign({}, this.originalModel, {
        code: this.toValue
      });
      errorLocation = 20;
      this.isCompared = true;
      window.scrollTo(0, 0); // scroll the window to top
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [onCompare] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'MaintenanceLogView.ts', "onCompare", errorLocation);
    }
  }



  inputDataResult(event): void {
    //Variable for errorLocation
    let errorLocation = 0;
    try {
      // Checks the inputData and stops the communication
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        errorLocation = 10;
        //this.dataExport.enabled = true;

        if (this.jsonReader.getRequestReplyStatus()) {
          if ((this.lastRecievedJSON != this.prevRecievedJSON)) {

            if (!this.jsonReader.isDataBuilding()) {
              errorLocation = 20;
              //get parent selected row details
              const logDate= this.jsonReader.getSingletons().logDate;
              this.dateVal.text= logDate;
              const userId= this.jsonReader.getSingletons().userId;
              this.userIdVal.text= userId;
              const userName= this.jsonReader.getSingletons().userName;
              this.userNameVal.text= userName?"<"+userName+">":"";
              const ipAddress= this.jsonReader.getSingletons().ipAddress;
              this.ipAddressVal.text= ipAddress;
              const tableName= this.jsonReader.getSingletons().tableName;
              this.facilityVal.text= tableName;
              const reference= this.jsonReader.getSingletons().reference;
              this.recordRefVal.text= reference;
              const action= this.getActionDesc(this.jsonReader.getSingletons().action);
              this.actionVal.text= action;
              errorLocation = 30;
              const obj = { columns: this.lastRecievedJSON.MaintLogView.maintLogViewGrid.metadata.columns };
              errorLocation = 40;
              this.viewLogGrid.CustomGrid(obj);
              errorLocation = 50;
              var gridRows = this.lastRecievedJSON.MaintLogView.maintLogViewGrid.rows;
              errorLocation = 60;
              if (gridRows.size > 0) {
                for (let i = 0; i < gridRows.size; i++) {
                  if (!(gridRows.row).length)
                    gridRows.row = [gridRows.row];
                  errorLocation = 70;
                  gridRows.row[i].changedFrom.content = gridRows.row[i].changedFrom.content ? Encryptor.decode64(gridRows.row[i].changedFrom.content) : "";
                  errorLocation = 80;
                  gridRows.row[i].changedTo.content = gridRows.row[i].changedTo.content ? Encryptor.decode64(gridRows.row[i].changedTo.content) : "";
                }
                this.viewLogGrid.gridData = gridRows;
                errorLocation = 90;
                this.viewLogGrid.setRowSize = this.jsonReader.getRowSize();
                errorLocation = 100;
                this.viewLogGrid.refresh();
              }
              else {
                this.viewLogGrid.gridData = { size: 0, row: [] };
              }
              this.prevRecievedJSON = this.lastRecievedJSON;
            }
          }
        } else {
          if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
            this.swtAlert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
          }

        }
      }
    } catch (error) {
      // log the error in ERROR LOG
      this.logger.error('method [inputDataResult] - error: ', error, 'errorLocation: ', errorLocation);
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'MaintenanceLogView.ts', "inputDataResult", errorLocation);
    }
  }

  getActionDesc(actionCode) {
    var desc = null;

    switch (actionCode) {
      case "I":
        desc = "Added";
        break;
      case "U":
        desc = "Changed";
        break;
      case "D":
        desc = "Deleted";
        break;
      default:
        break;
    }

    return desc;
  }

  closeHandler() {
    window.close();
  }

  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }


  /**                                                                                                                  
   * If a fault occurs with the connection with the server then display the lost connection label                      
   * @param event:FaultEvent                                                                                           
   **/
  private inputDataFault(event): void {
    this._invalidComms = event.fault.faultString + "\n" + event.fault.faultCode + "\n" + event.fault.faultDetail;
    this.swtAlert.show("fault " + this._invalidComms);
  }

}


//Define lazy loading routes
const routes: Routes = [
  { path: '', component: MaintenanceLogView}
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule,MonacoEditorModule,CommonModule],
  declarations: [MaintenanceLogView],
  entryComponents: []
})
export class MaintenanceLogViewModule {
 }
