# 🔄 Matching Process - Functional Specification

## 📋 Document Information

| **Field** | **Value** |
|-----------|-----------|
| **Document Title** | Matching Process Functional Specification |
| **Version** | 1.0 |
| **Date** | 2025-01-03 |
| **Purpose** | Customer Guide to Automated Movement Matching |
| **Audience** | Business Users, System Administrators, Support Teams |

---

## 🎯 Executive Summary

The **Matching Process** is an automated system designed to identify and link related financial movements across different position levels within the trading and settlement environment. This process ensures accurate reconciliation by intelligently matching movements based on multiple criteria including amounts, dates, accounts, counterparties, and reference numbers.

### Key Benefits
- **🤖 Automation**: Reduces manual reconciliation effort by up to 80%
- **🎯 Accuracy**: Uses sophisticated quality scoring to ensure reliable matches
- **⚡ Efficiency**: Processes thousands of movements in minutes
- **🔍 Transparency**: Provides clear audit trails for all matching decisions
- **📊 Flexibility**: Configurable parameters to meet business requirements

---

## 🏗️ System Architecture Overview

### Core Components

#### 1. **Position Level Processing**
The system processes movements across **11 different position levels** in a structured hierarchy:

| **Level** | **Description** | **Typical Source** |
|-----------|-----------------|-------------------|
| **9-6** | External Positions | Bank statements, external confirmations |
| **5-3** | Internal Positions | Trade confirmations, internal systems |
| **2-1** | Settlement Positions | Final settlement confirmations |
| **Pre-advice** | Preliminary Notifications | Advance notices, pre-settlement data |

#### 2. **Quality Assessment Engine**
- **Multi-factor Analysis**: Evaluates 7+ matching criteria
- **Scoring System**: A=5 (Perfect) to E=1 (Poor)
- **Threshold Management**: Configurable quality thresholds per position level

#### 3. **Match Action Framework**
- **Automated Decisions**: Auto-match high-quality candidates
- **Manual Review**: Flag uncertain matches for review
- **Exception Handling**: Special processing for complex scenarios

---

## 🔄 Process Flow Detailed Description

### Phase 1: Initialization & Configuration

#### System Startup
1. **Parameter Loading**: Retrieves configuration from `S_ENTITY` table
2. **Threshold Setting**: Applies position-level and currency-specific thresholds
3. **Scheduler Check**: Verifies system is enabled for processing

#### Key Configuration Parameters

| **Parameter** | **Default** | **Description** |
|---------------|-------------|-----------------|
| `POS_LEVEL_THRESHOLD` | 6 | Minimum position level for processing |
| `PREADVICE_SEARCH_ALWAYS` | N | Always search pre-advice positions |
| `PREADVICE_PREDICT_STRATEGY` | 1 | Pre-advice matching strategy |
| `ACC_LINKING_EXEMPTION_LEVEL` | 7 | Account linking exemption level |
| `ENFORCE_REFERENCE_SELECTION` | N | Enforce reference-based matching |
| `EUR_DAYS_AHEAD` | 0 | Days ahead for EUR processing |
| `OTHER_CURRENCIES_DAYS_AHEAD` | 7 | Days ahead for other currencies |

### Phase 2: Source Movement Selection

#### Selection Criteria
The system identifies source movements based on:

```sql
-- Core Selection Logic
WHERE MATCH_STATUS = 'L'           -- Outstanding status
  AND PREDICT_STATUS IN ('E', 'I') -- External or Internal
  AND TO_MATCH_DATE <= INPUT_DATE  -- Within processing window
  AND AMOUNT >= THRESHOLD_AMOUNT   -- Above minimum threshold
  AND POSITION_LEVEL = CURRENT_LEVEL -- Current processing level
```

#### Movement Locking
- **Concurrent Protection**: Prevents duplicate processing
- **Stage Progression**: Updates `TO_MATCH_STAGE` for tracking
- **Timestamp Recording**: Records processing timestamps

### Phase 3: Target Identification Strategy

The system employs different strategies based on source characteristics:

#### Strategy A: Reference-Based Matching
**When**: Source has strong reference data
**Targets**: Movements with matching references
**Quality Focus**: Reference accuracy, party matching

#### Strategy B: Amount-Based Matching  
**When**: Source has reliable amount information
**Targets**: Movements with matching or complementary amounts
**Quality Focus**: Amount precision, date proximity

#### Strategy C: Pre-advice Matching
**When**: Processing pre-advice positions
**Targets**: Confirmed movements awaiting pre-advice
**Quality Focus**: Timing, reference validation

#### Strategy D: Higher Position Matching
**When**: Source is from lower position levels
**Targets**: Higher position level movements
**Quality Focus**: Hierarchical consistency

### Phase 4: Quality Assessment

#### Multi-Factor Quality Calculation

##### 1. **Date Quality Assessment**
```
Perfect Match (A=5): Source Date = Target Date
Good Match (B=4):    Within ±1 business day
Poor Match (E=1):    Beyond tolerance range
```

##### 2. **Amount Quality Assessment**
```
Perfect Match (A=5): Exact amount match
Good Match (B=4):    Within configured tolerance %
Poor Match (E=1):    Beyond tolerance range
```

##### 3. **Account Quality Assessment**
```
Perfect Match (A=5): Same account number
Good Match (B=4):    Linked accounts (per linking table)
Poor Match (E=1):    Unrelated accounts
```

##### 4. **Party Quality Assessment**
- **Counterparty Matching**: Exact vs. partial name matching
- **Beneficiary Matching**: Account holder validation
- **Custodian Matching**: Custodian bank verification

##### 5. **Reference Quality Assessment**
- **Cross-Reference Validation**: System reference matching
- **External Reference Matching**: Client reference correlation
- **Book Code Matching**: Trading book consistency

#### Quality Matrix Application
The system applies position-level specific quality matrices from `P_MATCH_QUALITY`:

```sql
-- Quality Matrix Logic
FINAL_QUALITY = APPLY_MATRIX(
    DATE_QUALITY,
    AMOUNT_QUALITY, 
    ACCOUNT_QUALITY,
    PARTY_QUALITY,
    REFERENCE_QUALITY,
    POSITION_LEVEL
)
```

### Phase 5: Match Decision Processing

#### Match Actions Framework

| **Action** | **Code** | **Description** | **System Behavior** |
|------------|----------|-----------------|-------------------|
| **Auto Match** | A | High confidence match | Automatically confirms match |
| **Offer Match** | B | Good candidate | Presents for user review |
| **Confirm Match** | C | Requires confirmation | Awaits manual approval |
| **Decline Match** | D | Poor quality | Rejects the match |
| **Exception Match** | E | Special handling | Routes to exception queue |
| **No Action** | N | Insufficient data | No processing action |

#### Decision Logic
```
IF Quality Score >= Auto_Threshold THEN
    Action = 'A' (Auto Match)
ELSIF Quality Score >= Offer_Threshold THEN  
    Action = 'B' (Offer Match)
ELSIF Quality Score >= Minimum_Threshold THEN
    Action = 'C' (Confirm Match)  
ELSE
    Action = 'D' (Decline Match)
END IF
```

### Phase 6: Amount Total Processing

#### Position-Level Calculations
For certain position levels, the system performs amount total validations:

1. **Aggregate Calculations**: Sums amounts by position and currency
2. **Balance Validation**: Ensures debits equal credits
3. **Tolerance Checking**: Applies amount total tolerances
4. **Exception Flagging**: Identifies out-of-balance conditions

### Phase 7: Match Completion & Finalization

#### Successful Match Processing
1. **Status Updates**: Updates `P_MATCH` table with match results
2. **Cross-Reference Creation**: Establishes movement relationships
3. **Lock Removal**: Releases movement locks
4. **Audit Trail**: Records match decisions and quality scores

#### Exception Handling
1. **Error Logging**: Captures processing errors
2. **Rollback Processing**: Reverses partial matches if needed
3. **Alert Generation**: Notifies support teams of issues
4. **Recovery Procedures**: Enables manual intervention

---

## ⚙️ Configuration Management

### System Parameters

#### Entity-Level Configuration
Parameters stored in `S_ENTITY` table control system behavior:

```sql
-- Example Configuration Query
SELECT PARAMETER_NAME, PARAMETER_VALUE, DESCRIPTION
FROM S_ENTITY_PARAMETERS 
WHERE ENTITY_ID = 'YOUR_ENTITY'
  AND PARAMETER_TYPE = 'MATCHING'
```

#### Position-Level Quality Configuration
Quality requirements defined in `P_MATCH_QUALITY`:

```sql
-- Quality Configuration Structure
CREATE TABLE P_MATCH_QUALITY (
    POSITION_LEVEL NUMBER,
    QUALITY_TYPE VARCHAR2(10),
    MINIMUM_SCORE NUMBER,
    WEIGHT_FACTOR NUMBER,
    ACTIVE_FLAG VARCHAR2(1)
)
```

#### Match Action Configuration
Match actions defined in `P_MATCH_ACTION`:

```sql
-- Match Action Structure  
CREATE TABLE P_MATCH_ACTION (
    POSITION_LEVEL NUMBER,
    QUALITY_RANGE_MIN NUMBER,
    QUALITY_RANGE_MAX NUMBER, 
    ACTION_CODE VARCHAR2(1),
    DESCRIPTION VARCHAR2(100)
)
```

### Currency-Specific Settings

#### Processing Windows
Different currencies may have different processing windows:

| **Currency** | **Days Ahead** | **Rationale** |
|--------------|----------------|---------------|
| **EUR** | 0 | Same-day settlement |
| **USD** | 7 | T+2 settlement cycle |
| **GBP** | 7 | T+2 settlement cycle |
| **JPY** | 7 | T+2 settlement cycle |
| **Others** | 7 | Standard settlement |

---

## 📊 Monitoring & Reporting

### Key Performance Indicators

#### Processing Metrics
- **Throughput**: Movements processed per hour
- **Match Rate**: Percentage of movements successfully matched
- **Quality Distribution**: Breakdown of matches by quality score
- **Exception Rate**: Percentage requiring manual intervention

#### Quality Metrics
- **Auto-Match Rate**: Percentage of automatic matches
- **Manual Review Rate**: Percentage requiring review
- **False Positive Rate**: Incorrect automatic matches
- **Processing Time**: Average time per movement

### Standard Reports

#### Daily Processing Summary
```
Date: 2025-01-03
Total Movements Processed: 15,847
Successful Matches: 14,203 (89.6%)
Auto Matches: 12,156 (85.6%)
Manual Review: 1,644 (11.6%) 
Exceptions: 403 (2.8%)
Average Processing Time: 0.23 seconds/movement
```

#### Quality Score Distribution
```
Quality Score A (5): 8,745 matches (61.5%)
Quality Score B (4): 3,411 matches (24.0%)
Quality Score C (3): 1,644 matches (11.6%)
Quality Score D (2): 301 matches (2.1%)
Quality Score E (1): 102 matches (0.7%)
```

---

## 🚨 Exception Handling & Troubleshooting

### Common Exception Scenarios

#### 1. **No Quality Configuration**
**Symptom**: Position level skipped during processing
**Cause**: Missing `P_MATCH_QUALITY` configuration
**Resolution**: Add quality configuration for position level

#### 2. **System Disabled**
**Symptom**: Processing stops with status 'D'
**Cause**: `S_SCHEDULER` status set to disabled
**Resolution**: Enable scheduler or check system maintenance

#### 3. **Cross-Reference Conflicts**
**Symptom**: Valid matches rejected
**Cause**: Conflicting cross-reference data
**Resolution**: Review and clean cross-reference tables

#### 4. **Amount Total Imbalances**
**Symptom**: Matches fail amount total validation
**Cause**: Rounding differences or missing movements
**Resolution**: Adjust tolerances or investigate missing data

### Diagnostic Queries

#### Check System Status
```sql
SELECT STATUS, LAST_UPDATE_DATE 
FROM S_SCHEDULER 
WHERE JOB_NAME = 'MATCHING_PROCESS'
```

#### Review Processing Statistics
```sql
SELECT POSITION_LEVEL, COUNT(*) as PROCESSED,
       AVG(QUALITY_SCORE) as AVG_QUALITY
FROM P_MATCH_HISTORY 
WHERE PROCESS_DATE = TRUNC(SYSDATE)
GROUP BY POSITION_LEVEL
```

#### Identify Exceptions
```sql
SELECT MOVEMENT_ID, EXCEPTION_TYPE, EXCEPTION_MESSAGE
FROM P_MATCH_EXCEPTIONS
WHERE EXCEPTION_DATE >= TRUNC(SYSDATE)
ORDER BY EXCEPTION_DATE DESC
```

---

## 🔧 Maintenance & Administration

### Regular Maintenance Tasks

#### Daily Tasks
- **Monitor Processing**: Review daily processing reports
- **Exception Review**: Investigate and resolve exceptions
- **Performance Check**: Monitor system performance metrics

#### Weekly Tasks  
- **Configuration Review**: Verify parameter settings
- **Quality Analysis**: Analyze match quality trends
- **Capacity Planning**: Monitor system resource usage

#### Monthly Tasks
- **Parameter Tuning**: Adjust thresholds based on performance
- **Archive Management**: Archive old processing data
- **System Optimization**: Review and optimize performance

### System Administration

#### User Access Management
- **Processing Rights**: Control who can run matching process
- **Configuration Rights**: Control parameter modification access
- **Reporting Rights**: Control access to reports and data

#### Backup & Recovery
- **Configuration Backup**: Regular backup of system parameters
- **Data Archival**: Archive processed match data
- **Recovery Procedures**: Document recovery processes

---

## 📞 Support & Contact Information

### Technical Support
- **Level 1 Support**: Daily operations and basic troubleshooting
- **Level 2 Support**: Configuration changes and complex issues  
- **Level 3 Support**: System modifications and enhancements

### Escalation Procedures
1. **Operational Issues**: Contact Level 1 Support
2. **Configuration Issues**: Escalate to Level 2 Support
3. **System Issues**: Escalate to Level 3 Support
4. **Business Impact**: Follow critical incident procedures

---

## 📚 Appendices

### Appendix A: Quick Reference Guide

#### Quality Score Quick Reference
| Score | Grade | Description | Typical Use Case |
|-------|-------|-------------|------------------|
| 5 | A | Perfect Match | Exact amount, date, reference match |
| 4 | B | Good Match | Within tolerance, strong indicators |
| 3 | C | Fair Match | Acceptable with some differences |
| 2 | D | Poor Match | Significant differences |
| 1 | E | Bad Match | Major discrepancies |

#### Match Action Quick Reference
| Code | Action | When Used | Result |
|------|--------|-----------|--------|
| A | Auto | Quality ≥ 4.5 | Automatic confirmation |
| B | Offer | Quality 3.5-4.4 | User review required |
| C | Confirm | Quality 2.5-3.4 | Manual confirmation needed |
| D | Decline | Quality < 2.5 | Automatic rejection |
| E | Exception | Special cases | Exception queue |
| N | None | Insufficient data | No action |

#### Position Level Quick Reference
| Level | Type | Description | Processing Priority |
|-------|------|-------------|-------------------|
| 9-8 | External High | Bank statements | 1st (Highest) |
| 7-6 | External Mid | External confirmations | 2nd |
| 5-4 | Internal High | Trade confirmations | 3rd |
| 3-2 | Internal Mid | Internal positions | 4th |
| 1 | Settlement | Final confirmations | 5th |
| Pre-advice | Preliminary | Advance notices | Special |

### Appendix B: Common Configuration Examples

#### Standard Quality Configuration
```sql
-- High-quality auto-matching for external positions
INSERT INTO P_MATCH_QUALITY VALUES (9, 'AUTO', 4.5, 1.0, 'Y');
INSERT INTO P_MATCH_QUALITY VALUES (8, 'AUTO', 4.5, 1.0, 'Y');

-- Manual review for internal positions
INSERT INTO P_MATCH_QUALITY VALUES (5, 'MANUAL', 3.5, 1.0, 'Y');
INSERT INTO P_MATCH_QUALITY VALUES (4, 'MANUAL', 3.5, 1.0, 'Y');
```

#### Currency-Specific Settings
```sql
-- EUR same-day processing
UPDATE S_ENTITY SET PARAMETER_VALUE = '0'
WHERE PARAMETER_NAME = 'EUR_DAYS_AHEAD';

-- USD T+2 processing
UPDATE S_ENTITY SET PARAMETER_VALUE = '2'
WHERE PARAMETER_NAME = 'USD_DAYS_AHEAD';
```

### Appendix C: Troubleshooting Quick Guide

#### Issue: No Matches Found
**Check**:
1. Quality configuration exists for position level
2. Source movements have correct status ('L')
3. Date ranges are appropriate
4. Amount thresholds are not too restrictive

#### Issue: Too Many Manual Reviews
**Check**:
1. Quality thresholds may be too strict
2. Tolerance settings may be too narrow
3. Reference data quality issues

#### Issue: System Performance Slow
**Check**:
1. Database indexes on key tables
2. Amount of data being processed
3. System resource utilization
4. Concurrent processing conflicts

---

*This document provides comprehensive guidance for understanding and operating the Matching Process system. For additional support or clarification, please contact the technical support team.*
