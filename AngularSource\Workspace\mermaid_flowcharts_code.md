# 🔄 Mermaid Flowchart Code Collection

This file contains all the Mermaid flowchart code that can be copied and pasted into the Mermaid Live Editor (https://mermaid.live/).

---

## 📊 Chart 1: Main Matching Process (Developer Version)

```mermaid
flowchart TD
    A[Start Matching Process] --> B[Initialize Parameters]
    B --> C[Get Entity Configuration]
    C --> D[Set Position Level Thresholds]
    D --> E[Set Currency-specific Days Ahead]
    E --> F[Start Position Level Loop<br/>CNTR1: 1 to 11]
    
    F --> G{Determine Source Position Level<br/>Based on CNTR1}
    G -->|CNTR1=1| H1[Position = MAX_INTERNAL_POS]
    G -->|CNTR1=2| H2[Position = 9 or 8]
    G -->|CNTR1=3| H3[Position = 8 or 7]
    G -->|CNTR1=4| H4[Position = 7 or 6]
    G -->|CNTR1=5| H5[Position = 6 or 5]
    G -->|CNTR1=6| H6[Position = 5 or 4]
    G -->|CNTR1=7| H7[Position = 4 or 3]
    G -->|CNTR1=8| H8[Position = 3 or 2 or 1]
    G -->|CNTR1=9| H9[Position = MAX_INTERNAL_POS<br/>Stage = 2]
    G -->|CNTR1=10| H10[Position = PRE_ADVICE_POS]
    G -->|CNTR1=11| H11[Position = MAX_POS_OTHER_STAGE<br/>Stage = 2]
    
    H1 --> I[Check Match Quality Exists<br/>for Position Level]
    H2 --> I
    H3 --> I
    H4 --> I
    H5 --> I
    H6 --> I
    H7 --> I
    H8 --> I
    H9 --> I
    H10 --> I
    H11 --> I
    
    I --> J{Quality Config<br/>Exists?}
    J -->|No| K[Skip to Next Position]
    J -->|Yes| L[Open Source Cursor<br/>CR_INS_SOURCE]
    
    K --> F
    
    L --> M[Fetch Source Movement]
    M --> N{Source Found?}
    N -->|No| O[Close Source Cursor]
    O --> F
    
    N -->|Yes| P[Initialize Variables]
    P --> Q[Check Scheduler Status]
    Q --> R{Matching Enabled?}
    R -->|No - Disabled| S[Clean Up & Exit]
    R -->|Yes| T[Lock Source Movement]
    
    T --> U[Update Match Stage & Date]
    U --> V{Source Match Status = 'L'?}
    V -->|No| W[Skip Source]
    V -->|Yes| X[Set Currency Code for Quality]
    
    X --> Y[Determine Target Position Check]
    Y --> Z{Target Position Check Type}
    Z -->|'N'| AA[No Target Processing]
    Z -->|'H'| BB[Higher Position Targets]
    Z -->|'P'| CC[Pre-advice Targets]
    Z -->|Other| DD[Standard Targets]
    
    BB --> EE[Open CR_INSERT_TARGET_SUPP_PASS]
    CC --> FF[Open CR_INS_TARGET_PREADVICE]
    DD --> GG{Force Reference Only?}
    GG -->|Yes| HH[Open CR_INS_TARGET_REF_PASS]
    GG -->|No| II[Open CR_INS_TARGET_VD_AMT_PASS]
    
    EE --> JJ[Target Processing Loop]
    FF --> JJ
    HH --> JJ
    II --> JJ
    
    JJ --> KK[Fetch Target Movement]
    KK --> LL{Target Found?}
    LL -->|No| MM[Close Target Cursor]
    LL -->|Yes| NN[Calculate Quality Flag]
    
    NN --> OO{Quality > 0?}
    OO -->|No| PP[Skip Target]
    OO -->|Yes| QQ[Check Movement Already Exists]
    
    QQ --> RR{Already Exists?}
    RR -->|Yes| PP
    RR -->|No| SS[Determine Insert Flag]
    
    SS --> TT{Source Internal &<br/>Target External?}
    TT -->|Yes| UU[Set Insert Flag = Y]
    TT -->|No| VV{Both Internal?}
    
    VV -->|Yes| WW{Cross Reference<br/>Exists?}
    WW -->|Yes| UU
    WW -->|No| XX{Target = Pre-advice &<br/>Check = 'B'?}
    XX -->|Yes| YY[Check Higher Position Quality]
    XX -->|No| ZZ[Skip Insert]
    
    YY --> AAA{Higher Quality<br/>Exists?}
    AAA -->|Yes| UU
    AAA -->|No| ZZ
    
    VV -->|No| BBB{Source External?}
    BBB -->|Yes| CCC[External Target Processing]
    BBB -->|No| UU
    
    CCC --> DDD[Reference Validation]
    DDD --> EEE{References Match?}
    EEE -->|Yes| UU
    EEE -->|No| FFF[Break Match if Needed]
    
    UU --> GGG[Insert Target Movement]
    GGG --> HHH[Add Cross References]
    HHH --> III[Update Quality Counters]
    
    ZZ --> PP
    FFF --> PP
    PP --> JJ
    III --> JJ
    
    MM --> JJJ[Process Amount Total Logic]
    JJJ --> KKK[Inner Target Processing]
    KKK --> LLL[Update Match Actions]
    LLL --> MMM[Check Match Completion]
    
    MMM --> NNN{Match Complete?}
    NNN -->|Yes| OOO[Finalize Match]
    NNN -->|No| PPP[Continue Processing]
    
    OOO --> QQQ[Update Match Status]
    QQQ --> RRR[Clean Up Targets]
    RRR --> SSS[Remove Locks]
    
    PPP --> W
    SSS --> W
    W --> M
    
    AA --> TTT[End Position Processing]
    TTT --> F
    
    S --> UUU[End Process]
    
    style A fill:#e1f5fe
    style UUU fill:#ffebee
    style R fill:#fff3e0
    style OO fill:#f3e5f5
    style WW fill:#e8f5e8
    style NNN fill:#fff8e1
```

---

## ⚙️ Chart 2: Detailed Conditions & Decisions (Developer Version)

```mermaid
flowchart TD
    A[SP_MATCHING_PROCEDURE Start] --> B[Initialize Configuration]
    B --> B1[Get PRE_ADVICE_POS from S_ENTITY]
    B1 --> B2[Get POS_LEVEL_THRESHOLD Default: 6]
    B2 --> B3[Get DEBUG mode Default: N]
    B3 --> B4[Get PREADVICE_SEARCH_ALWAYS Default: N]
    B4 --> B5[Get PREADVICE_PREDICT_STRATEGY Default: 1]
    B5 --> B6[Get ACC_LINKING_EXEMPTION_LEVEL Default: 7]
    B6 --> B7[Get ENFORCE_REFERENCE_SELECTION Default: N]
    B7 --> B8[Get PREADVICE_SEARCH_POSITIONS Default: 6,4]
    B8 --> B9[Set Days Ahead: EUR=0, Others=7]
    
    B9 --> C[Position Loop: CNTR1 = 1 to 11]
    
    C --> D{CNTR1 Value}
    D -->|1| E1[Source Pos = MAX_INTERNAL_POS Stage = 1]
    D -->|2| E2[Source Pos = 9 or 8 Stage = 1]
    D -->|3| E3[Source Pos = 8 or 7 Stage = 1]
    D -->|4| E4[Source Pos = 7 or 6 Stage = 1]
    D -->|5| E5[Source Pos = 6 or 5 Stage = 1]
    D -->|6| E6[Source Pos = 5 or 4 Stage = 1]
    D -->|7| E7[Source Pos = 4 or 3 Stage = 1]
    D -->|8| E8[Source Pos = 3/2/1 Stage = 1]
    D -->|9| E9[Source Pos = MAX_INTERNAL_POS Stage = 2]
    D -->|10| E10[Source Pos = PRE_ADVICE_POS Stage = 1]
    D -->|11| E11[Source Pos = MAX_POS_OTHER_STAGE Stage = 2]
    
    E1 --> F[Check P_MATCH_QUALITY exists]
    E2 --> F
    E3 --> F
    E4 --> F
    E5 --> F
    E6 --> F
    E7 --> F
    E8 --> F
    E9 --> F
    E10 --> F
    E11 --> F
    
    F --> G{Quality Config Found?}
    G -->|No| H[Next Position]
    G -->|Yes| I[Open CR_INS_SOURCE Cursor]
    
    H --> C
    
    I --> J[Source Selection Criteria]
    J --> K[Fetch Source Movement]
    K --> L{Source Found?}
    L -->|No| M[Close Cursor Next Position]
    L -->|Yes| N[Initialize Source Variables]
    
    M --> C
    
    N --> O[Check S_SCHEDULER Status]
    O --> P{Job Status?}
    P -->|Disabled 'D'| Q[Clean Up & Exit with Status 'D']
    P -->|Enabled 'E'| R[Lock Source Movement]
    
    R --> S[Update TO_MATCH_STAGE + 1 Update TO_MATCH_DATE]
    S --> T{Source MATCH_STATUS = 'L'?}
    T -->|No| U[Skip to Next Source]
    T -->|Yes| V[Set Currency Code for Quality]
    
    V --> W[Determine Target Position Check]
    W --> X{Target Check Logic}
    X -->|Pre-advice Search| Y1[Check = 'P' - Pre-advice]
    X -->|Higher Position| Y2[Check = 'H' - Higher Position]
    X -->|Both Positions| Y3[Check = 'B' - Both Positions]
    X -->|Lower Positions| Y4[Check = 'L' - Lower Positions]
    X -->|No Targets| Y5[Check = 'N' - No Processing]
    
    Y1 --> Z1[Open CR_INS_TARGET_PREADVICE]
    Y2 --> Z2[Open CR_INSERT_TARGET_SUPP_PASS]
    Y3 --> Z3{Force Reference Only?}
    Y4 --> Z3
    Y5 --> AA[Skip Target Processing]
    
    Z3 -->|Yes| Z4[Open CR_INS_TARGET_REF_PASS]
    Z3 -->|No| Z5[Open CR_INS_TARGET_VD_AMT_PASS]
    
    Z1 --> BB[Target Processing Loop]
    Z2 --> BB
    Z4 --> BB
    Z5 --> BB
    
    BB --> CC[Fetch Target Movement]
    CC --> DD{Target Found?}
    DD -->|No| EE[Process Amount Total Logic]
    DD -->|Yes| FF[Calculate Quality using FN_UPDATE_P_B_MATQUAL_POSLEVEL]
    
    FF --> GG{Quality > 0?}
    GG -->|No| HH[Skip Target]
    GG -->|Yes| II[Check if Movement Already Exists in P_B_TARGET_MOVEMENTS]
    
    II --> JJ{Already Exists?}
    JJ -->|Yes| HH
    JJ -->|No| KK[Determine Insert Flag Logic]
    
    KK --> LL{Source Internal & Target External?}
    LL -->|Yes| MM[Insert Flag = Y]
    LL -->|No| NN{Both Internal?}
    
    NN -->|Yes| OO{Cross Reference Check}
    OO -->|FNISCROSSREFEXISTS = 'Y'| MM
    OO -->|No Cross Ref| PP{Target not Pre-advice & Check = 'B'?}
    PP -->|Yes| QQ[Check Higher Position Quality]
    PP -->|No| RR[Insert Flag = N]
    
    QQ --> SS{Higher Quality Count > 0?}
    SS -->|Yes| MM
    SS -->|No| RR
    
    NN -->|No| TT{Source External?}
    TT -->|Yes| UU[External Target Processing Reference Validation]
    TT -->|No| MM
    
    UU --> VV{References Match?}
    VV -->|Yes| MM
    VV -->|No| WW[Break Match Logic]
    
    MM --> XX[Insert into Tables]
    XX --> YY[Update Quality Counters by Position]
    
    RR --> HH
    WW --> HH
    HH --> BB
    YY --> BB
    
    EE --> ZZ[SP_MATCHING_AMOUNT_TOTAL]
    ZZ --> AAA[Inner Target Processing Quality-based Selection]
    AAA --> BBB[SP_UPDATE_MATCH_ACTIONS]
    BBB --> CCC[Check Match Completion]
    
    CCC --> DDD{Match Complete?}
    DDD -->|Yes| EEE[Update Match Status Clean Up Targets Remove Locks]
    DDD -->|No| FFF[Continue to Next Source]
    
    EEE --> FFF
    FFF --> U
    U --> K
    
    AA --> GGG[End Position Processing]
    GGG --> C
    
    Q --> HHH[End Process]
    
    style A fill:#e1f5fe
    style HHH fill:#ffebee
    style P fill:#fff3e0
    style GG fill:#f3e5f5
    style OO fill:#e8f5e8
    style DDD fill:#fff8e1
    style MM fill:#e8f5e8
    style RR fill:#ffebee
```

---

## 🎯 Chart 3: Quality Calculation & Matching Logic (Developer Version)

```mermaid
flowchart TD
    A[Quality Calculation Start] --> B[Input Parameters]
    B --> C[Initialize Quality Variables A=5 B=4 C=3 D=2 E=1]
    
    C --> D[Check Value Date Match]
    D --> E{Source Date = Target Date?}
    E -->|Yes| F[Date Quality = A]
    E -->|No| G{Within Tolerance?}
    G -->|Yes| H[Date Quality = B]
    G -->|No| I[Date Quality = E]
    
    F --> J[Check Amount Match]
    H --> J
    I --> J
    
    J --> K{Source Amount = Target Amount?}
    K -->|Yes| L[Amount Quality = A]
    K -->|No| M{Within Tolerance?}
    M -->|Yes| N[Amount Quality = B]
    M -->|No| O[Amount Quality = E]
    
    L --> P[Check Account Match]
    N --> P
    O --> P
    
    P --> Q{Account Check}
    Q -->|Same Account| R[Account Quality = A]
    Q -->|Linked Accounts| S[Account Quality = B]
    Q -->|Different| T[Account Quality = E]
    
    R --> U[Check Counterparty]
    S --> U
    T --> U
    
    U --> V{Counterparty Match?}
    V -->|Exact| W[Counterparty Quality = A]
    V -->|Partial| X[Counterparty Quality = B]
    V -->|None| Y[Counterparty Quality = E]
    
    W --> Z[Check Beneficiary]
    X --> Z
    Y --> Z
    
    Z --> AA{Beneficiary Match?}
    AA -->|Exact| BB[Beneficiary Quality = A]
    AA -->|Partial| CC[Beneficiary Quality = B]
    AA -->|None| DD[Beneficiary Quality = E]
    
    BB --> EE[Check Custodian]
    CC --> EE
    DD --> EE
    
    EE --> FF{Custodian Match?}
    FF -->|Exact| GG[Custodian Quality = A]
    FF -->|Partial| HH[Custodian Quality = B]
    FF -->|None| II[Custodian Quality = E]
    
    GG --> JJ[Check Book Code]
    HH --> JJ
    II --> JJ
    
    JJ --> KK{Book Code Match?}
    KK -->|Exact| LL[Book Code Quality = A]
    KK -->|None| MM[Book Code Quality = E]
    
    LL --> NN[Get Quality Matrix]
    MM --> NN
    
    NN --> OO[Calculate Combined Quality]
    OO --> PP[Apply Quality Matrix Logic]
    PP --> QQ[Return Final Quality Score]
    
    QQ --> RR{Quality Score OK?}
    RR -->|Yes| SS[Accept Target]
    RR -->|No| TT[Reject Target]
    
    SS --> UU[Insert into P_B_TARGET_MOVEMENTS]
    UU --> VV[Amount Total Processing]
    
    TT --> WW[Skip Target]
    WW --> VV
    
    VV --> XX{Amount Total Required?}
    XX -->|Yes| YY[Calculate Position Totals]
    XX -->|No| ZZ[Skip Amount Total]
    
    YY --> AAA[Inner Target Processing]
    ZZ --> AAA
    
    AAA --> BBB[Apply Match Actions]
    BBB --> CCC{Match Action}
    CCC -->|A| DDD[Auto Match]
    CCC -->|B| EEE[Offer Match]
    CCC -->|C| FFF[Confirm Match]
    CCC -->|D| GGG[Decline Match]
    CCC -->|E| HHH[Exception Match]
    CCC -->|N| III[No Action]
    
    DDD --> JJJ[Update Match Status]
    EEE --> JJJ
    FFF --> JJJ
    GGG --> KKK[Remove from Processing]
    HHH --> JJJ
    III --> KKK
    
    JJJ --> LLL{All Positions Done?}
    KKK --> LLL
    
    LLL -->|Yes| MMM[Finalize Match]
    LLL -->|No| NNN[Continue Processing]
    
    MMM --> OOO[Clean Up Tables]
    NNN --> VV
    
    OOO --> PPP[End Quality Processing]
    
    style A fill:#e1f5fe
    style PPP fill:#ffebee
    style RR fill:#fff3e0
    style XX fill:#f3e5f5
    style CCC fill:#e8f5e8
    style LLL fill:#fff8e1
```

---

## 🎨 Chart 4: Customer-Friendly Flowchart (Fixed Version)

```mermaid
flowchart TD
    START([🚀 Matching Process Starts]) --> INIT[📋 Initialize System Parameters]
    INIT --> CONFIG[⚙️ Load Configuration Settings]
    CONFIG --> LOOP[🔄 Start Position Level Processing]
    
    LOOP --> POS{📍 Select Position Level}
    POS -->|Level 6-9| HIGH[🏢 Process High-Level Positions]
    POS -->|Level 3-5| MID[🏬 Process Mid-Level Positions] 
    POS -->|Level 1-2| LOW[🏪 Process Low-Level Positions]
    POS -->|Pre-advice| PREADVICE[📨 Process Pre-advice Positions]
    
    HIGH --> QUALITY_CHECK[✅ Check Quality Configuration]
    MID --> QUALITY_CHECK
    LOW --> QUALITY_CHECK
    PREADVICE --> QUALITY_CHECK
    
    QUALITY_CHECK --> QUAL_OK{Quality Config Exists?}
    QUAL_OK -->|❌ No| NEXT_POS[➡️ Skip to Next Position]
    QUAL_OK -->|✅ Yes| SOURCE_SELECT[🎯 Select Source Movements]
    
    SOURCE_SELECT --> SOURCE_CRITERIA[📋 Apply Source Criteria]
    SOURCE_CRITERIA --> SOURCE_FOUND{Source Movement Found?}
    SOURCE_FOUND -->|❌ No| NEXT_POS
    SOURCE_FOUND -->|✅ Yes| SCHEDULER_CHECK[🔍 Check System Status]
    
    SCHEDULER_CHECK --> ENABLED{System Enabled?}
    ENABLED -->|❌ Disabled| CLEANUP[🧹 Clean Up & Exit]
    ENABLED -->|✅ Enabled| LOCK_SOURCE[🔒 Lock Source Movement]
    
    LOCK_SOURCE --> UPDATE_STAGE[📈 Update Match Stage]
    UPDATE_STAGE --> TARGET_STRATEGY[🎯 Determine Target Strategy]
    
    TARGET_STRATEGY --> STRATEGY{Target Strategy Type}
    STRATEGY -->|Reference-Based| REF_TARGETS[📄 Find Reference Matches]
    STRATEGY -->|Amount-Based| AMT_TARGETS[💰 Find Amount Matches]
    STRATEGY -->|Pre-advice| PREADVICE_TARGETS[📨 Find Pre-advice Matches]
    STRATEGY -->|Higher Position| HIGHER_TARGETS[⬆️ Find Higher Position Matches]
    
    REF_TARGETS --> TARGET_LOOP[🔄 Process Each Target]
    AMT_TARGETS --> TARGET_LOOP
    PREADVICE_TARGETS --> TARGET_LOOP
    HIGHER_TARGETS --> TARGET_LOOP
    
    TARGET_LOOP --> CALC_QUALITY[🎯 Calculate Match Quality]
    CALC_QUALITY --> QUALITY_FACTORS[📊 Evaluate Quality Factors]
    QUALITY_FACTORS --> QUALITY_SCORE[📈 Calculate Final Quality Score]
    QUALITY_SCORE --> QUALITY_OK_CHECK{Quality Score Acceptable?}
    
    QUALITY_OK_CHECK -->|❌ Poor Quality| REJECT_TARGET[❌ Reject Target]
    QUALITY_OK_CHECK -->|✅ Good Quality| VALIDATE_TARGET[✅ Validate Target]
    
    VALIDATE_TARGET --> CROSS_REF_CHECK[🔍 Check Cross References]
    CROSS_REF_CHECK --> REF_VALID{References Valid?}
    
    REF_VALID -->|✅ Valid| ACCEPT_TARGET[✅ Accept Target for Matching]
    REF_VALID -->|❌ Invalid| REJECT_TARGET
    
    ACCEPT_TARGET --> INSERT_TARGET[📝 Insert into Target Table]
    INSERT_TARGET --> MORE_TARGETS{More Targets to Process?}
    
    REJECT_TARGET --> MORE_TARGETS
    MORE_TARGETS -->|✅ Yes| TARGET_LOOP
    MORE_TARGETS -->|❌ No| AMOUNT_TOTAL[🧮 Calculate Amount Totals]
    
    AMOUNT_TOTAL --> INNER_PROCESSING[🔄 Inner Target Processing]
    INNER_PROCESSING --> MATCH_ACTIONS[⚡ Apply Match Actions]
    
    MATCH_ACTIONS --> ACTION_TYPE{Match Action Type}
    ACTION_TYPE -->|A| AUTO_MATCH[🤖 Auto Match]
    ACTION_TYPE -->|B| OFFER_MATCH[💡 Offer Match]
    ACTION_TYPE -->|C| CONFIRM_MATCH[✅ Confirm Match]
    ACTION_TYPE -->|D| DECLINE_MATCH[❌ Decline Match]
    ACTION_TYPE -->|E| EXCEPTION_MATCH[⚠️ Exception Match]
    ACTION_TYPE -->|N| NO_ACTION[⏸️ No Action]
    
    AUTO_MATCH --> UPDATE_STATUS[📊 Update Match Status]
    OFFER_MATCH --> UPDATE_STATUS
    CONFIRM_MATCH --> UPDATE_STATUS
    EXCEPTION_MATCH --> UPDATE_STATUS
    
    DECLINE_MATCH --> REMOVE_TARGET[🗑️ Remove from Processing]
    NO_ACTION --> REMOVE_TARGET
    
    UPDATE_STATUS --> COMPLETION_CHECK[🏁 Check Match Completion]
    REMOVE_TARGET --> COMPLETION_CHECK
    
    COMPLETION_CHECK --> COMPLETE{Match Complete?}
    COMPLETE -->|✅ Yes| FINALIZE[🎉 Finalize Match]
    COMPLETE -->|❌ No| MORE_SOURCES{More Sources?}
    
    MORE_SOURCES -->|✅ Yes| SOURCE_SELECT
    MORE_SOURCES -->|❌ No| NEXT_POS
    
    FINALIZE --> UPDATE_TABLES[📊 Update Match Tables]
    UPDATE_TABLES --> CLEAN_LOCKS[🔓 Remove Locks]
    CLEAN_LOCKS --> NEXT_POS
    
    NEXT_POS --> MORE_POSITIONS{More Positions?}
    MORE_POSITIONS -->|✅ Yes| LOOP
    MORE_POSITIONS -->|❌ No| END_SUCCESS[🎉 Process Complete]
    
    CLEANUP --> END_DISABLED[⏹️ Process Disabled]
    
    style START fill:#e8f5e8
    style END_SUCCESS fill:#e8f5e8
    style END_DISABLED fill:#ffebee
    style ENABLED fill:#fff3e0
    style QUALITY_OK_CHECK fill:#f3e5f5
    style REF_VALID fill:#e3f2fd
    style COMPLETE fill:#fff8e1
    style AUTO_MATCH fill:#e8f5e8
    style DECLINE_MATCH fill:#ffebee
```

---

## 📝 Usage Instructions

1. **Copy any chart code** from the sections above
2. **Go to https://mermaid.live/**
3. **Paste the code** into the editor
4. **View the rendered flowchart**
5. **Export as needed** (PNG, SVG, PDF)

## 🎯 Chart Descriptions

- **Chart 1**: Main process flow with all technical details
- **Chart 2**: Detailed conditions and decision points
- **Chart 3**: Quality calculation and matching logic
- **Chart 4**: Customer-friendly version with business terminology

All charts are compatible with Mermaid Live Editor and can be exported in various formats for presentations or documentation.
