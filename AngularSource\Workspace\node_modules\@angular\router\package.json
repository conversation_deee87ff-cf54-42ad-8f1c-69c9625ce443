{"_from": "@angular/router@7.2.4", "_id": "@angular/router@7.2.4", "_inBundle": false, "_integrity": "sha512-T8Uqf2H1SV1MQI38WwYJ4aa+4NNnvlp2Tp/rkfg6tKcp/cLkKqE6OOfiy9lmW+i/624v8tMgYoBMOUNBjAG23g==", "_location": "/@angular/router", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/router@7.2.4", "name": "@angular/router", "escapedName": "@angular%2frouter", "scope": "@angular", "rawSpec": "7.2.4", "saveSpec": null, "fetchSpec": "7.2.4"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@angular/router/-/router-7.2.4.tgz", "_shasum": "83f1997c2a4e6acda93b991b8d7f3dad2b3f91f0", "_spec": "@angular/router@7.2.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "description": "Angular - the routing library", "es2015": "./fesm2015/router.js", "esm2015": "./esm2015/router.js", "esm5": "./esm5/router.js", "fesm2015": "./fesm2015/router.js", "fesm5": "./fesm5/router.js", "homepage": "https://github.com/angular/angular/tree/master/packages/router", "keywords": ["angular", "router"], "license": "MIT", "main": "./bundles/router.umd.js", "module": "./fesm5/router.js", "name": "@angular/router", "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/http", "@angular/platform-server", "@angular/platform-webworker", "@angular/platform-webworker-dynamic", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/service-worker"]}, "peerDependencies": {"rxjs": "^6.0.0", "@angular/core": "7.2.4", "@angular/common": "7.2.4", "@angular/platform-browser": "7.2.4"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "sideEffects": false, "typings": "./router.d.ts", "version": "7.2.4"}