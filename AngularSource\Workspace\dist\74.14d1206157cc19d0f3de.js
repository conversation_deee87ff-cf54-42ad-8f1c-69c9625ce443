(window.webpackJsonp=window.webpackJsonp||[]).push([[74],{"E/gk":function(t,e,i){"use strict";i.r(e);var o=i("CcnG"),n=i("mrSG"),r=i("447K"),l=i("J8bQ"),s=i("ZYCi"),a=i("EVdn"),d=function(t){function e(e,i){var o=t.call(this,i,e)||this;return o.commonService=e,o.element=i,o.inputData=new r.G(o.commonService),o.sendData=new r.G(o.commonService),o.resizeData=new r.G(o.commonService),o.updateRefreshRate=new r.G(o.commonService),o.invalidComms="",o.screenVersion=new r.V(o.commonService),o.baseURL=r.Wb.getBaseURL(),o.actionMethod="",o.actionPath="",o.requestParams=[],o.comboChange=!1,o.refreshRate=30,o.jsonReader=new r.L,o.refreshFlag=!0,o.interval=null,o.moduleId="",o.errorLocation=0,o.states={},o.menuAccessIdParent=0,o.CANCELLED="cancelled",o.OTHER="other",o.NEW_COLUMN="newColumn",o.NEW_ROW="newRow",o.screenName="Forecast Monitor Screen",o.versionNumber="1.1.0002",o.releaseDate="20 May 2019",o.menuAccess=2,o.expandColumn="/assets/images/plus.gif",o.collapseColumn="/assets/images/minus.gif",o.maintainWidth=750,o.previousWidth=750,o.editdataChanged=new Object,o.editupdate=new Object,o.prevSelectedIndex=-1,o.alertPreventFlag=!1,o.itemId="",o.refreshStatus="N",o.columnLength=0,o.selectIndex=-1,o.itemBeginFlag=!1,o.dividerFlag=!1,o.oldDividerFlag=!0,o.lastX=0,o.lastY=0,o.rateRequest="",o.gridDataExpand=[],o.subGridDataExpand=[],o.index=0,o.isUpdateData=!1,o.swtAlert=new r.bb(e),window.Main=o,o}return n.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){var t=this;instanceElement=this,this.forecastMonitorGrid.uniqueColumn="date",this.forecastMonitorGridSub.editable=!0,this.forecastMonitorGrid.enableRowSelection=!1,this.forecastMonitorGridSub.lockedColumnCount=0,this.forecastMonitorGridSub.forceSameColumnSize=!0,this.forecastMonitorGrid.forceSameColumnSize=!0,this.forecastMonitorGrid.forceSameColumnException=["expand"],this.entityLabel.text=r.Wb.getPredictMessage("label.forecastMonitor.entity",null),this.cbEntity.toolTip=r.Wb.getPredictMessage('tooltip.forecastMonitor.selectEntity"',null),this.currencyLabel.text=r.Wb.getPredictMessage("label.forecastMonitor.currency",null),this.cbCurrency.toolTip=r.Wb.getPredictMessage("tooltip.forecastMonitor.selectCurrency",null),this.refreshButton.label=r.Wb.getPredictMessage("button.forecastMonitor.refresh",null),this.refreshButton.toolTip=r.Wb.getPredictMessage("tooltip.forecastMonitor.refreshWindow",null),this.rateButton.label=r.Wb.getPredictMessage("button.forecastMonitor.rate",null),this.rateButton.toolTip=r.Wb.getPredictMessage("tooltip.forecastMonitor.rateButton",null),this.optionsButton.label=r.Wb.getPredictMessage("button.forecastMonitor.option",null),this.optionsButton.toolTip=r.Wb.getPredictMessage("tooltip.forecastMonitor.option",null),this.closeButton.label=r.Wb.getPredictMessage("button.forecastMonitor.close",null),this.closeButton.toolTip=r.Wb.getPredictMessage("tooltip.forecastMonitor.close",null),this.templateIdLabel.text=r.Wb.getPredictMessage("label.forecastMonitor.templateId",null),this.movementRadio.label=r.Wb.getPredictMessage("label.entityMonitor.movementId",null),this.movementRadio.toolTip=r.Wb.getPredictMessage("tooltip.entityMonitor.mvmntBrkdown",null),this.bookRadio.label=r.Wb.getPredictMessage("label.entityMonitor.bookCode",null),this.bookRadio.toolTip=r.Wb.getPredictMessage("tooltip.entityMonitor.bookBrkdown",null),this.lastRef.text=r.Wb.getPredictMessage("screen.lastRefresh",null),this.refreshButton.setFocus(),this.forecastMonitorGrid.ITEM_CLICK.subscribe(function(e){t.changeTreeData(e)}),this.imgShowHideControlBar.toolTip="Hide Control Bar",this.imgShowHideButtonBar.toolTip="Hide Button Bar",this.initializeMenus(),r.v.subscribe(function(e){t.export(e)}),this.forecastMonitorGrid.listenVerticalScrollEvent=!0,this.forecastMonitorGridSub.fireVerticalScrollEvent=!0,this.forecastMonitorGridSub.listenVerticalScrollEvent=!0,this.forecastMonitorGrid.fireVerticalScrollEvent=!0,this.forecastMonitorGrid.hideVerticalScrollBar=!0},e.prototype.onLoad=function(){var t=this;try{this.initializeMenus(),this.menuAccessIdParent=r.x.call("eval","menuAccessIdParent"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.sendData.cbStart=this.startOfComms.bind(this),this.sendData.cbStop=this.endOfComms.bind(this),this.sendData.cbResult=function(e){t.sendDataResult(e)},this.sendData.cbFault=this.sendDataFault.bind(this),this.sendData.encodeURL=!1,this.actionPath="forecastMonitor.do?",this.actionMethod="menuAccess="+(this.menuAccessIdParent?this.menuAccessIdParent:0),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.sendData.url=this.baseURL+this.actionPath+"method=saveForecastScenario",this.inputData.send(this.requestParams),r.E.subscribe(function(e){t.currDividerPosition=Number(t.cvGridContainer.widthLeftPixel),t.updateWidths(e)}),this.forecastMonitorGridSub.columnWidthChanged.subscribe(function(e){t.columnWidthChange(e,"sub")}),this.forecastMonitorGrid.columnWidthChanged.subscribe(function(e){t.columnWidthChange(e,"main")})}catch(e){console.log(e,this.moduleId,"Forecast Monitor","onLoad")}},e.prototype.changeTreeData=function(t){var e=t.columnIndex,i=null!=t.target.data&&"Y"==t.target.data.expand;if(0==e&&i){if(this.forecastMonitorGrid.selectedIndex=-1,this.forecastMonitorGridSub.selectedIndex=-1,"Grand total"==t.target.data.date)for(var o=t.target.data.slickgrid_rowcontent.expand.opened,n=0;n<this.mainXml.forecastmonitor.grid.rows.row.length;n++){var l=this.mainXml.forecastmonitor.grid.rows.row[n];"Grand total"!=l.date.content?(this.mainXml.forecastmonitor.grid.rows.row[n].expand.opened=!o,this.mainXml.forecastmonitor.grid.rows.row[n].visible=!o,this.mainXml.forecastmonitor.gridSub.rows.row[n].visible=!o):"Grand total"==l.date.content&&(this.mainXml.forecastmonitor.grid.rows.row[n].expand.opened=!l.expand.opened)}else{for(var s=0;s<this.mainXml.forecastmonitor.grid.rows.row.length;s++){var a=this.mainXml.forecastmonitor.grid.rows.row[s];a.bucket.content==t.target.data.bucket&&String(a.date.content).search("otal")<0?this.mainXml.forecastmonitor.grid.rows.row[s].visible=!a.visible:a.bucket.content==t.target.data.bucket&&String(a.date.content).search("otal")>0&&(this.mainXml.forecastmonitor.grid.rows.row[s].expand.opened=!a.expand.opened)}for(var d=0;d<this.mainXml.forecastmonitor.gridSub.rows.row.length;d++){var h=this.mainXml.forecastmonitor.gridSub.rows.row[d];h.bucket==t.target.data.bucket&&String(h.date.content).search("otal")<0&&(this.mainXml.forecastmonitor.gridSub.rows.row[d].visible=!h.visible)}}var c=[];r.L.jsonpath(this.mainXml,"$.forecastmonitor.grid.rows.row.*").forEach(function(t){1!=t.visible&&"true"!=t.visible||t===Object(t)&&c.push(t)}),this.gridDataExpand=c;var u=[];r.L.jsonpath(this.mainXml,"$.forecastmonitor.gridSub.rows.row.*").forEach(function(t){1!=t.visible&&"true"!=t.visible||t===Object(t)&&u.push(t)}),this.subGridDataExpand=u,this.forecastMonitorGrid.gridData={row:this.gridDataExpand,size:this.gridDataExpand.length},this.forecastMonitorGridSub.gridData={row:this.subGridDataExpand,size:this.subGridDataExpand.length}}else this.forecastMonitorGrid.selectedIndices.length>0?(this.forecastMonitorGridSub.selectedIndex=this.forecastMonitorGrid.selectedIndex,this.selectIndex=this.forecastMonitorGrid.selectedIndex):(this.forecastMonitorGridSub.selectedIndex=-1,this.forecastMonitorGrid.selectedIndex=-1,this.selectIndex=-1)},e.prototype.changeTreeDataOnLoad=function(){this.forecastMonitorGrid.selectedIndex=-1,this.forecastMonitorGridSub.selectedIndex=-1;var t=[];r.L.jsonpath(this.mainXml,"$.forecastmonitor.grid.rows.row.*").forEach(function(e){1!=e.visible&&"true"!=e.visible||e===Object(e)&&t.push(e)}),this.gridDataExpand=t;var e=[];r.L.jsonpath(this.mainXml,"$.forecastmonitor.gridSub.rows.row.*").forEach(function(t){1!=t.visible&&"true"!=t.visible||t===Object(t)&&e.push(t)}),this.subGridDataExpand=e,this.forecastMonitorGrid.gridData={row:this.gridDataExpand,size:this.gridDataExpand.length},this.forecastMonitorGridSub.gridData={row:this.subGridDataExpand,size:this.subGridDataExpand.length}},e.prototype.deepCopy=function(t){var e,i=[];for(e in t)i[e]=t[e];return i},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.releaseDate);var t=new r.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=r.Eb.createPopUp(this,r.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.isModal=!0,this.showJSONPopup.display()},e.prototype.startOfComms=function(){this.disableFields(),this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableFields()},e.prototype.disableFields=function(){this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1},e.prototype.enableFields=function(){this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0},e.prototype.sendDataResult=function(t){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.updateData("yes"))},e.prototype.inputDataResult=function(t){var e=this;try{if(this.inputData.isBusy())this.inputData.cbStop;else{if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){this.deepCopy(this.lastRecievedJSON);this.preColumnXml=this.deepCopy(this.lastRecievedJSON),this.exportContainer.enabled=!0,this.dataBuildingText.visible=!1;var i=this.jsonReader.getScreenAttributes().lastRefTime;if(this.lastRefTime.text=i.replace(/\\u0028/g,"(").replace(/\\u0029/g,")"),this.refreshRate=Number(this.jsonReader.getScreenAttributes().refresh),this.lblTemplateId.text=this.jsonReader.getScreenAttributes().templateId,this.lblTemplateId.toolTip=this.jsonReader.getScreenAttributes().templateName,this.preColumnXml!=this.prevRecievedJSON||!this.refreshFlag){this.cbEntity.setComboData(this.jsonReader.getSelects(),!1),this.selectedEntity.text=this.cbEntity.selectedItem.value,this.cbCurrency.setComboData(this.jsonReader.getSelects(),!1),this.selectedCurrency.text=this.cbCurrency.selectedItem.value;var o=[],n=!1;if(this.jsonReader.isDataBuilding())this.dataBuildingText.visible=!0;else{for(var l=0;l<this.forecastMonitorGrid.dataProvider.length;l++)0==this.forecastMonitorGrid.dataProvider[l].slickgrid_rowcontent.expand.opened&&(o.push(this.forecastMonitorGrid.dataProvider[l].slickgrid_rowcontent.bucket.content),"Grand total"==this.forecastMonitorGrid.dataProvider[l].slickgrid_rowcontent.date.content&&(n=!0));this.forecastMonitorGrid.GroupId="date",this.forecastMonitorGrid.CustomGrid(this.lastRecievedJSON.forecastmonitor.grid.metadata),this.forecastMonitorGrid.rowHeight=22,this.forecastMonitorGrid.styleName="dataGridNormal";var s=this.lastRecievedJSON.forecastmonitor.grid.rows,a=this.lastRecievedJSON.forecastmonitor.gridSub.rows;for(l=0;l<this.forecastMonitorGrid.columnDefinitions.length;l++){if("date"==this.forecastMonitorGrid.columnDefinitions[l].field)for(var d=0;d<s.row.length;d++)s.row[d].date.bold=Boolean(s.row[d].date.haschildren)}for(l=0;l<this.lastRecievedJSON.forecastmonitor.grid.rows.row.length;l++){var h=this.lastRecievedJSON.forecastmonitor.grid.rows.row[l];"Grand total"!=h.date.content?(this.lastRecievedJSON.forecastmonitor.grid.rows.row[l].expand.opened=!n,this.lastRecievedJSON.forecastmonitor.grid.rows.row[l].visible=!n,this.lastRecievedJSON.forecastmonitor.gridSub.rows.row[l].visible=!n):h.date.content}for(d=0;d<s.row.length;d++)o.includes(s.row[d].bucket.content)&&(-1!==s.row[d].date.content.indexOf("otal")?s.row[d].expand.opened=!1:(s.row[d].visible=!1,a.row[d].visible=!1));this.forecastMonitorGrid.gridData=this.jsonReader.getGridData(),this.forecastMonitorGrid.setRowSize=this.lastRecievedJSON.forecastmonitor.grid.rows.size,this.columnChangeFlag=!0,this.forecastMonitorGridSub.CustomGrid(this.lastRecievedJSON.forecastmonitor.gridSub.metadata),this.forecastMonitorGridSub.rowHeight=22,this.forecastMonitorGridSub.styleName="dataGridNormal";for(l=0;l<this.forecastMonitorGridSub.columnDefinitions.length;l++){if("assumption"==this.forecastMonitorGridSub.columnDefinitions[l].field){for(d=0;d<a.row.length;d++)a.row[d].assumption.clickable=Boolean(!a.row[d].date.bold);break}}setTimeout(function(){e.changeTreeDataOnLoad()},0),this.forecastMonitorGridSub.gridData=this.lastRecievedJSON.forecastmonitor.gridSub.rows,this.forecastMonitorGridSub.setRowSize=this.lastRecievedJSON.forecastmonitor.gridSub.rows.size;var c=this.jsonReader.getScreenAttributes().dividerWidth;this.isUpdateData||(this.cvGridContainer.widthLeft=(100*c/window.screen.width).toFixed(2)+"%"),this.forecastMonitorGridSub.ITEM_CLICK.subscribe(function(t){e.onSubGridCellClick(t)}),this.forecastMonitorGridSub.onRowClick=function(t){e.subGridRowChange(t)},this.forecastMonitorGridSub.validate=function(t,i){t.scenario!=i.scenario&&e.validate(t,i)},this.preColumnXml==this.prevRecievedJSON&&this.refreshFlag||(this.mainXml=this.deepCopy(this.preColumnXml),this.selectIndex=-1),this.refreshFlag=!0,"Y"==this.refreshStatus&&(this.forecastMonitorGrid.selectedIndex=this.selectIndex,this.forecastMonitorGridSub.selectedIndex=this.selectIndex),this.preColumnXml.forecastmonitor.grid.rows.row.length<1?this.exportContainer.enabled=!1:this.exportContainer.enabled=!0,"N"==this.refreshStatus||"Y"==this.refreshStatus&&(this.refreshStatus="N"),"N"==this.refreshStatus||"Y"==this.refreshStatus&&(this.refreshStatus="N"),this.prevRecievedJSON=this.lastRecievedJSON}}""==r.Z.trim(this.cbCurrency.selectedLabel)?(this.swtAlert.error(r.Wb.getPredictMessage("alert.currencyAccess",null),"Error"),null!=this.autoRefresh&&(this.autoRefresh.running?(this.autoRefresh.stop(),this.autoRefresh=null):this.autoRefresh=null),this.exportContainer.enabled=!1):null==this.autoRefresh&&(this.refreshRate=parseInt(this.jsonReader.getRefreshRate(),10),this.autoRefresh=new r.cc(1e3*this.refreshRate,0),this.autoRefresh.addEventListener("timer",this.dataRefresh.bind(this)))}else null!=this.lastRecievedJSON&&this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error");null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())}}catch(h){console.log(h,this.moduleId,"Forecast Monitor","inputDataResult")}},e.prototype.validate=function(t,e){var i,o=this,n=t,l=e;if(this.index++,this.index%2==0)if(this.alertPreventFlag=!0,n.date.search("otal")>0)this.swtAlert.warning(r.Wb.getPredictMessage("alert.forecastMonitor.totalValues",null),"Warning",null,null,function(){n.scenario=l.scenario,o.forecastMonitorGridSub.dataProvider[o.forecastMonitorGridSub.selectedIndex].slickgrid_rowcontent.scenario.content=l.scenario,o.forecastMonitorGridSub.refresh()}),this.autoRefresh.start(),this.refreshStatus="N";else if(0==n.scenario.length)this.swtAlert.warning(r.Wb.getPredictMessage("alert.forecastMonitor.enterNumber",null),"Warning",null,null,function(){n.scenario=l.scenario,o.forecastMonitorGridSub.dataProvider[o.forecastMonitorGridSub.selectedIndex].slickgrid_rowcontent.scenario.content=l.scenario,o.forecastMonitorGridSub.refresh()});else{var s=n.scenario.charAt(0);i="-"==s?n.scenario.substring(1,n.scenario.length):n.scenario;var a=r.x.call("formatCurrency",i);"-"==s&&(a="-"+a),"invalid"==a||null==a||a.length<=0?this.swtAlert.warning(r.Wb.getPredictMessage("alert.forecastMonitor.validNumber",null),"Warning"):this.saveHandle()}},e.prototype.mainGridRowChange=function(t){this.forecastMonitorGrid.selectedIndices.length>0&&(this.forecastMonitorGridSub.selectedIndex=this.forecastMonitorGrid.selectedIndex)},e.prototype.subGridRowChange=function(t){this.forecastMonitorGridSub.selectedIndices.length>0&&(this.forecastMonitorGrid.selectedIndex=this.forecastMonitorGridSub.selectedIndex)},e.prototype.onMainGridCellClick=function(t){this.forecastMonitorGrid.selectedIndices.length>0?(this.forecastMonitorGridSub.selectedIndex=this.forecastMonitorGrid.selectedIndex,this.selectIndex=this.forecastMonitorGrid.selectedIndex):(this.forecastMonitorGridSub.selectedIndex=-1,this.selectIndex=-1)},e.prototype.onSubGridCellClick=function(t){this.forecastMonitorGridSub.selectedIndices.length>0?(this.forecastMonitorGrid.selectedIndex=this.forecastMonitorGridSub.selectedIndex,this.selectIndex=this.forecastMonitorGridSub.selectedIndex):(this.forecastMonitorGrid.selectedIndex=-1,this.selectIndex=-1);var e=t.target.field,i=t.target.data.slickgrid_rowcontent[e].content?t.target.data.slickgrid_rowcontent[e].content:"",o=new Object;if(i&&("assumption"==e&&(o.entity=this.cbEntity.selectedLabel,o.currency=this.cbCurrency.selectedLabel,o.date=String(t.target.data.slickgrid_rowcontent.date.content),o.templateId=this.lblTemplateId.text,!(t.target.data.slickgrid_rowcontent.maingroup&&String(t.target.data.slickgrid_rowcontent.maingroup.content).toLowerCase().search("total")>0)))){r.x.call("openAssumptionWindow",o),null!=this.autoRefresh&&(this.autoRefresh.stop(),this.refreshStatus="N")}},e.prototype.export=function(t){var e=[];e.push("Entity="+this.cbEntity.selectedLabel),this.selectedCurrency.text.search("(Millions)")>-1?e.push("Ccy="+this.cbCurrency.selectedLabel+" (Millions)"):this.selectedCurrency.text.search("(Billions)")>-1?e.push("Ccy="+this.cbCurrency.selectedLabel+" (Billions)"):this.selectedCurrency.text.search("(Thousands)")>-1?e.push("Ccy="+this.cbCurrency.selectedLabel+" (Thousands)"):e.push("Ccy="+this.cbCurrency.selectedLabel),e.push("Template ID="+this.lblTemplateId.text);for(var i=a.extend(!0,{},this.lastRecievedJSON.forecastmonitor.grid.metadata.columns),o=0;o<i.column.length;o++)if("expand"==i.column[o].dataelement){i.column.splice(o,1);break}this.exportContainer.convertData(i,this.forecastMonitorGrid,null,e,t,!0)},e.prototype.sendDataFault=function(t){this.lostConnectionText.visible=!0,this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail},e.prototype.connError=function(){this.swtAlert.error(""+this.invalidComms,"Error")},e.prototype.rateHandler=function(){try{this.autoRefresh.stop(),this.win=r.Eb.createPopUp(this,l.a,{title:"Refresh Rate",refreshText:this.refreshRate}),this.win.width="340",this.win.height="150",this.win.id="rateWindow",this.win.enableResize=!1,this.win.isModal=!0,this.win.showControls=!0,this.win.display()}catch(t){r.Wb.logError(t,this.moduleId,"Forecast Monitor","rateHandler",this.errorLocation)}},e.prototype.optionClick=function(){try{r.x.call("openForecastOptionsWindow"),null!=this.autoRefresh&&(this.autoRefresh.stop(),this.refreshStatus="N")}catch(t){r.Wb.logError(t,this.moduleId,"Forecast Monitor","optionsHandler",this.errorLocation)}},e.prototype.saveRefreshRate=function(t){try{if(isNaN(parseInt(t,10)))this.swtAlert.error(r.Wb.getPredictMessage("alert.forecastMonitor.notNumber",null),"Error");else{var e=!1;this.refreshRate=Number(t),this.refreshRate<5&&(this.refreshRate=5,e=!0),e&&this.swtAlert.warning(r.Wb.getPredictMessage("alert.forecastMonitor.refreshRate",null),"Warning"),this.itemId=r.x.call("eval","itemId"),this.rateRequest=r.x.call("getUpdateRefreshRequest",this.refreshRate,this.itemId),null!=this.rateRequest&&""!=this.rateRequest&&(this.updateRefreshRate.encodeURL=!1,this.updateRefreshRate.url=this.baseURL+this.rateRequest,this.updateRefreshRate.send()),this.autoRefreshAfterStop()}}catch(i){console.log(i,this.moduleId,"Forecast Monitor","saveRefreshRate")}},e.prototype.optionDateResult=function(){this.updateData("no")},e.prototype.autoRefreshAfterStop=function(){var t=this,e=this.refreshRate;clearInterval(this.interval),this.interval=setInterval(function(){t.dataRefresh()},1e3*e)},e.prototype.keyDownEventHandler=function(t){try{var e=Object(r.ic.getFocus()).name;t.keyCode==r.N.ENTER&&("closeButton"==e?this.closeHandler():"csv"==e?this.report("csv"):"excel"==e?this.report("xls"):"pdf"==e?this.report("pdf"):"helpIcon"===e&&this.doHelp())}catch(i){r.Wb.logError(i,this.moduleId,"Forecast Monitor","keyDownEventHandler",this.errorLocation)}},e.prototype.report=function(t){},e.prototype.printPage=function(t){try{r.x.call("printPage")}catch(e){r.Wb.logError(e,this.moduleId,"Forecast Monitor","printPage",this.errorLocation)}},e.prototype.dataRefresh=function(){try{this.updateData("no")}catch(t){r.Wb.logError(t,this.moduleId,"Forecast Monitor","dataRefresh",this.errorLocation)}},e.prototype.entityChangeCombo=function(t){try{this.comboChange=!0,this.selectedEntity.text=this.cbEntity.selectedItem.value,this.comboUpdateData("no","entity")}catch(e){console.log(e,this.moduleId,"Forecast Monitor","changeCombo")}},e.prototype.currencyChangeCombo=function(t){this.comboChange=!0,this.comboUpdateData("no","currency"),this.selectedCurrency.text=this.cbCurrency.selectedItem.value},e.prototype.updateData=function(t){try{this.isUpdateData=!0,this.requestParams=[],this.actionPath="forecastMonitor.do?",this.actionMethod="menuAccess="+(this.menuAccessIdParent?this.menuAccessIdParent:0),this.requestParams.selectedEntityId=this.cbEntity.selectedItem.content,this.requestParams.selectedCurrency=this.cbCurrency.selectedItem.content,this.requestParams.autoRefresh=t,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){console.log(e,this.moduleId,"Forecast Monitor","updateData"),r.Wb.logError(e,this.moduleId,"Forecast Monitor","updateData",this.errorLocation)}},e.prototype.comboUpdateData=function(t,e){this.requestParams=[],this.isUpdateData=!0,"entity"==e?(this.requestParams.selectedCurrency="",this.requestParams.retainCurrency=this.cbCurrency.selectedItem.content):this.requestParams.selectedCurrency=this.cbCurrency.selectedItem.content,this.requestParams.selectedEntityId=this.cbEntity.selectedItem.content,this.requestParams.autoRefresh=t,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.send(this.requestParams)},e.prototype.showHideControlBar=function(){this.controlBarHideFlag?(this.swtControlBar.visible=!0,this.imgShowHideControlBar.styleName="minusIcon",this.imgShowHideControlBar.toolTip="Hide Button Bar"):(this.swtControlBar.visible=!1,this.swtControlBar.includeInLayout=!1,this.imgShowHideControlBar.styleName="plusIcon",this.imgShowHideControlBar.toolTip="Show Button Bar",this.vbGridContainer.height="100%",this.forecastMonitorGrid.resizeGrid(),this.forecastMonitorGridSub.resizeGrid()),this.controlBarHideFlag=!this.controlBarHideFlag},e.prototype.showHideButtonBar=function(){this.buttonBarHideFlag?(this.swtButtonBar.visible=!0,this.imgShowHideButtonBar.styleName="minusIcon",this.imgShowHideButtonBar.toolTip="Hide Button Bar"):(this.swtButtonBar.visible=!1,this.swtButtonBar.includeInLayout=!1,this.imgShowHideButtonBar.styleName="plusIcon",this.imgShowHideButtonBar.toolTip="Show Button Bar",this.vbGridContainer.height="100%",this.forecastMonitorGrid.resizeGrid(),this.forecastMonitorGridSub.resizeGrid()),this.buttonBarHideFlag=!this.buttonBarHideFlag},e.prototype.closeHandler=function(){try{r.x.call("close")}catch(t){r.Wb.logError(t,r.Wb.SYSTEM_MODULE_ID,"Forecast Monitor"," closeHandler",this.errorLocation)}},e.prototype.inputDataFault=function(){this.lostConnectionText.visible=!0,null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())},e.prototype.doHelp=function(){try{r.x.call("help")}catch(t){r.Wb.logError(t,this.moduleId,"ForecastMonitor","doHelp",this.errorLocation)}},e.prototype.saveHandle=function(){var t=[];this.forecastMonitorGridSub.refresh();var e=this.forecastMonitorGridSub.changes.getValues();if(e.length>0){for(var i=0;i<e.length;i++)this.forecastMonitorGridSub.selectedItem.date.content==e[i].crud_data.date&&(t[e[i].crud_data.date]=e[i].crud_data.scenario,t.editupdate=e[i].crud_data.date,t.entity=this.cbEntity.selectedLabel,t.currency=this.cbCurrency.selectedLabel);t.editupdate.length>0&&(this.refreshFlag=!1,this.sendData.send(t),this.forecastMonitorGridSub.changes.clear())}},e.prototype.updateWidths=function(t){var e=this,i=[];i.push("divider="+this.currDividerPosition),this.requestParams=[],this.resizeData.encodeURL=!1,this.requestParams.width=i.toString(),this.actionPath="forecastMonitor.do?",this.actionMethod="method=saveColumnWidth&",this.resizeData.cbStart=this.startOfComms.bind(this),this.resizeData.cbStop=this.endOfComms.bind(this),this.resizeData.cbResult=function(t){e.inputDataResultColumnsChange(t)},this.resizeData.url=this.baseURL+this.actionPath+this.actionMethod,this.resizeData.send(this.requestParams)},e.prototype.columnWidthChange=function(t,e){var i;this.requestParams=[],this.resizeData.encodeURL=!1,this.actionMethod="method=saveColumnWidth",this.actionPath="forecastMonitor.do?",this.requestParams.method="saveColumnWidth",i="main"==e?this.forecastMonitorGrid.gridObj.getColumns():this.forecastMonitorGridSub.gridObj.getColumns();for(var o=0;o<i.length;o++)if("dummy"!=i[o].id&&i[o].width!=i[o].previousWidth){var n=i[o].width;n<20&&(n=20),n>200&&(n=200),this.requestParams.width="main"==e?"main="+n:"sub="+n,this.resizeData.url=this.baseURL+this.actionPath+this.actionMethod,this.resizeData.send(this.requestParams)}},e.prototype.inputDataResultColumnsChange=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else{var e=new r.L;e.setInputJSON(t),"Column width saved ok"!==e.getRequestReplyMessage()&&this.swtAlert.error(r.Wb.getPredictMessage("error.contactAdmin",null)+"\n"+e.getRequestReplyMessage())}},e.prototype.startAutoRefresh=function(t){""!=this.cbCurrency.selectedLabel&&"Y"==t&&(this.updateData("no"),this.autoRefresh&&this.autoRefresh.start())},e}(r.yb),h=[{path:"",component:d}],c=(s.l.forChild(h),function(){return function(){}}()),u=i("pMnS"),b=i("RChO"),g=i("t6HQ"),p=i("WFGK"),f=i("5FqG"),m=i("Ip0R"),w=i("gIcY"),R=i("t/Na"),S=i("sE5F"),C=i("OzfB"),v=i("T7CS"),I=i("S7LP"),y=i("6aHO"),M=i("WzUx"),G=i("A7o+"),x=i("zCE2"),B=i("Jg5P"),D=i("3R0m"),k=i("hhbb"),J=i("5rxC"),L=i("Fzqc"),T=i("21Lb"),N=i("hUWP"),W=i("3pJQ"),E=i("V9q+"),P=i("VDKW"),O=i("kXfT"),H=i("BGbe");i.d(e,"ForecastMonitorModuleNgFactory",function(){return A}),i.d(e,"RenderType_ForecastMonitor",function(){return F}),i.d(e,"View_ForecastMonitor_0",function(){return z}),i.d(e,"View_ForecastMonitor_Host_0",function(){return q}),i.d(e,"ForecastMonitorNgFactory",function(){return j});var A=o.Gb(c,[],function(t){return o.Qb([o.Rb(512,o.n,o.vb,[[8,[u.a,b.a,g.a,p.a,f.Cb,f.Pb,f.r,f.rc,f.s,f.Ab,f.Bb,f.Db,f.qd,f.Hb,f.k,f.Ib,f.Nb,f.Ub,f.yb,f.Jb,f.v,f.A,f.e,f.c,f.g,f.d,f.Kb,f.f,f.ec,f.Wb,f.bc,f.ac,f.sc,f.fc,f.lc,f.jc,f.Eb,f.Fb,f.mc,f.Lb,f.nc,f.Mb,f.dc,f.Rb,f.b,f.ic,f.Yb,f.Sb,f.kc,f.y,f.Qb,f.cc,f.hc,f.pc,f.oc,f.xb,f.p,f.q,f.o,f.h,f.j,f.w,f.Zb,f.i,f.m,f.Vb,f.Ob,f.Gb,f.Xb,f.t,f.tc,f.zb,f.n,f.qc,f.a,f.z,f.rd,f.sd,f.x,f.td,f.gc,f.l,f.u,f.ud,f.Tb,j]],[3,o.n],o.J]),o.Rb(4608,m.m,m.l,[o.F,[2,m.u]]),o.Rb(4608,w.c,w.c,[]),o.Rb(4608,w.p,w.p,[]),o.Rb(4608,R.j,R.p,[m.c,o.O,R.n]),o.Rb(4608,R.q,R.q,[R.j,R.o]),o.Rb(5120,R.a,function(t){return[t,new r.tb]},[R.q]),o.Rb(4608,R.m,R.m,[]),o.Rb(6144,R.k,null,[R.m]),o.Rb(4608,R.i,R.i,[R.k]),o.Rb(6144,R.b,null,[R.i]),o.Rb(4608,R.f,R.l,[R.b,o.B]),o.Rb(4608,R.c,R.c,[R.f]),o.Rb(4608,S.c,S.c,[]),o.Rb(4608,S.g,S.b,[]),o.Rb(5120,S.i,S.j,[]),o.Rb(4608,S.h,S.h,[S.c,S.g,S.i]),o.Rb(4608,S.f,S.a,[]),o.Rb(5120,S.d,S.k,[S.h,S.f]),o.Rb(5120,o.b,function(t,e){return[C.j(t,e)]},[m.c,o.O]),o.Rb(4608,v.a,v.a,[]),o.Rb(4608,I.a,I.a,[]),o.Rb(4608,y.a,y.a,[o.n,o.L,o.B,I.a,o.g]),o.Rb(4608,M.c,M.c,[o.n,o.g,o.B]),o.Rb(4608,M.e,M.e,[M.c]),o.Rb(4608,G.l,G.l,[]),o.Rb(4608,G.h,G.g,[]),o.Rb(4608,G.c,G.f,[]),o.Rb(4608,G.j,G.d,[]),o.Rb(4608,G.b,G.a,[]),o.Rb(4608,G.k,G.k,[G.l,G.h,G.c,G.j,G.b,G.m,G.n]),o.Rb(4608,M.i,M.i,[[2,G.k]]),o.Rb(4608,M.r,M.r,[M.L,[2,G.k],M.i]),o.Rb(4608,M.t,M.t,[]),o.Rb(4608,M.w,M.w,[]),o.Rb(1073742336,s.l,s.l,[[2,s.r],[2,s.k]]),o.Rb(1073742336,m.b,m.b,[]),o.Rb(1073742336,w.n,w.n,[]),o.Rb(1073742336,w.l,w.l,[]),o.Rb(1073742336,x.a,x.a,[]),o.Rb(1073742336,B.a,B.a,[]),o.Rb(1073742336,w.e,w.e,[]),o.Rb(1073742336,D.a,D.a,[]),o.Rb(1073742336,G.i,G.i,[]),o.Rb(1073742336,M.b,M.b,[]),o.Rb(1073742336,R.e,R.e,[]),o.Rb(1073742336,R.d,R.d,[]),o.Rb(1073742336,S.e,S.e,[]),o.Rb(1073742336,k.b,k.b,[]),o.Rb(1073742336,J.b,J.b,[]),o.Rb(1073742336,C.c,C.c,[]),o.Rb(1073742336,L.a,L.a,[]),o.Rb(1073742336,T.d,T.d,[]),o.Rb(1073742336,N.c,N.c,[]),o.Rb(1073742336,W.a,W.a,[]),o.Rb(1073742336,E.a,E.a,[[2,C.g],o.O]),o.Rb(1073742336,P.b,P.b,[]),o.Rb(1073742336,O.a,O.a,[]),o.Rb(1073742336,H.b,H.b,[]),o.Rb(1073742336,r.Tb,r.Tb,[]),o.Rb(1073742336,c,c,[]),o.Rb(256,R.n,"XSRF-TOKEN",[]),o.Rb(256,R.o,"X-XSRF-TOKEN",[]),o.Rb(256,"config",{},[]),o.Rb(256,G.m,void 0,[]),o.Rb(256,G.n,void 0,[]),o.Rb(256,"popperDefaults",{},[]),o.Rb(1024,s.i,function(){return[[{path:"",component:d}]]},[])])}),_=[[""]],F=o.Hb({encapsulation:2,styles:_,data:{}});function z(t){return o.dc(0,[o.Zb(402653184,1,{_container:0}),o.Zb(402653184,2,{forecastMonitorGrid:0}),o.Zb(402653184,3,{forecastMonitorGridSub:0}),o.Zb(402653184,4,{customGrid:0}),o.Zb(402653184,5,{customGridSub:0}),o.Zb(402653184,6,{appContainer:0}),o.Zb(402653184,7,{vbGridContainer:0}),o.Zb(402653184,8,{swtControlBar:0}),o.Zb(402653184,9,{swtButtonBar:0}),o.Zb(402653184,10,{cvGridContainer:0}),o.Zb(402653184,11,{loadingImage:0}),o.Zb(402653184,12,{imgShowHideControlBar:0}),o.Zb(402653184,13,{imgShowHideButtonBar:0}),o.Zb(402653184,14,{cbEntity:0}),o.Zb(402653184,15,{cbCurrency:0}),o.Zb(402653184,16,{entityLabel:0}),o.Zb(402653184,17,{selectedEntity:0}),o.Zb(402653184,18,{currencyLabel:0}),o.Zb(402653184,19,{selectedCurrency:0}),o.Zb(402653184,20,{templateIdLabel:0}),o.Zb(402653184,21,{lblTemplateId:0}),o.Zb(402653184,22,{lastRef:0}),o.Zb(402653184,23,{lastRefTime:0}),o.Zb(402653184,24,{dataBuildingText:0}),o.Zb(402653184,25,{lostConnectionText:0}),o.Zb(402653184,26,{rateButton:0}),o.Zb(402653184,27,{closeButton:0}),o.Zb(402653184,28,{refreshButton:0}),o.Zb(402653184,29,{optionsButton:0}),o.Zb(402653184,30,{exportContainer:0}),o.Zb(402653184,31,{helpIcon:0}),o.Zb(402653184,32,{breakdown:0}),o.Zb(402653184,33,{movementRadio:0}),o.Zb(402653184,34,{bookRadio:0}),(t()(),o.Jb(34,0,null,null,128,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var o=!0,n=t.component;"creationComplete"===e&&(o=!1!==n.onLoad()&&o);return o},f.ad,f.hb)),o.Ib(35,4440064,null,0,r.yb,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),o.Jb(36,0,null,0,126,"VBox",[["height","100%"],["id","appContainer"],["paddingLeft","5"],["paddingRight","5"],["verticalGap","0"],["width","100%"]],null,null,null,f.od,f.vb)),o.Ib(37,4440064,[[6,4],["appContainer",4]],0,r.ec,[o.r,r.i,o.T],{id:[0,"id"],verticalGap:[1,"verticalGap"],width:[2,"width"],height:[3,"height"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),o.Jb(38,0,null,0,7,"HBox",[["height","2%"],["width","100%"]],null,null,null,f.Dc,f.K)),o.Ib(39,4440064,null,0,r.C,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(40,0,null,0,1,"HBox",[["width","99%"]],null,null,null,f.Dc,f.K)),o.Ib(41,4440064,null,0,r.C,[o.r,r.i],{width:[0,"width"]},null),(t()(),o.Jb(42,0,null,0,3,"HBox",[["horizontalAlign","right"],["width","1%"]],null,null,null,f.Dc,f.K)),o.Ib(43,4440064,null,0,r.C,[o.r,r.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),o.Jb(44,0,null,0,1,"SwtButton",[["id","imgShowHideControlBar"],["styleName","minusIcon"],["toolTip","Hide Control Bar"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.showHideControlBar()&&o);return o},f.Mc,f.T)),o.Ib(45,4440064,[[12,4],["imgShowHideControlBar",4]],0,r.cb,[o.r,r.i],{id:[0,"id"],toolTip:[1,"toolTip"],styleName:[2,"styleName"]},{onClick_:"click"}),(t()(),o.Jb(46,0,null,0,51,"SwtCanvas",[["height","10%"],["marginTop","-5"],["minWidth","900"],["width","99%"]],null,null,null,f.Nc,f.U)),o.Ib(47,4440064,[[8,4],["swtControlBar",4]],0,r.db,[o.r,r.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],marginTop:[3,"marginTop"]},null),(t()(),o.Jb(48,0,null,0,49,"HBox",[["height","100%"],["width","100%"]],null,null,null,f.Dc,f.K)),o.Ib(49,4440064,null,0,r.C,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(50,0,null,0,29,"Grid",[["height","100%"],["verticalGap","2"],["width","80%"]],null,null,null,f.Cc,f.H)),o.Ib(51,4440064,null,0,r.z,[o.r,r.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(52,0,null,0,13,"GridRow",[["height","100%"],["paddingLeft","10"],["width","50%"]],null,null,null,f.Bc,f.J)),o.Ib(53,4440064,null,0,r.B,[o.r,r.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),o.Jb(54,0,null,0,3,"GridItem",[["height","100%"],["width","110"]],null,null,null,f.Ac,f.I)),o.Ib(55,4440064,null,0,r.A,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(56,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["paddingLeft","5"],["paddingTop","0"]],null,null,null,f.Yc,f.fb)),o.Ib(57,4440064,[[16,4],["entityLabel",4]],0,r.vb,[o.r,r.i],{paddingTop:[0,"paddingTop"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),o.Jb(58,0,null,0,3,"GridItem",[["height","100%"],["width","160"]],null,null,null,f.Ac,f.I)),o.Ib(59,4440064,null,0,r.A,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(60,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","cbEntity"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var n=!0,r=t.component;"window:mousewheel"===e&&(n=!1!==o.Tb(t,61).mouseWeelEventHandler(i.target)&&n);"change"===e&&(n=!1!==r.entityChangeCombo(i)&&n);return n},f.Pc,f.W)),o.Ib(61,4440064,[[14,4],["cbEntity",4]],0,r.gb,[o.r,r.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),o.Jb(62,0,null,0,3,"GridItem",[["height","100%"],["width","20%"]],null,null,null,f.Ac,f.I)),o.Ib(63,4440064,null,0,r.A,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(64,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingTop","2"],["textAlign","right"]],null,null,null,f.Yc,f.fb)),o.Ib(65,4440064,[[17,4],["selectedEntity",4]],0,r.vb,[o.r,r.i],{id:[0,"id"],textAlign:[1,"textAlign"],paddingTop:[2,"paddingTop"],fontWeight:[3,"fontWeight"]},null),(t()(),o.Jb(66,0,null,0,13,"GridRow",[["height","100%"],["paddingLeft","10"],["width","50%"]],null,null,null,f.Bc,f.J)),o.Ib(67,4440064,null,0,r.B,[o.r,r.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),o.Jb(68,0,null,0,3,"GridItem",[["height","100%"],["width","110"]],null,null,null,f.Ac,f.I)),o.Ib(69,4440064,null,0,r.A,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(70,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["paddingLeft","5"],["paddingTop","0"]],null,null,null,f.Yc,f.fb)),o.Ib(71,4440064,[[18,4],["currencyLabel",4]],0,r.vb,[o.r,r.i],{paddingTop:[0,"paddingTop"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),o.Jb(72,0,null,0,3,"GridItem",[["height","100%"],["width","160"]],null,null,null,f.Ac,f.I)),o.Ib(73,4440064,null,0,r.A,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(74,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","cbCurrency"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var n=!0,r=t.component;"window:mousewheel"===e&&(n=!1!==o.Tb(t,75).mouseWeelEventHandler(i.target)&&n);"change"===e&&(n=!1!==r.currencyChangeCombo(i)&&n);return n},f.Pc,f.W)),o.Ib(75,4440064,[[15,4],["cbCurrency",4]],0,r.gb,[o.r,r.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),o.Jb(76,0,null,0,3,"GridItem",[["height","100%"],["width","20%"]],null,null,null,f.Ac,f.I)),o.Ib(77,4440064,null,0,r.A,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(78,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCurrency"],["paddingTop","2"],["textAlign","right"]],null,null,null,f.Yc,f.fb)),o.Ib(79,4440064,[[19,4],["selectedCurrency",4]],0,r.vb,[o.r,r.i],{id:[0,"id"],textAlign:[1,"textAlign"],paddingTop:[2,"paddingTop"],fontWeight:[3,"fontWeight"]},null),(t()(),o.Jb(80,0,null,0,5,"HBox",[["height","100%"],["horizontalAlign","right"],["paddingLeft","3"],["width","20%"]],null,null,null,f.Dc,f.K)),o.Ib(81,4440064,null,0,r.C,[o.r,r.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"]},null),(t()(),o.Jb(82,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["paddingTop","-4"]],null,null,null,f.Yc,f.fb)),o.Ib(83,4440064,[[20,4],["templateIdLabel",4]],0,r.vb,[o.r,r.i],{paddingTop:[0,"paddingTop"],fontWeight:[1,"fontWeight"]},null),(t()(),o.Jb(84,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblTemplateId"],["paddingTop","-4"]],null,null,null,f.Yc,f.fb)),o.Ib(85,4440064,[[21,4],["lblTemplateId",4]],0,r.vb,[o.r,r.i],{id:[0,"id"],paddingTop:[1,"paddingTop"],fontWeight:[2,"fontWeight"]},null),(t()(),o.Jb(86,0,null,0,11,"HBox",[["horizontalAlign","right"],["includeInLayout","false"],["visible","false"],["width","50%"]],null,null,null,f.Dc,f.K)),o.Ib(87,4440064,null,0,r.C,[o.r,r.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],includeInLayout:[2,"includeInLayout"],visible:[3,"visible"]},null),(t()(),o.Jb(88,0,null,0,9,"fieldset",[],null,null,null,null,null)),(t()(),o.Jb(89,0,null,null,1,"legend",[],null,null,null,null,null)),(t()(),o.bc(-1,null,["Breakdown"])),(t()(),o.Jb(91,0,null,null,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","breakdown"]],null,null,null,f.ed,f.lb)),o.Ib(92,4440064,[[32,4],["breakdown",4]],1,r.Hb,[R.c,o.r,r.i],{id:[0,"id"],align:[1,"align"]},null),o.Zb(603979776,35,{radioItems:1}),(t()(),o.Jb(94,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","movementRadio"],["value","M"]],null,null,null,f.fd,f.mb)),o.Ib(95,4440064,[[35,4],[33,4],["movementRadio",4]],0,r.Ib,[o.r,r.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),o.Jb(96,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","bookRadio"],["value","B"]],null,null,null,f.fd,f.mb)),o.Ib(97,4440064,[[35,4],[34,4],["bookRadio",4]],0,r.Ib,[o.r,r.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),o.Jb(98,0,null,0,23,"VBox",[["height","80%"],["id","vbGridContainer"],["minWidth","900"],["width","100%"]],null,null,null,f.od,f.vb)),o.Ib(99,4440064,[[7,4],["vbGridContainer",4]],0,r.ec,[o.r,r.i,o.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"]},null),(t()(),o.Jb(100,0,null,0,21,"HDividedBox",[["height","100%"],["id","cvGridContainer"],["width","100%"]],null,null,null,f.Ec,f.L)),o.Ib(101,4440064,[[10,4],["cvGridContainer",4]],0,r.D,[o.r,r.i,o.B,o.n],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(102,0,null,0,9,"SwtCanvas",[["borderColor","#f9f9f9"],["borderStyle","solid"],["class","left"],["cornerRadius","4"],["dropShadowEnabled","true"],["height","100%"],["id","customGrid"],["width","50%"]],null,null,null,f.Nc,f.U)),o.Ib(103,4440064,[[4,4],["customGrid",4]],0,r.db,[o.r,r.i],{id:[0,"id"],dropShadowEnabled:[1,"dropShadowEnabled"],cornerRadius:[2,"cornerRadius"],borderStyle:[3,"borderStyle"],borderColor:[4,"borderColor"],width:[5,"width"],height:[6,"height"]},null),(t()(),o.Jb(104,0,null,0,7,"SwtCommonGrid",[["height","100%"],["id","forecastMonitorGrid"],["width","100%"]],null,null,null,f.Rc,f.X)),o.Yb(4608,null,M.o,M.o,[M.d,M.f,M.g,M.h,M.j,M.k,M.l,M.v,M.z,M.B,M.C,M.G,M.H,M.I,M.J,[2,G.k]]),o.Yb(512,null,M.J,M.J,[]),o.Yb(512,null,G.k,G.k,[G.l,G.h,G.c,G.j,G.b,G.m,G.n]),o.Yb(512,null,M.p,M.p,[M.J,[2,G.k]]),o.Yb(512,null,M.d,M.d,[M.p,M.J]),o.Yb(512,null,M.i,M.i,[[2,G.k]]),o.Ib(111,4440064,[[2,4],["forecastMonitorGrid",4]],0,r.hb,[o.r,r.i,M.d,M.p,M.J,M.i,G.k],{id:[0,"id"],_width:[1,"_width"],_height:[2,"_height"]},null),(t()(),o.Jb(112,0,null,1,9,"SwtCanvas",[["borderColor","#f9f9f9"],["borderStyle","solid"],["class","right"],["cornerRadius","4"],["dropShadowEnabled","true"],["height","100%"],["id","customGridSub"],["width","50%"]],null,null,null,f.Nc,f.U)),o.Ib(113,4440064,[[5,4],["customGridSub",4]],0,r.db,[o.r,r.i],{id:[0,"id"],dropShadowEnabled:[1,"dropShadowEnabled"],cornerRadius:[2,"cornerRadius"],borderStyle:[3,"borderStyle"],borderColor:[4,"borderColor"],width:[5,"width"],height:[6,"height"]},null),(t()(),o.Jb(114,0,null,0,7,"SwtCommonGrid",[["height","100%"],["id","forecastMonitorGridSub"],["width","100%"]],null,null,null,f.Rc,f.X)),o.Yb(4608,null,M.o,M.o,[M.d,M.f,M.g,M.h,M.j,M.k,M.l,M.v,M.z,M.B,M.C,M.G,M.H,M.I,M.J,[2,G.k]]),o.Yb(512,null,M.J,M.J,[]),o.Yb(512,null,G.k,G.k,[G.l,G.h,G.c,G.j,G.b,G.m,G.n]),o.Yb(512,null,M.p,M.p,[M.J,[2,G.k]]),o.Yb(512,null,M.d,M.d,[M.p,M.J]),o.Yb(512,null,M.i,M.i,[[2,G.k]]),o.Ib(121,4440064,[[3,4],["forecastMonitorGridSub",4]],0,r.hb,[o.r,r.i,M.d,M.p,M.J,M.i,G.k],{id:[0,"id"],_width:[1,"_width"],_height:[2,"_height"]},null),(t()(),o.Jb(122,0,null,0,1,"HBox",[["width","99%"]],null,null,null,f.Dc,f.K)),o.Ib(123,4440064,null,0,r.C,[o.r,r.i],{width:[0,"width"]},null),(t()(),o.Jb(124,0,null,0,7,"HBox",[["height","2%"],["width","100%"]],null,null,null,f.Dc,f.K)),o.Ib(125,4440064,null,0,r.C,[o.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(126,0,null,0,1,"HBox",[["width","99%"]],null,null,null,f.Dc,f.K)),o.Ib(127,4440064,null,0,r.C,[o.r,r.i],{width:[0,"width"]},null),(t()(),o.Jb(128,0,null,0,3,"HBox",[["horizontalAlign","right"],["width","1%"]],null,null,null,f.Dc,f.K)),o.Ib(129,4440064,null,0,r.C,[o.r,r.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),o.Jb(130,0,null,0,1,"SwtButton",[["id","imgShowHideButtonBar"],["styleName","minusIcon"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.showHideButtonBar()&&o);return o},f.Mc,f.T)),o.Ib(131,4440064,[[13,4],["imgShowHideButtonBar",4]],0,r.cb,[o.r,r.i],{id:[0,"id"],styleName:[1,"styleName"]},{onClick_:"click"}),(t()(),o.Jb(132,0,null,0,30,"SwtCanvas",[["height","5%"],["id","swtButtonBar"],["marginTop","-5"],["minWidth","900"],["width","99%"]],null,null,null,f.Nc,f.U)),o.Ib(133,4440064,[[9,4],["swtButtonBar",4]],0,r.db,[o.r,r.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"],marginTop:[4,"marginTop"]},null),(t()(),o.Jb(134,0,null,0,28,"HBox",[["width","100%"]],null,null,null,f.Dc,f.K)),o.Ib(135,4440064,null,0,r.C,[o.r,r.i],{width:[0,"width"]},null),(t()(),o.Jb(136,0,null,0,9,"HBox",[["width","60%"]],null,null,null,f.Dc,f.K)),o.Ib(137,4440064,null,0,r.C,[o.r,r.i],{width:[0,"width"]},null),(t()(),o.Jb(138,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.updateData("yes")&&o);return o},f.Mc,f.T)),o.Ib(139,4440064,[[28,4],["refreshButton",4]],0,r.cb,[o.r,r.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),o.Jb(140,0,null,0,1,"SwtButton",[["id","rateButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.rateHandler()&&o);return o},f.Mc,f.T)),o.Ib(141,4440064,[[26,4],["rateButton",4]],0,r.cb,[o.r,r.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),o.Jb(142,0,null,0,1,"SwtButton",[["id","optionsButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.optionClick()&&o);return o},f.Mc,f.T)),o.Ib(143,4440064,[[29,4],["optionsButton",4]],0,r.cb,[o.r,r.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),o.Jb(144,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.closeHandler()&&o);return o},f.Mc,f.T)),o.Ib(145,4440064,[[27,4],["closeButton",4]],0,r.cb,[o.r,r.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),o.Jb(146,0,null,0,16,"HBox",[["horizontalAlign","right"]],null,null,null,f.Dc,f.K)),o.Ib(147,4440064,null,0,r.C,[o.r,r.i],{horizontalAlign:[0,"horizontalAlign"]},null),(t()(),o.Jb(148,0,null,0,1,"SwtText",[["color","red"],["fontWeight","bold"],["height","16"],["id","dataBuildingText"],["text","DATA BUILD IN PROGRESS"],["visible","false"],["width","183"]],null,null,null,f.ld,f.qb)),o.Ib(149,4440064,[[24,4],["dataBuildingText",4]],0,r.Pb,[o.r,r.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],visible:[3,"visible"],text:[4,"text"],fontWeight:[5,"fontWeight"],color:[6,"color"]},null),(t()(),o.Jb(150,0,null,0,1,"SwtText",[["color","red"],["fontWeight","bold"],["height","16"],["id","lostConnectionText"],["text","CONNECTION ERROR"],["visible","false"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.connError()&&o);return o},f.ld,f.qb)),o.Ib(151,4440064,[[25,4],["lostConnectionText",4]],0,r.Pb,[o.r,r.i],{id:[0,"id"],height:[1,"height"],visible:[2,"visible"],text:[3,"text"],fontWeight:[4,"fontWeight"],color:[5,"color"]},{onClick_:"click"}),(t()(),o.Jb(152,0,null,0,1,"SwtText",[["height","16"],["id","lastRef"]],null,null,null,f.ld,f.qb)),o.Ib(153,4440064,[[22,4],["lastRef",4]],0,r.Pb,[o.r,r.i],{id:[0,"id"],height:[1,"height"]},null),(t()(),o.Jb(154,0,null,0,1,"SwtText",[["height","16"],["id","lastRefTime"]],null,null,null,f.ld,f.qb)),o.Ib(155,4440064,[[23,4],["lastRefTime",4]],0,r.Pb,[o.r,r.i],{id:[0,"id"],height:[1,"height"]},null),(t()(),o.Jb(156,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),o.Jb(157,0,null,null,1,"DataExport",[["id","exportContainer"]],null,null,null,f.Sc,f.Z)),o.Ib(158,4440064,[[30,4],["exportContainer",4]],0,r.kb,[r.i,o.r],{id:[0,"id"]},null),(t()(),o.Jb(159,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doHelp()&&o);return o},f.Wc,f.db)),o.Ib(160,4440064,[[31,4],["helpIcon",4]],0,r.rb,[o.r,r.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),o.Jb(161,0,null,0,1,"SwtLoadingImage",[["id","loadingImage"]],null,null,null,f.Zc,f.gb)),o.Ib(162,114688,[[11,4],["loadingImage",4]],0,r.xb,[o.r],null,null)],function(t,e){t(e,35,0,"100%","100%");t(e,37,0,"appContainer","0","100%","100%","5","5");t(e,39,0,"100%","2%");t(e,41,0,"99%");t(e,43,0,"right","1%");t(e,45,0,"imgShowHideControlBar","Hide Control Bar","minusIcon");t(e,47,0,"99%","10%","900","-5");t(e,49,0,"100%","100%");t(e,51,0,"2","80%","100%");t(e,53,0,"50%","100%","10");t(e,55,0,"110","100%");t(e,57,0,"0","5","bold");t(e,59,0,"160","100%");t(e,61,0,"entity","135","cbEntity");t(e,63,0,"20%","100%");t(e,65,0,"selectedEntity","right","2","normal");t(e,67,0,"50%","100%","10");t(e,69,0,"110","100%");t(e,71,0,"0","5","bold");t(e,73,0,"160","100%");t(e,75,0,"currency","135","cbCurrency");t(e,77,0,"20%","100%");t(e,79,0,"selectedCurrency","right","2","normal");t(e,81,0,"right","20%","100%","3");t(e,83,0,"-4","bold");t(e,85,0,"lblTemplateId","-4","bold");t(e,87,0,"right","50%","false","false");t(e,92,0,"breakdown","horizontal");t(e,95,0,"movementRadio","breakdown","M");t(e,97,0,"bookRadio","breakdown","B");t(e,99,0,"vbGridContainer","100%","80%","900");t(e,101,0,"cvGridContainer","100%","100%");t(e,103,0,"customGrid","true","4","solid","#f9f9f9","50%","100%");t(e,111,0,"forecastMonitorGrid","100%","100%");t(e,113,0,"customGridSub","true","4","solid","#f9f9f9","50%","100%");t(e,121,0,"forecastMonitorGridSub","100%","100%");t(e,123,0,"99%");t(e,125,0,"100%","2%");t(e,127,0,"99%");t(e,129,0,"right","1%");t(e,131,0,"imgShowHideButtonBar","minusIcon");t(e,133,0,"swtButtonBar","99%","5%","900","-5");t(e,135,0,"100%");t(e,137,0,"60%");t(e,139,0,"refreshButton",!0);t(e,141,0,"rateButton");t(e,143,0,"optionsButton");t(e,145,0,"closeButton");t(e,147,0,"right");t(e,149,0,"dataBuildingText","183","16","false","DATA BUILD IN PROGRESS","bold","red");t(e,151,0,"lostConnectionText","16","false","CONNECTION ERROR","bold","red");t(e,153,0,"lastRef","16");t(e,155,0,"lastRefTime","16");t(e,158,0,"exportContainer");t(e,160,0,"helpIcon"),t(e,162,0)},null)}function q(t){return o.dc(0,[(t()(),o.Jb(0,0,null,null,1,"app-forecast-monitor",[],null,null,null,z,F)),o.Ib(1,4440064,null,0,d,[r.i,o.r],null,null)],function(t,e){t(e,1,0)},null)}var j=o.Fb("app-forecast-monitor",d,q,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);