{"_from": "@angular/core@7.2.4", "_id": "@angular/core@7.2.4", "_inBundle": false, "_integrity": "sha512-kfAxhIxl89PmB7y81FR/RAv0yWRFcEYxEnTwV+o8jKGfemAXtQ0g/Vh+lJR0SD/TBgFilMxotN1mhwH4A8GShw==", "_location": "/@angular/core", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/core@7.2.4", "name": "@angular/core", "escapedName": "@angular%2fcore", "scope": "@angular", "rawSpec": "7.2.4", "saveSpec": null, "fetchSpec": "7.2.4"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@angular/core/-/core-7.2.4.tgz", "_shasum": "a6c84940c8edcfa37158f666a1f99c6e4a97bf95", "_spec": "@angular/core@7.2.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "description": "Angular - the core framework", "es2015": "./fesm2015/core.js", "esm2015": "./esm2015/core.js", "esm5": "./esm5/core.js", "fesm2015": "./fesm2015/core.js", "fesm5": "./fesm5/core.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/core.umd.js", "module": "./fesm5/core.js", "name": "@angular/core", "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/http", "@angular/platform-server", "@angular/platform-webworker", "@angular/platform-webworker-dynamic", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/service-worker"]}, "peerDependencies": {"rxjs": "^6.0.0", "zone.js": "~0.8.26"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "sideEffects": false, "typings": "./core.d.ts", "version": "7.2.4"}