{"_from": "highcharts@^10.2.1", "_id": "highcharts@10.3.3", "_inBundle": false, "_integrity": "sha512-r7wgUPQI9tr3jFDn3XT36qsNwEIZYcfgz4mkKEA6E4nn5p86y+u1EZjazIG4TRkl5/gmGRtkBUiZW81g029RIw==", "_location": "/swt-tool-box/highcharts", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "highcharts@^10.2.1", "name": "highcharts", "escapedName": "highcharts", "rawSpec": "^10.2.1", "saveSpec": null, "fetchSpec": "^10.2.1"}, "_requiredBy": ["/swt-tool-box"], "_resolved": "https://registry.npmjs.org/highcharts/-/highcharts-10.3.3.tgz", "_shasum": "b8acca24f2d4b1f2f726540734166e59e07b35c4", "_spec": "highcharts@^10.2.1", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "author": {"name": "Highsoft AS", "email": "<EMAIL>", "url": "http://www.highcharts.com/about"}, "bugs": {"url": "https://github.com/highcharts/highcharts/issues"}, "bundleDependencies": false, "deprecated": false, "description": "JavaScript charting framework", "homepage": "http://www.highcharts.com", "keywords": ["charts", "dataviz", "graphs", "visualization", "data", "browserify", "webpack"], "license": "https://www.highcharts.com/license", "main": "highcharts.js", "name": "highcharts", "repository": {"type": "git", "url": "git+https://github.com/highcharts/highcharts-dist.git"}, "types": "highcharts.d.ts", "version": "10.3.3"}