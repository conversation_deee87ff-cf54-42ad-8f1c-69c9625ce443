(window.webpackJsonp=window.webpackJsonp||[]).push([[46],{NrE2:function(t,i,e){"use strict";e.r(i);var n=e("CcnG"),a=e("ZYCi"),o=e("447K"),l=function(){function t(t,i){this.commonService=t,this.element=i,this.jsonReader=new o.L,this.inputData=new o.G(this.commonService),this.baseURL=o.Wb.getBaseURL(),this.actionMethod="",this.actionPath="",this.requestParams=[],this.logger=null,this.logger=new o.R("Account Currency Period maintenance",this.commonService.httpclient)}return t.prototype.ngOnInit=function(){this.viewLogGrid=this.viewLogGridContainer.addChild(o.hb),this.closeButton.label=o.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=o.Wb.getPredictMessage("tooltip.close",null)},t.prototype.onLoad=function(){var t=this;this.requestParams=[];var i=0;try{this.parameters=JSON.parse(o.x.call("eval","params")),this.menuAccessId=o.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),i=10,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(i){t.inputDataResult(i)},i=20,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="accountPeriod.do?",this.actionMethod="method=displayViewLog",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.reference=this.parameters[0].reference,this.requestParams.userId=this.parameters[0].userId,this.requestParams.logDate=this.parameters[0].date,this.requestParams.logTime=this.parameters[0].time,this.requestParams.ipAddress=this.parameters[0].ipAddress,this.requestParams.ccyCode=window.opener.instanceElement.ccyCode,i=30,this.requestParams.action=this.parameters[0].action,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,i=30,this.inputData.send(this.requestParams)}catch(e){this.logger.error("method [onLoad] - error: ",e,"errorLocation: ",i),o.Wb.logError(e,o.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaintLog.ts","onLoad",i)}},t.prototype.inputDataResult=function(t){var i=0;try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),i=10,this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON&&!this.jsonReader.isDataBuilding()){var e={columns:this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodViewLogGrid.metadata.columns};i=20,this.viewLogGrid.CustomGrid(e),i=30;var n=this.lastRecievedJSON.AcctCcyMaintPeriod.acctCcyMaintPeriodViewLogGrid.rows;i=40,n.size>0?(this.viewLogGrid.gridData=n,this.viewLogGrid.setRowSize=this.jsonReader.getRowSize(),i=50,this.viewLogGrid.refresh()):this.viewLogGrid.gridData={size:0,row:[]},this.prevRecievedJSON=this.lastRecievedJSON}}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error")}catch(a){this.logger.error("method [inputDataResult] - error: ",a,"errorLocation: ",i),o.Wb.logError(a,o.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaintLog.ts","inputDataResult",i)}},t.prototype.closeHandler=function(){window.close()},t.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},t.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},t.prototype.inputDataFault=function(t){this._invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},t}(),r=[{path:"",component:l}],s=(a.l.forChild(r),function(){return function(){}}()),c=e("pMnS"),d=e("RChO"),u=e("t6HQ"),b=e("WFGK"),h=e("5FqG"),g=e("Ip0R"),R=e("gIcY"),p=e("t/Na"),m=e("sE5F"),w=e("OzfB"),f=e("T7CS"),v=e("S7LP"),L=e("6aHO"),C=e("WzUx"),y=e("A7o+"),P=e("zCE2"),I=e("Jg5P"),D=e("3R0m"),S=e("hhbb"),J=e("5rxC"),A=e("Fzqc"),O=e("21Lb"),B=e("hUWP"),M=e("3pJQ"),q=e("V9q+"),G=e("VDKW"),N=e("kXfT"),k=e("BGbe");e.d(i,"AcctCcyPeriodMaintViewLogModuleNgFactory",function(){return T}),e.d(i,"RenderType_AcctCcyPeriodMaintViewLog",function(){return j}),e.d(i,"View_AcctCcyPeriodMaintViewLog_0",function(){return E}),e.d(i,"View_AcctCcyPeriodMaintViewLog_Host_0",function(){return F}),e.d(i,"AcctCcyPeriodMaintViewLogNgFactory",function(){return W});var T=n.Gb(s,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[c.a,d.a,u.a,b.a,h.Cb,h.Pb,h.r,h.rc,h.s,h.Ab,h.Bb,h.Db,h.qd,h.Hb,h.k,h.Ib,h.Nb,h.Ub,h.yb,h.Jb,h.v,h.A,h.e,h.c,h.g,h.d,h.Kb,h.f,h.ec,h.Wb,h.bc,h.ac,h.sc,h.fc,h.lc,h.jc,h.Eb,h.Fb,h.mc,h.Lb,h.nc,h.Mb,h.dc,h.Rb,h.b,h.ic,h.Yb,h.Sb,h.kc,h.y,h.Qb,h.cc,h.hc,h.pc,h.oc,h.xb,h.p,h.q,h.o,h.h,h.j,h.w,h.Zb,h.i,h.m,h.Vb,h.Ob,h.Gb,h.Xb,h.t,h.tc,h.zb,h.n,h.qc,h.a,h.z,h.rd,h.sd,h.x,h.td,h.gc,h.l,h.u,h.ud,h.Tb,W]],[3,n.n],n.J]),n.Rb(4608,g.m,g.l,[n.F,[2,g.u]]),n.Rb(4608,R.c,R.c,[]),n.Rb(4608,R.p,R.p,[]),n.Rb(4608,p.j,p.p,[g.c,n.O,p.n]),n.Rb(4608,p.q,p.q,[p.j,p.o]),n.Rb(5120,p.a,function(t){return[t,new o.tb]},[p.q]),n.Rb(4608,p.m,p.m,[]),n.Rb(6144,p.k,null,[p.m]),n.Rb(4608,p.i,p.i,[p.k]),n.Rb(6144,p.b,null,[p.i]),n.Rb(4608,p.f,p.l,[p.b,n.B]),n.Rb(4608,p.c,p.c,[p.f]),n.Rb(4608,m.c,m.c,[]),n.Rb(4608,m.g,m.b,[]),n.Rb(5120,m.i,m.j,[]),n.Rb(4608,m.h,m.h,[m.c,m.g,m.i]),n.Rb(4608,m.f,m.a,[]),n.Rb(5120,m.d,m.k,[m.h,m.f]),n.Rb(5120,n.b,function(t,i){return[w.j(t,i)]},[g.c,n.O]),n.Rb(4608,f.a,f.a,[]),n.Rb(4608,v.a,v.a,[]),n.Rb(4608,L.a,L.a,[n.n,n.L,n.B,v.a,n.g]),n.Rb(4608,C.c,C.c,[n.n,n.g,n.B]),n.Rb(4608,C.e,C.e,[C.c]),n.Rb(4608,y.l,y.l,[]),n.Rb(4608,y.h,y.g,[]),n.Rb(4608,y.c,y.f,[]),n.Rb(4608,y.j,y.d,[]),n.Rb(4608,y.b,y.a,[]),n.Rb(4608,y.k,y.k,[y.l,y.h,y.c,y.j,y.b,y.m,y.n]),n.Rb(4608,C.i,C.i,[[2,y.k]]),n.Rb(4608,C.r,C.r,[C.L,[2,y.k],C.i]),n.Rb(4608,C.t,C.t,[]),n.Rb(4608,C.w,C.w,[]),n.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),n.Rb(1073742336,g.b,g.b,[]),n.Rb(1073742336,R.n,R.n,[]),n.Rb(1073742336,R.l,R.l,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,I.a,I.a,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,D.a,D.a,[]),n.Rb(1073742336,y.i,y.i,[]),n.Rb(1073742336,C.b,C.b,[]),n.Rb(1073742336,p.e,p.e,[]),n.Rb(1073742336,p.d,p.d,[]),n.Rb(1073742336,m.e,m.e,[]),n.Rb(1073742336,S.b,S.b,[]),n.Rb(1073742336,J.b,J.b,[]),n.Rb(1073742336,w.c,w.c,[]),n.Rb(1073742336,A.a,A.a,[]),n.Rb(1073742336,O.d,O.d,[]),n.Rb(1073742336,B.c,B.c,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,q.a,q.a,[[2,w.g],n.O]),n.Rb(1073742336,G.b,G.b,[]),n.Rb(1073742336,N.a,N.a,[]),n.Rb(1073742336,k.b,k.b,[]),n.Rb(1073742336,o.Tb,o.Tb,[]),n.Rb(1073742336,s,s,[]),n.Rb(256,p.n,"XSRF-TOKEN",[]),n.Rb(256,p.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,y.m,void 0,[]),n.Rb(256,y.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,a.i,function(){return[[{path:"",component:l}]]},[])])}),V=[[""]],j=n.Hb({encapsulation:0,styles:V,data:{}});function E(t){return n.dc(0,[n.Zb(402653184,1,{loadingImage:0}),n.Zb(402653184,2,{viewLogGridContainer:0}),n.Zb(402653184,3,{closeButton:0}),(t()(),n.Jb(3,0,null,null,19,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,i,e){var n=!0,a=t.component;"creationComplete"===i&&(n=!1!==a.onLoad()&&n);return n},h.ad,h.hb)),n.Ib(4,4440064,null,0,o.yb,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(5,0,null,0,17,"VBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,h.od,h.vb)),n.Ib(6,4440064,null,0,o.ec,[n.r,o.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(t()(),n.Jb(7,0,null,0,3,"GridRow",[["height","100%"],["paddingBottom","10"],["width","100%"]],null,null,null,h.Bc,h.J)),n.Ib(8,4440064,null,0,o.B,[n.r,o.i],{width:[0,"width"],height:[1,"height"],paddingBottom:[2,"paddingBottom"]},null),(t()(),n.Jb(9,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","viewLogGridContainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,h.Nc,h.U)),n.Ib(10,4440064,[[2,4],["viewLogGridContainer",4]],0,o.db,[n.r,o.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),n.Jb(11,0,null,0,11,"SwtCanvas",[["height","35"],["width","100%"]],null,null,null,h.Nc,h.U)),n.Ib(12,4440064,null,0,o.db,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(13,0,null,0,9,"HBox",[["width","100%"]],null,null,null,h.Dc,h.K)),n.Ib(14,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(15,0,null,0,3,"HBox",[["paddingLeft","5"],["width","90%"]],null,null,null,h.Dc,h.K)),n.Ib(16,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(17,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,i,e){var n=!0,a=t.component;"click"===i&&(n=!1!==a.closeHandler()&&n);return n},h.Mc,h.T)),n.Ib(18,4440064,[[3,4],["closeButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(19,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingLeft","5"],["width","10%"]],null,null,null,h.Dc,h.K)),n.Ib(20,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(t()(),n.Jb(21,0,null,0,1,"SwtLoadingImage",[],null,null,null,h.Zc,h.gb)),n.Ib(22,114688,[[1,4],["loadingImage",4]],0,o.xb,[n.r],null,null)],function(t,i){t(i,4,0,"100%","100%");t(i,6,0,"100%","100%","5","5","5");t(i,8,0,"100%","100%","10");t(i,10,0,"viewLogGridContainer","canvasWithGreyBorder","100%","100%","false");t(i,12,0,"100%","35");t(i,14,0,"100%");t(i,16,0,"90%","5");t(i,18,0,"closeButton",!0);t(i,20,0,"right","10%","5"),t(i,22,0)},null)}function F(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-acct-ccy-period-maint-view-log",[],null,null,null,E,j)),n.Ib(1,114688,null,0,l,[o.i,n.r],null,null)],function(t,i){t(i,1,0)},null)}var W=n.Fb("app-acct-ccy-period-maint-view-log",l,F,{},{},[])}}]);