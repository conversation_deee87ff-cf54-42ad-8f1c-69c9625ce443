(window.webpackJsonp=window.webpackJsonp||[]).push([[35],{OKL6:function(t,e,i){"use strict";i.r(e);var l=i("CcnG"),n=i("mrSG"),a=i("447K"),u=i("ZYCi"),r=function(t){function e(e,i){var l=t.call(this,i,e)||this;return l.commonService=e,l.element=i,l.jsonReader=new a.L,l.inputData=new a.G(l.commonService),l.saveData=new a.G(l.commonService),l.baseURL=a.Wb.getBaseURL(),l.actionMethod="",l.actionPath="",l.requestParams=[],l.invalidComms="",l.screenName="addScreen",l.type="Numeric",l.errorLocation=0,l.menuAccessIdParent=0,l.attributeId=null,l.accountAttributeId=null,l.versionNumber="1.0",l.versionDate="04/11/2019",l.screenVersion=new a.V(l.commonService),l.closeWindow=!1,l.ASCII_RESTRICT_PATTERN="A-Za-z0-9d_ !\"#$%&'()*+,-./:;<=>?@[\\]^`{|}~",l.ACCOUNT_NAME_RESTRICT_PATTERN="a-zA-Z0-9d .,:;#()*?[]%>_+=^|\\+/",l.moduleId="Predict",l.swtAlert=new a.bb(e),l}return n.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){try{this.saveButton.label=a.Wb.getPredictMessage("button.save",null),this.saveButton.toolTip=a.Wb.getPredictMessage("button.save",null),this.cancelButton.label=a.Wb.getPredictMessage("button.cancel",null),this.cancelButton.toolTip=a.Wb.getPredictMessage("button.cancel",null),this.attributeNameInput.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.attributename",null),this.attributeNameLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.attributename",null),this.attributeIDInput.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.attributeid",null),this.attributeIDLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.attributeid",null)+" *",this.tooltipTextArea.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.tooltiptext",null),this.tooltipTextLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.tooltiptext",null),this.typeComboBox.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.type",null),this.typeLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.type",null),this.effectiveDateLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.effectivedate",null),this.effectiveDateLabel.toolTip=a.Wb.getPredictMessage("label.accountattributehdr.effectivedate",null),this.minLengthInput.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.minlength",null),this.minLengthLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.minlength",null),this.maxLengthInput.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.maxlength",null),this.maxLengthLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.maxlength",null),this.timeValidation1.label=a.Wb.getPredictMessage("label.accountattributehdr.allowentrytime",null),this.timeValidation2.label=a.Wb.getPredictMessage("label.accountattributehdr.defaulttime",null),this.minValueInput.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.minvalue",null),this.minValueLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.minvalue",null),this.maxValueInput.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.maxvalue",null),this.maxValueLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.maxvalue",null),this.required.label=a.Wb.getPredictMessage("label.accountattributehdr.requireddate",null),this.required.toolTip=a.Wb.getPredictMessage("label.accountattributehdr.requireddate",null),this.notRequired.label=a.Wb.getPredictMessage("label.accountattributehdr.norequireddate",null),this.notRequired.toolTip=a.Wb.getPredictMessage("label.accountattributehdr.norequireddate",null),this.allowEntryTime.label=a.Wb.getPredictMessage("label.accountattributehdr.allowentrytime",null),this.allowEntryTime.toolTip=a.Wb.getPredictMessage("label.accountattributehdr.allowentrytime",null),this.defaultTime.label=a.Wb.getPredictMessage("label.accountattributehdr.defaulttime",null),this.defaultTime.toolTip=a.Wb.getPredictMessage("label.accountattributehdr.defaulttime",null),this.dateValueLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.datevalue",null),this.rgxValidationLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.regexvalidation",null),this.regexValidation.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.regexvalidation",null),this.validationMsgLabel.text=a.Wb.getPredictMessage("label.accountattributehdr.validationmsg",null),this.validationMsg.toolTip=a.Wb.getPredictMessage("tooltip.accountattributehdr.validationmsg",null);var t=[];window.opener.instanceElement&&(window.opener.instanceElement.getParamsFromParent()||(t=window.opener.instanceElement.getParamsFromParent())&&(this.screenName=t[0].screenName,this.type=t[0].type)),instanceElement=this,"addScreen"==this.screenName?(this.gridTypeNumeric.visible=!0,this.gridTypeNumeric.includeInLayout=!0,this.gridTypeDate.visible=!1,this.gridTypeDate.includeInLayout=!1,this.gridTypeText.visible=!1,this.gridTypeText.includeInLayout=!1):"Numeric"==this.type?(this.gridTypeNumeric.visible=!0,this.gridTypeNumeric.includeInLayout=!0,this.gridTypeDate.visible=!1,this.gridTypeDate.includeInLayout=!1,this.gridTypeText.visible=!1,this.gridTypeText.includeInLayout=!1):"Date"==this.type?(this.gridTypeNumeric.visible=!1,this.gridTypeNumeric.includeInLayout=!1,this.gridTypeDate.visible=!0,this.gridTypeDate.includeInLayout=!0,this.gridTypeText.visible=!1,this.gridTypeText.includeInLayout=!1):(this.gridTypeNumeric.visible=!1,this.gridTypeNumeric.includeInLayout=!1,this.gridTypeDate.visible=!1,this.gridTypeDate.includeInLayout=!1,this.gridTypeText.visible=!0,this.gridTypeText.includeInLayout=!0)}catch(e){a.Wb.logError(e,this.moduleId,"AttributeDefinitionAdd","ngOnInit",this.errorLocation)}},e.prototype.onLoad=function(){var t=this;try{this.requestParams=[],this.menuAccessIdParent=a.x.call("eval","menuAccessIdParent"),this.screenName=a.x.call("eval","screenName"),this.attributeId=a.x.call("eval","attributeId"),1!=this.menuAccessIdParent&&"viewScreen"!=this.screenName||(this.saveButton.enabled=!1),this.initializeMenus(),this.actionPath="accountAttribute.do?",this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,null!=this.screenName&&"addScreen"==this.screenName?(this.actionMethod="method=displayAddAccountAttributeHDR",this.saveButton.enabled=!0):(this.actionMethod="method=displayChangeOrViewAccountAttributeHDR",this.requestParams.attributeId=a.Z.encode64(this.attributeId)),this.requestParams.loadFlex="true",this.saveData.cbResult=function(e){t.saveDataResult(e)},this.saveData.encodeURL=!1,this.saveData.cbFault=this.inputDataFault.bind(this),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){a.Wb.logError(e,this.moduleId,"AttributeDefinitionAdd","onLoad",this.errorLocation)}},e.prototype.saveDataResult=function(t){if(this.saveData.isBusy())this.saveData.cbStop();else{var e=t,i=new a.L;i.setInputJSON(e),"Data fetch OK"==i.getRequestReplyMessage()?window.opener.instanceElement&&(window.opener.instanceElement.updateData(),this.closeHandler(t)):"errors.DataIntegrityViolationExceptioninAdd"==i.getRequestReplyMessage()?this.swtAlert.warning(a.Wb.getPredictMessage("errors.DataIntegrityViolationExceptioninAdd",null),a.Wb.getPredictMessage("screen.warning",null)):this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),a.Wb.getPredictMessage("screen.error",null),a.c.OK,this,this.errorHandler)}},e.prototype.errorHandler=function(t){t.detail==a.c.OK&&this.closeWindow&&(this.closeWindow=!1)},e.prototype.inputDataResult=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),JSON.stringify(this.lastReceivedJSON)!==JSON.stringify(this.prevRecievedJSON)&&this.jsonReader.getRequestReplyStatus()){if(!this.jsonReader.isDataBuilding()){if(this.typeComboBox.setComboData(this.jsonReader.getSelects(),!1),this.screenName&&"addScreen"!=this.screenName){if(this.typeComboBox.enabled=!1,this.attributeIDInput.enabled=!1,this.attributeIDInput.text=this.jsonReader.getSingletons().attributeid,this.attributeNameInput.text=this.jsonReader.getSingletons().attibutename,this.tooltipTextArea.text=this.jsonReader.getSingletons().tooltip,this.effectiveDateRequired.selectedValue=this.jsonReader.getSingletons().effectivedaterequired,this.effectiveDateAllowTime.selectedValue=this.jsonReader.getSingletons().effectivedateallowtime,"N"==this.effectiveDateRequired.selectedValue?(this.allowEntryTime.selected=!1,this.defaultTime.selected=!1,this.gridRowTime.enabled=!1,this.effectiveDateAllowTime.enabled=!1):(this.gridRowTime.enabled=!0,this.effectiveDateAllowTime.enabled=!0),"Numeric"==this.typeComboBox.selectedLabel){this.gridTypeNumeric.visible=!0,this.gridTypeNumeric.includeInLayout=!0,this.gridTypeDate.visible=!1,this.gridTypeDate.includeInLayout=!1,this.gridTypeText.visible=!1,this.gridTypeText.includeInLayout=!1;var e=String(this.jsonReader.getSingletons().minvalue),i=String(this.jsonReader.getSingletons().maxvalue);-1!=this.jsonReader.getSingletons().currencyformat.indexOf("2")?(""!==e&&(e=e.replace(/\./g,","),this.minValueInput.text=e),""!==i&&(i=i.replace(/\./g,","),this.maxValueInput.text=i)):(this.minValueInput.text=e,this.maxValueInput.text=i)}"Date"==this.typeComboBox.selectedLabel&&(this.gridTypeNumeric.visible=!1,this.gridTypeNumeric.includeInLayout=!1,this.gridTypeDate.visible=!0,this.gridTypeDate.includeInLayout=!0,this.gridTypeText.visible=!1,this.gridTypeText.includeInLayout=!1,this.validateDateAllowTime.selectedValue=this.jsonReader.getSingletons().dateValue),"Text"==this.typeComboBox.selectedLabel&&(this.gridTypeNumeric.visible=!1,this.gridTypeNumeric.includeInLayout=!1,this.gridTypeDate.visible=!1,this.gridTypeDate.includeInLayout=!1,this.gridTypeText.visible=!0,this.gridTypeText.includeInLayout=!0,this.minLengthInput.text=this.jsonReader.getSingletons().minlength,this.maxLengthInput.text=this.jsonReader.getSingletons().maxlength,this.regexValidation.text=this.jsonReader.getSingletons().regexvalidation,this.validationMsg.text=this.jsonReader.getSingletons().validationmessage)}this.screenName&&"viewScreen"==this.screenName&&this.disableInterface()}this.prevRecievedJSON=this.lastReceivedJSON}}catch(l){console.log("error:   ",l),a.Wb.logError(l,this.moduleId,"AttributeDefinitionAdd","inputDataResult",this.errorLocation)}},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,"Define Attribute",this.versionNumber,this.versionDate);var t=new a.n("Show JSON");this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.disableInterface=function(){this.typeComboBox.enabled=!1,this.attributeIDInput.enabled=!1,this.attributeNameInput.enabled=!1,this.tooltipTextArea.enabled=!1,this.effectiveDateRequired.enabled=!1,this.effectiveDateAllowTime.enabled=!1,"Numeric"==this.typeComboBox.selectedLabel&&(this.minValueInput.enabled=!1,this.maxValueInput.enabled=!1),"Date"==this.typeComboBox.selectedLabel&&(this.validateDateAllowTime.enabled=!1),"Text"==this.typeComboBox.selectedLabel&&(this.minLengthInput.enabled=!1,this.maxLengthInput.enabled=!1,this.regexValidation.enabled=!1,this.validationMsg.enabled=!1)},e.prototype.enableInterface=function(){this.typeComboBox.enabled=!0,this.attributeIDInput.enabled=!0,this.attributeNameInput.enabled=!0,this.tooltipTextArea.enabled=!0,this.effectiveDateRequired.enabled=!0,this.effectiveDateAllowTime.enabled=!0,"Numeric"==this.typeComboBox.selectedLabel&&(this.minValueInput.enabled=!0,this.maxValueInput.enabled=!0),"Date"==this.typeComboBox.selectedLabel&&(this.validateDateAllowTime.enabled=!0),"Text"==this.typeComboBox.selectedLabel&&(this.minLengthInput.enabled=!0,this.maxLengthInput.enabled=!0,this.regexValidation.enabled=!0,this.validationMsg.enabled=!0)},e.prototype.inputDataFault=function(t){this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.error(this.invalidComms)},e.prototype.closeHandler=function(t){this.requestParams=null,this.baseURL=null,this.actionMethod=null,this.actionPath=null,this.titleWindow?this.close():window.close()},e.prototype.startOfComms=function(){try{this.disableInterface()}catch(t){a.Wb.logError(t,this.moduleId,"AttributeDefinition","startOfComms",this.errorLocation)}},e.prototype.endOfComms=function(){try{this.enableInterface()}catch(t){a.Wb.logError(t,this.moduleId,"AttributeDefinition","endOfComms",this.errorLocation)}},e.prototype.formatAmount=function(t){var e=null,i=null,l=a.x.call("eval","ccyPattern");try{null!=(e=t.text)&&""!=e&&(e=a.Z.unformatAmount(e,Number(l)),i=a.Z.formatAmount(e,Number(2),Number(l)),t.text=i,null==e&&""==e||""!=i||this.swtAlert.warning(a.Wb.getPredictMessage("ilmAccountGroupDetails.invalidAmountAlert",null),a.Wb.getPredictMessage("screen.warning",null)))}catch(n){this.swtAlert.warning(a.Wb.getPredictMessage("ilmAccountGroupDetails.invalidAmountAlert",null),a.Wb.getPredictMessage("screen.warning",null))}},e.prototype.validateFields=function(){var t=0,e=0;return"Numeric"==this.typeComboBox.selectedLabel&&""!=this.minValueInput.text&&""!=this.maxValueInput.text&&(t=Number(this.minValueInput.text.replace(/,/g,".")),e=Number(this.maxValueInput.text.replace(/,/g,"."))),"Text"==this.typeComboBox.selectedLabel&&""!=this.minLengthInput.text&&""!=this.maxLengthInput.text&&(t=parseInt(this.minLengthInput.text,10),e=parseInt(this.maxLengthInput.text,10)),e>=t},e.prototype.isValidRegex=function(){var t=!0;if("Text"==this.typeComboBox.selectedLabel){var e=this.regexValidation.text;""!=e&&(t=a.x.call("checkRegex",e))}return t},e.prototype.isAttributeDataExist=function(t){return"true"==a.x.call("checkExistingAttributeData",t)},e.prototype.confirmChange=function(t){t.detail!=a.c.CANCEL&&this.saveAccountAttribute()},e.prototype.saveAccountAttribute=function(){var t=this;this.saveData.cbStart=this.startOfComms.bind(this),this.saveData.cbStop=this.endOfComms.bind(this),this.saveData.cbResult=function(e){t.saveDataResult(e)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1,this.actionPath="accountAttribute.do?",this.actionMethod="method=saveAccountAttributeHDR",this.requestParams=[],this.saveData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.screenName=this.screenName,this.requestParams.accountAttributeId=this.attributeIDInput.text,this.requestParams.accountAttributeName=this.attributeNameInput.text,this.requestParams.tooltipText=this.tooltipTextArea.text,this.requestParams.effectiveDateRequired=this.effectiveDateRequired.selectedValue,this.requestParams.effectiveDateAllowTime=this.effectiveDateAllowTime.selectedValue,this.requestParams.valuetype=this.typeComboBox.selectedItem.content,"Numeric"==this.typeComboBox.selectedLabel&&(this.requestParams.validateNumMin=this.minValueInput.text.replace(/,/g,"."),this.requestParams.validateNumMax=this.maxValueInput.text.replace(/,/g,".")),"Date"==this.typeComboBox.selectedLabel&&(this.requestParams.validateDateAllowTime=this.validateDateAllowTime.selectedValue),"Text"==this.typeComboBox.selectedLabel&&(this.requestParams.validateTextMinLen=this.minLengthInput.text,this.requestParams.validateTextMaxLen=this.maxLengthInput.text,this.requestParams.validateTextRegex=this.regexValidation.text,this.requestParams.validateTextRegexMsg=this.validationMsg.text),this.saveData.send(this.requestParams)},e.prototype.saveHandler=function(t){if(this.attributeIDInput.text.length>0){if(!this.validateFields())return void this.swtAlert.warning(a.Wb.getPredictMessage("alert.accountattributehdr.checkminmax",null),a.Wb.getPredictMessage("screen.warning",null));if(!this.isValidRegex())return void this.swtAlert.warning(a.Wb.getPredictMessage("alert.accountattributehdr.checkregex",null),a.Wb.getPredictMessage("screen.warning",null));this.isAttributeDataExist(this.attributeIDInput.text)?this.swtAlert.warning(a.Wb.getPredictMessage("alert.change.existingattribute.data",null),a.Wb.getPredictMessage("screen.warning",null),a.c.OK|a.c.CANCEL,null,this.confirmChange,null):this.saveAccountAttribute()}else this.swtAlert.warning(a.Wb.getPredictMessage("alert.accountattributehdr.acctattridrequired",null),a.Wb.getPredictMessage("screen.warning",null))},e.prototype.numberValidator=function(t){var e=this,i=t.text;"minValueInput"!=t.id&&"maxValueInput"!=t.id||(i=i.replace(/,/g,"."));var l="'"+i+"'"+a.Wb.getPredictMessage("alert.accountattributehdr.checknumbervalue",null),n=a.Wb.getPredictMessage("screen.warning",null);isNaN(Number(i))&&i.length>1&&this.swtAlert.warning(l,n,a.c.OK,null,function(i){e.clearInput(i,t)},null)},e.prototype.clearInput=function(t,e){e.text="",e.setFocus()},e.prototype.effectiveDateChanged=function(t){this.required.selected?(this.gridRowTime.enabled=!0,this.effectiveDateAllowTime.enabled=!0,this.allowEntryTime.selected=!0):(this.gridRowTime.enabled=!1,this.effectiveDateAllowTime.enabled=!1,this.allowEntryTime.selected=!1),this.defaultTime.selected=!1},e.prototype.typeChangeCombo=function(t){"Numeric"==this.typeComboBox.selectedLabel?(this.gridTypeNumeric.visible=!0,this.gridTypeNumeric.includeInLayout=!0,this.gridTypeDate.visible=!1,this.gridTypeDate.includeInLayout=!1,this.gridTypeText.visible=!1,this.gridTypeText.includeInLayout=!1):"Date"==this.typeComboBox.selectedLabel?(this.gridTypeNumeric.visible=!1,this.gridTypeNumeric.includeInLayout=!1,this.gridTypeDate.visible=!0,this.gridTypeDate.includeInLayout=!0,this.gridTypeText.visible=!1,this.gridTypeText.includeInLayout=!1):"Text"==this.typeComboBox.selectedLabel&&(this.gridTypeNumeric.visible=!1,this.gridTypeNumeric.includeInLayout=!1,this.gridTypeDate.visible=!1,this.gridTypeDate.includeInLayout=!1,this.gridTypeText.visible=!0,this.gridTypeText.includeInLayout=!0)},e.prototype.doHelp=function(){try{a.x.call("help")}catch(t){a.Wb.logError(t,this.moduleId,"AttributeDefinitionAdd","doHelp",this.errorLocation)}},e}(a.yb),o=[{path:"",component:r}],d=(u.l.forChild(o),function(){return function(){}}()),s=i("pMnS"),b=i("RChO"),h=i("t6HQ"),c=i("WFGK"),g=i("5FqG"),m=i("Ip0R"),p=i("gIcY"),w=i("t/Na"),I=i("sE5F"),f=i("OzfB"),v=i("T7CS"),x=i("S7LP"),T=i("6aHO"),y=i("WzUx"),R=i("A7o+"),A=i("zCE2"),L=i("Jg5P"),D=i("3R0m"),C=i("hhbb"),N=i("5rxC"),J=i("Fzqc"),W=i("21Lb"),M=i("hUWP"),P=i("3pJQ"),S=i("V9q+"),B=i("VDKW"),V=i("kXfT"),G=i("BGbe");i.d(e,"AttributeDefinitionAddModuleNgFactory",function(){return q}),i.d(e,"RenderType_AttributeDefinitionAdd",function(){return Z}),i.d(e,"View_AttributeDefinitionAdd_0",function(){return k}),i.d(e,"View_AttributeDefinitionAdd_Host_0",function(){return _}),i.d(e,"AttributeDefinitionAddNgFactory",function(){return E});var q=l.Gb(d,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[s.a,b.a,h.a,c.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,E]],[3,l.n],l.J]),l.Rb(4608,m.m,m.l,[l.F,[2,m.u]]),l.Rb(4608,p.c,p.c,[]),l.Rb(4608,p.p,p.p,[]),l.Rb(4608,w.j,w.p,[m.c,l.O,w.n]),l.Rb(4608,w.q,w.q,[w.j,w.o]),l.Rb(5120,w.a,function(t){return[t,new a.tb]},[w.q]),l.Rb(4608,w.m,w.m,[]),l.Rb(6144,w.k,null,[w.m]),l.Rb(4608,w.i,w.i,[w.k]),l.Rb(6144,w.b,null,[w.i]),l.Rb(4608,w.f,w.l,[w.b,l.B]),l.Rb(4608,w.c,w.c,[w.f]),l.Rb(4608,I.c,I.c,[]),l.Rb(4608,I.g,I.b,[]),l.Rb(5120,I.i,I.j,[]),l.Rb(4608,I.h,I.h,[I.c,I.g,I.i]),l.Rb(4608,I.f,I.a,[]),l.Rb(5120,I.d,I.k,[I.h,I.f]),l.Rb(5120,l.b,function(t,e){return[f.j(t,e)]},[m.c,l.O]),l.Rb(4608,v.a,v.a,[]),l.Rb(4608,x.a,x.a,[]),l.Rb(4608,T.a,T.a,[l.n,l.L,l.B,x.a,l.g]),l.Rb(4608,y.c,y.c,[l.n,l.g,l.B]),l.Rb(4608,y.e,y.e,[y.c]),l.Rb(4608,R.l,R.l,[]),l.Rb(4608,R.h,R.g,[]),l.Rb(4608,R.c,R.f,[]),l.Rb(4608,R.j,R.d,[]),l.Rb(4608,R.b,R.a,[]),l.Rb(4608,R.k,R.k,[R.l,R.h,R.c,R.j,R.b,R.m,R.n]),l.Rb(4608,y.i,y.i,[[2,R.k]]),l.Rb(4608,y.r,y.r,[y.L,[2,R.k],y.i]),l.Rb(4608,y.t,y.t,[]),l.Rb(4608,y.w,y.w,[]),l.Rb(1073742336,u.l,u.l,[[2,u.r],[2,u.k]]),l.Rb(1073742336,m.b,m.b,[]),l.Rb(1073742336,p.n,p.n,[]),l.Rb(1073742336,p.l,p.l,[]),l.Rb(1073742336,A.a,A.a,[]),l.Rb(1073742336,L.a,L.a,[]),l.Rb(1073742336,p.e,p.e,[]),l.Rb(1073742336,D.a,D.a,[]),l.Rb(1073742336,R.i,R.i,[]),l.Rb(1073742336,y.b,y.b,[]),l.Rb(1073742336,w.e,w.e,[]),l.Rb(1073742336,w.d,w.d,[]),l.Rb(1073742336,I.e,I.e,[]),l.Rb(1073742336,C.b,C.b,[]),l.Rb(1073742336,N.b,N.b,[]),l.Rb(1073742336,f.c,f.c,[]),l.Rb(1073742336,J.a,J.a,[]),l.Rb(1073742336,W.d,W.d,[]),l.Rb(1073742336,M.c,M.c,[]),l.Rb(1073742336,P.a,P.a,[]),l.Rb(1073742336,S.a,S.a,[[2,f.g],l.O]),l.Rb(1073742336,B.b,B.b,[]),l.Rb(1073742336,V.a,V.a,[]),l.Rb(1073742336,G.b,G.b,[]),l.Rb(1073742336,a.Tb,a.Tb,[]),l.Rb(1073742336,d,d,[]),l.Rb(256,w.n,"XSRF-TOKEN",[]),l.Rb(256,w.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,R.m,void 0,[]),l.Rb(256,R.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,u.i,function(){return[[{path:"",component:r}]]},[])])}),O=[[".numericInput[_ngcontent-%COMP%]{font-size:11px!important;height:22px!important}"]],Z=l.Hb({encapsulation:0,styles:O,data:{}});function k(t){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{attributeNameInput:0}),l.Zb(402653184,3,{attributeIDInput:0}),l.Zb(402653184,4,{minValueInput:0}),l.Zb(402653184,5,{maxValueInput:0}),l.Zb(402653184,6,{tooltipTextArea:0}),l.Zb(402653184,7,{effectiveDateRequired:0}),l.Zb(402653184,8,{effectiveDateAllowTime:0}),l.Zb(402653184,9,{validateDateAllowTime:0}),l.Zb(402653184,10,{required:0}),l.Zb(402653184,11,{notRequired:0}),l.Zb(402653184,12,{allowEntryTime:0}),l.Zb(402653184,13,{defaultTime:0}),l.Zb(402653184,14,{timeValidation1:0}),l.Zb(402653184,15,{timeValidation2:0}),l.Zb(402653184,16,{typeComboBox:0}),l.Zb(402653184,17,{attributeIDLabel:0}),l.Zb(402653184,18,{attributeNameLabel:0}),l.Zb(402653184,19,{tooltipTextLabel:0}),l.Zb(402653184,20,{effectiveDateLabel:0}),l.Zb(402653184,21,{typeLabel:0}),l.Zb(402653184,22,{dateValueLabel:0}),l.Zb(402653184,23,{minValueLabel:0}),l.Zb(402653184,24,{maxValueLabel:0}),l.Zb(402653184,25,{minLengthLabel:0}),l.Zb(402653184,26,{maxLengthLabel:0}),l.Zb(402653184,27,{rgxValidationLabel:0}),l.Zb(402653184,28,{validationMsgLabel:0}),l.Zb(402653184,29,{minLengthInput:0}),l.Zb(402653184,30,{maxLengthInput:0}),l.Zb(402653184,31,{saveButton:0}),l.Zb(402653184,32,{cancelButton:0}),l.Zb(402653184,33,{gridTypeNumeric:0}),l.Zb(402653184,34,{gridTypeDate:0}),l.Zb(402653184,35,{gridTypeText:0}),l.Zb(402653184,36,{gridRowTime:0}),l.Zb(402653184,37,{regexValidation:0}),l.Zb(402653184,38,{validationMsg:0}),(t()(),l.Jb(38,0,null,null,172,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var l=!0,n=t.component;"creationComplete"===e&&(l=!1!==n.onLoad()&&l);return l},g.ad,g.hb)),l.Ib(39,4440064,null,0,a.yb,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(40,0,null,0,170,"VBox",[["height","100%"],["paddingBottom","15"],["paddingLeft","15"],["paddingRight","15"],["paddingTop","15"],["width","100%"]],null,null,null,g.od,g.vb)),l.Ib(41,4440064,null,0,a.ec,[l.r,a.i,l.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),l.Jb(42,0,null,0,154,"SwtCanvas",[["height","90%"],["width","100%"]],null,null,null,g.Nc,g.U)),l.Ib(43,4440064,null,0,a.db,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(44,0,null,0,152,"VBox",[["height","100%"],["verticalGap","1"],["width","100%"]],null,null,null,g.od,g.vb)),l.Ib(45,4440064,null,0,a.ec,[l.r,a.i,l.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(46,0,null,0,69,"Grid",[["height","52%"],["marginTop","3"],["verticalGap","1"],["width","100%"]],null,null,null,g.Cc,g.H)),l.Ib(47,4440064,null,0,a.z,[l.r,a.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],marginTop:[3,"marginTop"]},null),(t()(),l.Jb(48,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(49,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(50,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(51,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(52,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(53,4440064,[[17,4],["attributeIDLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(54,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(55,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(56,0,null,0,1,"SwtTextInput",[["id","attributeIDInput"],["maxChars","50"],["restrict","0-9a-zA-Z_"],["width","100%"]],null,null,null,g.kd,g.sb)),l.Ib(57,4440064,[[3,4],["attributeIDInput",4]],0,a.Rb,[l.r,a.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],width:[3,"width"]},null),(t()(),l.Jb(58,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(59,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(60,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(61,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(62,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(63,4440064,[[18,4],["attributeNameLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(64,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(65,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(66,0,null,0,1,"SwtTextInput",[["id","attributeNameInput"],["maxChars","100"],["restrict","a-zA-Z0-9\\d .,:;#\\(\\)\\*\\?\\[\\]%>_+=^\\|\\\\+/"],["width","100%"]],null,null,null,g.kd,g.sb)),l.Ib(67,4440064,[[2,4],["attributeNameInput",4]],0,a.Rb,[l.r,a.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],width:[3,"width"]},null),(t()(),l.Jb(68,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(69,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(70,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(71,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(72,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(73,4440064,[[19,4],["tooltipTextLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(74,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(75,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(76,0,null,0,1,"SwtTextArea",[["height","60"],["id","tooltipTextArea"],["maxChars","250"],["width","100%"]],null,null,null,g.jd,g.rb)),l.Ib(77,4440064,[[6,4],["tooltipTextArea",4]],0,a.Qb,[l.r,a.i,l.L],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"],height:[3,"height"]},null),(t()(),l.Jb(78,0,null,0,14,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(79,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(80,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(81,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(82,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(83,4440064,[[20,4],["effectiveDateLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(84,0,null,0,8,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(85,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(86,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","effectiveDateRequired"],["width","95%"]],null,[[null,"change"]],function(t,e,i){var l=!0,n=t.component;"change"===e&&(l=!1!==n.effectiveDateChanged(i)&&l);return l},g.ed,g.lb)),l.Ib(87,4440064,[[7,4],["effectiveDateRequired",4]],1,a.Hb,[w.c,l.r,a.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),l.Zb(603979776,39,{radioItems:1}),(t()(),l.Jb(89,0,null,0,1,"SwtRadioItem",[["groupName","effectiveDateRequired"],["id","required"],["selected","true"],["value","Y"],["width","50%"]],null,null,null,g.fd,g.mb)),l.Ib(90,4440064,[[39,4],[10,4],["required",4]],0,a.Ib,[l.r,a.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),l.Jb(91,0,null,0,1,"SwtRadioItem",[["groupName","effectiveDateRequired"],["id","notRequired"],["value","N"],["width","50%"]],null,null,null,g.fd,g.mb)),l.Ib(92,4440064,[[39,4],[11,4],["notRequired",4]],0,a.Ib,[l.r,a.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),l.Jb(93,0,null,0,12,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(94,4440064,[[36,4],["gridRowTime",4]],0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(95,0,null,0,1,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(96,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(97,0,null,0,8,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(98,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(99,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","effectiveDateAllowTime"],["width","95%"]],null,null,null,g.ed,g.lb)),l.Ib(100,4440064,[[8,4],["effectiveDateAllowTime",4]],1,a.Hb,[w.c,l.r,a.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},null),l.Zb(603979776,40,{radioItems:1}),(t()(),l.Jb(102,0,null,0,1,"SwtRadioItem",[["groupName","effectiveDateAllowTime"],["id","allowEntryTime"],["selected","true"],["value","Y"],["width","50%"]],null,null,null,g.fd,g.mb)),l.Ib(103,4440064,[[40,4],[12,4],["allowEntryTime",4]],0,a.Ib,[l.r,a.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),l.Jb(104,0,null,0,1,"SwtRadioItem",[["groupName","effectiveDateAllowTime"],["id","defaultTime"],["value","N"],["width","50%"]],null,null,null,g.fd,g.mb)),l.Ib(105,4440064,[[40,4],[13,4],["defaultTime",4]],0,a.Ib,[l.r,a.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),l.Jb(106,0,null,0,9,"GridRow",[["marginTop","10"]],null,null,null,g.Bc,g.J)),l.Ib(107,4440064,null,0,a.B,[l.r,a.i],{marginTop:[0,"marginTop"]},null),(t()(),l.Jb(108,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(109,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(110,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(111,4440064,[[21,4],["typeLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(112,0,null,0,3,"GridItem",[["width","80%"]],null,null,null,g.Ac,g.I)),l.Ib(113,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(114,0,null,0,1,"SwtComboBox",[["dataLabel","types"],["id","typeComboBox"],["width","150"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var n=!0,a=t.component;"window:mousewheel"===e&&(n=!1!==l.Tb(t,115).mouseWeelEventHandler(i.target)&&n);"change"===e&&(n=!1!==a.typeChangeCombo(i)&&n);return n},g.Pc,g.W)),l.Ib(115,4440064,[[16,4],["typeComboBox",4]],0,a.gb,[l.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),l.Jb(116,0,null,0,21,"Grid",[["height","15%"],["verticalGap","1"],["width","100%"]],null,null,null,g.Cc,g.H)),l.Ib(117,4440064,[[33,4],["gridTypeNumeric",4]],0,a.z,[l.r,a.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(118,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(119,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(120,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(121,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(122,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(123,4440064,[[23,4],["minValueLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(124,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(125,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(126,0,null,0,1,"SwtTextInput",[["id","minValueInput"],["maxChars","38"],["restrict","0-9.,-"],["textAlign","right"],["width","180"]],null,[[null,"focusOut"]],function(t,e,i){var n=!0,a=t.component;"focusOut"===e&&(n=!1!==a.numberValidator(l.Tb(t,127))&&n);return n},g.kd,g.sb)),l.Ib(127,4440064,[[4,4],["minValueInput",4]],0,a.Rb,[l.r,a.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],textAlign:[3,"textAlign"],width:[4,"width"]},{onFocusOut_:"focusOut"}),(t()(),l.Jb(128,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(129,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(130,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(131,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(132,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(133,4440064,[[24,4],["maxValueLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(134,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(135,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(136,0,null,0,1,"SwtTextInput",[["id","maxValueInput"],["maxChars","38"],["restrict","0-9.,-"],["textAlign","right"],["width","180"]],null,[[null,"focusOut"]],function(t,e,i){var n=!0,a=t.component;"focusOut"===e&&(n=!1!==a.numberValidator(l.Tb(t,137))&&n);return n},g.kd,g.sb)),l.Ib(137,4440064,[[5,4],["maxValueInput",4]],0,a.Rb,[l.r,a.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],textAlign:[3,"textAlign"],width:[4,"width"]},{onFocusOut_:"focusOut"}),(t()(),l.Jb(138,0,null,0,16,"Grid",[["height","7%"],["verticalGap","1"],["width","100%"]],null,null,null,g.Cc,g.H)),l.Ib(139,4440064,[[34,4],["gridTypeDate",4]],0,a.z,[l.r,a.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(140,0,null,0,14,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(141,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(142,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(143,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(144,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(145,4440064,[[22,4],["dateValueLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(146,0,null,0,8,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(147,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(148,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","validateDateAllowTime"],["width","95%"]],null,null,null,g.ed,g.lb)),l.Ib(149,4440064,[[9,4],["validateDateAllowTime",4]],1,a.Hb,[w.c,l.r,a.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},null),l.Zb(603979776,41,{radioItems:1}),(t()(),l.Jb(151,0,null,0,1,"SwtRadioItem",[["groupName","validateDateAllowTime"],["id","timeValidation1"],["selected","true"],["value","Y"],["width","50%"]],null,null,null,g.fd,g.mb)),l.Ib(152,4440064,[[41,4],[14,4],["timeValidation1",4]],0,a.Ib,[l.r,a.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),l.Jb(153,0,null,0,1,"SwtRadioItem",[["groupName","validateDateAllowTime"],["id","timeValidation2"],["value","N"],["width","50%"]],null,null,null,g.fd,g.mb)),l.Ib(154,4440064,[[41,4],[15,4],["timeValidation2",4]],0,a.Ib,[l.r,a.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),l.Jb(155,0,null,0,41,"Grid",[["height","48%"],["verticalGap","1"],["width","100%"]],null,null,null,g.Cc,g.H)),l.Ib(156,4440064,[[35,4],["gridTypeText",4]],0,a.z,[l.r,a.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),l.Jb(157,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(158,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(159,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(160,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(161,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(162,4440064,[[25,4],["minLengthLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(163,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(164,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(165,0,null,0,1,"SwtNumericInput",[["id","minLengthInput"],["maxChars","38"],["textAlign","right"],["width","180"]],null,[[null,"change"]],function(t,e,i){var n=!0,a=t.component;"change"===e&&(n=!1!==a.numberValidator(l.Tb(t,166))&&n);return n},g.cd,g.jb)),l.Ib(166,4440064,[[29,4],["minLengthInput",4]],0,a.Ab,[l.r,a.i],{maxChars:[0,"maxChars"],id:[1,"id"],textAlign:[2,"textAlign"],width:[3,"width"]},{change_:"change"}),(t()(),l.Jb(167,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(168,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(169,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(170,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(171,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(172,4440064,[[26,4],["maxLengthLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(173,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(174,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(175,0,null,0,1,"SwtNumericInput",[["id","maxLengthInput"],["maxChars","38"],["textAlign","right"],["width","180"]],null,[[null,"focusOut"]],function(t,e,i){var n=!0,a=t.component;"focusOut"===e&&(n=!1!==a.numberValidator(l.Tb(t,176))&&n);return n},g.cd,g.jb)),l.Ib(176,4440064,[[30,4],["maxLengthInput",4]],0,a.Ab,[l.r,a.i],{maxChars:[0,"maxChars"],id:[1,"id"],textAlign:[2,"textAlign"],width:[3,"width"]},{onFocusOut_:"focusOut"}),(t()(),l.Jb(177,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(178,4440064,null,0,a.B,[l.r,a.i],null,null),(t()(),l.Jb(179,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(180,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(181,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(182,4440064,[[27,4],["rgxValidationLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(183,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(184,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(185,0,null,0,1,"SwtTextArea",[["height","60"],["id","regexValidation"],["maxChars","250"],["width","77%"]],null,null,null,g.jd,g.rb)),l.Ib(186,4440064,[[37,4],["regexValidation",4]],0,a.Qb,[l.r,a.i,l.L],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"],height:[3,"height"]},null),(t()(),l.Jb(187,0,null,0,9,"GridRow",[["marginTop","3"]],null,null,null,g.Bc,g.J)),l.Ib(188,4440064,null,0,a.B,[l.r,a.i],{marginTop:[0,"marginTop"]},null),(t()(),l.Jb(189,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),l.Ib(190,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(191,0,null,0,1,"SwtLabel",[["fontWeight","bold"]],null,null,null,g.Yc,g.fb)),l.Ib(192,4440064,[[28,4],["validationMsgLabel",4]],0,a.vb,[l.r,a.i],{fontWeight:[0,"fontWeight"]},null),(t()(),l.Jb(193,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(194,4440064,null,0,a.A,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(195,0,null,0,1,"SwtTextArea",[["height","60"],["id","validationMsg"],["maxChars","250"],["width","77%"]],null,null,null,g.jd,g.rb)),l.Ib(196,4440064,[[38,4],["validationMsg",4]],0,a.Qb,[l.r,a.i,l.L],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"],height:[3,"height"]},null),(t()(),l.Jb(197,0,null,0,13,"SwtCanvas",[["id","canvasContainer"],["width","100%"]],null,null,null,g.Nc,g.U)),l.Ib(198,4440064,null,0,a.db,[l.r,a.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),l.Jb(199,0,null,0,11,"HBox",[["width","100%"]],null,null,null,g.Dc,g.K)),l.Ib(200,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(201,0,null,0,5,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,g.Dc,g.K)),l.Ib(202,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(203,0,null,0,1,"SwtButton",[["id","saveButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.saveHandler(i)&&l);return l},g.Mc,g.T)),l.Ib(204,4440064,[[31,4],["saveButton",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),l.Jb(205,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.closeHandler(i)&&l);return l},g.Mc,g.T)),l.Ib(206,4440064,[[32,4],["cancelButton",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),l.Jb(207,0,null,0,3,"HBox",[["horizontalAlign","right"]],null,null,null,g.Dc,g.K)),l.Ib(208,4440064,null,0,a.C,[l.r,a.i],{horizontalAlign:[0,"horizontalAlign"]},null),(t()(),l.Jb(209,0,null,0,1,"SwtHelpButton",[["enabled","true"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.doHelp()&&l);return l},g.Wc,g.db)),l.Ib(210,4440064,null,0,a.rb,[l.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"})],function(t,e){t(e,39,0,"100%","100%");t(e,41,0,"100%","100%","15","15","15","15");t(e,43,0,"100%","90%");t(e,45,0,"1","100%","100%");t(e,47,0,"1","100%","52%","3"),t(e,49,0);t(e,51,0,"20%");t(e,53,0,"bold");t(e,55,0,"75%");t(e,57,0,"50","0-9a-zA-Z_","attributeIDInput","100%"),t(e,59,0);t(e,61,0,"20%");t(e,63,0,"bold");t(e,65,0,"75%");t(e,67,0,"100","a-zA-Z0-9\\d .,:;#\\(\\)\\*\\?\\[\\]%>_+=^\\|\\\\+/","attributeNameInput","100%"),t(e,69,0);t(e,71,0,"20%");t(e,73,0,"bold");t(e,75,0,"75%");t(e,77,0,"250","tooltipTextArea","100%","60"),t(e,79,0);t(e,81,0,"20%");t(e,83,0,"bold");t(e,85,0,"75%");t(e,87,0,"effectiveDateRequired","95%","horizontal");t(e,90,0,"required","50%","effectiveDateRequired","Y","true");t(e,92,0,"notRequired","50%","effectiveDateRequired","N"),t(e,94,0);t(e,96,0,"20%");t(e,98,0,"75%");t(e,100,0,"effectiveDateAllowTime","95%","horizontal");t(e,103,0,"allowEntryTime","50%","effectiveDateAllowTime","Y","true");t(e,105,0,"defaultTime","50%","effectiveDateAllowTime","N");t(e,107,0,"10");t(e,109,0,"20%");t(e,111,0,"bold");t(e,113,0,"80%");t(e,115,0,"types","150","typeComboBox");t(e,117,0,"1","100%","15%"),t(e,119,0);t(e,121,0,"20%");t(e,123,0,"bold");t(e,125,0,"75%");t(e,127,0,"38","0-9.,-","minValueInput","right","180"),t(e,129,0);t(e,131,0,"20%");t(e,133,0,"bold");t(e,135,0,"75%");t(e,137,0,"38","0-9.,-","maxValueInput","right","180");t(e,139,0,"1","100%","7%"),t(e,141,0);t(e,143,0,"20%");t(e,145,0,"bold");t(e,147,0,"75%");t(e,149,0,"validateDateAllowTime","95%","horizontal");t(e,152,0,"timeValidation1","50%","validateDateAllowTime","Y","true");t(e,154,0,"timeValidation2","50%","validateDateAllowTime","N");t(e,156,0,"1","100%","48%"),t(e,158,0);t(e,160,0,"20%");t(e,162,0,"bold");t(e,164,0,"75%");t(e,166,0,"38","minLengthInput","right","180"),t(e,168,0);t(e,170,0,"20%");t(e,172,0,"bold");t(e,174,0,"75%");t(e,176,0,"38","maxLengthInput","right","180"),t(e,178,0);t(e,180,0,"20%");t(e,182,0,"bold");t(e,184,0,"75%");t(e,186,0,"250","regexValidation","77%","60");t(e,188,0,"3");t(e,190,0,"20%");t(e,192,0,"bold");t(e,194,0,"75%");t(e,196,0,"250","validationMsg","77%","60");t(e,198,0,"canvasContainer","100%");t(e,200,0,"100%");t(e,202,0,"100%","5");t(e,204,0,"saveButton","70");t(e,206,0,"cancelButton","70","true");t(e,208,0,"right");t(e,210,0,"helpIcon","true",!0)},null)}function _(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-attribute-definition-add",[],null,null,null,k,Z)),l.Ib(1,4440064,null,0,r,[a.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var E=l.Fb("app-attribute-definition-add",r,_,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);