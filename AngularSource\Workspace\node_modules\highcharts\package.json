{"_from": "highcharts@8.2.0", "_id": "highcharts@8.2.0", "_inBundle": false, "_integrity": "sha512-s3R7UEaMWUDJNxFfdjuiOtI8rnSvhEZUNN4TA7LiDRc9Tw7DUKvTv5WXSTYzxojokLbybwwlf9t3jg/meAUTnQ==", "_location": "/highcharts", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "highcharts@8.2.0", "name": "highcharts", "escapedName": "highcharts", "rawSpec": "8.2.0", "saveSpec": null, "fetchSpec": "8.2.0"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/highcharts/-/highcharts-8.2.0.tgz", "_shasum": "3d4893015caf1a7c9ed85284d983911e844649f3", "_spec": "highcharts@8.2.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "Highsoft AS", "email": "<EMAIL>", "url": "http://www.highcharts.com/about"}, "bugs": {"url": "https://github.com/highcharts/highcharts/issues"}, "bundleDependencies": false, "deprecated": false, "description": "JavaScript charting framework", "homepage": "http://www.highcharts.com", "keywords": ["charts", "dataviz", "graphs", "visualization", "data", "browserify", "webpack"], "license": "https://www.highcharts.com/license", "main": "highcharts.js", "name": "highcharts", "repository": {"type": "git", "url": "git+https://github.com/highcharts/highcharts-dist.git"}, "types": "highcharts.d.ts", "version": "8.2.0"}