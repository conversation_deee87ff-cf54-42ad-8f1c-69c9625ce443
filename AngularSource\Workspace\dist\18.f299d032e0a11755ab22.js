(window.webpackJsonp=window.webpackJsonp||[]).push([[18],{HDi1:function(l,e,t){"use strict";t.r(e);var n=t("CcnG"),i=t("mrSG"),u=t("447K"),a=t("ZYCi"),b=t("R1Kr"),o=function(l){function e(e,t){var n=l.call(this,t,e)||this;return n.commonService=e,n.element=t,n.jsonReader=new u.L,n.jsonReaderMethod=new u.L,n.inputData=new u.G(n.commonService),n.logicUpdate=new u.G(n.commonService),n.saveData=new u.G(n.commonService),n.baseURL=u.Wb.getBaseURL(),n.actionMethod="",n.actionPath="",n.requestParams=[],n.screenName=null,n.helpURL=null,n.message=null,n.title=null,n.groupId=null,n.searchQuery="",n.queryToDisplay="",n.errorLocation=0,n.moduleId=null,n.paymentRequestId=null,n.lastActionValue="",n.selectedTimeFrame="E",n.menuAccess=2,n.swtAlert=new u.bb(e),n}return i.d(e,l),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){if(this.enableDisableButtons(!1),window.opener&&window.opener.instanceElement){var l=window.opener.instanceElement.getParamsFromParent?window.opener.instanceElement.getParamsFromParent():"";l&&(this.paymentRequestId=l[0].payRequestId?l[0].payRequestId:"",this.selectedTimeFrame=l[0].timeFrame?l[0].timeFrame:"")}this.paymentRequestId?(this.paymentIdTextInput.visible=!1,this.paymentIdTextInput.includeInLayout=!1,this.paymentIdLabel.text=this.paymentRequestId):this.paymentIdLabel.visible=!1,instanceElement=this},e.prototype.inputDataFault=function(l){this.swtAlert.error(l.fault.faultstring+"\n"+l.fault.faultCode+"\n"+l.fault.faultDetail)},e.prototype.onLoad=function(l){var e=this;try{this.enableDisableButtons(!1),this.unStopButton.enabled=!1,this.releaseButton.enabled=!1,this.timeFrameRadioGroup.enabled=!1,this.changeCatgButton.enabled=!1,this.paymentRequestId&&(this.requestParams=[],this.actionPath="paymentDisplayPCM.do?",this.requestParams.moduleId=this.moduleId,"add"!=this.screenName&&(this.requestParams.payReqId=this.paymentRequestId,this.actionMethod="method=view"),this.selectedTimeFrame||(this.selectedTimeFrame="E"),"timeFrame"==l?this.selectedTimeFrame=this.timeFrameRadioGroup.selectedValue:this.timeFrameRadioGroup.selectedValue=this.selectedTimeFrame,this.requestParams.selectedTimeFrame=this.selectedTimeFrame,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(l){e.inputDataResult(l)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1)}catch(t){u.Wb.logError(t,this.moduleId,"className","onLoad",this.errorLocation)}},e.prototype.doShowMessageOutList=function(){u.x.call("openChildMessageOut","messageout")},e.prototype.doShowMessageList=function(){u.x.call("openChildMessageList","message")},e.prototype.doShowStopRulesList=function(){u.x.call("openChildStopRulesList","stop")},e.prototype.doShowLogs=function(){u.x.call("openChildLogs","logs")},e.prototype.inputDataResultFromChange=function(l){try{this.inputData.isBusy()?this.inputData.cbStop():(this.jsonReaderMethod.setInputJSON(l),"ERROR"==this.jsonReaderMethod.getRequestReplyMessage()?this.swtAlert.error("Error occured during Save, please contact your admininstrator for more"):"ERROR_PAYMENT_ENGINE"==this.jsonReaderMethod.getRequestReplyMessage()?this.swtAlert.error("Fail when sending response to the payment engine. Please check error logs for more details"):"ARCHIVE_LOCATION"==this.jsonReaderMethod.getRequestReplyMessage()||"INCORRECT_PREVIOUS_STATUS"==this.jsonReaderMethod.getRequestReplyMessage()?console.log("error:   ",this.jsonReaderMethod.getRequestReplyMessage()):(this.onLoad(null),window.opener&&window.opener.instanceElement&&(window.opener.instanceElement.paySelectedIndex=null,window.opener.instanceElement.updataDataWithPagination&&window.opener.instanceElement.updataDataWithPagination())))}catch(e){console.log("error:   ",e),u.Wb.logError(e,this.moduleId,"className","inputDataResultFromChange",this.errorLocation)}},e.prototype.inputDataResult=function(l){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=l,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON))if("INVALID_ID"==this.jsonReader.getRequestReplyMessage())this.swtAlert.error("Please enter a valid Id"),this.enableDisableButtons(!1);else{if(this.helpURL=this.jsonReader.getSingletons().helpurl,!this.jsonReader.isDataBuilding()){if(this.lastActionLabel.text=this.jsonReader.getSingletons().lastAction,this.accountidLabel.text=this.jsonReader.getSingletons().accountid,this.accountwithinstnameLabel.text=this.jsonReader.getSingletons().accountwithinstname,this.accountwithinstaccountLabel.text=this.jsonReader.getSingletons().accountwithinstaccount,this.accountwithinstbicLabel.text=this.jsonReader.getSingletons().accountwithinstbic,this.actionLabel.text=this.jsonReader.getSingletons().action,this.amountLabel.text=this.jsonReader.getSingletons().amount,this.boreferenceLabel.text=this.jsonReader.getSingletons().boreference,this.beneficiarycustnameLabel.text=this.jsonReader.getSingletons().beneficiarycustname,this.beneficiarycustaccountLabel.text=this.jsonReader.getSingletons().beneficiarycustaccount,this.beneficiarycustbicLabel.text=this.jsonReader.getSingletons().beneficiarycustbic,this.beneficiaryinstnameLabel.text=this.jsonReader.getSingletons().beneficiaryinstname,this.beneficiaryinstaccountLabel.text=this.jsonReader.getSingletons().beneficiaryinstaccount,this.beneficiaryinstbicLabel.text=this.jsonReader.getSingletons().beneficiaryinstbic,this.currencycodeLabel.text=this.jsonReader.getSingletons().currencycode,this.delivererscustodianaccountLabel.text=this.jsonReader.getSingletons().delivererscustodianaccount,this.delivererscustodianbicLabel.text=this.jsonReader.getSingletons().delivererscustodianbic,this.delivererscustodiannameLabel.text=this.jsonReader.getSingletons().delivererscustodianname,this.deliveryagentaccountLabel.text=this.jsonReader.getSingletons().deliveryagentaccount,this.deliveryagentbicLabel.text=this.jsonReader.getSingletons().deliveryagentbic,this.deliveryagentnameLabel.text=this.jsonReader.getSingletons().deliveryagentname,this.departmentLabel.text=this.jsonReader.getSingletons().department,this.entityidLabel.text=this.jsonReader.getSingletons().entityid,this.messageLabel.text=this.jsonReader.getSingletons().creationMsgId,this.gpiLabel.text=this.jsonReader.getSingletons().gpi,this.inputdateLabel.text=this.jsonReader.getSingletons().inputdate,this.intermediaryaccountLabel.text=this.jsonReader.getSingletons().intermediaryaccount,this.intermediarybicLabel.text=this.jsonReader.getSingletons().intermediarybic,this.intermediarynameLabel.text=this.jsonReader.getSingletons().intermediaryname,this.messagetypeLabel.text=this.jsonReader.getSingletons().messagetype,this.orderingcustaccountLabel.text=this.jsonReader.getSingletons().orderingcustaccount,this.orderingcustbeiLabel.text=this.jsonReader.getSingletons().orderingcustbei,this.orderingcustnameLabel.text=this.jsonReader.getSingletons().orderingcustname,this.orderinginstaccountLabel.text=this.jsonReader.getSingletons().orderinginstaccount,this.orderinginstbicLabel.text=this.jsonReader.getSingletons().orderinginstbic,this.orderinginstnameLabel.text=this.jsonReader.getSingletons().orderinginstname,this.paymenttypeLabel.text=this.jsonReader.getSingletons().paymenttype,this.placeofsettlementaccountLabel.text=this.jsonReader.getSingletons().placeofsettlementaccount,this.placeofsettlementbicLabel.text=this.jsonReader.getSingletons().placeofsettlementbic,this.placeofsettlementnameLabel.text=this.jsonReader.getSingletons().placeofsettlementname,this.receiverbicLabel.text=this.jsonReader.getSingletons().receiverbic,this.receiverscorresnameLabel.text=this.jsonReader.getSingletons().receiverscorresname,this.receiverscorresaccountLabel.text=this.jsonReader.getSingletons().receiverscorresaccount,this.receiverscorresbicLabel.text=this.jsonReader.getSingletons().receiverscorresbic,this.paymentreferenceLabel.text=this.jsonReader.getSingletons().paymentreference,this.relatedreferenceLabel.text=this.jsonReader.getSingletons().relatedreference,this.selleraccountLabel.text=this.jsonReader.getSingletons().selleraccount,this.sellerbicLabel.text=this.jsonReader.getSingletons().sellerbic,this.sellernameLabel.text=this.jsonReader.getSingletons().sellername,this.senderbicLabel.text=this.jsonReader.getSingletons().senderbic,this.senderscorresnameLabel.text=this.jsonReader.getSingletons().senderscorresname,this.senderreceiverinfoLabel.htmlText=this.jsonReader.getSingletons().senderreceiverinfo,this.senderscorresaccountLabel.text=this.jsonReader.getSingletons().senderscorresaccount,this.senderscorresbicLabel.text=this.jsonReader.getSingletons().senderscorresbic,this.sourceidLabel.text=this.jsonReader.getSingletons().sourceid,this.releasedByLabel.text=this.jsonReader.getSingletons().releasedBy,this.sourceurgencyindicatorLabel.text=this.jsonReader.getSingletons().urgentSpreadable,this.entitysubidLabel.text=this.jsonReader.getSingletons().entitysubid,this.thirdreimbsmntaccountLabel.text=this.jsonReader.getSingletons().thirdreimbsmntaccount,this.thirdreimbsmntbicLabel.text=this.jsonReader.getSingletons().thirdreimbsmntbic,this.thirdreimbsmntnameLabel.text=this.jsonReader.getSingletons().thirdreimbsmntname,this.valuedateLabel.text=this.jsonReader.getSingletons().valuedate,this.categoryRulelabel.text=this.jsonReader.getSingletons().categoryRulelabel,this.categoryIDlabel.text=this.jsonReader.getSingletons().categoryIDlabel,this.accountGroupLabel.text=this.jsonReader.getSingletons().accountGroupLabel,this.accountGroupNameLabel.text=this.jsonReader.getSingletons().accountGroupNameLabel,this.cutOffTimeLabel.text=this.jsonReader.getSingletons().cutOffTimeLabel,this.accounttypeLabel.text=this.jsonReader.getSingletons().accounttypeLabel,this.urgentLabel.text=this.jsonReader.getSingletons().sourceurgencyindicator,this.foreferenceLabel.text=this.jsonReader.getSingletons().foreferenceLabel,this.sourcereferenceLabel.text=this.jsonReader.getSingletons().sourcereferenceLabel,this.stoppedtimeLabel.text=this.jsonReader.getSingletons().stoppedtimeLabel,this.releaseDateLabel.text=this.jsonReader.getSingletons().releaseDateLabel,this.waitingDateLabel.text=this.jsonReader.getSingletons().waitingDateLabel,this.reqreleasetimeLabel.text=this.jsonReader.getSingletons().reqreleasetimeLabel,this.stoppedUserLabel.text=this.jsonReader.getSingletons().stoppedUserLabel,this.stoppedReasonLabel.text=this.jsonReader.getSingletons().stoppedReasonLabel,this.unstoppedtimeLabel.text=this.jsonReader.getSingletons().unstoppedtimeLabel,this.unstoppedUserLabel.text=this.jsonReader.getSingletons().unstoppedUserLabel,this.unstoppedReasonLabel.text=this.jsonReader.getSingletons().unstoppedReasonLabel,this.blockedtimeLabel.text=this.jsonReader.getSingletons().blockedtimeLabel,this.blockedUserLabel.text=this.jsonReader.getSingletons().blockedUserLabel,this.blockedReasonLabel.text=this.jsonReader.getSingletons().blockedReasonLabel,this.cancelledtimeLabel.text=this.jsonReader.getSingletons().cancelledtimeLabel,this.cancelledUserLabel.text=this.jsonReader.getSingletons().cancelledUserLabel,this.cancelledReasonLabel.text=this.jsonReader.getSingletons().cancelledReasonLabel,this.menuAccess=Number(this.jsonReader.getScreenAttributes().menuaccess),this.location=this.jsonReader.getSingletons().location,this.jsonReader.getSingletons().paymentMessage){var e=b.pd.xml(u.Z.decode64(this.jsonReader.getSingletons().paymentMessage));e=this.htmlEntities(e),this.paymentMessageArea.htmlText=e}this.lastActionValue=this.jsonReader.getSingletons().lastActionValue,this.resetTextInputsColor(),"N"==this.lastActionValue||"W"==this.lastActionValue?(this.waitingLabel.color="green",this.waitingDateLabel.color="green"):"S"==this.lastActionValue?(this.stoppedtimeLabel.color="green",this.stoppedUserLabel.color="green",this.stoppedReasonLabel.color="green",this.stoppedLabel.color="green"):"B"==this.lastActionValue?(this.blockedtimeLabel.color="green",this.blockedUserLabel.color="green",this.blockedReasonLabel.color="green",this.blockedLabel.color="green"):"C"==this.lastActionValue?(this.cancelledtimeLabel.color="green",this.cancelledUserLabel.color="green",this.cancelledReasonLabel.color="green",this.cancelledLabel.color="green"):"R"==this.lastActionValue&&(this.releasedSourceLabel.color="green",this.releasedByLabel.color="green",this.releaseDateLabel.color="green",this.releaseLabel.color="green"),"L"==this.location&&("S"==this.lastActionValue&&(this.unStopButton.enabled=0==this.menuAccess),"B"!=this.lastActionValue&&"S"!=this.lastActionValue&&"W"!=this.lastActionValue||(this.releaseButton.enabled=0==this.menuAccess)),this.enableDisableButtons(!0)}this.timeFrameRadioGroup.enabled=!0}}catch(t){console.log("error:   ",t),u.Wb.logError(t,this.moduleId,"className","inputDataResult",this.errorLocation)}},e.prototype.htmlEntities=function(l){return String(l).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/ /g,"&nbsp;")},e.prototype.getParamsFromParent=function(){var l=this.jsonReader.getSingletons().accountGroupSpreadId;return[{paymentRequestId:this.paymentRequestId,spreadId:l,screenName:"view"}]},e.prototype.keyDownOnPaymentRequestEventHandler=function(l){try{var e=this.paymentIdTextInput.text;l.keyCode==u.N.ENTER&&this.validatePaymentId(e)}catch(t){u.Wb.logError(t,this.moduleId,"className","keyDownOnDateEventHandler",this.errorLocation)}},e.prototype.validatePaymentId=function(l){l?(this.paymentRequestId=l,this.onLoad(null)):this.swtAlert.error("Please enter a valid Id")},e.prototype.enableDisableButtons=function(l){this.logButton.enabled=l,this.messageButton.enabled=l,this.messageOutButton.enabled=l,this.stopButton.enabled=l,this.spreadButton.enabled=l},e.prototype.resetTextInputsColor=function(){this.waitingDateLabel.color="#173553",this.stoppedtimeLabel.color="#173553",this.stoppedUserLabel.color="#173553",this.stoppedReasonLabel.color="#173553",this.blockedtimeLabel.color="#173553",this.blockedUserLabel.color="#173553",this.blockedReasonLabel.color="#173553",this.cancelledtimeLabel.color="#173553",this.cancelledUserLabel.color="#173553",this.cancelledReasonLabel.color="#173553",this.releaseDateLabel.color="#173553",this.stoppedLabel.color="#173553",this.unstoppedLabel.color="#173553",this.blockedLabel.color="#173553",this.cancelledLabel.color="#173553",this.releaseLabel.color="#173553",this.waitingLabel.color="#173553",this.releasedSourceLabel.color="#173553",this.releasedByLabel.color="#173553"},e.prototype.doReleasePaymentEventHandler=function(l){var e=this;l.detail===u.c.YES&&(this.actionMethod="method=unStopReleasePayment",this.actionPath="paymentDisplayPCM.do?",this.inputData.cbResult=function(l){e.inputDataResultFromChange(l)},"B"!=this.lastActionValue&&"S"!=this.lastActionValue&&"W"!=this.lastActionValue||(this.requestParams.paymentId=this.paymentRequestId.toString(),this.requestParams.previousStatus=this.lastActionValue),this.requestParams.paymentAction="R",this.requestParams.location=this.location,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},e.prototype.releasePayment=function(l){try{this.blockedReasonLabel.text||"S"==this.lastActionValue?this.checkblockedStoppedPay(this.paymentRequestId):this.swtAlert.confirm("Do you wish to Release this payment?","",u.c.YES|u.c.NO,null,this.doReleasePaymentEventHandler.bind(this))}catch(e){u.Wb.logError(e,this.moduleId,"ClassName","doDeletePCStopRule",this.errorLocation)}},e.prototype.checkblockedStoppedPay=function(l){var e=this;this.requestParams=[],this.actionMethod="method=checkBlockedStoppedPay",this.actionPath="paymentDisplayPCM.do?",this.logicUpdate.cbResult=function(l){e.isBlockedStoppedResult(l)},this.requestParams.paymentId=l?l.toString():"",this.requestParams.location=this.location,this.logicUpdate.cbStart=this.startOfComms.bind(this),this.logicUpdate.cbStop=this.endOfComms.bind(this),this.logicUpdate.encodeURL=!1,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams)},e.prototype.isBlockedStoppedResult=function(l){try{var e="",t="",n=void 0,i=[],a=[],b=[],o=u.Wb.getPredictMessage("dashboardDetails.alert.releaseBlockedPay",null),r=u.Wb.getPredictMessage("dashboardDetails.alert.lockedPay",null),d=u.Wb.getPredictMessage("dashboardDetails.paymentStopped",null),s=u.Wb.getPredictMessage("dashboardDetails.paymentWasStopped",null),h=u.Wb.getPredictMessage("dashboardDetails.until",null),c=u.Wb.getPredictMessage("dashboardDetails.wishContinue",null);if(this.logicUpdate.isBusy())this.logicUpdate.cbStop();else{var g=l,m=new u.L;if(m.setInputJSON(g),"ARCHIVE_LOCATION"!==m.getRequestReplyMessage()||"INCORRECT_PREVIOUS_STATUS"!==m.getRequestReplyMessage()){n=m.getSingletons().payIdRule.replace("{","").replace("}","");var w=[];if(n){w.push(n.split("#")),w=w[0];for(var p=0;p<w.length;p++)i.push(w[p].split("|")[0]),a.push(w[p].split("|")[1]),b.push(w[p].split("|")[2]);for(var L=0;L<i.length;L++)"Y"==i[L]&&null!=a[L]?t=t+d+" "+a[L]+"<br/>":"P"==i[L]&&null!=a[L]&&null!=b[L]?t=t+s+" "+a[L]+" "+h+" "+b[L]+"<br/>":"N"==i[L]?(e=o,this.swtAlert.confirm(e,"",u.c.YES|u.c.CANCEL,null,this.doReleasePaymentEventHandler.bind(this))):"L"==i[L]&&(e=r,this.swtAlert.warning(e));""!=t&&(t+=c,"S"==this.lastActionValue?this.swtAlert.confirm(t,"",u.c.YES|u.c.CANCEL,null,this.doReleasePaymentEventHandler.bind(this)):this.swtAlert.confirm(t,"",u.c.YES|u.c.CANCEL,null,this.secondConfirmRelease.bind(this)))}}else console.log("error:   ",this.jsonReaderMethod.getRequestReplyMessage())}}catch(f){}},e.prototype.secondConfirmRelease=function(l){var e=null;l.detail==u.c.YES&&(e="This payment is blocked. Do you wish to release ?",this.swtAlert.confirm(e,"",u.c.YES|u.c.CANCEL,null,this.doReleasePaymentEventHandler.bind(this)))},e.prototype.unStopPayEventHandler=function(l){var e=this;l.detail===u.c.YES&&(this.actionMethod="method=unStopReleasePayment",this.actionPath="paymentDisplayPCM.do?",this.inputData.cbResult=function(l){e.inputDataResultFromChange(l)},"S"==this.lastActionValue&&(this.requestParams.paymentId=this.paymentRequestId,this.requestParams.previousStatus=this.lastActionValue),this.requestParams.paymentAction="U",this.requestParams.location=this.location,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},e.prototype.unStopPay=function(l){try{this.swtAlert.confirm("Do you wish to Unstop this payment?","",u.c.YES|u.c.NO,null,this.unStopPayEventHandler.bind(this))}catch(e){u.Wb.logError(e,this.moduleId,"ClassName","doDeletePCStopRule",this.errorLocation)}},e.prototype.spreadDisplay=function(l){try{this.jsonReader.getSingletons().accountGroupSpreadId?u.x.call("openChildSpreadProfileAdd","spreadProfilesView"):this.swtAlert.warning("the selected Account Group is not provided with Spread Profile ID")}catch(e){console.log("eeeeee",e)}},e.prototype.changeCategory=function(l){},e.prototype.refreshParent=function(l){var e=this;this.requestParams=[],this.actionMethod="method=updatePaymentCategory",this.actionPath="dashboardPCM.do?",this.inputData.cbResult=function(l){e.inputDataResultFromChange(l)},this.requestParams.paymentId=this.paymentRequestId,this.requestParams.categoryId=l,this.requestParams.method="updateCategory",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.startOfComms=function(){},e.prototype.endOfComms=function(){},e.prototype.keyDownEventHandler=function(l){try{var e=Object(u.ic.getFocus()).name;l.keyCode===u.N.ENTER&&("addButton"===e||"viewButton"===e||("closeButton"===e?this.closeCurrentTab(l):"helpIcon"===e&&this.doHelp()))}catch(t){u.Wb.logError(t,this.moduleId,"ClassName","keyDownEventHandler",this.errorLocation)}},e.prototype.doHelp=function(){try{u.x.call("help")}catch(l){u.Wb.logError(l,this.moduleId,"ClassName","doHelp",this.errorLocation)}},e.prototype.closeCurrentTab=function(l){try{this.dispose()}catch(e){u.Wb.logError(e,u.Wb.SYSTEM_MODULE_ID,"ClassName","refreshGrid",this.errorLocation)}},e.prototype.dispose=function(){try{this.requestParams=null,this.inputData=null,this.jsonReader=null,this.lastRecievedJSON=null,this.prevRecievedJSON=null,u.x.call("close"),this.titleWindow?this.close():window.close()}catch(l){u.Wb.logError(l,this.moduleId,"ClassName","dispose",this.errorLocation)}},e}(u.yb),r=[{path:"",component:o}],d=(a.l.forChild(r),function(){return function(){}}()),s=t("pMnS"),h=t("RChO"),c=t("t6HQ"),g=t("WFGK"),m=t("5FqG"),w=t("Ip0R"),p=t("gIcY"),L=t("t/Na"),f=t("sE5F"),I=t("OzfB"),S=t("T7CS"),x=t("S7LP"),y=t("6aHO"),R=t("WzUx"),v=t("A7o+"),J=t("zCE2"),W=t("Jg5P"),C=t("3R0m"),B=t("hhbb"),D=t("5rxC"),A=t("Fzqc"),Y=t("21Lb"),T=t("hUWP"),k=t("3pJQ"),G=t("V9q+"),P=t("VDKW"),N=t("kXfT"),j=t("BGbe");t.d(e,"PaymentRequestDisplayModuleNgFactory",function(){return Z}),t.d(e,"RenderType_PaymentRequestDisplay",function(){return O}),t.d(e,"View_PaymentRequestDisplay_0",function(){return H}),t.d(e,"View_PaymentRequestDisplay_Host_0",function(){return M}),t.d(e,"PaymentRequestDisplayNgFactory",function(){return U});var Z=n.Gb(d,[],function(l){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[s.a,h.a,c.a,g.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,U]],[3,n.n],n.J]),n.Rb(4608,w.m,w.l,[n.F,[2,w.u]]),n.Rb(4608,p.c,p.c,[]),n.Rb(4608,p.p,p.p,[]),n.Rb(4608,L.j,L.p,[w.c,n.O,L.n]),n.Rb(4608,L.q,L.q,[L.j,L.o]),n.Rb(5120,L.a,function(l){return[l,new u.tb]},[L.q]),n.Rb(4608,L.m,L.m,[]),n.Rb(6144,L.k,null,[L.m]),n.Rb(4608,L.i,L.i,[L.k]),n.Rb(6144,L.b,null,[L.i]),n.Rb(4608,L.f,L.l,[L.b,n.B]),n.Rb(4608,L.c,L.c,[L.f]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.g,f.b,[]),n.Rb(5120,f.i,f.j,[]),n.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),n.Rb(4608,f.f,f.a,[]),n.Rb(5120,f.d,f.k,[f.h,f.f]),n.Rb(5120,n.b,function(l,e){return[I.j(l,e)]},[w.c,n.O]),n.Rb(4608,S.a,S.a,[]),n.Rb(4608,x.a,x.a,[]),n.Rb(4608,y.a,y.a,[n.n,n.L,n.B,x.a,n.g]),n.Rb(4608,R.c,R.c,[n.n,n.g,n.B]),n.Rb(4608,R.e,R.e,[R.c]),n.Rb(4608,v.l,v.l,[]),n.Rb(4608,v.h,v.g,[]),n.Rb(4608,v.c,v.f,[]),n.Rb(4608,v.j,v.d,[]),n.Rb(4608,v.b,v.a,[]),n.Rb(4608,v.k,v.k,[v.l,v.h,v.c,v.j,v.b,v.m,v.n]),n.Rb(4608,R.i,R.i,[[2,v.k]]),n.Rb(4608,R.r,R.r,[R.L,[2,v.k],R.i]),n.Rb(4608,R.t,R.t,[]),n.Rb(4608,R.w,R.w,[]),n.Rb(**********,a.l,a.l,[[2,a.r],[2,a.k]]),n.Rb(**********,w.b,w.b,[]),n.Rb(**********,p.n,p.n,[]),n.Rb(**********,p.l,p.l,[]),n.Rb(**********,J.a,J.a,[]),n.Rb(**********,W.a,W.a,[]),n.Rb(**********,p.e,p.e,[]),n.Rb(**********,C.a,C.a,[]),n.Rb(**********,v.i,v.i,[]),n.Rb(**********,R.b,R.b,[]),n.Rb(**********,L.e,L.e,[]),n.Rb(**********,L.d,L.d,[]),n.Rb(**********,f.e,f.e,[]),n.Rb(**********,B.b,B.b,[]),n.Rb(**********,D.b,D.b,[]),n.Rb(**********,I.c,I.c,[]),n.Rb(**********,A.a,A.a,[]),n.Rb(**********,Y.d,Y.d,[]),n.Rb(**********,T.c,T.c,[]),n.Rb(**********,k.a,k.a,[]),n.Rb(**********,G.a,G.a,[[2,I.g],n.O]),n.Rb(**********,P.b,P.b,[]),n.Rb(**********,N.a,N.a,[]),n.Rb(**********,j.b,j.b,[]),n.Rb(**********,u.Tb,u.Tb,[]),n.Rb(**********,d,d,[]),n.Rb(256,L.n,"XSRF-TOKEN",[]),n.Rb(256,L.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,v.m,void 0,[]),n.Rb(256,v.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,a.i,function(){return[[{path:"",component:o}]]},[])])}),E=[[""]],O=n.Hb({encapsulation:0,styles:E,data:{}});function H(l){return n.dc(0,[n.Zb(*********,1,{_container:0}),n.Zb(*********,2,{paymentIdTextInput:0}),n.Zb(*********,3,{lastActionLabel:0}),n.Zb(*********,4,{accountidLabel:0}),n.Zb(*********,5,{paymentIdLabel:0}),n.Zb(*********,6,{accountwithinstnameLabel:0}),n.Zb(*********,7,{accountwithinstaccountLabel:0}),n.Zb(*********,8,{accountwithinstbicLabel:0}),n.Zb(*********,9,{actionLabel:0}),n.Zb(*********,10,{amountLabel:0}),n.Zb(*********,11,{boreferenceLabel:0}),n.Zb(*********,12,{beneficiarycustnameLabel:0}),n.Zb(*********,13,{beneficiarycustaccountLabel:0}),n.Zb(*********,14,{beneficiarycustbicLabel:0}),n.Zb(*********,15,{beneficiaryinstnameLabel:0}),n.Zb(*********,16,{beneficiaryinstaccountLabel:0}),n.Zb(*********,17,{beneficiaryinstbicLabel:0}),n.Zb(*********,18,{currencycodeLabel:0}),n.Zb(*********,19,{delivererscustodianaccountLabel:0}),n.Zb(*********,20,{delivererscustodianbicLabel:0}),n.Zb(*********,21,{delivererscustodiannameLabel:0}),n.Zb(*********,22,{deliveryagentaccountLabel:0}),n.Zb(*********,23,{deliveryagentbicLabel:0}),n.Zb(*********,24,{deliveryagentnameLabel:0}),n.Zb(*********,25,{departmentLabel:0}),n.Zb(*********,26,{entityidLabel:0}),n.Zb(*********,27,{messageLabel:0}),n.Zb(*********,28,{gpiLabel:0}),n.Zb(*********,29,{inputdateLabel:0}),n.Zb(*********,30,{intermediaryaccountLabel:0}),n.Zb(*********,31,{intermediarybicLabel:0}),n.Zb(*********,32,{intermediarynameLabel:0}),n.Zb(*********,33,{messagetypeLabel:0}),n.Zb(*********,34,{orderingcustaccountLabel:0}),n.Zb(*********,35,{orderingcustbeiLabel:0}),n.Zb(*********,36,{orderingcustnameLabel:0}),n.Zb(*********,37,{orderinginstaccountLabel:0}),n.Zb(*********,38,{orderinginstbicLabel:0}),n.Zb(*********,39,{orderinginstnameLabel:0}),n.Zb(*********,40,{paymenttypeLabel:0}),n.Zb(*********,41,{placeofsettlementaccountLabel:0}),n.Zb(*********,42,{placeofsettlementbicLabel:0}),n.Zb(*********,43,{placeofsettlementnameLabel:0}),n.Zb(*********,44,{receiverbicLabel:0}),n.Zb(*********,45,{receiverscorresnameLabel:0}),n.Zb(*********,46,{receiverscorresaccountLabel:0}),n.Zb(*********,47,{receiverscorresbicLabel:0}),n.Zb(*********,48,{paymentreferenceLabel:0}),n.Zb(*********,49,{relatedreferenceLabel:0}),n.Zb(*********,50,{selleraccountLabel:0}),n.Zb(*********,51,{sellerbicLabel:0}),n.Zb(*********,52,{sellernameLabel:0}),n.Zb(*********,53,{senderbicLabel:0}),n.Zb(*********,54,{senderscorresnameLabel:0}),n.Zb(*********,55,{senderreceiverinfoLabel:0}),n.Zb(*********,56,{senderscorresaccountLabel:0}),n.Zb(*********,57,{senderscorresbicLabel:0}),n.Zb(*********,58,{sourceidLabel:0}),n.Zb(*********,59,{releasedByLabel:0}),n.Zb(*********,60,{sourceurgencyindicatorLabel:0}),n.Zb(*********,61,{entitysubidLabel:0}),n.Zb(*********,62,{thirdreimbsmntaccountLabel:0}),n.Zb(*********,63,{thirdreimbsmntbicLabel:0}),n.Zb(*********,64,{thirdreimbsmntnameLabel:0}),n.Zb(*********,65,{valuedateLabel:0}),n.Zb(*********,66,{releaseDateLabel:0}),n.Zb(*********,67,{waitingDateLabel:0}),n.Zb(*********,68,{reqreleasetimeLabel:0}),n.Zb(*********,69,{categoryRulelabel:0}),n.Zb(*********,70,{categoryIDlabel:0}),n.Zb(*********,71,{accountGroupLabel:0}),n.Zb(*********,72,{accountGroupNameLabel:0}),n.Zb(*********,73,{cutOffTimeLabel:0}),n.Zb(*********,74,{accounttypeLabel:0}),n.Zb(*********,75,{urgentLabel:0}),n.Zb(*********,76,{foreferenceLabel:0}),n.Zb(*********,77,{sourcereferenceLabel:0}),n.Zb(*********,78,{stoppedtimeLabel:0}),n.Zb(*********,79,{stoppedUserLabel:0}),n.Zb(*********,80,{stoppedReasonLabel:0}),n.Zb(*********,81,{unstoppedtimeLabel:0}),n.Zb(*********,82,{unstoppedUserLabel:0}),n.Zb(*********,83,{unstoppedReasonLabel:0}),n.Zb(*********,84,{blockedtimeLabel:0}),n.Zb(*********,85,{blockedUserLabel:0}),n.Zb(*********,86,{blockedReasonLabel:0}),n.Zb(*********,87,{cancelledtimeLabel:0}),n.Zb(*********,88,{cancelledUserLabel:0}),n.Zb(*********,89,{cancelledReasonLabel:0}),n.Zb(*********,90,{releasedSourceLabel:0}),n.Zb(*********,91,{paymentMessageArea:0}),n.Zb(*********,92,{stoppedLabel:0}),n.Zb(*********,93,{unstoppedLabel:0}),n.Zb(*********,94,{blockedLabel:0}),n.Zb(*********,95,{cancelledLabel:0}),n.Zb(*********,96,{releaseLabel:0}),n.Zb(*********,97,{waitingLabel:0}),n.Zb(*********,98,{logButton:0}),n.Zb(*********,99,{messageButton:0}),n.Zb(*********,100,{messageOutButton:0}),n.Zb(*********,101,{stopButton:0}),n.Zb(*********,102,{closeButton:0}),n.Zb(*********,103,{releaseButton:0}),n.Zb(*********,104,{changeCatgButton:0}),n.Zb(*********,105,{spreadButton:0}),n.Zb(*********,106,{unStopButton:0}),n.Zb(*********,107,{timeFrameRadioGroup:0}),n.Zb(*********,108,{radioC:0}),n.Zb(*********,109,{radioE:0}),n.Zb(*********,110,{radioS:0}),(l()(),n.Jb(110,0,null,null,679,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(l,e,t){var n=!0,i=l.component;"creationComplete"===e&&(n=!1!==i.onLoad(t)&&n);return n},m.ad,m.hb)),n.Ib(111,4440064,null,0,u.yb,[n.r,u.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(l()(),n.Jb(112,0,null,0,677,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(113,4440064,null,0,u.ec,[n.r,u.i,n.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(l()(),n.Jb(114,0,null,0,52,"SwtCanvas",[["height","9%"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(115,4440064,null,0,u.db,[n.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(116,0,null,0,50,"HBox",[["height","100%"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(117,4440064,null,0,u.C,[n.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(118,0,null,0,15,"VBox",[["height","100%"],["width","30%"]],null,null,null,m.od,m.vb)),n.Ib(119,4440064,null,0,u.ec,[n.r,u.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(120,0,null,0,5,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(121,4440064,null,0,u.C,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(122,0,null,0,1,"SwtLabel",[["text","Account Group"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(123,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(124,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","accountGroupLabel"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(125,4440064,[[71,4],["accountGroupLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(l()(),n.Jb(126,0,null,0,7,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(127,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(128,0,null,0,1,"SwtLabel",[["text","Payment Req*"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(129,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(130,0,null,0,1,"SwtTextInput",[["id","paymentIdTextInput"],["maxChars","20"],["restrict","0-9"],["width","150"]],null,[[null,"keydown"]],function(l,e,t){var n=!0,i=l.component;"keydown"===e&&(n=!1!==i.keyDownOnPaymentRequestEventHandler(t)&&n);return n},m.kd,m.sb)),n.Ib(131,4440064,[[2,4],["paymentIdTextInput",4]],0,u.Rb,[n.r,u.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],width:[3,"width"]},null),(l()(),n.Jb(132,0,null,0,1,"SwtLabel",[["id","paymentIdLabel"],["maxChars","20"]],null,null,null,m.Yc,m.fb)),n.Ib(133,4440064,[[5,4],["paymentIdLabel",4]],0,u.vb,[n.r,u.i],{maxChars:[0,"maxChars"],id:[1,"id"]},null),(l()(),n.Jb(134,0,null,0,11,"VBox",[["height","100%"],["width","35%"]],null,null,null,m.od,m.vb)),n.Ib(135,4440064,null,0,u.ec,[n.r,u.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(136,0,null,0,9,"VBox",[],null,null,null,m.od,m.vb)),n.Ib(137,4440064,null,0,u.ec,[n.r,u.i,n.T],null,null),(l()(),n.Jb(138,0,null,0,3,"HBox",[["width","50%"]],null,null,null,m.Dc,m.K)),n.Ib(139,4440064,null,0,u.C,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(140,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","accountGroupNameLabel"],["paddingLeft","20"]],null,null,null,m.Yc,m.fb)),n.Ib(141,4440064,[[72,4],["accountGroupNameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(l()(),n.Jb(142,0,null,0,3,"HBox",[["width","50%"]],null,null,null,m.Dc,m.K)),n.Ib(143,4440064,null,0,u.C,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(144,0,null,0,1,"SwtLabel",[["color","green"],["fontWeight","normal"],["id","lastActionLabel"],["paddingLeft","20"]],null,null,null,m.Yc,m.fb)),n.Ib(145,4440064,[[3,4],["lastActionLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"],color:[3,"color"]},null),(l()(),n.Jb(146,0,null,0,20,"VBox",[["height","100%"],["width","35%"]],null,null,null,m.od,m.vb)),n.Ib(147,4440064,null,0,u.ec,[n.r,u.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(148,0,null,0,18,"VBox",[],null,null,null,m.od,m.vb)),n.Ib(149,4440064,null,0,u.ec,[n.r,u.i,n.T],null,null),(l()(),n.Jb(150,0,null,0,3,"HBox",[["width","50%"]],null,null,null,m.Dc,m.K)),n.Ib(151,4440064,null,0,u.C,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(152,0,null,0,1,"SwtLabel",[["paddingLeft","20"],["text",""]],null,null,null,m.Yc,m.fb)),n.Ib(153,4440064,null,0,u.vb,[n.r,u.i],{paddingLeft:[0,"paddingLeft"],text:[1,"text"]},null),(l()(),n.Jb(154,0,null,0,12,"HBox",[["horizontalAlign","right"]],null,null,null,m.Dc,m.K)),n.Ib(155,4440064,null,0,u.C,[n.r,u.i],{horizontalAlign:[0,"horizontalAlign"]},null),(l()(),n.Jb(156,0,null,0,1,"SwtLabel",[["text","TimeFrame"],["width","75"]],null,null,null,m.Yc,m.fb)),n.Ib(157,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(158,0,null,0,8,"SwtRadioButtonGroup",[["align","horizontal"],["id","timeFrameRadioGroup"]],null,[[null,"change"]],function(l,e,t){var n=!0,i=l.component;"change"===e&&(n=!1!==i.onLoad("timeFrame")&&n);return n},m.ed,m.lb)),n.Ib(159,4440064,[[107,4],["timeFrameRadioGroup",4]],1,u.Hb,[L.c,n.r,u.i],{id:[0,"id"],align:[1,"align"]},{change_:"change"}),n.Zb(*********,111,{radioItems:1}),(l()(),n.Jb(161,0,null,0,1,"SwtRadioItem",[["groupName","timeFrameRadioGroup"],["id","radioE"],["label","Entity"],["selected","true"],["value","E"],["width","80"]],null,null,null,m.fd,m.mb)),n.Ib(162,4440064,[[111,4],[109,4],["radioE",4]],0,u.Ib,[n.r,u.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"],selected:[5,"selected"]},null),(l()(),n.Jb(163,0,null,0,1,"SwtRadioItem",[["groupName","timeFrameRadioGroup"],["id","radioC"],["label","Currency"],["value","C"],["width","90"]],null,null,null,m.fd,m.mb)),n.Ib(164,4440064,[[111,4],[108,4],["radioC",4]],0,u.Ib,[n.r,u.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"]},null),(l()(),n.Jb(165,0,null,0,1,"SwtRadioItem",[["groupName","timeFrameRadioGroup"],["id","radioS"],["label","System"],["value","S"],["width","80"]],null,null,null,m.fd,m.mb)),n.Ib(166,4440064,[[111,4],[110,4],["radioS",4]],0,u.Ib,[n.r,u.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"]},null),(l()(),n.Jb(167,0,null,0,594,"SwtCanvas",[["height","85%"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(168,4440064,null,0,u.db,[n.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(169,0,null,0,592,"SwtTabNavigator",[["height","100%"],["width","100%"]],null,null,null,m.id,m.pb)),n.Ib(170,4440064,[["aggAccountNavigator",4]],1,u.Ob,[n.r,u.i,n.k],{width:[0,"width"],height:[1,"height"]},null),n.Zb(*********,112,{tabChildren:1}),(l()(),n.Jb(172,0,null,0,283,"SwtTab",[["height","98%"],["id","general"],["label","General"]],null,null,null,m.nd,m.tb)),n.Ib(173,4440064,[[112,4],["general",4]],0,u.Xb,[n.r,u.i],{id:[0,"id"],height:[1,"height"],label:[2,"label"]},null),(l()(),n.Jb(174,0,null,0,259,"SwtPanel",[["height","93%"],["title","Main Details"],["width","100%"]],null,null,null,m.dd,m.kb)),n.Ib(175,4440064,null,0,u.Cb,[n.r,u.i,n.T],{width:[0,"width"],height:[1,"height"],title:[2,"title"]},null),(l()(),n.Jb(176,0,null,0,257,"VBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["verticalGap","0"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(177,4440064,null,0,u.ec,[n.r,u.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(l()(),n.Jb(178,0,null,0,85,"Grid",[["height","30%"],["width","85%"]],null,null,null,m.Cc,m.H)),n.Ib(179,4440064,null,0,u.z,[n.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(180,0,null,0,13,"GridRow",[["height","15%"]],null,null,null,m.Bc,m.J)),n.Ib(181,4440064,null,0,u.B,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(182,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(183,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(184,0,null,0,1,"SwtLabel",[["text","Entity"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(185,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(186,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","entityidLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(187,4440064,[[26,4],["entityidLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(188,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(189,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(190,0,null,0,1,"SwtLabel",[["text","GPI"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(191,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(192,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","gpiLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(193,4440064,[[28,4],["gpiLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(194,0,null,0,13,"GridRow",[["height","15%"]],null,null,null,m.Bc,m.J)),n.Ib(195,4440064,null,0,u.B,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(196,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(197,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(198,0,null,0,1,"SwtLabel",[["text","Currency"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(199,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(200,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","currencycodeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(201,4440064,[[18,4],["currencycodeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(202,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(203,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(204,0,null,0,1,"SwtLabel",[["text","Payment Type"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(205,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(206,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","paymenttypeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(207,4440064,[[40,4],["paymenttypeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(208,0,null,0,13,"GridRow",[["height","15%"]],null,null,null,m.Bc,m.J)),n.Ib(209,4440064,null,0,u.B,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(210,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(211,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(212,0,null,0,1,"SwtLabel",[["text","Account ID"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(213,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(214,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","accountidLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(215,4440064,[[4,4],["accountidLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(216,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(217,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(218,0,null,0,1,"SwtLabel",[["text","Cut-Off Time"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(219,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(220,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","cutOffTimeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(221,4440064,[[73,4],["cutOffTimeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(222,0,null,0,13,"GridRow",[["height","15%"]],null,null,null,m.Bc,m.J)),n.Ib(223,4440064,null,0,u.B,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(224,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(225,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(226,0,null,0,1,"SwtLabel",[["text","Type"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(227,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(228,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","accounttypeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(229,4440064,[[74,4],["accounttypeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(230,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(231,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(232,0,null,0,1,"SwtLabel",[["text","Urgent / Spreadable"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(233,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(234,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","urgentLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(235,4440064,[[75,4],["urgentLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(236,0,null,0,13,"GridRow",[["height","15%"]],null,null,null,m.Bc,m.J)),n.Ib(237,4440064,null,0,u.B,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(238,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(239,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(240,0,null,0,1,"SwtLabel",[["text","Category ID"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(241,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(242,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","categoryIDlabel"]],null,null,null,m.Yc,m.fb)),n.Ib(243,4440064,[[70,4],["categoryIDlabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(244,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(245,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(246,0,null,0,1,"SwtLabel",[["text","Category Name"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(247,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(248,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","categoryRulelabel"]],null,null,null,m.Yc,m.fb)),n.Ib(249,4440064,[[69,4],["categoryRulelabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(250,0,null,0,13,"GridRow",[["height","15%"]],null,null,null,m.Bc,m.J)),n.Ib(251,4440064,null,0,u.B,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(252,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(253,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(254,0,null,0,1,"SwtLabel",[["text","Value Date"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(255,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(256,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","valuedateLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(257,4440064,[[65,4],["valuedateLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(258,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(259,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(260,0,null,0,1,"SwtLabel",[["text","Amount"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(261,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(262,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","amountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(263,4440064,[[10,4],["amountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(264,0,null,0,169,"VBox",[["height","70%"],["paddingTop","5"],["styleName","vBoxWithBorders"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(265,4440064,null,0,u.ec,[n.r,u.i,n.T],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"]},null),(l()(),n.Jb(266,0,null,0,167,"Grid",[["borderColor","black"],["borderStyle","solid"],["borderThickness","1"],["paddingLeft","5"],["width","85%"]],null,null,null,m.Cc,m.H)),n.Ib(267,4440064,null,0,u.z,[n.r,u.i],{borderThickness:[0,"borderThickness"],borderStyle:[1,"borderStyle"],borderColor:[2,"borderColor"],width:[3,"width"],paddingLeft:[4,"paddingLeft"]},null),(l()(),n.Jb(268,0,null,0,9,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(269,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(270,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(271,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(272,0,null,0,1,"SwtLabel",[["styleName","underlineText"],["text","Payment Status"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(273,4440064,null,0,u.vb,[n.r,u.i],{styleName:[0,"styleName"],width:[1,"width"],text:[2,"text"]},null),(l()(),n.Jb(274,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(275,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(276,0,null,0,1,"SwtLabel",[["styleName","underlineText"],["text","Payment References"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(277,4440064,null,0,u.vb,[n.r,u.i],{styleName:[0,"styleName"],width:[1,"width"],text:[2,"text"]},null),(l()(),n.Jb(278,0,null,0,13,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(279,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(280,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(281,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(282,0,null,0,1,"SwtLabel",[["id","releaseLabel"],["text","RELEASED"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(283,4440064,[[96,4],["releaseLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],text:[2,"text"]},null),(l()(),n.Jb(284,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","releaseDateLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(285,4440064,[[66,4],["releaseDateLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(286,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(287,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(288,0,null,0,1,"SwtLabel",[["text","Payment Ref."],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(289,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(290,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","paymentreferenceLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(291,4440064,[[48,4],["paymentreferenceLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(292,0,null,0,13,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(293,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(294,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(295,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(296,0,null,0,1,"SwtLabel",[["id","releasedSourceLabel"],["text","By"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(297,4440064,[[90,4],["releasedSourceLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],text:[2,"text"]},null),(l()(),n.Jb(298,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","releasedByLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(299,4440064,[[59,4],["releasedByLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(300,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(301,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(302,0,null,0,1,"SwtLabel",[["text","Related Ref."],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(303,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(304,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","relatedreferenceLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(305,4440064,[[49,4],["relatedreferenceLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(306,0,null,0,13,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(307,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(308,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(309,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(310,0,null,0,1,"SwtLabel",[["id","waitingLabel"],["text","WAITING"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(311,4440064,[[97,4],["waitingLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],text:[2,"text"]},null),(l()(),n.Jb(312,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","waitingDateLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(313,4440064,[[67,4],["waitingDateLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(314,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(315,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(316,0,null,0,1,"SwtLabel",[["text","Source Ref."],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(317,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(318,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","sourcereferenceLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(319,4440064,[[77,4],["sourcereferenceLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(320,0,null,0,13,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(321,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(322,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(323,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(324,0,null,0,1,"SwtLabel",[["text","Required Released"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(325,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(326,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","reqreleasetimeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(327,4440064,[[68,4],["reqreleasetimeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(328,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(329,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(330,0,null,0,1,"SwtLabel",[["text","FO Ref."],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(331,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(332,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","foreferenceLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(333,4440064,[[76,4],["foreferenceLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(334,0,null,0,11,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(335,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(336,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(337,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(338,0,null,0,1,"SwtLabel",[["text","Cancelled"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(339,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(340,0,null,0,5,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(341,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(342,0,null,0,1,"SwtLabel",[["text","BO Ref."],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(343,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(344,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","boreferenceLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(345,4440064,[[11,4],["boreferenceLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(346,0,null,0,7,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(347,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(348,0,null,0,1,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(349,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(350,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(351,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(352,0,null,0,1,"SwtLabel",[["styleName","underlineText"],["text","Reason"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(353,4440064,null,0,u.vb,[n.r,u.i],{styleName:[0,"styleName"],width:[1,"width"],text:[2,"text"]},null),(l()(),n.Jb(354,0,null,0,19,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(355,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(356,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(357,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(358,0,null,0,3,"GridItem",[["width","39%"]],null,null,null,m.Ac,m.I)),n.Ib(359,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(360,0,null,0,1,"SwtLabel",[["id","stoppedLabel"],["text","Stopped"]],null,null,null,m.Yc,m.fb)),n.Ib(361,4440064,[[92,4],["stoppedLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],text:[1,"text"]},null),(l()(),n.Jb(362,0,null,0,3,"GridItem",[["width","33%"]],null,null,null,m.Ac,m.I)),n.Ib(363,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(364,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","stoppedtimeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(365,4440064,[[78,4],["stoppedtimeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(366,0,null,0,3,"GridItem",[["width","33%"]],null,null,null,m.Ac,m.I)),n.Ib(367,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(368,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","stoppedUserLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(369,4440064,[[79,4],["stoppedUserLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(370,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(371,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(372,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","stoppedReasonLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(373,4440064,[[80,4],["stoppedReasonLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(374,0,null,0,19,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(375,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(376,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(377,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(378,0,null,0,3,"GridItem",[["width","39%"]],null,null,null,m.Ac,m.I)),n.Ib(379,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(380,0,null,0,1,"SwtLabel",[["id","unstoppedLabel"],["text","Unstopped"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(381,4440064,[[93,4],["unstoppedLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],text:[2,"text"]},null),(l()(),n.Jb(382,0,null,0,3,"GridItem",[["width","33%"]],null,null,null,m.Ac,m.I)),n.Ib(383,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(384,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","unstoppedtimeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(385,4440064,[[81,4],["unstoppedtimeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(386,0,null,0,3,"GridItem",[["width","33%"]],null,null,null,m.Ac,m.I)),n.Ib(387,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(388,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","unstoppedUserLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(389,4440064,[[82,4],["unstoppedUserLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(390,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(391,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(392,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","unstoppedReasonLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(393,4440064,[[83,4],["unstoppedReasonLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(394,0,null,0,19,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(395,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(396,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(397,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(398,0,null,0,3,"GridItem",[["width","39%"]],null,null,null,m.Ac,m.I)),n.Ib(399,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(400,0,null,0,1,"SwtLabel",[["id","blockedLabel"],["text","Blocked"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(401,4440064,[[94,4],["blockedLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],text:[2,"text"]},null),(l()(),n.Jb(402,0,null,0,3,"GridItem",[["width","33%"]],null,null,null,m.Ac,m.I)),n.Ib(403,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(404,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","blockedtimeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(405,4440064,[[84,4],["blockedtimeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(406,0,null,0,3,"GridItem",[["width","33%"]],null,null,null,m.Ac,m.I)),n.Ib(407,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(408,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","blockedUserLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(409,4440064,[[85,4],["blockedUserLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(410,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(411,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(412,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","blockedReasonLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(413,4440064,[[86,4],["blockedReasonLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(414,0,null,0,19,"GridRow",[],null,null,null,m.Bc,m.J)),n.Ib(415,4440064,null,0,u.B,[n.r,u.i],null,null),(l()(),n.Jb(416,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(417,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(418,0,null,0,3,"GridItem",[["width","39%"]],null,null,null,m.Ac,m.I)),n.Ib(419,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(420,0,null,0,1,"SwtLabel",[["id","cancelledLabel"],["text","Cancelled"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(421,4440064,[[95,4],["cancelledLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],text:[2,"text"]},null),(l()(),n.Jb(422,0,null,0,3,"GridItem",[["width","33%"]],null,null,null,m.Ac,m.I)),n.Ib(423,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(424,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","cancelledtimeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(425,4440064,[[87,4],["cancelledtimeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(426,0,null,0,3,"GridItem",[["width","33%"]],null,null,null,m.Ac,m.I)),n.Ib(427,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(428,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","cancelledUserLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(429,4440064,[[88,4],["cancelledUserLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(430,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,m.Ac,m.I)),n.Ib(431,4440064,null,0,u.A,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(432,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","cancelledReasonLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(433,4440064,[[89,4],["cancelledReasonLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(434,0,null,0,21,"SwtPanel",[["height","7%"],["title","Original Message"],["width","100%"]],null,null,null,m.dd,m.kb)),n.Ib(435,4440064,null,0,u.Cb,[n.r,u.i,n.T],{width:[0,"width"],height:[1,"height"],title:[2,"title"]},null),(l()(),n.Jb(436,0,null,0,19,"VBox",[["paddingLeft","5"],["verticalGap","1"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(437,4440064,null,0,u.ec,[n.r,u.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(l()(),n.Jb(438,0,null,0,17,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(439,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(440,0,null,0,1,"SwtLabel",[["text","Source"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(441,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(442,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","sourceidLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(443,4440064,[[58,4],["sourceidLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(444,0,null,0,1,"spacer",[["width","50"]],null,null,null,m.Kc,m.R)),n.Ib(445,4440064,null,0,u.Y,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(446,0,null,0,1,"SwtLabel",[["text","Message Type"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(447,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(448,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","messagetypeLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(449,4440064,[[33,4],["messagetypeLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(450,0,null,0,1,"spacer",[["width","50"]],null,null,null,m.Kc,m.R)),n.Ib(451,4440064,null,0,u.Y,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(452,0,null,0,1,"SwtLabel",[["text","Message"],["width","150"]],null,null,null,m.Yc,m.fb)),n.Ib(453,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(454,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","messageLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(455,4440064,[[27,4],["messageLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(456,0,null,0,47,"SwtTab",[["id","moreInfo"],["label","Add\u2019l Info"]],null,null,null,m.nd,m.tb)),n.Ib(457,4440064,[[112,4],["moreInfo",4]],0,u.Xb,[n.r,u.i],{id:[0,"id"],label:[1,"label"]},null),(l()(),n.Jb(458,0,null,0,45,"SwtCanvas",[["height","100%"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(459,4440064,null,0,u.db,[n.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(460,0,null,0,43,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(461,4440064,null,0,u.ec,[n.r,u.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(l()(),n.Jb(462,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(463,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(464,0,null,0,1,"SwtLabel",[["text","Action"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(465,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(466,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","actionLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(467,4440064,[[9,4],["actionLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(468,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(469,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(470,0,null,0,1,"SwtLabel",[["text","Input Date"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(471,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(472,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","inputdateLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(473,4440064,[[29,4],["inputdateLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(474,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(475,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(476,0,null,0,1,"SwtLabel",[["text","Department"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(477,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(478,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","departmentLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(479,4440064,[[25,4],["departmentLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(480,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(481,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(482,0,null,0,1,"SwtLabel",[["text","Source Urgency Indicator"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(483,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(484,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","sourceurgencyindicatorLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(485,4440064,[[60,4],["sourceurgencyindicatorLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(486,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(487,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(488,0,null,0,1,"SwtLabel",[["text","Sub Entity Id"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(489,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(490,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","entitysubidLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(491,4440064,[[61,4],["entitysubidLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(492,0,null,0,5,"HBox",[["height","20%"]],null,null,null,m.Dc,m.K)),n.Ib(493,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(494,0,null,0,1,"SwtLabel",[["text","Sender Receiver Info"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(495,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(496,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","senderreceiverinfoLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(497,4440064,[[55,4],["senderreceiverinfoLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(498,0,null,0,5,"VBox",[["height","350"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(499,4440064,null,0,u.ec,[n.r,u.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(500,0,null,0,1,"SwtLabel",[["text","Payment Message"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(501,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(502,0,null,0,1,"SwtTextArea",[["editable","false"],["height","100%"],["id","paymentMessageArea"],["width","100%"]],null,null,null,m.jd,m.rb)),n.Ib(503,4440064,[[91,4],["paymentMessageArea",4]],0,u.Qb,[n.r,u.i,n.L],{id:[0,"id"],width:[1,"width"],height:[2,"height"],editable:[3,"editable"]},null),(l()(),n.Jb(504,0,null,0,143,"SwtTab",[["id","parties"],["label","Parties"]],null,null,null,m.nd,m.tb)),n.Ib(505,4440064,[[112,4],["parties",4]],0,u.Xb,[n.r,u.i],{id:[0,"id"],label:[1,"label"]},null),(l()(),n.Jb(506,0,null,0,141,"SwtCanvas",[["height","100%"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(507,4440064,null,0,u.db,[n.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(508,0,null,0,139,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(509,4440064,null,0,u.ec,[n.r,u.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(l()(),n.Jb(510,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(511,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(512,0,null,0,1,"SwtLabel",[["text","Sender BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(513,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(514,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","senderbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(515,4440064,[[53,4],["senderbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(516,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(517,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(518,0,null,0,1,"SwtLabel",[["text","Receiver BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(519,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(520,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","receiverbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(521,4440064,[[44,4],["receiverbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(522,0,null,0,5,"HBox",[["height","5%"],["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(523,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(l()(),n.Jb(524,0,null,0,1,"SwtLabel",[["text","Ordering Customer BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(525,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(526,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","orderingcustbeiLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(527,4440064,[[35,4],["orderingcustbeiLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(528,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(529,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(530,0,null,0,1,"SwtLabel",[["text","Ordering Customer Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(531,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(532,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","orderingcustaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(533,4440064,[[34,4],["orderingcustaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(534,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(535,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(536,0,null,0,1,"SwtLabel",[["text","Ordering Customer Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(537,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(538,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","orderingcustnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(539,4440064,[[36,4],["orderingcustnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(540,0,null,0,5,"HBox",[["height","5%"],["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(541,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(l()(),n.Jb(542,0,null,0,1,"SwtLabel",[["text","Ordering Institution BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(543,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(544,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","orderinginstbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(545,4440064,[[38,4],["orderinginstbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(546,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(547,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(548,0,null,0,1,"SwtLabel",[["text","Ordering Institution Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(549,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(550,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","orderinginstaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(551,4440064,[[37,4],["orderinginstaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(552,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(553,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(554,0,null,0,1,"SwtLabel",[["text","Ordering Institution Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(555,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(556,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","orderinginstnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(557,4440064,[[39,4],["orderinginstnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(558,0,null,0,5,"HBox",[["height","5%"],["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(559,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(l()(),n.Jb(560,0,null,0,1,"SwtLabel",[["text","Senders Correspondent BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(561,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(562,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","senderscorresbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(563,4440064,[[57,4],["senderscorresbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(564,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(565,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(566,0,null,0,1,"SwtLabel",[["text","Senders Correspondent Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(567,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(568,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","senderscorresaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(569,4440064,[[56,4],["senderscorresaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(570,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(571,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(572,0,null,0,1,"SwtLabel",[["text","Senders Correspondent Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(573,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(574,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","senderscorresnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(575,4440064,[[54,4],["senderscorresnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(576,0,null,0,5,"HBox",[["height","5%"],["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(577,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(l()(),n.Jb(578,0,null,0,1,"SwtLabel",[["text","Receivers Correspondent BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(579,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(580,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","receiverscorresbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(581,4440064,[[47,4],["receiverscorresbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(582,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(583,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(584,0,null,0,1,"SwtLabel",[["text","Receivers Correspondent Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(585,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(586,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","receiverscorresaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(587,4440064,[[46,4],["receiverscorresaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(588,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(589,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(590,0,null,0,1,"SwtLabel",[["text","Receivers Correspondent Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(591,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(592,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","receiverscorresnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(593,4440064,[[45,4],["receiverscorresnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(594,0,null,0,5,"HBox",[["height","5%"],["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(595,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(l()(),n.Jb(596,0,null,0,1,"SwtLabel",[["text","Intermediary BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(597,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(598,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","intermediarybicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(599,4440064,[[31,4],["intermediarybicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(600,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(601,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(602,0,null,0,1,"SwtLabel",[["text","Intermediary Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(603,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(604,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","intermediaryaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(605,4440064,[[30,4],["intermediaryaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(606,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(607,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(608,0,null,0,1,"SwtLabel",[["text","Intermediary Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(609,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(610,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","intermediarynameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(611,4440064,[[32,4],["intermediarynameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(612,0,null,0,5,"HBox",[["height","5%"],["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(613,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(l()(),n.Jb(614,0,null,0,1,"SwtLabel",[["text","Account With Institution BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(615,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(616,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","accountwithinstbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(617,4440064,[[8,4],["accountwithinstbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(618,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(619,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(620,0,null,0,1,"SwtLabel",[["text","Account With Institution Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(621,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(622,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","accountwithinstaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(623,4440064,[[7,4],["accountwithinstaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(624,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(625,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(626,0,null,0,1,"SwtLabel",[["text","Account With Institution Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(627,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(628,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","accountwithinstnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(629,4440064,[[6,4],["accountwithinstnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(630,0,null,0,5,"HBox",[["height","5%"],["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(631,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"],paddingTop:[1,"paddingTop"]},null),(l()(),n.Jb(632,0,null,0,1,"SwtLabel",[["text","Beneficiary Customer BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(633,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(634,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","beneficiarycustbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(635,4440064,[[14,4],["beneficiarycustbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(636,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(637,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(638,0,null,0,1,"SwtLabel",[["text","Beneficiary Customer Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(639,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(640,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","beneficiarycustaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(641,4440064,[[13,4],["beneficiarycustaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(642,0,null,0,5,"HBox",[["height","4%"]],null,null,null,m.Dc,m.K)),n.Ib(643,4440064,null,0,u.C,[n.r,u.i],{height:[0,"height"]},null),(l()(),n.Jb(644,0,null,0,1,"SwtLabel",[["text","Beneficiary Customer Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(645,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(646,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","beneficiarycustnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(647,4440064,[[12,4],["beneficiarycustnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(648,0,null,0,113,"SwtTab",[["id","parties2"],["label","Parties 2"]],null,null,null,m.nd,m.tb)),n.Ib(649,4440064,[[112,4],["parties2",4]],0,u.Xb,[n.r,u.i],{id:[0,"id"],label:[1,"label"]},null),(l()(),n.Jb(650,0,null,0,111,"SwtCanvas",[["height","100%"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(651,4440064,null,0,u.db,[n.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),n.Jb(652,0,null,0,109,"VBox",[["height","100%"],["verticalGap","0"],["width","100%"]],null,null,null,m.od,m.vb)),n.Ib(653,4440064,null,0,u.ec,[n.r,u.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(l()(),n.Jb(654,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(655,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(656,0,null,0,1,"SwtLabel",[["text","Beneficiary Institution BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(657,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(658,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","beneficiaryinstbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(659,4440064,[[17,4],["beneficiaryinstbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(660,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(661,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(662,0,null,0,1,"SwtLabel",[["text","Beneficiary Institution Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(663,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(664,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","beneficiaryinstaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(665,4440064,[[16,4],["beneficiaryinstaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(666,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(667,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(668,0,null,0,1,"SwtLabel",[["text","Beneficiary Institution Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(669,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(670,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","beneficiaryinstnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(671,4440064,[[15,4],["beneficiaryinstnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(672,0,null,0,5,"HBox",[["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(673,4440064,null,0,u.C,[n.r,u.i],{paddingTop:[0,"paddingTop"]},null),(l()(),n.Jb(674,0,null,0,1,"SwtLabel",[["text","Seller BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(675,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(676,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","sellerbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(677,4440064,[[51,4],["sellerbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(678,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(679,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(680,0,null,0,1,"SwtLabel",[["text","Seller Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(681,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(682,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selleraccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(683,4440064,[[50,4],["selleraccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(684,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(685,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(686,0,null,0,1,"SwtLabel",[["text","Seller Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(687,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(688,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","sellernameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(689,4440064,[[52,4],["sellernameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(690,0,null,0,5,"HBox",[["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(691,4440064,null,0,u.C,[n.r,u.i],{paddingTop:[0,"paddingTop"]},null),(l()(),n.Jb(692,0,null,0,1,"SwtLabel",[["text","Deliverers Custodian BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(693,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(694,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","delivererscustodianbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(695,4440064,[[20,4],["delivererscustodianbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(696,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(697,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(698,0,null,0,1,"SwtLabel",[["text","Deliverers Custodian Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(699,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(700,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","delivererscustodianaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(701,4440064,[[19,4],["delivererscustodianaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(702,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(703,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(704,0,null,0,1,"SwtLabel",[["text","Deliverers Custodian Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(705,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(706,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","delivererscustodiannameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(707,4440064,[[21,4],["delivererscustodiannameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(708,0,null,0,5,"HBox",[["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(709,4440064,null,0,u.C,[n.r,u.i],{paddingTop:[0,"paddingTop"]},null),(l()(),n.Jb(710,0,null,0,1,"SwtLabel",[["text","Delivery Agent BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(711,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(712,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","deliveryagentbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(713,4440064,[[23,4],["deliveryagentbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(714,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(715,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(716,0,null,0,1,"SwtLabel",[["text","Delivery Agent Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(717,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(718,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","deliveryagentaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(719,4440064,[[22,4],["deliveryagentaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(720,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(721,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(722,0,null,0,1,"SwtLabel",[["text","Delivery Agent Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(723,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(724,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","deliveryagentnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(725,4440064,[[24,4],["deliveryagentnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(726,0,null,0,5,"HBox",[["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(727,4440064,null,0,u.C,[n.r,u.i],{paddingTop:[0,"paddingTop"]},null),(l()(),n.Jb(728,0,null,0,1,"SwtLabel",[["text","Place Of Settlement BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(729,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(730,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","placeofsettlementbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(731,4440064,[[42,4],["placeofsettlementbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(732,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(733,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(734,0,null,0,1,"SwtLabel",[["text","Place Of Settlement Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(735,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(736,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","placeofsettlementaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(737,4440064,[[41,4],["placeofsettlementaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(738,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(739,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(740,0,null,0,1,"SwtLabel",[["text","Place Of Settlement Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(741,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(742,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","placeofsettlementnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(743,4440064,[[43,4],["placeofsettlementnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(744,0,null,0,5,"HBox",[["paddingTop","8"]],null,null,null,m.Dc,m.K)),n.Ib(745,4440064,null,0,u.C,[n.r,u.i],{paddingTop:[0,"paddingTop"]},null),(l()(),n.Jb(746,0,null,0,1,"SwtLabel",[["text","Third Reimbursement BIC"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(747,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(748,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","thirdreimbsmntbicLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(749,4440064,[[63,4],["thirdreimbsmntbicLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(750,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(751,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(752,0,null,0,1,"SwtLabel",[["text","Third Reimbursement Account"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(753,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(754,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","thirdreimbsmntaccountLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(755,4440064,[[62,4],["thirdreimbsmntaccountLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(756,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),n.Ib(757,4440064,null,0,u.C,[n.r,u.i],null,null),(l()(),n.Jb(758,0,null,0,1,"SwtLabel",[["text","Third Reimbursement Name"],["width","250"]],null,null,null,m.Yc,m.fb)),n.Ib(759,4440064,null,0,u.vb,[n.r,u.i],{width:[0,"width"],text:[1,"text"]},null),(l()(),n.Jb(760,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","thirdreimbsmntnameLabel"]],null,null,null,m.Yc,m.fb)),n.Ib(761,4440064,[[64,4],["thirdreimbsmntnameLabel",4]],0,u.vb,[n.r,u.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),n.Jb(762,0,null,0,27,"SwtCanvas",[["height","5%"],["id","canvasContainer"],["width","100%"]],null,null,null,m.Nc,m.U)),n.Ib(763,4440064,null,0,u.db,[n.r,u.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(l()(),n.Jb(764,0,null,0,25,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(765,4440064,null,0,u.C,[n.r,u.i],{width:[0,"width"]},null),(l()(),n.Jb(766,0,null,0,19,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,m.Dc,m.K)),n.Ib(767,4440064,null,0,u.C,[n.r,u.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(l()(),n.Jb(768,0,null,0,1,"SwtButton",[["enabled","false"],["id","releaseButton"],["label","Release"]],null,[[null,"click"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.releasePayment(t)&&n);return n},m.Mc,m.T)),n.Ib(769,4440064,[[103,4],["releaseButton",4]],0,u.cb,[n.r,u.i],{id:[0,"id"],enabled:[1,"enabled"],label:[2,"label"]},{onClick_:"click"}),(l()(),n.Jb(770,0,null,0,1,"SwtButton",[["enabled","false"],["id","spreadButton"],["label","Spread"],["toolTip","Spread Display"]],null,[[null,"click"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.spreadDisplay(t)&&n);return n},m.Mc,m.T)),n.Ib(771,4440064,[[105,4],["spreadButton",4]],0,u.cb,[n.r,u.i],{id:[0,"id"],toolTip:[1,"toolTip"],enabled:[2,"enabled"],label:[3,"label"],buttonMode:[4,"buttonMode"]},{onClick_:"click"}),(l()(),n.Jb(772,0,null,0,1,"SwtButton",[["enabled","false"],["id","unStopButton"],["label","Unstop"],["width","70"]],null,[[null,"click"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.unStopPay(t)&&n);return n},m.Mc,m.T)),n.Ib(773,4440064,[[106,4],["unStopButton",4]],0,u.cb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],label:[3,"label"]},{onClick_:"click"}),(l()(),n.Jb(774,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeCatgButton"],["label","Category"]],null,[[null,"click"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.changeCategory(t)&&n);return n},m.Mc,m.T)),n.Ib(775,4440064,[[104,4],["changeCatgButton",4]],0,u.cb,[n.r,u.i],{id:[0,"id"],enabled:[1,"enabled"],label:[2,"label"]},{onClick_:"click"}),(l()(),n.Jb(776,0,null,0,1,"SwtButton",[["id","logButton"],["label","Log"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.doShowLogs()&&n);"keyDown"===e&&(n=!1!==i.keyDownEventHandler(t)&&n);return n},m.Mc,m.T)),n.Ib(777,4440064,[[98,4],["logButton",4]],0,u.cb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(l()(),n.Jb(778,0,null,0,1,"SwtButton",[["id","messageButton"],["label","Message"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.doShowMessageList()&&n);"keyDown"===e&&(n=!1!==i.keyDownEventHandler(t)&&n);return n},m.Mc,m.T)),n.Ib(779,4440064,[[99,4],["messageButton",4]],0,u.cb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(l()(),n.Jb(780,0,null,0,1,"SwtButton",[["id","messageOutButton"],["label","Response"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.doShowMessageOutList()&&n);"keyDown"===e&&(n=!1!==i.keyDownEventHandler(t)&&n);return n},m.Mc,m.T)),n.Ib(781,4440064,[[100,4],["messageOutButton",4]],0,u.cb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(l()(),n.Jb(782,0,null,0,1,"SwtButton",[["id","stopButton"],["label","Stopped"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.doShowStopRulesList()&&n);"keyDown"===e&&(n=!1!==i.keyDownEventHandler(t)&&n);return n},m.Mc,m.T)),n.Ib(783,4440064,[[101,4],["stopButton",4]],0,u.cb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(l()(),n.Jb(784,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","closeButton"],["label","Close"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.closeCurrentTab(t)&&n);"keyDown"===e&&(n=!1!==i.keyDownEventHandler(t)&&n);return n},m.Mc,m.T)),n.Ib(785,4440064,[[102,4],["closeButton",4]],0,u.cb,[n.r,u.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"],buttonMode:[3,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(l()(),n.Jb(786,0,null,0,3,"HBox",[["horizontalAlign","right"],["right","10"],["top","6"]],null,null,null,m.Dc,m.K)),n.Ib(787,4440064,null,0,u.C,[n.r,u.i],{right:[0,"right"],top:[1,"top"],horizontalAlign:[2,"horizontalAlign"]},null),(l()(),n.Jb(788,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","groups-of-rules"],["id","helpIcon"]],null,[[null,"click"]],function(l,e,t){var n=!0,i=l.component;"click"===e&&(n=!1!==i.doHelp()&&n);return n},m.Wc,m.db)),n.Ib(789,4440064,null,0,u.rb,[n.r,u.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"})],function(l,e){l(e,111,0,"100%","100%");l(e,113,0,"vBox1","100%","100%","5","5","5","5");l(e,115,0,"100%","9%");l(e,117,0,"100%","100%");l(e,119,0,"30%","100%");l(e,121,0,"100%");l(e,123,0,"150","Account Group");l(e,125,0,"accountGroupLabel","150","normal"),l(e,127,0);l(e,129,0,"150","Payment Req*");l(e,131,0,"20","0-9","paymentIdTextInput","150");l(e,133,0,"20","paymentIdLabel");l(e,135,0,"35%","100%"),l(e,137,0);l(e,139,0,"50%");l(e,141,0,"accountGroupNameLabel","20","normal");l(e,143,0,"50%");l(e,145,0,"lastActionLabel","20","normal","green");l(e,147,0,"35%","100%"),l(e,149,0);l(e,151,0,"50%");l(e,153,0,"20","");l(e,155,0,"right");l(e,157,0,"75","TimeFrame");l(e,159,0,"timeFrameRadioGroup","horizontal");l(e,162,0,"radioE","80","timeFrameRadioGroup","Entity","E","true");l(e,164,0,"radioC","90","timeFrameRadioGroup","Currency","C");l(e,166,0,"radioS","80","timeFrameRadioGroup","System","S");l(e,168,0,"100%","85%");l(e,170,0,"100%","100%");l(e,173,0,"general","98%","General");l(e,175,0,"100%","93%","Main Details");l(e,177,0,"0","100%","100%","5","5");l(e,179,0,"85%","30%");l(e,181,0,"15%");l(e,183,0,"50%");l(e,185,0,"150","Entity");l(e,187,0,"entityidLabel","normal");l(e,189,0,"50%");l(e,191,0,"150","GPI");l(e,193,0,"gpiLabel","normal");l(e,195,0,"15%");l(e,197,0,"50%");l(e,199,0,"150","Currency");l(e,201,0,"currencycodeLabel","normal");l(e,203,0,"50%");l(e,205,0,"150","Payment Type");l(e,207,0,"paymenttypeLabel","normal");l(e,209,0,"15%");l(e,211,0,"50%");l(e,213,0,"150","Account ID");l(e,215,0,"accountidLabel","normal");l(e,217,0,"50%");l(e,219,0,"150","Cut-Off Time");l(e,221,0,"cutOffTimeLabel","normal");l(e,223,0,"15%");l(e,225,0,"50%");l(e,227,0,"150","Type");l(e,229,0,"accounttypeLabel","normal");l(e,231,0,"50%");l(e,233,0,"150","Urgent / Spreadable");l(e,235,0,"urgentLabel","normal");l(e,237,0,"15%");l(e,239,0,"50%");l(e,241,0,"150","Category ID");l(e,243,0,"categoryIDlabel","normal");l(e,245,0,"50%");l(e,247,0,"150","Category Name");l(e,249,0,"categoryRulelabel","normal");l(e,251,0,"15%");l(e,253,0,"50%");l(e,255,0,"150","Value Date");l(e,257,0,"valuedateLabel","normal");l(e,259,0,"50%");l(e,261,0,"150","Amount");l(e,263,0,"amountLabel","normal");l(e,265,0,"vBoxWithBorders","100%","70%","5");l(e,267,0,"1","solid","black","85%","5"),l(e,269,0);l(e,271,0,"50%");l(e,273,0,"underlineText","150","Payment Status");l(e,275,0,"50%");l(e,277,0,"underlineText","150","Payment References"),l(e,279,0);l(e,281,0,"50%");l(e,283,0,"releaseLabel","150","RELEASED");l(e,285,0,"releaseDateLabel","normal");l(e,287,0,"50%");l(e,289,0,"150","Payment Ref.");l(e,291,0,"paymentreferenceLabel","normal"),l(e,293,0);l(e,295,0,"50%");l(e,297,0,"releasedSourceLabel","150","By");l(e,299,0,"releasedByLabel","normal");l(e,301,0,"50%");l(e,303,0,"150","Related Ref.");l(e,305,0,"relatedreferenceLabel","normal"),l(e,307,0);l(e,309,0,"50%");l(e,311,0,"waitingLabel","150","WAITING");l(e,313,0,"waitingDateLabel","normal");l(e,315,0,"50%");l(e,317,0,"150","Source Ref.");l(e,319,0,"sourcereferenceLabel","normal"),l(e,321,0);l(e,323,0,"50%");l(e,325,0,"150","Required Released");l(e,327,0,"reqreleasetimeLabel","normal");l(e,329,0,"50%");l(e,331,0,"150","FO Ref.");l(e,333,0,"foreferenceLabel","normal"),l(e,335,0);l(e,337,0,"50%");l(e,339,0,"150","Cancelled");l(e,341,0,"50%");l(e,343,0,"150","BO Ref.");l(e,345,0,"boreferenceLabel","normal"),l(e,347,0);l(e,349,0,"50%");l(e,351,0,"50%");l(e,353,0,"underlineText","150","Reason"),l(e,355,0);l(e,357,0,"50%");l(e,359,0,"39%");l(e,361,0,"stoppedLabel","Stopped");l(e,363,0,"33%");l(e,365,0,"stoppedtimeLabel","normal");l(e,367,0,"33%");l(e,369,0,"stoppedUserLabel","normal");l(e,371,0,"50%");l(e,373,0,"stoppedReasonLabel","normal"),l(e,375,0);l(e,377,0,"50%");l(e,379,0,"39%");l(e,381,0,"unstoppedLabel","150","Unstopped");l(e,383,0,"33%");l(e,385,0,"unstoppedtimeLabel","normal");l(e,387,0,"33%");l(e,389,0,"unstoppedUserLabel","normal");l(e,391,0,"50%");l(e,393,0,"unstoppedReasonLabel","normal"),l(e,395,0);l(e,397,0,"50%");l(e,399,0,"39%");l(e,401,0,"blockedLabel","150","Blocked");l(e,403,0,"33%");l(e,405,0,"blockedtimeLabel","normal");l(e,407,0,"33%");l(e,409,0,"blockedUserLabel","normal");l(e,411,0,"50%");l(e,413,0,"blockedReasonLabel","normal"),l(e,415,0);l(e,417,0,"50%");l(e,419,0,"39%");l(e,421,0,"cancelledLabel","150","Cancelled");l(e,423,0,"33%");l(e,425,0,"cancelledtimeLabel","normal");l(e,427,0,"33%");l(e,429,0,"cancelledUserLabel","normal");l(e,431,0,"50%");l(e,433,0,"cancelledReasonLabel","normal");l(e,435,0,"100%","7%","Original Message");l(e,437,0,"1","100%","5"),l(e,439,0);l(e,441,0,"150","Source");l(e,443,0,"sourceidLabel","normal");l(e,445,0,"50");l(e,447,0,"150","Message Type");l(e,449,0,"messagetypeLabel","normal");l(e,451,0,"50");l(e,453,0,"150","Message");l(e,455,0,"messageLabel","normal");l(e,457,0,"moreInfo","Add\u2019l Info");l(e,459,0,"100%","100%");l(e,461,0,"0","100%","100%");l(e,463,0,"4%");l(e,465,0,"250","Action");l(e,467,0,"actionLabel","normal");l(e,469,0,"4%");l(e,471,0,"250","Input Date");l(e,473,0,"inputdateLabel","normal");l(e,475,0,"4%");l(e,477,0,"250","Department");l(e,479,0,"departmentLabel","normal");l(e,481,0,"4%");l(e,483,0,"250","Source Urgency Indicator");l(e,485,0,"sourceurgencyindicatorLabel","normal");l(e,487,0,"4%");l(e,489,0,"250","Sub Entity Id");l(e,491,0,"entitysubidLabel","normal");l(e,493,0,"20%");l(e,495,0,"250","Sender Receiver Info");l(e,497,0,"senderreceiverinfoLabel","normal");l(e,499,0,"100%","350");l(e,501,0,"250","Payment Message");l(e,503,0,"paymentMessageArea","100%","100%","false");l(e,505,0,"parties","Parties");l(e,507,0,"100%","100%");l(e,509,0,"0","100%","100%");l(e,511,0,"4%");l(e,513,0,"250","Sender BIC");l(e,515,0,"senderbicLabel","normal");l(e,517,0,"4%");l(e,519,0,"250","Receiver BIC");l(e,521,0,"receiverbicLabel","normal");l(e,523,0,"5%","8");l(e,525,0,"250","Ordering Customer BIC");l(e,527,0,"orderingcustbeiLabel","normal");l(e,529,0,"4%");l(e,531,0,"250","Ordering Customer Account");l(e,533,0,"orderingcustaccountLabel","normal");l(e,535,0,"4%");l(e,537,0,"250","Ordering Customer Name");l(e,539,0,"orderingcustnameLabel","normal");l(e,541,0,"5%","8");l(e,543,0,"250","Ordering Institution BIC");l(e,545,0,"orderinginstbicLabel","normal");l(e,547,0,"4%");l(e,549,0,"250","Ordering Institution Account");l(e,551,0,"orderinginstaccountLabel","normal");l(e,553,0,"4%");l(e,555,0,"250","Ordering Institution Name");l(e,557,0,"orderinginstnameLabel","normal");l(e,559,0,"5%","8");l(e,561,0,"250","Senders Correspondent BIC");l(e,563,0,"senderscorresbicLabel","normal");l(e,565,0,"4%");l(e,567,0,"250","Senders Correspondent Account");l(e,569,0,"senderscorresaccountLabel","normal");l(e,571,0,"4%");l(e,573,0,"250","Senders Correspondent Name");l(e,575,0,"senderscorresnameLabel","normal");l(e,577,0,"5%","8");l(e,579,0,"250","Receivers Correspondent BIC");l(e,581,0,"receiverscorresbicLabel","normal");l(e,583,0,"4%");l(e,585,0,"250","Receivers Correspondent Account");l(e,587,0,"receiverscorresaccountLabel","normal");l(e,589,0,"4%");l(e,591,0,"250","Receivers Correspondent Name");l(e,593,0,"receiverscorresnameLabel","normal");l(e,595,0,"5%","8");l(e,597,0,"250","Intermediary BIC");l(e,599,0,"intermediarybicLabel","normal");l(e,601,0,"4%");l(e,603,0,"250","Intermediary Account");l(e,605,0,"intermediaryaccountLabel","normal"),l(e,607,0);l(e,609,0,"250","Intermediary Name");l(e,611,0,"intermediarynameLabel","normal");l(e,613,0,"5%","8");l(e,615,0,"250","Account With Institution BIC");l(e,617,0,"accountwithinstbicLabel","normal");l(e,619,0,"4%");l(e,621,0,"250","Account With Institution Account");l(e,623,0,"accountwithinstaccountLabel","normal");l(e,625,0,"4%");l(e,627,0,"250","Account With Institution Name");l(e,629,0,"accountwithinstnameLabel","normal");l(e,631,0,"5%","8");l(e,633,0,"250","Beneficiary Customer BIC");l(e,635,0,"beneficiarycustbicLabel","normal");l(e,637,0,"4%");l(e,639,0,"250","Beneficiary Customer Account");l(e,641,0,"beneficiarycustaccountLabel","normal");l(e,643,0,"4%");l(e,645,0,"250","Beneficiary Customer Name");l(e,647,0,"beneficiarycustnameLabel","normal");l(e,649,0,"parties2","Parties 2");l(e,651,0,"100%","100%");l(e,653,0,"0","100%","100%"),l(e,655,0);l(e,657,0,"250","Beneficiary Institution BIC");l(e,659,0,"beneficiaryinstbicLabel","normal"),l(e,661,0);l(e,663,0,"250","Beneficiary Institution Account");l(e,665,0,"beneficiaryinstaccountLabel","normal"),l(e,667,0);l(e,669,0,"250","Beneficiary Institution Name");l(e,671,0,"beneficiaryinstnameLabel","normal");l(e,673,0,"8");l(e,675,0,"250","Seller BIC");l(e,677,0,"sellerbicLabel","normal"),l(e,679,0);l(e,681,0,"250","Seller Account");l(e,683,0,"selleraccountLabel","normal"),l(e,685,0);l(e,687,0,"250","Seller Name");l(e,689,0,"sellernameLabel","normal");l(e,691,0,"8");l(e,693,0,"250","Deliverers Custodian BIC");l(e,695,0,"delivererscustodianbicLabel","normal"),l(e,697,0);l(e,699,0,"250","Deliverers Custodian Account");l(e,701,0,"delivererscustodianaccountLabel","normal"),l(e,703,0);l(e,705,0,"250","Deliverers Custodian Name");l(e,707,0,"delivererscustodiannameLabel","normal");l(e,709,0,"8");l(e,711,0,"250","Delivery Agent BIC");l(e,713,0,"deliveryagentbicLabel","normal"),l(e,715,0);l(e,717,0,"250","Delivery Agent Account");l(e,719,0,"deliveryagentaccountLabel","normal"),l(e,721,0);l(e,723,0,"250","Delivery Agent Name");l(e,725,0,"deliveryagentnameLabel","normal");l(e,727,0,"8");l(e,729,0,"250","Place Of Settlement BIC");l(e,731,0,"placeofsettlementbicLabel","normal"),l(e,733,0);l(e,735,0,"250","Place Of Settlement Account");l(e,737,0,"placeofsettlementaccountLabel","normal"),l(e,739,0);l(e,741,0,"250","Place Of Settlement Name");l(e,743,0,"placeofsettlementnameLabel","normal");l(e,745,0,"8");l(e,747,0,"250","Third Reimbursement BIC");l(e,749,0,"thirdreimbsmntbicLabel","normal"),l(e,751,0);l(e,753,0,"250","Third Reimbursement Account");l(e,755,0,"thirdreimbsmntaccountLabel","normal"),l(e,757,0);l(e,759,0,"250","Third Reimbursement Name");l(e,761,0,"thirdreimbsmntnameLabel","normal");l(e,763,0,"canvasContainer","100%","5%");l(e,765,0,"100%");l(e,767,0,"100%","5");l(e,769,0,"releaseButton","false","Release");l(e,771,0,"spreadButton","Spread Display","false","Spread",!0);l(e,773,0,"unStopButton","70","false","Unstop");l(e,775,0,"changeCatgButton","false","Category");l(e,777,0,"logButton","70","Log");l(e,779,0,"messageButton","70","Message");l(e,781,0,"messageOutButton","70","Response");l(e,783,0,"stopButton","70","Stopped");l(e,785,0,"closeButton","70","Close","true");l(e,787,0,"10","6","right");l(e,789,0,"helpIcon","true",!0,"groups-of-rules")},null)}function M(l){return n.dc(0,[(l()(),n.Jb(0,0,null,null,1,"payment-request-display",[],null,null,null,H,O)),n.Ib(1,4440064,null,0,o,[u.i,n.r],null,null)],function(l,e){l(e,1,0)},null)}var U=n.Fb("payment-request-display",o,M,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);