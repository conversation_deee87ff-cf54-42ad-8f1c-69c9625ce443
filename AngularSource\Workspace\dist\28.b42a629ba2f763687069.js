(window.webpackJsonp=window.webpackJsonp||[]).push([[28],{"6StK":function(t,e,i){"use strict";i.r(e);var o=i("CcnG"),n=i("mrSG"),l=i("447K"),s=i("ZYCi"),a=function(t){function e(e,i){var o=t.call(this,i,e)||this;return o.commonService=e,o.element=i,o.jsonReader=new l.L,o.inputData=new l.G(o.commonService),o.checkAuthData=new l.G(o.commonService),o.logicUpdate=new l.G(o.commonService),o.requestParams=[],o.baseURL=l.Wb.getBaseURL(),o.actionMethod="",o.actionPath="",o.moduleName="Stop Rules Maintenance",o.versionNumber="1.00.00",o.releaseDate="04 March 2019",o.moduleURL=null,o.menuAccess="",o.programId="",o.componentId=!1,o.helpURL=null,o.message=null,o.title=null,o.errorLocation=0,o.moduleReportURL=null,o.moduleId="",o.groupId=null,o.groupName=null,o.groupType=null,o.moduleSearchURL=null,o.searchQuery="",o.searchFlag=!1,o.queryToDisplay="",o.sQuery="",o.mapMultiplierList=new l.H,o.mapPriorityList=new l.H,o.menuaccess=2,o.requireAuthorisation=!0,o.faciltiyId=null,o.doUpdateRecord=!1,o.childScreenName="",o.swtAlert=new l.bb(e),o}return n.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this},e.prototype.disableButtons=function(){-1==this.stopRulesGrid.selectedIndex&&this.disableComponents()},e.prototype.onLoad=function(){var t=this;this.stopRulesGrid=this.canvasStopRules.addChild(l.hb),this.stopRulesGrid.uniqueColumn="stopRuleId",this.stopRulesGrid.onFilterChanged=this.disableButtons.bind(this),this.stopRulesGrid.onSortChanged=this.disableButtons.bind(this);try{this.title=l.Wb.getAMLMessages("pcpriorityMaintenanceScreen.windowtitle.help_screen"),this.message=l.Wb.getAMLMessages("pcpriorityMaintenanceScreen.message.help_message"),this.actionMethod="method=display",this.actionPath="stopRulesPCM.do?",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.stopRulesGrid.onRowClick=function(e){t.checkIfMaintenanceEventExist(e)},this.stopRulesGrid.onRowDoubleClick=function(t){},this.addButton.label="Add",this.activateButton.label="Activate",this.deactivateButton.label="Deactivate",this.viewButton.label="View",this.changeButton.label="Change",this.deleteButton.label="Delete",this.closeButton.label="Close",this.addButton.setFocus(),this.disableComponents()}catch(e){console.log(e,this.moduleId,"ClassName","onLoad")}},e.prototype.inputDataFault=function(t){try{this.swtAlert.error(t.fault.faultstring+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail)}catch(e){l.Wb.logError(e,this.moduleId,"ClassName","inputDataFault",this.errorLocation)}},e.prototype.inputDataResult=function(t){var e,i=null;try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(this.doUpdateRecord&&(l.Z.isTrue(this.requireAuthorisation)&&this.swtAlert.show("This action needs second user authorisation","Warning",l.c.OK),this.doUpdateRecord=!1),this.helpURL=this.jsonReader.getSingletons().helpurl,!this.jsonReader.isDataBuilding()){i=this.jsonReader.getColumnData();for(var o=0;o<i.column.length;o++)e=l.Wb.getAMLMessages(i.column[o].heading),i.column[o].heading=e;var n={columns:i};this.stopRulesGrid.dateFormat=this.jsonReader.getScreenAttributes().dateformat,null!==this.stopRulesGrid&&void 0!==this.stopRulesGrid||(this.stopRulesGrid.screenID=this.programId,this.stopRulesGrid.componentID=this.jsonReader.getSingletons().screenid,this.stopRulesGrid.CustomGrid(n)),this.componentId=this.lastRecievedJSON.screenid,this.stopRulesGrid.doubleClickEnabled=!0,this.stopRulesGrid.CustomGrid(n),this.stopRulesGrid.dataProvider=null,this.stopRulesGrid.gridData=this.jsonReader.getGridData(),this.stopRulesGrid.setRowSize=this.jsonReader.getRowSize(),this.stopRulesGrid.doubleClickEnabled=!0,this.disableButtons(),this.stopRulesGrid.selectedIndex,this.menuaccess=this.jsonReader.getScreenAttributes().menuaccess,this.requireAuthorisation=this.jsonReader.getScreenAttributes().requireAuthorisation,this.faciltiyId=this.jsonReader.getScreenAttributes().faciltiyId,0==this.menuaccess&&(this.addButton.enabled=!0)}this.selectedStatus=this.jsonReader.getSingletons().status,this.selectedStatus?this.statusGroup.selectedValue=this.selectedStatus:this.statusGroup.selectedValue="A",this.prevRecievedJSON=this.lastRecievedJSON}else"PROCESS_RUNNING"==this.jsonReader.getRequestReplyMessage()?this.swtAlert.error("Stop rule Process is running on the selected Rule , the selected Rule cannot be deleted or deactivated for the moment"):"OTHER_RECORD_DEPEND"==this.jsonReader.getRequestReplyMessage()?this.swtAlert.error("Cannot delete a STOP rule that has previously stopped a Payment Request"):this.swtAlert.error("Error occurred, Please contact your System Administrator")}catch(s){console.log("error inputDataResult",s)}},e.prototype.startOfComms=function(){try{this.loadingImage.setVisible(!0),this.disableComponents()}catch(t){l.Wb.logError(t,this.moduleId,"ClassName","startOfComms",this.errorLocation)}},e.prototype.endOfComms=function(){try{this.loadingImage.setVisible(!1)}catch(t){l.Wb.logError(t,this.moduleId,"ClassName","endOfComms",this.errorLocation)}},e.prototype.checkIfMaintenanceEventExist=function(t){var e=this;try{if(1===this.stopRulesGrid.selectedIndices.length&&this.stopRulesGrid.selectable){this.checkAuthData.cbStart=this.startOfComms.bind(this),this.checkAuthData.cbStop=this.endOfComms.bind(this),this.checkAuthData.cbResult=function(t){e.checkResult(t)},this.requestParams.recordId=this.stopRulesGrid.selectedItem.stopRuleId.content,this.requestParams.facilityId=this.faciltiyId,this.checkAuthData.cbFault=this.inputDataFault.bind(this),this.checkAuthData.encodeURL=!1,this.checkAuthData.url=this.baseURL+"maintenanceEvent.do?method=checkIfMaintenenanceEventExist",this.checkAuthData.send(this.requestParams)}}catch(i){console.log(i)}},e.prototype.checkResult=function(t){try{if(this.checkAuthData&&this.checkAuthData.isBusy())this.checkAuthData.cbStop();else if(this.jsonReader.setInputJSON(t),"RECOD_EXIST"==this.jsonReader.getRequestReplyMessage()){var e=l.Wb.getPredictMessage("maintenanceEvent.alert.cannotBeAmended",null);this.swtAlert.error(e),this.cellClickEventHandler(!0)}else this.cellClickEventHandler(!1)}catch(i){console.log("error in inputData",i)}},e.prototype.cellClickEventHandler=function(t){void 0===t&&(t=!1);try{1===this.stopRulesGrid.selectedIndices.length&&this.stopRulesGrid.selectable?(0!=this.menuaccess||t||("true"==this.stopRulesGrid.selectedItem.isActive.content&&(this.enableDisableDeactivateButton(!0),"false"==this.stopRulesGrid.selectedItem.isChangable.content?this.enableDisableChangewButton(!1):this.enableDisableChangewButton(!0),this.enableDisableActivateButton(!1)),"false"==this.stopRulesGrid.selectedItem.isActive.content&&(this.enableDisableDeactivateButton(!1),parseInt(this.stopRulesGrid.selectedItem.numberOfLinkedPR.content)>0?(this.enableDisableActivateButton(!1),this.enableDisableChangewButton(!1)):("false"==this.stopRulesGrid.selectedItem.isChangable.content?this.enableDisableChangewButton(!1):this.enableDisableChangewButton(!0),this.enableDisableActivateButton(!0))),this.enableDisableDeleteButton(!0)),this.enableDisableViewButton(!0)):(this.enableDisableActivateButton(!1),this.enableDisableDeleteButton(!1),this.enableDisableDeactivateButton(!1),this.enableDisableViewButton(!1),this.enableDisableChangewButton(!1)),this.addButton.setFocus()}catch(e){l.Wb.logError(e,this.moduleId,"ClassName","cellClickEventHandler",this.errorLocation)}},e.prototype.disableComponents=function(){try{this.enableDisableActivateButton(!1),this.enableDisableDeleteButton(!1),this.enableDisableDeactivateButton(!1),this.enableDisableViewButton(!1),this.enableDisableChangewButton(!1)}catch(t){l.Wb.logError(t,this.moduleId,"ClassName","disableComponents",this.errorLocation)}},e.prototype.enableDisableViewButton=function(t){this.viewButton.enabled=t,this.viewButton.buttonMode=t},e.prototype.enableDisableChangewButton=function(t){this.changeButton.enabled=t,this.changeButton.buttonMode=t},e.prototype.enableDisableDeleteButton=function(t){this.deleteButton.enabled=t,this.deleteButton.buttonMode=t},e.prototype.enableDisableActivateButton=function(t){this.activateButton.enabled=t,this.activateButton.buttonMode=t},e.prototype.enableDisableDeactivateButton=function(t){this.deactivateButton.enabled=t,this.deleteButton.buttonMode=t},e.prototype.keyDownEventHandler=function(t){try{var e=Object(l.ic.getFocus()).name;t.keyCode===l.N.ENTER&&("addButton"===e||"viewButton"===e||("closeButton"===e?this.closeCurrentTab(t):"csv"===e?this.report("csv"):"excel"===e?this.report("xls"):"pdf"===e?this.report("pdf"):"helpIcon"===e?this.doHelp():"deleteButton"===e||"settingButton"===e||"cancelButton"===e||("activateButton"===e?this.doActivatePCStopRuleEventHandler(t):"deactivateButton"===e&&this.doActivatePCStopRuleEventHandler(t))))}catch(i){l.Wb.logError(i,this.moduleId,"ClassName","keyDownEventHandler",this.errorLocation)}},e.prototype.report=function(t){var e=null,i="",o=null;try{o=this.moduleId,e=""!==this.stopRulesGrid.filteredGridColumns?this.stopRulesGrid.getFilteredGridColumns():"",i=this.stopRulesGrid.getSortedGridColumn(),this.actionPath="aml/PCPriorityMaintenance!displayReport.do?",this.actionMethod="type="+t,this.actionMethod=this.actionMethod+"&programId="+this.programId,this.actionMethod=this.actionMethod+"&action=EXPORT",this.actionMethod=this.actionMethod+"&selectedFilter="+e,this.actionMethod=this.actionMethod+"&selectedSort="+i,this.actionMethod=this.actionMethod+"&currentModuleId="+o,this.actionMethod=this.actionMethod+"&sQuery="+this.searchQuery,this.actionMethod=this.actionMethod+"&searchFlag="+this.searchFlag,this.actionMethod=this.actionMethod+"&moduleId="+this.moduleId,this.actionMethod=this.actionMethod+"&print=ALL",l.x.call("getReports",this.actionPath+this.actionMethod)}catch(n){l.Wb.logError(n,o,"ClassName","report",this.errorLocation)}},e.prototype.closeCurrentTab=function(t){try{this.dispose()}catch(e){l.Wb.logError(e,l.Wb.SYSTEM_MODULE_ID,"ClassName","refreshGrid",this.errorLocation)}},e.prototype.dispose=function(){try{this.stopRulesGrid=null,this.requestParams=null,this.inputData=null,this.jsonReader=null,this.menuAccess=null,this.lastRecievedJSON=null,this.prevRecievedJSON=null,this.child=null,this.mapMultiplierList=null,this.searchQuery="",this.searchFlag=!1,l.x.call("close"),this.titleWindow?this.close():window.close()}catch(t){l.Wb.logError(t,this.moduleId,"ClassName","dispose",this.errorLocation)}},e.prototype.getParamsFromParent=function(){var t="";return this.stopRulesGrid.selectedIndex>-1&&(t=this.stopRulesGrid.selectedItem.stopRuleId.content),[{screenName:this.childScreenName,stopRuleId:t}]},e.prototype.doAddPCStopRule=function(t){try{this.childScreenName="add",l.x.call("openChildWindow","stopRuleAdd")}catch(e){l.Wb.logError(e,this.moduleId,"ClassName","doAddPCStopRule",this.errorLocation)}},e.prototype.doActivatePCStopRule=function(){try{this.doUpdateRecord=!0,this.requestParams=[],this.requestParams.stopRuleId=this.stopRulesGrid.selectedItem.stopRuleId.content,this.requestParams.selectedStatus=this.statusGroup.selectedValue,this.requestParams.active=!0,this.actionMethod="method=activateDesactive",this.actionPath="stopRulesPCM.do?",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.stopRulesGrid.selectedIndex=-1}catch(t){l.Wb.logError(t,this.moduleId,"ClassName","doActivatePCStopRule",this.errorLocation)}},e.prototype.doActivatePCStopRuleEventHandler=function(t){"activate"==t?this.doActivatePCStopRule():this.doDeactivateEventHanderPCStopRule()},e.prototype.doDeactivateEventHanderPCStopRule=function(){var t="When deactivated the attached Payments will be ";t+="'"+this.stopRulesGrid.selectedItem.actionOnDeactivation.content+"' . Do you want to continue?",this.swtAlert.confirm(t,null,l.c.YES|l.c.NO,null,this.doDeactivatePCStopRule.bind(this))},e.prototype.doDeactivatePCStopRule=function(t){if(t.detail===l.c.YES)try{this.doUpdateRecord=!0,this.requestParams=[],this.requestParams.stopRuleId=this.stopRulesGrid.selectedItem.stopRuleId.content,this.requestParams.selectedStatus=this.statusGroup.selectedValue,this.requestParams.active=!1,this.actionMethod="method=activateDesactive",this.actionPath="stopRulesPCM.do?",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.stopRulesGrid.selectedIndex=-1}catch(e){l.Wb.logError(e,this.moduleId,"ClassName","doDeactivatePCStopRule",this.errorLocation)}},e.prototype.doViewPCStopRule=function(t){try{this.childScreenName="view",l.x.call("openChildWindow","stopRuleAdd")}catch(e){l.Wb.logError(e,this.moduleId,"ClassName","doViewPCStopRule",this.errorLocation)}},e.prototype.doChangePCStopRule=function(t){try{this.childScreenName="change",l.x.call("openChildWindow","stopRuleAdd")}catch(e){l.Wb.logError(e,this.moduleId,"ClassName","doViewPCStopRule",this.errorLocation)}},e.prototype.doDeletePCStopRule=function(t){if(parseInt(this.stopRulesGrid.selectedItem.numberOfLinkedPR.content)>0)this.swtAlert.warning("Cannot delete Stop Rule: Stop Rule has stopped Payment Request(s).");else try{this.swtAlert.confirm("Do you wish to delete this row","Alert",l.c.YES|l.c.NO,null,this.stopRuleRemoveHandler.bind(this))}catch(e){l.Wb.logError(e,this.moduleId,"ClassName","doDeletePCStopRule",this.errorLocation)}},e.prototype.stopRulesRowRemove=function(){this.enableDisableActivateButton(!1),this.enableDisableDeleteButton(!1),this.enableDisableDeactivateButton(!1),this.enableDisableViewButton(!1),this.doUpdateRecord=!0,this.requestParams=[],this.requestParams.stopRuleId=this.stopRulesGrid.selectedItem.stopRuleId.content,this.actionMethod="method=delete",this.actionPath="stopRulesPCM.do?",this.requestParams.selectedStatus=this.statusGroup.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.stopRulesGrid.selectedIndex=-1},e.prototype.stopRuleRemoveHandler=function(t){t.detail===l.c.YES&&this.stopRulesRowRemove()},e.prototype.refreshGrid=function(){this.requestParams=[],this.doUpdateRecord=!1,this.actionMethod="method=display",this.actionPath="stopRulesPCM.do?",this.requestParams.selectedStatus=this.statusGroup.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.stopRulesGrid.selectedIndex=-1,this.inputData.send(this.requestParams)},e.prototype.doHelp=function(){try{l.x.call("help")}catch(t){l.Wb.logError(t,this.moduleId,"ClassName","doHelp",this.errorLocation)}},e}(l.yb),u=[{path:"",component:a}],d=(s.l.forChild(u),function(){return function(){}}()),r=i("pMnS"),c=i("RChO"),h=i("t6HQ"),b=i("WFGK"),p=i("5FqG"),R=i("Ip0R"),m=i("gIcY"),g=i("t/Na"),w=i("sE5F"),v=i("OzfB"),D=i("T7CS"),y=i("S7LP"),C=i("6aHO"),f=i("WzUx"),B=i("A7o+"),S=i("zCE2"),I=i("Jg5P"),k=i("3R0m"),M=i("hhbb"),P=i("5rxC"),G=i("Fzqc"),A=i("21Lb"),E=i("hUWP"),L=i("3pJQ"),N=i("V9q+"),_=i("VDKW"),O=i("kXfT"),T=i("BGbe");i.d(e,"StopRulesModuleNgFactory",function(){return q}),i.d(e,"RenderType_StopRules",function(){return x}),i.d(e,"View_StopRules_0",function(){return J}),i.d(e,"View_StopRules_Host_0",function(){return W}),i.d(e,"StopRulesNgFactory",function(){return F});var q=o.Gb(d,[],function(t){return o.Qb([o.Rb(512,o.n,o.vb,[[8,[r.a,c.a,h.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,F]],[3,o.n],o.J]),o.Rb(4608,R.m,R.l,[o.F,[2,R.u]]),o.Rb(4608,m.c,m.c,[]),o.Rb(4608,m.p,m.p,[]),o.Rb(4608,g.j,g.p,[R.c,o.O,g.n]),o.Rb(4608,g.q,g.q,[g.j,g.o]),o.Rb(5120,g.a,function(t){return[t,new l.tb]},[g.q]),o.Rb(4608,g.m,g.m,[]),o.Rb(6144,g.k,null,[g.m]),o.Rb(4608,g.i,g.i,[g.k]),o.Rb(6144,g.b,null,[g.i]),o.Rb(4608,g.f,g.l,[g.b,o.B]),o.Rb(4608,g.c,g.c,[g.f]),o.Rb(4608,w.c,w.c,[]),o.Rb(4608,w.g,w.b,[]),o.Rb(5120,w.i,w.j,[]),o.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),o.Rb(4608,w.f,w.a,[]),o.Rb(5120,w.d,w.k,[w.h,w.f]),o.Rb(5120,o.b,function(t,e){return[v.j(t,e)]},[R.c,o.O]),o.Rb(4608,D.a,D.a,[]),o.Rb(4608,y.a,y.a,[]),o.Rb(4608,C.a,C.a,[o.n,o.L,o.B,y.a,o.g]),o.Rb(4608,f.c,f.c,[o.n,o.g,o.B]),o.Rb(4608,f.e,f.e,[f.c]),o.Rb(4608,B.l,B.l,[]),o.Rb(4608,B.h,B.g,[]),o.Rb(4608,B.c,B.f,[]),o.Rb(4608,B.j,B.d,[]),o.Rb(4608,B.b,B.a,[]),o.Rb(4608,B.k,B.k,[B.l,B.h,B.c,B.j,B.b,B.m,B.n]),o.Rb(4608,f.i,f.i,[[2,B.k]]),o.Rb(4608,f.r,f.r,[f.L,[2,B.k],f.i]),o.Rb(4608,f.t,f.t,[]),o.Rb(4608,f.w,f.w,[]),o.Rb(1073742336,s.l,s.l,[[2,s.r],[2,s.k]]),o.Rb(1073742336,R.b,R.b,[]),o.Rb(1073742336,m.n,m.n,[]),o.Rb(1073742336,m.l,m.l,[]),o.Rb(1073742336,S.a,S.a,[]),o.Rb(1073742336,I.a,I.a,[]),o.Rb(1073742336,m.e,m.e,[]),o.Rb(1073742336,k.a,k.a,[]),o.Rb(1073742336,B.i,B.i,[]),o.Rb(1073742336,f.b,f.b,[]),o.Rb(1073742336,g.e,g.e,[]),o.Rb(1073742336,g.d,g.d,[]),o.Rb(1073742336,w.e,w.e,[]),o.Rb(1073742336,M.b,M.b,[]),o.Rb(1073742336,P.b,P.b,[]),o.Rb(1073742336,v.c,v.c,[]),o.Rb(1073742336,G.a,G.a,[]),o.Rb(1073742336,A.d,A.d,[]),o.Rb(1073742336,E.c,E.c,[]),o.Rb(1073742336,L.a,L.a,[]),o.Rb(1073742336,N.a,N.a,[[2,v.g],o.O]),o.Rb(1073742336,_.b,_.b,[]),o.Rb(1073742336,O.a,O.a,[]),o.Rb(1073742336,T.b,T.b,[]),o.Rb(1073742336,l.Tb,l.Tb,[]),o.Rb(1073742336,d,d,[]),o.Rb(256,g.n,"XSRF-TOKEN",[]),o.Rb(256,g.o,"X-XSRF-TOKEN",[]),o.Rb(256,"config",{},[]),o.Rb(256,B.m,void 0,[]),o.Rb(256,B.n,void 0,[]),o.Rb(256,"popperDefaults",{},[]),o.Rb(1024,s.i,function(){return[[{path:"",component:a}]]},[])])}),H=[[""]],x=o.Hb({encapsulation:0,styles:H,data:{}});function J(t){return o.dc(0,[o.Zb(402653184,1,{_container:0}),o.Zb(402653184,2,{canvasStopRules:0}),o.Zb(402653184,3,{loadingImage:0}),o.Zb(402653184,4,{addButton:0}),o.Zb(402653184,5,{activateButton:0}),o.Zb(402653184,6,{deactivateButton:0}),o.Zb(402653184,7,{viewButton:0}),o.Zb(402653184,8,{changeButton:0}),o.Zb(402653184,9,{deleteButton:0}),o.Zb(402653184,10,{closeButton:0}),o.Zb(402653184,11,{settingButton:0}),o.Zb(402653184,12,{inactiveRadioButton:0}),o.Zb(402653184,13,{bothRadioButton:0}),o.Zb(402653184,14,{activeRadioButton:0}),o.Zb(402653184,15,{statusGroup:0}),o.Zb(402653184,16,{csv:0}),o.Zb(402653184,17,{excel:0}),o.Zb(402653184,18,{pdf:0}),o.Zb(402653184,19,{helpIcon:0}),(t()(),o.Jb(19,0,null,null,46,"SwtModule",[["height","100%"],["paddingTop","10"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var o=!0,n=t.component;"creationComplete"===e&&(o=!1!==n.onLoad()&&o);return o},p.ad,p.hb)),o.Ib(20,4440064,null,0,l.yb,[o.r,l.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},{creationComplete:"creationComplete"}),(t()(),o.Jb(21,0,null,0,44,"VBox",[["height","100%"],["paddingLeft","10"],["paddingRight","10"],["width","100%"]],null,null,null,p.od,p.vb)),o.Ib(22,4440064,null,0,l.ec,[o.r,l.i,o.T],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"],paddingRight:[3,"paddingRight"]},null),(t()(),o.Jb(23,0,null,0,14,"SwtCanvas",[["height","9%"],["width","100%"]],null,null,null,p.Nc,p.U)),o.Ib(24,4440064,null,0,l.db,[o.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(25,0,null,0,12,"HBox",[["height","28"],["marginTop","8"],["paddingLeft","10"]],null,null,null,p.Dc,p.K)),o.Ib(26,4440064,null,0,l.C,[o.r,l.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"],marginTop:[2,"marginTop"]},null),(t()(),o.Jb(27,0,null,0,1,"SwtLabel",[["paddingTop","5"],["text","Status"],["width","115"]],null,null,null,p.Yc,p.fb)),o.Ib(28,4440064,null,0,l.vb,[o.r,l.i],{width:[0,"width"],paddingTop:[1,"paddingTop"],text:[2,"text"]},null),(t()(),o.Jb(29,0,null,0,8,"SwtRadioButtonGroup",[["align","horizontal"],["id","statusGroup"]],null,[[null,"change"]],function(t,e,i){var o=!0,n=t.component;"change"===e&&(o=!1!==n.refreshGrid()&&o);return o},p.ed,p.lb)),o.Ib(30,4440064,[[15,4],["statusGroup",4]],1,l.Hb,[g.c,o.r,l.i],{id:[0,"id"],align:[1,"align"]},{change_:"change"}),o.Zb(603979776,20,{radioItems:1}),(t()(),o.Jb(32,0,null,0,1,"SwtRadioItem",[["groupName","statusRadioGroup"],["id","activeRadioButton"],["label","Active"],["value","A"],["width","160"]],null,null,null,p.fd,p.mb)),o.Ib(33,4440064,[[20,4],[14,4],["activeRadioButton",4]],0,l.Ib,[o.r,l.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"]},null),(t()(),o.Jb(34,0,null,0,1,"SwtRadioItem",[["groupName","statusRadioGroup"],["id","inactiveRadioButton"],["label","Inactive"],["value","I"],["width","160"]],null,null,null,p.fd,p.mb)),o.Ib(35,4440064,[[20,4],[12,4],["inactiveRadioButton",4]],0,l.Ib,[o.r,l.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"]},null),(t()(),o.Jb(36,0,null,0,1,"SwtRadioItem",[["groupName","statusRadioGroup"],["id","bothRadioButton"],["label","Both"],["value","B"],["width","160"]],null,null,null,p.fd,p.mb)),o.Ib(37,4440064,[[20,4],[13,4],["bothRadioButton",4]],0,l.Ib,[o.r,l.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],label:[3,"label"],value:[4,"value"]},null),(t()(),o.Jb(38,0,null,0,1,"SwtCanvas",[["height","82%"],["id","canvasStopRules"],["width","100%"]],null,null,null,p.Nc,p.U)),o.Ib(39,4440064,[[2,4],["canvasStopRules",4]],0,l.db,[o.r,l.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),o.Jb(40,0,null,0,25,"SwtCanvas",[["height","40"],["width","100%"]],null,null,null,p.Nc,p.U)),o.Ib(41,4440064,null,0,l.db,[o.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(42,0,null,0,23,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),o.Ib(43,4440064,null,0,l.C,[o.r,l.i],{width:[0,"width"]},null),(t()(),o.Jb(44,0,null,0,15,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),o.Ib(45,4440064,null,0,l.C,[o.r,l.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),o.Jb(46,0,null,0,1,"SwtButton",[["enabled","false"],["id","addButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doAddPCStopRule(i)&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},p.Mc,p.T)),o.Ib(47,4440064,[[4,4],["addButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(48,0,null,0,1,"SwtButton",[["id","activateButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doActivatePCStopRuleEventHandler("activate")&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},p.Mc,p.T)),o.Ib(49,4440064,[[5,4],["activateButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(50,0,null,0,1,"SwtButton",[["id","deactivateButton"],["width","80"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doActivatePCStopRuleEventHandler("deactivate")&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},p.Mc,p.T)),o.Ib(51,4440064,[[6,4],["deactivateButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(52,0,null,0,1,"SwtButton",[["id","changeButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doChangePCStopRule(i)&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},p.Mc,p.T)),o.Ib(53,4440064,[[8,4],["changeButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(54,0,null,0,1,"SwtButton",[["id","viewButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doViewPCStopRule(i)&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},p.Mc,p.T)),o.Ib(55,4440064,[[7,4],["viewButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(56,0,null,0,1,"SwtButton",[["id","deleteButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doDeletePCStopRule(i)&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},p.Mc,p.T)),o.Ib(57,4440064,[[9,4],["deleteButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(58,0,null,0,1,"SwtButton",[["id","closeButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.closeCurrentTab(i)&&o);"keyDown"===e&&(o=!1!==n.keyDownEventHandler(i)&&o);return o},p.Mc,p.T)),o.Ib(59,4440064,[[10,4],["closeButton",4]],0,l.cb,[o.r,l.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),o.Jb(60,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","10"]],null,null,null,p.Dc,p.K)),o.Ib(61,4440064,null,0,l.C,[o.r,l.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),o.Jb(62,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),o.Ib(63,114688,[[3,4],["loadingImage",4]],0,l.xb,[o.r],null,null),(t()(),o.Jb(64,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","groups-of-rules"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var o=!0,n=t.component;"click"===e&&(o=!1!==n.doHelp()&&o);return o},p.Wc,p.db)),o.Ib(65,4440064,null,0,l.rb,[o.r,l.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"})],function(t,e){t(e,20,0,"100%","100%","10");t(e,22,0,"100%","100%","10","10");t(e,24,0,"100%","9%");t(e,26,0,"28","10","8");t(e,28,0,"115","5","Status");t(e,30,0,"statusGroup","horizontal");t(e,33,0,"activeRadioButton","160","statusRadioGroup","Active","A");t(e,35,0,"inactiveRadioButton","160","statusRadioGroup","Inactive","I");t(e,37,0,"bothRadioButton","160","statusRadioGroup","Both","B");t(e,39,0,"canvasStopRules","100%","82%");t(e,41,0,"100%","40");t(e,43,0,"100%");t(e,45,0,"100%","5");t(e,47,0,"addButton","70","false",!0);t(e,49,0,"activateButton","70",!0);t(e,51,0,"deactivateButton","80",!0);t(e,53,0,"changeButton","70",!0);t(e,55,0,"viewButton","70",!0);t(e,57,0,"deleteButton","70",!0);t(e,59,0,"closeButton","70",!0);t(e,61,0,"right","10"),t(e,63,0);t(e,65,0,"helpIcon","true",!0,"groups-of-rules")},null)}function W(t){return o.dc(0,[(t()(),o.Jb(0,0,null,null,1,"pcstop-rules",[],null,null,null,J,x)),o.Ib(1,4440064,null,0,a,[l.i,o.r],null,null)],function(t,e){t(e,1,0)},null)}var F=o.Fb("pcstop-rules",a,W,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);