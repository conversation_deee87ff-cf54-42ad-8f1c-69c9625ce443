<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matching Process - Customer Guide</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.0/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.8em;
            font-weight: 300;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            margin: 15px 0 0 0;
            font-size: 1.3em;
            opacity: 0.9;
        }
        .content {
            padding: 40px;
        }
        .diagram-section {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 40px;
            border: 2px solid #e9ecef;
        }
        .diagram-container {
            width: 100%;
            height: 900px;
            border: 2px solid #dee2e6;
            border-radius: 10px;
            overflow: auto;
            background: white;
            position: relative;
            box-shadow: inset 0 2px 4px rgba(0,0,0,0.1);
        }
        .mermaid {
            min-width: 1400px;
            min-height: 900px;
            transform-origin: top left;
            padding: 20px;
        }
        .zoom-controls {
            position: absolute;
            top: 15px;
            right: 15px;
            z-index: 1000;
            background: rgba(255,255,255,0.95);
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border: 1px solid #dee2e6;
        }
        .zoom-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 8px 15px;
            margin: 0 3px;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .zoom-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(102, 126, 234, 0.3);
        }
        .legend {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 25px;
            margin-top: 30px;
        }
        .legend-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #667eea;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .legend-item h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 1.2em;
        }
        .legend-item p {
            margin: 0;
            color: #666;
            line-height: 1.5;
        }
        .process-overview {
            background: linear-gradient(135deg, #e3f2fd, #bbdefb);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border-left: 6px solid #2196f3;
        }
        .process-overview h2 {
            margin: 0 0 20px 0;
            color: #1565c0;
            font-size: 1.8em;
        }
        .key-points {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .key-point {
            background: rgba(255,255,255,0.8);
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #4caf50;
        }
        .key-point strong {
            color: #2e7d32;
            display: block;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 Matching Process Overview</h1>
            <p>Comprehensive Guide to Automated Movement Matching</p>
        </div>
        
        <div class="content">
            <div class="process-overview">
                <h2>📋 What is the Matching Process?</h2>
                <p>The Matching Process is an automated system that identifies and links related financial movements across different position levels. It ensures accurate reconciliation by matching movements based on multiple criteria including amounts, dates, accounts, and references.</p>
                
                <div class="key-points">
                    <div class="key-point">
                        <strong>🎯 Purpose</strong>
                        Automatically match related financial movements to ensure accurate position reconciliation
                    </div>
                    <div class="key-point">
                        <strong>⚡ Automation</strong>
                        Reduces manual intervention by intelligently identifying matching candidates
                    </div>
                    <div class="key-point">
                        <strong>🔍 Quality-Based</strong>
                        Uses sophisticated scoring to ensure high-quality matches
                    </div>
                    <div class="key-point">
                        <strong>📊 Multi-Level</strong>
                        Processes movements across different position levels systematically
                    </div>
                </div>
            </div>
            
            <div class="diagram-section">
                <h2 style="margin-top: 0; color: #333; text-align: center;">🔄 Matching Process Flow</h2>
                <div class="diagram-container">
                    <div class="zoom-controls">
                        <button class="zoom-btn" onclick="zoomIn()">🔍 Zoom In</button>
                        <button class="zoom-btn" onclick="zoomOut()">🔍 Zoom Out</button>
                        <button class="zoom-btn" onclick="resetZoom()">↻ Reset</button>
                    </div>
                    <div class="mermaid" id="diagram">
flowchart TD
    START([🚀 Matching Process Starts]) --> INIT[📋 Initialize System Parameters]
    INIT --> CONFIG[⚙️ Load Configuration Settings]
    CONFIG --> LOOP[🔄 Start Position Level Processing]
    
    LOOP --> POS{📍 Select Position Level}
    POS -->|Level 6-9| HIGH[🏢 Process High-Level Positions]
    POS -->|Level 3-5| MID[🏬 Process Mid-Level Positions] 
    POS -->|Level 1-2| LOW[🏪 Process Low-Level Positions]
    POS -->|Pre-advice| PREADVICE[📨 Process Pre-advice Positions]
    
    HIGH --> QUALITY_CHECK[✅ Check Quality Configuration]
    MID --> QUALITY_CHECK
    LOW --> QUALITY_CHECK
    PREADVICE --> QUALITY_CHECK
    
    QUALITY_CHECK --> QUAL_OK{Quality Config Exists?}
    QUAL_OK -->|❌ No| NEXT_POS[➡️ Skip to Next Position]
    QUAL_OK -->|✅ Yes| SOURCE_SELECT[🎯 Select Source Movements]
    
    SOURCE_SELECT --> SOURCE_CRITERIA[📋 Apply Source Criteria:<br/>• Outstanding Status<br/>• Predict Status E/I<br/>• Amount Thresholds<br/>• Date Ranges]
    
    SOURCE_CRITERIA --> SOURCE_FOUND{Source Movement Found?}
    SOURCE_FOUND -->|❌ No| NEXT_POS
    SOURCE_FOUND -->|✅ Yes| SCHEDULER_CHECK[🔍 Check System Status]
    
    SCHEDULER_CHECK --> ENABLED{System Enabled?}
    ENABLED -->|❌ Disabled| CLEANUP[🧹 Clean Up & Exit]
    ENABLED -->|✅ Enabled| LOCK_SOURCE[🔒 Lock Source Movement]
    
    LOCK_SOURCE --> UPDATE_STAGE[📈 Update Match Stage]
    UPDATE_STAGE --> TARGET_STRATEGY[🎯 Determine Target Strategy]
    
    TARGET_STRATEGY --> STRATEGY{Target Strategy Type}
    STRATEGY -->|Reference-Based| REF_TARGETS[📄 Find Reference Matches]
    STRATEGY -->|Amount-Based| AMT_TARGETS[💰 Find Amount Matches]
    STRATEGY -->|Pre-advice| PREADVICE_TARGETS[📨 Find Pre-advice Matches]
    STRATEGY -->|Higher Position| HIGHER_TARGETS[⬆️ Find Higher Position Matches]
    
    REF_TARGETS --> TARGET_LOOP[🔄 Process Each Target]
    AMT_TARGETS --> TARGET_LOOP
    PREADVICE_TARGETS --> TARGET_LOOP
    HIGHER_TARGETS --> TARGET_LOOP
    
    TARGET_LOOP --> CALC_QUALITY[🎯 Calculate Match Quality]
    CALC_QUALITY --> QUALITY_FACTORS[📊 Evaluate Quality Factors:<br/>• Date Match (A=5, B=4, E=1)<br/>• Amount Match (A=5, B=4, E=1)<br/>• Account Match (A=5, B=4, E=1)<br/>• Party Matches<br/>• Reference Matches]
    
    QUALITY_FACTORS --> QUALITY_SCORE[📈 Calculate Final Quality Score]
    QUALITY_SCORE --> QUALITY_OK_CHECK{Quality Score Acceptable?}
    
    QUALITY_OK_CHECK -->|❌ Poor Quality| REJECT_TARGET[❌ Reject Target]
    QUALITY_OK_CHECK -->|✅ Good Quality| VALIDATE_TARGET[✅ Validate Target]
    
    VALIDATE_TARGET --> CROSS_REF_CHECK[🔍 Check Cross References]
    CROSS_REF_CHECK --> REF_VALID{References Valid?}
    
    REF_VALID -->|✅ Valid| ACCEPT_TARGET[✅ Accept Target for Matching]
    REF_VALID -->|❌ Invalid| REJECT_TARGET
    
    ACCEPT_TARGET --> INSERT_TARGET[📝 Insert into Target Table]
    INSERT_TARGET --> MORE_TARGETS{More Targets to Process?}
    
    REJECT_TARGET --> MORE_TARGETS
    MORE_TARGETS -->|✅ Yes| TARGET_LOOP
    MORE_TARGETS -->|❌ No| AMOUNT_TOTAL[🧮 Calculate Amount Totals]
    
    AMOUNT_TOTAL --> INNER_PROCESSING[🔄 Inner Target Processing]
    INNER_PROCESSING --> MATCH_ACTIONS[⚡ Apply Match Actions]
    
    MATCH_ACTIONS --> ACTION_TYPE{Match Action Type}
    ACTION_TYPE -->|A| AUTO_MATCH[🤖 Auto Match]
    ACTION_TYPE -->|B| OFFER_MATCH[💡 Offer Match]
    ACTION_TYPE -->|C| CONFIRM_MATCH[✅ Confirm Match]
    ACTION_TYPE -->|D| DECLINE_MATCH[❌ Decline Match]
    ACTION_TYPE -->|E| EXCEPTION_MATCH[⚠️ Exception Match]
    ACTION_TYPE -->|N| NO_ACTION[⏸️ No Action]
    
    AUTO_MATCH --> UPDATE_STATUS[📊 Update Match Status]
    OFFER_MATCH --> UPDATE_STATUS
    CONFIRM_MATCH --> UPDATE_STATUS
    EXCEPTION_MATCH --> UPDATE_STATUS
    
    DECLINE_MATCH --> REMOVE_TARGET[🗑️ Remove from Processing]
    NO_ACTION --> REMOVE_TARGET
    
    UPDATE_STATUS --> COMPLETION_CHECK[🏁 Check Match Completion]
    REMOVE_TARGET --> COMPLETION_CHECK
    
    COMPLETION_CHECK --> COMPLETE{Match Complete?}
    COMPLETE -->|✅ Yes| FINALIZE[🎉 Finalize Match]
    COMPLETE -->|❌ No| MORE_SOURCES{More Sources?}
    
    MORE_SOURCES -->|✅ Yes| SOURCE_SELECT
    MORE_SOURCES -->|❌ No| NEXT_POS
    
    FINALIZE --> UPDATE_TABLES[📊 Update Match Tables]
    UPDATE_TABLES --> CLEAN_LOCKS[🔓 Remove Locks]
    CLEAN_LOCKS --> NEXT_POS
    
    NEXT_POS --> MORE_POSITIONS{More Positions?}
    MORE_POSITIONS -->|✅ Yes| LOOP
    MORE_POSITIONS -->|❌ No| END_SUCCESS[🎉 Process Complete]
    
    CLEANUP --> END_DISABLED[⏹️ Process Disabled]
    
    style START fill:#e8f5e8
    style END_SUCCESS fill:#e8f5e8
    style END_DISABLED fill:#ffebee
    style ENABLED fill:#fff3e0
    style QUALITY_OK_CHECK fill:#f3e5f5
    style REF_VALID fill:#e3f2fd
    style COMPLETE fill:#fff8e1
    style AUTO_MATCH fill:#e8f5e8
    style DECLINE_MATCH fill:#ffebee
                    </div>
                </div>
            </div>
            
            <div class="legend">
                <div class="legend-item">
                    <h4>🎯 Quality Scoring System</h4>
                    <p><strong>A = 5:</strong> Perfect match (exact values)<br>
                    <strong>B = 4:</strong> Good match (within tolerance)<br>
                    <strong>C = 3:</strong> Fair match<br>
                    <strong>D = 2:</strong> Poor match<br>
                    <strong>E = 1:</strong> Bad match (significant differences)</p>
                </div>
                
                <div class="legend-item">
                    <h4>⚡ Match Actions</h4>
                    <p><strong>A - Auto:</strong> Automatically confirm the match<br>
                    <strong>B - Offer:</strong> Present as potential match<br>
                    <strong>C - Confirm:</strong> Require manual confirmation<br>
                    <strong>D - Decline:</strong> Reject the match<br>
                    <strong>E - Exception:</strong> Flag for special handling<br>
                    <strong>N - None:</strong> No action taken</p>
                </div>
                
                <div class="legend-item">
                    <h4>📍 Position Levels</h4>
                    <p><strong>High (6-9):</strong> External positions, bank statements<br>
                    <strong>Mid (3-5):</strong> Internal positions, trade confirmations<br>
                    <strong>Low (1-2):</strong> Settlement positions, final confirmations<br>
                    <strong>Pre-advice:</strong> Preliminary notifications</p>
                </div>
                
                <div class="legend-item">
                    <h4>🔍 Matching Criteria</h4>
                    <p><strong>Amount:</strong> Exact or within tolerance<br>
                    <strong>Date:</strong> Value date matching<br>
                    <strong>Account:</strong> Same or linked accounts<br>
                    <strong>References:</strong> Cross-reference validation<br>
                    <strong>Parties:</strong> Counterparty, beneficiary matching</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentZoom = 1;
        
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true,
                curve: 'basis'
            }
        });
        
        function zoomIn() {
            currentZoom += 0.2;
            applyZoom();
        }
        
        function zoomOut() {
            currentZoom = Math.max(0.3, currentZoom - 0.2);
            applyZoom();
        }
        
        function resetZoom() {
            currentZoom = 1;
            applyZoom();
        }
        
        function applyZoom() {
            const diagram = document.getElementById('diagram');
            diagram.style.transform = `scale(${currentZoom})`;
        }
    </script>
</body>
</html>
