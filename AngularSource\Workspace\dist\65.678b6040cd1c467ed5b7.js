(window.webpackJsonp=window.webpackJsonp||[]).push([[65],{vcQW:function(l,t,n){"use strict";n.r(t);var i=n("CcnG"),e=n("mrSG"),u=n("447K"),a=n("ZYCi"),o=n("sMtq"),r=n("xRo1"),b=function(l){function t(t,n){var i=l.call(this,n,t)||this;return i.commonService=t,i.element=n,i.inputData=new u.G(i.commonService),i.requestParams=[],i.baseURL=u.Wb.getBaseURL(),i.actionPath="",i.actionMethod="",i.jsonReader=new u.L,i.guiFacilityArray=[],i.xmlAsString="",i.swtalert=new u.bb(t),i}return e.d(t,l),t.prototype.ngOnInit=function(){this.subGuiGrid=this.subGuiCanvas.addChild(u.hb),this.scenarioIdLbl.text=u.Wb.getPredictMessage("scenario.scenarioId",null),this.guiFacilityId.text=u.Wb.getPredictMessage("scenario.guiHighlight.facilityId",null),this.reqScenLbl.text=u.Wb.getPredictMessage("scenario.guiHighlight.reqScenario",null),this.parameterIdLbl.text=u.Wb.getPredictMessage("scenario.guiHighlight.paramId",null),this.desLbl.text=u.Wb.getPredictMessage("scenario.guiHighlight.decription",null),this.mapFromLbl.text=u.Wb.getPredictMessage("scenario.guiHighlight.mapFrom",null),this.updateButton.label=u.Wb.getPredictMessage("button.update",null),this.saveButton.label=u.Wb.getPredictMessage("button.save",null),this.cancelButton.label=u.Wb.getPredictMessage("button.cancel",null),this.showXMLButton.label=u.Wb.getPredictMessage("screen.showXML",null),this.guiFacilityCombo.required=!0},t.prototype.onLoad=function(){var l,t=this;this.subGuiGrid.onRowClick=function(){t.rowClick()},window.opener&&window.opener.instanceElement&&(l=window.opener.instanceElement.sendDataToSub(),this.subGuiGrid.CustomGrid(l.gridData.metadata),this.scenarioIdtxt.text=l.scenarioId,this.guiFacilityCombo.dataProvider=l.guiFacilityList.option,this.guiFacilityArray=l.guiFacilityArray,"add"!=l.operation&&(this.guiFacilityCombo.selectedLabel=l.selctedFacilityId,this.selectedGuiFacility.text=l.selctedFacilityDescription,this.xmlAsString=l.parameterXML,this.showXMLButton.enabled=!0,setTimeout(function(){t.generateGridRows()},0)))},t.prototype.changeGuiFacility=function(){var l=this;this.selectedGuiFacility.text=this.guiFacilityCombo.selectedValue,this.subGuiGrid.gridData={row:[],size:0},""!=this.guiFacilityCombo.selectedLabel?(this.showXMLButton.enabled=!0,this.requestParams=[],this.actionPath="scenMaintenance.do?",this.actionMethod="method=getGuiFacilityData",this.inputData.cbResult=function(t){l.inputDataResult(t)},this.requestParams.selectedGuiFacility=this.guiFacilityCombo.selectedLabel,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)):this.showXMLButton.enabled=!1},t.prototype.rowClick=function(){this.subGuiGrid.selectedIndex>=0?(this.parameterIdTxt.text=this.subGuiGrid.selectedItem.subGuiId.content,this.descTxt.text=this.subGuiGrid.selectedItem.subGuiDescription.content,this.mapFromTxt.text=this.subGuiGrid.selectedItem.subGuiMapFrom.content,this.updateButton.enabled=!0,this.mapFromTxt.enabled=!0):(this.parameterIdTxt.text="",this.descTxt.text="",this.mapFromTxt.text="",this.updateButton.enabled=!1,this.mapFromTxt.enabled=!1)},t.prototype.inputDataResult=function(l){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=l,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.xmlAsString=this.jsonReader.getSingletons().parameterXML,this.generateGridRows(),this.reqScenCheck.selected="Y"==this.jsonReader.getSingletons().requireScInstance)},t.prototype.generateGridRows=function(){for(var l=r.xml2js(this.xmlAsString,{object:!1,reversible:!1,coerce:!1,sanitize:!0,trim:!0,arrayNotation:!1,alternateTextNode:!1,compact:!0,emptyTag:"{}"}).requiredParameters.parameter,t=0;t<l.length;t++){var n;n={subGuiReq:{content:l[t].isMandatory._text},subGuiId:{content:l[t].name._text},subGuiDescription:{content:l[t].description._text},subGuiType:{content:l[t].dataType._text},subGuiMapFrom:{content:l[t].mapFrom?l[t].mapFrom._text:""}},this.subGuiGrid.appendRow(n,!0)}},t.prototype.inputDataFault=function(){this.swtalert.error(u.Wb.getPredictMessage("alert.generic_exception"))},t.prototype.updateHandle=function(){this.subGuiGrid.dataProvider[this.subGuiGrid.selectedIndex].subGuiMapFrom=this.mapFromTxt.text,this.subGuiGrid.dataProvider[this.subGuiGrid.selectedIndex].slickgrid_rowcontent.subGuiMapFrom.content=this.mapFromTxt.text,this.subGuiGrid.refresh();var l=new DOMParser;this.xmlDocument=l.parseFromString(this.xmlAsString,"text/xml");var t=this.xmlDocument.createElement("mapFrom"),n=this.xmlDocument.getElementsByTagName("parameter")[this.subGuiGrid.selectedIndex];n.appendChild(t),n.getElementsByTagName("mapFrom")[0].textContent=this.mapFromTxt.text,null!=n.getElementsByTagName("mapFrom")[1]&&n.removeChild(n.getElementsByTagName("mapFrom")[1]);var i=new XMLSerializer;this.xmlAsString=i.serializeToString(this.xmlDocument)},t.prototype.saveHandler=function(){""==this.guiFacilityCombo.selectedLabel?this.swtalert.warning("Please select GUI facility"):-1!=this.guiFacilityArray.indexOf(this.guiFacilityCombo.selectedLabel)?this.swtalert.warning("Gui facility already exists"):window.opener&&window.opener.instanceElement&&(window.opener.instanceElement.refreshParent(this.guiFacilityCombo.selectedLabel+"-"+this.guiFacilityCombo.selectedValue,this.xmlAsString),u.x.call("close"))},t.prototype.cancelHandler=function(){u.x.call("close")},t.prototype.showXmlHandler=function(){this.win=u.Eb.createPopUp(this,o.a,{title:"Show XML",xmlData:this.xmlAsString}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="400",this.win.height="500",this.win.showControls=!0,this.win.id="guiShowXML",this.win.display()},t}(u.yb),d=[{path:"",component:b}],c=(a.l.forChild(d),function(){return function(){}}()),s=n("pMnS"),h=n("RChO"),g=n("t6HQ"),m=n("WFGK"),p=n("5FqG"),w=n("Ip0R"),I=n("gIcY"),R=n("t/Na"),G=n("sE5F"),x=n("OzfB"),f=n("T7CS"),y=n("S7LP"),F=n("6aHO"),C=n("WzUx"),L=n("A7o+"),S=n("zCE2"),v=n("Jg5P"),T=n("3R0m"),J=n("hhbb"),B=n("5rxC"),A=n("Fzqc"),k=n("21Lb"),M=n("hUWP"),_=n("3pJQ"),D=n("V9q+"),P=n("VDKW"),H=n("kXfT"),q=n("BGbe");n.d(t,"GuiHighlightAddModuleNgFactory",function(){return W}),n.d(t,"RenderType_GuiHighlightAdd",function(){return N}),n.d(t,"View_GuiHighlightAdd_0",function(){return O}),n.d(t,"View_GuiHighlightAdd_Host_0",function(){return Z}),n.d(t,"GuiHighlightAddNgFactory",function(){return E});var W=i.Gb(c,[],function(l){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[s.a,h.a,g.a,m.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,E]],[3,i.n],i.J]),i.Rb(4608,w.m,w.l,[i.F,[2,w.u]]),i.Rb(4608,I.c,I.c,[]),i.Rb(4608,I.p,I.p,[]),i.Rb(4608,R.j,R.p,[w.c,i.O,R.n]),i.Rb(4608,R.q,R.q,[R.j,R.o]),i.Rb(5120,R.a,function(l){return[l,new u.tb]},[R.q]),i.Rb(4608,R.m,R.m,[]),i.Rb(6144,R.k,null,[R.m]),i.Rb(4608,R.i,R.i,[R.k]),i.Rb(6144,R.b,null,[R.i]),i.Rb(4608,R.f,R.l,[R.b,i.B]),i.Rb(4608,R.c,R.c,[R.f]),i.Rb(4608,G.c,G.c,[]),i.Rb(4608,G.g,G.b,[]),i.Rb(5120,G.i,G.j,[]),i.Rb(4608,G.h,G.h,[G.c,G.g,G.i]),i.Rb(4608,G.f,G.a,[]),i.Rb(5120,G.d,G.k,[G.h,G.f]),i.Rb(5120,i.b,function(l,t){return[x.j(l,t)]},[w.c,i.O]),i.Rb(4608,f.a,f.a,[]),i.Rb(4608,y.a,y.a,[]),i.Rb(4608,F.a,F.a,[i.n,i.L,i.B,y.a,i.g]),i.Rb(4608,C.c,C.c,[i.n,i.g,i.B]),i.Rb(4608,C.e,C.e,[C.c]),i.Rb(4608,L.l,L.l,[]),i.Rb(4608,L.h,L.g,[]),i.Rb(4608,L.c,L.f,[]),i.Rb(4608,L.j,L.d,[]),i.Rb(4608,L.b,L.a,[]),i.Rb(4608,L.k,L.k,[L.l,L.h,L.c,L.j,L.b,L.m,L.n]),i.Rb(4608,C.i,C.i,[[2,L.k]]),i.Rb(4608,C.r,C.r,[C.L,[2,L.k],C.i]),i.Rb(4608,C.t,C.t,[]),i.Rb(4608,C.w,C.w,[]),i.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),i.Rb(1073742336,w.b,w.b,[]),i.Rb(1073742336,I.n,I.n,[]),i.Rb(1073742336,I.l,I.l,[]),i.Rb(1073742336,S.a,S.a,[]),i.Rb(1073742336,v.a,v.a,[]),i.Rb(1073742336,I.e,I.e,[]),i.Rb(1073742336,T.a,T.a,[]),i.Rb(1073742336,L.i,L.i,[]),i.Rb(1073742336,C.b,C.b,[]),i.Rb(1073742336,R.e,R.e,[]),i.Rb(1073742336,R.d,R.d,[]),i.Rb(1073742336,G.e,G.e,[]),i.Rb(1073742336,J.b,J.b,[]),i.Rb(1073742336,B.b,B.b,[]),i.Rb(1073742336,x.c,x.c,[]),i.Rb(1073742336,A.a,A.a,[]),i.Rb(1073742336,k.d,k.d,[]),i.Rb(1073742336,M.c,M.c,[]),i.Rb(1073742336,_.a,_.a,[]),i.Rb(1073742336,D.a,D.a,[[2,x.g],i.O]),i.Rb(1073742336,P.b,P.b,[]),i.Rb(1073742336,H.a,H.a,[]),i.Rb(1073742336,q.b,q.b,[]),i.Rb(1073742336,u.Tb,u.Tb,[]),i.Rb(1073742336,c,c,[]),i.Rb(256,R.n,"XSRF-TOKEN",[]),i.Rb(256,R.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,L.m,void 0,[]),i.Rb(256,L.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,a.i,function(){return[[{path:"",component:b}]]},[])])}),z=[[""]],N=i.Hb({encapsulation:0,styles:z,data:{}});function O(l){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{scenarioIdLbl:0}),i.Zb(402653184,3,{guiFacilityId:0}),i.Zb(402653184,4,{selectedGuiFacility:0}),i.Zb(402653184,5,{reqScenLbl:0}),i.Zb(402653184,6,{desLbl:0}),i.Zb(402653184,7,{parameterIdLbl:0}),i.Zb(402653184,8,{mapFromLbl:0}),i.Zb(402653184,9,{scenarioIdtxt:0}),i.Zb(402653184,10,{mapFromTxt:0}),i.Zb(402653184,11,{descTxt:0}),i.Zb(402653184,12,{parameterIdTxt:0}),i.Zb(402653184,13,{guiFacilityCombo:0}),i.Zb(402653184,14,{reqScenCheck:0}),i.Zb(402653184,15,{subGuiCanvas:0}),i.Zb(402653184,16,{updateButton:0}),i.Zb(402653184,17,{saveButton:0}),i.Zb(402653184,18,{cancelButton:0}),i.Zb(402653184,19,{showXMLButton:0}),(l()(),i.Jb(19,0,null,null,95,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(l,t,n){var i=!0,e=l.component;"creationComplete"===t&&(i=!1!==e.onLoad()&&i);return i},p.ad,p.hb)),i.Ib(20,4440064,null,0,u.yb,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(l()(),i.Jb(21,0,null,0,93,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(22,4440064,null,0,u.ec,[i.r,u.i,i.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(l()(),i.Jb(23,0,null,0,81,"SwtCanvas",[["height","90%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(24,4440064,null,0,u.db,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(25,0,null,0,79,"VBox",[["height","100%"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(26,4440064,null,0,u.ec,[i.r,u.i,i.T],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(27,0,null,0,35,"Grid",[["height","20%"],["paddingTop","10"],["width","100%"]],null,null,null,p.Cc,p.H)),i.Ib(28,4440064,null,0,u.z,[i.r,u.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(l()(),i.Jb(29,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(30,4440064,null,0,u.B,[i.r,u.i],null,null),(l()(),i.Jb(31,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,p.Ac,p.I)),i.Ib(32,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(l()(),i.Jb(33,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(34,4440064,[[2,4],["scenarioIdLbl",4]],0,u.vb,[i.r,u.i],null,null),(l()(),i.Jb(35,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(36,4440064,null,0,u.A,[i.r,u.i],null,null),(l()(),i.Jb(37,0,null,0,1,"SwtTextInput",[["enabled","false"],["width","200"]],null,null,null,p.kd,p.sb)),i.Ib(38,4440064,[[9,4],["scenarioIdtxt",4]],0,u.Rb,[i.r,u.i],{width:[0,"width"],enabled:[1,"enabled"]},null),(l()(),i.Jb(39,0,null,0,13,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(40,4440064,null,0,u.B,[i.r,u.i],null,null),(l()(),i.Jb(41,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,p.Ac,p.I)),i.Ib(42,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(l()(),i.Jb(43,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(44,4440064,[[3,4],["guiFacilityId",4]],0,u.vb,[i.r,u.i],null,null),(l()(),i.Jb(45,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(46,4440064,null,0,u.A,[i.r,u.i],null,null),(l()(),i.Jb(47,0,null,0,1,"SwtComboBox",[["dataLabel","guiHighlightFacilityList"]],null,[[null,"change"],["window","mousewheel"]],function(l,t,n){var e=!0,u=l.component;"window:mousewheel"===t&&(e=!1!==i.Tb(l,48).mouseWeelEventHandler(n.target)&&e);"change"===t&&(e=!1!==u.changeGuiFacility()&&e);return e},p.Pc,p.W)),i.Ib(48,4440064,[[13,4],["guiFacilityCombo",4]],0,u.gb,[i.r,u.i],{dataLabel:[0,"dataLabel"]},{change_:"change"}),(l()(),i.Jb(49,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(50,4440064,null,0,u.A,[i.r,u.i],null,null),(l()(),i.Jb(51,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,p.Yc,p.fb)),i.Ib(52,4440064,[[4,4],["selectedGuiFacility",4]],0,u.vb,[i.r,u.i],{fontWeight:[0,"fontWeight"]},null),(l()(),i.Jb(53,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(54,4440064,null,0,u.B,[i.r,u.i],null,null),(l()(),i.Jb(55,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,p.Ac,p.I)),i.Ib(56,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(l()(),i.Jb(57,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(58,4440064,[[5,4],["reqScenLbl",4]],0,u.vb,[i.r,u.i],null,null),(l()(),i.Jb(59,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(60,4440064,null,0,u.A,[i.r,u.i],null,null),(l()(),i.Jb(61,0,null,0,1,"SwtCheckBox",[["enabled","false"]],null,null,null,p.Oc,p.V)),i.Ib(62,4440064,[[14,4],["reqScenCheck",4]],0,u.eb,[i.r,u.i],{enabled:[0,"enabled"]},null),(l()(),i.Jb(63,0,null,0,1,"SwtCanvas",[["height","53%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(64,4440064,[[15,4],["subGuiCanvas",4]],0,u.db,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(65,0,null,0,39,"Grid",[["height","20%"],["paddingTop","10"],["width","100%"]],null,null,null,p.Cc,p.H)),i.Ib(66,4440064,null,0,u.z,[i.r,u.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(l()(),i.Jb(67,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(68,4440064,null,0,u.B,[i.r,u.i],null,null),(l()(),i.Jb(69,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,p.Ac,p.I)),i.Ib(70,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(l()(),i.Jb(71,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(72,4440064,[[7,4],["parameterIdLbl",4]],0,u.vb,[i.r,u.i],null,null),(l()(),i.Jb(73,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(74,4440064,null,0,u.A,[i.r,u.i],null,null),(l()(),i.Jb(75,0,null,0,1,"SwtText",[],null,null,null,p.ld,p.qb)),i.Ib(76,4440064,[[12,4],["parameterIdTxt",4]],0,u.Pb,[i.r,u.i],null,null),(l()(),i.Jb(77,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(78,4440064,null,0,u.B,[i.r,u.i],null,null),(l()(),i.Jb(79,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,p.Ac,p.I)),i.Ib(80,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(l()(),i.Jb(81,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(82,4440064,[[6,4],["desLbl",4]],0,u.vb,[i.r,u.i],null,null),(l()(),i.Jb(83,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(84,4440064,null,0,u.A,[i.r,u.i],null,null),(l()(),i.Jb(85,0,null,0,1,"SwtText",[],null,null,null,p.ld,p.qb)),i.Ib(86,4440064,[[11,4],["descTxt",4]],0,u.Pb,[i.r,u.i],null,null),(l()(),i.Jb(87,0,null,0,9,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(88,4440064,null,0,u.B,[i.r,u.i],null,null),(l()(),i.Jb(89,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,p.Ac,p.I)),i.Ib(90,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(l()(),i.Jb(91,0,null,0,1,"SwtLabel",[],null,null,null,p.Yc,p.fb)),i.Ib(92,4440064,[[8,4],["mapFromLbl",4]],0,u.vb,[i.r,u.i],null,null),(l()(),i.Jb(93,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(94,4440064,null,0,u.A,[i.r,u.i],null,null),(l()(),i.Jb(95,0,null,0,1,"SwtTextInput",[["enabled","false"],["width","200"]],null,null,null,p.kd,p.sb)),i.Ib(96,4440064,[[10,4],["mapFromTxt",4]],0,u.Rb,[i.r,u.i],{width:[0,"width"],enabled:[1,"enabled"]},null),(l()(),i.Jb(97,0,null,0,7,"GridRow",[],null,null,null,p.Bc,p.J)),i.Ib(98,4440064,null,0,u.B,[i.r,u.i],null,null),(l()(),i.Jb(99,0,null,0,1,"GridItem",[["width","15%"]],null,null,null,p.Ac,p.I)),i.Ib(100,4440064,null,0,u.A,[i.r,u.i],{width:[0,"width"]},null),(l()(),i.Jb(101,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(102,4440064,null,0,u.A,[i.r,u.i],null,null),(l()(),i.Jb(103,0,null,0,1,"SwtButton",[["enabled","false"]],null,[[null,"click"]],function(l,t,n){var i=!0,e=l.component;"click"===t&&(i=!1!==e.updateHandle()&&i);return i},p.Mc,p.T)),i.Ib(104,4440064,[[16,4],["updateButton",4]],0,u.cb,[i.r,u.i],{enabled:[0,"enabled"]},{onClick_:"click"}),(l()(),i.Jb(105,0,null,0,9,"SwtCanvas",[["height","7%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(106,4440064,null,0,u.db,[i.r,u.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(107,0,null,0,7,"HBox",[["horizontalGap","5"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(108,4440064,null,0,u.C,[i.r,u.i],{horizontalGap:[0,"horizontalGap"],width:[1,"width"]},null),(l()(),i.Jb(109,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(l,t,n){var i=!0,e=l.component;"click"===t&&(i=!1!==e.saveHandler()&&i);return i},p.Mc,p.T)),i.Ib(110,4440064,[[17,4],["saveButton",4]],0,u.cb,[i.r,u.i],null,{onClick_:"click"}),(l()(),i.Jb(111,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(l,t,n){var i=!0,e=l.component;"click"===t&&(i=!1!==e.cancelHandler()&&i);return i},p.Mc,p.T)),i.Ib(112,4440064,[[18,4],["cancelButton",4]],0,u.cb,[i.r,u.i],null,{onClick_:"click"}),(l()(),i.Jb(113,0,null,0,1,"SwtButton",[["enabled","false"]],null,[[null,"click"]],function(l,t,n){var i=!0,e=l.component;"click"===t&&(i=!1!==e.showXmlHandler()&&i);return i},p.Mc,p.T)),i.Ib(114,4440064,[[19,4],["showXMLButton",4]],0,u.cb,[i.r,u.i],{enabled:[0,"enabled"]},{onClick_:"click"})],function(l,t){l(t,20,0,"100%","100%");l(t,22,0,"100%","100%","5","5","5","5");l(t,24,0,"100%","90%");l(t,26,0,"100%","100%");l(t,28,0,"100%","20%","10"),l(t,30,0);l(t,32,0,"25%"),l(t,34,0),l(t,36,0);l(t,38,0,"200","false"),l(t,40,0);l(t,42,0,"25%"),l(t,44,0),l(t,46,0);l(t,48,0,"guiHighlightFacilityList"),l(t,50,0);l(t,52,0,"normal"),l(t,54,0);l(t,56,0,"25%"),l(t,58,0),l(t,60,0);l(t,62,0,"false");l(t,64,0,"100%","53%");l(t,66,0,"100%","20%","10"),l(t,68,0);l(t,70,0,"15%"),l(t,72,0),l(t,74,0),l(t,76,0),l(t,78,0);l(t,80,0,"15%"),l(t,82,0),l(t,84,0),l(t,86,0),l(t,88,0);l(t,90,0,"15%"),l(t,92,0),l(t,94,0);l(t,96,0,"200","false"),l(t,98,0);l(t,100,0,"15%"),l(t,102,0);l(t,104,0,"false");l(t,106,0,"100%","7%");l(t,108,0,"5","100%"),l(t,110,0),l(t,112,0);l(t,114,0,"false")},null)}function Z(l){return i.dc(0,[(l()(),i.Jb(0,0,null,null,1,"app-gui-highlight-add",[],null,null,null,O,N)),i.Ib(1,4440064,null,0,b,[u.i,i.r],null,null)],function(l,t){l(t,1,0)},null)}var E=i.Fb("app-gui-highlight-add",b,Z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);