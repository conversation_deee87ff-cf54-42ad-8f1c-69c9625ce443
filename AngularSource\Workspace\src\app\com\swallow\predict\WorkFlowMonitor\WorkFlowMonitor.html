<SwtModule (creationComplete)='onLoad()' width="100%" height="100%">
  <VBox width="100%" height="100%" paddingTop="5" paddingLeft="5" paddingRight="5" paddingBottom="0" verticalGap="1">
    <SwtCanvas width="100%" height="80" #filterContainer minWidth="1100">
      <HBox width="100%" height="100%">
        <HBox width="35%" minWidth="300" height="100%" paddingTop="5" paddingBottom="5">
          <Grid width="100%" height="100%">
            <GridRow width="100%" height="100%">
              <GridItem width="130" height="100%">
                <SwtLabel #lblComboEntity text="Entity"
                          styleName="labelBold"
                          paddingLeft="5"></SwtLabel>
              </GridItem>
              <GridItem width="135" height="100%">
                <SwtComboBox  #entityCombo dataLabel="entity"
                             toolTip="Entity"
                             width="135"
                             id="entityCombo"
                             (change)="changeCombo($event)"
                             (focusOut)="comboFocusOutHandler($event)">
                </SwtComboBox>
              </GridItem>
              <GridItem width="43%" height="100%">
                <SwtLabel #selectedEntity id="selectedEntity" paddingLeft="10"
                          styleName="labelLeft" fontWeight="normal"></SwtLabel>
              </GridItem>
            </GridRow>
            <GridRow width="100%" height="100%">
              <GridItem width="130" height="100%">
                <SwtLabel #lblComboCcy text="Currency Group" paddingLeft="5" styleName="labelBold"></SwtLabel>
              </GridItem>
              <GridItem width="135" height="100%">
                <SwtComboBox #ccyCombo dataLabel="currency"
                             toolTip='Select currency code'
                             id="ccyCombo"
                             width="135"
                             (change)="changeCombo($event)"
                             (focusout)="comboFocusOutHandler($event)"></SwtComboBox>
              </GridItem>
              <GridItem width="43%" height="100%" paddingLeft="10">
                <SwtLabel id="selectedCcy" #selectedCcy
                          styleName="labelRight" fontWeight="normal"></SwtLabel>
              </GridItem>
            </GridRow>
          </Grid>
        </HBox>
        <HBox  height="100%" width="65%">
          <Grid verticalGap="2" width="100%" height="100%">
            <GridRow width="100%" height="72%">
              <HBox width="100%" horizontalAlign="right" paddingRight="7">
              <GridItem>
                <SwtLabel id="statusLabel" #statusLabel fontWeight="bold" width="60">
                </SwtLabel>
              </GridItem>
              <GridItem >
                <SwtRadioButtonGroup #status id="status"  align="horizontal" width="100%" (change)="changeStatus()">
                  <VBox width="100%" height="100%">
                  <HBox width="100%" height="50%">
                  <SwtRadioItem value="All" width="75" groupName="status" id="all" #all>
                  </SwtRadioItem>
                  <SwtRadioItem value=""  width="75" groupName="status" id="allOpen" selected="true" #allOpen></SwtRadioItem>
                  <SwtRadioItem value="A" width="75" groupName="status" id="active" #active >
                  </SwtRadioItem>  
                  </HBox>   
                  <HBox width="100%" height="50%">            
                  <SwtRadioItem value="P" width="75" groupName="status" id="pending" #pending></SwtRadioItem>
                  <SwtRadioItem value="O" width="75" groupName="status" id="overdue" #overdue></SwtRadioItem>
                  <SwtRadioItem value="R" width="75" groupName="status" id="resolved" #resolved>
                  </SwtRadioItem>
                </HBox>
                </VBox>
                </SwtRadioButtonGroup>
              </GridItem>
              <GridItem paddingTop="16">
                <SwtLabel id="resolvedOnLbl" #resolvedOnLbl  fontWeight="normal" width="85">
                </SwtLabel>
              </GridItem>
              <GridItem paddingTop="16">
              <SwtDateField id="resolvedOnDate" #resolvedOnDate (change)="updateData('tree')"
              width="70" enabled="false"></SwtDateField>
              </GridItem>
            </HBox>
            </GridRow>
            <GridRow width="100%" height="28%">
              <GridItem width="250">

              <SwtTabNavigator #tabCategoryList id="tabCategoryList" width="100%" height="100%"
                               (onChange)="updateData('workFlowRefresh')" borderBottom="false"></SwtTabNavigator>
              </GridItem> 
             
              <HBox width="100%" horizontalAlign="right">
                              <GridItem verticalAlign="bottom" horizontalAlign="right" paddingBottom="5">
                                <SwtLabel width="170" fontWeight="bold" #labelExc3txtcurrLabel text="Apply Currency Threshold"></SwtLabel>
                                <SwtCheckBox toolTip="Apply Currency Threshold" id="currencyThreshold" #currencyThreshold
                                  (change)="changeCombo($event)"></SwtCheckBox>
                              </GridItem>
                            </HBox>
            </GridRow>
          </Grid>
        </HBox>
        <!--<HBox width="17%" height="100%"  horizontalAlign="right">
          <Grid height="100%" width ="100%" paddingTop="-5" verticalGap="-1">
            <GridRow  width="100%" height="20%">             
            </GridRow>
            <GridRow  width="100%" height="60%">
              <!--<GridItem>-->
              <!--<SwtLabel text="Server Date "-->
              <!--styleName="labelBold"></SwtLabel>-->
              <!--</GridItem>-->
              <!--<GridItem>-->
              <!--<SwtLabel id="dateLabel"></SwtLabel>-->
              <!--</GridItem>-->
              <!--<GridItem></GridItem>-->
              <!--<GridItem></GridItem>-->
            <!--</GridRow>
            <!--<GridRow height="30%" width="520">
              <!--<GridItem verticalAlign="bottom" visible="false" includeInLayout="false">-->
              <!--<SwtLabel text="Server Time " styleName="labelBold" visible="false" includeInLayout="false"></SwtLabel>-->
              <!--</GridItem>-->
              <!--<GridItem verticalAlign="bottom">-->
              <!--<SwtLabel id="timeLabel" visible="false" includeInLayout="false"></SwtLabel>-->
              <!--</GridItem>-->
              <!--<GridItem></GridItem>-->
              <!--<GridItem></GridItem>-->
            <!---  <GridItem width="325"></GridItem>
              <GridItem verticalAlign="bottom" horizontalAlign="right" paddingBottom="5" paddingRight="5">
                <SwtLabel width="170" fontWeight="bold" #labelExc3txtcurrLabel
                          text="Apply Currency Threshold"></SwtLabel>
                <SwtCheckBox toolTip="Apply Currency Threshold"
                             id="currencyThreshold" #currencyThreshold
                             (change)="changeCombo($event)"></SwtCheckBox>
              </GridItem>
            </GridRow>-->
          <!---</Grid>
        </HBox>-->
      </HBox>
    </SwtCanvas>
      <SwtCanvas width="100%" height="80%" minWidth="1100"  minHeight="450" #mainCanvas style="border: 1px solid gray" border="false">
      <HBox id="mainHGroup" width="100%" height="100%" paddingTop="5" paddingBottom="5">
        <HDividedBox  liveDrag="true" id="treeDivider" #treeDivider  width="100%" height="100%" 
        minHeight="5" extendedDividedBox="true" dividersAnimation="W" styleName="ExtendedDivider">
        <VBox class="left"  id="vbox" #vbox width="30%"  height="100%">

          <Grid width="100%" height="89%" verticalGap="0" paddingLeft="5">
            <GridRow width="100%" height="5%">
              <GridItem width="100%" height="100%">
                <SwtTabNavigator #tabList id="tabList" width="100%" height="100%"
                                 (onChange)="updateData('workFlowRefresh')" borderBottom="false"></SwtTabNavigator>
              </GridItem>
            </GridRow>
            <GridRow width="100%" height="95%">
              <GridItem width="100%" height="100%">
                <SwtCanvas width="100%" height="100%">
                  <Grid width="100%" height="100%" id="flowGrid" verticalGap="0">
                    <GridRow width="100%" height="100%">
                      <GridItem width="100%" height="100%">
                        <HBox width="100%" height="100%" paddingLeft="3" paddingRight="10">
                          <VBox  width="100%" height="100%">
                            <HBox id="titleGroup" width="100%" height="40" horizontalAlign="right" paddingRight="3" >
                              <VBox height="100%" paddingLeft="{gridIncTotal.x+gridIncTotal.width-90}" id="includedVbox" verticalGap="0">
                                <SwtText #incTxt
                                         fontWeight="bold"
                                         width="{mvtTxt.width}"
                                         paddingTop="5"
                                         text="Included" textAlign="right">
                                </SwtText>
                                <SwtText id="mvtTxt" #mvtTxt
                                         text="Movements"
                                         textAlign="left"
                                         fontWeight="bold"
                                         paddingTop="5">
                                </SwtText>
                              </VBox>
                              <VBox height="100%"  verticalGap="0" id="excludedVBox" paddingLeft="5">
                                <SwtText #excTxt
                                         width="{outTxt.width}"
                                         textAlign="right"
                                         fontWeight="bold"
                                         paddingTop="5"
                                         text="Excluded">

                                </SwtText>
                                <SwtText #outTxt
                                         fontWeight="bold"
                                         paddingTop="5"
                                         text="Outstandings"
                                         textAlign="right">
                                </SwtText>
                              </VBox>
                            </HBox>
                            <hr class="line">
                            <HBox id="linkGroup" width="100%" height="90%">
                              <Grid verticalGap="4%" horizontalGap="3" width="100%" height="100%">
                                <GridRow width="100%" height="6%">
                                  <GridItem paddingTop="1" height="100%" paddingLeft="10">
                                    <div class="box-style" style="background-color: #ef7651">1</div>
                                  </GridItem>
                                  <GridItem width="40%" height="100%" paddingLeft="10">
                                    <SwtLabel #labelExc0txt fontWeight="normal">
                                    </SwtLabel>
                                  </GridItem>
                                  <GridItem  height="100%" horizontalAlign="right">
                                    <LinkButton id="valueInc0Btn"
                                                #valueInc0Btn
                                                width="60"
                                                buttonMode="{{valueInc0Btn.enabled}}"
                                                (click)="incMovement(posLvlData[0].num)"
                                                enabled="{(entityCombo.id=='All'||valueInc0Btn.label=='')?false:true}"
                                                textAlign="right">
                                    </LinkButton>
                                  </GridItem>
                                  <GridItem  height="100%" horizontalAlign="right">
                                    <LinkButton id="valueExc0Btn" #valueExc0Btn
                                                width="90"
                                                buttonMode="{valueExc0Btn.enabled}"
                                                (click)="excOutStandings(1,posLvlData[0].excluded)"
                                                enabled="{(entityCombo.id=='All'||valueExc0Btn.label=='')?false:true}"
                                                textAlign="right">
                                    </LinkButton>
                                  </GridItem>
                                </GridRow>
                                <GridRow width="100%" height="6%">
                                  <GridItem height="100%"
                                            paddingLeft="10">
                                    <div class="box-style" style="background-color: #E9C836">2</div>
                                  </GridItem>
                                  <GridItem width="40%"
                                            height="100%"
                                            paddingLeft="10">
                                    <SwtLabel id="labelExc1txt" #labelExc1txt fontWeight="normal"></SwtLabel>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton id="valueInc1Btn" #valueInc1Btn
                                                width="60"
                                                buttonMode="{valueInc1Btn.enabled}"
                                                (click)="incMovement(posLvlData[1].num)"
                                                enabled="{(entityCombo.id=='All'||valueInc1Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton id="valueExc1Btn" #valueExc1Btn
                                                width="90"
                                                buttonMode="{valueExc1Btn.enabled}"
                                                (click)="excOutStandings(2,posLvlData[1].excluded)"
                                                enabled="{(entityCombo.id=='All'||valueExc1Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                </GridRow>
                                <GridRow width="100%" height="6%">
                                  <GridItem  height="100%" paddingLeft="10">
                                    <div class="box-style" style="background-color: #6FB35F">3</div>
                                  </GridItem>
                                  <GridItem width="40%"
                                            height="100%"
                                            paddingLeft="10">
                                    <SwtLabel id="labelExc2txt" #labelExc2txt fontWeight="normal"></SwtLabel>
                                  </GridItem>
                                  <GridItem  height="100%"
                                             horizontalAlign="right">
                                    <LinkButton id="valueInc2Btn" #valueInc2Btn
                                                width="60"
                                                buttonMode="{valueInc2Btn.enabled}"
                                                (click)="incMovement(posLvlData[2].num)"
                                                enabled="{(entityCombo.id=='All'||valueInc2Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton id="valueExc2Btn" #valueExc2Btn
                                                width="90"
                                                buttonMode="{valueExc2Btn.enabled}"
                                                (click)="excOutStandings(3,posLvlData[2].excluded)"
                                                enabled="{(entityCombo.id=='All'||valueExc2Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                </GridRow>
                                <GridRow width="100%" height="6%">
                                  <GridItem
                                    height="100%"
                                    paddingLeft="10">
                                    <div class="box-style" style="background-color: #A1AECF">4</div>
                                  </GridItem>
                                  <GridItem
                                    width="40%"
                                    height="100%"
                                    paddingLeft="10">
                                    <SwtLabel #labelExc3txt fontWeight="normal"></SwtLabel>
                                  </GridItem>
                                  <GridItem  height="100%"
                                             horizontalAlign="right">
                                    <LinkButton id="valueInc3Btn" #valueInc3Btn
                                                width="60"
                                                buttonMode="{valueInc3Btn.enabled}"
                                                (click)="incMovement(posLvlData[3].num)"
                                                enabled="{(entityCombo.id=='All'|| valueInc3Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton id="valueExc3Btn" #valueExc3Btn
                                                width="90"
                                                buttonMode="{valueExc3Btn.enabled}"
                                                (click)="excOutStandings(4,posLvlData[3].excluded)"
                                                enabled="{(entityCombo.id=='All'||valueExc3Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                </GridRow>
                                <GridRow width="100%" height="6%">
                                  <GridItem
                                    height="100%"
                                    paddingLeft="10">
                                    <div class="box-style" style="background-color: #FF00FF">5</div>
                                  </GridItem>
                                  <GridItem
                                    width="40%"
                                    height="100%"
                                    paddingLeft="10">
                                    <SwtLabel #labelExc4txt fontWeight="normal"></SwtLabel>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton id="valueInc4Btn" #valueInc4Btn
                                                width="60"
                                                buttonMode="{valueInc4Btn.enabled}"
                                                (click)="incMovement(posLvlData[4].num)"
                                                enabled="{(entityCombo.id=='All'||valueInc4Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton id="valueExc4Btn" #valueExc4Btn
                                                width="90"
                                                buttonMode="{valueExc4Btn.enabled}"
                                                (click)="excOutStandings(5,posLvlData[4].excluded)"
                                                enabled="{(entityCombo.id=='All'||valueExc4Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                </GridRow>
                                <GridRow width="100%" height="6%">
                                  <GridItem
                                    height="100%"
                                    paddingLeft="10">
                                    <div class="box-style" style="background-color: #00FFFF">6</div>
                                  </GridItem>
                                  <GridItem
                                    width="40%"
                                    height="100%"
                                    paddingLeft="10">
                                    <SwtLabel #labelExc5txt fontWeight="normal"></SwtLabel>
                                  </GridItem>
                                  <GridItem  height="100%"
                                             horizontalAlign="right">
                                    <LinkButton #valueInc5Btn
                                                width="60"
                                                buttonMode="{valueInc5Btn.enabled}"
                                                (click)="incMovement(posLvlData[5].num)"
                                                enabled="{(entityCombo.id=='All'||valueInc5Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton #valueExc5Btn
                                                width="90"
                                                buttonMode="{valueExc5Btn.enabled}"
                                                (click)="excOutStandings(6,posLvlData[5].excluded)"
                                                enabled ="{(entityCombo.id=='All'||valueExc5Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                </GridRow>
                                <GridRow width="100%" height="6%">
                                  <GridItem
                                    height="100%"
                                    paddingLeft="10">
                                    <div class="box-style" style="background-color: #999966">7</div>
                                  </GridItem>
                                  <GridItem width="40%"
                                            height="100%"
                                            paddingLeft="10">
                                    <SwtLabel #labelExc6txt fontWeight="normal"></SwtLabel>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton #valueInc6Btn
                                                width="60"
                                                buttonMode="{valueInc6Btn.enabled}"
                                                (click)="incMovement(posLvlData[6].num)"
                                                enabled="{(entityCombo.id=='All'||valueInc6Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton #valueExc6Btn
                                                width="90"
                                                buttonMode="{valueExc6Btn.enabled}"
                                                (click)="excOutStandings(7,posLvlData[6].excluded)"
                                                enabled="{(entityCombo.id =='All'||valueExc6Btn.label=='') ? false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                </GridRow>
                                <GridRow width="100%" height="6%">
                                  <GridItem
                                    height="100%"
                                    paddingLeft="10">
                                    <div class="box-style" style="background-color: #9966FF">8</div>
                                  </GridItem>
                                  <GridItem
                                    width="40%"
                                    height="100%"
                                    paddingLeft="10">
                                    <SwtLabel #labelExc7txt fontWeight="normal"></SwtLabel>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton #valueInc7Btn
                                                width="60"
                                                buttonMode="{valueInc7Btn.enabled}"
                                                (click)="incMovement(posLvlData[7].num)"
                                                enabled="{(entityCombo.id=='All'||valueInc7Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton #valueExc7Btn
                                                width="90"
                                                buttonMode="{valueExc7Btn.enabled}"
                                                (click)="excOutStandings(8,posLvlData[7].excluded)"
                                                enabled="{(entityCombo.id=='All'||valueExc7Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                </GridRow>
                                <GridRow width="100%" height="6%">
                                  <GridItem height="100%"
                                            paddingLeft="10">
                                    <div class="box-style" style="background-color: #ffff80">9</div>
                                  </GridItem>
                                  <GridItem
                                    width="40%"
                                    height="100%"
                                    paddingLeft="10">
                                    <SwtLabel #labelExc8txt fontWeight="normal"></SwtLabel>
                                  </GridItem>
                                  <GridItem height="100%"
                                            horizontalAlign="right">
                                    <LinkButton #valueInc8Btn
                                                width="60"
                                                buttonMode="{valueInc8Btn.enabled}"
                                                (click)="incMovement(posLvlData[8].num)"
                                                enabled="{(entityCombo.id=='All'||valueInc8Btn.label=='')?false:true}"
                                                textAlign="right"></LinkButton>
                                  </GridItem>
                                  <GridItem  height="100%"
                                             horizontalAlign="right">
                                    <LinkButton id="valueExc8Btn" #valueExc8Btn
                                                width="90"
                                                buttonMode="{valueExc8Btn.enabled}"
                                                (click)="excOutStandings(9,posLvlData[8].excluded)"
                                                enabled="{(entityCombo.id=='All'||valueExc8Btn.label=='')?false:true}"
                                                textAlign="right">
                                    </LinkButton>
                                  </GridItem>
                                </GridRow>
                                <GridRow width="100%" height="10%">
                                  <GridItem paddingLeft="10">
                                  </GridItem>
                                  <GridItem width="65%" height="100%" paddingLeft="26">
                                    <SwtLabel #labelExcTotalTxt
                                              text="Total" fontWeight="normal"></SwtLabel>
                                  </GridItem>
                                  <GridItem id="gridIncTotal" #gridIncTotal height="100%" horizontalAlign="right">
                                    <SwtLabel #labelIncTotalValue
                                              styleName="labelRight"
                                              fontWeight="bold"
                                              fontSize="11"
                                              text="0"
                                              textAlign="right"></SwtLabel>
                                  </GridItem>
                                  <GridItem id="gridTotal" #gridTotal height="100%" horizontalAlign="right">
                                    <SwtLabel width="88" #labelExcTotalValue
                                              fontWeight="bold"
                                              fontSize="11"
                                              styleName="labelRight"
                                              text="0"
                                              textAlign="right"></SwtLabel>
                                  </GridItem>
                                </GridRow>
                              </Grid>
                            </HBox>
                          </VBox>
                        </HBox>
                      </GridItem>
                    </GridRow>
                  </Grid>
                </SwtCanvas>
              </GridItem>
            </GridRow>
          </Grid>
          <VBox width="100%" height="10%" paddingLeft="5" minHeight="40">
            <fieldset style="height:100%">
               <legend style="background-color: transparent">System</legend>
              <Grid width="100%" height="80%" verticalGap="0">
                <GridRow width="100%" height="100%" >
                  <GridItem width="11%"></GridItem>
                  <GridItem width="34%" paddingTop="5">
                    <SwtLabel text="Logged on" fontWeight="normal" fontSize="11"></SwtLabel>
                  </GridItem>
                  <GridItem horizontalAlign="right" paddingLeft="10" paddingTop="5" width="10%">
                    <LinkButton id="logonBtn" #logonBtn
                                buttonMode="{logonBtn.enabled}"
                                (click)="loggedOnClick()"
                                textAlign="right">
                    </LinkButton>
                  </GridItem>
                </GridRow>
              </Grid>
            </fieldset>
          </VBox>
        </VBox>
        <SwtCanvas class="right" id="mainGr" #mainGr width="70%" height="100%">
          <VBox width="100%" height="100%">
            <HBox id="mainGroup" #mainGroup visible="false" width="100%" height="95%">
              <!--<SwtSummary id="summary" #summary IDField="id" width="100%" height="100%"></SwtSummary>-->
            </HBox>
            <HBox width="100%" height="5%">
              <HBox width="60%" paddingLeft="5">
              <SwtButton #detailsButton id="detailsButton" enabled="false" (click)="clickLink('frDetailsBtn')">
              </SwtButton>
              <SwtButton #goToButton id="goToButton" enabled="false" (click)="goTo($event)">
              </SwtButton>
              <SwtButton #reActiveButton id="reActiveButton" enabled="false" (click)="updateStatus('A')">
              </SwtButton>
              <SwtButton #resolveButton id="resolveButton" enabled="false" (click)="updateStatus('R')">
              </SwtButton>
          
              </HBox>
            </HBox>
          </VBox>

        </SwtCanvas>
      </HDividedBox>

      </HBox>
    </SwtCanvas>
    <SwtCanvas width="100%" height="35" #controlContainer minWidth="1100">
      <HBox width="100%" height="100%">
        <HBox paddingLeft="8" width="50%"  height="100%">
          <SwtButton #refreshButton id="refreshButton" (click)="updateData('workFlowRefresh')">
          </SwtButton>
          <SwtButton #rateButton id="rateButton" (click)="rateHandler()">
          </SwtButton>
          <SwtButton #optionsButton id="optionsButton" (click)="optionsHandler()">
          </SwtButton>
          <SwtButton #closeButton id="closeButton" (click)="closeHandler()">
          </SwtButton>
        </HBox>
        <HBox width="50%"  height="100%" verticalAlign="middle" #HgroupContent horizontalAlign="right">
          <SwtText
            visible="false"
            fontWeight="bold"
            text="DATA BUILD IN PROGRESS"
            styleName="redText"
            #dataBuildingText
            id="dataBuildingText"
            right="155"
            height="16">
          </SwtText>
          <SwtText
            visible="false"
            fontWeight="bold"
            text="CONNECTION ERROR"
            styleName="redText"
            #lostConnectionText
            id="lostConnectionText"
            right="45"
            height="16">
          </SwtText>
          <SwtText #lastRefTimeLabel
                   id="lastRefTimeLabel"
                   fontWeight="normal"
                   height="16">
          </SwtText>
          <SwtText #lastRefTime
                   id="lastRefTime"
                   fontWeight="normal"
                   width="120"
                   right="65"
                   height="16">
          </SwtText>
          <SwtButton   style="margin-top: 3px;"
                       #pdf
                       id="pdf"
                       enabled="true"
                       buttonMode="true"
                       styleName="pdfIcon"
                      (click)="export('pdf')">
          </SwtButton>
          <SwtHelpButton id="helpIcon"
                         #helpIcon
                         (click)="doHelp()">
          </SwtHelpButton>
          <SwtLoadingImage #loadingImage></SwtLoadingImage>
        </HBox>
      </HBox>
    </SwtCanvas>
  </VBox>
</SwtModule>
