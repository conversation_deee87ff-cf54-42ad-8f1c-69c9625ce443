{"_from": "rxjs-compat@6.4.0", "_id": "rxjs-compat@6.4.0", "_inBundle": false, "_integrity": "sha512-eo/O8RS83hJdJukCtA+IF6qnqa8FPOuVo+OPCzgVi+dbTle9KCdNv97IcQO0WwNVik7DJLKmf0F8uwzc0q40vw==", "_location": "/rxjs-compat", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "rxjs-compat@6.4.0", "name": "rxjs-compat", "escapedName": "rxjs-compat", "rawSpec": "6.4.0", "saveSpec": null, "fetchSpec": "6.4.0"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/rxjs-compat/-/rxjs-compat-6.4.0.tgz", "_shasum": "800923c15697948e1f30f18c531b12b49451c75c", "_spec": "rxjs-compat@6.4.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "bundleDependencies": false, "deprecated": false, "description": "<img src=\"doc/asset/Rx_Logo_S.png\" alt=\"RxJS Logo\" width=\"86\" height=\"86\"> RxJS: Reactive Extensions For JavaScript ======================================", "license": "Apache-2.0", "main": "./Rx.js", "name": "rxjs-compat", "typings": "./Rx.d.ts", "version": "6.4.0"}