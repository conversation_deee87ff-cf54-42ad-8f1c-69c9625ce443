(window.webpackJsonp=window.webpackJsonp||[]).push([[52],{"99XB":function(t,e,o){"use strict";o.r(e);var i=o("CcnG"),n=o("mrSG"),l=o("447K"),a=o("ZYCi"),s=o("wd/R"),r=o.n(s),h=o("BfSf"),d=(o("EVdn"),function(t){function e(e,o){var i=t.call(this,o,e)||this;return i.commonService=e,i.element=o,i.jsonReader=new l.L,i.inputData=new l.G(i.commonService),i.updateRefreshRate=new l.G(i.commonService),i.baseURL=l.Wb.getBaseURL(),i.actionMethod="",i.actionPath="",i.requestParams=[],i.refreshRate=10,i.comboOpen=!1,i.comboChange=!1,i.monitorcomboChange=!1,i.invalidComms="",i.versionDate="04/11/2019",i.versionNumber="1.1.0026",i.baseURLMETHOD="",i.screenId="",i.menuItemId="",i.monitorTypeOnLoad="",i.callFromParent="",i.entityId1="",i.currencyId1="",i.datefr="",i.dateSelected=new Date,i.bolDateChange=!1,i.bolTabChange=!1,i.dateCompare="",i.entitycomboChange=!1,i.updateFontSize=new l.G(i.commonService),i.fontValue="",i.fontLabel="",i.fontRequest="",i.tempFontSize="",i.existingEntityId="",i.arrayOftabs=[],i.screenVersion=new l.V(i.commonService),i.currencyPattern="",i.swtAlert=new l.bb(e),i}return n.d(e,t),e.prototype.ngOnInit=function(){this.refreshButton.toolTip=l.x.call("getBundle","tip","button-refresh","Refresh window"),this.refreshButton.label=l.x.call("getBundle","text","button-refresh","Refresh"),this.optionsButton.toolTip=l.x.call("getBundle","tip","button-options","Refresh window"),this.optionsButton.label=l.x.call("getBundle","text","button-options","Refresh"),this.closeButton.toolTip=l.x.call("getBundle","tip","button-close","Refresh window"),this.closeButton.label=l.x.call("getBundle","text","button-close","Refresh"),this.entityCombo.toolTip=l.x.call("getBundle","tip","entity","Select an entity ID"),this.ccyCombo.toolTip=l.x.call("getBundle","tip","currency","Select currency code"),this.locationCombo.toolTip=l.x.call("getBundle","tip","location","Select location id"),this.monitorCombo.toolTip=l.x.call("getBundle","tip","monitor","Select Monitor Type"),this.sodText.toolTip=l.x.call("getBundle","tip","balance","Total")},e.prototype.onLoad=function(){var t=this;this.cGrid=this.displaycontainer.addChild(l.hb),this.totalsGrid=this.totalsContainer.addChild(l.Ub),this.cGrid.lockedColumnCount=1,this.totalsGrid.lockedColumnCount=1,this.totalsGrid.selectable=!1,this.cGrid.columnWidthChanged.subscribe(function(e){t.resizeGrids(e)}),this.cGrid.columnOrderChanged.subscribe(function(e){t.resizeGrids(e)}),this.cGrid.listenHorizontalScrollEvent=!0,this.totalsGrid.fireHorizontalScrollEvent=!0,this.refreshButton.label="Refresh",this.groupLogic=new u,this.initializeMenus(),this.dateFormat=l.x.call("eval","dateFormat").toLowerCase(),this.systemDate=l.x.call("eval","dbDate"),this.monitorTypeOnLoad=l.x.call("eval","monitorType"),this.callFromParent=""+l.x.call("eval","calledFromParent"),this.startDate.formatString=this.dateFormat,"dd/mm/yyyy"==this.dateFormat?this.startDate.toolTip=l.x.call("getBundle","tip","dateMMDDYY","MM/dd/yyyy"):this.startDate.toolTip=l.x.call("getBundle","tip","date","Enter date"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,"true"==this.callFromParent?("Group"==this.monitorTypeOnLoad?(this.actionPath="metagroupmonitor.do?",this.actionMethod="method=displayGroupMonitorDetails&callstatus=y&systemDate="+this.systemDate):"Book"==this.monitorTypeOnLoad?(this.actionPath="metagroupmonitor.do?",this.actionMethod="method=displayBookMonitorDetails&callstatus=y&systemDate="+this.systemDate):"Metagroup"==this.monitorTypeOnLoad&&(this.actionPath="metagroupmonitor.do?",this.actionMethod="method=displayMetagroupMonitorDetails&callstatus=y&systemDate="+this.systemDate),this.entityId1=l.x.call("eval","entityId1"),this.currencyId1=l.x.call("eval","currencyId1"),this.datefr=l.x.call("eval","datefr"),this.requestParams["metagroupMonitor.date"]=this.datefr,this.requestParams["metagroupMonitor.entityId"]=this.entityId1,this.requestParams["metagroupMonitor.currencyId"]=this.currencyId1):this.monitorTypeOnLoad?"Group"==this.monitorTypeOnLoad?(this.actionPath="metagroupmonitor.do?",this.actionMethod="method=displayGroupMonitorDetails&systemDate="+this.systemDate):"Book"==this.monitorTypeOnLoad?(this.actionPath="metagroupmonitor.do?",this.actionMethod="method=displayBookMonitorDetails&systemDate="+this.systemDate):"Metagroup"==this.monitorTypeOnLoad&&(this.actionPath="metagroupmonitor.do?",this.actionMethod=""):(this.actionPath="metagroupmonitor.do?",this.actionMethod=""),this.backGroundTimer=new l.cc(1e3,0),this.backGroundTimer.addEventListener("BackgroundTimer",this.backgroundFlagCheck.bind(this)),this.backGroundTimer.start(),this.actionMethod=""==this.actionMethod?"method=unspecified":this.actionMethod,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),l.v.subscribe(function(e){t.export(e)}),this.cGrid.ITEM_CLICK.subscribe(function(e){t.obtainCell(e)})},e.prototype.backgroundFlagCheck=function(t){l.x.call("eval","refreshPending")&&(this.updateData("no"),l.x.call("eval","refreshPending=false"))},e.prototype.resizeGrids=function(t){try{this.totalsGrid.setRefreshColumnWidths(this.cGrid.gridObj.getColumns())}catch(e){console.log("resizeGrids",e)}},e.prototype.inputDataResult=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else{this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.lostConnectionText.visible=!1,this.currencyPattern=this.jsonReader.getScreenAttributes().currencyformat;var e=this.jsonReader.getScreenAttributes().lastRefTime;if(e=this.groupLogic.convertFromUnicodeToString(e),this.lastRefTime.text=e,this.jsonReader.getRequestReplyStatus()){if(this.ccyCombo.setComboData(this.jsonReader.getSelects()),""==l.Z.trim(this.ccyCombo.selectedItem.content)&&(null!=this.autoRefresh&&this.autoRefresh.stop(),this.comboOpen=!0,this.swtAlert.show(l.x.call("getBundle","text","alert.currencyAccess","Invalid: your role does not specify access to currencies/groups for this entity"),l.x.call("getBundle","text","alert-error","Error"))),this.lastRecievedJSON!=this.prevRecievedJSON){if(this.startDate.showToday=!1,this.systemDate=this.jsonReader.getScreenAttributes().sysDateFrmSession,this.existingEntityId=this.jsonReader.getScreenAttributes().existingEntityId,this.groupLogic.dateFormat=this.dateFormat,this.startDate.selectedDate=new Date(this.groupLogic.convertDate(this.jsonReader.getScreenAttributes().datefrom)),this.entityCombo.setComboData(this.jsonReader.getSelects()),this.selectedEntity.text=this.entityCombo.selectedValue,this.selectedCcy.text=this.ccyCombo.selectedValue,this.monitorCombo.setComboData(this.jsonReader.getSelects()),this.jsonReader.getSelects().select.find(function(t){return"parent-container"===t.id})&&(this.lastmonitortype=this.jsonReader.getSelects().select.find(function(t){return"parent-container"===t.id}).label),this.monitorCombo.selectedLabel=this.lastmonitortype,this.locationCombo.setComboData(this.jsonReader.getSelects()),this.locationType.text=this.locationCombo.selectedValue,this.sodText.text=this.jsonReader.getSingletons().sod?this.jsonReader.getSingletons().sod.content:"",""+this.jsonReader.getSingletons().sod.negative=="true"?this.sodText.setStyle("color","red"):this.sodText.setStyle("color","black"),""!=this.lastRecievedJSON.groupmonitor.tabs){if(0==this.arrayOftabs.length){if(this.tabs.getTabChildren().length>0)for(var o=0;o<this.arrayOftabs.length;o++)this.tabs.removeChild(this.arrayOftabs[o]);this.displayContainerToday=this.tabs.addChild(l.Xb),this.displayContainerTodayPlus=this.tabs.addChild(l.Xb),this.displayContainerTodayPlusPlus=this.tabs.addChild(l.Xb),this.displayContainerTodayPlusThree=this.tabs.addChild(l.Xb),this.displayContainerTodayPlusFour=this.tabs.addChild(l.Xb),this.displayContainerTodayPlusFive=this.tabs.addChild(l.Xb),this.displayContainerTodayPlusSix=this.tabs.addChild(l.Xb),this.displayContainerSelected=this.tabs.addChild(l.Xb),this.arrayOftabs=[],this.arrayOftabs=[this.displayContainerToday,this.displayContainerTodayPlus,this.displayContainerTodayPlusPlus,this.displayContainerTodayPlusThree,this.displayContainerTodayPlusFour,this.displayContainerTodayPlusFive,this.displayContainerTodayPlusSix,this.displayContainerSelected]}for(var i=0;i<this.lastRecievedJSON.groupmonitor.tabs.predictdate.length;i++)this.arrayOftabs[i].label=this.lastRecievedJSON.groupmonitor.tabs.predictdate[i].dateLabel,this.arrayOftabs[i].businessday=this.lastRecievedJSON.groupmonitor.tabs.predictdate[i].businessday,0==this.arrayOftabs[i].businessday?this.arrayOftabs[i].setTabHeaderStyle("color","darkgray"):this.arrayOftabs[i].setTabHeaderStyle("color","black");this.arrayOftabs[7].label=l.x.call("getBundle","text","tab4","Selected")}if(this.monitortype=this.monitorCombo.selectedItem.content,this.dateCompare=""+this.jsonReader.getScreenAttributes().dateComparing,"Metagroup"==this.monitortype&&(this.totalsContainer.visible=!1,this.totalsContainer.includeInLayout=!1,this.totalsGrid.gridData=null,this.menuItemId=l.x.call("eval","metagroupMenuItemId"),this.baseURLMETHOD=this.baseURL+"metagroupmonitor.do?menuItemId="+this.menuItemId+"&",this.selectGroup.visible=!1),"Group"==this.monitortype&&(this.totalsContainer.visible=!1,this.totalsContainer.includeInLayout=!1,this.totalsGrid.gridData=null,this.menuItemId=l.x.call("eval","groupMenuItemId"),this.baseURLMETHOD=this.baseURL+"metagroupmonitor.do?menuItemId="+this.menuItemId+"&",this.selectGroup.visible=!0,this.groupCombo.dataLabel="metagroup",this.groupCombo.toolTip=l.x.call("getBundle","tip","metagroup","Select metagroup id"),this.groupcomboLabel.text=l.x.call("getBundle","text","metagroup","Metagroup"),this.groupCombo.setComboData(this.jsonReader.getSelects()),this.selectedGroup.text=this.groupCombo.selectedValue),"Book"==this.monitortype&&(this.menuItemId=l.x.call("eval","bookMenuItemId"),this.baseURLMETHOD=this.baseURL+"metagroupmonitor.do?menuItemId="+this.menuItemId+"&",this.selectGroup.visible=!0,this.groupCombo.dataLabel="groupCode",this.groupCombo.toolTip=l.x.call("getBundle","tip","group","Select group id"),this.groupcomboLabel.text=l.x.call("getBundle","text","group","Group"),this.groupCombo.setComboData(this.jsonReader.getSelects()),this.selectedGroup.text=this.groupCombo.selectedItem.content),this.jsonReader.isDataBuilding())this.dataBuildingText.visible=!0;else{if(this.dataBuildingText.visible=!1,this.jsonReader.getRowSize()<1?this.dataExport.enabled=!1:this.dataExport.enabled=!0,this.cGrid.currencyFormat=this.currencyPattern,this.cGrid.hideHorizontalScrollBar=!0,1==this.monitorcomboChange)this.cGrid.uniqueColumn="group",this.cGrid.CustomGrid(t.groupmonitor.grid.metadata),this.handleDate(),this.cGrid.gridData=this.jsonReader.getGridData(),this.cGrid.setRowSize=this.jsonReader.getRowSize(),this.cGrid.entityID=this.entityCombo.selectedItem.content,"Book"==this.monitortype&&(this.totalsContainer.visible=!0,this.totalsGrid.CustomGrid(t.groupmonitor.grid.metadata),this.totalsGrid.gridData=this.jsonReader.getTotalsData(),this.cGrid.entityID=this.entityCombo.selectedItem.content);else{if(this.cGrid.metaData){if("false"==this.dateCompare)this.handleDate();else if("true"==this.dateCompare){var n=this.systemDate;new Date(l.j.parseDate(n,this.dateFormat)),this.handleDate()}}else this.cGrid.CustomGrid(t.groupmonitor.grid.metadata),this.handleDate(),"Book"==this.monitortype&&(this.totalsContainer.visible=!0,this.totalsGrid.CustomGrid(t.groupmonitor.grid.metadata),this.totalsGrid.gridData=this.jsonReader.getTotalsData(),this.cGrid.entityID=this.entityCombo.selectedItem.content),this.tempFontSize=this.jsonReader.getScreenAttributes().currfontsize,"Normal"==this.tempFontSize?(this.cGrid.styleName="dataGridNormal",this.cGrid.rowHeight=18,"Book"==this.monitortype&&(this.totalsGrid.styleName="dataGridNormal",this.totalsGrid.rowHeight=18)):"Small"==this.tempFontSize&&(this.cGrid.styleName="dataGridSmall",this.cGrid.rowHeight=15,"Book"==this.monitortype&&(this.totalsGrid.styleName="dataGridSmall",this.totalsGrid.rowHeight=15));this.cGrid.gridData=this.jsonReader.getGridData(),this.cGrid.setRowSize=this.jsonReader.getRowSize(),this.cGrid.entityID=this.entityCombo.selectedItem.content,"Book"==this.monitortype&&(this.totalsContainer.visible=!0,this.totalsGrid.CustomGrid(t.groupmonitor.grid.metadata),this.totalsGrid.gridData=this.jsonReader.getTotalsData(),this.cGrid.entityID=this.entityCombo.selectedItem.content)}this.tempFontSize=this.jsonReader.getScreenAttributes().currfontsize,"Normal"==this.tempFontSize?(this.selectedFont=0,this.cGrid.styleName="dataGridNormal",this.cGrid.rowHeight=18,"Book"==this.monitortype&&(this.totalsGrid.styleName="dataGridNormal",this.totalsGrid.rowHeight=18)):"Small"==this.tempFontSize&&(this.selectedFont=1,this.cGrid.styleName="dataGridSmall",this.cGrid.rowHeight=15,"Book"==this.monitortype&&(this.totalsGrid.styleName="dataGridSmall",this.totalsGrid.rowHeight=15))}null==this.autoRefresh&&(this.refreshRate=parseInt(this.jsonReader.getRefreshRate()),this.autoRefresh=new l.cc(1e3*this.refreshRate,0),this.autoRefresh.addEventListener("timer",this.dataRefresh.bind(this))),1==this.monitorcomboChange?(this.monitorcomboChange=!1,this.prevRecievedJSON=new Object):(this.prevRecievedJSON=new Object,this.prevRecievedJSON=this.lastRecievedJSON)}}else null!=this.lastmonitortype&&(this.monitorCombo.selectedItem=this.lastmonitortype,this.updateData("no"));null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start(),null!=this.cGrid&&null!=this.totalsGrid&&(this.tempFontSize=this.jsonReader.getScreenAttributes().currfontsize,"Normal"==this.tempFontSize?(this.selectedFont=0,this.cGrid.styleName="dataGridNormal",this.cGrid.rowHeight=18,"Book"==this.monitortype&&(this.totalsGrid.styleName="dataGridNormal",this.totalsGrid.rowHeight=18)):"Small"==this.tempFontSize&&(this.selectedFont=1,this.cGrid.styleName="dataGridSmall",this.cGrid.rowHeight=15,"Book"==this.monitortype&&(this.totalsGrid.styleName="dataGridSmall",this.totalsGrid.rowHeight=15))),this.cGrid.colWidthURL(this.baseURLMETHOD),this.cGrid.colOrderURL(this.baseURLMETHOD),this.cGrid.saveWidths=!0,this.cGrid.saveColumnOrder=!0)}}catch(a){console.log("TCL: BookGroupMonitor -> e",a)}},e.prototype.handleDate=function(){try{var t=!1,e=this.groupLogic.convertDate(this.systemDate),o=new Date(e),i=this.groupLogic.convertDate(this.startDate.text);if(i)for(var n=0;n<7;n++){if(o.setDate(e.getDate()+n),i.getTime()==o.getTime()){t=!0,this.tabs.selectedIndex=n;break}o=new Date(e)}t||(this.tabs.selectedIndex=7)}catch(l){}},e.prototype.obtainCell=function(t){try{if(null!=t){var e=t.target.field,o=t.target.data.slickgrid_rowcontent[e].clickable;t.target.field;this.metagroupId=t.target.data.group;var i=this.startDate.selectedDate,n=this.dateFormatConversion(this.dateFormat.toUpperCase(),i),l=this.ccyCombo.selectedItem.content,a=this.entityCombo.selectedItem.content,s=this.locationCombo.selectedItem.content;if(this.monitortype=this.monitorCombo.selectedItem.content,"Group"==this.monitortype)this.monitorcomboChange=!0,this.actionPath="metagroupmonitor.do?",this.actionMethod="method=displayBookMonitorDetails",this.requestParams["metagroupMonitor.selectedTabIndex"]=this.tabs.selectedIndex+1,this.requestParams["metagroupMonitor.date"]=n,this.requestParams["metagroupMonitor.locationId"]=s,this.requestParams["metagroupMonitor.entityId"]=a,this.requestParams["metagroupMonitor.currencyId"]=l,this.requestParams["metagroupMonitor.groupCode"]=this.metagroupId,this.monitortype="Book";else if("Metagroup"==this.monitortype)this.monitorcomboChange=!0,this.actionPath="metagroupmonitor.do?",this.actionMethod="method=displayGroupMonitorDetails",this.requestParams["metagroupMonitor.selectedTabIndex"]=this.tabs.selectedIndex+1,this.requestParams["metagroupMonitor.date"]=n,this.requestParams["metagroupMonitor.locationId"]=s,this.requestParams["metagroupMonitor.entityId"]=a,this.requestParams["metagroupMonitor.currencyId"]=l,this.requestParams["metagroupMonitor.metagroupId"]=this.metagroupId,this.monitortype="Group";else if("Book"==this.monitortype){var r=(this.tabs.selectedIndex+1).toString();this.groupCode=this.groupCombo.selectedItem.content,o&&this.groupLogic.getMovementDetails(a,l,r,n,this.metagroupId,this.baseURL)}o&&(this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))}}catch(t){console.log("TCL: BookGroupMonitor -> e",t)}},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,"Book Group",this.versionNumber,this.versionDate);var t=null;(t=new l.n(l.Wb.getPredictMessage("screen.showJSON",null))).MenuItemSelect=this.showXMLSelect.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showXMLSelect=function(t){this.showXMLPopup=l.Eb.createPopUp(this,l.M,{jsonData:this.lastRecievedJSON}),this.showXMLPopup.width="700",this.showXMLPopup.title="Last Received JSON",this.showXMLPopup.height="500",this.showXMLPopup.enableResize=!1,this.showXMLPopup.showControls=!0,this.showXMLPopup.isModal=!0,this.showXMLPopup.display()},e.prototype.dataRefresh=function(t){this.comboOpen||this.updateData("yes"),this.autoRefresh.stop()},e.prototype.optionsHandler=function(){var t=this;this.refreshRate=parseInt(this.jsonReader.getScreenAttributes().refresh),this.win=l.Eb.createPopUp(this,h.a,{title:"Options",fontSizeValue:this.selectedFont,refreshText:this.refreshRate.toString()}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="360",this.win.height="190",this.win.showControls=!0,this.win.id="optionsWindow",this.win.onClose.subscribe(function(e){t.submitFontSize(e),setTimeout(function(){t.submitRate(e)},1e3)}),this.win.display()},e.prototype.submitFontSize=function(t){var e;(this.fontValue=t.fontSize.value,this.monitortype=this.monitorCombo.selectedItem.content,"Metagroup"==this.monitortype&&(this.screenId=l.x.call("eval","metagroupScreenId")),"Group"==this.monitortype&&(this.screenId=l.x.call("eval","groupScreenId")),"Book"==this.monitortype&&(this.screenId=l.x.call("eval","bookScreenId")),"N"==this.fontValue?(this.selectedFont=0,this.fontLabel=t.fontSize.label,this.cGrid.styleName="dataGridNormal",this.cGrid.rowHeight=18,"Book"==this.monitortype&&(this.totalsGrid.styleName="dataGridNormal",this.totalsGrid.rowHeight=18),this.fontRequest=l.x.call("getUpdateFontSize",this.fontLabel,this.screenId)):"S"==this.fontValue&&(this.selectedFont=1,this.fontLabel=t.fontSize.label,this.cGrid.styleName="dataGridSmall",this.cGrid.rowHeight=15,"Book"==this.monitortype&&(this.totalsGrid.styleName="dataGridSmall",this.totalsGrid.rowHeight=15),this.fontRequest=l.x.call("getUpdateFontSize",this.fontLabel,this.screenId)),null!=this.fontRequest&&""!=this.fontRequest)&&(e=this.updateFontSize.url,this.updateFontSize.url=this.fontRequest,this.updateFontSize.send(),this.updateFontSize.url=e)},e.prototype.inputDataFault=function(t){this.lostConnectionText.visible=!0;var e=l.Wb.getPredictMessage("label.genericException",null);this.swtAlert.error(e),null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())},e.prototype.connError=function(t){this.swtAlert.show(""+this.invalidComms,l.x.call("getBundle","text","alert-error","Error"))},e.prototype.submitRate=function(t){if(isNaN(parseInt(t.refreshRate)))this.swtAlert.show(l.x.call("getBundle","text","label-notANumber","Not a number"));else{var e=!1;this.refreshRate=parseInt(t.refreshRate),this.refreshRate<5&&(this.refreshRate=5,e=!0),this.autoRefresh&&this.autoRefresh.delay(1e3*this.refreshRate),this.monitortype=this.monitorCombo.selectedItem.content,"Metagroup"==this.monitortype&&(this.screenId=l.x.call("eval","metagroupScreenId")),"Group"==this.monitortype&&(this.screenId=l.x.call("eval","groupScreenId")),"Book"==this.monitortype&&(this.screenId=l.x.call("eval","bookScreenId"));var o,i=l.x.call("getUpdateRefreshRequest",this.refreshRate,this.screenId);if(null!=i&&""!=i)o=this.updateFontSize.url,this.updateRefreshRate.url=i,this.updateRefreshRate.send(),this.updateRefreshRate.url=o;this.updateData("no"),e&&this.swtAlert.show(l.x.call("getBundle","text","label-refreshRateSelected","Refresh rate selected was below minimum.\nSet to 5 seconds"),l.x.call("getBundle","text","alert-warning","Warning"))}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.disableInterface(),this.startDate.enabled=!1},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableInterface(),this.startDate.enabled=!0},e.prototype.disableInterface=function(){this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1},e.prototype.enableInterface=function(){this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0},e.prototype.changeCombo=function(t){this.comboChange=!0,this.entitycomboChange=!1,this.updateData("no")},e.prototype.entityChangeCombo=function(t){this.comboChange=!0,this.entitycomboChange=!0,this.updateData("no")},e.prototype.openedCombo=function(t){this.comboOpen=!0},e.prototype.closedCombo=function(t){this.comboOpen=!1},e.prototype.closedDateField=function(t){this.comboOpen=!1,this.startDate.interruptComms=!1},e.prototype.updateData=function(t){var e;if(this.bolTabChange=!1,this.requestParams=[],this.actionMethod="",1==this.bolDateChange?(e=new Date(this.dateSelected),this.bolDateChange=!1):e=new Date(this.startDate.selectedDate),this.bolTabChange=!1,null!=e&&this.ccyCombo.selectedItem.content&&this.entityCombo.selectedItem.content&&this.locationCombo.selectedItem.content&&this.monitorCombo.selectedItem.content){var o=this.dateFormatConversion(this.dateFormat.toUpperCase(),e),i=this.ccyCombo.selectedItem.content,n=this.entityCombo.selectedItem.content,l=this.locationCombo.selectedItem.content;if(this.monitortype=this.monitorCombo.selectedItem.content,"Metagroup"==this.monitortype&&(this.actionPath="metagroupmonitor.do?",this.actionMethod="",this.actionMethod="method=displayMetagroupMonitorDetails",this.requestParams["metagroupMonitor.selectedTabIndex"]=this.tabs.selectedIndex+1,this.requestParams["metagroupMonitor.date"]=o,this.requestParams["metagroupMonitor.locationId"]=l,this.requestParams["metagroupMonitor.entityId"]=n,this.entitycomboChange||(this.requestParams["metagroupMonitor.currencyId"]=i),this.requestParams.systemDate=this.systemDate,this.requestParams.autoRefresh=t),"Group"==this.monitortype){if(null==this.groupCombo.selectedItem.content)return;this.metagroupId=this.groupCombo.selectedItem.content,this.actionPath="metagroupmonitor.do?",this.actionMethod="method=displayGroupMonitorDetails",this.requestParams["metagroupMonitor.selectedTabIndex"]=this.tabs.selectedIndex+1,this.requestParams["metagroupMonitor.date"]=o,this.requestParams["metagroupMonitor.locationId"]=l,this.requestParams["metagroupMonitor.entityId"]=n,this.entitycomboChange||(this.requestParams["metagroupMonitor.currencyId"]=i),this.requestParams["metagroupMonitor.metagroupId"]=this.metagroupId,this.requestParams.systemDate=this.systemDate,this.requestParams.autoRefresh=t}if("Book"==this.monitortype){if(null==this.groupCombo.selectedItem.content)return;this.groupCode=this.groupCombo.selectedItem.content,this.actionPath="metagroupmonitor.do?",this.actionMethod="",this.actionMethod="method=displayBookMonitorDetails",this.requestParams["metagroupMonitor.selectedTabIndex"]=this.tabs.selectedIndex+1,this.requestParams["metagroupMonitor.date"]=o,this.requestParams["metagroupMonitor.locationId"]=l,this.requestParams["metagroupMonitor.entityId"]=n,this.entitycomboChange||(this.requestParams["metagroupMonitor.currencyId"]=i),this.requestParams["metagroupMonitor.groupCode"]=this.groupCode,this.requestParams.systemDate=this.systemDate,this.requestParams.autoRefresh=t}this.requestParams.existingEntityId=this.existingEntityId,this.actionMethod=""==this.actionMethod?"method=unspecified":this.actionMethod,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}},e.prototype.dateFormatConversion=function(t,e){return"DD/MM/YYYY"==t?e?(e.getDate()<10?"0"+e.getDate():e.getDate())+"/"+(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"/"+e.getFullYear():null:e?(e.getMonth()+1<10?"0"+(e.getMonth()+1):e.getMonth()+1)+"/"+(e.getDate()<10?"0"+e.getDate():e.getDate())+"/"+e.getFullYear():null},e.prototype.closeHandler=function(){l.x.call("close")},e.prototype.mouseOverHandler=function(t){t.preventDefault(),t.currentTarget.setStyle("styleName","myItemHover")},e.prototype.mouseOutHandler=function(t){t.preventDefault(),t.currentTarget.setStyle("styleName","myItem")},e.prototype.tabChange=function(t){var e,o=!1;this.monitorcomboChange=!0,"true"==this.callFromParent&&(this.callFromParent="false"),(e=this.groupLogic.convertDate(this.systemDate,this.dateFormat)).setHours(12,0,0),this.tabs.selectedIndex<7&&(this.startDate.selectedDate=new Date(e.setDate(e.getDate()+this.tabs.selectedIndex))),7==this.tabs.selectedIndex&&(this.prevRecievedJSON=null,null!=this.startDate.selectedDate?this.startDate.selectedDate=new Date(this.startDate.selectedDate.getTime()):this.bolTabChange=!0,0==this.bolTabChange?(this.updateData("no"),o=!0):(this.bolTabChange=!1,o=!0)),0==o&&(0==this.bolTabChange?this.updateData("no"):this.bolTabChange=!1)},e.prototype.timeUpdate=function(t){this.bolDateChange=!0;try{this.monitorcomboChange=!0;var e,o;e=this.systemDate,(o=new Date(l.j.parseDate(e,this.dateFormat))).setHours(12,0,0),this.groupLogic.convertDate(this.startDate.text).setHours(12,0,0);var i=this.groupLogic.convertDate(this.startDate.text),n=null;this.dateSelected=i;var a="";if(n="DD/MM/YYYY"==this.dateFormat.toUpperCase()?/^(0[1-9]|[12][0-9]|3[01]|[1-9])\/(0[1-9]|1[012]|[1-9])\/(\d{4}|\d{2})$/:/^(0[1-9]|1[012]|[1-9])\/(0[1-9]|[12][0-9]|3[01]|[1-9])\/(\d{4}|\d{2})$/,null!=(a="DD/MM/YYYY"==this.dateFormat.toUpperCase()?i?(i.getDate()<10?"0"+i.getDate():i.getDate())+"/"+(i.getMonth()+1<10?"0"+(i.getMonth()+1):i.getMonth()+1)+"/"+i.getFullYear():null:i?(i.getMonth()+1<10?"0"+(i.getMonth()+1):i.getMonth()+1)+"/"+(i.getDate()<10?"0"+i.getDate():i.getDate())+"/"+i.getFullYear():null)&&""+n.test(this.startDate.text).toString()=="true")if(this.groupLogic.CheckDate(a))this.handleDate(),this.updateData("no");else{this.jsonReader.getScreenAttributes().datefrom&&(o=this.groupLogic.convertDate(this.jsonReader.getScreenAttributes().datefrom.toString(),this.dateFormat)),this.startDate.selectedDate=new Date(o.getTime());var s=this.startDate.selectedDate;this.dateSelected=s,this.tabs.selectedIndex=this.tabs.selectedIndex,this.updateData("no"),!1,this.bolTabChange=!1}}catch(r){}},e.prototype.monitorComboChangeHandle=function(t){try{this.requestParams=[],this.comboChange=!0,this.monitorcomboChange=!0;var e=this.startDate.text,o=this.jsonReader.getScreenAttributes().currfontsize;"Book"==this.monitortype&&null!=this.totalsGrid&&(this.totalsGrid.gridData=null),this.monitortype=this.monitorCombo.selectedItem.content,"Metagroup"==this.monitortype&&(this.actionPath="metagroupmonitor.do?",this.actionMethod="",this.actionMethod="method=displayMetagroupMonitorDetails",this.requestParams["metagroupMonitor.selectedTabIndex"]=this.tabs.selectedIndex+1,this.requestParams["metagroupMonitor.date"]=e,this.requestParams["metagroupMonitor.locationId"]=this.locationCombo.selectedItem.content,this.requestParams["metagroupMonitor.entityId"]=this.entityCombo.selectedItem.content,this.requestParams["metagroupMonitor.currencyId"]=this.ccyCombo.selectedItem.content,this.requestParams.systemDate=this.systemDate),"Group"==this.monitortype&&(this.metagroupId=this.groupCombo.selectedItem.content,this.actionPath="metagroupmonitor.do?",this.actionMethod="method=displayGroupMonitorDetails",this.requestParams["metagroupMonitor.selectedTabIndex"]=this.tabs.selectedIndex+1,this.requestParams["metagroupMonitor.date"]=e,this.requestParams["metagroupMonitor.locationId"]=this.locationCombo.selectedItem.content,this.requestParams["metagroupMonitor.entityId"]=this.entityCombo.selectedItem.content,this.requestParams["metagroupMonitor.currencyId"]=this.ccyCombo.selectedItem.content,this.requestParams["metagroupMonitor.metagroupId"]=this.metagroupId,this.requestParams.systemDate=this.systemDate),"Book"==this.monitortype&&(this.groupCode=this.groupCombo.selectedItem.content,this.actionPath="metagroupmonitor.do?",this.actionMethod="",this.actionMethod="method=displayBookMonitorDetails",this.requestParams["metagroupMonitor.selectedTabIndex"]=this.tabs.selectedIndex+1,this.requestParams["metagroupMonitor.date"]=e,this.requestParams["metagroupMonitor.locationId"]=this.locationCombo.selectedItem.content,this.requestParams["metagroupMonitor.entityId"]=this.entityCombo.selectedItem.content,this.requestParams["metagroupMonitor.currencyId"]=this.ccyCombo.selectedItem.content,this.requestParams["metagroupMonitor.groupCode"]=this.groupCode,this.requestParams.systemDate=this.systemDate),"Book"!=this.monitortype&&"Group"!=this.monitortype&&"Metagroup"!=this.monitortype||("Normal"==o?(this.selectedFont=0,this.cGrid.styleName="dataGridNormal",this.cGrid.rowHeight=18):"Small"==o&&(this.selectedFont=1,this.cGrid.styleName="dataGridSmall",this.cGrid.rowHeight=15)),this.requestParams.existingEntityId=this.existingEntityId,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(t){console.log("errro in chnage combo of monitor type",t)}},e.prototype.export=function(t){try{var e=new Array,o=(new Object,!1);e.push(l.x.call("getBundle","text","label-entity","Entity")+"="+this.entityCombo.selectedItem.content),e.push("Ccy="+this.ccyCombo.selectedItem.content),e.push(l.x.call("getBundle","text","label-location","Location")+"="+this.locationCombo.selectedItem.content),"Metagroup"!=this.monitortype&&e.push(this.groupcomboLabel.text+"="+this.groupCombo.selectedItem.content);var i=this.startDate.selectedDate,n=this.dateFormatConversion(this.dateFormat.toUpperCase(),i);e.push("Date="+n),e.push(l.x.call("getBundle","text","label-entity","Entity")+"="+(""!=this.sodText.text?this.sodText.text:"None")),this.lastRecievedJSON.groupmonitor.grid.totals&&(o=!0),this.dataExport.convertData(this.lastRecievedJSON.groupmonitor.grid.metadata.columns,this.cGrid,this.totalsGrid.gridData,e,t,o)}catch(a){console.log("TCL: BookGroupMonitor -> e",a)}},e.prototype.helphandler=function(){l.x.call("help")},e.prototype.validateStartDate=function(t){"closeButton"==Object(l.ic.getFocus()).id?this.closeHandler():(this.autoRefresh.stop(),this.validateDate(this.startDate))},e.prototype.validateDate=function(t){var e=this;t.text?r()(t.text,this.dateFormat.toUpperCase(),!0).isValid()||this.swtAlert.error(l.Wb.getPredictMessage("alert.enterValidDate",null),null,l.c.OK,null,function(){e.startDate.text=e.jsonReader.getScreenAttributes().datefrom.toString()}):this.swtAlert.error(l.Wb.getPredictMessage("alert.enterValidDate",null),null,l.c.OK,null,function(){e.startDate.text=e.jsonReader.getScreenAttributes().datefrom.toString()})},e.prototype.closeAlert=function(t){this.startDate.setFocus()},e.prototype.doHelp=function(){l.x.call("help")},e}(l.yb)),u=function(){function t(){}return t.prototype.GroupLogic=function(){},t.prototype.getMovementDetails=function(t,e,o,i,n,a){l.x.call("getMovementDetails",t,e,o,i,n,a)},t.prototype.CheckDate=function(t){return l.x.call("onDateKeyPress",t)},t.prototype.convertDate=function(t,e){return e?r()(t,e.toUpperCase()).toDate():r()(t,this.dateFormat.toUpperCase()).toDate()},t.prototype.convertFromUnicodeToString=function(t){return t&&(t=unescape(t)),t},t.prototype.doHelp=function(){l.x.call("help")},t}(),c=[{path:"",component:d}],b=(a.l.forChild(c),function(){return function(){}}()),m=o("pMnS"),p=o("RChO"),g=o("t6HQ"),y=o("WFGK"),I=o("5FqG"),f=o("Ip0R"),C=o("gIcY"),w=o("t/Na"),D=o("sE5F"),R=o("OzfB"),x=o("T7CS"),v=o("S7LP"),G=o("6aHO"),M=o("WzUx"),S=o("A7o+"),T=o("zCE2"),L=o("Jg5P"),B=o("3R0m"),P=o("hhbb"),k=o("5rxC"),A=o("Fzqc"),J=o("21Lb"),F=o("hUWP"),O=o("3pJQ"),N=o("V9q+"),q=o("VDKW"),z=o("kXfT"),H=o("BGbe");o.d(e,"BookGroupMonitorModuleNgFactory",function(){return E}),o.d(e,"RenderType_BookGroupMonitor",function(){return j}),o.d(e,"View_BookGroupMonitor_0",function(){return U}),o.d(e,"View_BookGroupMonitor_Host_0",function(){return _}),o.d(e,"BookGroupMonitorNgFactory",function(){return Y});var E=i.Gb(b,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[m.a,p.a,g.a,y.a,I.Cb,I.Pb,I.r,I.rc,I.s,I.Ab,I.Bb,I.Db,I.qd,I.Hb,I.k,I.Ib,I.Nb,I.Ub,I.yb,I.Jb,I.v,I.A,I.e,I.c,I.g,I.d,I.Kb,I.f,I.ec,I.Wb,I.bc,I.ac,I.sc,I.fc,I.lc,I.jc,I.Eb,I.Fb,I.mc,I.Lb,I.nc,I.Mb,I.dc,I.Rb,I.b,I.ic,I.Yb,I.Sb,I.kc,I.y,I.Qb,I.cc,I.hc,I.pc,I.oc,I.xb,I.p,I.q,I.o,I.h,I.j,I.w,I.Zb,I.i,I.m,I.Vb,I.Ob,I.Gb,I.Xb,I.t,I.tc,I.zb,I.n,I.qc,I.a,I.z,I.rd,I.sd,I.x,I.td,I.gc,I.l,I.u,I.ud,I.Tb,Y]],[3,i.n],i.J]),i.Rb(4608,f.m,f.l,[i.F,[2,f.u]]),i.Rb(4608,C.c,C.c,[]),i.Rb(4608,C.p,C.p,[]),i.Rb(4608,w.j,w.p,[f.c,i.O,w.n]),i.Rb(4608,w.q,w.q,[w.j,w.o]),i.Rb(5120,w.a,function(t){return[t,new l.tb]},[w.q]),i.Rb(4608,w.m,w.m,[]),i.Rb(6144,w.k,null,[w.m]),i.Rb(4608,w.i,w.i,[w.k]),i.Rb(6144,w.b,null,[w.i]),i.Rb(4608,w.f,w.l,[w.b,i.B]),i.Rb(4608,w.c,w.c,[w.f]),i.Rb(4608,D.c,D.c,[]),i.Rb(4608,D.g,D.b,[]),i.Rb(5120,D.i,D.j,[]),i.Rb(4608,D.h,D.h,[D.c,D.g,D.i]),i.Rb(4608,D.f,D.a,[]),i.Rb(5120,D.d,D.k,[D.h,D.f]),i.Rb(5120,i.b,function(t,e){return[R.j(t,e)]},[f.c,i.O]),i.Rb(4608,x.a,x.a,[]),i.Rb(4608,v.a,v.a,[]),i.Rb(4608,G.a,G.a,[i.n,i.L,i.B,v.a,i.g]),i.Rb(4608,M.c,M.c,[i.n,i.g,i.B]),i.Rb(4608,M.e,M.e,[M.c]),i.Rb(4608,S.l,S.l,[]),i.Rb(4608,S.h,S.g,[]),i.Rb(4608,S.c,S.f,[]),i.Rb(4608,S.j,S.d,[]),i.Rb(4608,S.b,S.a,[]),i.Rb(4608,S.k,S.k,[S.l,S.h,S.c,S.j,S.b,S.m,S.n]),i.Rb(4608,M.i,M.i,[[2,S.k]]),i.Rb(4608,M.r,M.r,[M.L,[2,S.k],M.i]),i.Rb(4608,M.t,M.t,[]),i.Rb(4608,M.w,M.w,[]),i.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),i.Rb(1073742336,f.b,f.b,[]),i.Rb(1073742336,C.n,C.n,[]),i.Rb(1073742336,C.l,C.l,[]),i.Rb(1073742336,T.a,T.a,[]),i.Rb(1073742336,L.a,L.a,[]),i.Rb(1073742336,C.e,C.e,[]),i.Rb(1073742336,B.a,B.a,[]),i.Rb(1073742336,S.i,S.i,[]),i.Rb(1073742336,M.b,M.b,[]),i.Rb(1073742336,w.e,w.e,[]),i.Rb(1073742336,w.d,w.d,[]),i.Rb(1073742336,D.e,D.e,[]),i.Rb(1073742336,P.b,P.b,[]),i.Rb(1073742336,k.b,k.b,[]),i.Rb(1073742336,R.c,R.c,[]),i.Rb(1073742336,A.a,A.a,[]),i.Rb(1073742336,J.d,J.d,[]),i.Rb(1073742336,F.c,F.c,[]),i.Rb(1073742336,O.a,O.a,[]),i.Rb(1073742336,N.a,N.a,[[2,R.g],i.O]),i.Rb(1073742336,q.b,q.b,[]),i.Rb(1073742336,z.a,z.a,[]),i.Rb(1073742336,H.b,H.b,[]),i.Rb(1073742336,l.Tb,l.Tb,[]),i.Rb(1073742336,b,b,[]),i.Rb(256,w.n,"XSRF-TOKEN",[]),i.Rb(256,w.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,S.m,void 0,[]),i.Rb(256,S.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,a.i,function(){return[[{path:"",component:d}]]},[])])}),W=[[".canvasWithGreyBorder{border:1px solid #696969!important}"]],j=i.Hb({encapsulation:2,styles:W,data:{}});function U(t){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{totalsContainer:0}),i.Zb(402653184,3,{displaycontainer:0}),i.Zb(402653184,4,{refreshButton:0}),i.Zb(402653184,5,{optionsButton:0}),i.Zb(402653184,6,{closeButton:0}),i.Zb(402653184,7,{tabs:0}),i.Zb(402653184,8,{displayContainerToday:0}),i.Zb(402653184,9,{displayContainerTodayPlus:0}),i.Zb(402653184,10,{displayContainerTodayPlusPlus:0}),i.Zb(402653184,11,{displayContainerTodayPlusThree:0}),i.Zb(402653184,12,{displayContainerTodayPlusFour:0}),i.Zb(402653184,13,{displayContainerTodayPlusFive:0}),i.Zb(402653184,14,{displayContainerTodayPlusSix:0}),i.Zb(402653184,15,{displayContainerSelected:0}),i.Zb(402653184,16,{ccyCombo:0}),i.Zb(402653184,17,{entityCombo:0}),i.Zb(402653184,18,{locationCombo:0}),i.Zb(402653184,19,{monitorCombo:0}),i.Zb(402653184,20,{groupCombo:0}),i.Zb(402653184,21,{groupcomboLabel:0}),i.Zb(402653184,22,{lostConnectionText:0}),i.Zb(402653184,23,{selectedEntity:0}),i.Zb(402653184,24,{selectedCcy:0}),i.Zb(402653184,25,{selectedGroup:0}),i.Zb(402653184,26,{locationType:0}),i.Zb(402653184,27,{dataBuildingText:0}),i.Zb(402653184,28,{dataExport:0}),i.Zb(402653184,29,{lastRefTime:0}),i.Zb(402653184,30,{loadingImage:0}),i.Zb(402653184,31,{sodText:0}),i.Zb(402653184,32,{startDate:0}),i.Zb(402653184,33,{selectGroup:0}),(t()(),i.Jb(33,0,null,null,147,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,o){var i=!0,n=t.component;"creationComplete"===e&&(i=!1!==n.onLoad()&&i);return i},I.ad,I.hb)),i.Ib(34,4440064,null,0,l.yb,[i.r,l.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(35,0,null,0,145,"VBox",[["height","100%"],["id","vBox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,I.od,I.vb)),i.Ib(36,4440064,null,0,l.ec,[i.r,l.i,i.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),i.Jb(37,0,null,0,103,"SwtCanvas",[["height","120"],["minWidth","800"],["width","100%"]],null,null,null,I.Nc,I.U)),i.Ib(38,4440064,null,0,l.db,[i.r,l.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),i.Jb(39,0,null,0,101,"Grid",[["height","100%"],["width","100%"]],null,null,null,I.Cc,I.H)),i.Ib(40,4440064,null,0,l.z,[i.r,l.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(41,0,null,0,27,"GridRow",[],null,null,null,I.Bc,I.J)),i.Ib(42,4440064,null,0,l.B,[i.r,l.i],null,null),(t()(),i.Jb(43,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,I.Ac,I.I)),i.Ib(44,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(45,0,null,0,3,"GridItem",[["width","120"]],null,null,null,I.Ac,I.I)),i.Ib(46,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(47,0,null,0,1,"SwtLabel",[["id","entityLabel"],["textDictionaryId","bookCode.entity"]],null,null,null,I.Yc,I.fb)),i.Ib(48,4440064,[["entityLabel",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(49,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(50,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(51,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"],["width","135"]],null,[[null,"open"],[null,"close"],[null,"change"],["window","mousewheel"]],function(t,e,o){var n=!0,l=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,52).mouseWeelEventHandler(o.target)&&n);"open"===e&&(n=!1!==l.openedCombo(o)&&n);"close"===e&&(n=!1!==l.closedCombo(o)&&n);"change"===e&&(n=!1!==l.entityChangeCombo(o)&&n);return n},I.Pc,I.W)),i.Ib(52,4440064,[[17,4],["entityCombo",4]],0,l.gb,[i.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),i.Jb(53,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(54,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(55,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedEntity"],["paddingLeft","10"]],null,null,null,I.Yc,I.fb)),i.Ib(56,4440064,[[23,4],["selectedEntity",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(57,0,null,0,11,"GridItem",[["width","50%"]],null,null,null,I.Ac,I.I)),i.Ib(58,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(59,0,null,0,9,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,I.Dc,I.K)),i.Ib(60,4440064,null,0,l.C,[i.r,l.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),i.Jb(61,0,null,0,3,"GridItem",[["width","181"]],null,null,null,I.Ac,I.I)),i.Ib(62,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(63,0,null,0,1,"SwtLabel",[["id","dateLabel"],["textDictionaryId","bookMonitor.date"]],null,null,null,I.Yc,I.fb)),i.Ib(64,4440064,[["dateLabel",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(65,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(66,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(67,0,null,0,1,"SwtDateField",[["id","startDate"],["restrict","0-9/"],["toolTip","Enter value date (DD/MM/YYYY)"],["width","70"]],null,[[null,"change"],[null,"open"],[null,"close"],[null,"focusOut"]],function(t,e,o){var i=!0,n=t.component;"change"===e&&(i=!1!==n.timeUpdate(o)&&i);"open"===e&&(i=!1!==n.openedCombo(o)&&i);"close"===e&&(i=!1!==n.closedDateField(o)&&i);"focusOut"===e&&(i=!1!==n.validateStartDate(o)&&i);return i},I.Tc,I.ab)),i.Ib(68,4308992,[[32,4],["startDate",4]],0,l.lb,[i.r,l.i,i.T],{restrict:[0,"restrict"],toolTip:[1,"toolTip"],id:[2,"id"],width:[3,"width"]},{openEventOutPut:"open",closeEventOutPut:"close",changeEventOutPut:"change",focusOutEventOutPut:"focusOut"}),(t()(),i.Jb(69,0,null,0,27,"GridRow",[],null,null,null,I.Bc,I.J)),i.Ib(70,4440064,null,0,l.B,[i.r,l.i],null,null),(t()(),i.Jb(71,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,I.Ac,I.I)),i.Ib(72,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(73,0,null,0,3,"GridItem",[["width","120"]],null,null,null,I.Ac,I.I)),i.Ib(74,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(75,0,null,0,1,"SwtLabel",[["id","currencyLabel"],["textDictionaryId","bookMonitor.currency"]],null,null,null,I.Yc,I.fb)),i.Ib(76,4440064,[["currencyLabel",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(77,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(78,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(79,0,null,0,1,"SwtComboBox",[["dataLabel","currency"],["id","ccyCombo"],["width","135"]],null,[[null,"change"],[null,"open"],[null,"close"],["window","mousewheel"]],function(t,e,o){var n=!0,l=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,80).mouseWeelEventHandler(o.target)&&n);"change"===e&&(n=!1!==l.changeCombo(o)&&n);"open"===e&&(n=!1!==l.openedCombo(o)&&n);"close"===e&&(n=!1!==l.closedCombo(o)&&n);return n},I.Pc,I.W)),i.Ib(80,4440064,[[16,4],["ccyCombo",4]],0,l.gb,[i.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),i.Jb(81,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(82,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(83,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedCcy"],["paddingLeft","10"]],null,null,null,I.Yc,I.fb)),i.Ib(84,4440064,[[24,4],["selectedCcy",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(85,0,null,0,11,"GridItem",[["width","50%"]],null,null,null,I.Ac,I.I)),i.Ib(86,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(87,0,null,0,9,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,I.Dc,I.K)),i.Ib(88,4440064,null,0,l.C,[i.r,l.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),i.Jb(89,0,null,0,3,"GridItem",[["width","145"]],null,null,null,I.Ac,I.I)),i.Ib(90,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(91,0,null,0,1,"SwtLabel",[["textDictionaryId","bookMonitor.monitor"]],null,null,null,I.Yc,I.fb)),i.Ib(92,4440064,null,0,l.vb,[i.r,l.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),i.Jb(93,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(94,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(95,0,null,0,1,"SwtComboBox",[["dataLabel","monitor"],["id","monitorCombo"],["width","135"]],null,[[null,"open"],[null,"close"],[null,"change"],["window","mousewheel"]],function(t,e,o){var n=!0,l=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,96).mouseWeelEventHandler(o.target)&&n);"open"===e&&(n=!1!==l.openedCombo(o)&&n);"close"===e&&(n=!1!==l.closedCombo(o)&&n);"change"===e&&(n=!1!==l.monitorComboChangeHandle(o)&&n);return n},I.Pc,I.W)),i.Ib(96,4440064,[[19,4],["monitorCombo",4]],0,l.gb,[i.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),i.Jb(97,0,null,0,27,"GridRow",[],null,null,null,I.Bc,I.J)),i.Ib(98,4440064,null,0,l.B,[i.r,l.i],null,null),(t()(),i.Jb(99,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,I.Ac,I.I)),i.Ib(100,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(101,0,null,0,3,"GridItem",[["width","120"]],null,null,null,I.Ac,I.I)),i.Ib(102,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(103,0,null,0,1,"SwtLabel",[["id","locationLabel"],["textDictionaryId","bookMonitor.location"]],null,null,null,I.Yc,I.fb)),i.Ib(104,4440064,[["locationLabel",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(105,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(106,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(107,0,null,0,1,"SwtComboBox",[["dataLabel","location"],["id","locationCombo"],["width","135"]],null,[[null,"open"],[null,"close"],[null,"change"],["window","mousewheel"]],function(t,e,o){var n=!0,l=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,108).mouseWeelEventHandler(o.target)&&n);"open"===e&&(n=!1!==l.openedCombo(o)&&n);"close"===e&&(n=!1!==l.closedCombo(o)&&n);"change"===e&&(n=!1!==l.changeCombo(o)&&n);return n},I.Pc,I.W)),i.Ib(108,4440064,[[18,4],["locationCombo",4]],0,l.gb,[i.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),i.Jb(109,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(110,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(111,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","locationType"],["paddingLeft","10"]],null,null,null,I.Yc,I.fb)),i.Ib(112,4440064,[[26,4],["locationType",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(113,0,null,0,11,"GridItem",[["width","50%"]],null,null,null,I.Ac,I.I)),i.Ib(114,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(115,0,null,0,9,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,I.Dc,I.K)),i.Ib(116,4440064,null,0,l.C,[i.r,l.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),i.Jb(117,0,null,0,3,"GridItem",[["width","125"]],null,null,null,I.Ac,I.I)),i.Ib(118,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(119,0,null,0,1,"SwtLabel",[["textDictionaryId","bookMonitor.balance"]],null,null,null,I.Yc,I.fb)),i.Ib(120,4440064,null,0,l.vb,[i.r,l.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),i.Jb(121,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(122,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(123,0,null,0,1,"SwtTextInput",[["editable","false"],["id","sodText"],["textAlign","right"],["width","160"]],null,null,null,I.kd,I.sb)),i.Ib(124,4440064,[[31,4],["sodText",4]],0,l.Rb,[i.r,l.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],editable:[3,"editable"]},null),(t()(),i.Jb(125,0,null,0,15,"GridRow",[["id","selectGroup"]],null,null,null,I.Bc,I.J)),i.Ib(126,4440064,[[33,4],["selectGroup",4]],0,l.B,[i.r,l.i],{id:[0,"id"]},null),(t()(),i.Jb(127,0,null,0,13,"GridItem",[["width","50%"]],null,null,null,I.Ac,I.I)),i.Ib(128,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(129,0,null,0,3,"GridItem",[["width","120"]],null,null,null,I.Ac,I.I)),i.Ib(130,4440064,null,0,l.A,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(131,0,null,0,1,"SwtLabel",[["id","groupcomboLabel"],["textDictionaryId","groupMonitor.group"]],null,null,null,I.Yc,I.fb)),i.Ib(132,4440064,[[21,4],["groupcomboLabel",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),i.Jb(133,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(134,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(135,0,null,0,1,"SwtComboBox",[["dataLabel","groupCode"],["id","groupCombo"],["width","135"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,o){var n=!0,l=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,136).mouseWeelEventHandler(o.target)&&n);"change"===e&&(n=!1!==l.changeCombo(o)&&n);return n},I.Pc,I.W)),i.Ib(136,4440064,[[20,4],["groupCombo",4]],0,l.gb,[i.r,l.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),i.Jb(137,0,null,0,3,"GridItem",[],null,null,null,I.Ac,I.I)),i.Ib(138,4440064,null,0,l.A,[i.r,l.i],null,null),(t()(),i.Jb(139,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedGroup"],["paddingLeft","10"]],null,null,null,I.Yc,I.fb)),i.Ib(140,4440064,[[25,4],["selectedGroup",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],paddingLeft:[1,"paddingLeft"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(141,0,null,0,2,"SwtTabNavigator",[["borderBottom","false"],["height","3%"],["id","tabs"],["width","100%"]],null,[[null,"onChange"]],function(t,e,o){var i=!0,n=t.component;"onChange"===e&&(i=!1!==n.tabChange(o)&&i);return i},I.id,I.pb)),i.Ib(142,4440064,[[7,4],["tabs",4]],1,l.Ob,[i.r,l.i,i.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],borderBottom:[3,"borderBottom"]},{onChange_:"onChange"}),i.Zb(603979776,34,{tabChildren:1}),(t()(),i.Jb(144,0,null,0,5,"VBox",[["height","100%"],["minWidth","800"],["paddingLeft","5"],["styleName","borderVBox"],["width","100%"]],null,null,null,I.od,I.vb)),i.Ib(145,4440064,null,0,l.ec,[i.r,l.i,i.T],{styleName:[0,"styleName"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"],paddingLeft:[4,"paddingLeft"]},null),(t()(),i.Jb(146,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","displaycontainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,I.Nc,I.U)),i.Ib(147,4440064,[[3,4],["displaycontainer",4]],0,l.db,[i.r,l.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),i.Jb(148,0,null,0,1,"SwtCanvas",[["border","false"],["height","40"],["id","totalsContainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,I.Nc,I.U)),i.Ib(149,4440064,[[2,4],["totalsContainer",4]],0,l.db,[i.r,l.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),i.Jb(150,0,null,0,30,"SwtCanvas",[["height","40"],["marginBottom","0"],["minWidth","800"],["width","100%"]],null,null,null,I.Nc,I.U)),i.Ib(151,4440064,null,0,l.db,[i.r,l.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],marginBottom:[3,"marginBottom"]},null),(t()(),i.Jb(152,0,null,0,28,"HBox",[["width","100%"]],null,null,null,I.Dc,I.K)),i.Ib(153,4440064,null,0,l.C,[i.r,l.i],{width:[0,"width"]},null),(t()(),i.Jb(154,0,null,0,7,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,I.Dc,I.K)),i.Ib(155,4440064,null,0,l.C,[i.r,l.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(156,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(t,e,o){var i=!0,n=t.component;"click"===e&&(i=!1!==n.updateData("yes")&&i);return i},I.Mc,I.T)),i.Ib(157,4440064,[[4,4],["refreshButton",4]],0,l.cb,[i.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(158,0,null,0,1,"SwtButton",[["id","optionsButton"]],null,[[null,"click"]],function(t,e,o){var i=!0,n=t.component;"click"===e&&(i=!1!==n.optionsHandler()&&i);return i},I.Mc,I.T)),i.Ib(159,4440064,[[5,4],["optionsButton",4]],0,l.cb,[i.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(160,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,o){var i=!0,n=t.component;"click"===e&&(i=!1!==n.closeHandler()&&i);return i},I.Mc,I.T)),i.Ib(161,4440064,[[6,4],["closeButton",4]],0,l.cb,[i.r,l.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(162,0,null,0,9,"HBox",[["horizontalAlign","right"],["horizontalGap","2"]],null,null,null,I.Dc,I.K)),i.Ib(163,4440064,null,0,l.C,[i.r,l.i],{horizontalGap:[0,"horizontalGap"],horizontalAlign:[1,"horizontalAlign"]},null),(t()(),i.Jb(164,0,null,0,1,"SwtLabel",[["color","red"],["height","16"],["id","dataBuildingText"],["textDictionaryId","screen.buildInProgress"],["visible","false"]],null,null,null,I.Yc,I.fb)),i.Ib(165,4440064,[[27,4],["dataBuildingText",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"],height:[2,"height"],visible:[3,"visible"],color:[4,"color"]},null),(t()(),i.Jb(166,0,null,0,1,"SwtLabel",[["color","red"],["height","16"],["id","lostConnectionText"],["textDictionaryId","screen.connectionError"],["visible","false"]],null,[[null,"click"]],function(t,e,o){var i=!0,n=t.component;"click"===e&&(i=!1!==n.connError(o)&&i);return i},I.Yc,I.fb)),i.Ib(167,4440064,[[22,4],["lostConnectionText",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"],height:[2,"height"],visible:[3,"visible"],color:[4,"color"]},{onClick_:"click"}),(t()(),i.Jb(168,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["textDictionaryId","screen.lastRefresh"]],null,null,null,I.Yc,I.fb)),i.Ib(169,4440064,null,0,l.vb,[i.r,l.i],{textDictionaryId:[0,"textDictionaryId"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(170,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","lastRefTime"],["styleName","labelLeftRefTime"]],null,null,null,I.Yc,I.fb)),i.Ib(171,4440064,[[29,4],["lastRefTime",4]],0,l.vb,[i.r,l.i],{id:[0,"id"],styleName:[1,"styleName"],fontWeight:[2,"fontWeight"]},null),(t()(),i.Jb(172,0,null,0,8,"HBox",[["horizontalAlign","right"],["horizontalGap","2"]],null,null,null,I.Dc,I.K)),i.Ib(173,4440064,null,0,l.C,[i.r,l.i],{horizontalGap:[0,"horizontalGap"],horizontalAlign:[1,"horizontalAlign"]},null),(t()(),i.Jb(174,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),i.Jb(175,0,null,null,1,"DataExport",[["id","dataExport"]],null,null,null,I.Sc,I.Z)),i.Ib(176,4440064,[[28,4],["dataExport",4]],0,l.kb,[l.i,i.r],{id:[0,"id"]},null),(t()(),i.Jb(177,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,o){var i=!0,n=t.component;"click"===e&&(i=!1!==n.doHelp()&&i);return i},I.Wc,I.db)),i.Ib(178,4440064,null,0,l.rb,[i.r,l.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),i.Jb(179,0,null,0,1,"SwtLoadingImage",[],null,null,null,I.Zc,I.gb)),i.Ib(180,114688,[[30,4],["loadingImage",4]],0,l.xb,[i.r],null,null)],function(t,e){t(e,34,0,"100%","100%");t(e,36,0,"vBox1","100%","100%","5","5","5","5");t(e,38,0,"100%","120","800");t(e,40,0,"100%","100%"),t(e,42,0);t(e,44,0,"50%");t(e,46,0,"120");t(e,48,0,"entityLabel","bookCode.entity"),t(e,50,0);t(e,52,0,"entity","135","entityCombo"),t(e,54,0);t(e,56,0,"selectedEntity","10","normal");t(e,58,0,"50%");t(e,60,0,"right","100%");t(e,62,0,"181");t(e,64,0,"dateLabel","bookMonitor.date"),t(e,66,0);t(e,68,0,"0-9/","Enter value date (DD/MM/YYYY)","startDate","70"),t(e,70,0);t(e,72,0,"50%");t(e,74,0,"120");t(e,76,0,"currencyLabel","bookMonitor.currency"),t(e,78,0);t(e,80,0,"currency","135","ccyCombo"),t(e,82,0);t(e,84,0,"selectedCcy","10","normal");t(e,86,0,"50%");t(e,88,0,"right","100%");t(e,90,0,"145");t(e,92,0,"bookMonitor.monitor"),t(e,94,0);t(e,96,0,"monitor","135","monitorCombo"),t(e,98,0);t(e,100,0,"50%");t(e,102,0,"120");t(e,104,0,"locationLabel","bookMonitor.location"),t(e,106,0);t(e,108,0,"location","135","locationCombo"),t(e,110,0);t(e,112,0,"locationType","10","normal");t(e,114,0,"50%");t(e,116,0,"right","100%");t(e,118,0,"125");t(e,120,0,"bookMonitor.balance"),t(e,122,0);t(e,124,0,"sodText","right","160","false");t(e,126,0,"selectGroup");t(e,128,0,"50%");t(e,130,0,"120");t(e,132,0,"groupcomboLabel","groupMonitor.group"),t(e,134,0);t(e,136,0,"groupCode","135","groupCombo"),t(e,138,0);t(e,140,0,"selectedGroup","10","normal");t(e,142,0,"tabs","100%","3%","false");t(e,145,0,"borderVBox","100%","100%","800","5");t(e,147,0,"displaycontainer","canvasWithGreyBorder","100%","100%","false");t(e,149,0,"totalsContainer","canvasWithGreyBorder","100%","40","false");t(e,151,0,"100%","40","800","0");t(e,153,0,"100%");t(e,155,0,"100%","5");t(e,157,0,"refreshButton",!0);t(e,159,0,"optionsButton",!0);t(e,161,0,"closeButton",!0);t(e,163,0,"2","right");t(e,165,0,"dataBuildingText","screen.buildInProgress","16","false","red");t(e,167,0,"lostConnectionText","screen.connectionError","16","false","red");t(e,169,0,"screen.lastRefresh","normal");t(e,171,0,"lastRefTime","labelLeftRefTime","normal");t(e,173,0,"2","right");t(e,176,0,"dataExport");t(e,178,0,"helpIcon"),t(e,180,0)},null)}function _(t){return i.dc(0,[(t()(),i.Jb(0,0,null,null,1,"app-book-group-monitor",[],null,null,null,U,j)),i.Ib(1,4440064,null,0,d,[l.i,i.r],null,null)],function(t,e){t(e,1,0)},null)}var Y=i.Fb("app-book-group-monitor",d,_,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);