{"_from": "q@^1.5.1", "_id": "q@1.5.1", "_inBundle": false, "_integrity": "sha512-kV/CThkXo6xyFEZUugw/+pIOywXcDbFYgSct5cT3gqlbkBE1SJdwy6UQoZvodiWF/ckQLZyDE/Bu1M6gVu5lVw==", "_location": "/q", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "q@^1.5.1", "name": "q", "escapedName": "q", "rawSpec": "^1.5.1", "saveSpec": null, "fetchSpec": "^1.5.1"}, "_requiredBy": ["/excel-builder-webpacker"], "_resolved": "https://registry.npmjs.org/q/-/q-1.5.1.tgz", "_shasum": "7e32f75b41381291d04611f1bf14109ac00651d7", "_spec": "q@^1.5.1", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\excel-builder-webpacker", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kriskowal"}, "bugs": {"url": "http://github.com/kriskowal/q/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kriskowal"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "http://jeditoolkit.com"}, {"name": "Domenic <PERSON>", "email": "<EMAIL>", "url": "http://domenicdenicola.com"}], "dependencies": {}, "deprecated": "You or someone you depend on is using Q, the JavaScript Promise library that gave JavaScript developers strong feelings about promises. They can almost certainly migrate to the native JavaScript promise now. Thank you literally everyone for joining me in this bet against the odds. Be excellent to each other.\n\n(For a CapTP with native promises, see @endo/eventual-send and @endo/captp)", "description": "A library for promises (CommonJS/Promises/A,B,D)", "devDependencies": {"cover": "*", "grunt": "~0.4.1", "grunt-cli": "~0.1.9", "grunt-contrib-uglify": "~0.9.1", "jasmine-node": "1.11.0", "jshint": "~2.1.9", "matcha": "~0.2.0", "opener": "*", "promises-aplus-tests": "1.x"}, "directories": {"test": "./spec"}, "engines": {"node": ">=0.6.0", "teleport": ">=0.2.0"}, "files": ["LICENSE", "q.js", "queue.js"], "homepage": "https://github.com/kriskowal/q", "keywords": ["q", "promise", "promises", "promises-a", "promises-aplus", "deferred", "future", "async", "flow control", "fluent", "browser", "node"], "license": "MIT", "main": "q.js", "name": "q", "overlay": {"teleport": {"dependencies": {"system": ">=0.0.4"}}}, "repository": {"type": "git", "url": "git://github.com/kriskowal/q.git"}, "scripts": {"benchmark": "matcha", "cover": "cover run jasmine-node spec && cover report html && opener cover_html/index.html", "lint": "j<PERSON>t q.js", "minify": "grunt", "prepublish": "grunt", "test": "npm ls -s && jasmine-node spec && promises-aplus-tests spec/aplus-adapter && npm run -s lint", "test-browser": "opener spec/q-spec.html"}, "version": "1.5.1"}