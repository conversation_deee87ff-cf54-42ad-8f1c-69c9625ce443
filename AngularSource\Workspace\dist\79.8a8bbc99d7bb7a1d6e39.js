(window.webpackJsonp=window.webpackJsonp||[]).push([[79],{dJpt:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),l=i("mrSG"),a=i("447K"),o=i("ZYCi"),s=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.jsonReader=new a.L,n.inputData=new a.G(n.commonService),n.saveData=new a.G(n.commonService),n.requestParams=[],n.baseURL=a.Wb.getBaseURL(),n.menuAccessId=null,n.templateId=null,n.templateName=null,n.isPublic=null,n.selectedUserId=null,n.screenName=null,n.callFrom=null,n.copyFromFlag=!1,n.copiedTemplateId=null,n.copiedUserId=null,n.color=null,n.closeWindow=!1,n.ordinalPos=null,n.rowId=0,n.columnId=null,n.displayName=null,n.description=null,n.type=null,n.modify=null,n.swtAlert=new a.bb(e),n}return l.d(e,t),e.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this,this.lblTempId.text=a.Wb.getPredictMessage("label.forecastMonitorTemplateAdd.templateId",null),this.txtTemplateId.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAdd.templateId",null),this.lblUserId.text=a.Wb.getPredictMessage("label.forecastMonitorTemplateAdd.userId",null),this.cbUserId.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAdd.userId",null),this.templateNameLabel.text=a.Wb.getPredictMessage("label.forecastMonitorTemplateAdd.templateName",null),this.txtTemplateName.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAdd.templateName",null),this.publicLabel.text=a.Wb.getPredictMessage("label.forecastMonitorTemplateAdd.public",null),this.chbPublic.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAdd.public",null),this.imgUpButton.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAdd.moveUp",null),this.imgDownButton.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAdd.moveDown",null),this.cpyFromButton.label=a.Wb.getPredictMessage("button.cpyFrom",null),this.cpyFromButton.toolTip=a.Wb.getPredictMessage("button.cpyFrom",null),this.addButton.label=a.Wb.getPredictMessage("button.add",null),this.addButton.toolTip=a.Wb.getPredictMessage("button.add",null),this.changeButton.label=a.Wb.getPredictMessage("button.change",null),this.changeButton.toolTip=a.Wb.getPredictMessage("button.change",null),this.deleteButton.label=a.Wb.getPredictMessage("button.delete",null),this.deleteButton.toolTip=a.Wb.getPredictMessage("button.delete",null),this.saveButton.label=a.Wb.getPredictMessage("button.close",null),this.saveButton.toolTip=a.Wb.getPredictMessage("button.close",null)},e.prototype.onLoad=function(){var t=this;this.forecastMonitorTemplateMainGrid=this.forecastMonitorTemplateMainCanvas.addChild(a.hb);try{this.selectedUserId=a.x.call("eval","userId"),this.templateId=a.x.call("eval","templateId"),this.templateName=a.x.call("eval","templateName"),this.isPublic=a.x.call("eval","isPublic"),this.screenName=a.x.call("eval","screenName"),this.callFrom=a.x.call("eval","callFrom"),this.actionPath="forecastMonitorTemplate.do?",null!=this.screenName&&"changeScreen"==this.screenName?(this.actionMethod="method=displayChangeMonitorTemplate&loadFlex=true",this.actionMethod=this.actionMethod+"&templateId="+this.templateId,this.actionMethod=this.actionMethod+"&templateName="+this.templateName,this.actionMethod=this.actionMethod+"&userId="+this.selectedUserId,this.actionMethod=this.actionMethod+"&isPublic="+this.isPublic):(this.lblTempId.text=this.lblTempId.text+" *",this.lblUserId.text=this.lblUserId.text+" *",this.actionMethod="method=displayAddMonitorTemplate&loadFlex=true"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.forecastMonitorTemplateMainGrid.onRowClick=function(e){t.cellLogic(e)}}catch(e){console.log("errr",e)}},e.prototype.copyResult=function(t){this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.imgDownButton.enabled=!1,this.imgUpButton.enabled=!1,this.forecastMonitorTemplateMainGrid.gridData=this.jsonReader.getGridData(),this.gridJSONList=this.jsonReader.getGridData()},e.prototype.inputDataResult=function(t){var e=this;try{if(this.inputData&&this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(!this.jsonReader.isDataBuilding()){this.cbUserId.setComboData(this.jsonReader.getSelects(),!0),this.txtTemplateId.text=this.jsonReader.getScreenAttributes().selectedTemplateId,this.txtTemplateName.text=this.jsonReader.getScreenAttributes().selectedTemplateName,this.changeHighLightBorder(),null!=this.screenName&&"changeScreen"==this.screenName&&(this.txtTemplateId.enabled=!1,this.cbUserId.enabled=!1,this.chbPublic.selected=!("No"==this.isPublic));var i={columns:this.jsonReader.getColumnData()};this.forecastMonitorTemplateMainGrid.CustomGrid(i),this.jsonReader.getGridData()?(this.forecastMonitorTemplateMainGrid.gridData=this.jsonReader.getGridData(),this.gridJSONList=this.jsonReader.getGridData(),this.forecastMonitorTemplateMainGrid.rowColorFunction=function(t,i,n){return e.drawRowBackground(t,i,n)},this.forecastMonitorTemplateMainGrid.setRowSize=this.jsonReader.getRowSize(),this.deleteButton.enabled=!1,this.changeButton.enabled=!1,this.imgDownButton.enabled=!1,this.imgUpButton.enabled=!1):(this.forecastMonitorTemplateMainGrid.dataProvider=[],this.forecastMonitorTemplateMainGrid.selectedIndex=-1),"*DEFAULT*"==this.cbUserId.selectedLabel||"*DEFAULT*"==this.txtTemplateId.text?(this.chbPublic.enabled=!1,this.chbPublic.selected=!0):this.chbPublic.enabled=!0}this.prevRecievedJSON=this.lastRecievedJSON}else this.swtAlert.error("Generic exception error")}catch(n){console.log("error in inputData",n)}},e.prototype.drawRowBackground=function(t,e,i){var n;try{var l;"grey"==(l=t.slickgrid_rowcontent.columnno.color)?n="#C9C9C9":"red"==l&&(n="#FA8C8C")}catch(a){console.log("error drawRowBackground ",a)}return n},e.prototype.closeHandler=function(){this.swtAlert.question(a.Wb.getPredictMessage("alert.forecasttemplate.savetemplate",null),"Confirm",a.c.YES|a.c.NO,null,this.saveChanges.bind(this),null)},e.prototype.saveChanges=function(t){var e=this,i=[],n=[];if(t.detail==a.c.YES){var l=a.x.call("checkTemplateExists",this.txtTemplateId.text.toString(),this.cbUserId.selectedLabel);if(""!=a.Z.trim(this.txtTemplateId.text.toString())&&""!=a.Z.trim(this.txtTemplateName.text.toString()))if("changeScreen"==this.screenName||"true"!=l){if(this.saveData.cbStart=this.startOfComms.bind(this),this.saveData.cbStop=this.endOfComms.bind(this),this.saveData.cbResult=function(t){e.saveDataResult(t)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1,this.actionPath="forecastMonitorTemplate.do?method=","changeScreen"==this.screenName?this.actionMethod="updateTemplates":this.actionMethod="saveTemplates",this.requestParams.templateId=a.Z.trim(this.txtTemplateId.text.toString()),this.requestParams.templateName=a.Z.trim(this.txtTemplateName.text.toString()),this.requestParams.userId=this.cbUserId.selectedLabel,1==this.chbPublic.selected?this.requestParams.isPublic="Y":this.requestParams.isPublic="N",this.copyFromFlag&&(this.requestParams.copyFromFlag="true",this.requestParams.copiedTemplateId=this.copiedTemplateId,this.requestParams.copiedUserId=this.copiedUserId),this.forecastMonitorTemplateMainGrid.dataProvider.length>0)for(var o=0;o<this.forecastMonitorTemplateMainGrid.dataProvider.length;o++)i[o]=this.forecastMonitorTemplateMainGrid.dataProvider[o].rowid,n[o]=this.forecastMonitorTemplateMainGrid.dataProvider[o].displayname;this.requestParams.saveOrdinalPos=i,this.requestParams.saveDisplayName=n,this.saveData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveData.send(this.requestParams)}else this.swtAlert.warning(a.Wb.getPredictMessage("alert.forecasttemplate.templateexist",null));else this.swtAlert.warning(a.Wb.getPredictMessage("alert.forecasttemplate.mandatory",null)),""==this.txtTemplateId.text?this.txtTemplateId.setFocus():""==this.txtTemplateName.text&&this.txtTemplateName.setFocus()}else a.x.call("clearSessionInstance"),a.x.call("close")},e.prototype.saveDataResult=function(t){var e=t;this.imgDownButton.enabled=!1,this.imgUpButton.enabled=!1,null!=this.callFrom&&""!=this.callFrom?(a.x.call("clearSessionInstance"),a.x.call("refreshMonitor")):(this.jsonReader.setInputJSON(e),this.jsonReader.getRequestReplyStatus()?(a.x.call("clearSessionInstance"),window.opener&&window.opener.instanceElement&&window.opener.instanceElement.refreshdetails(),window.close()):(this.lastRecievedJSON.hasOwnProperty("requestthis.reply")&&"true"==this.lastRecievedJSON.requestthis.reply.closewindow&&(this.closeWindow=!0),this.swtAlert.warning(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error",a.c.OK,this,null)))},e.prototype.disableOrEnableButtons=function(t){t?(this.changeButton.enabled=!0,this.deleteButton.enabled="red"!=this.color):(this.changeButton.enabled=!1,this.deleteButton.enabled=!1)},e.prototype.changeHighLightBorder=function(){this.txtTemplateId.required=""==this.txtTemplateId.text,this.txtTemplateName.required=""==this.txtTemplateName.text},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(){this.swtAlert.error("Generic exception error")},e.prototype.moveRecord=function(t){var e,i,n=this.forecastMonitorTemplateMainGrid.selectedIndex;"imgUpButton"==t?(i=this.gridJSONList.row[n],this.gridJSONList.row[n]=this.gridJSONList.row[n-1],this.gridJSONList.row[n].rowid.content=this.forecastMonitorTemplateMainGrid.dataProvider[n].rowid,this.gridJSONList.row[n-1]=i,this.gridJSONList.row[n-1].rowid.content=this.forecastMonitorTemplateMainGrid.dataProvider[n-1].rowid,e=this.gridJSONList,this.forecastMonitorTemplateMainGrid.gridData=e,this.forecastMonitorTemplateMainGrid.selectedIndex=n-1):(i=this.gridJSONList.row[n],this.gridJSONList.row[n]=this.gridJSONList.row[n+1],this.gridJSONList.row[n].rowid.content=this.forecastMonitorTemplateMainGrid.dataProvider[n].rowid,this.gridJSONList.row[n+1]=i,this.gridJSONList.row[n+1].rowid.content=this.forecastMonitorTemplateMainGrid.dataProvider[n+1].rowid,e=this.gridJSONList,this.forecastMonitorTemplateMainGrid.gridData=e,this.forecastMonitorTemplateMainGrid.selectedIndex=n+1),this.cellClickEventHandler(event)},e.prototype.cellLogic=function(t){this.forecastMonitorTemplateMainGrid.selectedIndex>-1?(this.ordinalPos=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].ordinalpos,this.rowId=parseInt(this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].rowid),this.columnId=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].columnno,this.displayName=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].displayname,this.description=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].description,this.type=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].type,this.modify=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].modify,this.color=this.forecastMonitorTemplateMainGrid.dataProvider[this.forecastMonitorTemplateMainGrid.selectedIndex].slickgrid_rowcontent.columnno.color,"grey"!=this.color?this.disableOrEnableButtons(!0):this.disableOrEnableButtons(!1)):this.disableOrEnableButtons(!1),this.cellClickEventHandler(t)},e.prototype.cellClickEventHandler=function(t){var e,i,n,l=0,a=0;0==this.forecastMonitorTemplateMainGrid.selectedIndices.length||this.forecastMonitorTemplateMainGrid.isFiltered?(this.imgUpButton.enabled=!1,this.imgDownButton.enabled=!1):this.forecastMonitorTemplateMainGrid.selectable&&(l=this.forecastMonitorTemplateMainGrid.selectedIndex,a=this.forecastMonitorTemplateMainGrid.dataProvider.length,(e=this.forecastMonitorTemplateMainGrid.dataProvider[l])&&"Fixed"!=e.type?(l+1<a&&(n=this.forecastMonitorTemplateMainGrid.dataProvider[l+1]),l-1>=0&&(i=this.forecastMonitorTemplateMainGrid.dataProvider[l-1]),this.imgUpButton.enabled=i&&"Fixed"!=i.type,this.imgDownButton.enabled=n&&"Fixed"!=n.type):(this.imgDownButton.enabled=!1,this.imgUpButton.enabled=!1))},e.prototype.doHelp=function(){a.x.call("help")},e.prototype.loadCopyfrom=function(){this.actionMethod="loadCopyFrom","changeScreen"==this.screenName&&(this.actionMethod=this.actionMethod+"&templateId="+this.templateId),this.actionMethod=this.actionMethod+"&userId="+this.cbUserId.selectedLabel,a.x.call("openCopyFromWindow",this.actionMethod)},e.prototype.reloadCopy=function(t,e){var i=this;this.actionMethod="method=displayCopyMonitorTemplate&loadFlex=true",this.actionMethod=this.actionMethod+"&templateId="+e,this.actionMethod=this.actionMethod+"&userId="+t,this.actionMethod=this.actionMethod+"&actualUserId="+this.selectedUserId,this.actionMethod=this.actionMethod+"&screen=copyFrom",this.copyFromFlag=!0,this.inputData.cbResult=function(t){i.copyResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.addClickHandler=function(){if(""!=a.Z.trim(this.txtTemplateId.text.toString())&&""!=a.Z.trim(this.txtTemplateName.text.toString())){var t=a.x.call("checkTemplateExists",this.txtTemplateId.text.toString(),this.cbUserId.selectedLabel);"changeScreen"==this.screenName||"true"!=t?(this.actionMethod="addForecastDetails&templateId="+this.txtTemplateId.text,this.actionMethod=this.actionMethod+"&templateName="+this.txtTemplateName.text,this.actionMethod=this.actionMethod+"&userId="+this.cbUserId.selectedLabel,this.actionMethod=this.actionMethod+"&addClick="+!0,a.x.call("openChildWindow",this.actionMethod)):this.swtAlert.warning(a.Wb.getPredictMessage("alert.forecasttemplate.templateexist",null))}else this.swtAlert.warning(a.Wb.getPredictMessage("alert.forecasttemplate.mandatory",null)),""==this.txtTemplateId.text?this.txtTemplateId.setFocus():""==this.txtTemplateName.text&&this.txtTemplateName.setFocus()},e.prototype.changeClickHandler=function(){""!=a.Z.trim(this.txtTemplateId.text.toString())&&""!=a.Z.trim(this.txtTemplateName.text.toString())?(this.actionMethod="changeForecastDetails","save"==this.modify?this.actionMethod=this.actionMethod+"&ordinalPos="+this.columnId:"Total"==this.displayName?this.actionMethod=this.actionMethod+"&ordinalPos="+this.ordinalPos:this.actionMethod=this.actionMethod+"&ordinalPos="+this.rowId,this.actionMethod=this.actionMethod+"&userId="+this.cbUserId.selectedLabel,this.actionMethod=this.actionMethod+"&columnId="+this.columnId,this.actionMethod=this.actionMethod+"&templateId="+this.txtTemplateId.text,this.actionMethod=this.actionMethod+"&templateName="+this.txtTemplateName.text,this.actionMethod=this.actionMethod+"&shortName="+this.displayName,this.actionMethod=this.actionMethod+"&description="+escape(escape(this.description)),this.actionMethod=this.actionMethod+"&type="+this.type,this.actionMethod=this.actionMethod+"&modify="+this.modify,this.copyFromFlag&&(this.actionMethod=this.actionMethod+"&copyFromFlag=true",this.actionMethod=this.actionMethod+"&copiedTemplateId="+this.copiedTemplateId,this.actionMethod=this.actionMethod+"&copiedUserId="+this.copiedUserId),this.actionMethod=this.actionMethod+"&modify="+this.modify,a.x.call("openChildWindow",this.actionMethod)):(this.swtAlert.warning(a.Wb.getPredictMessage("alert.forecasttemplate.mandatory",null)),""==this.txtTemplateId.text?this.txtTemplateId.setFocus():""==this.txtTemplateName.text&&this.txtTemplateName.setFocus())},e.prototype.deleteHandler=function(){this.swtAlert.question(a.Wb.getPredictMessage("alert.forecasttemplate.columndelete",null),null,a.c.YES|a.c.NO,null,this.removeRecord.bind(this),null)},e.prototype.removeRecord=function(t){t.detail==a.c.YES&&(this.requestParams.shortName=this.forecastMonitorTemplateMainGrid.selectedItem.displayname.content,this.actionMethod="method=deleteForecastColumn",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.chbPublic.selected?this.isPublic="Yes":this.isPublic="No",this.forecastMonitorTemplateMainGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1)},e.prototype.userChange=function(){"*DEFAULT*"==this.cbUserId.selectedLabel?(this.chbPublic.enabled=!1,this.chbPublic.selected=!0):(this.chbPublic.selected=!1,this.chbPublic.enabled=!0)},e.prototype.reloadMain=function(){var t=this;this.actionMethod="method=refreshMonitorTemplate",this.inputData.cbResult=function(e){t.refreshDetailGrid(e)},this.inputData.cbFault=this.inputDataFault,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.refreshDetailGrid=function(t){this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.imgDownButton.enabled=!1,this.imgUpButton.enabled=!1,this.deleteButton.enabled=!1,this.changeButton.enabled=!1,this.forecastMonitorTemplateMainGrid.gridData=this.jsonReader.getGridData(),this.gridJSONList=this.jsonReader.getGridData(),this.forecastMonitorTemplateMainGrid.setRowSize=this.jsonReader.getRowSize()},e}(a.yb),r=[{path:"",component:s}],d=(o.l.forChild(r),function(){return function(){}}()),h=i("pMnS"),c=i("RChO"),u=i("t6HQ"),b=i("WFGK"),p=i("5FqG"),m=i("Ip0R"),g=i("gIcY"),M=i("t/Na"),w=i("sE5F"),f=i("OzfB"),T=i("T7CS"),I=i("S7LP"),R=i("6aHO"),x=i("WzUx"),v=i("A7o+"),y=i("zCE2"),B=i("Jg5P"),P=i("3R0m"),S=i("hhbb"),C=i("5rxC"),N=i("Fzqc"),D=i("21Lb"),L=i("hUWP"),G=i("3pJQ"),k=i("V9q+"),F=i("VDKW"),J=i("kXfT"),O=i("BGbe");i.d(e,"ForecastMonitorTemplateMainModuleNgFactory",function(){return U}),i.d(e,"RenderType_ForecastMonitorTemplateMain",function(){return W}),i.d(e,"View_ForecastMonitorTemplateMain_0",function(){return _}),i.d(e,"View_ForecastMonitorTemplateMain_Host_0",function(){return H}),i.d(e,"ForecastMonitorTemplateMainNgFactory",function(){return q});var U=n.Gb(d,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[h.a,c.a,u.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,q]],[3,n.n],n.J]),n.Rb(4608,m.m,m.l,[n.F,[2,m.u]]),n.Rb(4608,g.c,g.c,[]),n.Rb(4608,g.p,g.p,[]),n.Rb(4608,M.j,M.p,[m.c,n.O,M.n]),n.Rb(4608,M.q,M.q,[M.j,M.o]),n.Rb(5120,M.a,function(t){return[t,new a.tb]},[M.q]),n.Rb(4608,M.m,M.m,[]),n.Rb(6144,M.k,null,[M.m]),n.Rb(4608,M.i,M.i,[M.k]),n.Rb(6144,M.b,null,[M.i]),n.Rb(4608,M.f,M.l,[M.b,n.B]),n.Rb(4608,M.c,M.c,[M.f]),n.Rb(4608,w.c,w.c,[]),n.Rb(4608,w.g,w.b,[]),n.Rb(5120,w.i,w.j,[]),n.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),n.Rb(4608,w.f,w.a,[]),n.Rb(5120,w.d,w.k,[w.h,w.f]),n.Rb(5120,n.b,function(t,e){return[f.j(t,e)]},[m.c,n.O]),n.Rb(4608,T.a,T.a,[]),n.Rb(4608,I.a,I.a,[]),n.Rb(4608,R.a,R.a,[n.n,n.L,n.B,I.a,n.g]),n.Rb(4608,x.c,x.c,[n.n,n.g,n.B]),n.Rb(4608,x.e,x.e,[x.c]),n.Rb(4608,v.l,v.l,[]),n.Rb(4608,v.h,v.g,[]),n.Rb(4608,v.c,v.f,[]),n.Rb(4608,v.j,v.d,[]),n.Rb(4608,v.b,v.a,[]),n.Rb(4608,v.k,v.k,[v.l,v.h,v.c,v.j,v.b,v.m,v.n]),n.Rb(4608,x.i,x.i,[[2,v.k]]),n.Rb(4608,x.r,x.r,[x.L,[2,v.k],x.i]),n.Rb(4608,x.t,x.t,[]),n.Rb(4608,x.w,x.w,[]),n.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),n.Rb(1073742336,m.b,m.b,[]),n.Rb(1073742336,g.n,g.n,[]),n.Rb(1073742336,g.l,g.l,[]),n.Rb(1073742336,y.a,y.a,[]),n.Rb(1073742336,B.a,B.a,[]),n.Rb(1073742336,g.e,g.e,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,v.i,v.i,[]),n.Rb(1073742336,x.b,x.b,[]),n.Rb(1073742336,M.e,M.e,[]),n.Rb(1073742336,M.d,M.d,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,S.b,S.b,[]),n.Rb(1073742336,C.b,C.b,[]),n.Rb(1073742336,f.c,f.c,[]),n.Rb(1073742336,N.a,N.a,[]),n.Rb(1073742336,D.d,D.d,[]),n.Rb(1073742336,L.c,L.c,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,k.a,k.a,[[2,f.g],n.O]),n.Rb(1073742336,F.b,F.b,[]),n.Rb(1073742336,J.a,J.a,[]),n.Rb(1073742336,O.b,O.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,d,d,[]),n.Rb(256,M.n,"XSRF-TOKEN",[]),n.Rb(256,M.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,v.m,void 0,[]),n.Rb(256,v.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,o.i,function(){return[[{path:"",component:s}]]},[])])}),A=[[""]],W=n.Hb({encapsulation:0,styles:A,data:{}});function _(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{forecastMonitorTemplateMainCanvas:0}),n.Zb(402653184,3,{addButton:0}),n.Zb(402653184,4,{cpyFromButton:0}),n.Zb(402653184,5,{changeButton:0}),n.Zb(402653184,6,{deleteButton:0}),n.Zb(402653184,7,{saveButton:0}),n.Zb(402653184,8,{imgUpButton:0}),n.Zb(402653184,9,{imgDownButton:0}),n.Zb(402653184,10,{lblTempId:0}),n.Zb(402653184,11,{templateNameLabel:0}),n.Zb(402653184,12,{lblUserId:0}),n.Zb(402653184,13,{publicLabel:0}),n.Zb(402653184,14,{txtTemplateName:0}),n.Zb(402653184,15,{txtTemplateId:0}),n.Zb(402653184,16,{cbUserId:0}),n.Zb(402653184,17,{chbPublic:0}),n.Zb(402653184,18,{loadingImage:0}),(t()(),n.Jb(18,0,null,null,69,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"],[null,"close"]],function(t,e,i){var n=!0,l=t.component;"creationComplete"===e&&(n=!1!==l.onLoad()&&n);"close"===e&&(n=!1!==l.closeHandler()&&n);return n},p.ad,p.hb)),n.Ib(19,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(20,0,null,0,67,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(21,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(22,0,null,0,31,"SwtCanvas",[["height","15%"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(23,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(24,0,null,0,29,"VBox",[["height","100%"],["paddingLeft","10"],["paddingTop","5"],["verticalGap","0"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(25,4440064,null,0,a.ec,[n.r,a.i,n.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingLeft:[4,"paddingLeft"]},null),(t()(),n.Jb(26,0,null,0,13,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(27,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(28,0,null,0,5,"HBox",[["width","50%"]],null,null,null,p.Dc,p.K)),n.Ib(29,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(30,0,null,0,1,"SwtLabel",[["width","120"]],null,null,null,p.Yc,p.fb)),n.Ib(31,4440064,[[10,4],["lblTempId",4]],0,a.vb,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(32,0,null,0,1,"SwtTextInput",[["maxChars","15"],["width","160"]],null,[[null,"focusOut"]],function(t,e,i){var n=!0,l=t.component;"focusOut"===e&&(n=!1!==l.changeHighLightBorder()&&n);return n},p.kd,p.sb)),n.Ib(33,4440064,[[15,4],["txtTemplateId",4]],0,a.Rb,[n.r,a.i],{maxChars:[0,"maxChars"],width:[1,"width"]},{onFocusOut_:"focusOut"}),(t()(),n.Jb(34,0,null,0,5,"HBox",[["width","50%"]],null,null,null,p.Dc,p.K)),n.Ib(35,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(36,0,null,0,1,"SwtLabel",[["width","70"]],null,null,null,p.Yc,p.fb)),n.Ib(37,4440064,[[12,4],["lblUserId",4]],0,a.vb,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(38,0,null,0,1,"SwtComboBox",[["dataLabel","users"],["width","160"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var l=!0,a=t.component;"window:mousewheel"===e&&(l=!1!==n.Tb(t,39).mouseWeelEventHandler(i.target)&&l);"change"===e&&(l=!1!==a.userChange()&&l);return l},p.Pc,p.W)),n.Ib(39,4440064,[[16,4],["cbUserId",4]],0,a.gb,[n.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"]},{change_:"change"}),(t()(),n.Jb(40,0,null,0,13,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(41,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(42,0,null,0,5,"HBox",[["width","50%"]],null,null,null,p.Dc,p.K)),n.Ib(43,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(44,0,null,0,1,"SwtLabel",[["width","120"]],null,null,null,p.Yc,p.fb)),n.Ib(45,4440064,[[11,4],["templateNameLabel",4]],0,a.vb,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(46,0,null,0,1,"SwtTextInput",[["maxChars","50"],["width","160"]],null,[[null,"focusOut"]],function(t,e,i){var n=!0,l=t.component;"focusOut"===e&&(n=!1!==l.changeHighLightBorder()&&n);return n},p.kd,p.sb)),n.Ib(47,4440064,[[14,4],["txtTemplateName",4]],0,a.Rb,[n.r,a.i],{maxChars:[0,"maxChars"],width:[1,"width"]},{onFocusOut_:"focusOut"}),(t()(),n.Jb(48,0,null,0,5,"HBox",[["width","50%"]],null,null,null,p.Dc,p.K)),n.Ib(49,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(50,0,null,0,1,"SwtLabel",[["width","70"]],null,null,null,p.Yc,p.fb)),n.Ib(51,4440064,[[13,4],["publicLabel",4]],0,a.vb,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(52,0,null,0,1,"SwtCheckBox",[],null,null,null,p.Oc,p.V)),n.Ib(53,4440064,[[17,4],["chbPublic",4]],0,a.eb,[n.r,a.i],null,null),(t()(),n.Jb(54,0,null,0,11,"SwtCanvas",[["height","75%"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(55,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(56,0,null,0,9,"HBox",[["height","100%"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(57,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(58,0,null,0,1,"SwtCanvas",[["height","100%"],["width","96%"]],null,null,null,p.Nc,p.U)),n.Ib(59,4440064,[[2,4],["forecastMonitorTemplateMainCanvas",4]],0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(60,0,null,0,5,"VBox",[["height","100%"],["horizontalAlign","center"],["verticalAlign","middle"],["width","5%"]],null,null,null,p.od,p.vb)),n.Ib(61,4440064,null,0,a.ec,[n.r,a.i,n.T],{horizontalAlign:[0,"horizontalAlign"],verticalAlign:[1,"verticalAlign"],width:[2,"width"],height:[3,"height"]},null),(t()(),n.Jb(62,0,null,0,1,"SwtButton",[["buttonMode","false"],["enabled","false"],["id","upButton"],["styleName","upButtonEnable"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.moveRecord("imgUpButton")&&n);return n},p.Mc,p.T)),n.Ib(63,4440064,[[8,4],["imgUpButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(64,0,null,0,1,"SwtButton",[["buttonMode","false"],["enabled","false"],["id","downButton"],["styleName","downButtonEnable"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.moveRecord("imgDownButton")&&n);return n},p.Mc,p.T)),n.Ib(65,4440064,[[9,4],["imgDownButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(66,0,null,0,21,"SwtCanvas",[["height","8%"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(67,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(68,0,null,0,19,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(69,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(70,0,null,0,11,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(71,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(72,0,null,0,1,"SwtButton",[["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.loadCopyfrom()&&n);return n},p.Mc,p.T)),n.Ib(73,4440064,[[4,4],["cpyFromButton",4]],0,a.cb,[n.r,a.i],{width:[0,"width"]},{onClick_:"click"}),(t()(),n.Jb(74,0,null,0,1,"SwtButton",[["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.addClickHandler()&&n);return n},p.Mc,p.T)),n.Ib(75,4440064,[[3,4],["addButton",4]],0,a.cb,[n.r,a.i],{width:[0,"width"]},{onClick_:"click"}),(t()(),n.Jb(76,0,null,0,1,"SwtButton",[["enabled","false"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.changeClickHandler()&&n);return n},p.Mc,p.T)),n.Ib(77,4440064,[[5,4],["changeButton",4]],0,a.cb,[n.r,a.i],{width:[0,"width"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(78,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.deleteHandler()&&n);return n},p.Mc,p.T)),n.Ib(79,4440064,[[6,4],["deleteButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(80,0,null,0,1,"SwtButton",[["id","saveButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.closeHandler()&&n);return n},p.Mc,p.T)),n.Ib(81,4440064,[[7,4],["saveButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),n.Jb(82,0,null,0,5,"HBox",[["horizontalAlign","right"],["right","5"],["top","3"]],null,null,null,p.Dc,p.K)),n.Ib(83,4440064,null,0,a.C,[n.r,a.i],{right:[0,"right"],top:[1,"top"],horizontalAlign:[2,"horizontalAlign"]},null),(t()(),n.Jb(84,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.doHelp()&&n);return n},p.Wc,p.db)),n.Ib(85,4440064,[["helpIcon",4]],0,a.rb,[n.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(86,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),n.Ib(87,114688,[[18,4],["loadingImage",4]],0,a.xb,[n.r],null,null)],function(t,e){t(e,19,0,"100%","100%");t(e,21,0,"100%","100%","5","5","5","5");t(e,23,0,"100%","15%");t(e,25,0,"0","100%","100%","5","10");t(e,27,0,"100%");t(e,29,0,"50%");t(e,31,0,"120");t(e,33,0,"15","160");t(e,35,0,"50%");t(e,37,0,"70");t(e,39,0,"users","160");t(e,41,0,"100%");t(e,43,0,"50%");t(e,45,0,"120");t(e,47,0,"50","160");t(e,49,0,"50%");t(e,51,0,"70"),t(e,53,0);t(e,55,0,"100%","75%");t(e,57,0,"100%","100%");t(e,59,0,"96%","100%");t(e,61,0,"center","middle","5%","100%");t(e,63,0,"upButton","upButtonEnable","false","false");t(e,65,0,"downButton","downButtonEnable","false","false");t(e,67,0,"100%","8%");t(e,69,0,"100%");t(e,71,0,"100%","5");t(e,73,0,"70");t(e,75,0,"70");t(e,77,0,"70","false");t(e,79,0,"deleteButton","70","false");t(e,81,0,"saveButton","70");t(e,83,0,"5","3","right");t(e,85,0,"helpIcon"),t(e,87,0)},null)}function H(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-forecast-monitor-template-main",[],null,null,null,_,W)),n.Ib(1,4440064,null,0,s,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var q=n.Fb("app-forecast-monitor-template-main",s,H,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);