import { Component, ElementRef, HostListener, ViewChild, ModuleWithProviders, NgModule } from '@angular/core';
import moment from "moment";
import {
  CommonService,
  CommonUtil,
  ExternalInterface,
  focusManager,
  HashMap,
  HBox,
  HTTPComms,
  JSONReader,
  Keyboard,
  Logger,
  Spacer,
  StringUtils,
  SwtAlert,
  SwtButton,
  SwtCanvas,
  SwtComboBox,
  SwtCommonGrid,
  SwtDateField,
  SwtLabel,
  SwtList,
  SwtLoadingImage,
  SwtModule,
  SwtPopUpManager,
  SwtStepper,
  SwtTextArea,
  SwtTextInput,
  SwtUtil,
  TitleWindow,
  VBox,
  SwtToolBoxModule,
  Alert
} from 'swt-tool-box';
import { ListValues } from "../ListValues/ListValues";
import { Routes, RouterModule } from '@angular/router';
declare function validateCurrencyPlaces(strField, strPat, currCode): any;
declare  function validateFormatTime(strField): any;

@Component({
  selector: 'app-rules-definition-add-rule',
  templateUrl: './RulesDefinitionAddRule.html',
  styleUrls: ['./RulesDefinitionAddRule.css']
})
export class RulesDefinitionAddRule extends SwtModule {
 
 

  @HostListener('window:unload', ['$event'])
      unloadHandler(event) {
        window.opener.instanceElement.enableButtons();
  }

  /**********************  Labels ***************************************************/
  @ViewChild('propertiesLabel') propertiesLabel: SwtLabel;
  @ViewChild('operator') operator: SwtLabel;
  @ViewChild('nameQueryLabel') nameQueryLabel: SwtLabel;
  @ViewChild('nameSortLabel') nameSortLabel: SwtLabel;
  @ViewChild('labelQuery') labelQuery: SwtLabel;

  /**********************  CheckBox ***************************************************/
 // @ViewChild('chkboxClosed') chkboxClosed: SwtCheckBox;




  /**********************  Buttons ***************************************************/
  @ViewChild('undoButton') undoButton:  SwtButton;
  @ViewChild('leftParentheseButton') leftParentheseButton:  SwtButton;
  @ViewChild('rightParentheseButton') rightParentheseButton:  SwtButton;
  @ViewChild('andButton') andButton:  SwtButton;
  @ViewChild('orButton') orButton:  SwtButton;
  @ViewChild('saveButton') saveButton:  SwtButton;
  @ViewChild('okButton') okButton:  SwtButton;
  @ViewChild('okSortButton') okSortButton:  SwtButton;
  @ViewChild('cancelSortButton') cancelSortButton:  SwtButton;
  @ViewChild('removeSortButton') removeSortButton:  SwtButton;
  @ViewChild('addButton') addButton:  SwtButton;
  @ViewChild('callSortButton') callSortButton:  SwtButton;
  @ViewChild('callButton') callButton:  SwtButton;
  @ViewChild('resetButton') resetButton:  SwtButton;
  @ViewChild('equalButton') equalButton:  SwtButton;
  @ViewChild('differentButton') differentButton:  SwtButton;
  @ViewChild('higherButton') higherButton:  SwtButton;
  @ViewChild('higherequalButton') higherequalButton:  SwtButton;
  @ViewChild('lowerequalButton') lowerequalButton:  SwtButton;
  @ViewChild('lowerButton') lowerButton:  SwtButton;
  @ViewChild('notLikeButton') notLikeButton:  SwtButton;
  @ViewChild('inButton') inButton:  SwtButton;
  @ViewChild('notinButton') notinButton:  SwtButton;
  @ViewChild('likeButton') likeButton:  SwtButton;
  @ViewChild('betweenButton') betweenButton:  SwtButton;
  @ViewChild('inListButton') inListButton:  SwtButton;
  @ViewChild('cancelButton') cancelButton:  SwtButton;
  @ViewChild('helpIcon') helpIcon:  SwtButton;
  @ViewChild('saveSortButton') saveSortButton:  SwtButton;
  @ViewChild('resetSortButton') resetSortButton:  SwtButton;
  @ViewChild('addItemsAscBtn') addItemsAscBtn:  SwtButton;
  @ViewChild('addItemsDescBtn') addItemsDescBtn:  SwtButton;
  @ViewChild('removeItemsButton') removeItemsButton:  SwtButton;
  @ViewChild('upButton') upButton:  SwtButton;
  @ViewChild('downButton') downButton:  SwtButton;
  @ViewChild('changeButton') changeButton:  SwtButton;
  @ViewChild('changeCritButton') changeCritButton:  SwtButton;
  @ViewChild('containsButton') containsButton:  SwtButton;
  
  /**********************  loadingImage **************************************************************************************/
  @ViewChild('loadingImage') loadingImage:    SwtLoadingImage;

  /**********************  TextInput **************************************************************************************/
  @ViewChild('filterText') filterText:    SwtTextInput;

  /**********************  commonGrid **************************************************************************************/
  private listCriteria: SwtCommonGrid;
  /**********************  HBox **************************************************************************************/
  @ViewChild('buttonHbox') buttonHbox: HBox;
  @ViewChild('hBoxContainer') hBoxContainer: HBox;
  @ViewChild('hBoxContainer2') hBoxContainer2: HBox;
  
  /**********************  VBox **************************************************************************************/

  @ViewChild('vBoxContainer') vBoxContainer: VBox;
  @ViewChild('queryText') queryText: SwtTextArea;

  /**********************  SwtCanvas ***************************************************/
  @ViewChild('customGrid') customGrid: SwtCanvas;

  /**********************  SwtList ***************************************************/
  @ViewChild('rightItemsList') rightItemsList: SwtList;
  @ViewChild('leftItemsList') leftItemsList: SwtList;





  private swtAlert: SwtAlert;

  /**
   * Private parameters
   **/
  private logger: Logger = null;
  // Request parameters to server
  private requestParams = [];
  // Application base url
  public baseURL = SwtUtil.getBaseURL() ; //"http://localhost:8081/swallowtech/"
  // Servlet method
  private actionMethod = null;
  // Servlet path
  private actionPath = null;
  // Variable for HTTPComms, which extends HttpService
  private  inputData:  HTTPComms = new HTTPComms(this.commonService);
  // To parse the response (result) json
  public jsonReader: JSONReader = new JSONReader();
  // To store result json
  private lastRecievedJSON: JSON = null;
  // Current Grid JSON
  private gridJSONList = null;
  public originalJSONList = null;
  private firstJSONList = null;
  private firstGridData = null;


  // To get tab name of the current search screen
  public tabName: string = null;
  // typeId
  public typeId: string = null;

  /**
   * Variables used to set label as Button Object
   **/

  private like: string = null;

  private notLike: string = null;

  private into: string = null;

  private notIn: string = null;

  private between: string = null;

  public operation: string = '';

  public operationToDisplay: string = '';

  private and: string = null;

  private or: string = null;

  private inList: string = null;
  private exists: string = null;
  private errorLocation=0;

  //to count number of opened and close parenthesis

  private countLeftP = 1;

  private countRightP = 1;

  // to store displayed query
  private arrayToDisplayQuery = [];
  // to store query that will be executed
  private arrayToExecuteQuery = [];

  // to store the composed query to execute
  public queryToExecute = '';


  public queryToDisplay = '';

  public andOrString = '';

  public andOrStringToDisplay = '';

  //////////

  private queryToDisplay2 = '';

  /**
   * Display Object
   *
   **/
  private comboBox;
  private comboBox2;
  private textInput;
  private textInput2;
  private listValuesButton;
  private dateField;
  private dateField2;
  private timeField;
  private timeField2;
  private sysDateButton;
  private tolerenceNumStep;
  private labelBetweenCombo;

  /**
   * Date variables
   **/
  private sysdateformat: string = null;
  private nameDays = [];
  private nameMonths = [];
  private tempNameDays = '';
  private tempNameMonths = '';
  // to get selected items from ListValues screen
  public selectedItemsList = null;

  // Currently focussed property id
  private buttonId: string = null;

  // string variable to hold moduleId
  public moduleId: string = null;
  // String variable to hold programId
  public programId: string = null;

  // Array variable to hold tables to join
  private tableToJoin = [];
  // String variable to hold tables name
  private tabNames: string = null;
  // int variable to hold index
  private index:number = 0;
  private tabAllConditions = [];
  private agregationFunc = [];
  private varToBind = [];
  private sysDateActive:boolean = false;
  private aliasList = [];
  public currQueryToDisplay: string = '';
  public currentSort: string = '';
  // variable to get the filter used in the current grid with the select form
  public currentFilter: string = '';
  // variable to get the sort used in the current grid
  public currentSortToDisplay: string = '';
  private commonSysDateFormat: string = 'DD/MM/YYYY';

  // userScreen
  public screenName: string = null;

  // ArrayCollection to hold left list


  private sortTab: string = '';
  private recUnitGroupDisplay: string = 'F';
  private execStatus: string = null;

  private isValidQuery:boolean = false;

  private actionToDo: string = null;
  //To store error message
  private  invalidComms: string = null;
  private externalFields: string = null;
  private ruleType: string = null;
  private ruleId: string = null;
  private message: string = null;

  private  previousCondId: number=0;
  private  previousTypeCode: string="";
  public fromSource: string;
  private filterTextOfInput: string = "";
  private spacer;

  private win:TitleWindow;
  private dateTimeArray = [];


  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.logger = new Logger('Search', this.commonService.httpclient);
    this.swtAlert = new SwtAlert(this.commonService);
  }




  /**
   * loadSearchDefaults
   *
   * Method to load the screen id and screen name
   */
  loadSearchDefaults() {
    try {

      let parentParams;
      
      if(window.opener && window.opener.instanceElement) {
        
        parentParams  = window.opener.instanceElement.getParamsFromParent();
        
        this.screenName = parentParams[0].screenName;
        if(parentParams[0].tabAllConditions && parentParams[0].tabAllConditions.length>0) {
          this.tabAllConditions = JSON.parse(parentParams[0].tabAllConditions);
          
        }
        if(parentParams[0].tableToJoin && parentParams[0].tableToJoin.length>0) {
          this.tableToJoin = JSON.parse(parentParams[0].tableToJoin);
        }

        if(parentParams[0].queryToExecute) {
          this.queryToExecute = parentParams[0].queryToExecute;
        }
        if(parentParams[0].queryToDisplay) {
          this.queryToDisplay = parentParams[0].queryToDisplay;
        }
     


      }
      if(!this.screenName){
        this.screenName = "add";
      }


      this.listCriteria  = <SwtCommonGrid>this.customGrid.addChild(SwtCommonGrid);
      if (this.screenName === 'change') {
         this.addButton.visible = false;
         this.addButton.includeInLayout = false;
        this.undoButton.visible = this.undoButton.includeInLayout = false;
        this.labelQuery.visible = true;
        this.leftParentheseButton.visible =this.leftParentheseButton.includeInLayout = false;
        this.rightParentheseButton.visible = this.rightParentheseButton.includeInLayout = false;
        this.andButton.visible = this.andButton.includeInLayout = false;
        this.changeButton.visible = true;
        this.orButton.visible = this.orButton.includeInLayout = false;
        this.changeCritButton.visible = true;

      } else {
        this.changeButton.visible = this.changeButton.includeInLayout = false;
        this.labelQuery.visible = this.labelQuery.includeInLayout = false;
        this.changeCritButton.visible =this.changeCritButton.includeInLayout = false;
      }
 

      if (this.screenName === 'change'){
        if (this.parentDocument && this.parentDocument.searchQuery!== '') {
          this.queryToDisplay = this.parentDocument.queryToDisplay;
          this.queryToExecute = this.parentDocument.searchQuery;
        
          this.title = SwtUtil.getCommonMessages('rulesDefinitionScreen.windowtitle.help_screen');
          this.message = SwtUtil.getCommonMessages('rulesDefinitionScreen.message.help_message');
        }
      }
     

      this.like = SwtUtil.getCommonMessages('searchScreen.label.like');
      this.notLike = SwtUtil.getCommonMessages('searchScreen.label.notLike');
      this.into = SwtUtil.getCommonMessages('searchScreen.label.in');
      this.notIn = SwtUtil.getCommonMessages('searchScreen.label.notIn');
      this.between = SwtUtil.getCommonMessages('searchScreen.label.between');
      this.and = "and"; //SwtUtil.getCommonMessages('searchScreen.label.and');
      this.or = SwtUtil.getCommonMessages('searchScreen.label.or');
      this.inList = SwtUtil.getCommonMessages('searchScreen.label.inList');
      this.exists = SwtUtil.getCommonMessages('searchScreen.label.contains');

      this.undoButton.label =  "Undo"; //SwtUtil.getCommonMessages('button.undo');
      //this.undoButton.toolTip = SwtUtil.getCommonMessages('button.tooltip.undo');
     this.addButton.label = "Add"; // SwtUtil.getCommonMessages('button.add');
     // this.addButton.toolTip = SwtUtil.getCommonMessages('button.tooltip.add');
      this.changeButton.label = "Change"; //SwtUtil.getCommonMessages('button.change');
      //this.changeButton.toolTip = SwtUtil.getCommonMessages('button.tooltip.change');
      //this.changeCritButton.toolTip = SwtUtil.getCommonMessages('button.tooltip.change');

      this.tempNameDays = "S,M,T,W,T,F,S";
      this.tempNameMonths = "January,February,March,April,May,June,July,August,September,October,November,December";
      this.nameDays = this.tempNameDays.split(',');
      this.nameMonths = this.tempNameMonths.split(',');
    } catch (error) {
      console.log('error', error);
    }
  }



  /**
   * init
   *
   * @param none
   *
   * This method perform multilingual and get the search criteria value
   */

   init(): void {
    try {
      this.actionMethod = 'method=getSearchListCriteria';
      this.actionPath = 'expressionBuilderPCM.do?';
      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbResult = (data) => {
        this.inputDataResult(data);
      };
      // this.inputDataResult(data);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.requestParams['method']= 'getSearchListCriteria';
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      // Array instance to store the parameter
      this.requestParams = [];
      if (this.screenName =='add') {
        this.requestParams['typeId'] = 10;
        this.requestParams['moduleId'] = this.moduleId;
        this.requestParams['programId'] = this.programId;
        this.requestParams['externalFields'] = this.externalFields;
        this.requestParams['ruleType'] = this.ruleType;
        this.requestParams['screenName'] = this.screenName;
      } else {
        this.requestParams['typeId'] = 10;
        this.requestParams['moduleId'] = this.moduleId;
        this.requestParams['programId'] = this.programId;
        this.requestParams['externalFields'] = this.externalFields;
        this.requestParams['ruleType'] = this.ruleType;
        this.requestParams['ruleId'] = this.ruleId;
        this.requestParams['screenName'] = this.screenName;
      }
      // send request to server
      this.inputData.send(this.requestParams);
    } catch (error) {
      console.log('error in init', error);
    }
  }


  /**
   * inputDataResult
   *
   * @param data: JSON
   *
   * This is a callback method, to handle result event
   */
  inputDataResult(data): void {
    //object json
    let jsonlist = null;
    let row: any;
    // pattern
    let pattern:RegExp = null;
    let unexprectedError = SwtUtil.getPredictMessage('queryBuilderScreen.alert.unexprectedError', null);
    try {
      this.logger.info('method [inputDataResult] - START ');
      //If the service is busy then remove the busy cursor
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        
        if (this.tabAllConditions && this.tabAllConditions.length > 0) {
        let listOfColumnsToChange = [];
        let conditionIdsOfColumns = [];
         
          for (let f = 0; f < this.tabAllConditions.length; f++) {
            listOfColumnsToChange.push(this.tabAllConditions[f].columnToStore);
            conditionIdsOfColumns[this.tabAllConditions[f].columnToStore] = this.tabAllConditions[f];
          }
          JSONReader.jsonpath(data,'$.search.grid.rows.*').forEach(function (value) {
            if(value === Object(value) && listOfColumnsToChange.indexOf(value.code)>-1) {

                value.conditionId = conditionIdsOfColumns[value.code].conditionId;
                value.toChange = 'Y';
                if(value.macroColumn) {
                  if(!conditionIdsOfColumns[value.code].clause) {
                    let macroColumn = value.macroColumn;
                    let expOper:RegExp=/ :op/ig;
                    let expParam:RegExp=/ :param/ig;
                    if(conditionIdsOfColumns[value.code].operation == 'in' || conditionIdsOfColumns[value.code].operation == 'not in'){
                      macroColumn=macroColumn.replace(expOper, " " + conditionIdsOfColumns[value.code].operation);
                      macroColumn=macroColumn.replace(expParam, "  " + StringUtils.trim(conditionIdsOfColumns[value.code].columnCodeValue ));
                    }else {
                      macroColumn=macroColumn.replace(expOper, " " + conditionIdsOfColumns[value.code].operation);
                      macroColumn=macroColumn.replace(expParam, " " + StringUtils.trim(conditionIdsOfColumns[value.code].columnCodeValue));

                    }
                    value.clause  = macroColumn;
                  }else {
                    value.clause = StringUtils.trim(conditionIdsOfColumns[value.code].clause)
                  }
                  
                }

                // value.clause = StringUtils.trim(conditionIdsOfColumns[value.code].clause);
                value.fieldValue = StringUtils.trim(conditionIdsOfColumns[value.code].columnCodeValue);
                value.operatorId= conditionIdsOfColumns[value.code].operation;
                value.condFieldName= StringUtils.trim(conditionIdsOfColumns[value.code].columnToStore);
                value.nextCondition= conditionIdsOfColumns[value.code].andOrString;
                value.profileFieldValue= conditionIdsOfColumns[value.code].profileFieldValue;
            }
          });
        }
          
        this.lastRecievedJSON = data;

        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        
        // If the result status is true, then load the grid
        if (this.jsonReader.getRequestReplyStatus()) {
           this.sysdateformat = this.jsonReader.getSingletons().dateFormat;
           this.currencyPattern = this.jsonReader.getSingletons().currencyPattern;
           
          // this.testDate = this.jsonReader.getSingletons().testdate;
          // // Pattern is m replace with 'M'
          // pattern = /y/gi;
          // // replace m to M
           this.sysdateformat = this.sysdateformat.replace(pattern, 'Y');
          // errorLocation = 50;
          // // Pattern is m replace with 'M'
          // pattern = /d/gi;
          // // replace m to M
           this.sysdateformat = this.sysdateformat.replace(pattern, 'D');
         
          let grid2dataset: any = [];
      if (this.screenName != 'change') {
            JSONReader.jsonpath(data,'$.search.grid.rows.*').forEach(function (value) {
              if(value === Object(value)){
                grid2dataset.push({'label': value});
              }
            });
      }
      if (this.screenName === 'change') {
        this.firstJSONList  = this.jsonReader.getGridData();
        this.originalJSONList = this.jsonReader.getGridData();
          JSONReader.jsonpath(data, '$.search.grid.rows.*').forEach(function (value) {
            if (value === Object(value) && value.toChange == 'Y' && Object(value).toChange != undefined) {

              grid2dataset.push({ 'label': value });
            }
          });
      }


      this.firstGridData =  {row: grid2dataset, size: grid2dataset.length};

          let metadata : any = {
            'columns':{
              'column':[
                {
                  columnorder:0,
                  dataelement: "label",
                  draggable:true,
                  filterable:true,
                  format: "",
                  heading: "Constraints",
                  sort:true,
                  type: "str",
                  visible:true,
                  visible_default:true,
                  width:300
                }
              ]
            }
          };


          this.listCriteria.CustomGrid(metadata);
          try{
              this.listCriteria.gridData =   {row: grid2dataset, size: grid2dataset.length};
              this.gridJSONList = this.listCriteria.gridData;
          } catch (e) {
          }
          // Add the event listener to listen for a cell click on a datagrid
          this.listCriteria.onRowClick = (event) => {
            this.selectCriteria();
          };
          this.filterText.enabled = true;
        } else {
          this.swtAlert.error(unexprectedError);
        }
      }
    } catch (error) {
      this.logger.error('method [inputDataResult] - error : ', error , '- errorLocation :',this.errorLocation);

      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this), 'inputDataResult', this.errorLocation);
    }
    this.logger.info('method [inputDataResult] - END ');
  }


  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  startOfComms(): void {
    this.loadingImage.setVisible(true);
    // this.disableInterface();
  }

  /**
   * Part of a callback function to all for control of the loading swf from the HTTPComms Object
   **/
  private endOfComms(): void {
    this.loadingImage.setVisible(false);
    //  this.enableInterface();
  }

  /**
   * If a fault occurs with the connection with the server then display the lost connection label
   **/
  public inputDataFault(event): void {
    this.invalidComms = event.faultString + '\n' + event.faultCode + '\n' + event.faultDetail;
    this.swtAlert.error(event.faultCode);

  }


  /**
   * doHelp
   *
   * @param event: Event
   *
   * Function is called when 'Help' button is click. Displays help window
   */
  public doHelp(event):void {
    try {
      ExternalInterface.call("help");
    } catch (error) {
        SwtUtil.logError(error, this.moduleId, 'ClassName', 'doHelp', this.errorLocation);
    }
  }



  /**
   * keyDownEventHandler
   *
   * @param event:  KeyboardEvent
   *
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(Button)
   */
  public keyDownEventHandler( event ): void {
    try {
      this.logger.info('method [keyDownEventHandler] - START ');

      if ( ( event.keyCode == Keyboard.ENTER ) ) {
       
        //Currently focused property name
        let eventString: string = Object( focusManager.getFocus() ).name;
        if ( eventString == 'addButton' ) {
         
          this.createQuery();
        }
        else if ( eventString == 'undoButton' ) {
         
          this.previous();
        }
        else if ( eventString == 'okButton' ) {
         
          this.executeQuery( event );
        }
        else if (eventString == "resetButton")
        {
          
          this.reset(event);
        }
        else if ( eventString == 'cancelButton' ) {
         
          this.close();
        }
        else if ( eventString == 'helpIcon' ) {
         
          this.doHelp( event );
        }
        else if ( eventString == 'equalButton' ) {
          this.operation = this.equalButton.label;
          this.operationToDisplay = this.equalButton.label;
          this.doOperation();
        }
        else if ( eventString == 'differentButton' ) {
          
          this.operation = this.differentButton.label;
          this.operationToDisplay = this.differentButton.label;
          this.doOperation();
        }
        else if ( eventString == 'higherButton' ) {
         
          this.operation = this.higherButton.label;
          this.operationToDisplay = this.higherButton.label;
          this.doOperation();
        }
        else if ( eventString == 'higherequalButton' ) {
          
          this.operation = this.higherequalButton.label;
          this.operationToDisplay = this.higherequalButton.label;
          this.doOperation();
        }
        else if ( eventString == 'lowerButton' ) {
          
          this.operation = this.lowerButton.label;
          this.operationToDisplay = this.lowerButton.label;
          this.doOperation();
        }
        else if ( eventString == 'lowerequalButton' ) {
         
          this.operation = this.lowerequalButton.label;
          this.operationToDisplay = this.lowerequalButton.label;
          this.doOperation();
        }
        else if ( eventString == 'likeButton' ) {
         
          this.operation = 'like';
          this.operationToDisplay = this.likeButton.label;
          this.doOperation();
        }
        else if ( eventString == 'notLikeButton' ) {
         
          this.operation = 'Not Like';
          this.operationToDisplay = this.notLikeButton.label;
          this.doOperation();
        }
        else if ( eventString == 'inButton' ) {
         
          this.operation = 'in';
          this.operationToDisplay = this.inButton.label;
          this.doOperation();
        }
        else if ( eventString == 'notinButton' ) {
         
          this.operation = 'not in';
          this.operationToDisplay = this.notinButton.label;
          this.doOperation();
        }
        else if ( eventString == 'betweenButton' ) {
         
          this.operation = 'between';
          this.operationToDisplay = this.betweenButton.label;
          this.doOperation();
        }
        else if ( eventString == 'andButton' ) {
         
          this.andOrString = 'and';
          this.andOrStringToDisplay = this.andButton.label;
          this.operationToDisplay = this.andButton.label;
          this.addOperation();
        }
        else if ( eventString == 'orButton' ) {
          this.andOrString = 'or';
          this.andOrStringToDisplay = this.orButton.label;
          this.operationToDisplay = this.orButton.label;
          this.addOperation();
        }
        else if ( eventString == 'leftParentheseButton' ) {
          this.doOpenCloseParentheses();
        }
        else if ( eventString == 'rightParentheseButton' ) {
          this.doOpenCloseParentheses();
        }
        else if ( eventString == 'listValuesButton' ) {
        
          this.addListValuesEventHandler( event );
        } else if ( eventString == 'sysDateButton' ) {
          
          this.addSysDateEventHandler( event );
        } else if ( eventString == 'inListButton' ) {
         
          this.operation = 'in list';
          this.operationToDisplay = this.inListButton.label;
          this.doOperation();
        }
        else if (eventString == "changeButton")
        {
         
          this.createQuery();
        } else if (eventString == "changeCritButton")
        {
          
          this.changeCriteria();
        }
        else if (eventString == "containsButton")
        {
         
          this.operation='exists';
          this.operationToDisplay =  this.containsButton.label;
          this.doOperation();
        }
      }
    } catch ( error ) {
      this.logger.error( 'method [keyDownEventHandler] - error : ', error, '- errorLocation :', this.errorLocation );

     // SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'keyDownEventHandler', this.errorLocation );
    }
    this.logger.info( 'method [keyDownEventHandler] - END ' );
  }
  /**
   * selectCriteria
   *
   *
   * This method is used to maintain the button operation when a criteria is clicked
   */
  private selectCriteria():void
  {
   
    
    try
    {

      this.logger.info('method [selectCriteria] - START ');

     
      /**/
      if (this.listCriteria.selectedIndices.length == 1 /*&& this.listCriteria.enabled*/)
      {        

        let code: string=this.listCriteria.selectedItem.label.code;
        let typeCode: string=this.listCriteria.selectedItem.label.typeCode;
        let typeEnum: string=this.listCriteria.selectedItem.label.typeEnum;
        let typeList: string=this.listCriteria.selectedItem.label.typeList;
        let macroColumn: string=this.listCriteria.selectedItem.label.macroColumn;
        let hiddenOperator: string=this.listCriteria.selectedItem.label.hiddenOperator;
        this.addButton.enabled = false;

        this.propertiesLabel.text=this.listCriteria.selectedItem.label.label;
        // set width of propertiesLabel to auto
        this.propertiesLabel.width=NaN;
        this.propertiesLabel.toolTip = null;

        this.hBoxContainer.removeAllChildren();
        /*if(vBoxContainer.contains(hBoxContainer2))
                  {
                      hBoxContainer2.removeAllChildren();
                      vBoxContainer.removeChild(hBoxContainer2);
                  }*/

        this.operator.text = '';
        
        if (typeCode == 'CHAR')
        {
          //if the data as char type
          
          this.equalButton.enabled=true;
          this.differentButton.enabled=true;
          this.higherButton.enabled = false;
          this.higherequalButton.enabled = false;
          this.lowerButton.enabled = false;
          this.lowerequalButton.enabled = false;
          if(code.toLowerCase().indexOf('time') > -1) {
            this.likeButton.enabled=false;
            this.notLikeButton.enabled=false;
          } else  {
            this.likeButton.enabled=true;
            this.notLikeButton.enabled=true;
          }
          this.inButton.enabled=true;
          this.notinButton.enabled=true;
          this.betweenButton.enabled = false;

          if (Number(typeList) != 0)
          {
            this.inListButton.enabled = true;
            this.containsButton.enabled = true;
          }
          else
          {
            this.inListButton.enabled = false;
            this.containsButton.enabled = false;

          }
          
        }
        else if (typeCode == 'NUM' || typeCode == "DECI")
        {
          // if the data as number type
          
          this.equalButton.enabled=true;
          this.differentButton.enabled=true;
          this.higherButton.enabled=true;
          this.higherequalButton.enabled=true;
          this.lowerButton.enabled=true;
          this.lowerequalButton.enabled=true;
          this.likeButton.enabled = false;
          this.notLikeButton.enabled = false;
          if(code && code.toLocaleLowerCase().indexOf('amount') ==  -1) {
            this.inButton.enabled=true;
            this.notinButton.enabled=true;
          }
          this.betweenButton.enabled=true;
          this.betweenButton.buttonMode=true;
          
          
          if (macroColumn != "")
              this.betweenButton.enabled=false;
          else
              this.betweenButton.enabled=true;

          if (Number(typeList) != 0 && typeList != undefined)
          {
            this.inListButton.enabled=true;
            this.containsButton.enabled = true;

          }
          else
          {
            this.inListButton.enabled = false;
            this.containsButton.enabled = false;

          }
        }
        else if (typeCode == 'DATE')
        {
          // if the data as date type
          
          this.equalButton.enabled=true;
          this.differentButton.enabled=true;
          this.higherButton.enabled=true;
          this.higherequalButton.enabled=true;
          this.lowerButton.enabled=true;
          this.lowerequalButton.enabled=true;
          this.likeButton.enabled = false;
          this.notLikeButton.enabled = false;
          this.inButton.enabled=true;
          this.notinButton.enabled=true;
          this.betweenButton.enabled=true;

          if (Number(typeList) != 0 &&  typeList != undefined)
          {
            this.inListButton.enabled=true;
            this.containsButton.enabled = true;

          }
          else
          {
            this.inListButton.enabled = false;
            this.containsButton.enabled = false;

          }
        } else if (typeCode == 'DTIME')
        {
          // if the data as date type

          this.equalButton.enabled=true;
          this.differentButton.enabled=true;
          this.higherButton.enabled=true;
          this.higherequalButton.enabled=true;
          this.lowerButton.enabled=true;
          this.lowerequalButton.enabled=true;
          this.likeButton.enabled = false;
          this.notLikeButton.enabled = false;
          this.inButton.enabled=true;
          this.notinButton.enabled=true;
          this.betweenButton.enabled=true;

          if (Number(typeList) != 0 &&  typeList != undefined)
          {
            this.inListButton.enabled=true;
            this.containsButton.enabled = true;

          }
          else
          {
            this.inListButton.enabled = false;
            this.containsButton.enabled = false;

          }
        }
        else if (typeCode == 'ENUM')
        {
          // if data as list
          
          let listType: string=this.jsonReader.getSelects()['select'].find(x => x.id == code).option[0].type;
          this.equalButton.enabled=true;
          this.differentButton.enabled=true;
          this.likeButton.enabled = false;
          this.notLikeButton.enabled = false;
          this.inButton.enabled=true;
          this.notinButton.enabled=true;

          if (Number(typeList) != 0 &&  typeList != undefined)
          {
            this.inListButton.enabled=true;
            this.containsButton.enabled = true;
          }
          else
          {
            this.containsButton.enabled = false;
            this.inListButton.enabled = false;
          }
          
          if (listType == 'NUM' || listType == "DECI") // if the type of list is numeric
          {
            if (macroColumn != "")
                this.betweenButton.enabled=false;
            else
                this.betweenButton.enabled=true;
            this.betweenButton.enabled = true;
            this.higherButton.enabled=true;
            this.higherequalButton.enabled=true;
            this.lowerButton.enabled=true;
            this.lowerequalButton.enabled=true;
          }
          else
          {
            
            this.betweenButton.enabled = false;
            this.higherButton.enabled = false;
            this.higherequalButton.enabled = false;
            this.lowerButton.enabled = false;
            this.lowerequalButton.enabled = false;
          }
        }

        

        if (hiddenOperator != '' && hiddenOperator != undefined)
        {
          
          let hiddenOperArr=hiddenOperator.split('|');
          
          for (let i:number=0; i < hiddenOperArr.length; i++)
          {
            
            if (hiddenOperArr[i] == 'in')
            {
              this.inButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'notin')
            {
              this.notinButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'equalButton')
            {
              this.equalButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'different')
            {
              this.differentButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'higher')
            {
              this.higherButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'higherequal')
            {
              this.higherequalButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'lower')
            {
              this.lowerButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'lowerequal')
            {
              this.lowerequalButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'like')
            {
              this.likeButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'notLike')
            {
              this.notLikeButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'between')
            {
              this.betweenButton.enabled = false;
            }
            if (hiddenOperArr[i] == 'inList')
            {
              this.inListButton.enabled = false;
            }
            if (hiddenOperArr[i] == "exists")
            {
              this.containsButton.enabled=false;
            }
          }
        }
        if (this.screenName == "change")
        {
          this.changeCritButton.enabled = true;
          this.labelQuery.text = this.getConditionById(this.listCriteria.selectedItem.label.conditionId - 1);

        }
      }else {
        this.changeCritButton.enabled = false;
        this.clearContraints();
      }
    } catch (error)
    {
      console.log("error in select criteria", error );

    }
  }




  /**
   * doOperation()
   *
   *
   **/
   doOperation():void {
   
    
    try {
      let code: string = this.listCriteria.selectedItem.label.code;
      let typeCode: string = this.listCriteria.selectedItem.label.typeCode;
      let typeEnum: string = this.listCriteria.selectedItem.label.typeEnum;
      let dataType: string = this.listCriteria.selectedItem.label.dataType;
      let profileField: string = this.listCriteria.selectedItem.label.profileField;
      let selectExist: number;
      let thresholdType: string="";

      this.addButton.enabled = true;
      this.addButton.buttonMode = true;
      this.changeButton.enabled = true;
      this.changeButton.buttonMode = true;
      this.operator.text=this.operationToDisplay;
      if (String(this.listCriteria.selectedItem.label.label).length * 8 > 250) {
        this.propertiesLabel.width = 250;
        this.propertiesLabel.toolTip = this.listCriteria.selectedItem.label.label;
      }
      if (this.vBoxContainer.contains(this.hBoxContainer2)) {
        
        /*this.hBoxContainer2.removeAllChildren();
        this.vBoxContainer.removeChild(this.hBoxContainer2);*/
      }
      this.hBoxContainer.removeAllChildren();

      
      if (typeCode == 'CHAR') {
        
        if (this.operation == this.equalButton.label || this.operation == this.differentButton.label) {
          
           selectExist = this.jsonReader.getSelects()['select'].find(x => x.id == code);
          if (selectExist != undefined) {
            if (typeEnum == '0') {
              
              this.comboBox= <SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
              this.comboBox.width='250';
              this.comboBox.styleName='dropDown';
              this.comboBox.dataLabel=code;
              this.comboBox.id = 'SwtComboBox';
              this.comboBox.setComboData(this.jsonReader.getSelects(), true);
              this.comboBox.change = () => {
                this.comboChangeHandler(event);
              };
              this.comboBox.enabled=true;
              this.comboBox.setFocus();
              this.comboChangeHandler(event);
            } else {
              this.textInput= <SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
              this.textInput.width='200';
              this.textInput.styleName= 'textbox';
              this.textInput.id='SwtTextInput';
              this.textInput.doubleClick = () => {
                this.openTextAreaInWindow(event);
              };
              if(code && code.toLocaleLowerCase().indexOf('time') > -1) {
                this.textInput.pattern='^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$';
                this.textInput.textAlign="center";
                this.textInput.width="70";
                this.textInput.onFocusOut = (event) => {
                  this.validateTime(this.textInput);
                };
              }
              this.textInput.enabled=true;
              this.textInput.setFocus();
            }
          }
          else {
            this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
            this.textInput.width='200';
            this.textInput.styleName='textbox';
            this.textInput.id='SwtTextInput';
            this.textInput.doubleClick = () => {
              this.openTextAreaInWindow(event);
            };
            if(code && code.toLocaleLowerCase().indexOf('time') > -1) {
              this.textInput.pattern='^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$';
              this.textInput.textAlign="center";
              this.textInput.width="70";
              this.textInput.onFocusOut = (event) => {
                this.validateTime(this.textInput);
              };
            }
            this.textInput.enabled=true;
            this.textInput.setFocus();
          }
        } else if (this.operation == 'in' || this.operation == 'not in') {
          
          this.textInput= <SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
          this.textInput.width='200';
          this.textInput.styleName='textbox';
          this.textInput.id='SwtTextInput';
          this.textInput.doubleClick = () => {
            this.openTextAreaInWindow(event);
          };

          this.textInput.enabled=true;
          this.textInput.setFocus();
          if(code && code.toLocaleLowerCase().indexOf('time') > -1 ) {
            let labelFormatDate:SwtLabel=<SwtLabel> this.hBoxContainer.addChild(SwtLabel);
            labelFormatDate.text= 'hh:mm, hh:mm, ...';
            labelFormatDate.fontWeight = 'normal';

          }

            selectExist = this.jsonReader.getSelects()['select'].find(x => x.id == code);
          if (selectExist != undefined) {
           
            this.listValuesButton = [];
            this.listValuesButton=<SwtButton> this.hBoxContainer.addChild(SwtButton);
            this.listValuesButton.styleName='flexButton';
            this.listValuesButton.label='...';
            this.listValuesButton.id='b1';
            this.listValuesButton.name='listValuesButton';
            //Event listener to listen for listValuesButton click
            this.listValuesButton.click = () => {
              this.addListValuesEventHandler(event);
            };
            this.listValuesButton.keyDown = () => {
              this.keyDownEventHandler(event);
            };
          }
        } else if (this.operation == 'in list' || this.operation == 'exists') {
          
          this.comboBox= <SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
          this.comboBox.width='250';
          this.comboBox.styleName='dropDown';
          this.comboBox.dataLabel=code + '_LIST';
          this.comboBox.id = 'SwtComboBox';
          this.comboBox.setComboData(this.jsonReader.getSelects(), true);
          this.comboBox.enabled=true;
          this.comboBox.setFocus();
          if (this.operation == "in list") {
            this.operation='in';
          }
        } else {
         
          this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
          this.textInput.width='200';
          this.textInput.styleName='textbox';
          this.textInput.id='SwtTextInput';
          this.textInput.doubleClick = () => {
            this.openTextAreaInWindow(event);
          };
          if(code && code.toLocaleLowerCase().indexOf('time') > -1 ) {
            this.textInput.pattern='^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$';
            this.textInput.textAlign="center";
            this.textInput.width="70";
            this.textInput.onFocusOut = (event) => {
              this.validateTime(this.textInput);
            };
          }
          this.textInput.enabled=true;
          this.textInput.setFocus();
          
        }
      } else if (typeCode == 'NUM'|| typeCode == "DECI") {
       
        if (this.operation == this.equalButton.label || this.operation == this.differentButton.label || this.operation == this.higherButton.label || this.operation == this.higherequalButton.label || this.operation == this.lowerButton.label || this.operation == this.lowerequalButton.label)
        {
       
          if (profileField == 'Y') // in the case of a profile: create the combobox of threshold "THRESHOLD_AMOUNT, THRESHOL COUNT"
          {
            if (dataType == 'A')
              thresholdType = "THRESHOLD_AMOUNT";
            else if (dataType == 'C')
              thresholdType = "THRESHOLD_COUNT";

            
            selectExist =this.jsonReader.getSelects()['select'].find(x => x.id == thresholdType);
          
            if (selectExist != undefined)
            {
              typeEnum="0";
              code=thresholdType;
            }
          }
          else {
            selectExist = this.jsonReader.getSelects()['select'].find(x => x.id == code);
          }
          if (selectExist != undefined ) {
         
            if (typeEnum == '0')
            {
              
              this.comboBox=<SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
              this.comboBox.width='280';
              this.comboBox.styleName='dropDown';
              this.comboBox.id = 'SwtComboBox';
              this.comboBox.dataLabel=code;
              this.comboBox.setComboData(this.jsonReader.getSelects(), true);
              this.comboBox.change = () => {
                this.comboChangeHandler(event);
              };
              this.comboBox.enabled=true;
              this.comboBox.setFocus();
              this.comboChangeHandler(event);
            }
            else
            {
              this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
              this.textInput.width='200';
              this.textInput.styleName='textbox';
              if(code && code.toLocaleLowerCase().indexOf('amount') > -1) {
                this.textInput.restrict='0-9-,.TBMtbm';
                this.textInput.onFocusOut = (event) => {
                  this.validateAmount();
                };
              }else {
                this.textInput.restrict='0-9.\\-';
              }
              this.textInput.enabled=true;
              this.textInput.setFocus();
            }
          }
          else // if this properties has not a slected list
          {

            this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
            this.textInput.width='200';
            this.textInput.styleName='textbox';
            this.textInput.enabled=true;
            
            if(code && code.toLocaleLowerCase().indexOf('amount') > -1) {
              this.textInput.restrict='0-9-,.TBMtbm';
              this.textInput.onFocusOut = (event) => {
                this.validateAmount();
              };
            }else {
              this.textInput.restrict='0-9.\\-';
            }
            this.textInput.setFocus();
          }
        }
        else if (this.operation == 'in' || this.operation == 'not in')
        {
          
          this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
          this.textInput.width='200';
          this.textInput.styleName='textbox';
          if(code && code.toLocaleLowerCase().indexOf('amount') > -1) {
            this.textInput.restrict='0-9-,.TBMtbm';
            this.textInput.onFocusOut = (event) => {
              this.validateAmount();
            };
          }else {
            this.textInput.restrict='0-9.\\-';
          }

          this.textInput.id='SwtTextInput';
          this.textInput.doubleClick = () => {
            this.openTextAreaInWindow(event);
          };
          this.textInput.enabled=true;
          this.textInput.setFocus();
          
           selectExist= this.jsonReader.getSelects()['select'].find(x => x.id == code);
          if (selectExist != undefined) // if property has a selected list ==> add the possibility to select from a list
          {
            
            this.listValuesButton=<SwtButton> this.hBoxContainer.addChild(SwtButton);
            this.listValuesButton.styleName='flexButton';
            this.listValuesButton.label='...';
            this.listValuesButton.id='b1';
            this.listValuesButton.name='listValuesButton';
            //Event listener to listen for listValuesButton click
            this.listValuesButton.click = () => {
              this.addListValuesEventHandler(event);
            };
            this.listValuesButton.keyDown = () => {
              this.keyDownEventHandler(event);
            };
          }
        }
        else if (this.operation == 'between')
        {
          if (profileField == 'Y') // in the case of a profile: create the combobox of threshold "THRESHOLD_AMOUNT, THRESHOL COUNT"
          {
           
            if (dataType == 'A')
              thresholdType="THRESHOLD_AMOUNT";
            else if (dataType == 'C')
              thresholdType="THRESHOLD_COUNT";
            selectExist = this.jsonReader.getSelects()['select'].find(x => x.id == thresholdType);
            if (selectExist != undefined )
            {
              typeEnum="0";
              code=thresholdType;
            }
          } else {
            selectExist = this.jsonReader.getSelects()['select'].find(x => x.id == code);
          }
          if (selectExist != undefined) // if property has a selected list ==> allow user to select from 2 dropdowns
          {
            this.comboBox=<SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
            this.comboBox.width='200';
            this.comboBox.styleName='dropDown';
            this.comboBox.dataLabel=code;
            this.comboBox.id = 'SwtComboBox';
            this.comboBox.setComboData(this.jsonReader.getSelects(), true);
            this.comboBox.enabled=true;
            this.comboBox.change = () => {
              this.comboBetweenChangeHandler(event);
            };
            this.comboBetweenChangeHandler(event);
            this.comboBox.setFocus();
            

            let label:SwtLabel=<SwtLabel> this.hBoxContainer.addChild(SwtLabel);
            label.text='  ' + this.and + '  ';
            label.styleName='label';
            label.width = "50";
           

            this.comboBox2=<SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
            this.comboBox2.width='200';
            this.comboBox2.styleName='dropDown';
            this.comboBox2.id = 'SwtComboBox';
            this.comboBox2.dataLabel=code;
            this.comboBox2.setComboData(this.jsonReader.getSelects(), true);
            this.comboBox2.enabled=true;
            this.comboBox2.change = () => {
              this.comboBetweenChangeHandler(event);
            };
            this.comboBetweenChangeHandler(event);
          
          }
          else //allow user to type 2 values manually
          {
           
            this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
            this.textInput.width='200';
            this.textInput.styleName='textbox';
            this.textInput.id='SwtTextInput';
            if(code && code.toLocaleLowerCase().indexOf('amount') > -1) {
              this.textInput.restrict='0-9-,.TBMtbm';
              this.textInput.onFocusOut = (event) => {
                this.validateAmount();
              };
            }else {
              this.textInput.restrict='0-9.\\-';
            }
            this.textInput.enabled=true;
            this.textInput.setFocus();
         

            let label4: SwtLabel =<SwtLabel> this.hBoxContainer.addChild(SwtLabel);
            label4.text='  ' + this.and + '  ';
            label4.styleName='label';
            label4.width= "50";
           

            this.textInput2=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
           this.textInput2.width='200';
            this.textInput2.styleName='textbox';
            this.textInput2.id='SwtTextInput';
            if(code && code.toLocaleLowerCase().indexOf('amount') > -1) {
              this.textInput2.restrict='0-9-,.TBMtbm';
              this.textInput2.onFocusOut = (event) => {
                this.validateAmount();
              };
            }else {
              this.textInput2.restrict='0-9.\\-';
            }
            this.textInput2.enabled=true;
          
          }
        }
        else if (this.operation == 'in list' || this.operation == "exists")
        {
        
          this.comboBox=<SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
          this.comboBox.width='250';
          this.comboBox.styleName='dropDown';
          this.comboBox.id = 'SwtComboBox';
          this.comboBox.dataLabel=code + '_LIST';
          this.comboBox.setComboData(this.jsonReader.getSelects(), true);
          this.comboBox.enabled=true;
          this.comboBox.setFocus();
          this.operation='in';
        }
        else
        {
          
          this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
          this.textInput.width='200';
          this.textInput.styleName='textbox';
          if(code && code.toLocaleLowerCase().indexOf('amount') > -1) {
            this.textInput.restrict='0-9-,.TBMtbm';
            this.textInput.onFocusOut = (event) => {
              this.validateAmount();
            };
          }else {
            this.textInput.restrict='0-9.\\-';
          }
          this.textInput.enabled=true;
          this.textInput.setFocus();
        }
      }else if (typeCode == 'DTIME')
      {

        if (this.operation == 'in' || this.operation == 'not in')
        {

          this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
          this.textInput.width='250';
          this.textInput.styleName='textbox';
          this.textInput.id='SwtTextInput';
          this.textInput.doubleClick = () => {
            this.openTextAreaInWindow(event);
          };
          this.textInput.onFocusOut = (event) => {
          };
          this.textInput.enabled=true;
          this.textInput.setFocus();

          let labelFormatDate:SwtLabel=<SwtLabel> this.hBoxContainer.addChild(SwtLabel);
          labelFormatDate.text=' ' + this.sysdateformat + ' hh:mm, ' + this.sysdateformat + ' hh:mm, ...';
          labelFormatDate.fontWeight = 'normal';
        }

        else
        {
          this.dateField=<SwtDateField> this.hBoxContainer.addChild(SwtDateField);
          //comment this line because sysdateformat is null
          this.dateField.formatString=this.sysdateformat;
          this.dateField.id='frmDateChooser';
          this.dateField.width='80';
          // this.dateField.dayNames= this.nameDays;
          // this.dateField.monthNames= this.nameMonths;
          this.dateField.restrict='0-9\\-';
          this.dateField.enabled=true;
          this.dateField.showToday = false;
          this.dateField.selectableRange={rangeEnd: new Date()};
          this.dateField.open = () => {
            this.datePickerOn(event);
          };
          this.dateField.close = () => {
            this.datePickerOff(event);
          };

          this.dateField.setFocus();
          this.spacer = <Spacer>this.hBoxContainer.addChild(Spacer);
          this.spacer.width = "5";
          this.timeField = <SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
          this.timeField.pattern='^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$';
          this.timeField.textAlign="center";
          this.timeField.width="70";
          this.timeField.onFocusOut = (event) => {
            this.validateTime(this.timeField);
          };

          if (this.operation == 'between')
          {
            let d2:Date=new Date();

            let label2:SwtLabel=<SwtLabel> this.hBoxContainer.addChild(SwtLabel);
            label2.text='  ' + this.and + '  ';
            label2.styleName='label';

            this.dateField2=<SwtDateField> this.hBoxContainer.addChild(SwtDateField);
            this.dateField2.id='toDateChooser';
            this.dateField2.width = '80';
            this.dateField2.restrict='0-9\\-';
            this.dateField2.enabled=true;
            this.dateField2.showToday=false;
            this.dateField2.formatString=this.sysdateformat;

            this.dateField2.open = () => {
              this.datePickerOn(event);
            };
            this.dateField2.close = () => {
              this.datePickerOff(event);
            };

            // set start date (date selected in from date) and end date (today)
            this.dateField2.selectableRange={rangeStart: d2, rangeEnd: new Date()};
            this.spacer = <Spacer>this.hBoxContainer.addChild(Spacer);
            this.spacer.width = "5";
            this.timeField2 = <SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
            this.timeField2.pattern='^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$';
            this.timeField2.textAlign="center";
            this.timeField2.width="70";
            this.timeField2.onFocusOut = (event) => {
              this.validateTime(this.timeField2);
            };

          }
          /*this.spacer = <Spacer>this.hBoxContainer.addChild(Spacer);
          this.spacer.width = "5";
          //this.sysDateButton.styleName='flexButton';
          this.sysDateButton.label= 'Sys Date';
          this.sysDateButton.id='b11';
          this.sysDateButton.name='sysDateButton';
          //Event listener to listen for sysDateButton click
          this.sysDateButton.click = () => {
            this.addSysDateEventHandler(event);
          };
          this.sysDateButton.keyDown = () => {
            this.keyDownEventHandler(event);
          };*/
        }
      }
      else if (typeCode == 'DATE')
      {
        
        if (this.operation == 'in' || this.operation == 'not in')
        {
         
          this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
          this.textInput.width='250';
          this.textInput.styleName='textbox';
          this.textInput.id='SwtTextInput';
          this.textInput.doubleClick = () => {
            this.openTextAreaInWindow(event);
          };
          this.textInput.enabled=true;
          this.textInput.setFocus();
        
          let selectListExist4: string=this.jsonReader.getSelects()['select'].find(x => x.id == code);
          if (selectListExist4 != undefined) // if property has a selected list ==> add the possibility to select from a list
          {
            
            this.listValuesButton=<SwtButton> this.hBoxContainer.addChild(SwtButton);
            this.listValuesButton.styleName='flexButton';
            this.listValuesButton.label='...';
            this.listValuesButton.id='b1';
            this.listValuesButton.name='listValuesButton';
           
            this.listValuesButton.click = () => {
              this.addListValuesEventHandler(event);
            };
            this.listValuesButton.keyDown = () => {
              this.keyDownEventHandler(event);
            };
          }

         
          let labelFormatDate:SwtLabel=<SwtLabel> this.hBoxContainer.addChild(SwtLabel);
          labelFormatDate.text=' ' + this.sysdateformat + ', ' + this.sysdateformat + ', ...';
          labelFormatDate.fontWeight = 'normal';
        }
        else if (this.operation == 'in list' || this.operation == "exists")
        {
          
          this.comboBox=<SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
          this.comboBox.width='250';
          this.comboBox.styleName='dropDown';
          this.comboBox.dataLabel=code + '_LIST';
          this.comboBox.id = 'SwtComboBox';
          this.comboBox.setComboData(this.jsonReader.getSelects(), true);
          this.comboBox.enabled=true;
          this.comboBox.setFocus();
          this.operation='in';
        }
        else
        {
          this.dateField=<SwtDateField> this.hBoxContainer.addChild(SwtDateField);
          //comment this line because sysdateformat is null
          this.dateField.formatString=this.sysdateformat;
          this.dateField.id='frmDateChooser';
          this.dateField.width='80';
          // this.dateField.dayNames= this.nameDays;
          // this.dateField.monthNames= this.nameMonths;
          this.dateField.restrict='0-9\\-';
          this.dateField.enabled=true;
          this.dateField.showToday = false;
          this.dateField.selectableRange={rangeEnd: new Date()};
          this.dateField.open = () => {
            this.datePickerOn(event);
          };
          this.dateField.close = () => {
            this.datePickerOff(event);
          };

          this.dateField.setFocus();

          


          if (this.operation == 'between')
          {
            let d2:Date=new Date();
           
            let label2:SwtLabel=<SwtLabel> this.hBoxContainer.addChild(SwtLabel);
            label2.text='  ' + this.and + '  ';
            label2.styleName='label';
           

            this.dateField2=<SwtDateField> this.hBoxContainer.addChild(SwtDateField);
            this.dateField2.id='toDateChooser';
            this.dateField2.width = '80';
            // this.dateField2.dayNames=this.nameDays;
            // this.dateField2.monthNames=this.nameMonths;
            this.dateField2.restrict='0-9\\-';
            this.dateField2.enabled=true;
            this.dateField2.showToday=false;
            this.dateField2.formatString=this.sysdateformat;
           
            this.dateField2.open = () => {
              this.datePickerOn(event);
            };
            this.dateField2.close = () => {
              this.datePickerOff(event);
            };

            // set start date (date selected in from date) and end date (today)
            this.dateField2.selectableRange={rangeStart: d2, rangeEnd: new Date()};
           
          }
          this.spacer = <Spacer>this.hBoxContainer.addChild(Spacer);
          this.spacer.width = "5";
          this.sysDateButton=<SwtButton> this.hBoxContainer.addChild(SwtButton);
          //this.sysDateButton.styleName='flexButton';
          this.sysDateButton.label= 'Sys Date';
          this.sysDateButton.id='b11';
          this.sysDateButton.name='sysDateButton';
          //Event listener to listen for sysDateButton click
          this.sysDateButton.click = () => {
            this.addSysDateEventHandler(event);
          };
          this.sysDateButton.keyDown = () => {
            this.keyDownEventHandler(event);
          };
        }
      }
      else if (typeCode == 'ENUM')
      {
       
        //let listType: string=this.jsonReader.getSelects().children().(@id == code).children()[0].@type;
        let listType: string=this.jsonReader.getSelects()['select'].find(x => x.id == code).option[0].type;

        if (this.operation == 'in' || this.operation == 'not in')
        {
          if (listType == 'CHAR')
          {
            this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
            this.textInput.width='200';
            //this.textInput.styleName='textbox';
            this.textInput.enabled=true;
            this.textInput.id='SwtTextInput';
            this.textInput.doubleClick = () => {
              this.openTextAreaInWindow(event);
            };

            this.textInput.setFocus();
           
            this.listValuesButton=<SwtButton> this.hBoxContainer.addChild(SwtButton);
            //this.listValuesButton.styleName='flexButton';
            this.listValuesButton.label='...';
            this.listValuesButton.id='b1';
            this.listValuesButton.width='70';
            this.listValuesButton.name='listValuesButton';
            //Event listener to listen for listValuesButton click
            this.listValuesButton.click = () => {
              this.addListValuesEventHandler(event);
            };

            this.listValuesButton.keyDown = () => {
              this.keyDownEventHandler(event);
            };
          }
          else if (listType == 'NUM' || listType == "DECI")
          {
          
            this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
            this.textInput.width='200';
            this.textInput.styleName='textbox';
            if(code && code.toLocaleLowerCase().indexOf('amount') > -1) {
              this.textInput.restrict='0-9-,.TBMtbm';
              this.textInput.onFocusOut = (event) => {
                this.validateAmount();
              };
            }else {
              this.textInput.restrict='0-9.\\-';
            }
            this.textInput.enabled=true;
            this.textInput.id='SwtTextInput';
            this.textInput.doubleClick = () => {
              this.openTextAreaInWindow(event);
            };
            this.textInput.setFocus();
          
            this.listValuesButton=<SwtButton> this.hBoxContainer.addChild(SwtButton);
            this.listValuesButton.styleName='flexButton';
            this.listValuesButton.label='...';
            this.listValuesButton.id='b1';
            this.listValuesButton.name='listValuesButton';
            //Event listener to listen for listValuesButton click
            this.listValuesButton.click = () => {
              this.addListValuesEventHandler(event);
            };
            this.listValuesButton.keyDown = () => {
              this.keyDownEventHandler(event);
            };
          }
          else if (listType == "BIND_C" || listType == "BIND_N")
          {
            this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
            this.textInput.width='200';
            this.textInput.styleName='textbox';
            this.textInput.enabled=false;
            this.textInput.id='SwtTextInput';
            this.textInput.doubleClick = () => {
              this.openTextAreaInWindow(event);
            };
            this.textInput.setFocus();
         
            this.listValuesButton=<SwtButton> this.hBoxContainer.addChild(SwtButton);
            this.listValuesButton.styleName='flexButton';
            this.listValuesButton.label='...';
            this.listValuesButton.id='b1';
            this.listValuesButton.name='listValuesButton';
            //Event listener to listen for listValuesButton click
            this.listValuesButton.click = () => {
              this.addListValuesEventHandler(event);
            };
            this.listValuesButton.keyDown = () => {
              this.keyDownEventHandler(event);
            };
          }
        }
        else if (this.operation == 'between')
        {
         
          this.comboBox=<SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
          this.comboBox.width='200';
          this.comboBox.styleName='dropDown';
          this.comboBox.id = 'SwtComboBox';
          this.comboBox.dataLabel=code;
          this.comboBox.setComboData(this.jsonReader.getSelects(), true);
          this.comboBox.change = () => {
            this.comboBetweenChangeHandler(event);
          };
          this.comboBox.enabled=true;
          this.comboBox.setFocus();
          this.comboChangeHandler(event);
         
          let label3:SwtLabel=<SwtLabel> this.hBoxContainer.addChild(SwtLabel);
          label3.text='  ' + this.and + '  ';
          label3.styleName='label';
         

          this.comboBox2=<SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
          this.comboBox2.width='200';
          this.comboBox2.styleName='dropDown';
          this.comboBox2.id = 'SwtComboBox';
          this.comboBox2.dataLabel=code;
          this.comboBox2.setComboData(this.jsonReader.getSelects(), true);
          this.comboBox2.change = () => {
            this.comboBetweenChangeHandler(event);
          };
          this.comboBox2.enabled=true;
          this.comboChangeHandler(event);
          
        }
        else if (this.operation == 'in list' ||  this.operation == "exists")
        {
      
          this.comboBox=<SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
          this.comboBox.width='250';
          this.comboBox.styleName='dropDown';
          this.comboBox.id = 'SwtComboBox';
          this.comboBox.dataLabel=code + '_LIST';
          this.comboBox.setComboData(this.jsonReader.getSelects(), true);
          this.comboBox.enabled=true;
          this.comboBox.setFocus();
          this.operation='in';
        }
        else
        {
         
            this.comboBox = <SwtComboBox> this.hBoxContainer.addChild(SwtComboBox);
            this.comboBox.width = '250';
            //this.comboBox.styleName='dropDown';
            this.comboBox.dataLabel=code;
            this.comboBox.id = 'SwtComboBox';
            this.comboBox.setComboData(this.jsonReader.getSelects(), true);
            this.comboBox.change = () => {
              this.comboChangeHandler(event);
            };
            this.comboBox.enabled = true;
            this.comboBox.setFocus();
            this.comboChangeHandler(event);
        }
      }

    } catch (error) {
      console.log('error in do operation', error);
      this.logger.error('method [doOperation] - error : ', error , '- errorLocation :', this.errorLocation);

      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this), 'doOperation', this.errorLocation);
    }
    this.logger.info('method [doOperation] - END ');
  }

  validateLikeOp(columnToDisplay, textInput) : any {
     if(textInput.indexOf('%') == -1) {
       this.swtAlert.question(columnToDisplay + " "+ SwtUtil.getPredictMessage('alert.likeOperator', null), null,Alert.YES | Alert.NO, null, this.checkTextInput.bind(this) );
       return false;
     }
     return true;
  }
  checkTextInput(event) : void {
     if(event.detail === Alert.YES) {

     } else {
      this.createQuery()
     }
  }
  chekcFields() :void {
    let columnToDisplay: string = this.listCriteria.selectedItem.label.label;
    if(this.operation == "like" || this.operation == "Not Like" ) {
      if(this.textInput.text == "") {
        this.swtAlert.warning(SwtUtil.getPredictMessage('queryBuilderScreen.alert.missingProperty', null) + ' ' + columnToDisplay, "Warning");
      } else if(this.textInput.text.indexOf('%') == -1) {
        this.validateLikeOp(columnToDisplay, this.textInput.text);
      }
       else {
        this.createQuery();
      }
    } else {
      this.createQuery()
    }
  }
  /**
   * createQuery()
   *
   * Function used to create query for current search
   **/
  public createQuery():void {
    try
    {

      this.logger.info('method [createQuery] - START ');
      let columnToStore: string = this.listCriteria.selectedItem.label.code;
      let columnToDisplay: string = this.listCriteria.selectedItem.label.label;
      let typeCode: string = this.listCriteria.selectedItem.label.typeCode;
      let tableName: string = this.listCriteria.selectedItem.label.tableName;
      let localValue: string = this.listCriteria.selectedItem.label.localValue;
      let profileField: string = this.listCriteria.selectedItem.label.profileField;
      let fieldExpression: string = this.listCriteria.selectedItem.label.expression;
      let fieldName: string = this.listCriteria.selectedItem.label.fieldName;
      let aliasTable: string = this.listCriteria.selectedItem.label.aliasTable;
      let columnValueUpper: string = this.listCriteria.selectedItem.label.columnValueUpper;
      let conditionId: number = this.listCriteria.selectedItem.label.conditionId;
      let isIndexFunction: boolean = this.listCriteria.selectedItem.label.isIndexFunction == 'true';
      let macroColumn: string = this.listCriteria.selectedItem.label.macroColumn === undefined ?'' : this.listCriteria.selectedItem.label.macroColumn;
      
      let columnValue: string = '';
      let columnValue2: string = '';
      let tempClauseToExecute: string = '';
      let tempClauseToDisplay: string = '';
      
      let tempArray = this.hBoxContainer.getChildren();
      let currentComponent: string = tempArray[0].childType;
      let currentComponents = [];
      for (let i=0; i<tempArray.length; i++) {
        currentComponents.push(tempArray[i].childType);
      }
      let expression:RegExp=/[0-9]+/ig;
      let expComma:RegExp=/[,]+/ig;
      let columnCodeValue: string = '';
      let columnCodeValue2: string = '';
      let expAppostrophe: RegExp = /[']/ig; //delete comma and dot that appears
      let expOper:RegExp=/ :op/ig;
      let expParam:RegExp=/ :param/ig;
      let alias: string = null;
      let value: string = null;
      let valueToDisplay: string = null;
      let find:boolean = false;
      let enumProfile: string = "";
      let selectedIndex: number = this.listCriteria.selectedIndex;
      currentComponent = currentComponent.replace(expression, '');


      
      
      /*if (String(this.listCriteria.selectedItem.label.label).length*8 > 250)
      {
        this.propertiesLabel.width=250;
        this.propertiesLabel.toolTip = this.listCriteria.selectedItem.label.label;
      }*/

      
      // in the case of a combobox and when the value is TYPETEXT, the current component is TextInput and not SwtComboBox
      if (currentComponent == "SwtComboBox" && this.comboBox.selectedItem.id == "TYPETEXT")
          currentComponent="SwtTextInput";
      
      if (this.screenName === 'change') {
        this.andOrString = this.listCriteria.selectedItem.label.nextCondition;
      }
      if (profileField === 'Y') {
        for (let pro = 0; pro < this.tableToJoin.length; pro++)
        {
         
          if (String(this.tableToJoin[pro]).indexOf(tableName) >= 0) {
            find = true;
            break;
          }
        }
        
        if (!find) {
          
          // add table to join to the array tableToJoin
          this.tableToJoin[this.index] = this.tabName;
          // increment index
          this.index = this.index + 1;
        }
      } else {
        if (tableName != null && tableName !== '') {
          
          for (let co = 0; co < this.tableToJoin.length; co++) {
            
            if (String(this.tableToJoin[co]).indexOf(tableName + ' ') >= 0) {
              find = true;
              break;
            }
          }
          if (!find) {
            
            if (columnToStore.indexOf('.') >= 0) {
              
              alias = columnToStore.substring(0, columnToStore.indexOf('.'));
              
              this.tableToJoin[this.index] = tableName + ' ' + alias;
              // increment index
              this.index = this.index + 1;
            } else {
              
              //Send message if any error occurs
              //SwtUtil.logError(new Error('Missing alias in the column s_cfg_type_values.code'), SwtUtil.SYSTEM_MODULE_ID, getQualifiedClassName(this), 'createQuery', this.errorLocation);
            }
          }
        } else {
        //  SwtUtil.logError(new Error("Missing alias in the column s_cfg_type_values.code"), this.moduleId, getQualifiedClassName(this) + ".mxml", "createQuery", this.errorLocation);
        }
      }


      if (currentComponent === 'SwtTextInput')
      {
        let emptyDate:boolean = false;
        let selItems = null;

       
        if (columnValueUpper === 'YY' || columnValueUpper === 'NY')
          columnValue = this.textInput.text.toUpperCase();
        else
          columnValue = this.textInput.text;

        
          

        //To avoid problem with apostrophe
        columnValue=columnValue.replace(expAppostrophe, "''");


        if (typeCode === 'DATE' && columnValue != '') // in the case of in, not in operator
        {
         
          columnValue = this.textInput.text;
          selItems=columnValue.split(',');

          selItems[0] = selItems[0].replace(/(^\s)|(\s$)/ig, '');
          if (selItems[0] == '')
            emptyDate=true;
          else {
            columnCodeValue=" TO_DATE('" + selItems[0] + "', '" + this.sysdateformat + "')";
          }

          if (selItems.length > 1)
          {
            for (let k = 1; k < selItems.length; k++)
            {
              selItems[k] = selItems[k].replace(/(^\s)|(\s$)/ig, '');
             
              if (selItems[k] === '')
                emptyDate=true;
              else {
                columnCodeValue=columnCodeValue + "," + " TO_DATE('" + selItems[k] + "', '" + this.sysdateformat + "')";

              }
            }
          }
        }
        if (typeCode === 'DTIME' && columnValue != '')
        {

          columnValue = this.textInput.text;
          selItems=columnValue.split(',');



          selItems[0] = selItems[0].replace(/(^\s)|(\s$)/ig, '');
          if (selItems[0] == '')
            emptyDate=true;
          else {
            columnCodeValue=" TO_DATE('" + selItems[0] + "', '" + this.sysdateformat  + " HH24:MI" + "')";
          }

          if (selItems.length > 1)
          {
            for (let k = 1; k < selItems.length; k++)
            {
              selItems[k] = selItems[k].replace(/(^\s)|(\s$)/ig, '');

              if (selItems[k] === '')
                emptyDate=true;
              else {
                columnCodeValue=columnCodeValue + "," + " TO_DATE('" + selItems[k] + "', '" + this.sysdateformat  + " HH24:MI" + "')";

              }
            }
          }
        }

        if (this.hBoxContainer.contains(this.listValuesButton) && columnValue !== '' && typeCode !== 'DATE' && typeCode !== 'DTIME')
        {
          columnValue = this.textInput.text;

          if (columnValueUpper == 'YY' || columnValueUpper == 'NY')
            selItems = columnValue.toUpperCase().split(',');
          else
            selItems = columnValue.split(',');

          let map:HashMap=new HashMap();
          //let gridJSONList=this.jsonReader.getSelects().children().(@id == columnToStore);
          let gridJSONList = this.jsonReader.getSelects()['select'].find(x => x.id == columnToStore).option;

          
          for (let j = 0; j < gridJSONList.length; j++)
          {
            
            let mapKey: string = gridJSONList[j].content;
            let mapValue: string = gridJSONList[j].id;
            if (columnValueUpper == 'YY' || columnValueUpper == 'NY')
              map.put(mapKey.toUpperCase(), mapValue.toUpperCase());
            else
              map.put(mapKey, mapValue);
          }
         

          for (let i = 0; i < selItems.length; i++)
          {
            selItems[i] = selItems[i].replace(/(^\s)|(\s$)/ig, '');
          
            if (map.containsKey(selItems[i])) {
              if (columnCodeValue == '')
                columnCodeValue=map.getValue(selItems[i]);
              else
                columnCodeValue=columnCodeValue + ',' + map.getValue(selItems[i]);
            } else {
              if (columnCodeValue == "")
                columnCodeValue=selItems[i];
              else
                columnCodeValue=columnCodeValue + "," + selItems[i];

            }
          }
        
        }
        else if (!this.hBoxContainer.contains(this.listValuesButton) && columnValue != '' && typeCode != 'DATE' && typeCode !== 'DTIME')
        {
          if (columnValueUpper == 'YY' || columnValueUpper == 'NY')
            columnCodeValue=this.textInput.text.toUpperCase();
          else
            columnCodeValue=this.textInput.text;
        }
        columnCodeValue = String(columnCodeValue);
       
        if (typeCode == 'NUM' || typeCode == "DECI") {
          
          if (this.operation == 'in' || this.operation == 'not in')
          {
           
            columnValue = this.verifyTextInput(columnValue, typeCode);
            columnCodeValue = this.verifyTextInput(columnCodeValue, typeCode);
            if (columnValue != 'false' && columnCodeValue != 'false')
            {
                if (macroColumn == "")
                {
                    tempClauseToExecute=columnToStore + " " + this.operation + " (" + columnCodeValue + ")";
                }
                else
                {
                    macroColumn=macroColumn.replace(expOper, " " + this.operation);
                    macroColumn=macroColumn.replace(expParam, " (" + columnCodeValue + ")");
                    tempClauseToExecute=macroColumn;                            
                }
                
              tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' (' + columnValue + ') ]';
              this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue:  " (" + columnCodeValue + ")", typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
            }
          }
          else if (this.operation == 'between') {
           
            columnValue2=this.textInput2.text;

            if (columnValue2 == '') {
             
              columnValue = '';
            }
            else
            {
              columnValue=this.verifyTextInput(columnValue, typeCode);
              columnValue2=this.verifyTextInput(columnValue2, typeCode);
              if (columnValue != 'false' && columnValue2 != 'false') {
               
                tempClauseToExecute=columnToStore + '  ' + this.operation + ' To_number(' + this.getNumberFromAmount(columnValue) + ')' + ' and ' + ' To_number(' + this.getNumberFromAmount(columnValue2) + ')';
                tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + columnValue + ' and ' + columnValue2 + ' ]';
                this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: " To_number(" + this.getNumberFromAmount(columnValue) + ")" + " and " + " To_number(" + this.getNumberFromAmount(columnValue2) + ")", typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
              }
            }
          }
          else
          {
            columnValue=this.verifyTextInput(columnValue, typeCode);

            if (columnValue != 'false') {
            
              if (columnValue == '')
              {
                if (this.operation == '=')
                {
                  value=' = To_number(0)';
                  valueToDisplay =  " = 0 "; // SwtUtil.getCommonMessages('searchScreen.label.isEmpty');
                  this.operation = '';
                  this.operationToDisplay = '';
                  columnValue='is null';
                }
                else if (this.operation == '<>')
                {
                  value=' = To_number(0)';
                  valueToDisplay= " = 0 "; // SwtUtil.getCommonMessages('searchScreen.label.isNotEmpty');
                  this.operation = '';
                  this.operationToDisplay = '';
                  columnValue=' is not null';
                }
              }
              else
              {
                value=' To_number(' + this.getNumberFromAmount(columnValue) + ')';
                valueToDisplay=columnValue;
              }
              
              if (macroColumn == "")
              {
                  tempClauseToExecute=columnToStore + " " + this.operation + value;
              }   
              else
              {
                  macroColumn=macroColumn.replace(expOper, " " + this.operation);
                  macroColumn=macroColumn.replace(expParam,  value);
                  tempClauseToExecute=macroColumn;
              }                                   
              
              tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + valueToDisplay + ' ]';
              this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: value, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
            }
          }
        }
        else if (typeCode == 'CHAR')
        {
          if (this.operation == "in" || this.operation == "not in") {
            if(columnToDisplay && columnToDisplay.toLocaleLowerCase().indexOf('time') > -1) {
              if(this.validateTimes(this.textInput.text) == "error_time")  {
                columnValue = "error_time";
              }
            }
            columnCodeValue=columnCodeValue.replace(/(^,)|(,$)/ig, "");
            columnValue=columnValue.replace(/(^,)|(,$)/ig, "");
			      // if (macroColumn != "")
            	columnCodeValue = columnCodeValue.replace(/[']/g, "''");
            
            columnCodeValue=columnCodeValue.replace(expComma, "','");
            columnValue=columnValue.replace(expComma, ",");

            
           
            if (columnValueUpper == "YY")
            {
              columnToStore="upper(" + columnToStore + ") ";
              columnCodeValue=" ('" + columnCodeValue.toUpperCase() + "')";
            }
            else if (columnValueUpper == "NY")
            {
              columnCodeValue=" ('" + columnCodeValue.toUpperCase() + "')";
            }
            else if (columnValueUpper == "YN")
            {
              columnToStore="upper(" + columnToStore + ") ";
              columnCodeValue=" ('" + columnCodeValue + "')";   
            }
            else
              columnCodeValue=" ('" + columnCodeValue + "')";
            
            
              
            if (macroColumn == "")
            {
                tempClauseToExecute=columnToStore + " " + this.operation + columnCodeValue;
            }   
            else
            {
                macroColumn=macroColumn.replace(expOper, " " + this.operation);
                macroColumn=macroColumn.replace(expParam," " + columnCodeValue);
                tempClauseToExecute=macroColumn;
            }                               

            tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " (" + columnValue + ") ]";
            this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnCodeValue, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
          }
          else {

          
            if (columnValue == "")
            {
              if (this.operation == "=")
              {
                value="IS NULL";
                valueToDisplay= " is null"; //SwtUtil.getCommonMessages('searchScreen.label.isEmpty');
                this.operation="";
                this.operationToDisplay="";
                columnValue=" is null";
              }
              else if (this.operation == "<>")
              {
                value="IS NOT NULL";
                valueToDisplay= " is not null"; //SwtUtil.getCommonMessages('searchScreen.label.isNotEmpty');
                this.operation="";
                this.operationToDisplay="";
                columnValue=" is not null";
              }
            }
            else
            {

              if (macroColumn != "")
                columnValue=columnValue.replace(/[']/g, "''");
              
              if (columnValueUpper == "YY" || columnValueUpper == "NY")
                value=" '" + columnValue.toUpperCase() + "'";
              else
                value=" '" + columnValue + "'";

              valueToDisplay=columnValue;
            }
            
            if (columnValueUpper == "YY")
            {
              columnToStore="upper(" + columnToStore + ") ";
            }
            


            if (macroColumn == "")
            {
                tempClauseToExecute=columnToStore + " " + this.operation + " " + value;
            }   
            else
            {
                macroColumn=macroColumn.replace(expOper, " " + this.operation);
                macroColumn=macroColumn.replace(expParam,  value);
                tempClauseToExecute=macroColumn;
            }

            tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " " + valueToDisplay + " ]";
            this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: value, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
          }
        }
        else if (typeCode == 'ENUM') {
         
          let code: string = this.listCriteria.selectedItem.label.code;
          //let listType: string=this.jsonReader.getSelects().children().(@id == code).children()[0].@type;
          let listType: string = this.jsonReader.getSelects()['select'].find(x => x.id == code).option[0].type;
          if (this.operation == 'in' || this.operation == 'not in')
          {
          
            if (listType == 'NUM' || listType == "DECI")
            {
           
              columnValue=this.verifyTextInput(columnValue, listType);
            
              if (columnValue != 'false')
              {
                if (macroColumn == "")
                {
                    tempClauseToExecute=columnToStore + " " + this.operation + " (" + columnValue + ")";
                }   
                else
                {
                     macroColumn=macroColumn.replace(expOper, " " + this.operation);
                     macroColumn=macroColumn.replace(expParam, " (" + columnValue + ")");
                     tempClauseToExecute=macroColumn;
                }   
                tempClauseToDisplay="[ " + columnToStore + " " + this.operationToDisplay + " (" + columnValue + ") ]";
                this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: " (" + columnValue + ")", typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});

              }
            } else if (listType == 'CHAR') {
              
              columnCodeValue = columnCodeValue.replace(/(^,)|(,$)/ig, "");
              columnValue=columnValue.replace(/(^,)|(,$)/ig, "");
              columnCodeValue=columnCodeValue.replace(expComma, "','");
              columnValue=columnValue.replace(expComma, ",");
              
              if (macroColumn != "")
                  columnCodeValue=columnCodeValue.replace(/[']/g, "''");
          
              if (columnValueUpper == "YY")
              {
                columnToStore="upper(" + columnToStore + ") ";
                columnCodeValue="('" + columnCodeValue.toUpperCase() + "')";
              }
              else if (columnValueUpper == "NY")
              {
                columnCodeValue="('" + columnCodeValue.toUpperCase() + "')";
              }
              else if (columnValueUpper == "YN")
              {
                  columnToStore="upper(" + columnToStore + ")";
                  columnCodeValue="('" + columnCodeValue + "')";
              }
              else
              {
                  columnCodeValue="('" + columnCodeValue + "')";
              }
              
              if (macroColumn == "")
              {
                  tempClauseToExecute=columnToStore + " " + this.operation + " " + columnCodeValue;
              }   
              else
              {
                  macroColumn=macroColumn.replace(expOper, " " + this.operation);
                  macroColumn=macroColumn.replace(expParam, " " + columnCodeValue);
                  tempClauseToExecute=macroColumn;
              }
              
              this.tabAllConditions.push({columnToStore: columnToStore, operation:  this.operation, columnCodeValue: columnCodeValue, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
              tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " (" + columnValue + ") ]";

            }
            if (listType == "BIND_N")
            {
            
              let varToBindArray = [];
              varToBindArray = columnValue.split(",");
              for (let n=0; n < varToBindArray.length; n++)
                this.varToBind.push(varToBindArray[n]);

            
              tempClauseToExecute=columnToStore + " " + this.operation + " (" + columnValue + ")";
              tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " (" + columnValue + ") ]";
              this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: " (" + columnValue + ")", typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
            }
            else if (listType == "BIND_C") {

              let varToBindArray=columnCodeValue.split(",");
              for (let m=0; m < varToBindArray.length; m++)
                this.varToBind.push(varToBindArray[m]);

             
              if (columnValueUpper == "YY")
              {
                columnToStore="upper(" + columnToStore + ") ";
                columnCodeValue=" (upper(" + columnCodeValue + "))";
                columnCodeValue=columnCodeValue.replace(expComma, "),upper(");
                tempClauseToExecute=columnToStore + this.operation + columnCodeValue;
              }
              else if (columnValueUpper == "NY")
              {
                columnCodeValue=" (upper(" + columnCodeValue + "))";
                columnCodeValue=columnCodeValue.replace(expComma, "),upper(");
                tempClauseToExecute=columnToStore + " " + this.operation + columnCodeValue;
              }
              else if (columnValueUpper == "YN")
              {
                columnCodeValue=" upper(" + columnCodeValue + ") ";
                tempClauseToExecute=columnToStore + " " + this.operation + columnCodeValue;
              }
              else
              {
                columnToStore="upper(" + columnToStore + ") ";
                tempClauseToExecute=columnToStore + " " + this.operation + columnCodeValue;
              }

              this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnCodeValue, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
              tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " (" + columnValue + ") ]";
            }
          }
          else if (this.operation == 'between') {
            
            columnValue2= this.textInput2.text;

            if (columnValue2 == "")
            {
             
              columnValue="";
            }
            else
            {
              if (listType == "BIND_N")
              {
                tempClauseToExecute=columnToStore + "  " + this.operation + " " + columnValue + " and " + columnValue2;
                tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " " + columnValue + " and " + columnValue2 + " ]";
                this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: " " + columnValue + " and " + " " + columnValue2, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});

                this.varToBind.push(columnValue);
                this.varToBind.push(columnValue2);
              }
              else
              {
                columnValue= this.verifyTextInput(columnValue, typeCode);
                columnValue2= this.verifyTextInput(columnValue2, typeCode);
                if (columnValue != "false" && columnValue2 != "false")
                {
                 
                  tempClauseToExecute=columnToStore + "  " + this.operation + " To_number(" + this.getNumberFromAmount(columnValue) + ")" + " and " + " To_number(" + this.getNumberFromAmount(columnValue2) + ")";
                  tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " " + columnValue + " and " + columnValue2 + " ]";
                  this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: " To_number(" + this.getNumberFromAmount(columnValue) + ")" + " and " + " To_number(" + this.getNumberFromAmount(columnValue2) + ")", typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
                }
              }
            }
          }
        } else if (typeCode == 'DATE') {
          if (this.operation == "in" || this.operation == "not in")
          {

            columnCodeValue=columnCodeValue.replace(/(^,)|(,$)/ig, "");
            columnValue=columnValue.replace(/(^,)|(,$)/ig, "");

            if (macroColumn == "")
            {
              tempClauseToExecute="TRUNC(" + columnToStore + ")" + " " + this.operation + " (" + columnCodeValue + ")";
            }
            else
            {
              macroColumn=macroColumn.replace(expOper, " " + this.operation);
              macroColumn=macroColumn.replace(expParam, " (" + columnCodeValue + ")");
              tempClauseToExecute=macroColumn;
            }


            if (emptyDate)
              tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " (" + columnValue + ")";
            else
              tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " (" + columnValue + ") ]";

            if (emptyDate)
            {
              tempClauseToExecute=tempClauseToExecute + " OR " + columnToStore + " IS NULL";
              tempClauseToDisplay=tempClauseToDisplay + " " + this.orButton.label + " " + columnToDisplay + " " +  " is null]";
            }

            this.tabAllConditions.push({tabName: tableName, columnToStore: columnToStore, clause: tempClauseToExecute});
          }
        } else if (typeCode == 'DTIME') {
          if (this.operation == "in" || this.operation == "not in")
          {

            columnCodeValue=columnCodeValue.replace(/(^,)|(,$)/ig, "");
            columnValue=columnValue.replace(/(^,)|(,$)/ig, "");
            if (this.validateDateTimeField(columnValue)  == "error_dateTime") {
             columnValue = "error_dateTime";
           }
            if (macroColumn == "")
            {
              tempClauseToExecute="TO_DATE(" + columnToStore + ")" + " " + this.operation + " (" + columnCodeValue + ")";
            }
            else
            {
              macroColumn=macroColumn.replace(expOper, " " + this.operation);
              macroColumn=macroColumn.replace(expParam, " (" + columnCodeValue + ")");
              tempClauseToExecute=macroColumn;
            }


            if (emptyDate)
              tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " (" + columnValue + ")";
            else
              tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " (" + columnValue + ") ]";

            if (emptyDate)
            {
              tempClauseToExecute=tempClauseToExecute + " OR " + columnToStore + " IS NULL";
              tempClauseToDisplay=tempClauseToDisplay + " " + this.orButton.label + " " + columnToDisplay + " " +  " is null]";
            }
            this.tabAllConditions.push({tabName: tableName, columnToStore: columnToStore, clause: tempClauseToExecute});
          }
        }
      }
      else if (currentComponent == 'SwtComboBox')
      {
       
        enumProfile=this.comboBox.selectedItem.content;
        //columnValue=this.comboBox.selectedItem.id;
        let columnValueToSave: string="";
      //  let columnValueToSave2: string="";
        let typeTextCombo1: string = '';
        let typeTextCombo2: string = '';
        let columnValueToDisplay: string = '';


        if (this.operation == 'between')
        {
         
          let columnValueToDisplay2: string="";
          let columnValueToSave2: string="";
          if (this.comboBox.selectedItem.id == 'TYPETEXT')
          {
            columnValue=this.textInput.text;
            columnValueToDisplay=this.textInput.text;
            typeTextCombo1='TT';
          }
          else
          {
            columnValue=this.comboBox.selectedItem.id;
            columnValueToDisplay=this.comboBox.selectedItem.content;
          }
         
          if (this.comboBox2.selectedItem.id == 'TYPETEXT')
          {
            if (this.textInput2.text == '')
              columnValue = '';
            else
            {
              columnValue2=this.textInput2.text;
              columnValueToDisplay2=this.textInput2.text;
              typeTextCombo2='TT';
            }
          }
          else
          {
            columnValue2=this.comboBox2.selectedItem.id;
            columnValueToDisplay2=this.comboBox2.selectedItem.content;
          }



          if ((this.comboBox.selectedItem.type == 'NUM'|| this.comboBox.selectedItem.type == "DECI") && typeTextCombo1 == "TT") {
            columnValueToSave="To_number(" + this.getNumberFromAmount(columnValue) + ")";
            tempClauseToExecute=columnToStore + " " + this.operation + " " + columnValueToSave;
          } else {
            columnValueToSave=columnValue;
            tempClauseToExecute=columnToStore + " " + this.operation + " " + columnValueToSave;
          }
         
          if ((this.comboBox2.selectedItem.type == "NUM" || this.comboBox2.selectedItem.type == "DECI") && typeTextCombo2 == "TT")
          {
            columnValueToSave2 = "To_number(" + this.getNumberFromAmount(columnValue2) + ")";
            tempClauseToExecute=tempClauseToExecute + " and " + columnValueToSave2;
          }
          else
          {
            columnValueToSave2=columnValue2;
            tempClauseToExecute=tempClauseToExecute + " and " + columnValueToSave2;
          }
          
          tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " " + columnValueToDisplay + " " + this.andButton.label + " " + columnValueToDisplay2 + " ]";
          if (typeTextCombo1 == "P" || typeTextCombo2 == "P") // P: to indicate that is a profile
            this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnValueToSave + " and " + columnValueToSave2, columnValue: columnValueToSave, columnValue2: columnValueToSave2, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "Y", index: selectedIndex, conditionId: conditionId});
          else if (typeTextCombo1 == "B" || typeTextCombo2 == "B") // B: to indicate that is a variable for binding
            this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnValueToSave + " and " + columnValueToSave2, columnValue: columnValueToSave, columnValue2: columnValueToSave2, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "B", index: selectedIndex, conditionId: conditionId});
          else
            this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnValueToSave + " and " + columnValueToSave2, columnValue: columnValueToSave, columnValue2: columnValueToSave2, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});

        }
        else if (this.operation == 'in' || this.operation == "exists") //when clicking on the in list button
          {

          columnValue=this.comboBox.selectedItem.id;
          if (this.comboBox.selectedItem.type == 'LIST') {
            
            tempClauseToExecute=columnToStore + ' ' + this.operation + ' (' + columnValue + ')';
          }
          else
          {
            
            if (this.comboBox.selectedItem.type == 'NUM') {
              
              columnValue=columnValue.replace(':value', this.comboBox.selectedItem.value);
              tempClauseToExecute=columnToStore + ' ' + this.operation + ' (' + columnValue + ')';
            }
            else if (this.comboBox.selectedItem.type == 'CHAR')
            {
             
              if(columnValueUpper == 'YN' || columnValueUpper == 'YY')
              {
                
                columnValue=columnValue.replace(/:value/g, "'" + this.comboBox.selectedItem.value + "'");
                tempClauseToExecute="upper(" + columnToStore + ") " + this.operation + " (" + columnValue + ")";
              }
              if (columnValueUpper == 'NY' || columnValueUpper == 'YY')
              {
               
                columnValue=columnValue.replace(/:value/g, "'" + String(this.comboBox.selectedItem.value).toUpperCase() + "'");
                tempClauseToExecute=columnToStore + " " + this.operation + " (" + columnValue + ")";
              }
              if (columnValueUpper == 'NN')
              {
                
                columnValue=columnValue.replace(/:value/g, " TO_DATE('" + this.comboBox.selectedItem.value + "', '" + this.commonSysDateFormat + "')");
                tempClauseToExecute=columnToStore + " " + this.operation + " (" + columnValue + ")";
              }
            }
            else if (this.comboBox.selectedItem.type == 'DATE')
            {
             
              columnValue=columnValue.replace(/:value/g, "'" + this.comboBox.selectedItem.value + "'");
              tempClauseToExecute=columnToStore + " " + this.operation + " (" + columnValue + ")";
            }
          }
          columnValueToSave="(" + columnValue.replace(/:column_code/g, columnToStore) + ")";
          columnValueToDisplay= this.comboBox.selectedItem.toString();
          typeCode= this.comboBox.selectedItem.type;
         
          tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " " + columnValueToDisplay + " ]";

          if (this.operation == "exists")
          {
            tempClauseToExecute=tempClauseToExecute.replace(columnToStore + " ","");
            tempClauseToExecute=tempClauseToExecute.replace("upper(" + columnToStore + ") ","");
          }

          tempClauseToExecute=tempClauseToExecute.replace(/:column_code/g, columnToStore);

          this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnValueToSave, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});

        }
        else
        {
          
          if (this.comboBox.selectedItem.id == 'TYPETEXT')
          {
            columnValue=this.textInput.text;
            columnValueToDisplay=this.textInput.text;
            enumProfile="TT";

            if (columnValue == '' && this.comboBox.selectedItem.type == 'CHAR')
            {
              if (this.operation == '=')
              {
                columnValueToDisplay= "is null"; //SwtUtil.getCommonMessages('searchScreen.label.isEmpty');
                this.operation = '';
                this.operationToDisplay = '';
                columnValue='is null';
              }
              else if (this.operation == '<>')
              {
                columnValueToDisplay= 'is not null'; //SwtUtil.getCommonMessages('searchScreen.label.isNotEmpty');
                this.operation = '';
                this.operationToDisplay = '';
                columnValue='is not null';
              }
            }
          }
          else
          {          
            columnValue=this.comboBox.selectedItem.id;
            columnValueToDisplay=this.comboBox.selectedItem.content;
          }

          if ((this.comboBox.selectedItem.type == "NUM" || this.comboBox.selectedItem.type == "DECI") && enumProfile != 'P' && enumProfile != 'B')
          {
            columnValueToSave="To_number(" + this.getNumberFromAmount(columnValue) + ")";
            columnCodeValue=" " + columnValue;
            tempClauseToExecute=columnToStore + " " + this.operation + " " + columnValueToSave;
          }
          else if (this.comboBox.selectedItem.type == "BIND_C")
          {
           
            if (columnValueUpper == "YY" || columnValueUpper == "NY")
              columnValueToSave="upper(" + columnValue + ")";
            else
              columnValueToSave=columnValue;

            if (columnValueUpper == "YY" || columnValueUpper == "YN")
            {
              columnToStore="upper(" + columnToStore + ") ";
              tempClauseToExecute=columnToStore + this.operation + " " + columnValueToSave;
            }
            else
            {
              tempClauseToExecute=columnToStore + " " + this.operation + " " + columnValueToSave;
            }
            this.varToBind.push(columnValue);
          }
          else if (this.comboBox.selectedItem.type == "BIND_N")
          {
            
            columnValueToSave=columnValue;
            tempClauseToExecute=columnToStore + " " + this.operation + " " + columnValueToSave;
            this.varToBind.push(columnValue);
          }
          else
          {
           
            if (macroColumn != "")
              columnValue = columnValue.replace(/[']/g, "''");
            
            if (enumProfile == 'P' || enumProfile == 'B')
              columnValueToSave=columnValue;
            else
            {
              
              if (columnValue == " is null" || columnValue == " is not null") // do not apply upper
              {
                columnValueToSave=columnValue;
              }
              else
              {
                if (columnValueUpper == "YY" || columnValueUpper == "NY")
                  columnValueToSave="'" + columnValue.toUpperCase() + "'";
                else
                  columnValueToSave="'" + columnValue + "'";
              }
            }

            
            if (macroColumn == "")
            {
                if (columnValueUpper == "YY" || columnValueUpper == "YN")
                {
                    tempClauseToExecute="upper(" + columnToStore + ") " + this.operation + " " + columnValueToSave;
                    columnToStore="upper(" + columnToStore + ") ";
                }
                else
                {
                    tempClauseToExecute=columnToStore + " " + this.operation + " " + columnValueToSave;
                }
            }   
            else
            {
                macroColumn=macroColumn.replace(expOper, " " + this.operation);
                macroColumn=macroColumn.replace(expParam, " " +columnValueToSave);
                tempClauseToExecute=macroColumn;
            }   
          }
        
          tempClauseToDisplay="[ " + columnToDisplay + " " + this.operationToDisplay + " " + columnValueToDisplay + " ]";
          if (enumProfile == 'P')
            this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnValueToSave, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "Y", index: selectedIndex, conditionId: conditionId});
          else if (enumProfile == 'B')
            this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnValueToSave, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "B", index: selectedIndex, conditionId: conditionId});
          else
            this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnValueToSave, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});

        }
      }
      else if (currentComponent == 'SwtDateField' && typeCode != "DTIME") {
        let sysdate: string= 'System date';
        let tolerenceValue: string = '';
        //                       let df1:DateFormatter=new DateFormatter();
        let transFormat: string = '';
        let date:Date;
        let objVar = {columnToStore:"", columnValue:""};

//                        df1.formatString=this.commonSysDateFormat;

        if (this.dateField.text != '')
        {
          if (this.validateDateField(this.dateField.text)  == "error_date") {
            columnValue = "error_date";
            return;
          }
          if (this.sysdateformat.toUpperCase() == 'DD-MMM-YYYY')
          {
            
            transFormat=this.transformDate2(this.dateField.dropdown.selectedDate);
            columnValue=CommonUtil.formatDate(this.dateField.parseDate(transFormat, this.sysdateformat.toUpperCase()), this.commonSysDateFormat);
//                              date=DateField.stringToDate(transFormat, 'DD/MM/YYYY');
//                              columnValue=df1.format(date);
          }
          else
          {
            // format datefield.text to Date and then format the date with commonSysDateFormat
            //                             columnValue=df1.format(DateField.stringToDate(this.dateField.text, this.sysdateformat));
            columnValue=CommonUtil.formatDate(this.dateField.parseDate(this.dateField.text, this.sysdateformat.toUpperCase()), this.commonSysDateFormat);


          }
          if (columnValue == '')
            columnValue='false';

        }
        let columnValue22: string;
        if (this.operation == 'between') {
          columnValue22=this.dateField2.text;
          if (columnValue22 == '')
            columnValue = '';
          else {
            if (this.sysdateformat.toUpperCase() == 'DD-MMM-YYYY') {
              transFormat = '';
              transFormat=this.transformDate2(this.dateField2.dropdown.selectedDate);
              columnValue=CommonUtil.formatDate(this.dateField2.parseDate(transFormat, this.sysdateformat.toUpperCase()), this.commonSysDateFormat);

            } else {
              columnValue=CommonUtil.formatDate(this.dateField2.parseDate(this.dateField.text, this.sysdateformat.toUpperCase()), this.commonSysDateFormat);
            }
            if (columnValue22 == '')
              columnValue='false';
            }
          
          if (this.sysDateActive)
          {
              if (this.tolerenceNumStep.value > 0)
                  tolerenceValue=" + " + this.tolerenceNumStep.value;
              else if (this.tolerenceNumStep.value < 0)
                  tolerenceValue="" + this.tolerenceNumStep.value;

              //columnValue22="TO_CHAR(SYSDATE,'" + commonSysDateFormat + "')";
              columnValue22="SYSDATE";

          }
          
          if (macroColumn == "")
          {
              if (this.sysDateActive)
              {
                  tempClauseToExecute=this.getOracleDateFormat(columnToStore, columnValue, columnValue22, this.operation, tolerenceValue, isIndexFunction, objVar);
              }
              else
              {
                  tempClauseToExecute=this.getOracleDateFormat(columnToStore, columnValue, columnValue22, this.operation, "", isIndexFunction, objVar);
              }
              
              columnValue=objVar.columnValue;
              columnToStore=objVar.columnToStore;
          }   
          else
          {
              var value2:string="";
              value=" TO_DATE('" + columnValue + "', '" + this.commonSysDateFormat + "')"; 
              
              if (this.sysDateActive)
                  value2=" TRUNC(SYSDATE)" + tolerenceValue;
              else
                  value2=" TO_DATE('" + columnValue22 + "', '" + this.commonSysDateFormat + "')";
                  
              macroColumn=macroColumn.replace(expOper, " " + this.operation);
              macroColumn=macroColumn.replace(expParam,value);
              tempClauseToExecute=macroColumn + " and" + value2;
          }   
         
          if (this.sysDateActive)
          {
            if (this.tolerenceNumStep.value > 0)
              tolerenceValue=' + ' + this.tolerenceNumStep.value;
            else if (this.tolerenceNumStep.value < 0)
              tolerenceValue=String(this.tolerenceNumStep.value);
            else
              tolerenceValue = '';

            tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + this.dateField.text + ' ' + this.andButton.label + ' ' + sysdate + tolerenceValue + ' ]';

          }
          else
          {
            tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + this.dateField.text + ' ' + this.andButton.label + ' ' + this.dateField2.text + ' ]';
          }
          
          this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnValue, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});                          
          
        }
        else
        {
         
          if (columnValue == '' && !this.sysDateActive)
          {
            if (this.operation == '=')
            {
              value='IS NULL';
              valueToDisplay= ' is null'; //SwtUtil.getCommonMessages('searchScreen.label.isEmpty');
              //this.operation = '';// this line is hidden because when column value is null, operator disappear and alert of missing... is always displayed
              this.operationToDisplay = '';
              columnValue='is null';
            }
            else if (this.operation == '<>')
            {
              value='IS NOT NULL';
              valueToDisplay= 'is not null'; //SwtUtil.getCommonMessages('searchScreen.label.isNotEmpty');
              //this.operation = '';
              this.operationToDisplay = '';
              columnValue='is not null';
            }
          }
          else if (columnValue != 'false')
          {
            value=' TO_DATE("" + columnValue + "','" + this.commonSysDateFormat + "")';
            valueToDisplay=this.dateField.text;
          }

          if (this.sysDateActive)
          {
              if (this.tolerenceNumStep.value > 0)
                  tolerenceValue=" + " + this.tolerenceNumStep.value;
              else if (this.tolerenceNumStep.value < 0)
                  tolerenceValue="" + this.tolerenceNumStep.value;  

              //columnValue="TO_CHAR(SYSDATE,'" + commonSysDateFormat + "')";
              columnValue="SYSDATE";
              
              // for macrocolumn
              if (tolerenceValue != "")
                  value="TRUNC(SYSDATE" + tolerenceValue + ")";
              else
                  value="TRUNC(SYSDATE)";
                  
          }

          if (macroColumn == "")
          {
              if (this.sysDateActive)
              {
                  tempClauseToExecute=this.getOracleDateFormat(columnToStore, columnValue, columnValue22, this.operation, tolerenceValue, isIndexFunction, objVar);
              }
              else
              {
                  tempClauseToExecute=this.getOracleDateFormat(columnToStore, columnValue, columnValue22, this.operation, "", isIndexFunction, objVar);
              }
              
              columnValue=objVar.columnValue;
              columnToStore=objVar.columnToStore;
              
          }
          else
          {
              macroColumn=macroColumn.replace(expOper, " " + this.operation);
              macroColumn=macroColumn.replace(expParam, " " +value);
              tempClauseToExecute=macroColumn;
          }   
          

         
          if (this.sysDateActive)
          {
          
            if (this.tolerenceNumStep.value > 0)
              tolerenceValue=' + ' + String(this.tolerenceNumStep.value);
            else if (this.tolerenceNumStep.value < 0)
              tolerenceValue=String(this.tolerenceNumStep.value);
            else
              tolerenceValue = '';

            tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + sysdate + tolerenceValue + ' ]';
          }
          else
          {
            tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + valueToDisplay + ' ]';
          }
          
          this.tabAllConditions.push({columnToStore: columnToStore, operation: (columnValue.indexOf("is null") !=  -1 || columnValue.indexOf("is not null")!= -1 ) ? "" : this.operation, columnCodeValue: columnValue, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
        }
      } else if(currentComponents.indexOf("SwtTextInput") > -1 && currentComponents.indexOf("SwtDateField") > -1) {
        let sysdate: string= 'System date';
        let tolerenceValue: string = '';
        let transFormat: string = '';
        let date:Date;
        let objVar = {columnToStore:"", columnValue:""};
        if (this.dateField.text != '' )
        {
          if (this.validateDateField(this.dateField.text)  == "error_date") {
            columnValue = "error_date";
            return;
          }
          if (this.sysdateformat.toUpperCase() == 'DD-MMM-YYYY') {
            transFormat=this.transformDate2(this.dateField.dropdown.selectedDate);
            columnValue=CommonUtil.formatDate(this.dateField.parseDate(transFormat, this.sysdateformat.toUpperCase()), this.commonSysDateFormat) +" " +(this.timeField.text) ?  this.timeField.text : "00:00";
          } else {
              if(this.timeField.text != "") {
                columnValue= CommonUtil.formatDate(this.dateField.parseDate(this.dateField.text, this.sysdateformat.toUpperCase()), this.commonSysDateFormat) + this.timeField.text;
              } else {
                columnValue= CommonUtil.formatDate(this.dateField.parseDate(this.dateField.text, this.sysdateformat.toUpperCase()), this.commonSysDateFormat);
              }
              

          }
          if (columnValue == '')
            columnValue='false';


        }
        let columnValue22: string;
        if (this.operation == 'between') {
          columnValue22=this.dateField2.text + (this.timeField.text) ? this.timeField.text : "00:00" ;
          if (columnValue22 == '')
            columnValue = '';
          else {
            if (this.sysdateformat.toUpperCase() == 'DD-MMM-YYYY') {
              transFormat = '';
              transFormat=this.transformDate2(this.dateField2.dropdown.selectedDate);
              columnValue=CommonUtil.formatDate(this.dateField2.parseDate(transFormat, this.sysdateformat.toUpperCase()), this.commonSysDateFormat) +" " + this.timeField.text;

            } else {
              columnValue=CommonUtil.formatDate(this.dateField2.parseDate(this.dateField.text, this.sysdateformat.toUpperCase()), this.commonSysDateFormat)  +" " + this.timeField.text;
            }
            if (columnValue22 == '')
              columnValue='false';
          }


          if (macroColumn == "")
          {
            tempClauseToExecute=this.getOracleDateFormat(columnToStore, columnValue, columnValue22, this.operation, "", isIndexFunction, objVar);
            columnValue=objVar.columnValue;
            columnToStore=objVar.columnToStore;
          }
          else
          {
            var value2:string="";
            value=" TO_DATE('" + columnValue + "', '" + this.commonSysDateFormat  + "HH24:MI" + "')";

            if (this.sysDateActive)
              value2=" TRUNC(SYSDATE)" + tolerenceValue;
            else
              value2=" TO_DATE('" + columnValue22 + "', '" + this.commonSysDateFormat + "HH24:MI" + "')";

            macroColumn=macroColumn.replace(expOper, " " + this.operation);
            macroColumn=macroColumn.replace(expParam,value);
            tempClauseToExecute=macroColumn + " and" + value2;
          }

          if (this.sysDateActive)
          {
            if (this.tolerenceNumStep.value > 0)
              tolerenceValue=' + ' + this.tolerenceNumStep.value;
            else if (this.tolerenceNumStep.value < 0)
              tolerenceValue=String(this.tolerenceNumStep.value);
            else
              tolerenceValue = '';

            tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + this.dateField.text + ' '+ this.timeField.text + ' ' + this.andButton.label + ' ' + sysdate + tolerenceValue + ' ]';

          }
          else
          {
            tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + this.dateField.text + ' '+ this.timeField.text  + ' ' + this.andButton.label + ' ' + this.dateField2.text + ' ]';
          }
          this.tabAllConditions.push({columnToStore: columnToStore, operation: this.operation, columnCodeValue: columnValue, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});

        }
        else
        {

          if (columnValue == '' && !this.sysDateActive)
          {
            if (this.operation == '=')
            {
              value='IS NULL';
              valueToDisplay= ' is null'; //SwtUtil.getCommonMessages('searchScreen.label.isEmpty');
              //this.operation = '';
              this.operationToDisplay = '';
              columnValue='is null';
            }
            else if (this.operation == '<>')
            {
              value='IS NOT NULL';
              valueToDisplay= 'is not null'; //SwtUtil.getCommonMessages('searchScreen.label.isNotEmpty');
              //this.operation = '';
              this.operationToDisplay = '';
              columnValue='is not null';
            }
          }
          else if (columnValue != 'false')
          {
            value=" TO_DATE('" + columnValue + "', '" + this.commonSysDateFormat  + " HH24:MI" + "')";
            valueToDisplay=this.dateField.text + " "+ this.timeField.text;
          }
          if (macroColumn == "")
          {
            if (this.sysDateActive)
            {
              tempClauseToExecute=this.getOracleDateTimeFormat(columnToStore, columnValue, columnValue22, this.operation, tolerenceValue, isIndexFunction, objVar);
            }
            else
            {
              tempClauseToExecute=this.getOracleDateTimeFormat(columnToStore, columnValue, columnValue22, this.operation, "", isIndexFunction, objVar);
            }

            columnValue=objVar.columnValue;
            columnToStore=objVar.columnToStore;


          }
          else
          {
            macroColumn=macroColumn.replace(expOper, " " + this.operation);
            macroColumn=macroColumn.replace(expParam, " " +value);
            tempClauseToExecute=macroColumn;
          }



          if (this.sysDateActive)
          {

            if (this.tolerenceNumStep.value > 0)
              tolerenceValue=' + ' + String(this.tolerenceNumStep.value);
            else if (this.tolerenceNumStep.value < 0)
              tolerenceValue=String(this.tolerenceNumStep.value);
            else
              tolerenceValue = '';

            tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + sysdate + tolerenceValue + ' ]';
          }
          else
          {
            tempClauseToDisplay='[ ' + columnToDisplay + ' ' + this.operationToDisplay + ' ' + valueToDisplay + ' ]';
          }
          this.tabAllConditions.push({columnToStore: columnToStore, operation: (columnValue.indexOf("is null") !=  -1 || columnValue.indexOf("is not null")!= -1 ) ? "" : this.operation, columnCodeValue: columnValue, typeCode: typeCode, localValue: localValue, tabName: tableName, profileField: profileField, andOrString: this.andOrString, fieldExpression: fieldExpression, clause: tempClauseToExecute, enumProfile: "N", index: selectedIndex, conditionId: conditionId});
        }

      }

      if (columnValue == '' && (this.operation != "like" && this.operation != "Not Like")) {

        this.swtAlert.warning(SwtUtil.getPredictMessage('queryBuilderScreen.alert.missingProperty', null) + ' ' + columnToDisplay, "Warning");
      } else
        if (columnValue == 'false') {
       
        this.swtAlert.warning(SwtUtil.getPredictMessage('queryBuilderScreen.alert.checkEnteredValue') + ' ' + columnToDisplay, "Warning");
      } else if (columnValue == 'error_dateTime') {

        this.swtAlert.warning(SwtUtil.getPredictMessage('queryBuilderScreen.alert.dateTimeFormat') + ' '+ this.sysdateformat.toUpperCase() + ' HH: MM');
      } else if (columnValue == 'error_time') {

        this.swtAlert.warning(SwtUtil.getPredictMessage('alert.validTime'));
      }
      else if (columnValue != '' && columnValue != 'false' && this.screenName == "add") {
        if (this.queryToDisplay == '') {
         
          this.arrayToDisplayQuery.push(tempClauseToDisplay);
          this.arrayToExecuteQuery.push(tempClauseToExecute);
          if (profileField == 'Y' || profileField == 'N')
          {
            this.agregationFunc.push(columnToStore);
          }
          //Ask Amani about enumProfile and profileField
          if (enumProfile == 'P' || enumProfile == 'B') {
            this.agregationFunc.push(this.tabAllConditions[this.tabAllConditions.length - 1].columnCodeValue);
          }

        }
        else {
      
          this.arrayToDisplayQuery.push(' ' + tempClauseToDisplay);
          this.arrayToExecuteQuery.push(' ' + tempClauseToExecute);
          if (profileField == 'Y')
          {
            this.agregationFunc.push(columnToStore);
          }
          if (enumProfile == 'P' || enumProfile == 'B') {
            this.agregationFunc.push(this.tabAllConditions[this.tabAllConditions.length - 1].columnCodeValue);
          }

        }
        
        this.queryToDisplay = '';
        this.queryToExecute = '';

        for (let l = 0; l < this.arrayToDisplayQuery.length; l++)
        {
       
          this.queryToDisplay=this.queryToDisplay + this.arrayToDisplayQuery[l];
          this.queryToExecute=this.queryToExecute + this.arrayToExecuteQuery[l];
        }
        //////////
        this.queryToDisplay2=this.queryToExecute;
       

        this.undoButton.enabled=true;
        this.undoButton.buttonMode=true;
        this.resetButton.enabled=true;
        this.resetButton.buttonMode=true;
        this.listCriteria.enabled = false;
        this.leftParentheseButton.enabled = false;
        this.leftParentheseButton.buttonMode = false;
        this.andOrString = '';

       
        if (this.countRightP % this.countLeftP == 0)
        {
          this.rightParentheseButton.enabled = false;
          this.rightParentheseButton.buttonMode = false;
          this.okButton.enabled=true;
          this.okButton.buttonMode=true;
       }
        else
        {
          this.rightParentheseButton.enabled=true;
          this.rightParentheseButton.buttonMode=true;
          this.okButton.enabled = false;
          this.okButton.buttonMode = false;
        }
        this.clearContraints();
      }
      else if (columnValue != "" && columnValue != "false" && this.screenName == "change")
      {

        
        let oldCondition="";
        let oldConditionWithoutSpaces="";
        let condFieldName="";
        this.queryToDisplay = this.replaceConditionToDisplayById(conditionId - 1, tempClauseToDisplay);
        this.labelQuery.text = tempClauseToDisplay;
        if (this.previousTypeCode != "") {
          typeCode = this.previousTypeCode;
        }
        
        
        condFieldName = this.listCriteria.selectedItem.label.condFieldName;
        if (this.listCriteria.selectedItem.label.operatorId == 'exists') {
          oldCondition = this.listCriteria.selectedItem.label.operatorId + " " + this.listCriteria.selectedItem.label.fieldValue;
        } else if ((this.listCriteria.selectedItem.label.typeCode == "DATE" && (this.previousTypeCode == "DATE" || this.previousTypeCode == "")) || this.previousTypeCode == "DATE"
                || (this.listCriteria.selectedItem.label.typeCode == "DTIME" && (this.previousTypeCode == "DTIME" || this.previousTypeCode == "")) || this.previousTypeCode == "DTIME")  // previousTypeCode is empty when only the value of a criterea is changed
        {
          oldCondition= this.listCriteria.selectedItem.label.fieldValue;
        }
        else
        {

         if(this.listCriteria.selectedItem.label.clause) {
          // let macroValue = this.listCriteria.selectedItem.label.macroColumn;
          // macroValue=macroValue.replace(expOper, " " + this.operation);
          // macroValue=macroValue.replace(expParam, " (" + columnCodeValue + ")");

          if (this.listCriteria.selectedItem.label.operatorId == ""){
            oldCondition=this.listCriteria.selectedItem.label.clause;
            oldConditionWithoutSpaces = " " + this.listCriteria.selectedItem.label.clause;
          }
          else{
            oldCondition= this.listCriteria.selectedItem.label.clause;
            oldConditionWithoutSpaces=" " + this.listCriteria.selectedItem.label.clause;
          }
         }else {
          if (this.listCriteria.selectedItem.label.operatorId == ""){
            oldCondition=condFieldName + "  " + this.listCriteria.selectedItem.label.fieldValue;
            oldConditionWithoutSpaces = condFieldName + " " + this.listCriteria.selectedItem.label.fieldValue;
          }
          else{
            oldCondition=condFieldName + " " + this.listCriteria.selectedItem.label.operatorId + "  " + this.listCriteria.selectedItem.label.fieldValue;
            oldConditionWithoutSpaces=condFieldName + " " + this.listCriteria.selectedItem.label.operatorId +" " + this.listCriteria.selectedItem.label.fieldValue;
          }
         }
         
          // if the operation id is null thne no need to add an extra space before field value
        
        }

        let newQueryToExecute: string = this.replaceConditionToExecuteById(conditionId - 1, oldCondition, oldConditionWithoutSpaces, tempClauseToExecute);
        if (newQueryToExecute == "" && typeCode == "DECI")
        {
         
          condFieldName = this.listCriteria.selectedItem.label.condFieldName + " * :prate";
          if (this.listCriteria.selectedItem.label.operatorId == "exists"){
          // test added to resolve problem of the concatenation of some conditions with two spaces after the operator (in the creation of condition)
            oldCondition= this.listCriteria.selectedItem.label.operatorId + "  " + this.listCriteria.selectedItem.label.fieldValue;
            oldConditionWithoutSpaces = this.listCriteria.selectedItem.label.operatorId  + " " + this.listCriteria.selectedItem.label.fieldValue;
          }
          else{
          // test added to resolve problem of the concatenation of some conditions with two spaces after the operator (in the creation of condition)
            oldCondition=condFieldName + " " + this.listCriteria.selectedItem.label.operatorId + "  " + this.listCriteria.selectedItem.label.fieldValue;
            oldConditionWithoutSpaces = condFieldName + " " + this.listCriteria.selectedItem.label.operatorId + " " +  this.listCriteria.selectedItem.label.fieldValue;
          }

            newQueryToExecute= this.replaceConditionToExecuteById(conditionId - 1, oldCondition,oldConditionWithoutSpaces, tempClauseToExecute);
        }
       

        if (newQueryToExecute == "") {
         
        //  SwtUtil.logError(errorToLog, moduleId, getQualifiedClassName(this) + ".mxml", "createQuery: Matching between query and oldCondition is false, oldCondition: ["+ oldCondition + "], query to be replaced: [" + queryToExecute + "], new Condition: [" + newQueryToExecute + "], new query to replace: [" + tempClauseToExecute + "]", this.errorLocation);
        }
        this.queryToExecute=newQueryToExecute;
       
        this.clearContraints();
        this.labelQuery.text="";
        this.okButton.enabled=true;
        this.okButton.buttonMode=true;
        this.resetButton.enabled=true;
        this.resetButton.buttonMode=true;
        let findDuplicateCond: boolean=false;
        let indexDuplicateCond: number=-1;
        for (let key in this.originalJSONList) {
         if(this.originalJSONList[key].code == this.selectedItemsList && this.selectedItemsList != null ) {
          this.originalJSONList[key].fieldValue = StringUtils.trim(String(this.tabAllConditions[this.tabAllConditions.length - 1].columnCodeValue));
          this.originalJSONList[key].operatorId= this.tabAllConditions[this.tabAllConditions.length - 1].operation;
          this.originalJSONList[key].conditionId= this.tabAllConditions[this.tabAllConditions.length - 1].conditionId;
          this.originalJSONList[key].condFieldName= StringUtils.trim(this.tabAllConditions[this.tabAllConditions.length - 1].columnToStore);
          this.originalJSONList[key].nextCondition= this.tabAllConditions[this.tabAllConditions.length - 1].andOrString;
          this.originalJSONList[key].profileFieldValue= this.tabAllConditions[this.tabAllConditions.length - 1].profileFieldValue;
          }
        }
        
        for (let jj: number=0; jj < this.tabAllConditions.length - 1; jj++)
        {
          
          // if (this.tabAllConditions[jj].index  == selectedIndex)

          if ( this.tabAllConditions[jj].conditionId== conditionId)
          {
            findDuplicateCond=true;
            indexDuplicateCond=jj;
            break;
          }
        }
       
        if (findDuplicateCond)
        {
          this.tabAllConditions.splice(indexDuplicateCond, 1);
        }
       
        let storeData = [];
        for (let key in this.originalJSONList) {
          if (this.originalJSONList[key].toChange == 'Y') {
            storeData.push({'label': this.originalJSONList[key]});
          }
        }
        this.gridJSONList = {row: storeData, size: storeData.length}; ///Be careful
        this.listCriteria.gridData = this.gridJSONList;
      }
    } catch (error)
    {
      this.logger.error('method [createQuery] - error : ', error , '- errorLocation :', this.errorLocation);
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this), 'createQuery', this.errorLocation);
    }
    this.logger.info('method [createQuery] - END ');
  }



  /**
   * transformDate
   *
   * @param date : String
   *
   * transform date when sysdateformat = 'DD-MMM-YYYY'
   *
   **/
  private  transformDate(date): string
  {
   
    
    let dateValue: string = '';
    try
    {
      this.logger.info('method [transformDate] - START ');
      
      let part1: string=(date).substr(0, 2);
      let part2: string=(date).substr(3, 3);
      let part3: string=(date).substr(7, (date).length);
      
      for (let n = 0; n < this.nameMonths.length; n++)
      {
        
        if (this.nameMonths[n].substr(0, 3) == part2)
        {
          if (n < 10)
            dateValue=part1 + '/0' + (n + 1) + '/' + part3;
          else
            dateValue=part1 + '/' + (n + 1) + '/' + part3;
        }
      }
    }
    catch ( error ) {
      this.logger.error( 'method [transformDate] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'transformDate', this.errorLocation );
    }
    this.logger.info( 'method [transformDate] - END ' );

    return dateValue;
  }

  /**
   * doOpenCloseParentheses()
   *
   * @param None
   *
   * Function used to open/close parentheses character
   **/
   doOpenCloseParentheses():void
  {
   
    
    try
    {
      
      this.logger.info('method [doOpenCloseParentheses] - START ');
      const focusObj: any = Object(focusManager.getFocus());
      let eventString: string;
      
      if (typeof focusObj.id === 'string') {
        eventString = focusObj.id;
      } else if (focusObj.id && typeof focusObj.id.id === 'string') {
        eventString = focusObj.id.id;
      } else {
        eventString = ''; // or handle the fallback case as needed
      }
      this.okButton.enabled = false;
      this.okButton.buttonMode = false;
      if (this.recUnitGroupDisplay == 'T')
        this.execStatus='N';
      
      if (eventString == 'leftParentheseButton')
      {
        
        this.openParenthese();
      }
      else if (eventString == 'rightParentheseButton')
      {
        
        this.closeParenthese();
      }
    }
    catch ( error ) {
      this.logger.error( 'method [doOpenCloseParentheses] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'doOpenCloseParentheses', this.errorLocation );
    }
    this.logger.info( 'method [doOpenCloseParentheses] - END ' );
  }

  /**
   * openParenthese()
   *
   * Method used to open parenthese character to query.
   **/
  private openParenthese():void
  {
   
    
    try
    {
      this.logger.info('method [openParenthese] - START ');
      
      this.countLeftP++;
      
      if (this.queryToDisplay == '')
      {
        
        this.queryToExecute = '';
        this.arrayToDisplayQuery.push('(');
        this.arrayToExecuteQuery.push('(');
        this.queryToDisplay=this.queryToDisplay + '(';
        this.queryToExecute=this.queryToExecute + '(';
      }
      else
      {
        
        this.arrayToDisplayQuery.push(' (');
        this.arrayToExecuteQuery.push(' (');
        this.queryToDisplay=this.queryToDisplay + ' (';
        this.queryToExecute=this.queryToExecute + ' (';
      }
      //////////
      this.queryToDisplay2=this.queryToExecute;

      
      this.undoButton.enabled=true;
      this.undoButton.buttonMode=true;
      this.resetButton.enabled=true;
      this.resetButton.buttonMode=true;
      this.listCriteria.enabled=true;
      this.rightParentheseButton.enabled = false;
      this.rightParentheseButton.buttonMode = false;
      this.andButton.enabled = false;
      this.andButton.buttonMode = false;
      this.orButton.enabled = false;
      this.orButton.buttonMode = false;
    }
    catch ( error ) {
      this.logger.error( 'method [openParenthese] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'openParenthese', this.errorLocation );
    }
    this.logger.info( 'method [openParenthese] - END ' );
  }

  /**
   * closeParenthese()
   *
   * Method used to close parenthese character to the query.
   **/
  private closeParenthese():void
  {
   
    
    try
    {
      this.logger.info('method [closeParenthese] - START ');
      
      this.countRightP++;

      
      this.arrayToDisplayQuery.push(' )');
      this.arrayToExecuteQuery.push(' )');
      this.queryToDisplay=this.queryToDisplay + ' )';
      this.queryToExecute=this.queryToExecute + ' )';
      //////////
      this.queryToDisplay2=this.queryToExecute;

      
      if (this.countRightP % this.countLeftP == 0)
      {
        this.rightParentheseButton.enabled = false;
        this.rightParentheseButton.buttonMode = false;
        this.okButton.enabled=true;
        this.okButton.buttonMode=true;
        if (this.recUnitGroupDisplay == 'T')
          this.execStatus ='Y';
      }
      else
      {
        this.rightParentheseButton.enabled=true;
        this.rightParentheseButton.buttonMode=true;
        this.okButton.enabled = false;
        this.okButton.buttonMode = false;
        if (this.recUnitGroupDisplay == 'T')
          this.execStatus ='N';
      }
      
    }
    catch ( error ) {
      this.logger.error( 'method [closeParenthese] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'closeParenthese', this.errorLocation );
    }
    this.logger.info( 'method [closeParenthese] - END ' );
  }

  /**
   * addOperation()
   *
   * Function used to add operation string to query.
   **/
  public addOperation():void
  {
   
    
    try
    {
      
      this.logger.info('method [addOperation] - START ');
      if (this.arrayToExecuteQuery[this.arrayToExecuteQuery.length - 1] == ' and' || this.arrayToExecuteQuery[this.arrayToExecuteQuery.length - 1] == ' or')
      {
        
        this.previous();
      }
      
      this.arrayToDisplayQuery.push(' ' + this.andOrStringToDisplay);
      this.arrayToExecuteQuery.push(' ' + this.andOrString);
      this.queryToDisplay=this.queryToDisplay + ' ' + this.andOrStringToDisplay;
      this.queryToExecute=this.queryToExecute + ' ' + this.andOrString;
      /////////
      this.queryToDisplay2=this.queryToDisplay2 + ' ' + this.andOrString;

      
      this.okButton.enabled = false;
      this.okButton.buttonMode = false;
      if (this.recUnitGroupDisplay == 'T')
        this.execStatus ='N';
      this.undoButton.enabled=true;
      this.resetButton.buttonMode=true;
      this.resetButton.enabled=true;
      this.undoButton.buttonMode=true;
      this.leftParentheseButton.enabled=true;
      this.leftParentheseButton.buttonMode=true;
      this.listCriteria.enabled=true;
      
    }
    catch ( error ) {
      this.logger.error( 'method [addOperation] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'addOperation', this.errorLocation );
    }
    this.logger.info( 'method [addOperation] - END ' );
  }

  /**
   * previous()
   *
   * Function used to cancel last modify in query
   **/
  public previous():void {
    
    this.clearContraints();
    try {
      this.logger.info('method [previous] - START ');
      if (this.arrayToDisplayQuery.length > 0) {
        //listCriteria.enabled = false;
        if (this.arrayToExecuteQuery[this.arrayToExecuteQuery.length - 1] == ' )')
          this.countRightP--;
        if ((this.arrayToExecuteQuery[this.arrayToExecuteQuery.length - 1] == '(') || (this.arrayToExecuteQuery[this.arrayToExecuteQuery.length - 1] == ' ('))
          this.countLeftP--;

        ///////////////////////// remove last item from tabAllConditions and tableToJoin - Start ////////
        let lastItem: string=String(this.arrayToDisplayQuery[this.arrayToDisplayQuery.length - 1]);
        
        let lastChar: string=lastItem.substr(lastItem.length - 2, lastItem.length);
        
        let firstChar: string=lastItem.substr(0, 2);
        

        if ((firstChar == '[ ' && lastChar == ' ]') || (firstChar == ' [' && lastChar == ' ]'))
        {
          
          if (this.tableToJoin.length > 0)
          {
            let lastTableToJoin: string=this.tabAllConditions[this.tabAllConditions.length - 1].tabName;
            let find:boolean = false;

            
            for (let j = 0; j < this.tabAllConditions.length - 1; j++)
            {
              
              if (this.tabAllConditions[j].tabName == lastTableToJoin)
                find=true;
              break;
            }
           
            if (!find)
            {
              this.tableToJoin.splice(this.tableToJoin.length - 1, 1);
              this.aliasList.splice(this.aliasList.length - 1, 1);
              this.index=this.index - 1;
            }
          }
          
          // remove last item from tabAllConditions
          this.tabAllConditions.splice(this.tabAllConditions.length - 1, 1);
        }
        ///////////////////////// remove last item from tabAllConditions and tableToJoin -END ////////

       
        this.arrayToDisplayQuery.splice(this.arrayToDisplayQuery.length - 1, 1);
        this.arrayToExecuteQuery.splice(this.arrayToExecuteQuery.length - 1, 1);


        /**                        this.arrayToDisplayQuery.refresh();
         this.arrayToExecuteQuery.refresh();
         **/                        this.queryToDisplay = '';
        this.queryToExecute = '';

       
        for (let i = 0; i < this.arrayToDisplayQuery.length; i++)
        {
          this.queryToDisplay=this.queryToDisplay + this.arrayToDisplayQuery[i];
          this.queryToExecute=this.queryToExecute + this.arrayToExecuteQuery[i];
        }
        //////////
        this.queryToDisplay2=this.queryToExecute;
        
        // test after removing an item from arrayToDisplayQuery
        if (this.arrayToDisplayQuery.length > 0)
        {
        
          this.undoButton.enabled=true;
          this.undoButton.buttonMode=true;
          this.resetButton.enabled=true;
          this.resetButton.buttonMode=true;

       

          if (this.arrayToExecuteQuery[this.arrayToExecuteQuery.length - 1] == ' and' || this.arrayToExecuteQuery[this.arrayToExecuteQuery.length - 1] == ' or')
          {
          
            this.okButton.enabled = false;
            this.okButton.buttonMode = false;
            if (this.recUnitGroupDisplay == 'T')
              this.execStatus ='N';
            this.listCriteria.enabled=true;
            this.leftParentheseButton.enabled=true;
            this.leftParentheseButton.buttonMode=true;
            this.rightParentheseButton.enabled = false;
            this.rightParentheseButton.buttonMode = false;
            this.andButton.enabled=true;
            this.andButton.buttonMode=true;
            this.orButton.enabled=true;
            this.orButton.buttonMode=true;
          }
          else
          {
           
            let lastAdd: string=String(this.arrayToDisplayQuery[this.arrayToDisplayQuery.length - 1]);
            let endWith: string=lastAdd.substr(lastAdd.length - 1, lastAdd.length);
           
            if (endWith == ']' || endWith == ')')
            {
             
              this.listCriteria.enabled = false;
              this.leftParentheseButton.enabled = false;
              this.leftParentheseButton.buttonMode = false;
              this.andButton.enabled=true;
              this.andButton.buttonMode=true;
              this.orButton.enabled=true;
              this.orButton.buttonMode=true;
              if (this.countRightP % this.countLeftP == 0)
              {
              
                this.okButton.enabled=true;
                this.okButton.buttonMode=true;
                if (this.recUnitGroupDisplay == 'T')
                  this.execStatus ='Y';
                this.rightParentheseButton.enabled = false;
                this.rightParentheseButton.buttonMode = false;
              }
              else
              {
               
                this.okButton.enabled = false;
                this.okButton.buttonMode = false;
                if (this.recUnitGroupDisplay == 'T')
                  this.execStatus ='N';
                this.saveButton.enabled = false;
                this.saveButton.buttonMode = false;
                this.rightParentheseButton.enabled=true;
                this.rightParentheseButton.buttonMode=true;
              }
            }
            else if (endWith == '(')
            {
            
              this.okButton.enabled = false;
              this.okButton.buttonMode = false;
              if (this.recUnitGroupDisplay == 'T')
                this.execStatus ='N';
              this.saveButton.enabled = false;
              this.saveButton.buttonMode = false;
              this.listCriteria.enabled=true;
              this.leftParentheseButton.enabled=true;
              this.leftParentheseButton.buttonMode=true;
              this.rightParentheseButton.enabled = false;
              this.rightParentheseButton.buttonMode = false;
              this.andButton.enabled = false;
              this.andButton.buttonMode = false;
              this.orButton.enabled = false;
              this.orButton.buttonMode = false;
            }
            else
            {
            
              this.okButton.enabled = false;
              this.okButton.buttonMode = false;
              if (this.recUnitGroupDisplay == 'T')
                this.execStatus ='N';
              this.saveButton.enabled = false;
              this.saveButton.buttonMode = false;
              this.listCriteria.enabled = false;
              this.leftParentheseButton.enabled = false;
              this.leftParentheseButton.buttonMode = false;
              this.rightParentheseButton.enabled = false;
              this.rightParentheseButton.buttonMode = false;
              this.andButton.enabled = false;
              this.andButton.buttonMode = false;
              this.orButton.enabled = false;
              this.orButton.buttonMode = false;
            }
          }
          
        }
        else
        {
         
          this.undoButton.enabled = false;
          this.undoButton.buttonMode = false;
          this.resetButton.enabled = false;
          this.resetButton.buttonMode = false;
          this.listCriteria.enabled=true;
          this.andButton.enabled = false;
          this.andButton.buttonMode = false;
          this.orButton.enabled = false;
          this.orButton.buttonMode = false;
          this.okButton.enabled = false;
          this.okButton.buttonMode = false;
          if (this.recUnitGroupDisplay == 'T')
            this.execStatus ='N';
          this.saveButton.enabled = false;
          this.saveButton.buttonMode = false;
          this.leftParentheseButton.enabled=true;
          this.leftParentheseButton.buttonMode=true;
          this.rightParentheseButton.enabled = false;
          this.rightParentheseButton.buttonMode = false;
        }
      }
    }
    catch ( error ) {
      this.logger.error( 'method [previous] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'previous', this.errorLocation );
    }
    this.logger.info( 'method [previous] - END ' );

  }

  /**
   * clearContraints()
   *
   * Initialize for new constraint
   **/
  private clearContraints():void
  {
   
    
    try
    {
      this.logger.info('method [init] - START ');
      
      //andOrString = '';
      this.propertiesLabel.text = '';
      this.operator.text = '';
      this.operation = '';
      this.operationToDisplay = '';
//                    this.listCriteria.selectedItem = null;
      this.listCriteria.selectedIndex=-1;
      this.equalButton.enabled = false;
      this.equalButton.buttonMode = false;
      this.differentButton.enabled = false;
      this.differentButton.buttonMode = false;
      this.higherButton.enabled = false;
      this.higherButton.buttonMode = false;
      this.higherequalButton.enabled = false;
      this.higherequalButton.buttonMode = false;
      this.lowerButton.enabled = false;
      this.lowerButton.buttonMode = false;
      this.lowerequalButton.enabled = false;
      this.lowerequalButton.buttonMode = false;
      this.likeButton.enabled = false;
      this.likeButton.buttonMode = false;
      this.notLikeButton.enabled = false;
      this.notLikeButton.buttonMode = false;
      this.inButton.enabled = false;
      this.inButton.buttonMode = false;
      this.notinButton.enabled = false;
      this.notinButton.buttonMode = false;
      this.betweenButton.enabled = false;
      this.betweenButton.buttonMode = false;
      this.andButton.enabled=true;
      this.andButton.buttonMode=true;
      this.orButton.enabled=true;
      this.orButton.buttonMode=true;
      this.addButton.enabled = false;
      this.addButton.buttonMode = false;
      this.inListButton.enabled = false;
      this.inListButton.buttonMode = false;
      this.changeButton.buttonMode = false;
      this.changeButton.enabled = false;
      this.changeCritButton.enabled = false;
      

      if (this.hBoxContainer.contains(this.listValuesButton))
      //listValuesButton.removeEventListener('click', addListValuesEventHandler, false);
        this.listValuesButton.click = () => {
          this.addListValuesEventHandler(event);
        };
      
      if (this.hBoxContainer.contains(this.dateField))
      {
       // this.dateField.removeEventListener('close', this.datePickerOff, false);
      }
      
      if (this.hBoxContainer.contains(this.dateField2))
      {
        
        //this.dateField2.removeEventListener('open', datePickerOn, false);
        this.dateField2.open = () => {
          this.datePickerOn(event);
        };
      }
      

      this.hBoxContainer.removeAllChildren();

      
      /*if(this.vBoxContainer.contains(this.hBoxContainer2))
                    {
                        
                        this.hBoxContainer2.removeAllChildren();
                        this.vBoxContainer.removeChild(this.hBoxContainer2);
                    }*/
    }
    catch ( error ) {
      this.logger.error( 'method [popupClosedEventHandler] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'popupClosedEventHandler', this.errorLocation );
    }
    this.logger.info( 'method [popupClosedEventHandler] - END ' );
  }

  /**
   * reset()
   *
   * clear query and initialize screen
   **/
  public reset(event):void
  {
   
    try
    {
      this.logger.info('method [reset] - START ');
      
      if (this.screenName == 'add') {
        this.queryToExecute = '';
        this.queryToDisplay = '';
        this.queryToDisplay2 = '';
      } else {
        if(this.parentDocument) {
          this.queryToExecute = this.parentDocument.searchQuery;
          this.queryToDisplay =  this.parentDocument.queryToDisplay;
        }
      }
      this.labelQuery.text="";
      this.originalJSONList = this.firstJSONList;
      for (let key in this.firstJSONList) {
        if(this.firstJSONList[key].toChange != undefined) {
          this.firstJSONList[key].toChange = 'Y';
        }
      }
      this.gridJSONList =  this.firstJSONList;
      
      
      this.countLeftP=1;
      this.countRightP=1;
      this.okButton.enabled= false;
      this.okButton.buttonMode= false;
      this.undoButton.enabled = false;
      this.undoButton.buttonMode = false;
      this.resetButton.enabled = false;
      this.resetButton.buttonMode = false;
      this.addButton.enabled = false;
      this.addButton.buttonMode = false;
      this.listCriteria.enabled=true;
      this.arrayToDisplayQuery = [];
      this.arrayToExecuteQuery = [];
      this.tabAllConditions = [];
      this.tableToJoin = [];
      this.index=0;
      this.andOrString = '';
      this.andOrStringToDisplay = '';
      this.clearContraints();
      this.andButton.enabled = false;
      this.andButton.buttonMode = false;
      this.orButton.enabled = false;
      this.orButton.buttonMode = false;
      this.leftParentheseButton.enabled=true;
      this.leftParentheseButton.buttonMode=true;

      // this.listCriteria.dataProvider= this.gridJSONList;

      this.listCriteria.gridData = this.firstGridData;
      this.tabAllConditions = [] ;
      this.previousCondId=0;
      this.previousTypeCode="";

      this.filtringGrid('');


      if(window.opener && window.opener.instanceElement) {
        let parentParams;
        parentParams  = window.opener.instanceElement.getParamsFromParent();
      
        this.screenName = parentParams[0].screenName;
        if(parentParams[0].tabAllConditions && parentParams[0].tabAllConditions.length>0) {
          this.tabAllConditions = JSON.parse(parentParams[0].tabAllConditions);
        }

        if(parentParams[0].tableToJoin && parentParams[0].tableToJoin.length>0) {
          this.tableToJoin = JSON.parse(parentParams[0].tableToJoin);
        }

        if(parentParams[0].queryToExecute) {
          this.queryToExecute = parentParams[0].queryToExecute;
        }
        if(parentParams[0].queryToDisplay) {
          this.queryToDisplay = parentParams[0].queryToDisplay;
        }

      }

    }
    catch ( error ) {
      console.log('error resttt',error);
    }

  }



  /**
   * executeQuery()
   *
   * @params event:Event
   *
   * This method get the search criteria composed by the user
   * and send result to parent document and close current screen.
   *
   **/
  public executeQuery(event):void {
    let columnCode: string;
    let currentTableName: string;
    // refresh requestParams
    
    try {

      
     
      if (this.queryToExecute != "") {
       
        if (this.screenName == 'change') {
          
          this.tableToJoin = [];
          let indexTable = 0;

          for (let g =0; g < this.gridJSONList.size; g++) {

           
            let alias: string;
            if (this.gridJSONList.row[g].label.code !== undefined) {
              columnCode = this.gridJSONList.row[g].label.code;
            }
            let findTableName: boolean = false;
            if (this.gridJSONList.row[g].label.tableName !== undefined) {
              currentTableName = this.gridJSONList.row[g].label.tableName;
            }
            if (columnCode.indexOf('.') >= 0) {
              for (let co = 0; co < this.tableToJoin.length; co++) {
               
                if (String(this.tableToJoin[co]).indexOf(currentTableName + " ") >= 0) {
                  findTableName = true;
                  break;
                }
              }
             
              if (!findTableName) {
                alias = columnCode.substr(0, columnCode.indexOf('.'));
               
                this.tableToJoin[indexTable] = currentTableName + " " + alias;
                indexTable++;
              }
            } else {
             
              for (let pro = 0; pro < this.tableToJoin.length; pro++) {
               
                if (String(this.tableToJoin[pro]).indexOf(this.tabName) >= 0) {
                  findTableName = true;
                  break;
                }
              }
             
              if (!findTableName) {
              
                // add table to join to the array tableToJoin
                this.tableToJoin[indexTable] = this.tabName;
                indexTable++;
              }
            }
          }
          // add profiles (code and values) and binding variables to the agregation table
           this.agregationFunc = [];
          for (let f = 0; f < this.tabAllConditions.length; f++) {
            if (this.tabAllConditions[f].profileField == 'Y') {
              this.agregationFunc.push(this.tabAllConditions[f].columnToStore);
            }
            if (this.tabAllConditions[f].enumProfile == 'Y' || this.tabAllConditions[f].enumProfile == 'B') {
              this.agregationFunc.push(this.tabAllConditions[f].columnCodeValue);
            }
          }
          for (let ff = 0; ff < this.gridJSONList.length; ff++) {
            if (this.gridJSONList[ff].slickgrid_rowcontent.label.profileField == 'Y') {
              this.agregationFunc.push(this.gridJSONList[ff].slickgrid_rowcontent.label.code);
            }
            if (this.gridJSONList[ff].slickgrid_rowcontent.label.profileFieldValue == 'Y') {
              this.agregationFunc.push(this.gridJSONList[ff].slickgrid_rowcontent.label.fieldValue);
            }
            if (this.gridJSONList[ff].slickgrid_rowcontent.label.profileFieldValue == 'B') {
              this.agregationFunc.push(this.gridJSONList[ff].slickgrid_rowcontent.label.fieldValue);
            }
          }
        
          if (this.queryToExecute.indexOf(' where (') >= 0) {
            this.queryToExecute = this.queryToExecute.substr(this.queryToExecute.indexOf(" where ") + 8, this.queryToExecute.length);
          } else {
            this.queryToExecute = this.queryToExecute.substr(this.queryToExecute.indexOf(" where ") + 7, this.queryToExecute.length);
          }
          this.queryToExecute = this.queryToExecute.replace("M.message_id = T.message_id and T.client_id = C.client_id and (", "");
          this.queryToExecute = this.queryToExecute.replace("M.message_id = T.message_id and (", "");
          this.queryToExecute = this.queryToExecute.replace("T.client_id = C.client_id and (", "");
          this.queryToExecute = this.queryToExecute.replace("M.message_id = T.message_id and T.client_id = C.client_id and (", "");
          this.queryToExecute = this.queryToExecute.replace("T.client_id = C.client_id and (", "");
          this.queryToExecute = this.queryToExecute.substr(0, this.queryToExecute.length - 1);

        }

        this.tabNames = this.tableToJoin[0];
        for (let j = 1; j < this.tableToJoin.length; j++) {
       
          this.tabNames = this.tabNames + ", " + this.tableToJoin[j];
        }
      
        let temp: string = this.queryToExecute;
        this.queryToExecute = "select count(*) from " + this.tabNames + " where (" + temp + ")";
       
        let queryToTest: string = this.queryToExecute;
        queryToTest = queryToTest.replace(" :prate ", " 1 ");
        if (this.externalFields == 'Y') {
         
          if (this.agregationFunc.length != 0) {
           
            let findCond: boolean = false;
            for (let i = 0; i < this.agregationFunc.length; i++) {
             
              findCond = false;
              for (let k = 0; k < this.tabAllConditions.length; k++) {
               
                if (this.tabAllConditions[k].columnToStore == this.agregationFunc[i]) {
                  findCond = true;
                  if (this.tabAllConditions[k].typeCode == "DATE") {
                   
                    queryToTest = queryToTest.replace(this.agregationFunc[i], " SYSDATE ");
                    this.queryToExecute = this.queryToExecute.replace("TRUNC(" + this.agregationFunc[i] + ")", " TO_DATE(" + this.agregationFunc[i] + ", '" + this.commonSysDateFormat + "')");
                  }
                  else {
                   
                    queryToTest = queryToTest.replace(this.agregationFunc[i], " 1 ");
                  }
                }
                if (this.tabAllConditions[k].columnCodeValue == this.agregationFunc[i]) {
                  findCond = true;
                  
                  if (this.tabAllConditions[k].operation == "between") {
                   
                    queryToTest = queryToTest.replace(this.tabAllConditions[k].columnValue, " 1 ");
                    queryToTest = queryToTest.replace(this.tabAllConditions[k].columnValue2, " 1 ");
                  } else {
                  
                   // queryToTest = queryToTest.replace(this.agregationFunc[i], " 1 ");
                  }
                }
                if (!findCond && this.screenName == "change")
                queryToTest = queryToTest.replace(this.agregationFunc[i], " 1 ");
              }
            }
          }
        }
        if (this.varToBind.length != 0) {
          for (let p = 0; p < this.varToBind.length; p++) {
              queryToTest = queryToTest.replace(this.varToBind[p], " '1' ");
          }
        }
        
        this.queryToExecute = this.verifyQuery(this.queryToExecute);
        if(this instanceof TitleWindow)
            this.result= this.queryToDisplay;
        let encodedQuery : string = StringUtils.encode64(this.queryToExecute);
        queryToTest = this.verifyQuery(queryToTest);
        this.actionMethod = "method=validateQuery";
        this.actionPath = "expressionBuilderPCM.do?";
        this.requestParams["sQuery"] = encodedQuery;// this.queryToExecute; queryToTest; // "select count(*) from VW_A_CLIENTS_ACCOUNTS C where (C.OPENING_DATE >= TO_DATE('********', 'DDMMYYYY') and C.OPENING_DATE < TO_DATE('********', 'DDMMYYYY')" + '+1)'; //"select count(*) from A_TRANSACTION T where (T.ACCOUNT_ID = '100')";
        this.inputData.cbResult = (event) => {
          this.validateQueryResult(event);
        };
        this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
        this.inputData.encodeURL = true;
        this.inputData.send(this.requestParams);

        //  window.close();
      } else {
        if(this.parentDocument) {
          this.parentDocument.searchQuery = "";
        }else {
          if(window.opener && window.opener.instanceElement )
            window.opener.instanceElement.saveRuleDetails([], [] , '', '' );
        }
      }
    } catch (error) {
    console.log("error in execu query", error);
//    SwtUtil.logError(error, ".mxml", "executeQuery", this.errorLocation);
  }
}
setParamsFromParents(): void {
    this.screenName = "add";
    alert(this.screenName );
}



  /**
   * verifyQuery
   *
   *@param : queryToVerify:String
   *
   * @return String
   *
   * This function is used to verify the queryToExecute
   */
  private  verifyQuery(queryToVerify: string): string
  {
   
  
    let expParenthese: RegExp = /\(  \)/ig; //delete parenthese that appears as "( )"
    try
    {
      queryToVerify = queryToVerify.replace(expParenthese, "");
    } catch (error) {
      console.log("error in verifyQuery", error)
      // SwtUtil.logError(error, ".mxml", "verifyQuery", this.errorLocation);
    }
    return queryToVerify;
  }
  /**
   * validateQueryResult
   *
   * @param event: ResultEvent
   *
   * This is a callback method, to handle result event
   */
  private  validateQueryResult(event):void
  {
   
  
    try {
      
      //If the service is busy then remove the busy cursor
      if (this.inputData.isBusy())
      {
        this.inputData.cbStop();
      } else {
        
        let jsonToValidateQuery: JSONReader = new JSONReader();
        //Parse result xml
        jsonToValidateQuery.setInputJSON(event);
        //
        //If the result status is true, then load the grid
        if (jsonToValidateQuery.getRequestReplyStatus())
        {
          
          try {
            if (this.parentDocument) {
              this.parentDocument.searchQuery = this.queryToExecute;
              this.parentDocument.queryToDisplay = this.queryToDisplay;
              this.parentDocument.tableToJoin = [];

              for (let i = 0; i < this.tableToJoin.length; i++) {
                this.parentDocument.tableToJoin[i] = this.tableToJoin[i];
              }

              for (let j = 0; j < this.tabAllConditions.length; j++) {
                this.parentDocument.tabAllConditions[j] = this.tabAllConditions[j];
              }
            }
          } catch (error) {
            console.log("the variable tableToJoin is undefined");
          }



          if(window.opener && window.opener.instanceElement) {
            for (let j=0; j < this.tabAllConditions.length; j++)
            {
              if(!this.tabAllConditions[j].conditionId) {
                this.tabAllConditions[j].conditionId = (j+1);
              }
            }
            this.tabAllConditions.sort(this.compare);

            window.opener.instanceElement.saveRuleDetails(JSON.stringify(this.tabAllConditions), JSON.stringify(this.tableToJoin) , this.queryToDisplay, this.queryToExecute);
          }
          if(this.titleWindow){
            this.close();
          }else {
            window.close();
          }
        } else {
          
          this.swtAlert.warning(SwtUtil.getPredictMessage('queryBuilderScreen.alert.invalidQuery') +' '+jsonToValidateQuery.getRequestReplyMessage());
          jsonToValidateQuery = null;
          this.queryToExecute = this.queryToExecute.substr(this.queryToExecute.indexOf(" where ") + 7, this.queryToExecute.length);
        }
      }
    } catch (error) {
      console.log('error in vqlidatio', error);
      //SwtUtil.logError(error,"validateQueryResult", this.errorLocation);
    }
  }

  
  compare(a, b){
    if (a.conditionId > b.conditionId) return 1;
    if (b.conditionId > a.conditionId) return -1;

    return 0;
  }
  /**
   * addListValuesEventHandler
   *
   * param event: Event
   *
   * Method to open list values screen
   */
  private addListValuesEventHandler(event): void
  {
   
    
    try
    {
      this.logger.info('method [addListValuesEventHandler] - START ');
      
      //Currently focused property name
      const focusObj: any = Object(focusManager.getFocus());

      if (typeof focusObj.id === 'string') {
        this.buttonId = focusObj.id;
      } else if (focusObj.id && typeof focusObj.id.id === 'string') {
        this.buttonId = focusObj.id.id;
      } else {
        this.buttonId = ''; // or some default value / fallback handling
      }
      this.selectedItemsList = null;
      this.listValuesButton.enabled = false;
      let columnLabel: string=this.listCriteria.selectedItem.label.label;
      let columnCode: string=this.listCriteria.selectedItem.label.code;
      
      this.moduleReadyEventHandler(event);
    /*  SwtPopUpManager.addPopUp(this, ListValues, {title: this.listCriteria.selectedItem.label.label,
        operation: this.operation,
        columnLabel: columnLabel,
        columnCode: columnCode}, true)
        .subscribe( res => this.popupClosedEventHandler(res));*/

      this.win =  SwtPopUpManager.createPopUp(this, ListValues, {title: this.listCriteria.selectedItem.label.label,
        operation: this.operation,
        columnLabel: columnLabel,
        columnCode: columnCode});
      this.win.enableResize = false;
      this.win.id = "listValuesPopup";
      this.win.width = '500';
      this.win.height = '500';
      this.win.showControls = true;
      this.win.onClose.subscribe((res) => {
        this.popupClosedEventHandler(res)
      });
      this.win.display();


    }
    catch ( error ) {
      this.logger.error( 'method [addListValuesEventHandler] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'addListValuesEventHandler', this.errorLocation );
    }
    this.logger.info( 'method [addListValuesEventHandler] - END ' );
  }


  /**
   * moduleReadyEventHandler
   *
   * @param event: ModuleEvent
   *
   * This method is used to load list values screen as a popup, once it is ready to load.
   */
  private moduleReadyEventHandler(event):void
  {
   
    

    try
    {
      this.logger.info('method [moduleReadyEventHandler] - START ');
      
      //this.okButton.enabled = false;
      this.cancelButton.enabled = false;
      if (this.buttonId == 'b1')
        this.listValuesButton.enabled = false;
      this.actionToDo = '';

    }
    catch ( error ) {

    }
  }


  /**
   * popupClosedEventHandler
   *
   * @param event: Event
   *
   * This function is used to handle the child window close event
   */
  private  popupClosedEventHandler(event):void
  {
   
   
    let selectedIndex: number = -1;
    let arrayToStore = [];
    let listToDelete = [];
    try
    {
      
      //okButton.enabled=true;
    //  this.cancelButton.enabled=true;
      if (this.selectedItemsList != "" && event != undefined)
      {
        let eventString: string;

        if(this.buttonId == "changeCritButton"  && event.buttonClicked == "okButton") //&& (eventString == "okButton" || eventString == "listValues"))
        {
          
          for (let key in this.originalJSONList) {
            if(this.originalJSONList[key].code != undefined && this.originalJSONList[key].code == this.listCriteria.selectedItem.label.code) {
               if(this.originalJSONList[key].toChange != undefined) {
                 this.originalJSONList[key].toChange = 'N';
                 listToDelete = this.originalJSONList[key];
               }
            }
             if(this.originalJSONList[key].code != undefined && this.originalJSONList[key].code == this.selectedItemsList ) {
               this.originalJSONList[key].toChange = 'Y';
               this.originalJSONList[key].fieldValue = this.listCriteria.selectedItem.label.fieldValue;
               this.originalJSONList[key].operatorId = this.listCriteria.selectedItem.label.operatorId;
               this.originalJSONList[key].conditionId = this.listCriteria.selectedItem.label.conditionId;
               this.originalJSONList[key].condFieldName = this.listCriteria.selectedItem.label.condFieldName;
               this.originalJSONList[key].nextCondition = this.listCriteria.selectedItem.label.nextCondition;
               this.originalJSONList[key].profileFieldValue = this.listCriteria.selectedItem.label.profileFieldValue;
               this.originalJSONList[key].clause = this.listCriteria.selectedItem.label.clause;
             }
             if (this.originalJSONList[key].toChange != undefined && this.originalJSONList[key].toChange == 'Y') {
               arrayToStore.push({'label': this.originalJSONList[key]});
             }
          }
          this.gridJSONList = {row: arrayToStore, size: arrayToStore.length};
          this.listCriteria.gridData = this.gridJSONList;
          /*for (let i = 0; i< this.listCriteria.dataProvider.length; i++) {
            if (this.listCriteria.dataProvider[i].label.code != undefined && this.listCriteria.dataProvider[i].label.code == this.selectedItemsList && this.listCriteria.dataProvider[i].label.conditionId == this.previousCondId)
            {
              selectedIndex= i;
            }
          }
          this.listCriteria.selectedItem = this.gridJSONList.row[selectedIndex] 
          */
          this.listCriteria.selectedIndex = -1 ; //this.listCriteria.dataProvider[selectedIndex];
          
          this.selectCriteria();
        }
        else if (this.operation == "in" || this.operation == "not in")
        {
          
          this.textInput.text = this.selectedItemsList;

        }
        else if (this.operation == "between")
        {
          
          if (this.buttonId == "b1")
          {
            this.textInput.text = this.selectedItemsList;
          }
        }
       // this.listValuesButton.enabled = true;
      }
      
    }
    catch (error)
    {
      console.log("error in pop upclose", error);
     // SwtUtil.logError(error, moduleId, getQualifiedClassName(this) + ".mxml", "popupClosedEventHandler", this.errorLocation);
    }

  }



  /**
   * datePickerOn
   *
   * @param event:DropdownEvent
   *
   * This function is used to get the date when the date picker is open
   */
  private datePickerOn(event):void
  {
   
    
    try
    {
      this.logger.info('method [datePickerOn] - START ');
      // If focus is on from date chooser then get the date
      if (this.dateField.id == 'frmDateChooser')
      {
        this.dateField.formatString=this.sysdateformat;
      }
       if (this.dateField2.id == 'toDateChooser')
      {
        this.dateField2.formatString=this.sysdateformat;
        let lastFrom:Date=this.dateField.parseDate(this.dateField.text, this.sysdateformat);
        // set start date (date selected in from date) and end date (today)
        this.dateField2.selectableRange={rangeStart: lastFrom, rangeEnd: new Date()};
      }
    }
    catch ( error ) {
      this.logger.error( 'method [datePickerOn] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'datePickerOn', this.errorLocation );
    }
    this.logger.info( 'method [datePickerOn] - END ' );
  }

  /**
   * datePickerOff
   *
   * @param event:DropdownEvent
   *
   * This function is used to get the date when the date picker is closed
   */
  private datePickerOff(event):void
  {
   
    
    try
    {
      this.logger.info('method [datePickerOff] - START ');
      const focusObj: any = Object(focusManager.getFocus());
      let focusId: string = '';
      
      if (typeof focusObj.id === 'string') {
        focusId = focusObj.id;
      } else if (focusObj.id && typeof focusObj.id.id === 'string') {
        focusId = focusObj.id.id;
      }
      
      // Now use focusId safely
      if (focusId === 'frmDateChooser') {
        // this.dateField.text = this.getAbbreviationFromDate(this.sysdateformat, this.dateField.text);
        if (this.operation !== 'between') {
          this.sysDateActive = false;
          this.sysDateButton.setStyle('color', 'black');
        }
      } else if (focusId === 'toDateChooser') 
      {
        
        //this.dateField2.text=this.getAbbreviationFromDate(this.sysdateformat, this.dateField2.dropdown.selectedDate);
        this.sysDateActive = false;
        this.sysDateButton.setStyle('color', 'black');
      }
    }
    catch ( error ) {
      this.logger.error( 'method [datePickerOff] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'datePickerOff', this.errorLocation );
    }
    this.logger.info( 'method [datePickerOff] - END ' );
  }


  /**
   * verifyTextInput
   *
   *@param : columnValue: string
   *@param : typeCode: string
   *
   * @return String
   *
   * This function is used to verify the entred text in inputText
   */
  private verifyTextInput( columnValue: string, typeCode: string ): string {
   
   

    let expCommaDotBegin: RegExp = /^[,.]/ig; //delete comma or dot that appears in the begining of the string
    let expComma: RegExp = /,{2,}/ig; //delete comma that appears more than once
    let expDot: RegExp = /\.{2,}/ig; //delete dot that appears more than once
    let expCommaOrDotEnd: RegExp = /[,.]$/ig; //delete comma or dot that appears in the end of the string
    //let expCommaAndDotEnd:RegExp=/\.,$/ig; //delete comma and dot that appears in the end of the string
    let expCommaAndDot: RegExp = /,.{2,}/ig; //delete comma and dot that appears


    try {
      this.logger.info( 'method [verifyTextInput] - START ' );
     
      if ( columnValue == '' )
        return columnValue;
      if ( typeCode == 'NUM' ) {
        columnValue = columnValue.replace( expCommaDotBegin, '' );
        columnValue = columnValue.replace( expComma, ',' );
        columnValue = columnValue.replace( expDot, '.' );
        columnValue = columnValue.replace( expCommaOrDotEnd, '' );
        //columnValue=columnValue.replace(expCommaOrDotEnd, '');

       
        let value: number;

        if ( this.operation == 'in' || this.operation == 'not in' || this.operation == 'between' ) {
         
          let array = columnValue.split( ',' );

          for ( let i = 0; i < array.length; i++ ) {
           
            value = Number( array[i].toString() );

            if ( value.toString() == 'NaN' ) {
             
              this.textInput.setFocus();
              //SwtAlert.error("Please check entered value", SwtUtil.getCommonMessages('alert_header.error'));
              columnValue = 'false';
            }
          }
        }
        else {
         
          value = Number( this.getNumberFromAmount(columnValue) );

          if ( value.toString() == 'NaN' ) {
           
            this.textInput.setFocus();
            //SwtAlert.error("Please check entered value", SwtUtil.getCommonMessages('alert_header.error'));
            columnValue = 'false';
          }
        }
      }
      else if ( typeCode == 'CHAR' ) {
       
        columnValue = columnValue.replace( /^[,]/ig, '' );
        columnValue = columnValue.replace( /[,.]$/ig, '' );
      }
    }
    catch ( error ) {
      this.logger.error( 'method [verifyTextInput] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'verifyTextInput', this.errorLocation );
    }
    this.logger.info( 'method [verifyTextInput] - END ' );

    return columnValue;
  }


  /**
   * enableAllButton()
   *
   * Method used to set enable all button
   **/
  private enableAllButton():void
  {
   
    

    try
    {
      this.logger.info('method [enableAllButton] - START ');
      
      if (this.arrayToDisplayQuery.length > 0)
      {
        
        this.undoButton.enabled=true;
        this.resetButton.enabled=true;
        

        if (this.arrayToExecuteQuery[this.arrayToExecuteQuery.length - 1] == ' and' || this.arrayToExecuteQuery[this.arrayToExecuteQuery.length - 1] == ' or')
        {
          
          this.okButton.enabled = false;
          if (this.recUnitGroupDisplay == 'T')
            this.execStatus ='N';
          this.saveButton.enabled = false;
          this.listCriteria.enabled=true;
          this.leftParentheseButton.enabled=true;
          this.rightParentheseButton.enabled = false;
          this.andButton.enabled=true;
          this.orButton.enabled=true;
        }
        else
        {
          
          let lastAdd: string=String(this.arrayToDisplayQuery[this.arrayToDisplayQuery.length - 1]);
          let endWith: string=lastAdd.substr(lastAdd.length - 1, lastAdd.length);
          
          if (endWith == ']' || endWith == ')')
          {
            
            this.listCriteria.enabled = false;
            this.leftParentheseButton.enabled = false;
            this.andButton.enabled=true;
            this.orButton.enabled=true;
            if (this.countRightP % this.countLeftP == 0)
            {
              
              this.okButton.enabled=true;
              if (this.recUnitGroupDisplay == 'T')
                this.execStatus ='Y';
              this.saveButton.enabled=true;
              this.rightParentheseButton.enabled = false;
            }
            else
            {
              
              this.okButton.enabled = false;
              if (this.recUnitGroupDisplay == 'T')
                this.execStatus ='N';
              this.saveButton.enabled = false;
              this.rightParentheseButton.enabled=true;
            }
          }
          else if (endWith == '(')
          {
            
            this.okButton.enabled = false;
            if (this.recUnitGroupDisplay == 'T')
              this.execStatus ='N';
            this.saveButton.enabled = false;
            this.listCriteria.enabled=true;
            this.leftParentheseButton.enabled=true;
            this.rightParentheseButton.enabled = false;
            this.andButton.enabled = false;
            this.orButton.enabled = false;
          }
          else
          {
            
            this.okButton.enabled = false;
            if (this.recUnitGroupDisplay == 'T')
              this.execStatus ='N';
            this.saveButton.enabled = false;
            this.listCriteria.enabled = false;
            this.leftParentheseButton.enabled = false;
            this.rightParentheseButton.enabled = false;
            this.andButton.enabled = false;
            this.orButton.enabled = false;
          }
        }
        
      }
      else
      {
        
        this.undoButton.enabled = false;
        this.resetButton.enabled = false;
        this.listCriteria.enabled=true;
        this.andButton.enabled = false;
        this.orButton.enabled = false;
        this.okButton.enabled = false;
        if (this.recUnitGroupDisplay == 'T')
          this.execStatus ='N';
        this.saveButton.enabled = false;
        this.leftParentheseButton.enabled=true;
        this.rightParentheseButton.enabled = false;

      }
    }
    catch ( error ) {
      this.logger.error( 'method [enableAllButton] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'enableAllButton', this.errorLocation );
    }
    this.logger.info( 'method [enableAllButton] - END ' );
  }

  /**
   * disableAllButton()
   *
   * Method used to set disable all button
   **/
  private disableAllButton():void
  {

   
    

    try
    {
      this.logger.info('method [disableAllButton] - START ');
      

      this.equalButton.enabled = false;
      this.equalButton.buttonMode = false;
      this.differentButton.enabled = false;
      this.differentButton.buttonMode = false;
      this.likeButton.enabled = false;
      this.likeButton.buttonMode = false;
      this.notLikeButton.enabled = false;
      this.notLikeButton.buttonMode = false;
      this.betweenButton.enabled = false;
      this.betweenButton.buttonMode = false;
      this.higherButton.enabled = false;
      this.higherButton.buttonMode = false;
      this.higherequalButton.enabled = false;
      this.higherequalButton.buttonMode = false;
      this.inButton.enabled = false;
      this.inButton.buttonMode = false;
      this.andButton.enabled = false;
      this.andButton.buttonMode = false;
      this.lowerButton.enabled = false;
      this.lowerButton.buttonMode = false;
      this.lowerequalButton.enabled = false;
      this.lowerequalButton.buttonMode = false;
      this.notinButton.enabled = false;
      this.notinButton.buttonMode = false;
      this.inListButton.enabled = false;
      this.inListButton.buttonMode = false;
      this.orButton.enabled = false;
      this.orButton.buttonMode = false;
      this.leftParentheseButton.enabled = false;
      this.leftParentheseButton.buttonMode = false;
      this.rightParentheseButton.enabled = false;
      this.rightParentheseButton.buttonMode = false;
      this.undoButton.enabled = false;
      this.undoButton.buttonMode = false;
      
      this.addButton.enabled = false;
      this.addButton.buttonMode = false;
      this.okButton.enabled = false;
      this.okButton.buttonMode = false;
      if (this.recUnitGroupDisplay == 'T')
        this.execStatus ='N';
      this.cancelButton.enabled = false;
      
      this.cancelButton.buttonMode = false;
      this.saveButton.enabled = false;
      this.saveButton.buttonMode = false;
      this.resetButton.enabled = false;
      this.resetButton.buttonMode = false;
      this.listCriteria.enabled = false;
      this.helpIcon.enabled = false;
      this.helpIcon.buttonMode = false;

    }
    catch ( error ) {
      console.log('errorrr');
    }

  }

  /**
   * addSysDateEventHandler
   *
   * Method to active sysDateButton
   *
   * @param event - Event that occurs
   *
   * */
  private addSysDateEventHandler(event):void
  {   
    

    try
    {
      this.logger.info('method [addSysDateEventHandler] - START ');
      
      this.sysDateActive=true;
      this.sysDateButton.setStyle('color', '#4169E1');
      
      if (this.operation == 'between')
      {
        
       // this.dateField2.text=this.getAbbreviationFromDate(this.sysdateformat, new Date());
        this.dateField2.text = CommonUtil.formatDate(new Date(), this.sysdateformat.toUpperCase());
      }
      else
      {
        
        this.dateField.text=this.getAbbreviationFromDate(this.sysdateformat.toUpperCase(), new Date());
        this.dateField.text = CommonUtil.formatDate(new Date(), this.sysdateformat.toUpperCase());
      }
      
      if (!this.hBoxContainer.contains(this.tolerenceNumStep))
      {
        this.tolerenceNumStep=<SwtStepper> this.hBoxContainer.addChild(SwtStepper);
        //this.tolerenceNumStep.styleName='textbox';
        this.tolerenceNumStep.id='tolerenceNumStep';
        this.tolerenceNumStep.text='0';
        this.tolerenceNumStep.height = "28";
        this.tolerenceNumStep.width = '40';
        //*******  Changed limit as 100 year to fix the mantis 3928 (Default Value of NumStepper from 0 to 10)- Ezzeddine *******///
        this.tolerenceNumStep.minimum=-36525;
        this.tolerenceNumStep.maximum=36525;
        //*******  Changed limit as 100 year to fix the mantis 3928 (Default Value of NumStepper from 0 to 10)- Ezzeddine *******///
        this.tolerenceNumStep.keyDown = () => {
          this.keyDownEventHandler(event);
        };
      }
    }
    catch (error)
    {
      this.logger.error('method [addSysDateEventHandler] - error : ', error , '- errorLocation :',this.errorLocation);

      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this), 'addSysDateEventHandler', this.errorLocation);
    }
    this.logger.info('method [addSysDateEventHandler] - END ');
  }

  /**
   * getAbbreviationFromDate
   *
   * This function is used to get abbreviation from date when it is in DD-MMM-YYYY format
   **/
  private getAbbreviationFromDate(df, date:Date): string
  {
       
    let formatCurDate: string = '';
    let day:number;
    let month:number;
    let year:number;
    try
    {
      this.logger.info('method [getAbbreviationFromDate] - START ');
      
      if (this.sysdateformat == 'DD-MMM-YYYY')
      {
        
        day=date.getDate();
        month=date.getMonth();
        year=date.getFullYear();
        
        if (day >= 10)
        {
          
          formatCurDate=day + '-' + this.nameMonths[month].substr(0, 3) + '-' + year;
        }
        else
        {
          
          formatCurDate='0' + day + '-' + this.nameMonths[month].substr(0, 3) + '-' + year;
        }
      }
      else
      {
        formatCurDate = CommonUtil.formatDate(date, df);
      }
    }
    catch (error)
    {
      this.logger.error('method [getAbbreviationFromDate] - error : ', error , '- errorLocation :', this.errorLocation);

      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this), 'getAbbreviationFromDate', this.errorLocation);
    }
    this.logger.info('method [getAbbreviationFromDate] - END ');
    return formatCurDate;
  }

  /**
   * getOracleDateFormat
   *
   * Method to get oracle date format according to the current operator
   *
   * @param columnToStore
   * @param date1
   * @param date2
   * @param operator
   * @tolerance
   * @isIndexFunction
   *
   * */
  private getOracleDateFormat( columnToStore: string, date1: string, date2: string, operator: string, tolerence: string, isIndexFunction: boolean, objParam: any ): string {

    let oracleDate: string = '';

    try {
      this.logger.info( 'method [getOracleDateFormat] - START ');

      if ( operator == '' ) {
        oracleDate = columnToStore + ' ' + date1;
      }
      else {
        if ( !isIndexFunction ) {
          if ( operator == '>=' || operator == '<' ) {

            if ( date1.indexOf( 'SYSDATE' ) < 0 )
              oracleDate=columnToStore + " " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "')" + tolerence;
            else
              oracleDate=columnToStore + " " + operator + " TRUNC(" + date1 + ") " + tolerence;
          }
          else if ( operator == '<=' || operator == '>' ) {

            if ( operator == '>' )
              operator = '>=';
            else
              operator = '<';

            if ( date1.indexOf( 'SYSDATE' ) < 0 )
              oracleDate=columnToStore + " " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "') + 1" + tolerence;
            else
              oracleDate=columnToStore + " " + operator + " TRUNC(" + date1 + " + 1)" + tolerence;
          }
          else if ( operator == '=' ) {

            if ( date1.indexOf( 'SYSDATE' ) < 0 ) {
              oracleDate=columnToStore + " >= TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "')" + tolerence + " and " + columnToStore + " < TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "') + 1" + tolerence;
            }
            else
              oracleDate=columnToStore + " >= TRUNC(" + date1 + ") " + tolerence + " and " + columnToStore + " < TRUNC(" + date1 + " + 1)" + tolerence;
          }
          else if ( operator == '<>' ) {

            if ( date1.indexOf( 'SYSDATE' ) < 0 )
              oracleDate=columnToStore + " < TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "')" + tolerence + " or " + columnToStore + " >= TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "') + 1" + tolerence;
            else
              oracleDate=columnToStore + " < TRUNC(" + date1 + ") " + tolerence + " or " + columnToStore + " >= TRUNC(" + date1 + " + 1)" + tolerence;
          }
          else if ( operator == 'between' ) {

            if ( date2.indexOf( 'SYSDATE' ) < 0 )
              oracleDate=columnToStore + " >= TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "')" + tolerence + " and " + columnToStore + " < TO_DATE('" + date2 + "', '" + this.commonSysDateFormat + "') + 1" + tolerence;
            else
              oracleDate=columnToStore + " >= TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "')" + tolerence + " and " + columnToStore + " < TRUNC(" + date2 + " + 1)" + tolerence;
          } else {

            oracleDate = columnToStore + ' ' + operator + ' ' + date1;
          }
        } else {
          if ( operator == 'between' ) {

            if ( date2.indexOf( 'SYSDATE' ) < 0 )
              oracleDate="TRUNC(" + columnToStore + ") " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "')" + " and " + " TO_DATE('" + date2 + "', '" + this.commonSysDateFormat + "')" + tolerence;
            else
              oracleDate="TRUNC(" + columnToStore + ") " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "')" + " and " + " TRUNC(" + date2 + ")" + tolerence;
            columnToStore="TRUNC(" + columnToStore + ") ";

          }
          else if ( operator == '>=' || operator == '<' || operator == '<=' || operator == '>' || operator == '=' || operator == '<>' ) {

            if ( date1.indexOf( 'SYSDATE' ) < 0 )
              oracleDate="TRUNC(" + columnToStore + ") " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + "')" + tolerence;
            else
              oracleDate="TRUNC(" + columnToStore + ") " + operator + " TRUNC(" + date1 + ")" + tolerence;
            columnToStore="TRUNC(" + columnToStore + ") ";
          }
          else {

            oracleDate=columnToStore + " " + operator + " " + date1;
          }
        }
        objParam.columnToStore = columnToStore;
        objParam.columnValue = oracleDate;
      }
    }
    catch ( error ) {
      this.logger.error( 'method [getOracleDateFormat] - error : ', error, '- errorLocation :', this.errorLocation );

      // SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'getOracleDateFormat', this.errorLocation );
    }
    this.logger.info( 'method [getOracleDateFormat] - END ' );
    return oracleDate;
  } /**
   * getOracleDateFormat
   *
   * Method to get oracle date format according to the current operator
   *
   * @param columnToStore
   * @param date1
   * @param date2
   * @param operator
   * @tolerance
   * @isIndexFunction
   *
   * */
  private getOracleDateTimeFormat( columnToStore: string, date1: string, date2: string, operator: string, tolerence: string, isIndexFunction: boolean, objParam: any ): string {

    let oracleDate: string = '';

    try {
      this.logger.info( 'method [getOracleDateTimeFormat] - START ');
      if ( operator == '' ) {
        oracleDate = columnToStore + ' ' + date1;
      }
      else {
        if ( !isIndexFunction ) {
          if ( operator == '>=' || operator == '<' ) {

            if ( date1.indexOf( 'SYSDATE' ) < 0 )
              oracleDate=columnToStore + " " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "')" + tolerence;
            else
              oracleDate=columnToStore + " " + operator + " TRUNC(" + date1 + ") " + tolerence;
          }
          else if ( operator == '<=' || operator == '>' ) {

            if ( operator == '>' )
              operator = '>=';
            else
              operator = '<';

            if ( date1.indexOf( 'SYSDATE' ) < 0 )
              oracleDate=columnToStore + " " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "') + 1" + tolerence;
            else
              oracleDate=columnToStore + " " + operator + " TRUNC(" + date1 + " + 1)" + tolerence;
          }
          else if ( operator == '=' ) {

            if ( date1.indexOf( 'SYSDATE' ) < 0 ) {
              oracleDate=columnToStore + " >= TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "')" + tolerence + " and " + columnToStore + " < TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "') + 1" + tolerence;

            }
            else
              oracleDate=columnToStore + " >= TRUNC(" + date1 + ") " + tolerence + " and " + columnToStore + " < TRUNC(" + date1 + " + 1)" + tolerence;
          }
          else if ( operator == '<>' ) {

            if ( date1.indexOf( 'SYSDATE' ) < 0 )
              oracleDate=columnToStore + " < TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "')" + tolerence + " or " + columnToStore + " >= TO_DATE('" + date1 + "', '" + this.commonSysDateFormat  + " HH24:MI"+ "') + 1" + tolerence;
            else
              oracleDate=columnToStore + " < TRUNC(" + date1 + ") " + tolerence + " or " + columnToStore + " >= TRUNC(" + date1 + " + 1)" + tolerence;
          }
          else if ( operator == 'between' ) {

            if ( date2.indexOf( 'SYSDATE' ) < 0 )
              oracleDate=columnToStore + " >= TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "')" + tolerence + " and " + columnToStore + " < TO_DATE('" + date2 + "', '" + this.commonSysDateFormat + " HH24:MI" + "') + 1" + tolerence;
            else
              oracleDate=columnToStore + " >= TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "')" + tolerence + " and " + columnToStore + " < TRUNC(" + date2 + " + 1)" + tolerence;
          } else {

            oracleDate = columnToStore + ' ' + operator + ' ' + date1;
          }
        } else {
          if ( operator == 'between' ) {

            if ( date2.indexOf( 'SYSDATE' ) < 0 )
              oracleDate="TRUNC(" + columnToStore + ") " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "')" + " and " + " TO_DATE('" + date2 + "', '" + this.commonSysDateFormat  + " HH24:MI"+ "')" + tolerence;
            else
              oracleDate="TRUNC(" + columnToStore + ") " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "')" + " and " + " TRUNC(" + date2 + ")" + tolerence;
            columnToStore="TRUNC(" + columnToStore + ") ";

          }
          else if ( operator == '>=' || operator == '<' || operator == '<=' || operator == '>' || operator == '=' || operator == '<>' ) {

            if ( date1.indexOf( 'SYSDATE' ) < 0 )
              oracleDate="TRUNC(" + columnToStore + ") " + operator + " TO_DATE('" + date1 + "', '" + this.commonSysDateFormat + " HH24:MI" + "')" + tolerence;
            else
              oracleDate="TRUNC(" + columnToStore + ") " + operator + " TRUNC(" + date1 + ")" + tolerence;
            columnToStore="TRUNC(" + columnToStore + ") ";
          }
          else {

            oracleDate=columnToStore + " " + operator + " " + date1;
          }
        }
        objParam.columnToStore = columnToStore;
        objParam.columnValue = oracleDate;
      }
    }
    catch ( error ) {
      this.logger.error( 'method [getOracleDateFormat] - error : ', error, '- errorLocation :', this.errorLocation );

      // SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'getOracleDateFormat', this.errorLocation );
    }
    this.logger.info( 'method [getOracleDateTimeFormat] - END ' );
    return oracleDate;
  }

  /**
   * transformDate2
   *
   * @param date : Date
   *
   * transform date when sysdateformat = 'DD-MMM-YYYY'
   *
   **/
  private transformDate2(date:Date): string
  {
   
    
    let dateValue: string = '';
    let day:number;
    let month:number;
    let year:number;
    try
    {
      
      if (date != null)
      {
        day=date.getDate();
        month=date.getMonth() + 1;
        year=date.getFullYear();
        
        if (day >= 10)
        {
          
          if (month >= 10)
            dateValue=day + '/' + month + '/' + year;
          else
            dateValue=day + '/' + '0' + month + '/' + year;
        }
        else
        {
          
          if (month >= 10)
            dateValue='0' + day + '/' + month + '/' + year;
          else
            dateValue=day + '/' + '0' + month + '/' + year;
        }
      }
    }
    catch (error)
    {
      this.logger.error('method [transformDate2] - error : ', error , '- errorLocation :', this.errorLocation);
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName(this), 'transformDate2', this.errorLocation);
    }
    this.logger.info('method [transformDate2] - END ');
    return dateValue;
  }



  /**
   * comboChangeHandler
   *
   * @param event:ListEvent
   * This method is used to display textInput if the selected value in the combobox is TYPETEXT
   */
  private comboChangeHandler(event):void
  {
    // Variable to hold the error location
    
    try
    {
      this.logger.info('method [comboChangeHandler] - START ');
      if (this.comboBox.selectedItem.id == 'TYPETEXT' && !this.hBoxContainer.contains(this.textInput))
      {
        
        this.textInput=<SwtTextInput> this.hBoxContainer.addChild(SwtTextInput);
        this.textInput.width=200;
        //this.textInput.styleName='textbox';
        this.textInput.id='SwtTextInput';
        this.textInput.doubleClick = () => {
          this.openTextAreaInWindow(event);
        };
        if (this.comboBox.selectedItem.type == 'NUM' || this.comboBox.selectedItem.type == 'DECI' || this.comboBox.selectedItem.type == 'BIND_N') {
          this.textInput.restrict='0-9.\\-';
        }

        
      // this.hBoxContainer.addChild(this.textInput);
        
        this.textInput.enabled=true;
        this.textInput.setFocus();
      }
      else if (this.comboBox.selectedItem.id != 'TYPETEXT' && this.hBoxContainer.contains(this.textInput))
      {
        
        this.hBoxContainer.removeChild(this.textInput);
      }
    }
    catch ( error ) {
      this.logger.error( 'method [comboChangeHandler] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'comboChangeHandler', this.errorLocation );
    }
    this.logger.info( 'method [comboChangeHandler] - END ' );
  }


  /**
   * comboBetweenChangeHandle
   * @param event:ListEvent
   *
   * This method is used to display textInput if the selected value in the combobox is TYPETEXT
   */
  private comboBetweenChangeHandler(event): void
  {
    // Variable to hold the error location
    
    try
    {
      this.logger.info('method [comboBetweenChangeHandler] - START ');
      

      if (!this.vBoxContainer.contains(this.hBoxContainer2))
      {
        this.vBoxContainer.addChild(HBox);
      }
      if (this.comboBox != undefined && this.comboBox.selectedItem.id == 'TYPETEXT' && !this.hBoxContainer2.contains(this.textInput))
      {
        
        this.textInput = <SwtTextInput> this.hBoxContainer2.addChild(SwtTextInput);
        this.textInput.width='200';
        this.textInput.styleName='textbox';
        this.textInput.id='SwtTextInput';
        this.textInput.doubleClick = () => {
          this.openTextAreaInWindow(event);
        };
        if (this.comboBox != undefined && this.comboBox.selectedItem.type == 'NUM' || this.comboBox.selectedItem.type == 'DECI' || this.comboBox.selectedItem.type == 'BIND_N')
          this.textInput.restrict='0-9.\\-';
        
        this.hBoxContainer2.addChildAt(SwtTextInput, 0);
          
        this.textInput.enabled=true;
        this.textInput.setFocus();
        if (this.hBoxContainer2.contains(this.labelBetweenCombo))
          this.labelBetweenCombo.width= "50";
      }
      else if (this.comboBox != undefined && this.comboBox.selectedItem.id != 'TYPETEXT' && this.hBoxContainer2.contains(this.textInput))
      {
        
        this.hBoxContainer2.removeChild(this.textInput);
        if (this.hBoxContainer2.contains(this.textInput2))
          this.labelBetweenCombo.width= "308";
      }
      // Add an empty label between the 2 combobox
      if (!this.hBoxContainer2.contains(this.labelBetweenCombo))
      {
        
        this.labelBetweenCombo=<SwtLabel> this.hBoxContainer2.addChild(SwtLabel);
        this.labelBetweenCombo.styleName='label';
        if (this.hBoxContainer2.contains(this.textInput))
        {
          
          this.labelBetweenCombo.width= "50";
          this.hBoxContainer2.addChildAt(SwtComboBox, 1);
             }
        else
        {
          
          this.labelBetweenCombo.width= "308";
          this.hBoxContainer2.addChildAt(SwtComboBox, 0);
        }
      }
      
      if ( this.comboBox2 != undefined && this.comboBox2.selectedItem.id == 'TYPETEXT' && !this.hBoxContainer2.contains(this.textInput2))
      {        
        this.textInput2=<SwtTextInput> this.hBoxContainer2.addChild(SwtTextInput);
        this.textInput2.width='200';
        this.textInput2.styleName='textbox';
        this.textInput2.id='SwtTextInput2';
        this.textInput2.doubleClick = () => {
          this.openTextAreaInWindow(event);
        };

        if ( this.comboBox2 != undefined && this.comboBox2.selectedItem.type == 'NUM' || this.comboBox2.selectedItem.type == 'DECI' || this.comboBox2.selectedItem.type == 'BIND_N')
          this.textInput2.restrict='0-9.\\-';
        
        if (this.hBoxContainer2.contains(this.textInput))
        {
             this.hBoxContainer2.addChildAt(SwtTextInput, 2);
        } else {
         this.hBoxContainer2.addChildAt(SwtTextInput, 0); //this.hBoxContainer2.addChildAt(SwtTextInput, 1);
           this.labelBetweenCombo.width= "265";
        }
        
        this.textInput2.enabled=true;
        this.textInput2.setFocus();
      }
      else if (this.comboBox2 != undefined && this.comboBox2.selectedItem.id != 'TYPETEXT' && this.hBoxContainer2.contains(this.textInput2))
      {
       
        this.hBoxContainer2.removeChild(this.textInput2);
      }
      
      if (!this.hBoxContainer2.contains(this.textInput) && !this.hBoxContainer2.contains(this.textInput2))
      {
        this.hBoxContainer2.removeChild(this.labelBetweenCombo);
        this.vBoxContainer.removeChild(this.hBoxContainer2);
      }

    }
    catch ( error ) {
      console.log('error in combo change', error);
      this.logger.error( 'method [comboBetweenChangeHandler] - error : ', error, '- errorLocation :', this.errorLocation );

      SwtUtil.logError( error, SwtUtil.SYSTEM_MODULE_ID, this.commonService.getQualifiedClassName( this ), 'comboBetweenChangeHandler', this.errorLocation );
    }
    this.logger.info( 'method [comboBetweenChangeHandler] - END ' );
  }

  /**
   * filtringGrid()
   *
   * @param item: string
   *
   * Method used to filter the data from grid
   **/
  filtringGrid( search: string ): void {
    let event: Event;
    try {
      if ( this.arrayToDisplayQuery.length > 0 )
        this.clearContraints();
      // else
      //   this.reset(event);
      this.filterTextOfInput = search;
      this.updateFilter();

    }
    catch ( error ) {
      console.log('error', error);
    }
  }
  updateFilter() {
  try {
    this.listCriteria.dataviewObj.beginUpdate();
    this.listCriteria.dataviewObj.setItems(this.listCriteria.gridData);
    this.listCriteria.dataviewObj.setFilterArgs({
      searchString: this.filterTextOfInput
    });
    this.listCriteria.dataviewObj.setFilter(this.filterFunction);
    this.listCriteria.dataviewObj.endUpdate();

    this.listCriteria.selectedIndex = -1;
}catch (e) {
    console.log('errror', e)
  }

  }
  filterFunction(item, args) {
    if (item["label"].toLowerCase().indexOf(args.searchString.toLowerCase()) == -1) {
      return false;
    }
    return true;
  }


  private currencyPattern;

  validateAmount(): any {
    
    let currency = null;

    if (!(validateCurrencyPlaces(this.textInput, this.currencyPattern, null))) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.validAmount', null));
      return false;
    }
  }
  validateTime(textInput): any {
    if (textInput.text.endsWith(":")) {
      textInput.text = textInput.text + "00";
    }
    if (textInput.text && validateFormatTime(textInput) == false) {
      this.swtAlert.warning(SwtUtil.getPredictMessage('alert.validTime', null), null);
      textInput.text = "";
      return false;
    } else {
      textInput.text = textInput.text.substring(0, 5);
    }
  }
  validateTimes(textInput): any {
    let timeArray = [];
    let arraySplit = textInput.split(",");
    if (arraySplit.toString().endsWith(','))  {
      arraySplit = arraySplit.substr(0,arraySplit.length -1)
    }
    arraySplit.forEach(function(obj){
      timeArray.push(obj);
    });
    if(timeArray.length >= 1) {
      for (let i = 0; i < timeArray.length; i++) {
        let time = timeArray[i].toString().trim();
        if (time.endsWith(":")) {
          time = time + "00";
        }
        let timeMoment = moment(time, "HH:mm", true );
        if(!timeMoment.isValid()) {
          return "error_time";
        }  else {
        }
      }
    }
    return true;

  }
  validateDateTimeField(dateTime) : any{
    try{
      let dateTimeArray = [];
      let arraySplit = dateTime.split(",");
      if (arraySplit.toString().endsWith(','))  {
        arraySplit = arraySplit.substr(0,arraySplit.length -1)
      }
      arraySplit.forEach(function(obj){
        dateTimeArray.push(obj);
      });

      if(dateTimeArray.length >= 1) {
      for (let i =0; i< dateTimeArray.length ; i++) {
        let dateTime = dateTimeArray[i].toString().trim();
          let dateTimeM = moment(dateTime, this.sysdateformat.toUpperCase() + " HH:mm", true );
        if(!dateTimeM.isValid()) {
            //this.swtAlert.warning('Please Enter date time in the format '+ this.sysdateformat.toUpperCase() + ' HH: MM');
            return "error_dateTime";
          }  else {
        }
      }
    }
      return true;
    }catch(error){
      console.log('error',error);
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 'PCDashboard', ' validateDateTimeField', this.errorLocation);
    }

    return true;
  }
  validateDateField(dateField) : any{
    try{
      let date;
      if(dateField) {

        date = moment(dateField, this.sysdateformat.toUpperCase() , true);

        if(!date.isValid()) {
          this.swtAlert.warning(SwtUtil.getPredictMessage('queryBuilderScreen.alert.dateFormat', null)+ " "+ this.sysdateformat.toUpperCase());
          return "error_date";
        } else {
        }
      }
      return true;
    }catch(error){
      SwtUtil.logError(error, SwtUtil.SYSTEM_MODULE_ID, 'PCDashboard', ' validateDateField', this.errorLocation);
    }

    return true;
  }

  getNumberFromAmount(amountAsString){
    let result ;
    
    if (this.currencyPattern == "currencyPat2") {
      if(amountAsString)
      result = Number(amountAsString.replace(/\./g, '').replace(/,/g, '.'));
     } else if (this.currencyPattern == "currencyPat1") {
       if(amountAsString)
       result = Number(amountAsString.replace(/,/g, ''));
     }

     return result;
     
  }

  /**
   * openTextAreaInWindow()
   *
   * @param event:MouseEvent
   *
   * show the value of a textInput in an textArea
   **/
  private openTextAreaInWindow(event):void {

  }

  /**
   * closeTextAreaInWindow()
   *
   * @param event:MouseEvent
   *
   * listner to the textArea close event
   **/
  private closeTextAreaInWindow(event):void {
  }



  /**
   * getConditionById()
   *
   * used to get the bracket '[]' number i from the queryToDisplay
   **/
  private  getConditionById(selectedIndex: number): any
  {
   
  
    let conditions = [];
    let condition: string = null;
    let patternCondition:RegExp=/\[(.*?)\]/ig;

    try
    {
      
      conditions = this.queryToDisplay.match(patternCondition);
      
      condition = conditions[selectedIndex];

    } catch (error) {
      // SwtUtil.logError(error, moduleId, getQualifiedClassName(this) + ".mxml", "getConditionById", this.errorLocation);
    }
    return condition;
  }
  /**
   * replaceConditionToDisplayById()
   *
   * used to replace the changed condition that exist between brackets '[]' with the new one
   **/
  private  replaceConditionToDisplayById(selectedIndex: number, newCondition: string): any {
   
  
    let newQueryToDisplay: string=null;
    let patternCondition:RegExp = /\[(.*?)\]/ig;
    let indexBegin: number;
    let indexEnd: number;

    try
    {
  

      let conditions : any; //new Array();
      conditions  = patternCondition.exec(this.queryToDisplay);
      let i: number=0;

      while (conditions != null)
      {
        if (i == selectedIndex)
        {
         
          indexBegin= conditions.index;
          indexEnd=patternCondition.lastIndex;
          break;
        }
     
        conditions = patternCondition.exec(this.queryToDisplay);
        i++;
      }
      
      newQueryToDisplay= this.queryToDisplay.substring(0, indexBegin) + newCondition + this.queryToDisplay.substr(indexEnd, this.queryToDisplay.length);
    } catch (error) {
    //  SwtUtil.logError(error, moduleId, getQualifiedClassName(this) + ".mxml", "replaceConditionToDisplayById", this.errorLocation);
    }
    return newQueryToDisplay;
  }
  /**
   * replaceConditionToDisplayById()
   *
   * used to replace the changed condition of the execute query with the new one
   **/
  private  replaceConditionToExecuteById(selectedIndex: number, oldCondition: string, oldConditionWithoutSpaces: string, newCondition: string): string {
    
    let newQueryToExecute: string="";
    let occurenceNbr: number = 0;
    let indexBegin: number;
    let indexEnd: number;
    let fromOldWithSpaces : boolean = false;
    try {
      
      
      for (let i: number=0; i <= this.listCriteria.selectedIndex; i++) {
     
        if (this.listCriteria.selectedItem.label.content == this.listCriteria.dataProvider[i].label) {
          occurenceNbr++;
        }
      }
      indexBegin=this.nthIndexOf(this.queryToExecute, oldCondition + " ", occurenceNbr, 0);
      
      if (indexBegin < 0) {
        indexBegin = this.nthIndexOf(this.queryToExecute, oldCondition + ")", occurenceNbr, 0);
      }


      if (indexBegin < 0) {
        indexBegin=this.nthIndexOf(this.queryToExecute, oldConditionWithoutSpaces + " ", occurenceNbr, 0);
        fromOldWithSpaces = true;
      }


      if (indexBegin < 0) {
        indexBegin = this.nthIndexOf(this.queryToExecute, oldConditionWithoutSpaces + ")", occurenceNbr, 0);
        fromOldWithSpaces = true;
      }
      if(fromOldWithSpaces)
        indexEnd = indexBegin + oldConditionWithoutSpaces.length;
      else 
        indexEnd = indexBegin + oldCondition.length;
      
  
      if (indexBegin >= 0) {
        newQueryToExecute= this.queryToExecute.substring(0, indexBegin) + newCondition + this.queryToExecute.substr(indexEnd, this.queryToExecute.length);
      }


     
      if (newQueryToExecute == "")
       console.log( "createQuery: Matching between query and oldCondition is false, oldCondition: [" + oldCondition + "], query to be replaced: [" + this.queryToExecute + "], new Condition: [" + newQueryToExecute + "], new query to replace: [" + newCondition + "]" + this.errorLocation);

    } catch (error) {
      console.log('errror in replaceConditionToExecuteById', error)
     // SwtUtil.logError(error, moduleId, getQualifiedClassName(this) + ".mxml", "replaceConditionToDisplayById", this.errorLocation);
    }
    return newQueryToExecute;
  }
  /**
   * nthIndexOf()
   *
   * used to return the position of the nth occurence of a string
   **/
  private  nthIndexOf(searchFrom: string, searchElement: string, n: number, fromElement: number): number {
   
   
    try {
      
      n = n || 0;
      fromElement = fromElement || 0;
      while (n > 0)
      {
        
        fromElement= searchFrom.indexOf(searchElement, fromElement);
        if (fromElement < 0)
        {
          return -1;
        }
        --n;
        ++fromElement;
      }
    }
    catch (error)
    {
      console.log( 'error in nth index', error);
     // SwtUtil.logError(error, moduleId, getQualifiedClassName(this) + ".mxml", "nthIndexOf", this.errorLocation);
    }
    return fromElement - 1;
  }
  /**
   * changeCriteria()
   *
   * used to change the selected criteria
   **/
  public  changeCriteria():void {
    let childTitle: string;
    try {
      
      //Currently focussed property name
      this.selectedItemsList = null;
    
      if (String(this.listCriteria.selectedItem.label.label).length * 8 > 350) {
        childTitle=String(this.listCriteria.selectedItem.label.label).substr(0,45) + "...";
      } else {
        childTitle = this.listCriteria.selectedItem.label.label;
      }
   
    //  this.listValuesButton.enabled = false;
      let columnLabel: string = "Constraints"; //SwtUtil.getCommonMessages('searchScreen.label.constraints');
      let columnCode: string = "code";
      this.previousCondId = this.listCriteria.selectedItem.label.conditionId;
      this.previousTypeCode = this.listCriteria.selectedItem.label.typeCode;
      this.buttonId = "changeCritButton";
    /*  SwtPopUpManager.addPopUp(this, ListValues,
        {title: childTitle, dataSource: "fromGridData",
        columnLabel: columnLabel,
        columnCode: columnCode}, true)
        .subscribe( res => {
          console.log(res);
            this.popupClosedEventHandler(res);
          });*/


          this.win =  SwtPopUpManager.createPopUp(this, ListValues, {title: childTitle, dataSource: "fromGridData",
          columnLabel: columnLabel,
          columnCode: columnCode},);
          this.win.enableResize = false;
          this.win.id = "listValuesPopup";
          this.win.width = '500';
          this.win.height = '500';
          this.win.showControls = true;
          this.win.onClose.subscribe((res) => {
            this.popupClosedEventHandler(res)
          });
          this.win.display();

    } catch (error) {
      console.log('error in changeCriteria', error);
     // SwtUtil.logError(error, moduleId, getQualifiedClassName(this) + ".mxml", "changeCriteria", this.errorLocation);
    }
  }
  closePopUp(): void  {
    window.close()
     // SwtPopUpManager.getPopUpById(this.fromSource).close();
  }
}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: RulesDefinitionAddRule }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [RulesDefinitionAddRule],
  entryComponents: []
})
export class ExpressionBuilderModule { }

