(window.webpackJsonp=window.webpackJsonp||[]).push([[78],{hVKr:function(t,e,i){"use strict";i.r(e);var l=i("CcnG"),o=i("mrSG"),a=i("447K"),n=i("ZYCi"),s=function(t){function e(e,i){var l=t.call(this,i,e)||this;return l.commonService=e,l.element=i,l.jsonReader=new a.L,l.inputData=new a.G(l.commonService),l.saveData=new a.G(l.commonService),l.requestParams=[],l.baseURL=a.Wb.getBaseURL(),l.menuAccessId=null,l.templateId=null,l.userId=null,l.type=null,l.templateName=null,l.screenName=null,l.ordnialPos=null,l.shortName=null,l.modify=null,l.copyFromFlag=null,l.copiedTemplateId=null,l.copiedUserId=null,l.totalMultiplier=null,l.totalChanged=!1,l.columnId=null,l.shortNameOnLoad=null,l.typeId=null,l.selectedRowId=null,l.selectedEntityId=null,l.selectedId=null,l.selectedColumn=null,l.detailRowCount=0,window.Main=l,l.swtAlert=new a.bb(e),l}return o.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this,this.lblColumnType.text=a.Wb.getPredictMessage("label.forecastMonitorTemplateAddDetail.columnType",null),this.cbColumnType.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAddDetail.columnType",null),this.lblColumnNo.text=a.Wb.getPredictMessage("label.forecastMonitorTemplateAddDetail.columnId",null),this.txtColumnNo.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAddDetail.columnId",null),this.lblShortName.text=a.Wb.getPredictMessage("label.forecastMonitorTemplateAddDetail.shortName",null),this.txtShortName.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAddDetail.shortName",null),this.lblDesc.text=a.Wb.getPredictMessage("label.forecastMonitorTemplateAddDetail.description",null),this.txtDescription.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAddDetail.description",null),this.chbPublic.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAddDetail.contributeCheck",null),this.lblContribute.text=a.Wb.getPredictMessage("label.forecastMonitorTemplateAddDetail.contributeCheck",null),this.txtMulitiplier.toolTip=a.Wb.getPredictMessage("tooltip.forecastMonitorTemplateAddDetail.contributeText",null),this.suggestButton.label=a.Wb.getPredictMessage("button.forecastmonitor.suggest",null),this.addButton.label=a.Wb.getPredictMessage("button.add",null),this.deleteButton.label=a.Wb.getPredictMessage("button.delete",null),this.okButton.label=a.Wb.getPredictMessage("button.ok",null),this.closeButton.label=a.Wb.getPredictMessage("button.close",null)},e.prototype.onLoad=function(){var t=this;this.forecastMonitorTemplateDetailGrid=this.forecastMonitorTemplateDetailCanvas.addChild(a.hb),this.forecastMonitorTemplateDetailGrid.onFilterChanged=this.disableDeleteButton.bind(this),this.forecastMonitorTemplateDetailGrid.onSortChanged=this.disableDeleteButton.bind(this),this.type=a.x.call("eval","type"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="forecastMonitorTemplate.do?method=",this.screenName=a.x.call("eval","screenName"),this.templateId=a.x.call("eval","templateId"),this.templateName=a.x.call("eval","templateName"),this.ordnialPos=a.x.call("eval","ordinalPos"),this.userId=a.x.call("eval","userId"),this.shortName=a.x.call("eval","shortName"),this.modify=a.x.call("eval","modify"),this.copyFromFlag=a.x.call("eval","copyFromFlag"),this.copiedTemplateId=a.x.call("eval","copiedTemplateId"),this.copiedUserId=a.x.call("eval","copiedUserId"),"change"!=this.screenName?(this.lblColumnType.text=this.lblColumnType.text+" *",this.actionMethod="addForecastDetails&loadFlex=true",this.actionMethod=this.actionMethod+"&templateId="+this.templateId,this.actionMethod=this.actionMethod+"&templateName="+this.templateName):("Normal"==this.type?this.actionMethod="changeForecastDetails&loadFlex=true":this.actionMethod="loadSubtotalChange&loadFlex=true",this.actionMethod=this.actionMethod+"&ordinalPos="+this.ordnialPos,this.actionMethod=this.actionMethod+"&userId="+this.userId,this.actionMethod=this.actionMethod+"&columnId="+a.x.call("eval","columnId"),this.actionMethod=this.actionMethod+"&templateId="+this.templateId,this.actionMethod=this.actionMethod+"&templateName="+this.templateName,this.actionMethod=this.actionMethod+"&shortName="+this.shortName,this.actionMethod=this.actionMethod+"&description="+a.x.call("eval","description"),this.actionMethod=this.actionMethod+"&type="+this.type,this.actionMethod=this.actionMethod+"&modify="+this.modify,this.actionMethod=this.actionMethod+"&copiedTemplateId="+this.copiedTemplateId,this.actionMethod=this.actionMethod+"&copiedUserId="+this.copiedUserId,this.actionMethod=this.actionMethod+"&copyFromFlag="+this.copyFromFlag,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,"Total"==this.shortName&&(this.txtColumnNo.enabled=!1,this.txtDescription.enabled=!1,this.txtShortName.enabled=!1,this.chbPublic.enabled=!1),this.cbColumnType.enabled=!1),this.actionMethod=this.actionMethod+"&addClick="+a.x.call("eval","addClick"),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.forecastMonitorTemplateDetailGrid.onRowClick=function(e){t.cellLogic(e)}},e.prototype.inputDataResult=function(t){try{if(this.inputData&&this.inputData.isBusy())this.inputData.cbStop();else{if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(!this.jsonReader.isDataBuilding()){this.cbColumnType.setComboData(this.jsonReader.getSelects(),!0);var e={columns:this.jsonReader.getColumnData()};this.forecastMonitorTemplateDetailGrid.CustomGrid(e),this.jsonReader.getGridData()?this.forecastMonitorTemplateDetailGrid.gridData=this.jsonReader.getGridData():(this.forecastMonitorTemplateDetailGrid.dataProvider=[],this.forecastMonitorTemplateDetailGrid.selectedIndex=-1),"change"==this.screenName&&(this.txtColumnNo.text=this.jsonReader.getScreenAttributes().ordinalPos.toString(),this.txtShortName.text=this.shortName,this.shortNameOnLoad=this.txtShortName.text,this.txtDescription.text=this.jsonReader.getScreenAttributes().description,this.totalMultiplier=this.jsonReader.getScreenAttributes().totalMultiplier,""!=this.totalMultiplier&&null!=this.totalMultiplier&&(this.chbPublic.selected=!0,this.txtMulitiplier.text=this.totalMultiplier,this.txtMulitiplier.enabled=!0),"Total"==this.cbColumnType.selectedLabel&&(this.chbPublic.enabled=!1,this.chbPublic.selected=!1,this.txtMulitiplier.text="",this.txtMulitiplier.enabled=!1),this.columnId=this.jsonReader.getScreenAttributes().columnId),this.txtShortName.text=this.jsonReader.getScreenAttributes().displayName,this.txtDescription.text=this.jsonReader.getScreenAttributes().description}this.suggestButton.enabled="Normal"!=this.cbColumnType.selectedLabel,this.prevRecievedJSON=this.lastRecievedJSON}else this.swtAlert.error(a.Wb.getCommonMessages("alert.generic_exception"));this.changeHighLightBorder()}}catch(i){console.log("error in inputData",i)}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(){this.swtAlert.error("alert.generic_exception")},e.prototype.closeHandler=function(){a.x.call("close")},e.prototype.cellLogic=function(t){this.forecastMonitorTemplateDetailGrid.selectedIndex>=0?(this.deleteButton.enabled=!0,"Normal"==this.cbColumnType.selectedLabel?(this.typeId=this.forecastMonitorTemplateDetailGrid.selectedItem.type.content,this.selectedEntityId=this.forecastMonitorTemplateDetailGrid.selectedItem.entity.content,this.selectedId=this.forecastMonitorTemplateDetailGrid.selectedItem.id.content):(this.selectedId=this.forecastMonitorTemplateDetailGrid.selectedItem.columnno.content,this.selectedColumn=this.forecastMonitorTemplateDetailGrid.selectedItem.displayname.content)):this.deleteButton.enabled=!1},e.prototype.typeChange=function(){"SubTotal"==this.cbColumnType.selectedLabel?(this.actionMethod="loadSubtotalAdd",this.actionMethod+="&type="+this.cbColumnType.selectedLabel,this.suggestButton.enabled=!0):(this.actionMethod="addForecastDetails&loadFlex=true",this.actionMethod+="&type="+this.cbColumnType.selectedLabel,this.suggestButton.enabled=!1),this.actionMethod=this.actionMethod+"&ordinalPos="+this.ordnialPos,this.actionMethod=this.actionMethod+"&userId="+this.userId,this.actionMethod=this.actionMethod+"&columnId="+a.x.call("eval","columnId"),this.actionMethod=this.actionMethod+"&templateId="+this.templateId,this.actionMethod=this.actionMethod+"&templateName="+this.templateName,this.actionMethod=this.actionMethod+"&displayName="+a.x.call("eval","displayName"),this.actionMethod=this.actionMethod+"&description="+a.x.call("eval","description"),this.actionMethod=this.actionMethod+"&type="+this.type,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.loadAddPopup=function(t){try{""==a.Z.trim(this.txtColumnNo.text.toString())||""==a.Z.trim(this.txtShortName.text.toString())||""==a.Z.trim(this.txtDescription.text.toString())?this.swtAlert.warning(a.Wb.getPredictMessage("alert.forecasttemplate.mandatory",null)):("Normal"==this.cbColumnType.selectedLabel?this.actionMethod="addPopUp":(this.actionMethod="addPopUpForSubtotal",this.actionMethod+="&pressedbutton="+t),this.actionMethod+="&type="+this.cbColumnType.selectedLabel,this.actionMethod+="&templateId="+this.templateId,this.actionMethod+="&templateName="+this.templateName,this.actionMethod+="&ordinalPos="+a.Z.trim(this.txtColumnNo.text.toString()),this.actionMethod+="&userId="+this.userId,this.actionMethod+="&columnId="+a.Z.trim(this.txtColumnNo.text.toString()),this.actionMethod+="&description="+escape(escape(a.Z.trim(this.txtDescription.text.toString()))),this.actionMethod+="&shortName="+this.shortNameOnLoad,this.actionMethod+="&detailRowCount="+this.detailRowCount,a.x.call("openChildWindow",this.actionMethod))}catch(e){console.log("error",e)}},e.prototype.saveTemplateDetail=function(){var t=this,e=!1,i=[],l=[],o=[],n=[],s=[],h=[];if(""==a.Z.trim(this.txtColumnNo.text.toString())||""==a.Z.trim(this.txtShortName.text.toString())||""==a.Z.trim(this.txtDescription.text.toString()))this.swtAlert.warning(a.Wb.getPredictMessage("alert.forecasttemplate.mandatory",null));else if(this.chbPublic.selected&&""==this.txtMulitiplier.text)this.swtAlert.warning(a.x.call("getBundle","alert","template-totalmultiplier","Please enter Total Multiplier"));else if(Number(this.txtColumnNo.text.toString())>2&&Number(this.txtColumnNo.text.toString())<960||"Total"==this.cbColumnType.selectedLabel){if(null!=this.shortNameOnLoad&&this.shortNameOnLoad!=this.txtShortName.text&&(e=!0),this.actionMethod="saveTemplatesSrcInSession",this.requestParams=[],this.requestParams.type=this.cbColumnType.selectedLabel,this.requestParams.fromFlex=!0,this.requestParams.templateId=this.templateId,this.requestParams.columnId=a.Z.trim(this.txtColumnNo.text.toString()),this.requestParams.shortName=a.Z.trim(this.txtShortName.text.toString()),this.requestParams.isNameChanged=e,this.requestParams.shortNameOnLoad=e?this.shortNameOnLoad:a.Z.trim(this.txtShortName.text.toString()),this.requestParams.description=a.Z.trim(this.txtDescription.text.toString()),this.requestParams.userId=this.userId,this.requestParams.multiplier=this.txtMulitiplier.text,"change"!=this.screenName?this.requestParams.modifiedValue="save":this.requestParams.modifiedValue="update",this.jsonReader.setInputJSON(this.lastRecievedJSON),this.forecastMonitorTemplateDetailGrid.dataProvider.length>0){for(var r=0;r<this.jsonReader.getRowSize();r++)"Normal"==this.cbColumnType.selectedLabel?(i[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r].type,l[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r].entity,o[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r]._id,n[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r].name,s[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r].multiplier):(i[r]="C",o[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r].columnno,l[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r].displayname,n[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r].description,s[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r].multiplier,h[r]=this.forecastMonitorTemplateDetailGrid.dataProvider[r].ordinalpos);this.requestParams.columnSourceType=i,this.requestParams.entity=l,this.requestParams.sourceId=o,this.requestParams.sourceName=n,this.requestParams.sourceMultiplier=s,this.requestParams.sourceOrdinalPos=h}this.saveData.cbStart=this.startOfComms.bind(this),this.saveData.cbStop=this.endOfComms.bind(this),this.saveData.cbResult=function(e){t.saveDataResult(e)},this.saveData.cbFault=this.inputDataFault.bind(this),this.saveData.encodeURL=!1,this.saveData.url=this.baseURL+this.actionPath+this.actionMethod,this.saveData.send(this.requestParams)}else this.swtAlert.warning(a.x.call("getBundle","alert","template-columnnumbers","Column number should be in the range 3 to 959"))},e.prototype.saveDataResult=function(t){this.jsonReader.setInputJSON(t),this.jsonReader.getRequestReplyStatus()?(a.x.call("refreshParent"),window.opener&&window.opener.instanceElement&&window.opener.instanceElement.reloadMain()):this.swtAlert.error(this.jsonReader.getRequestReplyMessage())},e.prototype.deleteHandler=function(){this.swtAlert.question(a.x.call("getBundle","alert","template-columndelete","Are you sure you want to remove this record?"),null,a.c.YES|a.c.NO,null,this.removeRecord.bind(this),null)},e.prototype.removeRecord=function(t){var e=this;t.detail==a.c.YES&&("Normal"==this.cbColumnType.selectedLabel?(this.requestParams.type=this.typeId,this.requestParams.id=this.selectedId,this.requestParams.entity=this.selectedEntityId):(this.requestParams.type="C",this.requestParams.id=this.selectedId,this.requestParams.displayname=this.selectedColumn),this.requestParams.columnType=this.cbColumnType.selectedLabel,this.requestParams.templateId=this.templateId,null!=this.shortNameOnLoad?this.requestParams.shortName=this.shortNameOnLoad:this.requestParams.shortName=a.Z.trim(this.txtShortName.text.toString()),this.requestParams.fromFlex=!0,this.actionMethod="deleteColumnSrc",this.inputData.cbResult=function(t){e.refreshDetailGrid(t)},this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.deleteButton.enabled=!1)},e.prototype.changeTextMultiplier=function(){1==this.chbPublic.selected?(this.txtMulitiplier.enabled=!0,this.txtMulitiplier.text="1",this.totalChanged=!1):(this.txtMulitiplier.enabled=!1,this.txtMulitiplier.text="",this.totalChanged=!0)},e.prototype.validateNumber=function(){isNaN(Number(this.txtMulitiplier.text))&&this.txtMulitiplier.text&&this.swtAlert.error("Please enter a valid number")},e.prototype.refreshDetail=function(){var t=this;this.actionMethod="refreshDetail",this.actionMethod=this.actionMethod+"&ordinalPos="+this.txtColumnNo.text.toString(),this.actionMethod=this.actionMethod+"&userId="+this.userId,this.actionMethod=this.actionMethod+"&columnId="+a.x.call("eval","columnId"),this.actionMethod=this.actionMethod+"&templateId="+this.templateId,this.actionMethod=this.actionMethod+"&templateName="+this.templateName,this.actionMethod=this.actionMethod+"&shortName="+this.shortName,this.actionMethod=this.actionMethod+"&description="+a.x.call("eval","description"),this.actionMethod=this.actionMethod+"&type="+this.type,this.actionMethod=this.actionMethod+"&modify="+this.modify,this.actionMethod=this.actionMethod+"&copiedTemplateId="+this.copiedTemplateId,this.actionMethod=this.actionMethod+"&totalMultiplier="+this.txtMulitiplier.text,this.actionMethod=this.actionMethod+"&copiedUserId="+this.copiedUserId,this.actionMethod=this.actionMethod+"&copyFromFlag="+this.copyFromFlag,this.inputData.cbResult=function(e){t.refreshDetailGrid(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.refreshSubDetail=function(){var t=this;this.actionMethod="refreshSubDetail",this.actionMethod=this.actionMethod+"&ordinalPos="+this.txtColumnNo.text.toString(),this.actionMethod=this.actionMethod+"&userId="+this.userId,this.actionMethod=this.actionMethod+"&columnId="+a.x.call("eval","columnId"),this.actionMethod=this.actionMethod+"&templateId="+this.templateId,this.actionMethod=this.actionMethod+"&templateName="+this.templateName,this.actionMethod=this.actionMethod+"&shortName="+this.shortName,this.actionMethod=this.actionMethod+"&description="+a.x.call("eval","description"),this.actionMethod=this.actionMethod+"&type="+this.type,this.actionMethod=this.actionMethod+"&modify="+this.modify,this.actionMethod=this.actionMethod+"&copiedTemplateId="+this.copiedTemplateId,this.actionMethod=this.actionMethod+"&totalMultiplier="+this.txtMulitiplier.text,this.actionMethod=this.actionMethod+"&copiedUserId="+this.copiedUserId,this.actionMethod=this.actionMethod+"&copyFromFlag="+this.copyFromFlag,this.inputData.cbResult=function(e){t.refreshDetailGrid(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.refreshDetailGrid=function(t){this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON);var e={columns:this.jsonReader.getColumnData()};this.forecastMonitorTemplateDetailGrid.CustomGrid(e),this.forecastMonitorTemplateDetailGrid.gridData=this.jsonReader.getGridData(),this.forecastMonitorTemplateDetailGrid.setRowSize=this.jsonReader.getRowSize(),this.deleteButton.enabled=!1},e.prototype.changeHighLightBorder=function(){this.txtColumnNo.required=""==this.txtColumnNo.text.toString(),this.txtShortName.required=""==this.txtShortName.text,this.txtDescription.required=""==this.txtDescription.text},e.prototype.doHelp=function(){a.x.call("help")},e.prototype.disableDeleteButton=function(){-1==this.forecastMonitorTemplateDetailGrid.selectedIndex&&(this.deleteButton.enabled=!1)},e}(a.yb),h=[{path:"",component:s}],r=(n.l.forChild(h),function(){return function(){}}()),d=i("pMnS"),c=i("RChO"),u=i("t6HQ"),p=i("WFGK"),m=i("5FqG"),b=i("Ip0R"),g=i("gIcY"),M=i("t/Na"),x=i("sE5F"),f=i("OzfB"),R=i("T7CS"),I=i("S7LP"),D=i("6aHO"),C=i("WzUx"),w=i("A7o+"),y=i("zCE2"),T=i("Jg5P"),N=i("3R0m"),v=i("hhbb"),S=i("5rxC"),P=i("Fzqc"),B=i("21Lb"),L=i("hUWP"),k=i("3pJQ"),F=i("V9q+"),G=i("VDKW"),A=i("kXfT"),q=i("BGbe");i.d(e,"ForecastMonitorTemplateDetailModuleNgFactory",function(){return O}),i.d(e,"RenderType_ForecastMonitorTemplateDetail",function(){return _}),i.d(e,"View_ForecastMonitorTemplateDetail_0",function(){return Z}),i.d(e,"View_ForecastMonitorTemplateDetail_Host_0",function(){return W}),i.d(e,"ForecastMonitorTemplateDetailNgFactory",function(){return j});var O=l.Gb(r,[],function(t){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[d.a,c.a,u.a,p.a,m.Cb,m.Pb,m.r,m.rc,m.s,m.Ab,m.Bb,m.Db,m.qd,m.Hb,m.k,m.Ib,m.Nb,m.Ub,m.yb,m.Jb,m.v,m.A,m.e,m.c,m.g,m.d,m.Kb,m.f,m.ec,m.Wb,m.bc,m.ac,m.sc,m.fc,m.lc,m.jc,m.Eb,m.Fb,m.mc,m.Lb,m.nc,m.Mb,m.dc,m.Rb,m.b,m.ic,m.Yb,m.Sb,m.kc,m.y,m.Qb,m.cc,m.hc,m.pc,m.oc,m.xb,m.p,m.q,m.o,m.h,m.j,m.w,m.Zb,m.i,m.m,m.Vb,m.Ob,m.Gb,m.Xb,m.t,m.tc,m.zb,m.n,m.qc,m.a,m.z,m.rd,m.sd,m.x,m.td,m.gc,m.l,m.u,m.ud,m.Tb,j]],[3,l.n],l.J]),l.Rb(4608,b.m,b.l,[l.F,[2,b.u]]),l.Rb(4608,g.c,g.c,[]),l.Rb(4608,g.p,g.p,[]),l.Rb(4608,M.j,M.p,[b.c,l.O,M.n]),l.Rb(4608,M.q,M.q,[M.j,M.o]),l.Rb(5120,M.a,function(t){return[t,new a.tb]},[M.q]),l.Rb(4608,M.m,M.m,[]),l.Rb(6144,M.k,null,[M.m]),l.Rb(4608,M.i,M.i,[M.k]),l.Rb(6144,M.b,null,[M.i]),l.Rb(4608,M.f,M.l,[M.b,l.B]),l.Rb(4608,M.c,M.c,[M.f]),l.Rb(4608,x.c,x.c,[]),l.Rb(4608,x.g,x.b,[]),l.Rb(5120,x.i,x.j,[]),l.Rb(4608,x.h,x.h,[x.c,x.g,x.i]),l.Rb(4608,x.f,x.a,[]),l.Rb(5120,x.d,x.k,[x.h,x.f]),l.Rb(5120,l.b,function(t,e){return[f.j(t,e)]},[b.c,l.O]),l.Rb(4608,R.a,R.a,[]),l.Rb(4608,I.a,I.a,[]),l.Rb(4608,D.a,D.a,[l.n,l.L,l.B,I.a,l.g]),l.Rb(4608,C.c,C.c,[l.n,l.g,l.B]),l.Rb(4608,C.e,C.e,[C.c]),l.Rb(4608,w.l,w.l,[]),l.Rb(4608,w.h,w.g,[]),l.Rb(4608,w.c,w.f,[]),l.Rb(4608,w.j,w.d,[]),l.Rb(4608,w.b,w.a,[]),l.Rb(4608,w.k,w.k,[w.l,w.h,w.c,w.j,w.b,w.m,w.n]),l.Rb(4608,C.i,C.i,[[2,w.k]]),l.Rb(4608,C.r,C.r,[C.L,[2,w.k],C.i]),l.Rb(4608,C.t,C.t,[]),l.Rb(4608,C.w,C.w,[]),l.Rb(1073742336,n.l,n.l,[[2,n.r],[2,n.k]]),l.Rb(1073742336,b.b,b.b,[]),l.Rb(1073742336,g.n,g.n,[]),l.Rb(1073742336,g.l,g.l,[]),l.Rb(1073742336,y.a,y.a,[]),l.Rb(1073742336,T.a,T.a,[]),l.Rb(1073742336,g.e,g.e,[]),l.Rb(1073742336,N.a,N.a,[]),l.Rb(1073742336,w.i,w.i,[]),l.Rb(1073742336,C.b,C.b,[]),l.Rb(1073742336,M.e,M.e,[]),l.Rb(1073742336,M.d,M.d,[]),l.Rb(1073742336,x.e,x.e,[]),l.Rb(1073742336,v.b,v.b,[]),l.Rb(1073742336,S.b,S.b,[]),l.Rb(1073742336,f.c,f.c,[]),l.Rb(1073742336,P.a,P.a,[]),l.Rb(1073742336,B.d,B.d,[]),l.Rb(1073742336,L.c,L.c,[]),l.Rb(1073742336,k.a,k.a,[]),l.Rb(1073742336,F.a,F.a,[[2,f.g],l.O]),l.Rb(1073742336,G.b,G.b,[]),l.Rb(1073742336,A.a,A.a,[]),l.Rb(1073742336,q.b,q.b,[]),l.Rb(1073742336,a.Tb,a.Tb,[]),l.Rb(1073742336,r,r,[]),l.Rb(256,M.n,"XSRF-TOKEN",[]),l.Rb(256,M.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,w.m,void 0,[]),l.Rb(256,w.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,n.i,function(){return[[{path:"",component:s}]]},[])])}),J=[[".numericInput[_ngcontent-%COMP%]{font-size:11px!important;height:23px!important;margin-bottom:5px!important}"]],_=l.Hb({encapsulation:0,styles:J,data:{}});function Z(t){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{forecastMonitorTemplateDetailCanvas:0}),l.Zb(402653184,3,{addButton:0}),l.Zb(402653184,4,{okButton:0}),l.Zb(402653184,5,{deleteButton:0}),l.Zb(402653184,6,{closeButton:0}),l.Zb(402653184,7,{suggestButton:0}),l.Zb(402653184,8,{lblColumnType:0}),l.Zb(402653184,9,{lblColumnNo:0}),l.Zb(402653184,10,{lblShortName:0}),l.Zb(402653184,11,{lblDesc:0}),l.Zb(402653184,12,{lblContribute:0}),l.Zb(402653184,13,{txtColumnNo:0}),l.Zb(402653184,14,{txtShortName:0}),l.Zb(402653184,15,{txtDescription:0}),l.Zb(402653184,16,{txtMulitiplier:0}),l.Zb(402653184,17,{cbColumnType:0}),l.Zb(402653184,18,{chbPublic:0}),l.Zb(402653184,19,{loadingImage:0}),(t()(),l.Jb(19,0,null,null,63,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var l=!0,o=t.component;"creationComplete"===e&&(l=!1!==o.onLoad()&&l);return l},m.ad,m.hb)),l.Ib(20,4440064,null,0,a.yb,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),l.Jb(21,0,null,0,61,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,m.od,m.vb)),l.Ib(22,4440064,null,0,a.ec,[l.r,a.i,l.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),l.Jb(23,0,null,0,35,"SwtCanvas",[["height","30%"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(24,4440064,null,0,a.db,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(25,0,null,0,33,"VBox",[["height","100%"],["paddingLeft","10"],["paddingTop","5"],["verticalGap","0"],["width","100%"]],null,null,null,m.od,m.vb)),l.Ib(26,4440064,null,0,a.ec,[l.r,a.i,l.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingLeft:[4,"paddingLeft"]},null),(t()(),l.Jb(27,0,null,0,5,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),l.Ib(28,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(29,0,null,0,1,"SwtLabel",[["width","110"]],null,null,null,m.Yc,m.fb)),l.Ib(30,4440064,[[8,4],["lblColumnType",4]],0,a.vb,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(31,0,null,0,1,"SwtComboBox",[["dataLabel","columnType"],["width","163"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var o=!0,a=t.component;"window:mousewheel"===e&&(o=!1!==l.Tb(t,32).mouseWeelEventHandler(i.target)&&o);"change"===e&&(o=!1!==a.typeChange()&&o);return o},m.Pc,m.W)),l.Ib(32,4440064,[[17,4],["cbColumnType",4]],0,a.gb,[l.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"]},{change_:"change"}),(t()(),l.Jb(33,0,null,0,5,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),l.Ib(34,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(35,0,null,0,1,"SwtLabel",[["width","110"]],null,null,null,m.Yc,m.fb)),l.Ib(36,4440064,[[9,4],["lblColumnNo",4]],0,a.vb,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(37,0,null,0,1,"SwtNumericInput",[["maxChars","12"],["textAlign","right"],["width","160"]],null,null,null,m.cd,m.jb)),l.Ib(38,4440064,[[13,4],["txtColumnNo",4]],0,a.Ab,[l.r,a.i],{maxChars:[0,"maxChars"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),l.Jb(39,0,null,0,5,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),l.Ib(40,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(41,0,null,0,1,"SwtLabel",[["width","110"]],null,null,null,m.Yc,m.fb)),l.Ib(42,4440064,[[10,4],["lblShortName",4]],0,a.vb,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(43,0,null,0,1,"SwtTextInput",[["maxChars","12"],["restrict","^\xe1\xe9\xed\xf3\xfa\xf1\xc1\xc9\xcd\xd3\xda\xd1@\\.\\'\\/#$%()=*?\xa1\xbf'\\,\\\"\\&|\xb0!\\<>;:\\^\xa8+\\{\\}\\[\\]\\`~\\\\"],["width","160"]],null,[[null,"change"]],function(t,e,i){var l=!0,o=t.component;"change"===e&&(l=!1!==o.changeHighLightBorder()&&l);return l},m.kd,m.sb)),l.Ib(44,4440064,[[14,4],["txtShortName",4]],0,a.Rb,[l.r,a.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],width:[2,"width"]},{change_:"change"}),(t()(),l.Jb(45,0,null,0,5,"HBox",[],null,null,null,m.Dc,m.K)),l.Ib(46,4440064,null,0,a.C,[l.r,a.i],null,null),(t()(),l.Jb(47,0,null,0,1,"SwtLabel",[["width","110"]],null,null,null,m.Yc,m.fb)),l.Ib(48,4440064,[[11,4],["lblDesc",4]],0,a.vb,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(49,0,null,0,1,"SwtTextInput",[["maxChars","50"],["textAlign","left"],["width","360"]],null,[[null,"change"]],function(t,e,i){var l=!0,o=t.component;"change"===e&&(l=!1!==o.changeHighLightBorder()&&l);return l},m.kd,m.sb)),l.Ib(50,4440064,[[15,4],["txtDescription",4]],0,a.Rb,[l.r,a.i],{maxChars:[0,"maxChars"],textAlign:[1,"textAlign"],width:[2,"width"]},{change_:"change"}),(t()(),l.Jb(51,0,null,0,7,"HBox",[],null,null,null,m.Dc,m.K)),l.Ib(52,4440064,null,0,a.C,[l.r,a.i],null,null),(t()(),l.Jb(53,0,null,0,1,"SwtCheckBox",[],null,[[null,"change"]],function(t,e,i){var l=!0,o=t.component;"change"===e&&(l=!1!==o.changeTextMultiplier()&&l);return l},m.Oc,m.V)),l.Ib(54,4440064,[[18,4],["chbPublic",4]],0,a.eb,[l.r,a.i],null,{change_:"change"}),(t()(),l.Jb(55,0,null,0,1,"SwtLabel",[["paddingTop","2"]],null,null,null,m.Yc,m.fb)),l.Ib(56,4440064,[[12,4],["lblContribute",4]],0,a.vb,[l.r,a.i],{paddingTop:[0,"paddingTop"]},null),(t()(),l.Jb(57,0,null,0,1,"SwtTextInput",[["enabled","false"],["restrict","0-9\\-\\."],["textAlign","right"],["width","160"]],null,[[null,"focusOut"]],function(t,e,i){var l=!0,o=t.component;"focusOut"===e&&(l=!1!==o.validateNumber()&&l);return l},m.kd,m.sb)),l.Ib(58,4440064,[[16,4],["txtMulitiplier",4]],0,a.Rb,[l.r,a.i],{restrict:[0,"restrict"],textAlign:[1,"textAlign"],width:[2,"width"],enabled:[3,"enabled"]},{onFocusOut_:"focusOut"}),(t()(),l.Jb(59,0,null,0,1,"SwtCanvas",[["height","59%"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(60,4440064,[[2,4],["forecastMonitorTemplateDetailCanvas",4]],0,a.db,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(61,0,null,0,21,"SwtCanvas",[["height","7%"],["width","100%"]],null,null,null,m.Nc,m.U)),l.Ib(62,4440064,null,0,a.db,[l.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),l.Jb(63,0,null,0,19,"HBox",[["width","100%"]],null,null,null,m.Dc,m.K)),l.Ib(64,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"]},null),(t()(),l.Jb(65,0,null,0,11,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,m.Dc,m.K)),l.Ib(66,4440064,null,0,a.C,[l.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),l.Jb(67,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(t,e,i){var l=!0,o=t.component;"click"===e&&(l=!1!==o.loadAddPopup("suggest")&&l);return l},m.Mc,m.T)),l.Ib(68,4440064,[[7,4],["suggestButton",4]],0,a.cb,[l.r,a.i],null,{onClick_:"click"}),(t()(),l.Jb(69,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(t,e,i){var l=!0,o=t.component;"click"===e&&(l=!1!==o.loadAddPopup("add")&&l);return l},m.Mc,m.T)),l.Ib(70,4440064,[[3,4],["addButton",4]],0,a.cb,[l.r,a.i],null,{onClick_:"click"}),(t()(),l.Jb(71,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,o=t.component;"click"===e&&(l=!1!==o.deleteHandler()&&l);return l},m.Mc,m.T)),l.Ib(72,4440064,[[5,4],["deleteButton",4]],0,a.cb,[l.r,a.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(t()(),l.Jb(73,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(t,e,i){var l=!0,o=t.component;"click"===e&&(l=!1!==o.saveTemplateDetail()&&l);return l},m.Mc,m.T)),l.Ib(74,4440064,[[4,4],["okButton",4]],0,a.cb,[l.r,a.i],null,{onClick_:"click"}),(t()(),l.Jb(75,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var l=!0,o=t.component;"click"===e&&(l=!1!==o.closeHandler()&&l);return l},m.Mc,m.T)),l.Ib(76,4440064,[[6,4],["closeButton",4]],0,a.cb,[l.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),l.Jb(77,0,null,0,5,"HBox",[["horizontalAlign","right"],["right","5"],["top","3"]],null,null,null,m.Dc,m.K)),l.Ib(78,4440064,null,0,a.C,[l.r,a.i],{right:[0,"right"],top:[1,"top"],horizontalAlign:[2,"horizontalAlign"]},null),(t()(),l.Jb(79,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var l=!0,o=t.component;"click"===e&&(l=!1!==o.doHelp()&&l);return l},m.Wc,m.db)),l.Ib(80,4440064,[["helpIcon",4]],0,a.rb,[l.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),l.Jb(81,0,null,0,1,"SwtLoadingImage",[],null,null,null,m.Zc,m.gb)),l.Ib(82,114688,[[19,4],["loadingImage",4]],0,a.xb,[l.r],null,null)],function(t,e){t(e,20,0,"100%","100%");t(e,22,0,"100%","100%","5","5","5","5");t(e,24,0,"100%","30%");t(e,26,0,"0","100%","100%","5","10");t(e,28,0,"100%");t(e,30,0,"110");t(e,32,0,"columnType","163");t(e,34,0,"100%");t(e,36,0,"110");t(e,38,0,"12","right","160");t(e,40,0,"100%");t(e,42,0,"110");t(e,44,0,"12","^\xe1\xe9\xed\xf3\xfa\xf1\xc1\xc9\xcd\xd3\xda\xd1@\\.\\'\\/#$%()=*?\xa1\xbf'\\,\\\"\\&|\xb0!\\<>;:\\^\xa8+\\{\\}\\[\\]\\`~\\\\","160"),t(e,46,0);t(e,48,0,"110");t(e,50,0,"50","left","360"),t(e,52,0),t(e,54,0);t(e,56,0,"2");t(e,58,0,"0-9\\-\\.","right","160","false");t(e,60,0,"100%","59%");t(e,62,0,"100%","7%");t(e,64,0,"100%");t(e,66,0,"100%","5"),t(e,68,0),t(e,70,0);t(e,72,0,"deleteButton","false"),t(e,74,0);t(e,76,0,"closeButton");t(e,78,0,"5","3","right");t(e,80,0,"helpIcon"),t(e,82,0)},null)}function W(t){return l.dc(0,[(t()(),l.Jb(0,0,null,null,1,"app-forecast-monitor-template-detail",[],null,null,null,Z,_)),l.Ib(1,4440064,null,0,s,[a.i,l.r],null,null)],function(t,e){t(e,1,0)},null)}var j=l.Fb("app-forecast-monitor-template-detail",s,W,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);