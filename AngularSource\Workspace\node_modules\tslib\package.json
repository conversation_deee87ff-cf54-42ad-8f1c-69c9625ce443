{"_from": "tslib@1.9.0", "_id": "tslib@1.9.0", "_inBundle": false, "_integrity": "sha512-f/qGG2tUkrISBlQZEjEqoZ3B2+npJjIf04H1wuAv9iA8i04Icp+61KRXxFdha22670NJopsZCIjhC3SnjPRKrQ==", "_location": "/tslib", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "tslib@1.9.0", "name": "tslib", "escapedName": "tslib", "rawSpec": "1.9.0", "saveSpec": null, "fetchSpec": "1.9.0"}, "_requiredBy": ["/", "/@angular-devkit/architect/rxjs", "/@angular-devkit/build-angular/rxjs", "/@angular-devkit/build-webpack/rxjs", "/@angular-devkit/core/rxjs", "/@angular-devkit/schematics/rxjs", "/@angular/animations", "/@angular/cdk", "/@angular/cli/rxjs", "/@angular/common", "/@angular/compiler", "/@angular/compiler-cli", "/@angular/core", "/@angular/flex-layout", "/@angular/forms", "/@angular/http", "/@angular/material", "/@angular/platform-browser", "/@angular/platform-browser-dynamic", "/@angular/router", "/@ctrl/ngx-codemirror", "/@ngtools/webpack/rxjs", "/@ngx-translate/core", "/@ngx-translate/http-loader", "/@schematics/angular/rxjs", "/@schematics/update/rxjs", "/@tinymce/tinymce-angular", "/angular-resize-event", "/angular-slickgrid", "/angular-tippy", "/ng-multiselect-dropdown-angular7", "/ng-select", "/ng2-file-upload", "/ng5-slider", "/ngx-monaco-editor", "/ngx-popper", "/ngx-quill", "/rxjs", "/swt-tool-box", "/tslint", "/tsutils"], "_resolved": "https://registry.npmjs.org/tslib/-/tslib-1.9.0.tgz", "_shasum": "e37a86fda8cbbaf23a057f473c9f4dc64e5fc2e8", "_spec": "tslib@1.9.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "Microsoft Corp."}, "bugs": {"url": "https://github.com/Microsoft/TypeScript/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Runtime library for TypeScript helper functions", "homepage": "http://typescriptlang.org/", "jsnext:main": "tslib.es6.js", "keywords": ["TypeScript", "Microsoft", "compiler", "language", "javascript", "tslib", "runtime"], "license": "Apache-2.0", "main": "tslib.js", "module": "tslib.es6.js", "name": "tslib", "repository": {"type": "git", "url": "git+https://github.com/Microsoft/tslib.git"}, "typings": "tslib.d.ts", "version": "1.9.0"}