{"_from": "source-list-map@^2.0.0", "_id": "source-list-map@2.0.1", "_inBundle": false, "_integrity": "sha512-qnQ7gVMxGNxsiL4lEuJwe/To8UnK7fAnmbGEEH8RpLouuKbeEm0lhbQVFIrNSuB+G7tVrAlVsZgETT5nljf+Iw==", "_location": "/source-list-map", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "source-list-map@^2.0.0", "name": "source-list-map", "escapedName": "source-list-map", "rawSpec": "^2.0.0", "saveSpec": null, "fetchSpec": "^2.0.0"}, "_requiredBy": ["/webpack-sources"], "_resolved": "https://registry.npmjs.org/source-list-map/-/source-list-map-2.0.1.tgz", "_shasum": "3993bd873bfc48479cca9ea3a547835c7c154b34", "_spec": "source-list-map@^2.0.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\webpack-sources", "author": {"name": "<PERSON> @sokra"}, "bugs": {"url": "https://github.com/webpack/source-list-map/issues"}, "bundleDependencies": false, "deprecated": false, "description": "Fast line to line SourceMap generator.", "devDependencies": {"mocha": "^2.2.1", "should": "^5.2.0"}, "files": ["lib"], "homepage": "https://github.com/webpack/source-list-map", "keywords": ["source-map"], "license": "MIT", "main": "lib/index.js", "name": "source-list-map", "repository": {"type": "git", "url": "git+https://github.com/webpack/source-list-map.git"}, "scripts": {"test": "mocha -R spec"}, "version": "2.0.1"}