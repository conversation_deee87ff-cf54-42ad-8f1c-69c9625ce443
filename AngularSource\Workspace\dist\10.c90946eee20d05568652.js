(window.webpackJsonp=window.webpackJsonp||[]).push([[10],{lyA8:function(e,t,i){"use strict";i.r(t);var l=i("CcnG"),n=i("mrSG"),r=i("447K"),a=i("ZYCi"),s=function(e){function t(t,i){var l=e.call(this,i,t)||this;return l.commonService=t,l.element=i,l.jsonReader=new r.L,l.inputData=new r.G(l.commonService),l.logicUpdate=new r.G(l.commonService),l.baseURL=r.Wb.getBaseURL(),l.actionMethod="",l.actionPath="",l.requestParams=[],l.screenName=null,l.helpURL=null,l.title=null,l.categoryRuleId=null,l.ruleId=null,l.errorLocation=0,l.categoryId="",l.maxOrderCR=1,l.ordinal=null,l.ruleAssignPriority=null,l.childScreenName=null,l.tableToJoinQueryBuilder=[],l.from="",l.reOrder=!1,l.fourEyesRequired=!1,l.changesInRule=!1,l.moduleName="PC Category Maintenance Details",l.versionNumber="1.00.00",l.releaseDate="20 Feb 2019",l.moduleId="PC",l.spreadId="",l.ruleGridList=null,l.operationsList=new r.hc("<operationsList/>"),l.stateBeforeChange=!1,l.swtAlert=new r.bb(t),l}return n.d(t,e),t.prototype.ngOnDestroy=function(){instanceElement=null},t.prototype.ngOnInit=function(){var e=[];window.opener.instanceElement&&(e=window.opener.instanceElement.getParamsFromParent())&&(this.screenName=e[0].screenName,this.categoryId=e[0].categoryId,this.maxOrder=e[0].maxOrder,this.listOrder=e[0].listOrder,this.listRuleAssignPriority=e[0].listRuleAssignPriority,this.paymentRequest=!0===e[0].paymentRequest,this.pcmInputFlag=e[0].pcmInputFlag),instanceElement=this,this.timeInput.enabled=!0,this.gridRowRelTime.visible=!1,this.gridRowRelTime.includeInLayout=!1,this.gridRowOffsetDays.visible=!1,this.gridRowOffsetDays.includeInLayout=!1,this.relTimeGroup.enabled=!0,this.timeInput.visible=!1,this.saveButton.label=r.Wb.getPredictMessage("button.save",null),this.cancelButton.label=r.Wb.getPredictMessage("button.cancel",null),this.categoryNameLabel.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.name",null),this.categoryNameTxtInput.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.name",null),this.categoryIdLabel.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.id",null),this.categoryIdTxtInput.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.id",null),this.actCheckbox.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.active",null),this.actCheckbox.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.active",null),this.orderLabel.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.order",null),this.ordinalNumInput.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.order",null),this.ruleAssignPriorityLabel.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.ruleAssignmentPriority",null),this.ruleAssignPriorityInput.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.ruleAssignmentPriority",null),this.instRelGroupLabel.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.type",null),this.instRelGroup.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.type",null),this.urgent.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.urgent",null),this.spread.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.spreadable",null),this.labelRelTime.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.releaseTime",null),this.relTimeGroup.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.releaseTime",null),this.radioK.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.kickOff",null),this.radioU.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.froPaymentRequest",null),this.radioI.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.immediate",null),this.radioT.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.specificTime",null),this.timeInput.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.time",null),this.labelOffsetDays.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.daysOffset",null),this.offsetNum.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.daysOffset",null),this.assigMetLabel.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.assignmentMethod",null),this.assigMetGroup.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.assignmentMethod",null),this.radioB.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.both",null),this.radioM.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.manual",null),this.radioS.label=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.system",null),this.useliqLabel.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.liqCheck",null),this.useliqCheckBox.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.liqCheck",null),this.incTargLabel.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.target",null),this.incTargCheckBox.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.target",null),this.incLiqLabel.text=r.Wb.getPredictMessage("categoryMaintenanceDetails.label.incAvailLiq",null),this.incLiqCheckBox.toolTip=r.Wb.getPredictMessage("categoryMaintenanceDetails.tooltip.incAvailLiq",null),this.panelRulesGrid.title=r.Wb.getPredictMessage("categoryMaintenanceDetails.rulePanel.title",null),this.panelSpreadGrid.title=r.Wb.getPredictMessage("categoryMaintenanceDetails.spreadPanel.title",null),this.addRuleButton.label=r.Wb.getPredictMessage("button.add",null),this.changeRuleButton.label=r.Wb.getPredictMessage("button.change",null),this.deleteRuleButton.label=r.Wb.getPredictMessage("button.delete",null),this.viewRuleButton.label=r.Wb.getPredictMessage("button.view",null),this.categoryIdTxtInput.enabled=!0,this.viewRuleButton.enabled=!1,this.disableOrEnableButtons(!1),"add"==this.screenName?(this.categoryIdTxtInput.setFocus(),this.actCheckbox.enabled=!0,this.categoryNameTxtInput.enabled=!0,this.categoryIdTxtInput.enabled=!0,this.ordinalNumInput.enabled=!0,this.ruleAssignPriorityInput.enabled="Y"==this.pcmInputFlag,this.saveButton.visible=!0,this.instRelGroup.enabled=!0,this.assigMetGroup.enabled=!0,this.incTargCheckBox.enabled=!0,this.incLiqCheckBox.enabled=!0,this.addRuleButton.enabled=!0):"view"==this.screenName?(this.categoryNameTxtInput.enabled=!1,this.categoryIdTxtInput.enabled=!1,this.ordinalNumInput.enabled=!1,this.ruleAssignPriorityInput.enabled=!1,this.useliqCheckBox.enabled=!1,this.actCheckbox.enabled=!1,this.incTargCheckBox.enabled=!1,this.incLiqCheckBox.enabled=!1,this.relTimeGroup.enabled=!1,this.instRelGroup.enabled=!1,this.assigMetGroup.enabled=!1,this.timeInput.enabled=!1,this.addRuleButton.enabled=!1,this.saveButton.visible=!1,this.offsetNum.enabled=!1):"change"==this.screenName&&(this.categoryNameTxtInput.enabled=!0,this.ordinalNumInput.enabled=!0,this.ruleAssignPriorityInput.enabled="Y"==this.pcmInputFlag,this.categoryIdTxtInput.enabled=!1,this.saveButton.visible=!0,this.instRelGroup.enabled=!0,this.actCheckbox.enabled=!0,this.assigMetGroup.enabled=!0,this.incTargCheckBox.enabled=!0,this.incLiqCheckBox.enabled=!0,this.addRuleButton.enabled=!0)},t.prototype.onLoad=function(){var e=this;try{this.spreadGrid=this.canvasSpreadGrid.addChild(r.hb),this.rulesGrid=this.canvasRulesGrid.addChild(r.hb),this.rulesGrid.onFilterChanged=this.disableButtons.bind(this),this.rulesGrid.onSortChanged=this.disableButtons.bind(this),this.requestParams=[],this.actionPath="categoryPCM.do?","add"!=this.screenName?(this.requestParams.categoryId=this.categoryId,this.actionMethod="method=view"):this.actionMethod="method=add",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.rulesGrid.onRowClick=function(t){e.cellClickEventHandler(t)},this.spreadGrid.cellLinkClick.subscribe(function(t){e.clickLinkHandler(t)}),this.swtModule.subscribeSpy([this.rulesGrid]),this.swtModule.onSpyChange.subscribe(function(t){e.ruleGridList=e.rulesGrid.changes})}catch(t){r.Wb.logError(t,this.moduleId,"CategoryMaintenance","onLoad",this.errorLocation)}},t.prototype.startOfComms=function(){this.disableComponents(!1)},t.prototype.endOfComms=function(){this.disableComponents(!0)},t.prototype.inputDataResult=function(e){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&this.jsonReader.getRequestReplyStatus()&&(this.paymentRequest="true"===this.jsonReader.getSingletons().existPayReq,this.helpURL=this.jsonReader.getSingletons().helpurl,!this.jsonReader.isDataBuilding()&&(this.fourEyesRequired=this.jsonReader.getScreenAttributes().fourEyesRequired,this.rulesGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridRules.metadata),this.spreadGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridSpread.metadata),this.jsonReader.getSingletons().ruleAssignPriority?(this.ruleAssignPriority=Number(this.jsonReader.getSingletons().ruleAssignPriority),this.ruleAssignPriorityInput.text=this.ruleAssignPriority):(this.ruleAssignPriorityInput.text=1,this.ruleAssignPriority=1),"add"==this.screenName&&(this.listOrderCR=[]),"add"!==this.screenName))){"change"===this.screenName?(this.instRelGroup.enabled=0==this.paymentRequest,this.relTimeGroup.enabled=0==this.paymentRequest,this.timeInput.enabled=0==this.paymentRequest):(this.useliqCheckBox.enabled=!1,this.actCheckbox.enabled=!1,this.incTargCheckBox.enabled=!1,this.incLiqCheckBox.enabled=!1,this.relTimeGroup.enabled=!1,this.instRelGroup.enabled=!1,this.assigMetGroup.enabled=!1,this.timeInput.enabled=!1);var t=this.jsonReader.getSingletons().maxOrdinalCR;""!=t&&(this.maxOrderCR=Number(t)),this.listOrderCR=this.jsonReader.getSingletons().ordinaListCR,this.listOrderCR=this.listOrderCR.replace("[","").replace("]",""),""!=this.listOrderCR?this.listOrderCR=this.listOrderCR.split(",").map(Number):this.listOrderCR=[];var i=this.listRuleAssignPriority.indexOf(Number(this.ruleAssignPriority));-1!==i&&this.listRuleAssignPriority.splice(i,1),null!==this.rulesGrid&&void 0!==this.rulesGrid||this.rulesGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridRules.metadata),this.rulesGrid.doubleClickEnabled=!1,this.rulesGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridRules.metadata),this.lastRecievedJSON.categoryRule.gridRules.rows.size>0&&(this.rulesGrid.gridData=this.lastRecievedJSON.categoryRule.gridRules.rows,this.rulesGrid.setRowSize=this.lastRecievedJSON.categoryRule.gridRules.rows.size),null!==this.spreadGrid&&void 0!==this.spreadGrid||this.spreadGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridSpread.metadata),this.spreadGrid.doubleClickEnabled=!1,this.spreadGrid.CustomGrid(this.lastRecievedJSON.categoryRule.gridSpread.metadata),this.lastRecievedJSON.categoryRule.gridSpread.rows.size>0?(this.spreadGrid.gridData=this.lastRecievedJSON.categoryRule.gridSpread.rows,this.spreadGrid.setRowSize=this.lastRecievedJSON.categoryRule.gridSpread.rows.size):(this.spreadGrid.dataProvider=null,this.spreadGrid.selectedIndex=-1),this.categoryNameTxtInput.text=this.jsonReader.getSingletons().categoryName,this.categoryIdTxtInput.text=this.jsonReader.getSingletons().categoryId,""!=this.jsonReader.getSingletons().ordinal.content&&(this.ordinal=Number(this.jsonReader.getSingletons().ordinal),this.ordinalNumInput.text=this.ordinal),this.assigMetGroup.selectedValue=this.jsonReader.getSingletons().assignMethod,this.instRelGroup.selectedValue=this.jsonReader.getSingletons().type,this.gridRowRelTime.visible=this.urgent.selected,this.gridRowRelTime.includeInLayout=this.urgent.selected,this.gridRowOffsetDays.visible=this.urgent.selected,this.gridRowOffsetDays.includeInLayout=this.urgent.selected,this.urgent.selected&&(this.relTimeGroup.selectedValue=this.jsonReader.getSingletons().setReleaseTime,this.radioT.selected&&(this.timeInput.visible=!0,this.timeInput.includeInLayout=!0,this.timeInput.text=this.jsonReader.getSingletons().specifiedTime),this.offsetNum.text=this.jsonReader.getSingletons().offsetDays?this.jsonReader.getSingletons().offsetDays:"",this.validateUseLiquidity()),this.incTargCheckBox.selected="Y"==this.jsonReader.getSingletons().inclTargetPercent,this.incLiqCheckBox.selected="Y"==this.jsonReader.getSingletons().inclInAvailableLiq,this.useliqCheckBox.selected="Y"==this.jsonReader.getSingletons().useLiqCheck,this.actCheckbox.selected="Y"==this.jsonReader.getSingletons().isActive,this.stateBeforeChange=this.actCheckbox.selected}}catch(l){console.log(l,this.moduleId,"CategoryMaintenance","inputDataResult"),r.Wb.logError(l,this.moduleId,"CategoryMaintenance","inputDataResult",this.errorLocation)}},t.prototype.inputDataFault=function(e){var t=r.Wb.getPredictMessage("label.genericException",null);this.swtAlert.error(t)},t.prototype.clickLinkHandler=function(e){e.target.name;try{this.goToSpreadProfileDetails()}catch(t){r.Wb.logError(t,this.moduleId,"CategoryMaintenance","clickLinkHandler",this.errorLocation)}},t.prototype.cellClickEventHandler=function(e){try{this.rulesGrid.selectedIndex>=0&&this.rulesGrid.selectable?("view"==this.screenName?this.disableOrEnableButtons(!1):this.disableOrEnableButtons(!0),this.viewRuleButton.enabled=!0):this.disableOrEnableButtons(!1),e.stopPropagation()}catch(t){r.Wb.logError(t,this.moduleId,"CategoryMaintenance","cellClickEventHandler",this.errorLocation)}},t.prototype.goToSpreadProfileDetails=function(){try{this.from="Spread",this.spreadId=this.spreadGrid.selectedItem.spreadId.content,r.x.call("openSpreadProfile","spreadProfilesAdd","view")}catch(e){r.Wb.logError(e,this.moduleId,"CategoryMaintenance","goToSearchTool",this.errorLocation)}},t.prototype.saveEventHandler=function(){r.c.okLabel="OK",0!=this.categoryIdTxtInput.text.length&&0!=this.categoryNameTxtInput.text.length&&this.ordinalNumInput.text&&this.ruleAssignPriorityInput.text?0!=this.categoryNameTxtInput.text.trim().length?(this.spread.selected||!this.radioT.selected||this.validateTime(this.timeInput))&&(this.validateUseLiquidity(),this.xmlData(),this.save()):this.swtAlert.warning("Category Name can not be saved as just spaces"):this.swtAlert.warning("Please fill all mandatory fields (marked with *)")},t.prototype.save=function(){var e=this;this.actionPath="categoryPCM.do?",this.logicUpdate.cbResult=function(t){e.logicUpdateResult(t)},this.logicUpdate.cbFault=this.inputDataFault.bind(this),this.logicUpdate.encodeURL=!1,this.requestParams=[];try{"add"==this.screenName?this.actionMethod="method=save":this.actionMethod="method=update","<operationsList/>"!=this.operationsList.toString()&&(this.requestParams.xmlData=this.operationsList.toString()),this.urgent.selected&&(this.requestParams.releaseTime=this.relTimeGroup.selectedValue,this.radioT.selected&&(this.requestParams.specifiedTime=this.timeInput.text)),this.requestParams.isActive=this.actCheckbox.selected?"Y":"N",this.requestParams.ordinal=this.ordinalNumInput.text?this.ordinalNumInput.text:"",this.requestParams.ruleAssignPriority=this.ruleAssignPriorityInput.text?this.ruleAssignPriorityInput.text:"1",this.requestParams.categoryId=this.categoryIdTxtInput.text,this.requestParams.categoryName=this.categoryNameTxtInput.text,this.requestParams.instantRelease=this.instRelGroup.selectedValue,this.requestParams.includeInTarget=this.incTargCheckBox.selected?"Y":"N",this.requestParams.includeInLiquidity=this.incLiqCheckBox.selected?"Y":"N",this.requestParams.useLiquidity=1==this.useliqCheckBox.selected?"Y":"N",this.requestParams.assignMethod=this.assigMetGroup.selectedValue,this.requestParams.offsetDays=this.offsetNum.text?this.offsetNum.text:"",this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams),this.operationsList=new r.hc("<operationsList/>")}catch(t){console.log(t,this.moduleId,"CategoryMaintenance","save"),r.Wb.logError(t,this.moduleId,"CategoryMaintenance","save",this.errorLocation)}},t.prototype.xmlData=function(){if(this.ruleGridList)for(var e=void 0,t=0;t<this.ruleGridList.getValues().length;t++)e=[],"add"!=this.screenName&&(e.RULE_ID=this.ruleGridList.getValues()[t].crud_data.ruleId,e.CATEGORY_RULE_ID=this.ruleGridList.getValues()[t].crud_data.categoryRuleId),e.APPLY_ONLY_TO_SOURCE="string"==typeof this.ruleGridList.getValues()[t].crud_data.toSource?this.ruleGridList.getValues()[t].crud_data.toSource:"",e.APPLY_ONLY_TO_CCY="string"==typeof this.ruleGridList.getValues()[t].crud_data.toCcy?this.ruleGridList.getValues()[t].crud_data.toCcy:"",e.APPLY_ONLY_TO_ENTITY="string"==typeof this.ruleGridList.getValues()[t].crud_data.toEntity?this.ruleGridList.getValues()[t].crud_data.toEntity:"",e.ORDINAL=this.ruleGridList.getValues()[t].crud_data.ordinal,e.NAME=this.ruleGridList.getValues()[t].crud_data.categoryRuleName,e.RULE_TYPE="G",e.RULE_QUERY=this.ruleGridList.getValues()[t].crud_data.ruleQuery,e.RULE_TEXT=this.ruleGridList.getValues()[t].crud_data.ruleText,e.TAB_CONDITION=this.ruleGridList.getValues()[t].crud_data.ruleConditions,"I"==this.ruleGridList.getValues()[t].crud_operation&&(this.operationsList.appendChild(r.Z.getKVTypeTabAsXML(e,"PC_CATEGORY_RULE","I","M")),this.changesInRule=!0),"U"==this.ruleGridList.getValues()[t].crud_operation.substring(0,1)&&(this.operationsList.appendChild(r.Z.getKVTypeTabAsXML(e,"PC_CATEGORY_RULE","U","M")),this.changesInRule=-1!=this.ruleGridList.getValues()[t].crud_operation.indexOf("ruleQuery")||-1!=this.ruleGridList.getValues()[t].crud_operation.indexOf("ruleText")||-1!=this.ruleGridList.getValues()[t].crud_operation.indexOf("ruleConditions")),"D"==this.ruleGridList.getValues()[t].crud_operation.substring(0,1)&&(this.operationsList.appendChild(r.Z.getKVTypeTabAsXML(e,"PC_CATEGORY_RULE","D","M")),this.changesInRule=!0)},t.prototype.logicUpdateResult=function(e){try{var t=void 0;if(this.logicUpdate.isBusy())this.logicUpdate.cbStop();else{var i=e,l=new r.L;l.setInputJSON(i),"RECORD_EXIST"==l.getRequestReplyMessage()?(t=r.Wb.getPredictMessage("errors.DataIntegrityViolationExceptioninAdd",null),this.swtAlert.error(t)):"ERROR_SAVE"==l.getRequestReplyMessage()?(t=r.Wb.getPredictMessage("alert.contactAdminForcategoryMaintenance",null),this.swtAlert.error(t)):"ERROR_NAME_RULE"==l.getRequestReplyMessage()?(t=r.Wb.getPredictMessage("errors.CouldNotSaveCategoryRuleNameWithSameNameExceptioninAdd",null),this.swtAlert.error(t)):window.opener.instanceElement&&(window.opener.instanceElement.updateData(),this.popupClosed())}}catch(n){r.Wb.logError(n,this.moduleId,"CategoryMaintenance","logicUpdateResult",this.errorLocation)}},t.prototype.disableOrEnableButtons=function(e){try{this.enableChangeButton(e),this.enableViewButton(e),this.enableDeleteButton(e)}catch(t){r.Wb.logError(t,this.moduleId,"CategoryMaintenance","disableOrEnableButtons",this.errorLocation)}},t.prototype.disableButtons=function(){-1==this.rulesGrid.selectedIndex?this.disableOrEnableButtons(!1):this.disableOrEnableButtons(!0)},t.prototype.enableButtons=function(){this.saveButton.enabled=!0},t.prototype.enableAddButton=function(e){this.addRuleButton.enabled=e,this.addRuleButton.buttonMode=e},t.prototype.enableChangeButton=function(e){this.changeRuleButton.enabled=e,this.changeRuleButton.buttonMode=e},t.prototype.enableViewButton=function(e){this.viewRuleButton.enabled=e,this.viewRuleButton.buttonMode=e},t.prototype.enableDeleteButton=function(e){this.deleteRuleButton.enabled=e,this.deleteRuleButton.buttonMode=e},t.prototype.refreshGrid=function(){this.rulesGrid.selectedIndex=-1,this.rulesGrid.selectable=!0,this.disableOrEnableButtons(!1)},t.prototype.disableComponents=function(e){},t.prototype.keyDownEventHandler=function(e){try{if(e){var t=Object(r.ic.getFocus()).name;e.keyCode==r.N.ENTER&&("saveButton"==t?this.checkPriorityBeforeSave():"cancelButton"==t&&this.popupClosed())}}catch(i){console.log(i,this.moduleId,"CategoryMaintenance","keyDownEventHandler")}},t.prototype.validateTime=function(e){return e.text.endsWith(":")&&(e.text=e.text+"00"),0==validateFormatTime(e)?(this.swtAlert.warning("Please enter a valid time",null),e.text="",!1):(e.text=e.text.substring(0,5),!0)},t.prototype.validateUseLiquidity=function(){if("view"!==this.screenName){if(""!==this.offsetNum.text)return this.offsetNum.text>=1?(this.useliqCheckBox.enabled=!1,this.useliqCheckBox.selected=!1,!1):(this.useliqCheckBox.enabled=!0,!0);this.useliqCheckBox.enabled=!0}},t.prototype.ordinalAlertListener=function(e){try{if(e.detail===r.c.YES)this.reOrder=!0;else if(this.reOrder=!1,"add"==this.screenName)this.ordinalNumInput.text=this.maxOrder+1;else{var t=String(this.jsonReader.getSingletons().ordinal);this.ordinalNumInput.text=""==t?this.maxOrder+1:this.jsonReader.getSingletons().ordinal}}catch(i){console.log(i,this.moduleId,"CategoryMaintenance","ordinalAlertListener")}},t.prototype.changeInstantRelease=function(){this.urgent.selected?this.validateUseLiquidity():this.useliqCheckBox.enabled=!0,this.gridRowRelTime.visible=this.urgent.selected,this.gridRowRelTime.includeInLayout=this.urgent.selected,this.gridRowOffsetDays.visible=this.urgent.selected,this.gridRowOffsetDays.includeInLayout=this.urgent.selected,this.spread.selected&&(this.radioT.selected=!1,this.timeInput.visible=!1,this.timeInput.includeInLayout=!1,this.radioK.selected=!0)},t.prototype.activeEventHandler=function(){},t.prototype.doAddCategoryRule=function(e){try{this.rulesGrid.selectedIndex=-1,this.childScreenName="add",this.saveButton.enabled=!1,r.x.call("openChildWindow","categoryRuleAdd")}catch(t){console.log(t,this.moduleId,"CategoryMaintenance","doAddRule")}},t.prototype.doChangeCategoryRule=function(e){var t=this;try{var i=this.rulesGrid.dataProvider.findIndex(function(e){return e.num==t.rulesGrid.selectedItem.num.content});this.childScreenName="change",this.categoryRuleId=this.rulesGrid.dataProvider[i].categoryRuleId,this.saveButton.enabled=!1,r.x.call("openChildWindow","categoryRuleAdd")}catch(l){console.log(l,this.moduleId,"CategoryMaintenance","doChangeRule")}},t.prototype.doViewCategoryRule=function(e){var t=this;try{var i=this.rulesGrid.dataProvider.findIndex(function(e){return e.num==t.rulesGrid.selectedItem.num.content});this.childScreenName="view",this.categoryRuleId=this.rulesGrid.dataProvider[i].categoryRuleId,r.x.call("openChildWindow","categoryRuleAdd")}catch(l){console.log(l,this.moduleId,"CategoryMaintenance","doViewCategoryRule")}},t.prototype.refreshParent=function(e,t,i,l,n,r,a,s,o,u,d,c,h){var b=this;try{var g=void 0,p=[];if(1==e){for(var m=0;m<this.rulesGrid.dataProvider.length;m++)Number(this.rulesGrid.dataProvider[m].ordinal)>=Number(n)&&Number(this.rulesGrid.dataProvider[m].ordinal)!=t&&(this.rulesGrid.dataProvider[m].ordinal=Number(this.rulesGrid.dataProvider[m].ordinal)+1,this.rulesGrid.dataProvider[m].slickgrid_rowcontent.ordinal={content:Number(this.rulesGrid.dataProvider[m].ordinal)+1},p.push(Number(this.rulesGrid.dataProvider[m].ordinal)));this.listOrderCR=p}if("add"==this.childScreenName){var y={categoryRuleName:{content:l},ordinal:{content:String(n)},toSource:{content:String(r)},toCcy:{content:String(a)},toEntity:{content:String(s)},ruleText:{content:String(u)},ruleQuery:{content:String(d)},tabToJoin:{content:String(h)},ruleConditions:{content:c}};this.rulesGrid.appendRow(y)}else g=this.rulesGrid.dataProvider.findIndex(function(e){return e.num==b.rulesGrid.selectedItem.num.content}),this.rulesGrid.dataProvider[g].categoryRuleId=Number(i),this.rulesGrid.dataProvider[g].ruleId=o,this.rulesGrid.dataProvider[g].categoryRuleName=l,this.rulesGrid.dataProvider[g].ordinal=n,this.rulesGrid.dataProvider[g].toSource=r,this.rulesGrid.dataProvider[g].toCcy=a,this.rulesGrid.dataProvider[g].toEntity=s,this.rulesGrid.dataProvider[g].ruleText=u,this.rulesGrid.dataProvider[g].ruleQuery=d,this.rulesGrid.dataProvider[g].tabToJoin=h,this.rulesGrid.dataProvider[g].ruleConditions=c,this.rulesGrid.dataProvider[g].slickgrid_rowcontent.categoryRuleName={content:l},this.rulesGrid.dataProvider[g].slickgrid_rowcontent.ordinal={content:n},this.rulesGrid.dataProvider[g].slickgrid_rowcontent.toSource={content:r},this.rulesGrid.dataProvider[g].slickgrid_rowcontent.toCcy={content:a},this.rulesGrid.dataProvider[g].slickgrid_rowcontent.toEntity={content:s},this.rulesGrid.dataProvider[g].slickgrid_rowcontent.ruleText={content:u},this.rulesGrid.dataProvider[g].slickgrid_rowcontent.ruleQuery={content:d},this.rulesGrid.dataProvider[g].slickgrid_rowcontent.tabToJoin={content:h},this.rulesGrid.dataProvider[g].slickgrid_rowcontent.ruleConditions={content:c},this.rulesGrid.refresh();this.listOrderCR.push(Number(n)),this.maxOrderCR=Math.max.apply(null,this.listOrderCR),this.maxOrderCR+=1,this.tableToJoinQueryBuilder=JSON.parse(h),this.rulesGrid.selectedIndex=-1}catch(R){console.log(R,this.moduleId,"CategoryMaintenance","refreshParent")}},t.prototype.getParamsFromParent=function(){var e=this,t=[];if("Spread"==this.from)this.rulesGrid.selectedIndex=-1,this.spreadId=this.spreadGrid.selectedItem.spreadId.content,t=[{screenName:"view",spreadId:this.spreadId}],this.from="";else if("add"==this.childScreenName)t=[{screenName:this.childScreenName,categoryRuleId:"",ruleId:"",dataProviderSelectedIndex:[],maxOrder:this.maxOrderCR,listOrder:this.listOrderCR,listPriority:this.listRuleAssignPriority}];else{var i=this.rulesGrid.dataProvider.findIndex(function(t){return t.num==e.rulesGrid.selectedItem.num.content});this.categoryRuleId=this.rulesGrid.dataProvider[i].categoryRuleId,this.ruleId=this.rulesGrid.dataProvider[i].ruleId,t=[{screenName:this.childScreenName,categoryRuleId:this.categoryRuleId,ruleId:this.ruleId,dataProviderSelectedIndex:this.rulesGrid.dataProvider[i],maxOrder:this.maxOrderCR,listOrder:this.listOrderCR,listPriority:this.listRuleAssignPriority}]}return t},t.prototype.doDeleteCategoryRule=function(e){try{r.c.yesLabel="Yes",r.c.noLabel="No";this.swtAlert.confirm("Do you wish to delete this row?","Alert",r.c.OK|r.c.CANCEL,null,this.deleteRule.bind(this))}catch(t){r.Wb.logError(t,this.moduleId,"CategoryMaintenance","doDeleteRule",this.errorLocation)}},t.prototype.deleteRule=function(e){var t=this;try{var i=this.rulesGrid.dataProvider.findIndex(function(e){return e.num==t.rulesGrid.selectedItem.num.content});if(e.detail===r.c.OK){var l=this.rulesGrid.dataProvider[i].ordinal;this.removeElement(this.listOrderCR,l),0==this.listOrderCR.length?this.maxOrderCR=1:(this.maxOrderCR=Math.max.apply(null,this.listOrderCR),this.maxOrderCR++),this.rulesGrid.removeSelected(),this.rulesGrid.selectedIndex=-1,this.disableOrEnableButtons(!1)}}catch(n){r.Wb.logError(n,this.moduleId,"CategoryMaintenance","deleteRule",this.errorLocation)}},t.prototype.removeElement=function(e,t){var i=e.indexOf(Number(t));i>=-1&&(e.splice(i,1),this.listOrderCR=e)},t.prototype.changeTimeGroup=function(e){this.radioT.selected?this.timeInput.visible=!0:(this.timeInput.visible=!1,this.timeInput.text="")},t.prototype.popupClosed=function(){this.dispose()},t.prototype.dispose=function(){try{this.requestParams=null,this.baseURL=null,this.actionMethod=null,this.actionPath=null,window.opener.instanceElement&&window.opener.instanceElement.refreshGrid(),this.titleWindow?this.close():window.close()}catch(e){console.log(e,this.moduleId,"CategoryMaintenance","dispose")}},t.prototype.checkIfPriorityExists=function(){r.c.yesLabel=r.Wb.getPredictMessage("alert.yes.label"),r.c.noLabel=r.Wb.getPredictMessage("alert.no.label");var e=r.Wb.getPredictMessage("alert.CategoryMaintenanceAddRulePriority",null);this.ruleAssignPriorityInput.text&&("0"==this.ruleAssignPriorityInput.text?this.ruleAssignPriorityInput.text=this.ruleAssignPriority?this.ruleAssignPriority:"1":"N"!=this.pcmInputFlag&&-1!==this.listRuleAssignPriority.indexOf(Number(this.ruleAssignPriorityInput.text))&&this.swtAlert.confirm(e,r.Wb.getPredictMessage("alert_header.confirm"),r.c.YES|r.c.NO,null,this.priorityAlertListener.bind(this)))},t.prototype.priorityAlertListener=function(e){e.detail===r.c.NO&&(this.ruleAssignPriorityInput.text=this.ruleAssignPriority?this.ruleAssignPriority:"1")},t.prototype.checkPriorityBeforeSave=function(){r.c.yesLabel=r.Wb.getPredictMessage("alert.yes.label"),r.c.noLabel=r.Wb.getPredictMessage("alert.no.label");var e=r.Wb.getPredictMessage("alert.CategoryMaintenanceAddRulePriority",null);this.ruleAssignPriorityInput.text&&"N"!=this.pcmInputFlag&&-1!==this.listRuleAssignPriority.indexOf(Number(this.ruleAssignPriorityInput.text))?this.swtAlert.confirm(e,r.Wb.getPredictMessage("alert_header.confirm"),r.c.YES|r.c.NO,null,this.savePriorityAlertListener.bind(this)):this.saveEventHandler()},t.prototype.savePriorityAlertListener=function(e){e.detail===r.c.NO?this.ruleAssignPriorityInput.text=this.ruleAssignPriority?this.ruleAssignPriority:"1":this.saveEventHandler()},t}(r.yb),o=[{path:"",component:s}],u=(a.l.forChild(o),function(){return function(){}}()),d=i("pMnS"),c=i("RChO"),h=i("t6HQ"),b=i("WFGK"),g=i("5FqG"),p=i("Ip0R"),m=i("gIcY"),y=i("t/Na"),R=i("sE5F"),I=i("OzfB"),w=i("T7CS"),v=i("S7LP"),f=i("6aHO"),C=i("WzUx"),x=i("A7o+"),G=i("zCE2"),P=i("Jg5P"),M=i("3R0m"),B=i("hhbb"),N=i("5rxC"),T=i("Fzqc"),A=i("21Lb"),L=i("hUWP"),S=i("3pJQ"),k=i("V9q+"),O=i("VDKW"),D=i("kXfT"),J=i("BGbe");i.d(t,"CategoryMaintenanceAddModuleNgFactory",function(){return _}),i.d(t,"RenderType_CategoryMaintenanceAdd",function(){return E}),i.d(t,"View_CategoryMaintenanceAdd_0",function(){return W}),i.d(t,"View_CategoryMaintenanceAdd_Host_0",function(){return Z}),i.d(t,"CategoryMaintenanceAddNgFactory",function(){return U});var _=l.Gb(u,[],function(e){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[d.a,c.a,h.a,b.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,U]],[3,l.n],l.J]),l.Rb(4608,p.m,p.l,[l.F,[2,p.u]]),l.Rb(4608,m.c,m.c,[]),l.Rb(4608,m.p,m.p,[]),l.Rb(4608,y.j,y.p,[p.c,l.O,y.n]),l.Rb(4608,y.q,y.q,[y.j,y.o]),l.Rb(5120,y.a,function(e){return[e,new r.tb]},[y.q]),l.Rb(4608,y.m,y.m,[]),l.Rb(6144,y.k,null,[y.m]),l.Rb(4608,y.i,y.i,[y.k]),l.Rb(6144,y.b,null,[y.i]),l.Rb(4608,y.f,y.l,[y.b,l.B]),l.Rb(4608,y.c,y.c,[y.f]),l.Rb(4608,R.c,R.c,[]),l.Rb(4608,R.g,R.b,[]),l.Rb(5120,R.i,R.j,[]),l.Rb(4608,R.h,R.h,[R.c,R.g,R.i]),l.Rb(4608,R.f,R.a,[]),l.Rb(5120,R.d,R.k,[R.h,R.f]),l.Rb(5120,l.b,function(e,t){return[I.j(e,t)]},[p.c,l.O]),l.Rb(4608,w.a,w.a,[]),l.Rb(4608,v.a,v.a,[]),l.Rb(4608,f.a,f.a,[l.n,l.L,l.B,v.a,l.g]),l.Rb(4608,C.c,C.c,[l.n,l.g,l.B]),l.Rb(4608,C.e,C.e,[C.c]),l.Rb(4608,x.l,x.l,[]),l.Rb(4608,x.h,x.g,[]),l.Rb(4608,x.c,x.f,[]),l.Rb(4608,x.j,x.d,[]),l.Rb(4608,x.b,x.a,[]),l.Rb(4608,x.k,x.k,[x.l,x.h,x.c,x.j,x.b,x.m,x.n]),l.Rb(4608,C.i,C.i,[[2,x.k]]),l.Rb(4608,C.r,C.r,[C.L,[2,x.k],C.i]),l.Rb(4608,C.t,C.t,[]),l.Rb(4608,C.w,C.w,[]),l.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),l.Rb(1073742336,p.b,p.b,[]),l.Rb(1073742336,m.n,m.n,[]),l.Rb(1073742336,m.l,m.l,[]),l.Rb(1073742336,G.a,G.a,[]),l.Rb(1073742336,P.a,P.a,[]),l.Rb(1073742336,m.e,m.e,[]),l.Rb(1073742336,M.a,M.a,[]),l.Rb(1073742336,x.i,x.i,[]),l.Rb(1073742336,C.b,C.b,[]),l.Rb(1073742336,y.e,y.e,[]),l.Rb(1073742336,y.d,y.d,[]),l.Rb(1073742336,R.e,R.e,[]),l.Rb(1073742336,B.b,B.b,[]),l.Rb(1073742336,N.b,N.b,[]),l.Rb(1073742336,I.c,I.c,[]),l.Rb(1073742336,T.a,T.a,[]),l.Rb(1073742336,A.d,A.d,[]),l.Rb(1073742336,L.c,L.c,[]),l.Rb(1073742336,S.a,S.a,[]),l.Rb(1073742336,k.a,k.a,[[2,I.g],l.O]),l.Rb(1073742336,O.b,O.b,[]),l.Rb(1073742336,D.a,D.a,[]),l.Rb(1073742336,J.b,J.b,[]),l.Rb(1073742336,r.Tb,r.Tb,[]),l.Rb(1073742336,u,u,[]),l.Rb(256,y.n,"XSRF-TOKEN",[]),l.Rb(256,y.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,x.m,void 0,[]),l.Rb(256,x.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,a.i,function(){return[[{path:"",component:s}]]},[])])}),q=[[".labelForm[_ngcontent-%COMP%]{width:140px!important;height:19px!important}.numericInput[_ngcontent-%COMP%]{font-size:11px!important;height:22px!important}"]],E=l.Hb({encapsulation:0,styles:q,data:{}});function W(e){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{categoryNameTxtInput:0}),l.Zb(402653184,3,{categoryIdTxtInput:0}),l.Zb(402653184,4,{ordinalNumInput:0}),l.Zb(402653184,5,{ruleAssignPriorityInput:0}),l.Zb(402653184,6,{timeInput:0}),l.Zb(402653184,7,{offsetNum:0}),l.Zb(402653184,8,{categoryNameLabel:0}),l.Zb(402653184,9,{categoryIdLabel:0}),l.Zb(402653184,10,{orderLabel:0}),l.Zb(402653184,11,{instRelGroupLabel:0}),l.Zb(402653184,12,{labelRelTime:0}),l.Zb(402653184,13,{labelOffsetDays:0}),l.Zb(402653184,14,{assigMetLabel:0}),l.Zb(402653184,15,{incTargLabel:0}),l.Zb(402653184,16,{useliqLabel:0}),l.Zb(402653184,17,{incLiqLabel:0}),l.Zb(402653184,18,{ruleAssignPriorityLabel:0}),l.Zb(402653184,19,{saveButton:0}),l.Zb(402653184,20,{cancelButton:0}),l.Zb(402653184,21,{addRuleButton:0}),l.Zb(402653184,22,{changeRuleButton:0}),l.Zb(402653184,23,{viewRuleButton:0}),l.Zb(402653184,24,{deleteRuleButton:0}),l.Zb(402653184,25,{incTargCheckBox:0}),l.Zb(402653184,26,{incLiqCheckBox:0}),l.Zb(402653184,27,{actCheckbox:0}),l.Zb(402653184,28,{useliqCheckBox:0}),l.Zb(402653184,29,{instRelGroup:0}),l.Zb(402653184,30,{assigMetGroup:0}),l.Zb(402653184,31,{relTimeGroup:0}),l.Zb(402653184,32,{urgent:0}),l.Zb(402653184,33,{spread:0}),l.Zb(402653184,34,{radioB:0}),l.Zb(402653184,35,{radioM:0}),l.Zb(402653184,36,{radioS:0}),l.Zb(402653184,37,{radioK:0}),l.Zb(402653184,38,{radioU:0}),l.Zb(402653184,39,{radioI:0}),l.Zb(402653184,40,{radioT:0}),l.Zb(402653184,41,{queryText:0}),l.Zb(402653184,42,{canvasSpreadGrid:0}),l.Zb(402653184,43,{canvasRulesGrid:0}),l.Zb(402653184,44,{panelSpreadGrid:0}),l.Zb(402653184,45,{panelRulesGrid:0}),l.Zb(402653184,46,{gridRowRelTime:0}),l.Zb(402653184,47,{gridRowOffsetDays:0}),l.Zb(402653184,48,{swtModule:0}),(e()(),l.Jb(48,0,null,null,176,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,i){var l=!0,n=e.component;"creationComplete"===t&&(l=!1!==n.onLoad()&&l);return l},g.ad,g.hb)),l.Ib(49,4440064,[[48,4],["swtModule",4]],0,r.yb,[l.r,r.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),l.Jb(50,0,null,0,174,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),l.Ib(51,4440064,null,0,r.ec,[l.r,r.i,l.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(e()(),l.Jb(52,0,null,0,164,"SwtCanvas",[["height","93%"],["width","100%"]],null,null,null,g.Nc,g.U)),l.Ib(53,4440064,null,0,r.db,[l.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(54,0,null,0,162,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),l.Ib(55,4440064,null,0,r.ec,[l.r,r.i,l.T],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(56,0,null,0,138,"Grid",[["height","42%"],["marginTop","3"],["width","100%"]],null,null,null,g.Cc,g.H)),l.Ib(57,4440064,null,0,r.z,[l.r,r.i],{width:[0,"width"],height:[1,"height"],marginTop:[2,"marginTop"]},null),(e()(),l.Jb(58,0,null,0,21,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(59,4440064,null,0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(60,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,g.Ac,g.I)),l.Ib(61,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(62,0,null,0,1,"SwtLabel",[["id","categoryIdLabel"]],null,null,null,g.Yc,g.fb)),l.Ib(63,4440064,[[9,4],["categoryIdLabel",4]],0,r.vb,[l.r,r.i],{id:[0,"id"]},null),(e()(),l.Jb(64,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),l.Ib(65,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(66,0,null,0,1,"SwtTextInput",[["id","categoryIdTxtInput"],["maxChars","20"],["required","true"],["restrict","a-zA-Z0-9\\-_"],["width","200"]],null,null,null,g.kd,g.sb)),l.Ib(67,4440064,[[3,4],["categoryIdTxtInput",4]],0,r.Rb,[l.r,r.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],width:[3,"width"],required:[4,"required"]},null),(e()(),l.Jb(68,0,null,0,3,"GridItem",[["width","6%"]],null,null,null,g.Ac,g.I)),l.Ib(69,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(70,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(71,4440064,[[8,4],["categoryNameLabel",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(72,0,null,0,3,"GridItem",[["width","45%"]],null,null,null,g.Ac,g.I)),l.Ib(73,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(74,0,null,0,1,"SwtTextInput",[["id","categoryNameTxtInput"],["maxChars","50"],["required","true"],["restrict","A-Za-z0-9\\d_ !\\\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^`{|}~"],["width","400"]],null,null,null,g.kd,g.sb)),l.Ib(75,4440064,[[2,4],["categoryNameTxtInput",4]],0,r.Rb,[l.r,r.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],width:[3,"width"],required:[4,"required"]},null),(e()(),l.Jb(76,0,null,0,3,"GridItem",[["width","8%"]],null,null,null,g.Ac,g.I)),l.Ib(77,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(78,0,null,0,1,"SwtCheckBox",[["id","actCheckbox"],["selected","true"],["styleName","checkbox"],["value","Y"]],null,null,null,g.Oc,g.V)),l.Ib(79,4440064,[[27,4],["actCheckbox",4]],0,r.eb,[l.r,r.i],{id:[0,"id"],styleName:[1,"styleName"],value:[2,"value"],selected:[3,"selected"]},null),(e()(),l.Jb(80,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(81,4440064,null,0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(82,0,null,0,3,"GridItem",[["width","15%"]],null,null,null,g.Ac,g.I)),l.Ib(83,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(84,0,null,0,1,"SwtLabel",[["id","orderLabel"]],null,null,null,g.Yc,g.fb)),l.Ib(85,4440064,[[10,4],["orderLabel",4]],0,r.vb,[l.r,r.i],{id:[0,"id"]},null),(e()(),l.Jb(86,0,null,0,3,"GridItem",[["width","85%"]],null,null,null,g.Ac,g.I)),l.Ib(87,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(88,0,null,0,1,"SwtNumericInput",[["class","numericInput"],["id","ordinalNumInput"],["required","true"],["textAlign","right"],["width","60"]],null,null,null,g.cd,g.jb)),l.Ib(89,4440064,[[4,4],["ordinalNumInput",4]],0,r.Ab,[l.r,r.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"],required:[3,"required"]},null),(e()(),l.Jb(90,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(91,4440064,null,0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(92,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),l.Ib(93,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(94,0,null,0,1,"SwtLabel",[["id","ruleAssignPriorityLabel"]],null,null,null,g.Yc,g.fb)),l.Ib(95,4440064,[[18,4],["ruleAssignPriorityLabel",4]],0,r.vb,[l.r,r.i],{id:[0,"id"]},null),(e()(),l.Jb(96,0,null,0,3,"GridItem",[["width","75%"]],null,null,null,g.Ac,g.I)),l.Ib(97,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(98,0,null,0,1,"SwtNumericInput",[["class","numericInput"],["id","ruleAssignPriorityInput"],["maxChars","3"],["required","true"],["textAlign","right"],["width","60"]],null,[[null,"focusOut"]],function(e,t,i){var l=!0,n=e.component;"focusOut"===t&&(l=!1!==n.checkIfPriorityExists()&&l);return l},g.cd,g.jb)),l.Ib(99,4440064,[[5,4],["ruleAssignPriorityInput",4]],0,r.Ab,[l.r,r.i],{maxChars:[0,"maxChars"],id:[1,"id"],textAlign:[2,"textAlign"],width:[3,"width"],required:[4,"required"]},{onFocusOut_:"focusOut"}),(e()(),l.Jb(100,0,null,0,14,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(101,4440064,null,0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(102,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),l.Ib(103,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(104,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(105,4440064,[[11,4],["instRelGroupLabel",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(106,0,null,0,8,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),l.Ib(107,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(108,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","instRelGroup"],["width","100%"]],null,[[null,"change"]],function(e,t,i){var l=!0,n=e.component;"change"===t&&(l=!1!==n.changeInstantRelease()&&l);return l},g.ed,g.lb)),l.Ib(109,4440064,[[29,4],["instRelGroup",4]],1,r.Hb,[y.c,l.r,r.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),l.Zb(603979776,49,{radioItems:1}),(e()(),l.Jb(111,0,null,0,1,"SwtRadioItem",[["groupName","instRelGroup"],["id","urgent"],["value","U"],["width","120"]],null,null,null,g.fd,g.mb)),l.Ib(112,4440064,[[49,4],[32,4],["urgent",4]],0,r.Ib,[l.r,r.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(e()(),l.Jb(113,0,null,0,1,"SwtRadioItem",[["groupName","instRelGroup"],["id","spread"],["selected","true"],["value","S"],["width","120"]],null,null,null,g.fd,g.mb)),l.Ib(114,4440064,[[49,4],[33,4],["spread",4]],0,r.Ib,[l.r,r.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(e()(),l.Jb(115,0,null,0,22,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(116,4440064,[[46,4],["gridRowRelTime",4]],0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(117,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),l.Ib(118,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(119,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(120,4440064,[[12,4],["labelRelTime",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(121,0,null,0,12,"GridItem",[["width","55%"]],null,null,null,g.Ac,g.I)),l.Ib(122,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(123,0,null,0,10,"SwtRadioButtonGroup",[["align","horizontal"],["id","relTimeGroup"],["width","100%"]],null,[[null,"change"]],function(e,t,i){var l=!0,n=e.component;"change"===t&&(l=!1!==n.changeTimeGroup(i)&&l);return l},g.ed,g.lb)),l.Ib(124,4440064,[[31,4],["relTimeGroup",4]],1,r.Hb,[y.c,l.r,r.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),l.Zb(603979776,50,{radioItems:1}),(e()(),l.Jb(126,0,null,0,1,"SwtRadioItem",[["groupName","relTimeGroup"],["id","radioK"],["selected","true"],["value","K"],["width","120"]],null,null,null,g.fd,g.mb)),l.Ib(127,4440064,[[50,4],[37,4],["radioK",4]],0,r.Ib,[l.r,r.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(e()(),l.Jb(128,0,null,0,1,"SwtRadioItem",[["groupName","relTimeGroup"],["id","radioU"],["value","U"],["width","160"]],null,null,null,g.fd,g.mb)),l.Ib(129,4440064,[[50,4],[38,4],["radioU",4]],0,r.Ib,[l.r,r.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(e()(),l.Jb(130,0,null,0,1,"SwtRadioItem",[["groupName","relTimeGroup"],["id","radioI"],["value","I"],["width","100"]],null,null,null,g.fd,g.mb)),l.Ib(131,4440064,[[50,4],[39,4],["radioI",4]],0,r.Ib,[l.r,r.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(e()(),l.Jb(132,0,null,0,1,"SwtRadioItem",[["groupName","relTimeGroup"],["id","radioT"],["value","T"],["width","100"]],null,null,null,g.fd,g.mb)),l.Ib(133,4440064,[[50,4],[40,4],["radioT",4]],0,r.Ib,[l.r,r.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(e()(),l.Jb(134,0,null,0,3,"GridItem",[["width","10%"]],null,null,null,g.Ac,g.I)),l.Ib(135,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(136,0,null,0,1,"SwtTextInput",[["class","numericInput"],["id","timeInput"],["maxChars","5"],["pattern","^(0[0-9]|1[0-9]|2[0-3]|[0-9]):[0-5][0-9]$"],["required","true"],["textAlign","center"],["width","50"]],null,[[null,"focusOut"]],function(e,t,i){var n=!0,r=e.component;"focusOut"===t&&(n=!1!==r.validateTime(l.Tb(e,137))&&n);return n},g.kd,g.sb)),l.Ib(137,4440064,[[6,4],["timeInput",4]],0,r.Rb,[l.r,r.i],{maxChars:[0,"maxChars"],id:[1,"id"],textAlign:[2,"textAlign"],width:[3,"width"],required:[4,"required"]},{onFocusOut_:"focusOut"}),(e()(),l.Jb(138,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(139,4440064,[[47,4],["gridRowOffsetDays",4]],0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(140,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),l.Ib(141,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(142,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(143,4440064,[[13,4],["labelOffsetDays",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(144,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),l.Ib(145,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(146,0,null,0,1,"SwtNumericInput",[["class","numericInput"],["id","offsetNum"],["maxChars","4"],["textAlign","right"],["width","60"]],null,[[null,"focusOut"]],function(e,t,i){var l=!0,n=e.component;"focusOut"===t&&(l=!1!==n.validateUseLiquidity()&&l);return l},g.cd,g.jb)),l.Ib(147,4440064,[[7,4],["offsetNum",4]],0,r.Ab,[l.r,r.i],{maxChars:[0,"maxChars"],id:[1,"id"],textAlign:[2,"textAlign"],width:[3,"width"]},{onFocusOut_:"focusOut"}),(e()(),l.Jb(148,0,null,0,16,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(149,4440064,null,0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(150,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),l.Ib(151,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(152,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(153,4440064,[[14,4],["assigMetLabel",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(154,0,null,0,10,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),l.Ib(155,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(156,0,null,0,8,"SwtRadioButtonGroup",[["align","horizontal"],["id","assigMetGroup"]],null,null,null,g.ed,g.lb)),l.Ib(157,4440064,[[30,4],["assigMetGroup",4]],1,r.Hb,[y.c,l.r,r.i],{id:[0,"id"],align:[1,"align"]},null),l.Zb(603979776,51,{radioItems:1}),(e()(),l.Jb(159,0,null,0,1,"SwtRadioItem",[["groupName","assigMetGroup"],["id","radioM"],["value","M"],["width","120"]],null,null,null,g.fd,g.mb)),l.Ib(160,4440064,[[51,4],[35,4],["radioM",4]],0,r.Ib,[l.r,r.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(e()(),l.Jb(161,0,null,0,1,"SwtRadioItem",[["groupName","assigMetGroup"],["id","radioS"],["value","S"],["width","160"]],null,null,null,g.fd,g.mb)),l.Ib(162,4440064,[[51,4],[36,4],["radioS",4]],0,r.Ib,[l.r,r.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(e()(),l.Jb(163,0,null,0,1,"SwtRadioItem",[["groupName","assigMetGroup"],["id","radioB"],["selected","true"],["value","B"],["width","120"]],null,null,null,g.fd,g.mb)),l.Ib(164,4440064,[[51,4],[34,4],["radioB",4]],0,r.Ib,[l.r,r.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(e()(),l.Jb(165,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(166,4440064,null,0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(167,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),l.Ib(168,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(169,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(170,4440064,[[16,4],["useliqLabel",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(171,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),l.Ib(172,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(173,0,null,0,1,"SwtCheckBox",[["id","useliqCheckBox"],["selected","false"],["styleName","checkbox"],["value","N"]],null,null,null,g.Oc,g.V)),l.Ib(174,4440064,[[28,4],["useliqCheckBox",4]],0,r.eb,[l.r,r.i],{id:[0,"id"],styleName:[1,"styleName"],value:[2,"value"],selected:[3,"selected"]},null),(e()(),l.Jb(175,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(176,4440064,null,0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(177,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),l.Ib(178,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(179,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(180,4440064,[[15,4],["incTargLabel",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(181,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),l.Ib(182,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(183,0,null,0,1,"SwtCheckBox",[["id","incTargCheckBox"],["selected","true"],["styleName","checkbox"],["value","Y"]],null,null,null,g.Oc,g.V)),l.Ib(184,4440064,[[25,4],["incTargCheckBox",4]],0,r.eb,[l.r,r.i],{id:[0,"id"],styleName:[1,"styleName"],value:[2,"value"],selected:[3,"selected"]},null),(e()(),l.Jb(185,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),l.Ib(186,4440064,null,0,r.B,[l.r,r.i],null,null),(e()(),l.Jb(187,0,null,0,3,"GridItem",[["width","25%"]],null,null,null,g.Ac,g.I)),l.Ib(188,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(189,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(190,4440064,[[17,4],["incLiqLabel",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(191,0,null,0,3,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),l.Ib(192,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(193,0,null,0,1,"SwtCheckBox",[["id","incLiqCheckBox"],["selected","true"],["styleName","checkbox"],["value","Y"]],null,null,null,g.Oc,g.V)),l.Ib(194,4440064,[[26,4],["incLiqCheckBox",4]],0,r.eb,[l.r,r.i],{id:[0,"id"],styleName:[1,"styleName"],value:[2,"value"],selected:[3,"selected"]},null),(e()(),l.Jb(195,0,null,0,15,"SwtPanel",[["height","30%"],["id","panelRulesGrid"],["width","100%"]],null,null,null,g.dd,g.kb)),l.Ib(196,4440064,[[45,4],["panelRulesGrid",4]],0,r.Cb,[l.r,r.i,l.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(197,0,null,0,13,"HBox",[["height","100%"],["paddingBottom","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.Dc,g.K)),l.Ib(198,4440064,null,0,r.C,[l.r,r.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"]},null),(e()(),l.Jb(199,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","canvasRulesGrid"],["width","90%"]],null,null,null,g.Nc,g.U)),l.Ib(200,4440064,[[43,4],["canvasRulesGrid",4]],0,r.db,[l.r,r.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(e()(),l.Jb(201,0,null,0,9,"VBox",[["height","100%"],["horizontalAlign","center"],["verticalAlign","middle"],["width","10%"]],null,null,null,g.od,g.vb)),l.Ib(202,4440064,null,0,r.ec,[l.r,r.i,l.T],{horizontalAlign:[0,"horizontalAlign"],verticalAlign:[1,"verticalAlign"],width:[2,"width"],height:[3,"height"]},null),(e()(),l.Jb(203,0,null,0,1,"SwtButton",[["id","addRuleButton"],["marginBottom","6"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.doAddCategoryRule(i)&&l);"keyDown"===t&&(l=!1!==n.keyDownEventHandler(i)&&l);return l},g.Mc,g.T)),l.Ib(204,4440064,[[21,4],["addRuleButton",4]],0,r.cb,[l.r,r.i],{id:[0,"id"],width:[1,"width"],marginBottom:[2,"marginBottom"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),l.Jb(205,0,null,0,1,"SwtButton",[["id","changeRuleButton"],["marginBottom","6"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.doChangeCategoryRule(i)&&l);"keyDown"===t&&(l=!1!==n.keyDownEventHandler(i)&&l);return l},g.Mc,g.T)),l.Ib(206,4440064,[[22,4],["changeRuleButton",4]],0,r.cb,[l.r,r.i],{id:[0,"id"],width:[1,"width"],marginBottom:[2,"marginBottom"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),l.Jb(207,0,null,0,1,"SwtButton",[["id","viewRuleButton"],["marginBottom","6"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.doViewCategoryRule(i)&&l);"keyDown"===t&&(l=!1!==n.keyDownEventHandler(i)&&l);return l},g.Mc,g.T)),l.Ib(208,4440064,[[23,4],["viewRuleButton",4]],0,r.cb,[l.r,r.i],{id:[0,"id"],width:[1,"width"],marginBottom:[2,"marginBottom"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),l.Jb(209,0,null,0,1,"SwtButton",[["id","deleteRuleButton"],["width","60"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.doDeleteCategoryRule(i)&&l);"keyDown"===t&&(l=!1!==n.keyDownEventHandler(i)&&l);return l},g.Mc,g.T)),l.Ib(210,4440064,[[24,4],["deleteRuleButton",4]],0,r.cb,[l.r,r.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),l.Jb(211,0,null,0,5,"SwtPanel",[["height","25%"],["id","panelSpreadGrid"],["paddingTop","5"],["width","100%"]],null,null,null,g.dd,g.kb)),l.Ib(212,4440064,[[44,4],["panelSpreadGrid",4]],0,r.Cb,[l.r,r.i,l.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"]},null),(e()(),l.Jb(213,0,null,0,3,"HBox",[["height","100%"],["paddingBottom","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.Dc,g.K)),l.Ib(214,4440064,null,0,r.C,[l.r,r.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"]},null),(e()(),l.Jb(215,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","canvasSpreadGrid"],["width","100%"]],null,null,null,g.Nc,g.U)),l.Ib(216,4440064,[[42,4],["canvasSpreadGrid",4]],0,r.db,[l.r,r.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(e()(),l.Jb(217,0,null,0,7,"SwtCanvas",[["height","5%"],["id","canvasContainer"],["width","100%"]],null,null,null,g.Nc,g.U)),l.Ib(218,4440064,null,0,r.db,[l.r,r.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(219,0,null,0,5,"HBox",[["height","100%"],["width","100%"]],null,null,null,g.Dc,g.K)),l.Ib(220,4440064,null,0,r.C,[l.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(221,0,null,0,1,"SwtButton",[["id","saveButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.checkPriorityBeforeSave()&&l);"keyDown"===t&&(l=!1!==n.keyDownEventHandler(i)&&l);return l},g.Mc,g.T)),l.Ib(222,4440064,[[19,4],["saveButton",4]],0,r.cb,[l.r,r.i],{id:[0,"id"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),l.Jb(223,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,i){var l=!0,n=e.component;"click"===t&&(l=!1!==n.popupClosed()&&l);"keyDown"===t&&(l=!1!==n.keyDownEventHandler(i)&&l);return l},g.Mc,g.T)),l.Ib(224,4440064,[[20,4],["cancelButton",4]],0,r.cb,[l.r,r.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"})],function(e,t){e(t,49,0,"100%","100%");e(t,51,0,"100%","100%","5","5","5","5");e(t,53,0,"100%","93%");e(t,55,0,"100%","100%");e(t,57,0,"100%","42%","3"),e(t,59,0);e(t,61,0,"15%");e(t,63,0,"categoryIdLabel");e(t,65,0,"25%");e(t,67,0,"20","a-zA-Z0-9\\-_","categoryIdTxtInput","200","true");e(t,69,0,"6%"),e(t,71,0);e(t,73,0,"45%");e(t,75,0,"50","A-Za-z0-9\\d_ !\\\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^`{|}~","categoryNameTxtInput","400","true");e(t,77,0,"8%");e(t,79,0,"actCheckbox","checkbox","Y","true"),e(t,81,0);e(t,83,0,"15%");e(t,85,0,"orderLabel");e(t,87,0,"85%");e(t,89,0,"ordinalNumInput","right","60","true"),e(t,91,0);e(t,93,0,"25%");e(t,95,0,"ruleAssignPriorityLabel");e(t,97,0,"75%");e(t,99,0,"3","ruleAssignPriorityInput","right","60","true"),e(t,101,0);e(t,103,0,"25%"),e(t,105,0);e(t,107,0,"70%");e(t,109,0,"instRelGroup","100%","horizontal");e(t,112,0,"urgent","120","instRelGroup","U");e(t,114,0,"spread","120","instRelGroup","S","true"),e(t,116,0);e(t,118,0,"25%"),e(t,120,0);e(t,122,0,"55%");e(t,124,0,"relTimeGroup","100%","horizontal");e(t,127,0,"radioK","120","relTimeGroup","K","true");e(t,129,0,"radioU","160","relTimeGroup","U");e(t,131,0,"radioI","100","relTimeGroup","I");e(t,133,0,"radioT","100","relTimeGroup","T");e(t,135,0,"10%");e(t,137,0,"5","timeInput","center","50","true"),e(t,139,0);e(t,141,0,"25%"),e(t,143,0);e(t,145,0,"70%");e(t,147,0,"4","offsetNum","right","60"),e(t,149,0);e(t,151,0,"25%"),e(t,153,0);e(t,155,0,"70%");e(t,157,0,"assigMetGroup","horizontal");e(t,160,0,"radioM","120","assigMetGroup","M");e(t,162,0,"radioS","160","assigMetGroup","S");e(t,164,0,"radioB","120","assigMetGroup","B","true"),e(t,166,0);e(t,168,0,"25%"),e(t,170,0);e(t,172,0,"70%");e(t,174,0,"useliqCheckBox","checkbox","N","false"),e(t,176,0);e(t,178,0,"25%"),e(t,180,0);e(t,182,0,"70%");e(t,184,0,"incTargCheckBox","checkbox","Y","true"),e(t,186,0);e(t,188,0,"25%"),e(t,190,0);e(t,192,0,"70%");e(t,194,0,"incLiqCheckBox","checkbox","Y","true");e(t,196,0,"panelRulesGrid","100%","30%");e(t,198,0,"100%","100%","5","5");e(t,200,0,"canvasRulesGrid","90%","100%","false");e(t,202,0,"center","middle","10%","100%");e(t,204,0,"addRuleButton","60","6");e(t,206,0,"changeRuleButton","60","6");e(t,208,0,"viewRuleButton","60","6");e(t,210,0,"deleteRuleButton","60");e(t,212,0,"panelSpreadGrid","100%","25%","5");e(t,214,0,"100%","100%","5","5");e(t,216,0,"canvasSpreadGrid","100%","100%","false");e(t,218,0,"canvasContainer","100%","5%");e(t,220,0,"100%","100%");e(t,222,0,"saveButton");e(t,224,0,"cancelButton","true")},null)}function Z(e){return l.dc(0,[(e()(),l.Jb(0,0,null,null,1,"app-pccategory-maintenance-add",[],null,null,null,W,E)),l.Ib(1,4440064,null,0,s,[r.i,l.r],null,null)],function(e,t){e(t,1,0)},null)}var U=l.Fb("app-pccategory-maintenance-add",s,Z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);