<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>PFCAngular7</title>
  <base href="./">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <script>

    function getBundle(type, key, defaultValue)
    {
      try{
        var labelValue = new String(label[type][key]);
        if(labelValue=="undefined"|| labelValue==null){
          return defaultValue;
        }else{
          return ""+labelValue.replace(/&nbsp;/g, ' ').replace(/'/g, "'");
        }
      }catch(err){
        return defaultValue;
      }
    }



    function checkLockOnServer(movementId)
{
	// var oXMLHTTP = new XMLHttpRequest();
	// var sURL = "http://localhost:8080/swallowtech/movementLock.do?method=checkLock";
	// sURL = sURL + "&movementId="+movementId;

	// oXMLHTTP.open( "POST", sURL, false );
	// oXMLHTTP.send();
	// var str=oXMLHTTP.responseText;
  console.log("checkLockOnServer")
	return 'true';
}



function setMsgButtonStatus(movId) {    

  console.log("setMsgButtonStatus")
	return '';
}


function checkLocksOnServer(movementId)
{
  console.log("checkLocksOnServer")
	return '';
}

function callApp(method,movementId,notesFlag) {
	
	var event = new CustomEvent("mmsd.movementNotes", 
        {
            detail: {method:method,movementId:movementId,notesFlag:notesFlag},
            bubbles: true,
            cancelable: true
        }
    );    
    window.dispatchEvent(event);
    
// 	Main.movementNotes(method,movementId,notesFlag);
}
function accountAccessConfirm(accountId,entity){

	console.log("accountAccessConfirm")
	return true;
}	


  </script>
<link rel="stylesheet" href="angularSources/styles.ac9086e019f60e6cc2d8.css"></head>
<body>
  <app-root></app-root>
<script>
  function getBundle(type, key, defaultValue)
  {
    try{
      var labelValue = new String(label[type][key]);
      if(labelValue=="undefined"|| labelValue==null){
        return defaultValue;
      }else{
        return ""+labelValue.replace(/&nbsp;/g, ' ').replace(/'/g, "'");
      }
    }catch(err){
      return defaultValue;
    }
  }
</script>
<script type="text/javascript" src="angularSources/runtime.5c23dbffcff536bb9500.js"></script><script type="text/javascript" src="angularSources/polyfills.f15611604fb5c94155e0.js"></script><script type="text/javascript" src="angularSources/scripts.c5ff09d4dc1c31c01fa9.js"></script><script type="text/javascript" src="angularSources/main.e57ace1735f955a6a08a.js"></script></body>
</html>
