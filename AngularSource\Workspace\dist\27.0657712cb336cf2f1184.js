(window.webpackJsonp=window.webpackJsonp||[]).push([[27],{"t7+v":function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),a=i("mrSG"),s=i("447K"),o=i("ZYCi"),r=function(t){function e(e,i,n){var a=t.call(this,i,e)||this;return a.commonService=e,a.element=i,a.router=n,a.jsonReader=new s.L,a.inputData=new s.G(a.commonService),a.logicUpdate=new s.G(a.commonService),a.baseURL=s.Wb.getBaseURL(),a.actionMethod="",a.actionPath="",a.requestParams=[],a.screenName=null,a.helpURL=null,a.spreadId=null,a.spreadName=null,a.spreadCurrency=null,a.errorLocation=0,a.time=null,a.targetValue=null,a.processCategories=null,a.categories=null,a.processName=null,a.operationsList=new s.hc("<operationsList/>"),a.moduleName="Spread Profile Maintenance - Add Details",a.versionNumber="1.00.00",a.releaseDate="12 March 2019",a.moduleId="PCM",a.maintEventId=null,a.processId=null,a.authOthers=!1,a.actionFromParent=null,a.parentMenuAccess=null,a.requireAuthorisation=!0,a.canAmendFacility=!1,a.swtAlert=new s.bb(e),a}return a.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){this.acceptButton.label=s.Wb.getPredictMessage("maintenanceevent.details.button.accept.label",null),this.acceptButton.toolTip=s.Wb.getPredictMessage("maintenanceevent.details.button.accept.tooltip",null),this.rejectButton.label=s.Wb.getPredictMessage("maintenanceevent.details.button.reject.label",null),this.rejectButton.toolTip=s.Wb.getPredictMessage("maintenanceevent.details.button.reject.tooltip",null),this.amendButton.label=s.Wb.getPredictMessage("maintenanceevent.details.button.amend.label",null),this.amendButton.toolTip=s.Wb.getPredictMessage("maintenanceevent.details.button.amend.tooltip",null),this.cancelAmendButton.label=s.Wb.getPredictMessage("button.cancel",null),this.cancelAmendButton.toolTip=s.Wb.getPredictMessage("tooltip.CancelChanges",null),this.closeButton.label=s.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=s.Wb.getPredictMessage("tooltip.entityMonitor.close",null);var t=[];window.opener.instanceElement?(t=window.opener.instanceElement.getParamsFromParent())&&(this.screenName=t[0].screenName,this.spreadId=t[0].spreadId,t[0].maintEventId&&(this.maintEventId=t[0].maintEventId),t[0].action&&(this.actionFromParent=t[0].action),t[0].parentMenuAccess&&(this.parentMenuAccess=t[0].parentMenuAccess),t[0].authOthers&&(this.authOthers=t[0].authOthers),t[0].amendInFacilityAccess&&(this.canAmendFacility=t[0].amendInFacilityAccess)):(this.screenName="change",this.spreadId="testFull",this.maintEventId="138"),instanceElement=this,this.addButton.label="Add",this.changeButton.label="Change",this.viewButton.label="View",this.deleteButton.label="Delete",this.saveButton.label="Save",this.cancelButton.label="Cancel",this.disableOrEnableButtons(!1),"add"==this.screenName?(this.spreadIdTxtInput.setFocus(),this.addButton.enabled=!0):"view"==this.screenName?(this.spreadNameTxtInput.enabled=!1,this.spreadIdTxtInput.enabled=!1,this.saveButton.visible=this.saveButton.includeInLayout=!1,this.addButton.enabled=!1):"change"==this.screenName&&(this.spreadNameTxtInput.enabled=!0,this.spreadIdTxtInput.enabled=!1,this.spreadNameTxtInput.setFocus(),this.addButton.enabled=!0)},e.prototype.destoyAllTooltips=function(){$(".ui-tooltip").each(function(t){$(this).remove()})},e.prototype.amendEventHandler=function(){var t=this;this.destoyAllTooltips(),window.opener.instanceElement.setViewOrAmendSubScreenFromChild("change");this.router.url;this.router.navigateByUrl("/",{skipLocationChange:!0}).then(function(){t.router.navigateByUrl("/spreadProfilesAdd")})},e.prototype.cancelAmendEventHandler=function(){var t=this;this.destoyAllTooltips(),window.opener.instanceElement.setViewOrAmendSubScreenFromChild("view");this.router.url;this.router.navigateByUrl("/",{skipLocationChange:!0}).then(function(){t.router.navigateByUrl("/spreadProfilesAdd")})},e.prototype.onLoad=function(){var t=this;try{this.maintEventId&&s.Z.isTrue(this.authOthers)&&"change"!=this.screenName&&(this.rejectButton.visible=!0,this.acceptButton.visible=!0),this.maintEventId&&"view"==this.screenName?(this.amendButton.visible=!0,this.amendButton.includeInLayout=!0,this.saveButton.visible=!1,this.saveButton.includeInLayout=!1,s.Z.isTrue(this.canAmendFacility)?(this.amendButton.enabled=!0,this.rejectButton.visible=!0):this.amendButton.enabled=!1):this.maintEventId&&(this.saveButton.visible=!0,this.saveButton.includeInLayout=!0,this.cancelAmendButton.visible=!0,this.cancelAmendButton.includeInLayout=!0,this.closeButton.visible=!0,this.closeButton.includeInLayout=!0,this.cancelButton.visible=!1,this.cancelButton.includeInLayout=!1,this.amendButton.visible=!1,this.amendButton.includeInLayout=!1),this.timesList=[],this.namesList=[],this.spreadDetailsGrid=this.spreadDetailsCanvasGrid.addChild(s.hb),this.aggregateAccountsGrid=this.aggregateAccountsCanvasGrid.addChild(s.hb),this.spreadDetailsGrid.onRowClick=function(e){t.cellClickEventHandler(e)},this.spreadDetailsGrid.rowColorFunction=function(e,i,n,a){return t.drawRowBackground(e,i,n,a)},this.requestParams=[],this.actionPath="spreadProfilesPCM.do?",this.requestParams.moduleId=this.moduleId,"add"!=this.screenName?(this.requestParams.spreadId=this.spreadId,this.actionMethod="method=displayChangeOrView"):this.actionMethod="method=displayAdd",this.requestParams.isFromMaintenanceEvent=this.maintEventId?"true":"false",this.requestParams.maintEventId=this.maintEventId,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){s.Wb.logError(e,this.moduleId,"className","onLoad",this.errorLocation)}},e.prototype.drawRowBackground=function(t,e,i,n){var a;try{"Y"==t.slickgrid_rowcontent[n].isDeletedRow?a="#ff808a":"Y"==t.slickgrid_rowcontent[n].isNewRow?a="#c6efce":"Y"==t.slickgrid_rowcontent[n].isUpdatedRow&&(a="#ee82ee")}catch(s){console.log("error drawRowBackground ",s)}return a},e.prototype.startOfComms=function(){this.disableComponents(!1),this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.disableComponents(!0)},e.prototype.inputDataResult=function(t){var e=this;try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&this.jsonReader.getRequestReplyMessage()&&(this.jsonReader.isDataBuilding()||("add"!=this.screenName&&(this.spreadName=this.jsonReader.getSingletons().spreadName,this.spreadCurrency=this.jsonReader.getSingletons().ccy),this.requireAuthorisation=this.jsonReader.getScreenAttributes().requireAuthorisation,this.currencyComboBox.setComboData(this.jsonReader.getSelects(),!1),this.ccyLabel.text=this.currencyComboBox.selectedItem.value,this.spreadDetailsGrid.doubleClickEnabled=!1,this.spreadDetailsGrid.CustomGrid(t.SpreadProfilesMaintenanceAdd.gridSpreadProcessPoints.metadata),null!=t.SpreadProfilesMaintenanceAdd.gridSpreadProcessPoints.rows.size?(this.spreadDetailsGrid.gridData=t.SpreadProfilesMaintenanceAdd.gridSpreadProcessPoints.rows,this.spreadDetailsGrid.setRowSize=t.SpreadProfilesMaintenanceAdd.gridSpreadProcessPoints.rows.size):this.spreadDetailsGrid.dataProvider=[],"add"!=this.screenName&&(this.aggregateAccountsGrid.CustomGrid(t.SpreadProfilesMaintenanceAdd.gridAccountGroups.metadata),this.aggregateAccountsGrid.doubleClickEnabled=!1,null!=t.SpreadProfilesMaintenanceAdd.gridAccountGroups.rows.size?(this.aggregateAccountsGrid.gridData=t.SpreadProfilesMaintenanceAdd.gridAccountGroups.rows,this.aggregateAccountsGrid.setRowSize=t.SpreadProfilesMaintenanceAdd.gridAccountGroups.rows.size):(this.aggregateAccountsGrid.dataProvider=null,this.aggregateAccountsGrid.selectedIndex=-1)),"add"!=this.screenName&&(this.spreadNameTxtInput.text=this.spreadName,this.maintEventId&&this.jsonReader.getSingletons().spreadName_oldValue!=this.jsonReader.getSingletons().spreadName&&(this.spreadNameTxtInput.toolTipPreviousValue=this.jsonReader.getSingletons().spreadName_oldValue),this.spreadIdTxtInput.text=this.spreadId,this.currencyComboBox.selectedLabel=this.spreadCurrency,this.ccyLabel.text=this.currencyComboBox.selectedItem.value),this.swtModule.subscribeSpy([this.spreadDetailsGrid]),this.swtModule.onSpyChange.subscribe(function(t){e.processPointsList=e.spreadDetailsGrid.changes}))))}catch(i){console.log("error:   ",i),s.Wb.logError(i,this.moduleId,"className","inputDataResult",this.errorLocation)}},e.prototype.inputDataFault=function(t){this.swtAlert.error(t.fault.faultstring+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail)},e.prototype.saveEventHandler=function(){0!=this.spreadIdTxtInput.text.length&&0!=this.spreadNameTxtInput.text.length?""!=this.spreadNameTxtInput.text.trim()?0!=this.spreadDetailsGrid.dataProvider.length?this.save():this.swtAlert.warning("Cannot add a spread profile without any process point !"):this.swtAlert.warning("Spread Profile Name can not be saved as just spaces"):this.swtAlert.warning("Please fill all mandatory fields (marked with *)")},e.prototype.save=function(){var t=this;this.xmlDataProcessPoints();try{this.actionMethod="method=save",this.logicUpdate.cbResult=function(e){t.logicUpdateResult(e)},this.requestParams=[],"I"==this.actionFromParent?this.requestParams.screenName="add":this.requestParams.screenName=this.screenName,this.requestParams.spreadProfileId=this.spreadIdTxtInput.text,this.requestParams.spreadProfileName=this.spreadNameTxtInput.text,this.requestParams.currencyCode=this.currencyComboBox.selectedLabel,this.requestParams.xmlData=this.operationsList.toString(),this.requestParams.maintEventId=this.maintEventId,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams)}catch(e){s.Wb.logError(e,this.moduleId,"className","save",this.errorLocation)}},e.prototype.xmlDataProcessPoints=function(){var t=[];if(this.operationsList=new s.hc("<operationsList/>"),null!=this.processPointsList)for(var e=0;e<this.processPointsList.getValues().length;e++)(t=[]).PROCESS_ID=this.processPointsList.getValues()[e].crud_data.processId,t.PROCESS_NAME=this.processPointsList.getValues()[e].crud_data.processName,t.TIME=this.processPointsList.getValues()[e].crud_data.time,t.TARGET=this.processPointsList.getValues()[e].crud_data.target,t.PROCESS=this.processPointsList.getValues()[e].crud_data.process,t.CATEGORIES=this.processPointsList.getValues()[e].crud_data.categories,"I"==this.processPointsList.getValues()[e].crud_operation&&this.operationsList.appendChild(s.Z.getKVTypeTabAsXML(t,"PC_SPREAD_PROCESS_POINT","I","M")),"U"==this.processPointsList.getValues()[e].crud_operation.substring(0,1)&&this.operationsList.appendChild(s.Z.getKVTypeTabAsXML(t,"PC_SPREAD_PROCESS_POINT","U","M")),"D"==this.processPointsList.getValues()[e].crud_operation.substring(0,1)&&this.operationsList.appendChild(s.Z.getKVTypeTabAsXML(t,"PC_SPREAD_PROCESS_POINT","D","M"))},e.prototype.logicUpdateResult=function(t){try{var e=t,i=new s.L;if(i.setInputJSON(e),i.getRequestReplyStatus())this.updateData();else if("errors.DataIntegrityViolationExceptioninAdd"==i.getRequestReplyMessage())this.swtAlert.error("Unable to save, spread Profile Id already exists");else if("errors.processPointTimeNotInRange"==i.getRequestReplyMessage()){var n="Could not add a spread process point with time outside <br>kick-off and End of Intraday Release Phase for the following account groups : <br>",a=i.getRequestReplyLocation(),o=[],r=0;-1!=a.indexOf(",")?(o[0]=a.substr(0,a.indexOf(",")+1),r=(a=a.substr(a.indexOf(",")+1)).indexOf(","),-1==a.indexOf(",")&&(o[0]+=a)):o[0]=a;for(var l=0,d=1;-1!=a.indexOf(",");)3==d&&(o[++l]="",d=0),o[l]+=a.substr(0,a.indexOf(",")+1),d++,-1==(r=(a=a.substr(r+1)).indexOf(","))&&(o[l]+=a);for(var c=0;c<o.length;c++)n+=o[c]+"<br>";this.swtAlert.error(n)}else this.swtAlert.error(i.getRequestReplyMessage())}catch(u){s.Wb.logError(u,this.moduleId,"ClassName","logicUpdateResult",this.errorLocation)}},e.prototype.updateData=function(){try{s.Z.isTrue(this.requireAuthorisation)?this.swtAlert.show(s.Wb.getPredictMessage("maintenanceevent.details.alert.actionneedauthorisation",null),"Warning",s.c.OK,null,this.closeWindow.bind(this)):this.maintEventId?(window.opener&&window.opener.opener&&window.opener.opener.instanceElement&&window.opener.opener.instanceElement.updateData(),window.opener&&window.opener.instanceElement&&window.opener.close(),window.close()):(window.opener.instanceElement&&window.opener.instanceElement.updateData(),this.closeBtn())}catch(t){s.Wb.logError(t,s.Wb.AML_MODULE_ID,"ClassName","updateData",this.errorLocation)}},e.prototype.disableComponents=function(t){},e.prototype.keyDownEventHandler=function(t){try{var e=Object(s.ic.getFocus()).name;t.keyCode==s.N.ENTER&&("saveButton"==e?this.saveEventHandler():"cancelButton"==e&&this.closeBtn())}catch(i){console.log(i,this.moduleId,"className","keyDownEventHandler")}},e.prototype.cellClickEventHandler=function(t){try{1===this.spreadDetailsGrid.selectedIndices.length&&this.spreadDetailsGrid.selectable?this.disableOrEnableButtons(!0):this.disableOrEnableButtons(!1),t.stopPropagation(),this.addButton.setFocus()}catch(e){s.Wb.logError(e,this.moduleId,"ClassName","cellClickEventHandler",this.errorLocation)}},e.prototype.disableOrEnableButtons=function(t){t?(this.changeButton.enabled="view"!=this.screenName,this.viewButton.enabled=!0,this.deleteButton.enabled="view"!=this.screenName):(this.changeButton.enabled=!1,this.viewButton.enabled=!1,this.deleteButton.enabled=!1)},e.prototype.doAddSpreadDetails=function(t){var e=this;try{this.timesList=[],this.namesList=[];for(var i=0;i<this.spreadDetailsGrid.dataProvider.length;i++)this.timesList.push(this.spreadDetailsGrid.dataProvider[i].time),this.namesList.push(this.spreadDetailsGrid.dataProvider[i].processName);this.win=s.Eb.createPopUp(this),this.win.title="Spread Process Points - Add",this.win.isModal=!0,this.win.width="620",this.win.height="500",this.win.id="spreadDetails",this.win.showControls=!0,this.win.enableResize=!1,this.win.showControls=!0,this.win.screenName="add",this.win.moduleId=this.moduleId,this.win.timesList=this.timesList,this.win.namesList=this.namesList;var n=new s.T(this.commonService);n.addEventListener(s.S.READY,function(t){return e.moduleReadyEventHandler(t,"add")}),n.loadModule("spreadDetails")}catch(a){console.log("error add",a),s.Wb.logError(a,this.moduleId,"ClassName","doAddSpreadDetails",this.errorLocation)}},e.prototype.doChangeSpreadDetails=function(t){var e=this;try{var i=this.spreadDetailsGrid.dataProvider.findIndex(function(t){return t.num==e.spreadDetailsGrid.selectedItem.num.content});this.timesList=[],this.namesList=[],this.time=this.spreadDetailsGrid.dataProvider[i].time,this.targetValue=this.spreadDetailsGrid.dataProvider[i].target,this.processCategories=this.spreadDetailsGrid.dataProvider[i].process,this.categories=this.spreadDetailsGrid.dataProvider[i].categories,this.processName=this.spreadDetailsGrid.dataProvider[i].processName,this.processId=this.spreadDetailsGrid.dataProvider[i].processId;for(var n=0;n<this.spreadDetailsGrid.dataProvider.length;n++)this.spreadDetailsGrid.dataProvider[n].time!=this.time&&this.timesList.push(this.spreadDetailsGrid.dataProvider[n].time),this.spreadDetailsGrid.dataProvider[n].processName!=this.processName&&this.namesList.push(this.spreadDetailsGrid.dataProvider[n].processName);this.win=s.Eb.createPopUp(this),this.win.title="Spread Process Points - Change",this.win.width="620",this.win.height="500",this.win.id="spreadDetails",this.win.isModal=!0,this.win.enableResize=!1,this.win.showControls=!0,this.win.screenName="change",this.win.moduleId=this.moduleId,this.win.processName=this.processName,this.win.selectedDataRow=this.spreadDetailsGrid.dataProvider[i],this.win.spreadId=this.spreadId,this.win.processId=this.processId,this.win.maintEventId=this.maintEventId,this.win.time=this.time,this.win.targetValue=this.targetValue,this.win.processCategories=this.processCategories,this.win.categories=this.categories,this.win.timesList=this.timesList,this.win.namesList=this.namesList;var a=new s.T(this.commonService);a.addEventListener(s.S.READY,function(t){return e.moduleReadyEventHandler(t,"change")}),a.loadModule("spreadDetails")}catch(o){s.Wb.logError(o,this.moduleId,"ClassName","doChangeSpreadDetails",this.errorLocation)}},e.prototype.doViewSpreadDetails=function(t){var e=this;try{var i=this.spreadDetailsGrid.dataProvider.findIndex(function(t){return t.num==e.spreadDetailsGrid.selectedItem.num.content});this.time=this.spreadDetailsGrid.dataProvider[i].time,this.targetValue=this.spreadDetailsGrid.dataProvider[i].target,this.processCategories=this.spreadDetailsGrid.dataProvider[i].process,this.categories=this.spreadDetailsGrid.dataProvider[i].categories,this.processName=this.spreadDetailsGrid.dataProvider[i].processName,this.win=s.Eb.createPopUp(this),this.win.title="Spread Process Points - View",this.win.width="620",this.win.height="500",this.win.id="spreadDetails",this.win.isModal=!0,this.win.enableResize=!1,this.win.showControls=!0,this.win.screenName="view",this.win.moduleId=this.moduleId,this.win.processName=this.processName,this.win.time=this.time,this.win.targetValue=this.targetValue,this.win.processCategories=this.processCategories,this.win.categories=this.categories;var n=new s.T(this.commonService);n.addEventListener(s.S.READY,function(t){return e.moduleReadyEventHandler(t,"view")}),n.loadModule("spreadDetails")}catch(a){s.Wb.logError(a,this.moduleId,"ClassName","doViewSpreadDetails",this.errorLocation)}},e.prototype.moduleReadyEventHandler=function(t,e){var i=this;if(this.win.addChild(t.target),this.win.display(),"add"==e)this.win.onClose.subscribe(function(){if(null!=i.win.getChild().result&&null!=i.win.getChild().result){var t={time:{content:String(i.win.getChild().result.time)},processName:{content:i.win.getChild().result.processName},process:{content:i.win.getChild().result.process},categories:{content:i.win.getChild().result.categories},target:{content:i.win.getChild().result.target}};i.spreadDetailsGrid.appendRow(t),i.changeButton.enabled=!0,i.viewButton.enabled=!0,i.deleteButton.enabled=!0}});else if("change"==e){var n=this.spreadDetailsGrid.dataProvider.findIndex(function(t){return t.num==i.spreadDetailsGrid.selectedItem.num.content}),a=this.spreadDetailsGrid.selectedIndex;this.win.onClose.subscribe(function(){null!=i.win.getChild().result&&null!=i.win.getChild().result&&(i.spreadDetailsGrid.dataProvider[n].processName=i.win.getChild().result.processName,i.spreadDetailsGrid.dataProvider[n].time=i.win.getChild().result.time,i.spreadDetailsGrid.dataProvider[n].target=i.win.getChild().result.target,i.spreadDetailsGrid.dataProvider[n].process=i.win.getChild().result.process,i.spreadDetailsGrid.dataProvider[n].categories=i.win.getChild().result.categories,i.spreadDetailsGrid.dataProvider[n].slickgrid_rowcontent.processName.content=i.win.getChild().result.processName,i.spreadDetailsGrid.dataProvider[n].slickgrid_rowcontent.time.content=i.win.getChild().result.time,i.spreadDetailsGrid.dataProvider[n].slickgrid_rowcontent.target.content=i.win.getChild().result.target,i.spreadDetailsGrid.dataProvider[n].slickgrid_rowcontent.process.content=i.win.getChild().result.process,i.spreadDetailsGrid.dataProvider[n].slickgrid_rowcontent.categories.content=i.win.getChild().result.categories,i.spreadDetailsGrid.refresh(),i.spreadDetailsGrid.selectedIndex=a)})}},e.prototype.doDeleteSpreadDetails=function(){try{this.swtAlert.confirm("Do you wish to delete this row","Alert",s.c.YES|s.c.NO,null,this.gridRowRemove.bind(this))}catch(t){console.log("error in delete",t)}},e.prototype.gridRowRemove=function(t){if(t.detail===s.c.YES){for(var e=0;e<this.timesList.length;e++)if(this.timesList[e]==this.spreadDetailsGrid.dataProvider[this.spreadDetailsGrid.selectedIndex].time){this.timesList.splice(e,1);break}for(e=0;e<this.namesList.length;e++)if(this.namesList[e]==this.spreadDetailsGrid.dataProvider[this.spreadDetailsGrid.selectedIndex].processName){this.namesList.splice(e,1);break}this.spreadDetailsGrid.removeSelected(),this.spreadDetailsGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1,this.viewButton.enabled=!1}},e.prototype.popupClosed=function(){this.dispose()},e.prototype.dispose=function(){try{this.requestParams=null,this.baseURL=null,this.actionMethod=null,this.actionPath=null,this.titleWindow?this.close():window.close()}catch(t){console.log(t,this.moduleId,"className","dispose")}},e.prototype.closeBtn=function(){try{this.dispose()}catch(t){s.Wb.logError(t,this.moduleId,"className","closeBtn",this.errorLocation)}},e.prototype.changeComboCurrency=function(t){this.ccyLabel.text=this.currencyComboBox.selectedItem.value},e.prototype.printPage=function(){try{this.actionMethod="type=pdf",this.actionMethod=this.actionMethod+"&action=EXPORT",this.actionMethod=this.actionMethod+"&currentModuleId="+this.moduleId,this.actionMethod=this.actionMethod+"&print=PAGE",s.x.call("getReports",this.actionPath+this.actionMethod)}catch(t){s.Wb.logError(t,this.moduleId,"className","printPage",this.errorLocation)}},e.prototype.doHelp=function(){try{s.sb.open(this.baseURL+"help/aml/fr/index.html#page=groupes-des-regles.html"),s.sb.resizable(!0)}catch(t){s.Wb.logError(t,this.moduleId,"ClassName","doHelp",this.errorLocation)}},e.prototype.acceptEventEventHandler=function(){var t=s.Wb.getPredictMessage("maintenanceevent.details.alert.areyousuretoaccept",null);this.swtAlert.confirm(t,s.Wb.getPredictMessage("button.confirm",null),s.c.YES|s.c.NO,null,this.acceptStatusHandler.bind(this),null)},e.prototype.rejectEventEventHandler=function(){var t=s.Wb.getPredictMessage("maintenanceevent.details.alert.areyousuretoreject",null);this.swtAlert.confirm(t,s.Wb.getPredictMessage("button.confirm",null),s.c.YES|s.c.NO,null,this.rejectStatusHandler.bind(this),null)},e.prototype.acceptStatusHandler=function(t){t.detail==s.c.YES&&window.opener&&window.opener.instanceElement&&this.changeStatusHandler("A")},e.prototype.rejectStatusHandler=function(t){t.detail==s.c.YES&&window.opener&&window.opener.instanceElement&&this.changeStatusHandler("R")},e.prototype.updateMaintenanceEventStatusResult=function(t){var e=0;try{this.inputData.isBusy()?this.inputData.cbStop():(this.jsonReader.setInputJSON(this.lastRecievedJSON),e=10,this.jsonReader.getRequestReplyStatus()?this.swtAlert.show(s.Wb.getPredictMessage("maintenanceevent.details.alert.actionperfermored",null),"Warning",s.c.OK,null,this.closeWindow.bind(this)):this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error"))}catch(i){s.Wb.logError(i,s.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaintLog.ts","cellClickEventHandler",e)}},e.prototype.closeWindow=function(t){t.detail==s.c.OK&&(this.maintEventId?(window.opener&&window.opener.opener&&window.opener.opener.instanceElement&&window.opener.opener.instanceElement.updateData(),window.opener&&window.opener.instanceElement&&window.opener.close(),window.close()):(window.opener.instanceElement&&window.opener.instanceElement.updateData(),this.closeBtn()))},e.prototype.changeStatusHandler=function(t){var e=this,i=0;try{this.actionPath="maintenanceEvent.do?",this.actionMethod="method=updateMaintenanceEventStatus",i=50,this.requestParams=[],this.requestParams.menuAccessId=this.parentMenuAccess,i=60,this.requestParams.maintEventId=this.maintEventId,i=70,this.requestParams.action=t,this.inputData.cbResult=function(t){e.updateMaintenanceEventStatusResult(t)},this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,i=100,this.inputData.send(this.requestParams)}catch(n){s.Wb.logError(n,s.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaintLog.ts","onLoad",i)}},e}(s.yb),l=[{path:"",component:r}],d=(o.l.forChild(l),function(){return function(){}}()),c=i("pMnS"),u=i("RChO"),h=i("t6HQ"),p=i("WFGK"),b=i("5FqG"),g=i("Ip0R"),m=i("gIcY"),w=i("t/Na"),v=i("sE5F"),f=i("OzfB"),y=i("T7CS"),D=i("S7LP"),I=i("6aHO"),R=i("WzUx"),C=i("A7o+"),P=i("zCE2"),B=i("Jg5P"),S=i("3R0m"),k=i("hhbb"),L=i("5rxC"),E=i("Fzqc"),A=i("21Lb"),M=i("hUWP"),N=i("3pJQ"),T=i("V9q+"),x=i("VDKW"),G=i("kXfT"),_=i("BGbe");i.d(e,"SpreadProfilesMaintenanceAddModuleNgFactory",function(){return O}),i.d(e,"RenderType_SpreadProfilesMaintenanceAdd",function(){return J}),i.d(e,"View_SpreadProfilesMaintenanceAdd_0",function(){return W}),i.d(e,"View_SpreadProfilesMaintenanceAdd_Host_0",function(){return q}),i.d(e,"SpreadProfilesMaintenanceAddNgFactory",function(){return V});var O=n.Gb(d,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[c.a,u.a,h.a,p.a,b.Cb,b.Pb,b.r,b.rc,b.s,b.Ab,b.Bb,b.Db,b.qd,b.Hb,b.k,b.Ib,b.Nb,b.Ub,b.yb,b.Jb,b.v,b.A,b.e,b.c,b.g,b.d,b.Kb,b.f,b.ec,b.Wb,b.bc,b.ac,b.sc,b.fc,b.lc,b.jc,b.Eb,b.Fb,b.mc,b.Lb,b.nc,b.Mb,b.dc,b.Rb,b.b,b.ic,b.Yb,b.Sb,b.kc,b.y,b.Qb,b.cc,b.hc,b.pc,b.oc,b.xb,b.p,b.q,b.o,b.h,b.j,b.w,b.Zb,b.i,b.m,b.Vb,b.Ob,b.Gb,b.Xb,b.t,b.tc,b.zb,b.n,b.qc,b.a,b.z,b.rd,b.sd,b.x,b.td,b.gc,b.l,b.u,b.ud,b.Tb,V]],[3,n.n],n.J]),n.Rb(4608,g.m,g.l,[n.F,[2,g.u]]),n.Rb(4608,m.c,m.c,[]),n.Rb(4608,m.p,m.p,[]),n.Rb(4608,w.j,w.p,[g.c,n.O,w.n]),n.Rb(4608,w.q,w.q,[w.j,w.o]),n.Rb(5120,w.a,function(t){return[t,new s.tb]},[w.q]),n.Rb(4608,w.m,w.m,[]),n.Rb(6144,w.k,null,[w.m]),n.Rb(4608,w.i,w.i,[w.k]),n.Rb(6144,w.b,null,[w.i]),n.Rb(4608,w.f,w.l,[w.b,n.B]),n.Rb(4608,w.c,w.c,[w.f]),n.Rb(4608,v.c,v.c,[]),n.Rb(4608,v.g,v.b,[]),n.Rb(5120,v.i,v.j,[]),n.Rb(4608,v.h,v.h,[v.c,v.g,v.i]),n.Rb(4608,v.f,v.a,[]),n.Rb(5120,v.d,v.k,[v.h,v.f]),n.Rb(5120,n.b,function(t,e){return[f.j(t,e)]},[g.c,n.O]),n.Rb(4608,y.a,y.a,[]),n.Rb(4608,D.a,D.a,[]),n.Rb(4608,I.a,I.a,[n.n,n.L,n.B,D.a,n.g]),n.Rb(4608,R.c,R.c,[n.n,n.g,n.B]),n.Rb(4608,R.e,R.e,[R.c]),n.Rb(4608,C.l,C.l,[]),n.Rb(4608,C.h,C.g,[]),n.Rb(4608,C.c,C.f,[]),n.Rb(4608,C.j,C.d,[]),n.Rb(4608,C.b,C.a,[]),n.Rb(4608,C.k,C.k,[C.l,C.h,C.c,C.j,C.b,C.m,C.n]),n.Rb(4608,R.i,R.i,[[2,C.k]]),n.Rb(4608,R.r,R.r,[R.L,[2,C.k],R.i]),n.Rb(4608,R.t,R.t,[]),n.Rb(4608,R.w,R.w,[]),n.Rb(1073742336,o.l,o.l,[[2,o.r],[2,o.k]]),n.Rb(1073742336,g.b,g.b,[]),n.Rb(1073742336,m.n,m.n,[]),n.Rb(1073742336,m.l,m.l,[]),n.Rb(1073742336,P.a,P.a,[]),n.Rb(1073742336,B.a,B.a,[]),n.Rb(1073742336,m.e,m.e,[]),n.Rb(1073742336,S.a,S.a,[]),n.Rb(1073742336,C.i,C.i,[]),n.Rb(1073742336,R.b,R.b,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,w.d,w.d,[]),n.Rb(1073742336,v.e,v.e,[]),n.Rb(1073742336,k.b,k.b,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,f.c,f.c,[]),n.Rb(1073742336,E.a,E.a,[]),n.Rb(1073742336,A.d,A.d,[]),n.Rb(1073742336,M.c,M.c,[]),n.Rb(1073742336,N.a,N.a,[]),n.Rb(1073742336,T.a,T.a,[[2,f.g],n.O]),n.Rb(1073742336,x.b,x.b,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,_.b,_.b,[]),n.Rb(1073742336,s.Tb,s.Tb,[]),n.Rb(1073742336,d,d,[]),n.Rb(256,w.n,"XSRF-TOKEN",[]),n.Rb(256,w.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,C.m,void 0,[]),n.Rb(256,C.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,o.i,function(){return[[{path:"",component:r}]]},[])])}),H=[[""]],J=n.Hb({encapsulation:0,styles:H,data:{}});function W(t){return n.dc(0,[n.Zb(*********,1,{_container:0}),n.Zb(*********,2,{spreadNameTxtInput:0}),n.Zb(*********,3,{spreadIdTxtInput:0}),n.Zb(*********,4,{loadingImage:0}),n.Zb(*********,5,{currencyComboBox:0}),n.Zb(*********,6,{addButton:0}),n.Zb(*********,7,{changeButton:0}),n.Zb(*********,8,{viewButton:0}),n.Zb(*********,9,{deleteButton:0}),n.Zb(*********,10,{saveButton:0}),n.Zb(*********,11,{cancelButton:0}),n.Zb(*********,12,{expressionBuilderButton:0}),n.Zb(*********,13,{spreadDetailsCanvasGrid:0}),n.Zb(*********,14,{aggregateAccountsCanvasGrid:0}),n.Zb(*********,15,{processPointsPanel:0}),n.Zb(*********,16,{ccyLabel:0}),n.Zb(*********,17,{swtModule:0}),n.Zb(*********,18,{hboxID:0}),n.Zb(*********,19,{acceptButton:0}),n.Zb(*********,20,{rejectButton:0}),n.Zb(*********,21,{amendButton:0}),n.Zb(*********,22,{cancelAmendButton:0}),n.Zb(*********,23,{closeButton:0}),(t()(),n.Jb(23,0,null,null,79,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"close"],[null,"creationComplete"]],function(t,e,i){var n=!0,a=t.component;"close"===e&&(n=!1!==a.popupClosed()&&n);"creationComplete"===e&&(n=!1!==a.onLoad()&&n);return n},b.ad,b.hb)),n.Ib(24,4440064,[[17,4],["swtModule",4]],0,s.yb,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(25,0,null,0,77,"VBox",[["height","100%"],["paddingBottom","10"],["paddingLeft","10"],["paddingRight","10"],["paddingTop","10"],["width","100%"]],null,null,null,b.od,b.vb)),n.Ib(26,4440064,null,0,s.ec,[n.r,s.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(27,0,null,0,47,"SwtCanvas",[["height","89%"],["width","100%"]],null,null,null,b.Nc,b.U)),n.Ib(28,4440064,null,0,s.db,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(29,0,null,0,45,"VBox",[["height","100%"],["width","100%"]],null,null,null,b.od,b.vb)),n.Ib(30,4440064,null,0,s.ec,[n.r,s.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(31,0,null,0,21,"HBox",[["height","10%"],["marginLeft","5"],["marginTop","5"],["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(32,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"],height:[1,"height"],marginTop:[2,"marginTop"],marginLeft:[3,"marginLeft"]},null),(t()(),n.Jb(33,0,null,0,5,"HBox",[["width","29%"]],null,null,null,b.Dc,b.K)),n.Ib(34,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(35,0,null,0,1,"SwtLabel",[["paddingRight","15"],["text","Spread ID*"]],null,null,null,b.Yc,b.fb)),n.Ib(36,4440064,null,0,s.vb,[n.r,s.i],{paddingRight:[0,"paddingRight"],text:[1,"text"]},null),(t()(),n.Jb(37,0,null,0,1,"SwtTextInput",[["height","22"],["id","spreadIdTxtInput"],["maxChars","20"],["restrict","a-zA-Z0-9\\-_"],["toolTip","Spread ID*"],["width","150"]],null,null,null,b.kd,b.sb)),n.Ib(38,4440064,[[3,4],["spreadIdTxtInput",4]],0,s.Rb,[n.r,s.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],toolTip:[3,"toolTip"],width:[4,"width"],height:[5,"height"],required:[6,"required"]},null),(t()(),n.Jb(39,0,null,0,5,"HBox",[["width","38%"]],null,null,null,b.Dc,b.K)),n.Ib(40,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(41,0,null,0,1,"SwtLabel",[["paddingRight","15"],["text","Spread Name*"]],null,null,null,b.Yc,b.fb)),n.Ib(42,4440064,null,0,s.vb,[n.r,s.i],{paddingRight:[0,"paddingRight"],text:[1,"text"]},null),(t()(),n.Jb(43,0,null,0,1,"SwtTextInput",[["height","22"],["id","spreadNameTxtInput"],["maxChars","30"],["required","true"],["restrict","A-Za-z0-9\\d_ !\\\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^`{|}~"],["toolTip","Spread Name"],["width","210"]],null,null,null,b.kd,b.sb)),n.Ib(44,4440064,[[2,4],["spreadNameTxtInput",4]],0,s.Rb,[n.r,s.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],toolTip:[3,"toolTip"],width:[4,"width"],height:[5,"height"],required:[6,"required"]},null),(t()(),n.Jb(45,0,null,0,7,"HBox",[["width","33%"]],null,null,null,b.Dc,b.K)),n.Ib(46,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(47,0,null,0,1,"SwtLabel",[["paddingLeft","10"],["paddingRight","15"],["text","Currency"]],null,null,null,b.Yc,b.fb)),n.Ib(48,4440064,null,0,s.vb,[n.r,s.i],{paddingLeft:[0,"paddingLeft"],paddingRight:[1,"paddingRight"],text:[2,"text"]},null),(t()(),n.Jb(49,0,null,0,1,"SwtComboBox",[["dataLabel","currencyList"],["id","currencyComboBox"],["toolTip","Select Currency"],["width","70"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,s=t.component;"window:mousewheel"===e&&(a=!1!==n.Tb(t,50).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==s.changeComboCurrency(i)&&a);return a},b.Pc,b.W)),n.Ib(50,4440064,[[5,4],["currencyComboBox",4]],0,s.gb,[n.r,s.i],{dataLabel:[0,"dataLabel"],toolTip:[1,"toolTip"],width:[2,"width"],id:[3,"id"],enabled:[4,"enabled"]},{change_:"change"}),(t()(),n.Jb(51,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["paddingLeft","10"],["text"," "]],null,null,null,b.Yc,b.fb)),n.Ib(52,4440064,[[16,4],["ccyLabel",4]],0,s.vb,[n.r,s.i],{paddingLeft:[0,"paddingLeft"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(53,0,null,0,15,"SwtPanel",[["id","processPointsPanel"],["paddingBottom","10"],["paddingTop","5"],["title","Spread Process Points"],["width","100%"]],null,null,null,b.dd,b.kb)),n.Ib(54,4440064,[[15,4],["processPointsPanel",4]],0,s.Cb,[n.r,s.i,n.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],title:[5,"title"]},null),(t()(),n.Jb(55,0,null,0,13,"HBox",[["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(56,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(57,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","spreadDetailsCanvasGrid"],["width","90%"]],null,null,null,b.Nc,b.U)),n.Ib(58,4440064,[[13,4],["spreadDetailsCanvasGrid",4]],0,s.db,[n.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(t()(),n.Jb(59,0,null,0,9,"VBox",[["height","100%"],["horizontalAlign","center"],["paddingLeft","5"],["paddingTop","5"],["verticalAlign","middle"],["width","10%"]],null,null,null,b.od,b.vb)),n.Ib(60,4440064,null,0,s.ec,[n.r,s.i,n.T],{horizontalAlign:[0,"horizontalAlign"],verticalAlign:[1,"verticalAlign"],width:[2,"width"],height:[3,"height"],paddingTop:[4,"paddingTop"],paddingLeft:[5,"paddingLeft"]},null),(t()(),n.Jb(61,0,null,0,1,"SwtButton",[["id","addButton"],["marginBottom","6"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doAddSpreadDetails(i)&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(62,4440064,[[6,4],["addButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],marginBottom:[2,"marginBottom"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(63,0,null,0,1,"SwtButton",[["id","changeButton"],["marginBottom","6"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doChangeSpreadDetails(i)&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(64,4440064,[[7,4],["changeButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],marginBottom:[2,"marginBottom"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(65,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","viewButton"],["marginBottom","6"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doViewSpreadDetails(i)&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(66,4440064,[[8,4],["viewButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],marginBottom:[2,"marginBottom"],buttonMode:[3,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(67,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","deleteButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doDeleteSpreadDetails()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(68,4440064,[[9,4],["deleteButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(69,0,null,0,5,"HBox",[["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(70,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"],height:[1,"height"],visible:[2,"visible"]},null),(t()(),n.Jb(71,0,null,0,3,"SwtPanel",[["height","98%"],["paddingTop","5"],["title","Associated Account Groups"],["width","100%"]],null,null,null,b.dd,b.kb)),n.Ib(72,4440064,null,0,s.Cb,[n.r,s.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],title:[3,"title"]},null),(t()(),n.Jb(73,0,null,0,1,"SwtCanvas",[["border","false"],["height","150"],["id","aggregateAccountsCanvasGrid"],["width","100%"]],null,null,null,b.Nc,b.U)),n.Ib(74,4440064,[[14,4],["aggregateAccountsCanvasGrid",4]],0,s.db,[n.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],border:[3,"border"]},null),(t()(),n.Jb(75,0,null,0,27,"SwtCanvas",[["height","9%"],["width","100%"]],null,null,null,b.Nc,b.U)),n.Ib(76,4440064,null,0,s.db,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(77,0,null,0,25,"HBox",[["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(78,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"]},null),(t()(),n.Jb(79,0,null,0,11,"HBox",[["height","100%"],["style","margin-top: 3px; margin-left: 5px"],["width","100%"]],null,null,null,b.Dc,b.K)),n.Ib(80,4440064,null,0,s.C,[n.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(81,0,null,0,1,"SwtButton",[["id","saveButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.saveEventHandler()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(82,4440064,[[10,4],["saveButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(83,0,null,0,1,"SwtButton",[["id","amendButton"],["includeInLayout","false"],["label","Amend"],["visible","false"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.amendEventHandler()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(84,4440064,[[21,4],["amendButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],includeInLayout:[2,"includeInLayout"],visible:[3,"visible"],label:[4,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(85,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelAmendButton"],["includeInLayout","false"],["visible","false"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.cancelAmendEventHandler()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(86,4440064,[[22,4],["cancelAmendButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],includeInLayout:[2,"includeInLayout"],visible:[3,"visible"],buttonMode:[4,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(87,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","closeButton"],["includeInLayout","false"],["marginLeft","5"],["visible","false"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.popupClosed()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(88,4440064,[[23,4],["closeButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],includeInLayout:[2,"includeInLayout"],visible:[3,"visible"],marginLeft:[4,"marginLeft"],buttonMode:[5,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(89,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"],["marginLeft","5"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.closeBtn()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(90,4440064,[[11,4],["cancelButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],marginLeft:[1,"marginLeft"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(91,0,null,0,11,"HBox",[["horizontalAlign","right"],["marginTop","5"],["paddingRight","10"]],null,null,null,b.Dc,b.K)),n.Ib(92,4440064,null,0,s.C,[n.r,s.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"],marginTop:[2,"marginTop"]},null),(t()(),n.Jb(93,0,null,0,1,"SwtButton",[["id","acceptButton"],["label","Accept"],["visible","false"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.acceptEventEventHandler()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(94,4440064,[[19,4],["acceptButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],visible:[2,"visible"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(95,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","rejectButton"],["label","Reject"],["visible","false"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.rejectEventEventHandler()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(96,4440064,[[20,4],["rejectButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],width:[1,"width"],visible:[2,"visible"],label:[3,"label"],buttonMode:[4,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(97,0,null,0,1,"SwtLoadingImage",[],null,null,null,b.Zc,b.gb)),n.Ib(98,114688,[[4,4],["loadingImage",4]],0,s.xb,[n.r],null,null),(t()(),n.Jb(99,0,null,0,1,"SwtButton",[["id","printButton"],["styleName","printIcon"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.printPage()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},b.Mc,b.T)),n.Ib(100,4440064,[["printButton",4]],0,s.cb,[n.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(101,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","spread-profile"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.doHelp()&&n);return n},b.Wc,b.db)),n.Ib(102,4440064,null,0,s.rb,[n.r,s.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"})],function(t,e){var i=e.component;t(e,24,0,"100%","100%");t(e,26,0,"100%","100%","10","10","10","10");t(e,28,0,"100%","89%");t(e,30,0,"100%","100%");t(e,32,0,"100%","10%","5","5");t(e,34,0,"29%");t(e,36,0,"15","Spread ID*");t(e,38,0,"20","a-zA-Z0-9\\-_","spreadIdTxtInput","Spread ID*","150","22",n.Lb(1,"","add"==i.screenName?"true":"false",""));t(e,40,0,"38%");t(e,42,0,"15","Spread Name*");t(e,44,0,"30","A-Za-z0-9\\d_ !\\\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^`{|}~","spreadNameTxtInput","Spread Name","210","22","true");t(e,46,0,"33%");t(e,48,0,"10","15","Currency");t(e,50,0,"currencyList","Select Currency","70","currencyComboBox",n.Lb(1,"","add"==i.screenName?"true":"false",""));t(e,52,0,"10"," ","normal");t(e,54,0,"processPointsPanel","100%",n.Lb(1,"","add"==i.screenName?"89%":"49%",""),"5","10","Spread Process Points");t(e,56,0,"100%",n.Lb(1,"","add"==i.screenName?"345":"180",""));t(e,58,0,"spreadDetailsCanvasGrid","90%","100%","false");t(e,60,0,"center","middle","10%","100%","5","5");t(e,62,0,"addButton","70","6");t(e,64,0,"changeButton","70","6");t(e,66,0,"viewButton","70","6","true");t(e,68,0,"deleteButton","70","true");t(e,70,0,"100%",n.Lb(1,"","add"==i.screenName?"0%":"40%",""),n.Lb(1,"","add"==i.screenName?"false":"true",""));t(e,72,0,"100%","98%","5","Associated Account Groups");t(e,74,0,"aggregateAccountsCanvasGrid","100%","150","false");t(e,76,0,"100%","9%");t(e,78,0,"100%");t(e,80,0,"100%","100%");t(e,82,0,"saveButton");t(e,84,0,"amendButton","70","false","false","Amend");t(e,86,0,"cancelAmendButton","70","false","false","true");t(e,88,0,"closeButton","70","false","false","5","true");t(e,90,0,"cancelButton","5","true");t(e,92,0,"right","10","5");t(e,94,0,"acceptButton","70","false","Accept");t(e,96,0,"rejectButton","70","false","Reject","true"),t(e,98,0);t(e,100,0,"printButton","printIcon",!0);t(e,102,0,"helpIcon","true",!0,"spread-profile")},null)}function q(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-spread-profiles-maintenance-add",[],null,null,null,W,J)),n.Ib(1,4440064,null,0,r,[s.i,n.r,o.k],null,null)],function(t,e){t(e,1,0)},null)}var V=n.Fb("app-spread-profiles-maintenance-add",r,q,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);