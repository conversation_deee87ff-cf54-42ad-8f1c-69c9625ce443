{"_from": "@ngx-translate/core@^11.0.1", "_id": "@ngx-translate/core@11.0.1", "_inBundle": false, "_integrity": "sha512-nBCa1ZD9fAUY/3eskP3Lql2fNg8OMrYIej1/5GRsfcutx9tG/5fZLCv9m6UCw1aS+u4uK/vXjv1ctG/FdMvaWg==", "_location": "/@ngx-translate/core", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@ngx-translate/core@^11.0.1", "name": "@ngx-translate/core", "escapedName": "@ngx-translate%2fcore", "scope": "@ngx-translate", "rawSpec": "^11.0.1", "saveSpec": null, "fetchSpec": "^11.0.1"}, "_requiredBy": ["/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@ngx-translate/core/-/core-11.0.1.tgz", "_shasum": "cecefad41f06368f5859dac48fec8fcc4485615f", "_spec": "@ngx-translate/core@^11.0.1", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/ngx-translate/core/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "description": "The internationalization (i18n) library for Angular", "devDependencies": {"semantic-release": "8.2.3"}, "es2015": "fesm2015/ngx-translate-core.js", "esm2015": "esm2015/ngx-translate-core.js", "esm5": "esm5/ngx-translate-core.js", "fesm2015": "fesm2015/ngx-translate-core.js", "fesm5": "fesm5/ngx-translate-core.js", "homepage": "https://github.com/ngx-translate/core", "keywords": ["angular", "angular 2", "i18n", "translate", "ngx-translate"], "license": "MIT", "main": "bundles/ngx-translate-core.umd.js", "metadata": "ngx-translate-core.metadata.json", "module": "fesm5/ngx-translate-core.js", "name": "@ngx-translate/core", "peerDependencies": {"rxjs": ">=6.3.0", "@angular/core": ">=7.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/ngx-translate/core.git"}, "scripts": {"semantic-release": "semantic-release pre && npm publish && semantic-release post"}, "sideEffects": false, "typings": "ngx-translate-core.d.ts", "version": "11.0.1"}