/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.5 (2019-12-19)
 */
!function(f){"use strict";function o(e){return e}var R=function(e){function n(){return t}var t=e;return{get:n,set:function(e){t=e},clone:function(){return R(n())}}},T=function(){},O=function(t,r){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return t(r.apply(null,e))}},D=function(e){return function(){return e}};function b(r){for(var o=[],e=1;e<arguments.length;e++)o[e-1]=arguments[e];return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=o.concat(e);return r.apply(null,t)}}function d(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return!t.apply(null,e)}}function e(){return u}var n,s=D(!1),i=D(!0),u=(n={fold:function(e,n){return e()},is:s,isSome:s,isNone:i,getOr:c,getOrThunk:r,getOrDie:function(e){throw new Error(e||"error: getOrDie called on none.")},getOrNull:D(null),getOrUndefined:D(undefined),or:c,orThunk:r,map:e,each:T,bind:e,exists:s,forall:i,filter:e,equals:t,equals_:t,toArray:function(){return[]},toString:D("none()")},Object.freeze&&Object.freeze(n),n);function t(e){return e.isNone()}function r(e){return e()}function c(e){return e}function a(n){return function(e){return function(e){if(null===e)return"null";var n=typeof e;return"object"==n&&(Array.prototype.isPrototypeOf(e)||e.constructor&&"Array"===e.constructor.name)?"array":"object"==n&&(String.prototype.isPrototypeOf(e)||e.constructor&&"String"===e.constructor.name)?"string":n}(e)===n}}function l(e,n){return-1<function(e,n){return Ue.call(e,n)}(e,n)}function m(e,n){for(var t=0,r=e.length;t<r;t++){if(n(e[t],t))return!0}return!1}function g(e,n){for(var t=e.length,r=new Array(t),o=0;o<t;o++){var i=e[o];r[o]=n(i,o)}return r}function p(e,n){for(var t=0,r=e.length;t<r;t++){n(e[t],t)}}function h(e,n){for(var t=[],r=0,o=e.length;r<o;r++){var i=e[r];n(i,r)&&t.push(i)}return t}function v(e,n,t){return function(e,n){for(var t=e.length-1;0<=t;t--){n(e[t],t)}}(e,function(e){t=n(t,e)}),t}function w(e,n,t){return p(e,function(e){t=n(t,e)}),t}function y(e,n){for(var t=0,r=e.length;t<r;t++){var o=e[t];if(n(o,t))return Me.some(o)}return Me.none()}function C(e,n){for(var t=0,r=e.length;t<r;t++){if(n(e[t],t))return Me.some(t)}return Me.none()}function S(e){for(var n=[],t=0,r=e.length;t<r;++t){if(!Le(e[t]))throw new Error("Arr.flatten item "+t+" was not an array, input: "+e);qe.apply(n,e[t])}return n}function x(e,n){var t=g(e,n);return S(t)}function A(e,n){for(var t=0,r=e.length;t<r;++t){if(!0!==n(e[t],t))return!1}return!0}function E(e){var n=Fe.call(e,0);return n.reverse(),n}function N(e,n){for(var t=Ve(e),r=0,o=t.length;r<o;r++){var i=t[r];n(e[i],i)}}function k(e,t){return Ye(e,function(e,n){return{k:n,v:t(e,n)}})}function I(e,n){return Ke(e,n)?Me.from(e[n]):Me.none()}function B(){for(var n=[],e=0;e<arguments.length;e++)n[e]=arguments[e];return function(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];if(n.length!==t.length)throw new Error('Wrong number of arguments to struct. Expected "['+n.length+']", got '+t.length+" arguments");var r={};return p(n,function(e,n){r[e]=D(t[n])}),r}}function P(e){return e.slice(0).sort()}function M(e,n){throw new Error("All required keys ("+P(e).join(", ")+") were not specified. Specified keys were: "+P(n).join(", ")+".")}function W(e){throw new Error("Unsupported keys for object: "+P(e).join(", "))}function _(n,e){if(!Le(e))throw new Error("The "+n+" fields must be an array. Was: "+e+".");p(e,function(e){if(!_e(e))throw new Error("The value "+e+" in the "+n+" fields was not a string.")})}function L(e){var t=P(e);y(t,function(e,n){return n<t.length-1&&e===t[n+1]}).each(function(e){throw new Error("The field: "+e+" occurs more than once in the combined fields: ["+t.join(", ")+"].")})}function j(e){return e.dom().nodeType}function z(n){return function(e){return j(e)===n}}function H(e){return j(e)===$e||"#comment"===en(e)}function F(e,n,t){if(!(_e(t)||je(t)||He(t)))throw f.console.error("Invalid call to Attr.set. Key ",n,":: Value ",t,":: Element ",e),new Error("Attribute value was not simple");e.setAttribute(n,t+"")}function U(e,n,t){F(e.dom(),n,t)}function q(e,n){var t=e.dom();N(n,function(e,n){F(t,n,e)})}function V(e,n){var t=e.dom().getAttribute(n);return null===t?undefined:t}function G(e,n){var t=e.dom();return!(!t||!t.hasAttribute)&&t.hasAttribute(n)}function Y(e,n){e.dom().removeAttribute(n)}function K(e){return w(e.dom().attributes,function(e,n){return e[n.name]=n.value,e},{})}function X(e,n,t){return""===n||!(e.length<n.length)&&e.substr(t,t+n.length)===n}function $(e,n){return-1!==e.indexOf(n)}function J(e,n){return X(e,n,0)}function Q(e){return e.style!==undefined&&ze(e.style.getPropertyValue)}function Z(t){var r,o=!1;return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return o||(o=!0,r=t.apply(null,e)),r}}function ee(e){var n=tn(e)?e.dom().parentNode:e.dom();return n!==undefined&&null!==n&&n.ownerDocument.body.contains(n)}function ne(e,n,t){if(!_e(t))throw f.console.error("Invalid call to CSS.set. Property ",n,":: Value ",t,":: Element ",e),new Error("CSS value must be a string: "+t);Q(e)&&e.style.setProperty(n,t)}function te(e,n,t){var r=e.dom();ne(r,n,t)}function re(e,n){var t=e.dom();N(n,function(e,n){ne(t,n,e)})}function oe(e,n){var t=e.dom(),r=f.window.getComputedStyle(t).getPropertyValue(n),o=""!==r||ee(e)?r:an(t,n);return null===o?undefined:o}function ie(e,n){var t=e.dom(),r=an(t,n);return Me.from(r).filter(function(e){return 0<e.length})}function ue(e,n){!function(e,n){Q(e)&&e.style.removeProperty(n)}(e.dom(),n),G(e,"style")&&""===function(e){return e.replace(/^\s+|\s+$/g,"")}(V(e,"style"))&&Y(e,"style")}function ce(e,n,t){return 0!=(e.compareDocumentPosition(n)&t)}function ae(e,n){var t=function(e,n){for(var t=0;t<e.length;t++){var r=e[t];if(r.test(n))return r}return undefined}(e,n);if(!t)return{major:0,minor:0};function r(e){return Number(n.replace(t,"$"+e))}return dn(r(1),r(2))}function le(e,n){return function(){return n===e}}function fe(e,n){return function(){return n===e}}function se(e,n){var t=String(n).toLowerCase();return y(e,function(e){return e.search(t)})}function de(n){return function(e){return $(e,n)}}function me(){return En.get()}function ge(e,n){var t=e.dom();if(t.nodeType!==Nn)return!1;var r=t;if(r.matches!==undefined)return r.matches(n);if(r.msMatchesSelector!==undefined)return r.msMatchesSelector(n);if(r.webkitMatchesSelector!==undefined)return r.webkitMatchesSelector(n);if(r.mozMatchesSelector!==undefined)return r.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")}function pe(e){return e.nodeType!==Nn&&e.nodeType!==kn||0===e.childElementCount}function he(e){return on.fromDom(e.dom().ownerDocument)}function ve(e){return Me.from(e.dom().parentNode).map(on.fromDom)}function be(e,n){for(var t=ze(n)?n:s,r=e.dom(),o=[];null!==r.parentNode&&r.parentNode!==undefined;){var i=r.parentNode,u=on.fromDom(i);if(o.push(u),!0===t(u))break;r=i}return o}function we(e){return Me.from(e.dom().previousSibling).map(on.fromDom)}function ye(e){return Me.from(e.dom().nextSibling).map(on.fromDom)}function Ce(e){return g(e.dom().childNodes,on.fromDom)}function Se(e,n){var t=e.dom().childNodes;return Me.from(t[n]).map(on.fromDom)}function xe(n,t){ve(n).each(function(e){e.dom().insertBefore(t.dom(),n.dom())})}function Re(e,n){ye(e).fold(function(){ve(e).each(function(e){Mn(e,n)})},function(e){xe(e,n)})}function Te(n,t){(function(e){return Se(e,0)})(n).fold(function(){Mn(n,t)},function(e){n.dom().insertBefore(t.dom(),e.dom())})}function Oe(e,n){xe(e,n),Mn(n,e)}function De(r,o){p(o,function(e,n){var t=0===n?r:o[n-1];Re(t,e)})}function Ae(n,e){p(e,function(e){Mn(n,e)})}function Ee(e){e.dom().textContent="",p(Ce(e),function(e){Wn(e)})}function Ne(e){var n=Ce(e);0<n.length&&function(n,e){p(e,function(e){xe(n,e)})}(e,n),Wn(e)}function ke(e,n,t){return function(e,n,t){return h(be(e,t),n)}(e,function(e){return ge(e,n)},t)}function Ie(e,n){return function(e,n){return h(Ce(e),n)}(e,function(e){return ge(e,n)})}function Be(e,n){return function(e,n){var t=n===undefined?f.document:n.dom();return pe(t)?[]:g(t.querySelectorAll(e),on.fromDom)}(n,e)}var Pe=function(t){function e(){return o}function n(e){return e(t)}var r=D(t),o={fold:function(e,n){return n(t)},is:function(e){return t===e},isSome:i,isNone:s,getOr:r,getOrThunk:r,getOrDie:r,getOrNull:r,getOrUndefined:r,or:e,orThunk:e,map:function(e){return Pe(e(t))},each:function(e){e(t)},bind:n,exists:n,forall:n,filter:function(e){return e(t)?o:u},toArray:function(){return[t]},toString:function(){return"some("+t+")"},equals:function(e){return e.is(t)},equals_:function(e,n){return e.fold(s,function(e){return n(t,e)})}};return o},Me={some:Pe,none:e,from:function(e){return null===e||e===undefined?u:Pe(e)}},We=tinymce.util.Tools.resolve("tinymce.PluginManager"),_e=a("string"),Le=a("array"),je=a("boolean"),ze=a("function"),He=a("number"),Fe=Array.prototype.slice,Ue=Array.prototype.indexOf,qe=Array.prototype.push,Ve=(ze(Array.from)&&Array.from,Object.keys),Ge=Object.hasOwnProperty,Ye=function(e,r){var o={};return N(e,function(e,n){var t=r(e,n);o[t.k]=t.v}),o},Ke=function(e,n){return Ge.call(e,n)},Xe=function(o,i){var u=o.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return _("required",o),_("optional",i),L(u),function(n){var t=Ve(n);A(o,function(e){return l(t,e)})||M(o,t);var e=h(t,function(e){return!l(u,e)});0<e.length&&W(e);var r={};return p(o,function(e){r[e]=D(n[e])}),p(i,function(e){r[e]=D(Object.prototype.hasOwnProperty.call(n,e)?Me.some(n[e]):Me.none())}),r}},$e=(f.Node.ATTRIBUTE_NODE,f.Node.CDATA_SECTION_NODE,f.Node.COMMENT_NODE),Je=f.Node.DOCUMENT_NODE,Qe=(f.Node.DOCUMENT_TYPE_NODE,f.Node.DOCUMENT_FRAGMENT_NODE,f.Node.ELEMENT_NODE),Ze=f.Node.TEXT_NODE,en=(f.Node.PROCESSING_INSTRUCTION_NODE,f.Node.ENTITY_REFERENCE_NODE,f.Node.ENTITY_NODE,f.Node.NOTATION_NODE,"undefined"!=typeof f.window?f.window:Function("return this;")(),function(e){return e.dom().nodeName.toLowerCase()}),nn=z(Qe),tn=z(Ze),rn=function(e){if(null===e||e===undefined)throw new Error("Node cannot be null or undefined");return{dom:D(e)}},on={fromHtml:function(e,n){var t=(n||f.document).createElement("div");if(t.innerHTML=e,!t.hasChildNodes()||1<t.childNodes.length)throw f.console.error("HTML does not have a single root node",e),new Error("HTML must have a single root node");return rn(t.childNodes[0])},fromTag:function(e,n){var t=(n||f.document).createElement(e);return rn(t)},fromText:function(e,n){var t=(n||f.document).createTextNode(e);return rn(t)},fromDom:rn,fromPoint:function(e,n,t){var r=e.dom();return Me.from(r.elementFromPoint(n,t)).map(rn)}},un=Z(function(){return cn(on.fromDom(f.document))}),cn=function(e){var n=e.dom().body;if(null===n||n===undefined)throw new Error("Body is not available yet");return on.fromDom(n)},an=function(e,n){return Q(e)?e.style.getPropertyValue(n):""},ln=function(e,n){return ce(e,n,f.Node.DOCUMENT_POSITION_CONTAINED_BY)},fn=function(){return(fn=Object.assign||function(e){for(var n,t=1,r=arguments.length;t<r;t++)for(var o in n=arguments[t])Object.prototype.hasOwnProperty.call(n,o)&&(e[o]=n[o]);return e}).apply(this,arguments)},sn=function(){return dn(0,0)},dn=function(e,n){return{major:e,minor:n}},mn={nu:dn,detect:function(e,n){var t=String(n).toLowerCase();return 0===e.length?sn():ae(e,t)},unknown:sn},gn="Firefox",pn=function(e){var n=e.current;return{current:n,version:e.version,isEdge:le("Edge",n),isChrome:le("Chrome",n),isIE:le("IE",n),isOpera:le("Opera",n),isFirefox:le(gn,n),isSafari:le("Safari",n)}},hn={unknown:function(){return pn({current:undefined,version:mn.unknown()})},nu:pn,edge:D("Edge"),chrome:D("Chrome"),ie:D("IE"),opera:D("Opera"),firefox:D(gn),safari:D("Safari")},vn="Windows",bn="Android",wn="Solaris",yn="FreeBSD",Cn=function(e){var n=e.current;return{current:n,version:e.version,isWindows:fe(vn,n),isiOS:fe("iOS",n),isAndroid:fe(bn,n),isOSX:fe("OSX",n),isLinux:fe("Linux",n),isSolaris:fe(wn,n),isFreeBSD:fe(yn,n)}},Sn={unknown:function(){return Cn({current:undefined,version:mn.unknown()})},nu:Cn,windows:D(vn),ios:D("iOS"),android:D(bn),linux:D("Linux"),osx:D("OSX"),solaris:D(wn),freebsd:D(yn)},xn=function(e,t){return se(e,t).map(function(e){var n=mn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},Rn=function(e,t){return se(e,t).map(function(e){var n=mn.detect(e.versionRegexes,t);return{current:e.name,version:n}})},Tn=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,On=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(e){return $(e,"edge/")&&$(e,"chrome")&&$(e,"safari")&&$(e,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Tn],search:function(e){return $(e,"chrome")&&!$(e,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(e){return $(e,"msie")||$(e,"trident")}},{name:"Opera",versionRegexes:[Tn,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:de("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:de("firefox")},{name:"Safari",versionRegexes:[Tn,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(e){return($(e,"safari")||$(e,"mobile/"))&&$(e,"applewebkit")}}],Dn=[{name:"Windows",search:de("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(e){return $(e,"iphone")||$(e,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:de("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:de("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:de("linux"),versionRegexes:[]},{name:"Solaris",search:de("sunos"),versionRegexes:[]},{name:"FreeBSD",search:de("freebsd"),versionRegexes:[]}],An={browsers:D(On),oses:D(Dn)},En=R(function(e,n){var t=An.browsers(),r=An.oses(),o=xn(t,e).fold(hn.unknown,hn.nu),i=Rn(r,e).fold(Sn.unknown,Sn.nu);return{browser:o,os:i,deviceType:function(e,n,t,r){var o=e.isiOS()&&!0===/ipad/i.test(t),i=e.isiOS()&&!o,u=e.isiOS()||e.isAndroid(),c=u||r("(pointer:coarse)"),a=o||!i&&u&&r("(min-device-width:768px)"),l=i||u&&!a,f=n.isSafari()&&e.isiOS()&&!1===/safari/i.test(t),s=!l&&!a&&!f;return{isiPad:D(o),isiPhone:D(i),isTablet:D(a),isPhone:D(l),isTouch:D(c),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:D(f),isDesktop:D(s)}}(i,o,e,n)}}(f.navigator.userAgent,function(e){return f.window.matchMedia(e).matches})),Nn=Qe,kn=Je,In=function(e,n){return e.dom()===n.dom()},Bn=me().browser.isIE()?function(e,n){return ln(e.dom(),n.dom())}:function(e,n){var t=e.dom(),r=n.dom();return t!==r&&t.contains(r)},Pn=ge,Mn=(B("element","offset"),function(e,n){e.dom().appendChild(n.dom())}),Wn=function(e){var n=e.dom();null!==n.parentNode&&n.parentNode.removeChild(n)},_n=(B("width","height"),B("width","height"),B("rows","columns")),Ln=B("row","column"),jn=(B("x","y"),B("element","rowspan","colspan")),zn=B("element","rowspan","colspan","isNew"),Hn=B("element","rowspan","colspan","row","column"),Fn=B("element","cells","section"),Un=B("element","isNew"),qn=B("element","cells","section","isNew"),Vn=B("cells","section"),Gn=B("details","section"),Yn=B("startRow","startCol","finishRow","finishCol"),Kn=function(e,n){var t=[];return p(Ce(e),function(e){n(e)&&(t=t.concat([e])),t=t.concat(Kn(e,n))}),t};function Xn(e,n,t,r,o){return e(t,r)?Me.some(t):ze(o)&&o(t)?Me.none():n(t,r,o)}function $n(e,n,t){for(var r=e.dom(),o=ze(t)?t:D(!1);r.parentNode;){r=r.parentNode;var i=on.fromDom(r);if(n(i))return Me.some(i);if(o(i))break}return Me.none()}function Jn(e,n,t){return $n(e,function(e){return ge(e,n)},t)}function Qn(e,n){return function(e,n){return y(e.dom().childNodes,function(e){return n(on.fromDom(e))}).map(on.fromDom)}(e,function(e){return ge(e,n)})}function Zn(e,n){return function(e,n){var t=n===undefined?f.document:n.dom();return pe(t)?Me.none():Me.from(t.querySelector(e)).map(on.fromDom)}(n,e)}function et(e,n,t){return Xn(ge,Jn,e,n,t)}function nt(e,n,t){return void 0===t&&(t=s),t(n)?Me.none():l(e,en(n))?Me.some(n):Jn(n,e.join(","),function(e){return ge(e,"table")||t(e)})}function tt(n,e){return ve(e).map(function(e){return Ie(e,n)})}function rt(e,n){return parseInt(V(e,n),10)}function ot(e,n){return e+","+n}var it=function(e,n,t){return x(Ce(e),function(e){return ge(e,n)?t(e)?[e]:[]:it(e,n,t)})},ut={firstLayer:function(e,n){return it(e,n,D(!0))},filterFirstLayer:it},ct=b(tt,"th,td"),at=b(tt,"tr"),lt={cell:function(e,n){return nt(["td","th"],e,n)},firstCell:function(e){return Zn(e,"th,td")},cells:function(e){return ut.firstLayer(e,"th,td")},neighbourCells:ct,table:function(e,n){return et(e,"table",n)},row:function(e,n){return nt(["tr"],e,n)},rows:function(e){return ut.firstLayer(e,"tr")},notCell:function(e,n){return nt(["caption","tr","tbody","tfoot","thead"],e,n)},neighbourRows:at,attr:rt,grid:function(e,n,t){var r=rt(e,n),o=rt(e,t);return _n(r,o)}},ft=function(e){var n=lt.rows(e);return g(n,function(e){var n=e,t=ve(n).map(function(e){var n=en(e);return"tfoot"===n||"thead"===n||"tbody"===n?n:"tbody"}).getOr("tbody"),r=g(lt.cells(e),function(e){var n=G(e,"rowspan")?parseInt(V(e,"rowspan"),10):1,t=G(e,"colspan")?parseInt(V(e,"colspan"),10):1;return jn(e,n,t)});return Fn(n,r,t)})},st=function(e,t){return g(e,function(e){var n=g(lt.cells(e),function(e){var n=G(e,"rowspan")?parseInt(V(e,"rowspan"),10):1,t=G(e,"colspan")?parseInt(V(e,"colspan"),10):1;return jn(e,n,t)});return Fn(e,n,t.section())})},dt=function(e,n){var t=x(e.all(),function(e){return e.cells()});return h(t,n)},mt={generate:function(e){var l={},n=[],t=e.length,f=0;p(e,function(e,c){var a=[];p(e.cells(),function(e){for(var n=0;l[ot(c,n)]!==undefined;)n++;for(var t=Hn(e.element(),e.rowspan(),e.colspan(),c,n),r=0;r<e.colspan();r++)for(var o=0;o<e.rowspan();o++){var i=n+r,u=ot(c+o,i);l[u]=t,f=Math.max(f,i+1)}a.push(t)}),n.push(Fn(e.element(),a,e.section()))});var r=_n(t,f);return{grid:D(r),access:D(l),all:D(n)}},getAt:function(e,n,t){var r=e.access()[ot(n,t)];return r!==undefined?Me.some(r):Me.none()},findItem:function(e,n,t){var r=dt(e,function(e){return t(n,e.element())});return 0<r.length?Me.some(r[0]):Me.none()},filterItems:dt,justCells:function(e){var n=g(e.all(),function(e){return e.cells()});return S(n)}},gt=B("minRow","minCol","maxRow","maxCol"),pt=function(e,n){function t(e){return ge(e.element(),n)}var r=ft(e),o=mt.generate(r),i=function(e,i){var n=e.grid().columns(),u=e.grid().rows(),c=n,a=0,l=0;return N(e.access(),function(e){if(i(e)){var n=e.row(),t=n+e.rowspan()-1,r=e.column(),o=r+e.colspan()-1;n<u?u=n:a<t&&(a=t),r<c?c=r:l<o&&(l=o)}}),gt(u,c,a,l)}(o,t),u="th:not("+n+"),td:not("+n+")",c=ut.filterFirstLayer(e,"th,td",function(e){return ge(e,u)});return p(c,Wn),function(e,n,t,r){for(var o,i,u,c=n.grid().columns(),a=n.grid().rows(),l=0;l<a;l++)for(var f=!1,s=0;s<c;s++){if(!(l<t.minRow()||l>t.maxRow()||s<t.minCol()||s>t.maxCol()))mt.getAt(n,l,s).filter(r).isNone()?(o=f,void 0,i=e[l].element(),u=on.fromTag("td"),Mn(u,on.fromTag("br")),(o?Mn:Te)(i,u)):f=!0}}(r,o,i,t),function(e,n){var t=h(ut.firstLayer(e,"tr"),function(e){return 0===e.dom().childElementCount});p(t,Wn),n.minCol()!==n.maxCol()&&n.minRow()!==n.maxRow()||p(ut.firstLayer(e,"th,td"),function(e){Y(e,"rowspan"),Y(e,"colspan")}),Y(e,"width"),Y(e,"height"),ue(e,"width"),ue(e,"height")}(e,i),e};function ht(e){return Bt.get(e)}function vt(e){return Bt.getOption(e)}function bt(e,n){Bt.set(e,n)}function wt(e){return"img"===en(e)?1:vt(e).fold(function(){return Ce(e).length},function(e){return e.length})}function yt(e){return function(e){return vt(e).filter(function(e){return 0!==e.trim().length||-1<e.indexOf("\xa0")}).isSome()}(e)||l(Pt,en(e))}function Ct(e){return function(e,o){var i=function(e){for(var n=0;n<e.childNodes.length;n++){var t=on.fromDom(e.childNodes[n]);if(o(t))return Me.some(t);var r=i(e.childNodes[n]);if(r.isSome())return r}return Me.none()};return i(e.dom())}(e,yt)}function St(e){return Mt(e,yt)}function xt(e,n){return on.fromDom(e.dom().cloneNode(n))}function Rt(e){return xt(e,!1)}function Tt(e){return xt(e,!0)}function Ot(e,n){var t=function(e,n){var t=on.fromTag(n),r=K(e);return q(t,r),t}(e,n),r=Ce(Tt(e));return Ae(t,r),t}function Dt(){var e=on.fromTag("td");return Mn(e,on.fromTag("br")),e}function At(e,n,t){var r=Ot(e,n);return N(t,function(e,n){null===e?Y(r,n):U(r,n,e)}),r}function Et(e){return e}function Nt(e){return function(){return on.fromTag("tr",e.dom())}}function kt(e,n){return n.column()>=e.startCol()&&n.column()+n.colspan()-1<=e.finishCol()&&n.row()>=e.startRow()&&n.row()+n.rowspan()-1<=e.finishRow()}function It(e,n,t){var r=mt.findItem(e,n,In),o=mt.findItem(e,t,In);return r.bind(function(n){return o.map(function(e){return function(e,n){return Yn(Math.min(e.row(),n.row()),Math.min(e.column(),n.column()),Math.max(e.row()+e.rowspan()-1,n.row()+n.rowspan()-1),Math.max(e.column()+e.colspan()-1,n.column()+n.colspan()-1))}(n,e)})})}var Bt=function Qf(t,r){var n=function(e){return t(e)?Me.from(e.dom().nodeValue):Me.none()};return{get:function(e){if(!t(e))throw new Error("Can only get "+r+" value of a "+r+" node");return n(e).getOr("")},getOption:n,set:function(e,n){if(!t(e))throw new Error("Can only set raw "+r+" value of a "+r+" node");e.dom().nodeValue=n}}}(tn,"text"),Pt=["img","br"],Mt=function(e,i){var u=function(e){for(var n=Ce(e),t=n.length-1;0<=t;t--){var r=n[t];if(i(r))return Me.some(r);var o=u(r);if(o.isSome())return o}return Me.none()};return u(e)},Wt={cellOperations:function(i,e,u){return{row:Nt(e),cell:function(e){var n=he(e.element()),t=on.fromTag(en(e.element()),n.dom()),r=u.getOr(["strong","em","b","i","span","font","h1","h2","h3","h4","h5","h6","p","div"]),o=0<r.length?function(r,o,i){return Ct(r).map(function(e){var n=i.join(","),t=ke(e,n,function(e){return In(e,r)});return v(t,function(e,n){var t=Rt(n);return Y(t,"contenteditable"),Mn(e,t),t},o)}).getOr(o)}(e.element(),t,r):t;return Mn(o,on.fromTag("br")),function(e,n){var t=e.dom(),r=n.dom();Q(t)&&Q(r)&&(r.style.cssText=t.style.cssText)}(e.element(),t),ue(t,"height"),1!==e.colspan()&&ue(e.element(),"width"),i(e.element(),t),t},replace:At,gap:Dt}},paste:function(e){return{row:Nt(e),cell:Dt,replace:Et,gap:Dt}}},_t=function(e,n){var t=n.column(),r=n.column()+n.colspan()-1,o=n.row(),i=n.row()+n.rowspan()-1;return t<=e.finishCol()&&r>=e.startCol()&&o<=e.finishRow()&&i>=e.startRow()},Lt=function(e,n){for(var t=!0,r=b(kt,n),o=n.startRow();o<=n.finishRow();o++)for(var i=n.startCol();i<=n.finishCol();i++)t=t&&mt.getAt(e,o,i).exists(r);return t?Me.some(n):Me.none()},jt=It,zt=function(n,e,t){return It(n,e,t).bind(function(e){return Lt(n,e)})},Ht=function(r,e,o,i){return mt.findItem(r,e,In).bind(function(e){var n=0<o?e.row()+e.rowspan()-1:e.row(),t=0<i?e.column()+e.colspan()-1:e.column();return mt.getAt(r,n+o,t+i).map(function(e){return e.element()})})},Ft=function(t,e,n){return jt(t,e,n).map(function(e){var n=mt.filterItems(t,b(_t,e));return g(n,function(e){return e.element()})})},Ut=function(e,n){return mt.findItem(e,n,function(e,n){return Bn(n,e)}).map(function(e){return e.element()})},qt=function(e){var n=ft(e);return mt.generate(n)},Vt=function(t,r,o){return lt.table(t).bind(function(e){var n=qt(e);return Ht(n,t,r,o)})},Gt=function(e,n,t){var r=qt(e);return Ft(r,n,t)},Yt=function(e,n,t,r,o){var i=qt(e),u=In(e,t)?Me.some(n):Ut(i,n),c=In(e,o)?Me.some(r):Ut(i,r);return u.bind(function(n){return c.bind(function(e){return Ft(i,n,e)})})},Kt=function(e,n,t){var r=qt(e);return zt(r,n,t)},Xt=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","li","table","thead","tbody","tfoot","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"];function $t(){return{up:D({selector:Jn,closest:et,predicate:$n,all:be}),down:D({selector:Be,predicate:Kn}),styles:D({get:oe,getRaw:ie,set:te,remove:ue}),attrs:D({get:V,set:U,remove:Y,copyTo:function(e,n){var t=K(e);q(n,t)}}),insert:D({before:xe,after:Re,afterAll:De,append:Mn,appendAll:Ae,prepend:Te,wrap:Oe}),remove:D({unwrap:Ne,remove:Wn}),create:D({nu:on.fromTag,clone:function(e){return on.fromDom(e.dom().cloneNode(!1))},text:on.fromText}),query:D({comparePosition:function(e,n){return e.dom().compareDocumentPosition(n.dom())},prevSibling:we,nextSibling:ye}),property:D({children:Ce,name:en,parent:ve,document:function(e){return e.dom().ownerDocument},isText:tn,isComment:H,isElement:nn,getText:ht,setText:bt,isBoundary:function(e){return!!nn(e)&&("body"===en(e)||l(Xt,en(e)))},isEmptyTag:function(e){return!!nn(e)&&l(["br","img","hr","input"],en(e))}}),eq:In,is:Pn}}function Jt(e,n,t){var r=e.property().children(n);return C(r,b(e.eq,t)).map(function(e){return{before:D(r.slice(0,e)),after:D(r.slice(e+1))}})}function Qt(e,n){return b(e.eq,n)}function Zt(n,e,t,r){function o(n){return C(n,r).fold(function(){return n},function(e){return n.slice(0,e+1)})}void 0===r&&(r=s);var i=[e].concat(n.up().all(e)),u=[t].concat(n.up().all(t)),c=o(i),a=o(u),l=y(c,function(e){return m(a,Qt(n,e))});return{firstpath:D(c),secondpath:D(a),shared:D(l)}}function er(e){return Jn(e,"table")}function nr(c,a,r){function l(n){return function(e){return r!==undefined&&r(e)||In(e,n)}}return In(c,a)?Me.some(sr.create({boxes:Me.some([c]),start:c,finish:a})):er(c).bind(function(u){return er(a).bind(function(i){if(In(u,i))return Me.some(sr.create({boxes:Gt(u,c,a),start:c,finish:a}));if(Bn(u,i)){var e=0<(n=ke(a,"td,th",l(u))).length?n[n.length-1]:a;return Me.some(sr.create({boxes:Yt(u,c,u,a,i),start:c,finish:e}))}if(Bn(i,u)){var n,t=0<(n=ke(c,"td,th",l(i))).length?n[n.length-1]:c;return Me.some(sr.create({boxes:Yt(i,c,u,a,i),start:c,finish:t}))}return fr.ancestors(c,a).shared().bind(function(e){return et(e,"table",r).bind(function(e){var n=ke(a,"td,th",l(e)),t=0<n.length?n[n.length-1]:a,r=ke(c,"td,th",l(e)),o=0<r.length?r[r.length-1]:c;return Me.some(sr.create({boxes:Yt(e,c,u,a,i),start:o,finish:t}))})})})})}function tr(e,n){return Rr.cata(n.get(),D([]),o,D([e]))}function rr(e){return{element:D(e),mergable:Me.none,unmergable:Me.none,selection:D([e])}}var or=B("left","right"),ir=B("first","second","splits"),ur=function(r,o,e,n){var t=o(r,e);return v(n,function(e,n){var t=o(r,n);return cr(r,e,t)},t)},cr=function(n,e,t){return e.bind(function(e){return t.filter(b(n.eq,e))})},ar={sharedOne:function(e,n,t){return 0<t.length?function(e,n,t,r){return r(e,n,t[0],t.slice(1))}(e,n,t,ur):Me.none()},subset:function(n,e,t){var r=Zt(n,e,t);return r.shared().bind(function(e){return function(o,i,e,n){var u=o.property().children(i);if(o.eq(i,e[0]))return Me.some([e[0]]);if(o.eq(i,n[0]))return Me.some([n[0]]);function t(e){var n=E(e),t=C(n,Qt(o,i)).getOr(-1),r=t<n.length-1?n[t+1]:n[t];return C(u,Qt(o,r))}var r=t(e),c=t(n);return r.bind(function(r){return c.map(function(e){var n=Math.min(r,e),t=Math.max(r,e);return u.slice(n,t+1)})})}(n,e,r.firstpath(),r.secondpath())})},ancestors:Zt,breakToLeft:function(t,r,o){return Jt(t,r,o).map(function(e){var n=t.create().clone(r);return t.insert().appendAll(n,e.before().concat([o])),t.insert().appendAll(r,e.after()),t.insert().before(r,n),or(n,r)})},breakToRight:function(t,r,e){return Jt(t,r,e).map(function(e){var n=t.create().clone(r);return t.insert().appendAll(n,e.after()),t.insert().after(r,n),or(r,n)})},breakPath:function(i,e,u,c){var a=function(e,n,o){var t=ir(e,Me.none(),o);return u(e)?ir(e,n,o):i.property().parent(e).bind(function(r){return c(i,r,e).map(function(e){var n=[{first:e.left,second:e.right}],t=u(r)?r:e.left();return a(t,Me.some(e.right()),o.concat(n))})}).getOr(t)};return a(e,Me.none(),[])}},lr=$t(),fr={sharedOne:function(t,e){return ar.sharedOne(lr,function(e,n){return t(n)},e)},subset:function(e,n){return ar.subset(lr,e,n)},ancestors:function(e,n,t){return ar.ancestors(lr,e,n,t)},breakToLeft:function(e,n){return ar.breakToLeft(lr,e,n)},breakToRight:function(e,n){return ar.breakToRight(lr,e,n)},breakPath:function(e,n,r){return ar.breakPath(lr,e,n,function(e,n,t){return r(n,t)})}},sr={create:Xe(["boxes","start","finish"],[])},dr=nr,mr=function(e,n){var t=Be(e,n);return 0<t.length?Me.some(t):Me.none()},gr=function(e,n,t,r,o){return function(e,n){return y(e,function(e){return ge(e,n)})}(e,o).bind(function(e){return Vt(e,n,t).bind(function(e){return function(n,t){return Jn(n,"table").bind(function(e){return Zn(e,t).bind(function(e){return nr(e,n).bind(function(n){return n.boxes().map(function(e){return{boxes:D(e),start:D(n.start()),finish:D(n.finish())}})})})})}(e,r)})})},pr=function(e,n,r){return Zn(e,n).bind(function(t){return Zn(e,r).bind(function(n){return fr.sharedOne(er,[t,n]).map(function(e){return{first:D(t),last:D(n),table:D(e)}})})})},hr=function(e,n){return mr(e,n)},vr=function(o,e,n){return pr(o,e,n).bind(function(t){function e(e){return In(o,e)}var n=Jn(t.first(),"thead,tfoot,tbody,table",e),r=Jn(t.last(),"thead,tfoot,tbody,table",e);return n.bind(function(n){return r.bind(function(e){return In(n,e)?Kt(t.table(),t.first(),t.last()):Me.none()})})})},br="data-mce-selected",wr="data-mce-first-selected",yr="data-mce-last-selected",Cr={selected:D(br),selectedSelector:D("td[data-mce-selected],th[data-mce-selected]"),attributeSelector:D("[data-mce-selected]"),firstSelected:D(wr),firstSelectedSelector:D("td[data-mce-first-selected],th[data-mce-first-selected]"),lastSelected:D(yr),lastSelectedSelector:D("td[data-mce-last-selected],th[data-mce-last-selected]")},Sr=function(u){if(!Le(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var c=[],t={};return p(u,function(e,r){var n=Ve(e);if(1!==n.length)throw new Error("one and only one name per case");var o=n[0],i=e[o];if(t[o]!==undefined)throw new Error("duplicate key detected:"+o);if("cata"===o)throw new Error("cannot have a case named cata (sorry)");if(!Le(i))throw new Error("case arguments must be an array");c.push(o),t[o]=function(){var e=arguments.length;if(e!==i.length)throw new Error("Wrong number of arguments to case "+o+". Expected "+i.length+" ("+i+"), got "+e);for(var t=new Array(e),n=0;n<t.length;n++)t[n]=arguments[n];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[r].apply(null,t)},match:function(e){var n=Ve(e);if(c.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+c.join(",")+"\nActual: "+n.join(","));if(!A(c,function(e){return l(n,e)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+c.join(", "));return e[o].apply(null,t)},log:function(e){f.console.log(e,{constructors:c,constructor:o,params:t})}}}}),t},xr=Sr([{none:[]},{multiple:["elements"]},{single:["selection"]}]),Rr={cata:function(e,n,t,r){return e.fold(n,t,r)},none:xr.none,multiple:xr.multiple,single:xr.single},Tr=function(t,e){return Rr.cata(e.get(),Me.none,function(n,e){return 0===n.length?Me.none():vr(t,Cr.firstSelectedSelector(),Cr.lastSelectedSelector()).bind(function(e){return 1<n.length?Me.some({bounds:D(e),cells:D(n)}):Me.none()})},Me.none)},Or=function(e,n){var t=tr(e,n);return 0<t.length&&A(t,function(e){return G(e,"rowspan")&&1<parseInt(V(e,"rowspan"),10)||G(e,"colspan")&&1<parseInt(V(e,"colspan"),10)})?Me.some(t):Me.none()},Dr=tr,Ar=B("element","clipboard","generators"),Er={noMenu:rr,forMenu:function(e,n,t){return{element:D(t),mergable:D(Tr(n,e)),unmergable:D(Or(t,e)),selection:D(Dr(t,e))}},notCell:function(e){return rr(e)},paste:Ar,pasteRows:function(e,n,t,r,o){return{element:D(t),mergable:Me.none,unmergable:Me.none,selection:D(Dr(t,e)),clipboard:D(r),generators:D(o)}}},Nr={registerEvents:function(c,e,a,l){c.on("BeforeGetContent",function(n){!0===n.selection&&Rr.cata(e.get(),T,function(e){n.preventDefault(),function(e){return lt.table(e[0]).map(Tt).map(function(e){return[pt(e,Cr.attributeSelector())]})}(e).each(function(e){n.content="text"===n.format?function(e){return g(e,function(e){return e.dom().innerText}).join("")}(e):function(n,e){return g(e,function(e){return n.selection.serializer.serialize(e.dom(),{})}).join("")}(c,e)})},T)}),c.on("BeforeSetContent",function(u){!0===u.selection&&!0===u.paste&&Me.from(c.dom.getParent(c.selection.getStart(),"th,td")).each(function(e){var i=on.fromDom(e);lt.table(i).each(function(n){var e=h(function(e,n){var t=(n||f.document).createElement("div");return t.innerHTML=e,Ce(on.fromDom(t))}(u.content),function(e){return"meta"!==en(e)});if(1===e.length&&"table"===en(e[0])){u.preventDefault();var t=on.fromDom(c.getDoc()),r=Wt.paste(t),o=Er.paste(i,e[0],r);a.pasteCells(n,o).each(function(e){c.selection.setRng(e),c.focus(),l.clear(n)})}})})})}};function kr(r,o){function e(e){var n=o(e);if(n<=0||null===n){var t=oe(e,r);return parseFloat(t)||0}return n}function i(o,e){return w(e,function(e,n){var t=oe(o,n),r=t===undefined?0:parseInt(t,10);return isNaN(r)?e:e+r},0)}return{set:function(e,n){if(!He(n)&&!n.match(/^[0-9]+$/))throw new Error(r+".set accepts only positive integer values. Value was "+n);var t=e.dom();Q(t)&&(t.style[r]=n+"px")},get:e,getOuter:e,aggregate:i,max:function(e,n,t){var r=i(e,t);return r<n?n-r:0}}}function Ir(e){return Qr.get(e)}function Br(e){return Qr.getOuter(e)}function Pr(e){return Zr.get(e)}function Mr(e){return Zr.getOuter(e)}function Wr(e,n,t){return function(e,n){var t=parseFloat(e);return isNaN(t)?n:t}(oe(e,n),t)}function _r(e,n){te(e,"height",n+"px")}function Lr(e,n,t,r){var o=parseInt(e,10);return function(e,n){return X(e,n,e.length-n.length)}(e,"%")&&"table"!==en(n)?function(e,t,r,n){var o=lt.table(e).map(function(e){var n=r(e);return Math.floor(t/100*n)}).getOr(t);return n(e,o),o}(n,o,t,r):o}function jr(e){var n=function(e){return ie(e,"height").getOrThunk(function(){return no(e)+"px"})}(e);return n?Lr(n,e,Ir,_r):Ir(e)}function zr(e){return ie(e,"width").fold(function(){return Me.from(V(e,"width"))},function(e){return Me.some(e)})}function Hr(e,n){return e/n.pixelWidth()*100}function Fr(e,n){return e!==undefined?e:n!==undefined?n:0}function Ur(e){var n=e.dom().ownerDocument,t=n.body,r=n.defaultView,o=n.documentElement;if(t===e.dom())return lo(t.offsetLeft,t.offsetTop);var i=Fr(r.pageYOffset,o.scrollTop),u=Fr(r.pageXOffset,o.scrollLeft),c=Fr(o.clientTop,t.clientTop),a=Fr(o.clientLeft,t.clientLeft);return fo(e).translate(u-a,i-c)}function qr(e){return Ur(e).left()+Mr(e)}function Vr(e){return Ur(e).left()}function Gr(e,n){return mo(e,Vr(n))}function Yr(e,n){return mo(e,qr(n))}function Kr(e){return Ur(e).top()}function Xr(e,n){return so(e,Kr(n))}function $r(e,n){return so(e,Kr(n)+Br(n))}function Jr(t,n,r){if(0===r.length)return[];var e=g(r.slice(1),function(e,n){return e.map(function(e){return t(n,e)})}),o=r[r.length-1].map(function(e){return n(r.length-1,e)});return e.concat([o])}var Qr=kr("height",function(e){var n=e.dom();return ee(e)?n.getBoundingClientRect().height:n.offsetHeight}),Zr=kr("width",function(e){return e.dom().offsetWidth}),eo=me(),no=function(e){return eo.browser.isIE()||eo.browser.isEdge()?function(e){var n=Wr(e,"padding-top",0),t=Wr(e,"padding-bottom",0),r=Wr(e,"border-top-width",0),o=Wr(e,"border-bottom-width",0),i=e.dom().getBoundingClientRect().height;return"border-box"===oe(e,"box-sizing")?i:i-n-t-(r+o)}(e):Wr(e,"height",Ir(e))},to=/(\d+(\.\d+)?)(\w|%)*/,ro=/(\d+(\.\d+)?)%/,oo=/(\d+(\.\d+)?)px|em/,io=function(e,n){return G(e,n)?parseInt(V(e,n),10):1},uo={percentageBasedSizeRegex:D(ro),pixelBasedSizeRegex:D(oo),setPixelWidth:function(e,n){te(e,"width",n+"px")},setPercentageWidth:function(e,n){te(e,"width",n+"%")},setHeight:_r,getPixelWidth:function(n,t){return zr(n).fold(function(){return Pr(n)},function(e){return function(e,n,t){var r=oo.exec(n);if(null!==r)return parseInt(r[1],10);var o=ro.exec(n);return null===o?Pr(e):function(e,n){return e/100*n.pixelWidth()}(parseFloat(o[1]),t)}(n,e,t)})},getPercentageWidth:function(n,t){return zr(n).fold(function(){var e=Pr(n);return Hr(e,t)},function(e){return function(e,n,t){var r=ro.exec(n);if(null!==r)return parseFloat(r[1]);var o=Pr(e);return Hr(o,t)}(n,e,t)})},getGenericWidth:function(e){return zr(e).bind(function(e){var n=to.exec(e);return null!==n?Me.some({width:D(parseFloat(n[1])),unit:D(n[3])}):Me.none()})},setGenericWidth:function(e,n,t){te(e,"width",n+t)},getHeight:function(e){return function(e,n,t){return t(e)/io(e,n)}(e,"rowspan",jr)},getRawWidth:zr},co=function(t,r){uo.getGenericWidth(t).each(function(e){var n=e.width()/2;uo.setGenericWidth(t,n,e.unit()),uo.setGenericWidth(r,n,e.unit())})},ao=function(t,r){return{left:D(t),top:D(r),translate:function(e,n){return ao(t+e,r+n)}}},lo=ao,fo=function(e){var n=e.dom(),t=n.ownerDocument.body;return t===n?lo(t.offsetLeft,t.offsetTop):ee(e)?function(e){var n=e.getBoundingClientRect();return lo(n.left,n.top)}(n):lo(0,0)},so=B("row","y"),mo=B("col","x"),go={height:{delta:o,positions:function(e){return Jr(Xr,$r,e)},edge:Kr},rtl:{delta:function(e){return-e},edge:qr,positions:function(e){return Jr(Yr,Gr,e)}},ltr:{delta:o,edge:Vr,positions:function(e){return Jr(Gr,Yr,e)}}},po={ltr:go.ltr,rtl:go.rtl};function ho(n){function t(e){return n(e).isRtl()?po.rtl:po.ltr}return{delta:function(e,n){return t(n).delta(e,n)},edge:function(e){return t(e).edge(e)},positions:function(e,n){return t(n).positions(e,n)}}}function vo(e){for(var n=[],t=function(e){n.push(e)},r=0;r<e.length;r++)e[r].each(t);return n}function bo(e,n){for(var t=0;t<e.length;t++){var r=n(e[t],t);if(r.isSome())return r}return Me.none()}function wo(e,n,t,r){t===r?Y(e,n):U(e,n,t)}function yo(e,n){var t=V(e,n);return t===undefined||""===t?[]:t.split(" ")}function Co(e){return e.dom().classList!==undefined}function So(e,n){return function(e,n,t){var r=yo(e,n).concat([t]);return U(e,n,r.join(" ")),!0}(e,"class",n)}function xo(e,n){return function(e,n,t){var r=h(yo(e,n),function(e){return e!==t});return 0<r.length?U(e,n,r.join(" ")):Y(e,n),!1}(e,"class",n)}function Ro(e,n){Co(e)?e.dom().classList.add(n):So(e,n)}function To(e){0===(Co(e)?e.dom().classList:function(e){return yo(e,"class")}(e)).length&&Y(e,"class")}function Oo(e,n){return Co(e)&&e.dom().classList.contains(n)}function Do(e,n){for(var t=[],r=e;r<n;r++)t.push(r);return t}function Ao(n,t){if(t<0||t>=n.length-1)return Me.none();var e=n[t].fold(function(){var e=E(n.slice(0,t));return bo(e,function(e,n){return e.map(function(e){return{value:e,delta:n+1}})})},function(e){return Me.some({value:e,delta:0})}),r=n[t+1].fold(function(){var e=n.slice(t+1);return bo(e,function(e,n){return e.map(function(e){return{value:e,delta:n+1}})})},function(e){return Me.some({value:e,delta:1})});return e.bind(function(t){return r.map(function(e){var n=e.delta+t.delta;return Math.abs(e.value-t.value)/n})})}function Eo(e){var n=e.replace(/\./g,"-");return{resolve:function(e){return n+"-"+e}}}function No(e){var n=Be(e.parent(),"."+iu);p(n,Wn)}function ko(t,e,r){var o=t.origin();p(e,function(e,n){e.each(function(e){var n=r(o,e);Ro(n,iu),Mn(t.parent(),n)})})}function Io(e,n,t,r,o,i){var u=Ur(n);!function(e,n,r,o){ko(e,n,function(e,n){var t=ou(n.row(),r.left()-e.left(),n.y()-e.top(),o,7);return Ro(t,uu),t})}(e,0<t.length?o.positions(t,n):[],u,Mr(n)),function(e,n,r,o){ko(e,n,function(e,n){var t=ru(n.col(),n.x()-e.left(),r.top()-e.top(),7,o);return Ro(t,cu),t})}(e,0<r.length?i.positions(r,n):[],u,Br(n))}function Bo(e,n){var t=Be(e.parent(),"."+iu);p(t,n)}function Po(e,n){return e.cells()[n]}function Mo(e,n){if(0===e.length)return 0;var t=e[0];return C(e,function(e){return!n(t.element(),e.element())}).fold(function(){return e.length},function(e){return e})}function Wo(e,t){return g(e,function(e){var n=function(e){return bo(e,function(e){return ve(e.element()).map(function(e){var n=ve(e).isNone();return Un(e,n)})}).getOrThunk(function(){return Un(t.row(),!0)})}(e.details());return qn(n.element(),e.details(),e.section(),n.isNew())})}function _o(e,n){var t=vu(e,In);return Wo(t,n)}function Lo(e,n){var t=S(g(e.all(),function(e){return e.cells()}));return y(t,function(e){return In(n,e.element())})}function jo(c,a,l,f,s){return function(t,r,e,o,i){var n=ft(r),u=mt.generate(n);return a(u,e).map(function(e){var n=function(e,n){return bu(e,n,!1)}(u,o),t=c(n,e,In,s(o)),r=_o(t.grid(),o);return{grid:D(r),cursor:t.cursor}}).fold(function(){return Me.none()},function(e){var n=Ji(r,e.grid());return l(r,e.grid(),i),f(r),au(t,r,go.height,i),Me.some({cursor:e.cursor,newRows:n.newRows,newCells:n.newCells})})}}function zo(n,e){return lt.cell(e.element()).bind(function(e){return Lo(n,e)})}function Ho(n,e){var t=g(e.selection(),function(e){return lt.cell(e).bind(function(e){return Lo(n,e)})}),r=vo(t);return 0<r.length?Me.some({cells:r,generators:e.generators,clipboard:e.clipboard}):Me.none()}function Fo(n,e){var t=g(e.selection(),function(e){return lt.cell(e).bind(function(e){return Lo(n,e)})}),r=vo(t);return 0<r.length?Me.some(r):Me.none()}function Uo(e,n){return g(e,function(){return Un(n.cell(),!0)})}function qo(n,e,t){return n.concat(function(e,n){for(var t=[],r=0;r<e;r++)t.push(n(r));return t}(e,function(e){return pu.setCells(n[n.length-1],Uo(n[n.length-1].cells(),t))}))}function Vo(e,n,t){return g(e,function(e){return pu.setCells(e,e.cells().concat(Uo(Do(0,n),t)))})}function Go(e,t,r,n){return g(e,function(e){return pu.mapCells(e,function(e){return function(n){return m(t,function(e){return r(n.element(),e.element())})}(e)?Un(n(e.element(),r),!0):e})})}function Yo(e,n,t,r){return pu.getCellElement(e[n],t)!==undefined&&0<n&&r(pu.getCellElement(e[n-1],t),pu.getCellElement(e[n],t))}function Ko(e,n,t){return 0<n&&t(pu.getCellElement(e,n-1),pu.getCellElement(e,n))}function Xo(e,n){return G(e,n)&&1<parseInt(V(e,n),10)}function $o(e,n,t){return ie(e,n).fold(function(){return t(e)+"px"},function(e){return e})}function Jo(e,n){return $o(e,"width",function(e){return uo.getPixelWidth(e,n)})}function Qo(e){return $o(e,"height",uo.getHeight)}function Zo(e,n,t,r,o){var i=eu(e),u=g(i,function(e){return e.map(n.edge)});return g(i,function(e,n){return e.filter(d(Hu.hasColspan)).fold(function(){var e=Ao(u,n);return r(e)},function(e){return t(e,o)})})}function ei(e){return e.map(function(e){return e+"px"}).getOr("")}function ni(e,n,t,r){var o=nu(e),i=g(o,function(e){return e.map(n.edge)});return g(o,function(e,n){return e.filter(d(Hu.hasRowspan)).fold(function(){var e=Ao(i,n);return r(e)},function(e){return t(e)})})}function ti(e,n,t){for(var r=0,o=e;o<n;o++)r+=t[o]!==undefined?t[o]:0;return r}function ri(e){var n=o;return{width:D(e),pixelWidth:D(e),getWidths:Fu.getPixelWidths,getCellDelta:n,singleColumnWidth:function(e,n){return[Math.max(Hu.minWidth(),e+n)-e]},minCellWidth:Hu.minWidth,setElementWidth:uo.setPixelWidth,setTableWidth:function(e,n,t){var r=v(n,function(e,n){return e+n},0);uo.setPixelWidth(e,r)}}}function oi(e,n){var t=uo.percentageBasedSizeRegex().exec(n);if(null!==t)return function(e,n){var o=parseFloat(e),t=Pr(n);return{width:D(o),pixelWidth:D(t),getWidths:Fu.getPercentageWidths,getCellDelta:function(e){return e/t*100},singleColumnWidth:function(e,n){return[100-e]},minCellWidth:function(){return Hu.minWidth()/t*100},setElementWidth:uo.setPercentageWidth,setTableWidth:function(e,n,t){var r=t/100*o;uo.setPercentageWidth(e,o+r)}}}(t[1],e);var r=uo.pixelBasedSizeRegex().exec(n);if(null!==r){var o=parseInt(r[1],10);return ri(o)}var i=Pr(e);return ri(i)}function ii(e){return mt.generate(e)}function ui(e){var n=ft(e);return ii(n)}function ci(n,e){var t=h(e,function(e){return!l(n,e)});0<t.length&&W(t)}function ai(e){return function(e,n){return $u(e,n,{validate:ze,label:"function"})}(ci,e)}function li(e){var n=G(e,"colspan")?parseInt(V(e,"colspan"),10):1,t=G(e,"rowspan")?parseInt(V(e,"rowspan"),10):1;return{element:D(e),colspan:D(n),rowspan:D(t)}}function fi(e,n){var t=e.property().name(n);return l(nc,t)}function si(e,n){return l(["br","img","hr","input"],e.property().name(n))}function di(e){0===lt.cells(e).length&&Wn(e)}function mi(e,n,t){return sc(e,n,t).orThunk(function(){return sc(e,0,0)})}function gi(e,n,t){return fc(e,sc(e,n,t))}function pi(e){return w(e,function(e,n){return m(e,function(e){return e.row()===n.row()})?e:e.concat([n])},[]).sort(function(e,n){return e.row()-n.row()})}function hi(e){return w(e,function(e,n){return m(e,function(e){return e.column()===n.column()})?e:e.concat([n])},[]).sort(function(e,n){return e.column()-n.column()})}function vi(e,n,t){var r=st(e,t),o=mt.generate(r);return bu(o,n,!0)}function bi(e){return e.getBoundingClientRect().width}function wi(e){return e.getBoundingClientRect().height}function yi(e){return/^[0-9]+$/.test(e)&&(e+="px"),e}function Ci(e){var n=Be(e,"td[data-mce-style],th[data-mce-style]");Y(e,"data-mce-style"),p(n,function(e){Y(e,"data-mce-style")})}function Si(e){return e.getParam("table_default_attributes",yc,"object")}function xi(e){return e.getParam("table_default_styles",wc,"object")}function Ri(e){return e.getParam("table_cell_advtab",!0,"boolean")}function Ti(e){return e.getParam("table_row_advtab",!0,"boolean")}function Oi(e){return e.getParam("table_advtab",!0,"boolean")}function Di(e){return e.getParam("table_style_by_css",!1,"boolean")}function Ai(e){return e.getParam("table_class_list",[],"array")}function Ei(e){return!1===e.getParam("table_responsive_width")}function Ni(e,n){return e.fire("newrow",{node:n})}function ki(e,n){return e.fire("newcell",{node:n})}function Ii(e,n,t,r){e.fire("ObjectResizeStart",{target:n,width:t,height:r})}function Bi(e,n,t,r){e.fire("ObjectResized",{target:n,width:t,height:r})}function Pi(n,e){function t(e){return J(e,"rgb")?n.toHex(e):e}return{borderwidth:ie(on.fromDom(e),"border-width").getOr(""),borderstyle:ie(on.fromDom(e),"border-style").getOr(""),bordercolor:ie(on.fromDom(e),"border-color").map(t).getOr(""),backgroundcolor:ie(on.fromDom(e),"background-color").map(t).getOr("")}}function Mi(e,n,t,r,o){var i={};return Dc.each(e.split(" "),function(e){r.formatter.matchNode(o,n+e)&&(i[t]=e)}),i[t]||(i[t]=""),i}function Wi(e,n){e.setAttrib("scope",n.scope),e.setAttrib("class",n["class"]),e.setStyle("width",yi(n.width)),e.setStyle("height",yi(n.height))}function _i(e,n){e.setStyle("background-color",n.backgroundcolor),e.setStyle("border-color",n.bordercolor),e.setStyle("border-style",n.borderstyle),e.setStyle("border-width",yi(n.borderwidth))}function Li(e,n,t){var r=e.dom,o=t.celltype&&n[0].nodeName.toLowerCase()!==t.celltype?r.rename(n[0],t.celltype):n[0],i=qc.normal(r,o);Wi(i,t),Ri(e)&&_i(i,t),Nc(e,o),kc(e,o),t.halign&&Ac(e,o,t.halign),t.valign&&Ec(e,o,t.valign)}function ji(t,e,r){var o=t.dom;Dc.each(e,function(e){r.celltype&&e.nodeName.toLowerCase()!==r.celltype&&(e=o.rename(e,r.celltype));var n=qc.ifTruthy(o,e);Wi(n,r),Ri(t)&&_i(n,r),r.halign&&Ac(t,e,r.halign),r.valign&&Ec(t,e,r.valign)})}function zi(e,n,t){var r=t.getData();t.close(),e.undoManager.transact(function(){(1===n.length?Li:ji)(e,n,r),e.focus()})}function Hi(t,e,r,n){var o=t.dom,i=n.getData();n.close();var u=1===e.length?qc.normal:qc.ifTruthy;t.undoManager.transact(function(){Dc.each(e,function(e){i.type!==e.parentNode.nodeName.toLowerCase()&&function(e,n,t){var r=e.getParent(n,"table"),o=n.parentNode,i=e.select(t,r)[0];i||(i=e.create(t),r.firstChild?"CAPTION"===r.firstChild.nodeName?e.insertAfter(i,r.firstChild):r.insertBefore(i,r.firstChild):r.appendChild(i)),i.appendChild(n),o.hasChildNodes()||e.remove(o)}(t.dom,e,i.type);var n=u(o,e);n.setAttrib("scope",i.scope),n.setAttrib("class",i["class"]),n.setStyle("height",yi(i.height)),Ti(t)&&function(e,n){e.setStyle("background-color",n.backgroundcolor),e.setStyle("border-color",n.bordercolor),e.setStyle("border-style",n.borderstyle)}(n,i),i.align!==r.align&&(Nc(t,e),Ac(t,e,i.align))}),t.focus()})}function Fi(e,n,t,r,o){void 0===o&&(o=Qc);var i=on.fromTag("table");re(i,o.styles),q(i,o.attributes);var u=on.fromTag("tbody");Mn(i,u);for(var c=[],a=0;a<e;a++){for(var l=on.fromTag("tr"),f=0;f<n;f++){var s=a<t||f<r?on.fromTag("th"):on.fromTag("td");f<r&&U(s,"scope","row"),a<t&&U(s,"scope","col"),Mn(s,on.fromTag("br")),o.percentages&&te(s,"width",100/n+"%"),Mn(l,s)}c.push(l)}return Ae(u,c),i}function Ui(e,n){e.selection.select(n.dom(),!0),e.selection.collapse(!0)}function qi(t,r,e){var o,i=t.dom,u=e.getData();e.close(),""===u["class"]&&delete u["class"],t.undoManager.transact(function(){if(!r){var e=parseInt(u.cols,10)||1,n=parseInt(u.rows,10)||1;r=Zc(t,e,n)}!function(e,n,t){var r=e.dom,o={},i={};if(o["class"]=t["class"],i.height=yi(t.height),r.getAttrib(n,"width")&&!Di(e)?o.width=function(e){return e?e.replace(/px$/,""):""}(t.width):i.width=yi(t.width),Di(e)?(i["border-width"]=yi(t.border),i["border-spacing"]=yi(t.cellspacing)):(o.border=t.border,o.cellpadding=t.cellpadding,o.cellspacing=t.cellspacing),Di(e)&&n.children)for(var u=0;u<n.children.length;u++)na(r,n.children[u],{"border-width":yi(t.border),padding:yi(t.cellpadding)}),Oi(e)&&na(r,n.children[u],{"border-color":t.bordercolor});Oi(e)&&(i["background-color"]=t.backgroundcolor,i["border-color"]=t.bordercolor,i["border-style"]=t.borderstyle),o.style=r.serializeStyle($c(xi(e),i)),r.setAttribs(n,$c(Si(e),o))}(t,r,u),(o=i.select("caption",r)[0])&&!u.caption&&i.remove(o),!o&&u.caption&&((o=i.create("caption")).innerHTML=Jc.ie?"\xa0":'<br data-mce-bogus="1"/>',r.insertBefore(o,r.firstChild)),""===u.align?Nc(t,r):Ac(t,r,u.align),t.focus(),t.addVisual()})}function Vi(n){return function(e){return Me.from(e.dom.getParent(e.selection.getStart(),n)).map(on.fromDom)}}function Gi(e){function n(){e.stopPropagation()}function t(){e.preventDefault()}var r=on.fromDom(e.target),o=O(t,n);return function(e,n,t,r,o,i,u){return{target:D(e),x:D(n),y:D(t),stop:r,prevent:o,kill:i,raw:D(u)}}(r,e.clientX,e.clientY,n,t,o,e)}function Yi(e,n,t,r,o){var i=function(n,t){return function(e){n(e)&&t(Gi(e))}}(t,r);return e.dom().addEventListener(n,i,o),{unbind:b(sa,e,n,i,o)}}function Ki(e,n,t){return function(e,n,t,r){return Yi(e,n,t,r,!1)}(e,n,da,t)}var Xi,$i=function(e){var n=ft(e);return mt.generate(n).grid()},Ji=function(o,e){function n(e,n){0<e.length?function(e,n){var t=Qn(o,n).getOrThunk(function(){var e=on.fromTag(n,he(o).dom());return Mn(o,e),e});Ee(t);var r=g(e,function(e){e.isNew()&&i.push(e.element());var n=e.element();return Ee(n),p(e.cells(),function(e){e.isNew()&&u.push(e.element()),wo(e.element(),"colspan",e.colspan(),1),wo(e.element(),"rowspan",e.rowspan(),1),Mn(n,e.element())}),n});Ae(t,r)}(e,n):function(e){Qn(o,e).each(Wn)}(n)}var i=[],u=[],t=[],r=[],c=[];return p(e,function(e){switch(e.section()){case"thead":t.push(e);break;case"tbody":r.push(e);break;case"tfoot":c.push(e)}}),n(t,"thead"),n(r,"tbody"),n(c,"tfoot"),{newRows:D(i),newCells:D(u)}},Qi=function(e){return g(e,function(e){var t=Rt(e.element());return p(e.cells(),function(e){var n=Tt(e.element());wo(n,"colspan",e.colspan(),1),wo(n,"rowspan",e.rowspan(),1),Mn(t,n)}),t})},Zi=function(e,n,t){var r=e();return y(r,n).orThunk(function(){return Me.from(r[0]).orThunk(t)}).map(function(e){return e.element()})},eu=function(t){var e=t.grid(),n=Do(0,e.columns()),r=Do(0,e.rows());return g(n,function(n){return Zi(function(){return x(r,function(e){return mt.getAt(t,e,n).filter(function(e){return e.column()===n}).fold(D([]),function(e){return[e]})})},function(e){return 1===e.colspan()},function(){return mt.getAt(t,0,n)})})},nu=function(t){var e=t.grid(),n=Do(0,e.rows()),r=Do(0,e.columns());return g(n,function(n){return Zi(function(){return x(r,function(e){return mt.getAt(t,n,e).filter(function(e){return e.row()===n}).fold(D([]),function(e){return[e]})})},function(e){return 1===e.rowspan()},function(){return mt.getAt(t,n,0)})})},tu={resolve:Eo("ephox-snooker").resolve},ru=function(e,n,t,r,o){var i=on.fromTag("div");return re(i,{position:"absolute",left:n-r/2+"px",top:t+"px",height:o+"px",width:r+"px"}),q(i,{"data-column":e,role:"presentation"}),i},ou=function(e,n,t,r,o){var i=on.fromTag("div");return re(i,{position:"absolute",left:n+"px",top:t-o/2+"px",height:o+"px",width:r+"px"}),q(i,{"data-row":e,role:"presentation"}),i},iu=tu.resolve("resizer-bar"),uu=tu.resolve("resizer-rows"),cu=tu.resolve("resizer-cols"),au=function(e,n,t,r){No(e);var o=ft(n),i=mt.generate(o),u=nu(i),c=eu(i);Io(e,n,u,c,t,r)},lu=function(e){Bo(e,function(e){te(e,"display","none")})},fu=function(e){Bo(e,function(e){te(e,"display","block")})},su=No,du=function(e){return Oo(e,uu)},mu=function(e){return Oo(e,cu)},gu=function(e,n){return Vn(n,e.section())},pu={addCell:function(e,n,t){var r=e.cells(),o=r.slice(0,n),i=r.slice(n),u=o.concat([t]).concat(i);return gu(e,u)},setCells:gu,mutateCell:function(e,n,t){e.cells()[n]=t},getCell:Po,getCellElement:function(e,n){return Po(e,n).element()},mapCells:function(e,n){var t=e.cells(),r=g(t,n);return Vn(r,e.section())},cellLength:function(e){return e.cells().length}},hu=function(e,n,t,r){var o=function(e,n){return e[n]}(e,n).cells().slice(t),i=Mo(o,r),u=function(e,n){return g(e,function(e){return pu.getCell(e,n)})}(e,t).slice(n),c=Mo(u,r);return{colspan:D(i),rowspan:D(c)}},vu=function(o,i){var u=g(o,function(e,n){return g(e.cells(),function(e,n){return!1})});return g(o,function(e,r){var n=x(e.cells(),function(e,n){if(!1!==u[r][n])return[];var t=hu(o,r,n,i);return function(e,n,t,r){for(var o=e;o<e+t;o++)for(var i=n;i<n+r;i++)u[o][i]=!0}(r,n,t.rowspan(),t.colspan()),[zn(e.element(),t.rowspan(),t.colspan(),e.isNew())]});return Gn(n,e.section())})},bu=function(e,n,t){for(var r=[],o=0;o<e.grid().rows();o++){for(var i=[],u=0;u<e.grid().columns();u++){var c=mt.getAt(e,o,u).map(function(e){return Un(e.element(),t)}).getOrThunk(function(){return Un(n.gap(),!0)});i.push(c)}var a=Vn(i,e.all()[o].section());r.push(a)}return r},wu=function(t){return{is:function(e){return t===e},isValue:i,isError:s,getOr:D(t),getOrThunk:D(t),getOrDie:D(t),or:function(e){return wu(t)},orThunk:function(e){return wu(t)},fold:function(e,n){return n(t)},map:function(e){return wu(e(t))},mapError:function(e){return wu(t)},each:function(e){e(t)},bind:function(e){return e(t)},exists:function(e){return e(t)},forall:function(e){return e(t)},toOption:function(){return Me.some(t)}}},yu=function(t){return{is:s,isValue:s,isError:i,getOr:o,getOrThunk:function(e){return e()},getOrDie:function(){return function(e){return function(){throw new Error(e)}}(String(t))()},or:function(e){return e},orThunk:function(e){return e()},fold:function(e,n){return e(t)},map:function(e){return yu(t)},mapError:function(e){return yu(e(t))},each:T,bind:function(e){return yu(t)},exists:s,forall:i,toOption:Me.none}},Cu={value:wu,error:yu,fromOption:function(e,n){return e.fold(function(){return yu(n)},wu)}},Su=function(e,n,t){if(e.row()>=n.length||e.column()>pu.cellLength(n[0]))return Cu.error("invalid start address out of table bounds, row: "+e.row()+", column: "+e.column());var r=n.slice(e.row()),o=r[0].cells().slice(e.column()),i=pu.cellLength(t[0]),u=t.length;return Cu.value({rowDelta:D(r.length-u),colDelta:D(o.length-i)})},xu=function(e,n){var t=pu.cellLength(e[0]),r=pu.cellLength(n[0]);return{rowDelta:D(0),colDelta:D(t-r)}},Ru=function(e,n,t){var r=n.colDelta()<0?Vo:o;return(n.rowDelta()<0?qo:o)(r(e,Math.abs(n.colDelta()),t),Math.abs(n.rowDelta()),t)},Tu=function(e,n,t,r){if(0===e.length)return e;for(var o=n.startRow();o<=n.finishRow();o++)for(var i=n.startCol();i<=n.finishCol();i++)pu.mutateCell(e[o],i,Un(r(),!1));return e},Ou=function(e,n,t,r){for(var o=!0,i=0;i<e.length;i++)for(var u=0;u<pu.cellLength(e[0]);u++){var c=t(pu.getCellElement(e[i],u),n);!0===c&&!1===o?pu.mutateCell(e[i],u,Un(r(),!0)):!0===c&&(o=!1)}return e},Du=function(i,t,u,c){if(0<t&&t<i.length){var e=function(e,t){return w(e,function(e,n){return m(e,function(e){return t(e.element(),n.element())})?e:e.concat([n])},[])}(i[t-1].cells(),u);p(e,function(r){for(var o=Me.none(),e=function(t){for(var e=function(n){var e=i[t].cells()[n];u(e.element(),r.element())&&(o.isNone()&&(o=Me.some(c())),o.each(function(e){pu.mutateCell(i[t],n,Un(e,!0))}))},n=0;n<pu.cellLength(i[0]);n++)e(n)},n=t;n<i.length;n++)e(n)})}return i},Au=function(t,r,o,i,u){return Su(t,r,o).map(function(e){var n=Ru(r,e,i);return function(e,n,t,r,o){for(var i,u,c,a,l,f=e.row(),s=e.column(),d=f+t.length,m=s+pu.cellLength(t[0]),g=f;g<d;g++)for(var p=s;p<m;p++){i=n,u=g,c=p,l=a=void 0,a=b(o,pu.getCell(i[u],c).element()),l=i[u],1<i.length&&1<pu.cellLength(l)&&(0<c&&a(pu.getCellElement(l,c-1))||c<l.cells().length-1&&a(pu.getCellElement(l,c+1))||0<u&&a(pu.getCellElement(i[u-1],c))||u<i.length-1&&a(pu.getCellElement(i[u+1],c)))&&Ou(n,pu.getCellElement(n[g],p),o,r.cell);var h=pu.getCellElement(t[g-f],p-s),v=r.replace(h);pu.mutateCell(n[g],p,Un(v,!0))}return n}(t,n,o,i,u)})},Eu=function(e,n,t,r,o){Du(n,e,o,r.cell);var i=xu(t,n),u=Ru(t,i,r),c=xu(n,u),a=Ru(n,c,r);return a.slice(0,e).concat(u).concat(a.slice(e,a.length))},Nu=function(t,r,e,o,i){var n=t.slice(0,r),u=t.slice(r),c=pu.mapCells(t[e],function(e,n){return 0<r&&r<t.length&&o(pu.getCellElement(t[r-1],n),pu.getCellElement(t[r],n))?pu.getCell(t[r],n):Un(i(e.element(),o),!0)});return n.concat([c]).concat(u)},ku=function(e,t,r,o,i){return g(e,function(e){var n=0<t&&t<pu.cellLength(e)&&o(pu.getCellElement(e,t-1),pu.getCellElement(e,t))?pu.getCell(e,t):Un(i(pu.getCellElement(e,r),o),!0);return pu.addCell(e,t,n)})},Iu=function(e,r,o,i,u){var c=o+1;return g(e,function(e,n){var t=n===r?Un(u(pu.getCellElement(e,o),i),!0):pu.getCell(e,o);return pu.addCell(e,c,t)})},Bu=function(e,n,t,r,o){var i=n+1,u=e.slice(0,i),c=e.slice(i),a=pu.mapCells(e[n],function(e,n){return n===t?Un(o(e.element(),r),!0):e});return u.concat([a]).concat(c)},Pu=function(e,n,t){return e.slice(0,n).concat(e.slice(t+1))},Mu=function(e,t,r){var n=g(e,function(e){var n=e.cells().slice(0,t).concat(e.cells().slice(r+1));return Vn(n,e.section())});return h(n,function(e){return 0<e.cells().length})},Wu=function(t,r,o,e){var n=x(t,function(e,n){return Yo(t,n,r,o)||Ko(e,r,o)?[]:[pu.getCell(e,r)]});return Go(t,n,o,e)},_u=function(t,r,o,e){var i=t[r],n=x(i.cells(),function(e,n){return Yo(t,r,n,o)||Ko(i,n,o)?[]:[e]});return Go(t,n,o,e)},Lu=Sr([{none:[]},{only:["index"]},{left:["index","next"]},{middle:["prev","index","next"]},{right:["prev","index"]}]),ju=fn({},Lu),zu=function(e,n,i,u){function c(e){return g(e,D(0))}function r(e,n){if(0<=i){var t=Math.max(u.minCellWidth(),a[n]-i);return c(a.slice(0,e)).concat([i,t-a[n]]).concat(c(a.slice(n+1)))}var r=Math.max(u.minCellWidth(),a[e]+i),o=a[e]-r;return c(a.slice(0,e)).concat([r-a[e],o]).concat(c(a.slice(n+1)))}var a=e.slice(0),t=function(e,n){return 0===e.length?ju.none():1===e.length?ju.only(0):0===n?ju.left(0,1):n===e.length-1?ju.right(n-1,n):0<n&&n<e.length-1?ju.middle(n-1,n,n+1):ju.none()}(e,n),o=D(c(a)),l=r;return t.fold(o,function(e){return u.singleColumnWidth(a[e],i)},l,function(e,n,t){return r(n,t)},function(e,n){if(0<=i)return c(a.slice(0,n)).concat([i]);var t=Math.max(u.minCellWidth(),a[n]+i);return c(a.slice(0,n)).concat([t-a[n]])})},Hu={hasColspan:function(e){return Xo(e,"colspan")},hasRowspan:function(e){return Xo(e,"rowspan")},minWidth:D(10),minHeight:D(10),getInt:function(e,n){return parseInt(oe(e,n),10)}},Fu={getRawWidths:function(e,n,t){return Zo(e,n,Jo,ei,t)},getPixelWidths:function(e,n,t){return Zo(e,n,uo.getPixelWidth,function(e){return e.getOrThunk(t.minCellWidth)},t)},getPercentageWidths:function(e,n,t){return Zo(e,n,uo.getPercentageWidth,function(e){return e.fold(function(){return t.minCellWidth()},function(e){return e/t.pixelWidth()*100})},t)},getPixelHeights:function(e,n){return ni(e,n,uo.getHeight,function(e){return e.getOrThunk(Hu.minHeight)})},getRawHeights:function(e,n){return ni(e,n,Qo,ei)}},Uu=function(e,t){var n=mt.justCells(e);return g(n,function(e){var n=ti(e.column(),e.column()+e.colspan(),t);return{element:e.element,width:D(n),colspan:e.colspan}})},qu=function(e,t){var n=mt.justCells(e);return g(n,function(e){var n=ti(e.row(),e.row()+e.rowspan(),t);return{element:e.element,height:D(n),rowspan:e.rowspan}})},Vu=function(e,t){return g(e.all(),function(e,n){return{element:e.element,height:D(t[n])}})},Gu=function(n){return uo.getRawWidth(n).fold(function(){var e=Pr(n);return ri(e)},function(e){return oi(n,e)})},Yu=function(e,n,t,r){var o=Gu(e),i=o.getCellDelta(n),u=ui(e),c=o.getWidths(u,r,o),a=zu(c,t,i,o),l=g(a,function(e,n){return e+c[n]}),f=Uu(u,l);p(f,function(e){o.setElementWidth(e.element(),e.width())}),t===u.grid().columns()-1&&o.setTableWidth(e,l,i)},Ku=function(e,t,r,n){var o=ui(e),i=Fu.getPixelHeights(o,n),u=g(i,function(e,n){return r===n?Math.max(t+e,Hu.minHeight()):e}),c=qu(o,u),a=Vu(o,u);p(a,function(e){uo.setHeight(e.element(),e.height())}),p(c,function(e){uo.setHeight(e.element(),e.height())});var l=function(e){return v(e,function(e,n){return e+n},0)}(u);uo.setHeight(e,l)},Xu=function(e,n,t){var r=Gu(e),o=ii(n),i=r.getWidths(o,t,r),u=Uu(o,i);p(u,function(e){r.setElementWidth(e.element(),e.width())}),0<u.length&&r.setTableWidth(e,i,r.getCellDelta(0))},$u=function(r,o,i){if(0===o.length)throw new Error("You must specify at least one required field.");return _("required",o),L(o),function(n){var t=Ve(n);A(o,function(e){return l(t,e)})||M(o,t),r(o,t);var e=h(o,function(e){return!i.validate(n[e],e)});return 0<e.length&&function(e,n){throw new Error("All values need to be of type: "+n+". Keys ("+P(e).join(", ")+") were not.")}(e,i.label),n}},Ju=ai(["cell","row","replace","gap"]),Qu=function(n,t){void 0===t&&(t=li),Ju(n);function r(e){return function(e){return n.cell(e)}(t(e))}function o(e){var n=r(e);return i.get().isNone()&&i.set(Me.some(n)),u=Me.some({item:e,replacement:n}),n}var i=R(Me.none()),u=Me.none();return{getOrInit:function(n,t){return u.fold(function(){return o(n)},function(e){return t(n,e.item)?e.replacement:o(n)})},cursor:i.get}},Zu=function(c,a){return function(r){var o=R(Me.none());Ju(r);function i(e){var n={scope:c},t=r.replace(e,a,n);return u.push({item:e,sub:t}),o.get().isNone()&&o.set(Me.some(t)),t}var u=[];return{replaceOrInit:function(n,t){return function(n,t){return y(u,function(e){return t(e.item,n)})}(n,t).fold(function(){return i(n)},function(e){return t(n,e.item)?e.sub:i(n)})},cursor:o.get}}},ec=function(t){Ju(t);var e=R(Me.none());return{combine:function(n){return e.get().isNone()&&e.set(Me.some(n)),function(){var e=t.cell({element:D(n),colspan:D(1),rowspan:D(1)});return ue(e,"width"),ue(n,"width"),e}},cursor:e.get}},nc=["body","p","div","article","aside","figcaption","figure","footer","header","nav","section","ol","ul","table","thead","tfoot","tbody","caption","tr","td","th","h1","h2","h3","h4","h5","h6","blockquote","pre","address"],tc=fi,rc=function(e,n){var t=e.property().name(n);return l(["ol","ul"],t)},oc=si,ic=$t(),uc=function(e){return tc(ic,e)},cc=function(e){return rc(ic,e)},ac=function(e){return oc(ic,e)},lc=function(e){function o(e){return"br"===en(e)}function t(r){return St(r).bind(function(n){var t=function(e){return ye(e).map(function(e){return!!uc(e)||!!ac(e)&&"img"!==en(e)}).getOr(!1)}(n);return ve(n).map(function(e){return!0===t||function(e){return"li"===en(e)||$n(e,cc).isSome()}(e)||o(n)||uc(e)&&!In(r,e)?[]:[on.fromTag("br")]})}).getOr([])}var n,r=0===(n=x(e,function(e){var n=Ce(e);return function(e){return A(e,function(e){return o(e)||tn(e)&&0===ht(e).trim().length})}(n)?[]:n.concat(t(e))})).length?[on.fromTag("br")]:n;Ee(e[0]),Ae(e[0],r)},fc=B("grid","cursor"),sc=function(e,n,t){return Me.from(e[n]).bind(function(e){return Me.from(e.cells()[t]).bind(function(e){return Me.from(e.element())})})},dc=Xu,mc={insertRowBefore:jo(function(e,n,t,r){var o=n.row(),i=n.row(),u=Nu(e,i,o,t,r.getOrInit);return gi(u,i,n.column())},zo,T,T,Qu),insertRowsBefore:jo(function(e,n,t,r){var o=n[0].row(),i=n[0].row(),u=pi(n),c=w(u,function(e,n){return Nu(e,i,o,t,r.getOrInit)},e);return gi(c,i,n[0].column())},Fo,T,T,Qu),insertRowAfter:jo(function(e,n,t,r){var o=n.row(),i=n.row()+n.rowspan(),u=Nu(e,i,o,t,r.getOrInit);return gi(u,i,n.column())},zo,T,T,Qu),insertRowsAfter:jo(function(e,n,t,r){var o=pi(n),i=o[o.length-1].row(),u=o[o.length-1].row()+o[o.length-1].rowspan(),c=w(o,function(e,n){return Nu(e,u,i,t,r.getOrInit)},e);return gi(c,u,n[0].column())},Fo,T,T,Qu),insertColumnBefore:jo(function(e,n,t,r){var o=n.column(),i=n.column(),u=ku(e,i,o,t,r.getOrInit);return gi(u,n.row(),i)},zo,dc,T,Qu),insertColumnsBefore:jo(function(e,n,t,r){var o=hi(n),i=o[0].column(),u=o[0].column(),c=w(o,function(e,n){return ku(e,u,i,t,r.getOrInit)},e);return gi(c,n[0].row(),u)},Fo,dc,T,Qu),insertColumnAfter:jo(function(e,n,t,r){var o=n.column(),i=n.column()+n.colspan(),u=ku(e,i,o,t,r.getOrInit);return gi(u,n.row(),i)},zo,dc,T,Qu),insertColumnsAfter:jo(function(e,n,t,r){var o=n[n.length-1].column(),i=n[n.length-1].column()+n[n.length-1].colspan(),u=hi(n),c=w(u,function(e,n){return ku(e,i,o,t,r.getOrInit)},e);return gi(c,n[0].row(),i)},Fo,dc,T,Qu),splitCellIntoColumns:jo(function(e,n,t,r){var o=Iu(e,n.row(),n.column(),t,r.getOrInit);return gi(o,n.row(),n.column())},zo,dc,T,Qu),splitCellIntoRows:jo(function(e,n,t,r){var o=Bu(e,n.row(),n.column(),t,r.getOrInit);return gi(o,n.row(),n.column())},zo,T,T,Qu),eraseColumns:jo(function(e,n,t,r){var o=hi(n),i=Mu(e,o[0].column(),o[o.length-1].column()),u=mi(i,n[0].row(),n[0].column());return fc(i,u)},Fo,dc,di,Qu),eraseRows:jo(function(e,n,t,r){var o=pi(n),i=Pu(e,o[0].row(),o[o.length-1].row()),u=mi(i,n[0].row(),n[0].column());return fc(i,u)},Fo,T,di,Qu),makeColumnHeader:jo(function(e,n,t,r){var o=Wu(e,n.column(),t,r.replaceOrInit);return gi(o,n.row(),n.column())},zo,T,T,Zu("row","th")),unmakeColumnHeader:jo(function(e,n,t,r){var o=Wu(e,n.column(),t,r.replaceOrInit);return gi(o,n.row(),n.column())},zo,T,T,Zu(null,"td")),makeRowHeader:jo(function(e,n,t,r){var o=_u(e,n.row(),t,r.replaceOrInit);return gi(o,n.row(),n.column())},zo,T,T,Zu("col","th")),unmakeRowHeader:jo(function(e,n,t,r){var o=_u(e,n.row(),t,r.replaceOrInit);return gi(o,n.row(),n.column())},zo,T,T,Zu(null,"td")),mergeCells:jo(function(e,n,t,r){var o=n.cells();lc(o);var i=Tu(e,n.bounds(),t,D(o[0]));return fc(i,Me.from(o[0]))},function(e,n){return n.mergable()},T,T,ec),unmergeCells:jo(function(e,n,t,r){var o=v(n,function(e,n){return Ou(e,n,t,r.combine(n))},e);return fc(o,Me.from(n[0]))},function(e,n){return n.unmergable()},dc,T,ec),pasteCells:jo(function(e,t,n,r){var o,i,u,c,a=(o=t.clipboard(),i=t.generators(),u=ft(o),c=mt.generate(u),bu(c,i,!0)),l=Ln(t.row(),t.column());return Au(l,e,a,t.generators(),n).fold(function(){return fc(e,Me.some(t.element()))},function(e){var n=mi(e,t.row(),t.column());return fc(e,n)})},function(n,t){return lt.cell(t.element()).bind(function(e){return Lo(n,e).map(function(e){return fn(fn({},e),{generators:t.generators,clipboard:t.clipboard})})})},dc,T,Qu),pasteRowsBefore:jo(function(e,n,t,r){var o=e[n.cells[0].row()],i=n.cells[0].row(),u=vi(n.clipboard(),n.generators(),o),c=Eu(i,e,u,n.generators(),t),a=mi(c,n.cells[0].row(),n.cells[0].column());return fc(c,a)},Ho,T,T,Qu),pasteRowsAfter:jo(function(e,n,t,r){var o=e[n.cells[0].row()],i=n.cells[n.cells.length-1].row()+n.cells[n.cells.length-1].rowspan(),u=vi(n.clipboard(),n.generators(),o),c=Eu(i,e,u,n.generators(),t),a=mi(c,n.cells[0].row(),n.cells[0].column());return fc(c,a)},Ho,T,T,Qu)},gc=function(e){return on.fromDom(e.getBody())},pc=function(n){return function(e){return In(e,gc(n))}},hc={isRtl:D(!1)},vc={isRtl:D(!0)},bc={directionAt:function(e){return"rtl"===function(e){return"rtl"===oe(e,"direction")?"rtl":"ltr"}(e)?vc:hc}},wc={"border-collapse":"collapse",width:"100%"},yc={border:"1"},Cc=function(e){return e.getParam("table_tab_navigation",!0,"boolean")},Sc=function(e){var n=e.getParam("table_clone_elements");return _e(n)?Me.some(n.split(/[ ,]/)):Array.isArray(n)?Me.some(n):Me.none()},xc=function(e,n,t,r,o){e.fire("TableSelectionChange",{cells:n,start:t,finish:r,otherCells:o})},Rc=function(e){e.fire("TableSelectionClear")},Tc=function(f,e){function t(e){return"table"===en(gc(e))}function n(u,c,a,l){return function(e,n){Ci(e);var t=l(),r=on.fromDom(f.getDoc()),o=ho(bc.directionAt),i=Wt.cellOperations(a,r,s);return c(e)?u(t,e,n,i,o).bind(function(e){return p(e.newRows(),function(e){Ni(f,e.dom())}),p(e.newCells(),function(e){ki(f,e.dom())}),e.cursor().map(function(e){var n=f.dom.createRng();return n.setStart(e.dom(),0),n.setEnd(e.dom(),0),n})}):Me.none()}}var s=Sc(f);return{deleteRow:n(mc.eraseRows,function(e){var n=$i(e);return!1===t(f)||1<n.rows()},T,e),deleteColumn:n(mc.eraseColumns,function(e){var n=$i(e);return!1===t(f)||1<n.columns()},T,e),insertRowsBefore:n(mc.insertRowsBefore,i,T,e),insertRowsAfter:n(mc.insertRowsAfter,i,T,e),insertColumnsBefore:n(mc.insertColumnsBefore,i,co,e),insertColumnsAfter:n(mc.insertColumnsAfter,i,co,e),mergeCells:n(mc.mergeCells,i,T,e),unmergeCells:n(mc.unmergeCells,i,T,e),pasteRowsBefore:n(mc.pasteRowsBefore,i,T,e),pasteRowsAfter:n(mc.pasteRowsAfter,i,T,e),pasteCells:n(mc.pasteCells,i,T,e)}},Oc=function(e,n,r){var t=ft(e),o=mt.generate(t);return Fo(o,n).map(function(e){var n=bu(o,r,!1).slice(e[0].row(),e[e.length-1].row()+e[e.length-1].rowspan()),t=_o(n,r);return Qi(t)})},Dc=tinymce.util.Tools.resolve("tinymce.util.Tools"),Ac=function(e,n,t){t&&e.formatter.apply("align"+t,{},n)},Ec=function(e,n,t){t&&e.formatter.apply("valign"+t,{},n)},Nc=function(n,t){Dc.each("left center right".split(" "),function(e){n.formatter.remove("align"+e,{},t)})},kc=function(n,t){Dc.each("top middle bottom".split(" "),function(e){n.formatter.remove("valign"+e,{},t)})},Ic=function(o,e,i){var n;return n=function(e,n){for(var t=0;t<n.length;t++){var r=o.getStyle(n[t],i);if(void 0===e&&(e=r),e!==r)return""}return e}(n,o.select("td,th",e))},Bc=b(Mi,"left center right"),Pc=b(Mi,"top middle bottom"),Mc=function(e,r,n){var o=function(e,t){return t=t||[],Dc.each(e,function(e){var n={text:e.text||e.title};e.menu?n.menu=o(e.menu):(n.value=e.value,r&&r(n)),t.push(n)}),t};return o(e,n||[])},Wc=function(e){var o=e[0],n=e.slice(1),t=Ve(o);return p(n,function(e){p(t,function(r){N(e,function(e,n){var t=o[r];""!==t&&r===n&&t!==e&&(o[r]="")})})}),o},_c=function(e){var n=[{name:"borderstyle",type:"selectbox",label:"Border style",items:[{text:"Select...",value:""},{text:"Solid",value:"solid"},{text:"Dotted",value:"dotted"},{text:"Dashed",value:"dashed"},{text:"Double",value:"double"},{text:"Groove",value:"groove"},{text:"Ridge",value:"ridge"},{text:"Inset",value:"inset"},{text:"Outset",value:"outset"},{text:"None",value:"none"},{text:"Hidden",value:"hidden"}]},{name:"bordercolor",type:"colorinput",label:"Border color"},{name:"backgroundcolor",type:"colorinput",label:"Background color"}];return{title:"Advanced",name:"advanced",items:"cell"===e?[{name:"borderwidth",type:"input",label:"Border width"}].concat(n):n}},Lc=function(e,n,t){var r,o,i,u=e.dom;return fn(fn({width:u.getStyle(n,"width")||u.getAttrib(n,"width"),height:u.getStyle(n,"height")||u.getAttrib(n,"height"),cellspacing:u.getStyle(n,"border-spacing")||u.getAttrib(n,"cellspacing"),cellpadding:u.getAttrib(n,"cellpadding")||Ic(e.dom,n,"padding"),border:(r=u,o=n,i=ie(on.fromDom(o),"border-width"),Di(e)&&i.isSome()?i.getOr(""):r.getAttrib(o,"border")||Ic(e.dom,o,"border-width")||Ic(e.dom,o,"border")),caption:!!u.select("caption",n)[0],"class":u.getAttrib(n,"class","")},Bc("align","align",e,n)),t?Pi(u,n):{})},jc=function(e,n,t){var r=e.dom;return fn(fn({height:r.getStyle(n,"height")||r.getAttrib(n,"height"),scope:r.getAttrib(n,"scope"),"class":r.getAttrib(n,"class",""),align:"",type:n.parentNode.nodeName.toLowerCase()},Bc("align","align",e,n)),t?Pi(r,n):{})},zc=function(e,n,t){var r=e.dom;return fn(fn(fn({width:r.getStyle(n,"width")||r.getAttrib(n,"width"),height:r.getStyle(n,"height")||r.getAttrib(n,"height"),scope:r.getAttrib(n,"scope"),celltype:n.nodeName.toLowerCase(),"class":r.getAttrib(n,"class","")},Bc("align","halign",e,n)),Pc("valign","valign",e,n)),t?Pi(r,n):{})},Hc=function(e,n){var t,r,o,i,u=xi(e),c=Si(e),a=e.dom,l=n?(t=a,r=I(u,"border-style").getOr(""),o=I(u,"border-color").getOr(""),i=I(u,"background-color").getOr(""),{borderstyle:r,bordercolor:f(o),backgroundcolor:f(i)}):{};function f(e){return J(e,"rgb")?t.toHex(e):e}var s,d,m;return fn(fn(fn(fn(fn(fn({},{height:"",width:"100%",cellspacing:"",cellpadding:"",caption:!1,"class":"",align:"",border:""}),u),c),l),(m=u["border-width"],Di(e)&&m?{border:m}:I(c,"border").fold(function(){return{}},function(e){return{border:e}}))),(s=I(u,"border-spacing").or(I(c,"cellspacing")).fold(function(){return{}},function(e){return{cellspacing:e}}),d=I(u,"border-padding").or(I(c,"cellpadding")).fold(function(){return{}},function(e){return{cellpadding:e}}),fn(fn({},s),d)))},Fc=[{name:"width",type:"input",label:"Width"},{name:"height",type:"input",label:"Height"},{name:"celltype",type:"selectbox",label:"Cell type",items:[{text:"Cell",value:"td"},{text:"Header cell",value:"th"}]},{name:"scope",type:"selectbox",label:"Scope",items:[{text:"None",value:""},{text:"Row",value:"row"},{text:"Column",value:"col"},{text:"Row group",value:"rowgroup"},{text:"Column group",value:"colgroup"}]},{name:"halign",type:"selectbox",label:"H Align",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{name:"valign",type:"selectbox",label:"V Align",items:[{text:"None",value:""},{text:"Top",value:"top"},{text:"Middle",value:"middle"},{text:"Bottom",value:"bottom"}]}],Uc=function(e){return function(n){var e=function(e){return e.getParam("table_cell_class_list",[],"array")}(n),t=Mc(e,function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"tr",classes:[e.value]})})});return 0<e.length?Me.some({name:"class",type:"selectbox",label:"Class",items:t}):Me.none()}(e).fold(function(){return Fc},function(e){return Fc.concat(e)})},qc={normal:function(t,r){return{setAttrib:function(e,n){t.setAttrib(r,e,n)},setStyle:function(e,n){t.setStyle(r,e,n)}}},ifTruthy:function(t,r){return{setAttrib:function(e,n){n&&t.setAttrib(r,e,n)},setStyle:function(e,n){n&&t.setStyle(r,e,n)}}}},Vc=function(n){var e,t=[];if(t=n.dom.select("td[data-mce-selected],th[data-mce-selected]"),e=n.dom.getParent(n.selection.getStart(),"td,th"),!t.length&&e&&t.push(e),e=e||t[0]){var r=Dc.map(t,function(e){return zc(n,e,Ri(n))}),o=Wc(r),i={type:"tabpanel",tabs:[{title:"General",name:"general",items:Uc(n)},_c("cell")]},u={type:"panel",items:[{type:"grid",columns:2,items:Uc(n)}]};n.windowManager.open({title:"Cell Properties",size:"normal",body:Ri(n)?i:u,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onSubmit:b(zi,n,t)})}},Gc=[{type:"selectbox",name:"type",label:"Row type",items:[{text:"Header",value:"thead"},{text:"Body",value:"tbody"},{text:"Footer",value:"tfoot"}]},{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]},{label:"Height",name:"height",type:"input"}],Yc=function(e){return function(n){var e=function(e){return e.getParam("table_row_class_list",[],"array")}(n),t=Mc(e,function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"tr",classes:[e.value]})})});return 0<e.length?Me.some({name:"class",type:"selectbox",label:"Class",items:t}):Me.none()}(e).fold(function(){return Gc},function(e){return Gc.concat(e)})},Kc=function(n){var e,t,r=n.dom,o=[];if((e=r.getParent(n.selection.getStart(),"table"))&&(t=r.getParent(n.selection.getStart(),"td,th"),Dc.each(e.rows,function(n){Dc.each(n.cells,function(e){if((r.getAttrib(e,"data-mce-selected")||e===t)&&o.indexOf(n)<0)return o.push(n),!1})}),o[0])){var i=Dc.map(o,function(e){return jc(n,e,Ti(n))}),u=Wc(i),c={type:"tabpanel",tabs:[{title:"General",name:"general",items:Yc(n)},_c("row")]},a={type:"panel",items:[{type:"grid",columns:2,items:Yc(n)}]};n.windowManager.open({title:"Row Properties",size:"normal",body:Ti(n)?c:a,buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:u,onSubmit:b(Hi,n,o,u)})}},Xc=Object.prototype.hasOwnProperty,$c=(Xi=function(e,n){return n},function(){for(var e=new Array(arguments.length),n=0;n<e.length;n++)e[n]=arguments[n];if(0===e.length)throw new Error("Can't merge zero objects");for(var t={},r=0;r<e.length;r++){var o=e[r];for(var i in o)Xc.call(o,i)&&(t[i]=Xi(t[i],o[i]))}return t}),Jc=tinymce.util.Tools.resolve("tinymce.Env"),Qc={styles:{"border-collapse":"collapse",width:"100%"},attributes:{border:"1"},percentages:!0},Zc=function(n,e,t){var r=xi(n),o={styles:r,attributes:Si(n),percentages:function(e){return _e(e)&&-1!==e.indexOf("%")}(r.width)&&!Ei(n)},i=Fi(t,e,0,0,o);U(i,"data-mce-id","__mce");var u=function(e){var n=on.fromTag("div"),t=on.fromDom(e.dom().cloneNode(!0));return Mn(n,t),function(e){return e.dom().innerHTML}(n)}(i);return n.insertContent(u),Zn(gc(n),'table[data-mce-id="__mce"]').map(function(e){return Ei(n)&&te(e,"width",oe(e,"width")),Y(e,"data-mce-id"),function(n,e){p(Be(e,"tr"),function(e){Ni(n,e.dom()),p(Be(e,"th,td"),function(e){ki(n,e.dom())})})}(n,e),function(e,n){Zn(n,"td,th").each(b(Ui,e))}(n,e),e.dom()}).getOr(null)},ea=function(n,e,t){var r=t?[{type:"input",name:"cols",label:"Cols",inputMode:"numeric"},{type:"input",name:"rows",label:"Rows",inputMode:"numeric"}]:[],o=function(e){return e.getParam("table_appearance_options",!0,"boolean")}(n)?[{type:"input",name:"cellspacing",label:"Cell spacing",inputMode:"numeric"},{type:"input",name:"cellpadding",label:"Cell padding",inputMode:"numeric"},{type:"input",name:"border",label:"Border width"},{type:"label",label:"Caption",items:[{type:"checkbox",name:"caption",label:"Show caption"}]}]:[],i=e?[{type:"selectbox",name:"class",label:"Class",items:Mc(Ai(n),function(e){e.value&&(e.textStyle=function(){return n.formatter.getCssText({block:"table",classes:[e.value]})})})}]:[];return r.concat([{type:"input",name:"width",label:"Width"},{type:"input",name:"height",label:"Height"}]).concat(o).concat([{type:"selectbox",name:"align",label:"Alignment",items:[{text:"None",value:""},{text:"Left",value:"left"},{text:"Center",value:"center"},{text:"Right",value:"right"}]}]).concat(i)},na=function(e,n,t,r){if("TD"===n.tagName||"TH"===n.tagName)_e(t)?e.setStyle(n,t,r):e.setStyle(n,t);else if(n.children)for(var o=0;o<n.children.length;o++)na(e,n.children[o],t,r)},ta=function(e,n){var t,r=e.dom,o=Hc(e,Oi(e));!1===n?(t=r.getParent(e.selection.getStart(),"table"))?o=Lc(e,t,Oi(e)):Oi(e)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor=""):(o.cols="1",o.rows="1",Oi(e)&&(o.borderstyle="",o.bordercolor="",o.backgroundcolor=""));var i=0<Ai(e).length;i&&o["class"]&&(o["class"]=o["class"].replace(/\s*mce\-item\-table\s*/g,""));var u={type:"grid",columns:2,items:ea(e,i,n)},c=Oi(e)?{type:"tabpanel",tabs:[{title:"General",name:"general",items:[u]},_c("table")]}:{type:"panel",items:[u]};e.windowManager.open({title:"Table Properties",size:"normal",body:c,onSubmit:b(qi,e,t),buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o})},ra=Vi("th,td"),oa=Vi("th,td,caption"),ia=Dc.each,ua={registerCommands:function(c,n,a,l,t){function f(e){return lt.table(e,s)}function i(e){return{width:bi(e.dom()),height:bi(e.dom())}}function r(o){ra(c).each(function(r){f(r).each(function(n){var e=Er.forMenu(l,n,r),t=i(n);o(n,e).each(function(e){!function(e,n,t){var r=i(t);n.width===r.width&&n.height===r.height||(Ii(e,t.dom(),n.width,n.height),Bi(e,t.dom(),r.width,r.height))}(c,t,n),c.selection.setRng(e),c.focus(),a.clear(n),Ci(n)})})})}function o(e){return ra(c).map(function(o){return f(o).bind(function(e){var n=on.fromDom(c.getDoc()),t=Er.forMenu(l,e,o),r=Wt.cellOperations(T,n,Me.none());return Oc(e,t,r)})})}function u(u){t.get().each(function(e){var i=g(e,function(e){return Tt(e)});ra(c).each(function(o){f(o).each(function(n){var e=on.fromDom(c.getDoc()),t=Wt.paste(e),r=Er.pasteRows(l,n,o,i,t);u(n,r).each(function(e){c.selection.setRng(e),c.focus(),a.clear(n)})})})})}var s=pc(c);ia({mceTableSplitCells:function(){r(n.unmergeCells)},mceTableMergeCells:function(){r(n.mergeCells)},mceTableInsertRowBefore:function(){r(n.insertRowsBefore)},mceTableInsertRowAfter:function(){r(n.insertRowsAfter)},mceTableInsertColBefore:function(){r(n.insertColumnsBefore)},mceTableInsertColAfter:function(){r(n.insertColumnsAfter)},mceTableDeleteCol:function(){r(n.deleteColumn)},mceTableDeleteRow:function(){r(n.deleteRow)},mceTableCutRow:function(e){o().each(function(e){t.set(e),r(n.deleteRow)})},mceTableCopyRow:function(e){o().each(function(e){t.set(e)})},mceTablePasteRowBefore:function(e){u(n.pasteRowsBefore)},mceTablePasteRowAfter:function(e){u(n.pasteRowsAfter)},mceTableDelete:function(){oa(c).each(function(e){lt.table(e,s).filter(d(s)).each(function(e){var n=on.fromText("");if(Re(e,n),Wn(e),c.dom.isEmpty(c.getBody()))c.setContent(""),c.selection.setCursorLocation();else{var t=c.dom.createRng();t.setStart(n.dom(),0),t.setEnd(n.dom(),0),c.selection.setRng(t),c.nodeChanged()}})})}},function(e,n){c.addCommand(n,e)}),ia({mceInsertTable:b(ta,c,!0),mceTableProps:b(ta,c,!1),mceTableRowProps:b(Kc,c),mceTableCellProps:b(Vc,c)},function(e,n){c.addCommand(n,function(){e()})})}},ca=function(e){var n=Me.from(e.dom().documentElement).map(on.fromDom).getOr(e);return{parent:D(n),view:D(e),origin:D(lo(0,0))}},aa=function(e,n){return{parent:D(n),view:D(e),origin:D(lo(0,0))}},la=function(e){var r=B.apply(null,e),o=[];return{bind:function(e){if(e===undefined)throw new Error("Event bind error: undefined handler");o.push(e)},unbind:function(n){o=h(o,function(e){return e!==n})},trigger:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];var t=r.apply(null,e);p(o,function(e){e(t)})}}},fa={create:function(e){return{registry:k(e,function(e){return{bind:e.bind,unbind:e.unbind}}),trigger:k(e,function(e){return e.trigger})}}},sa=function(e,n,t,r){e.dom().removeEventListener(n,t,r)},da=D(!0),ma={resolve:Eo("ephox-dragster").resolve},ga=ai(["compare","extract","mutate","sink"]),pa=ai(["element","start","stop","destroy"]),ha=ai(["forceDrop","drop","move","delayDrop"]),va=ga({compare:function(e,n){return lo(n.left()-e.left(),n.top()-e.top())},extract:function(e){return Me.some(lo(e.x(),e.y()))},sink:function(e,n){var t=function(e){var n=$c({layerClass:ma.resolve("blocker")},e),t=on.fromTag("div");U(t,"role","presentation"),re(t,{position:"fixed",left:"0px",top:"0px",width:"100%",height:"100%"}),Ro(t,ma.resolve("blocker")),Ro(t,n.layerClass);return{element:function(){return t},destroy:function(){Wn(t)}}}(n),r=Ki(t.element(),"mousedown",e.forceDrop),o=Ki(t.element(),"mouseup",e.drop),i=Ki(t.element(),"mousemove",e.move),u=Ki(t.element(),"mouseout",e.delayDrop);return pa({element:t.element,start:function(e){Mn(e,t.element())},stop:function(){Wn(t.element())},destroy:function(){t.destroy(),o.unbind(),i.unbind(),u.unbind(),r.unbind()}})},mutate:function(e,n){e.mutate(n.left(),n.top())}});function ba(){var r=Me.none(),t=fa.create({move:la(["info"])});return{onEvent:function(e,n){n.extract(e).each(function(e){(function(n,t){var e=r.map(function(e){return n.compare(e,t)});return r=Me.some(t),e})(n,e).each(function(e){t.trigger.move(e)})})},reset:function(){r=Me.none()},events:t.registry}}function wa(){var e=function r(){return{onEvent:T,reset:T}}(),n=ba(),t=e;return{on:function(){t.reset(),t=n},off:function(){t.reset(),t=e},isOn:function(){return t===n},onEvent:function(e,n){t.onEvent(e,n)},events:n.events}}function ya(){var t=fa.create({drag:la(["xDelta","yDelta","target"])}),r=Me.none(),e=function(){var t=fa.create({drag:la(["xDelta","yDelta"])});return{mutate:function(e,n){t.trigger.drag(e,n)},events:t.registry}}();return e.events.drag.bind(function(n){r.each(function(e){t.trigger.drag(n.xDelta(),n.yDelta(),e)})}),{assign:function(e){r=Me.some(e)},get:function(){return r},mutate:e.mutate,events:t.registry}}function Ca(e){return"true"===V(e,"contenteditable")}function Sa(o,n,i){function e(e,n){return Me.from(V(e,n))}var t=ya(),r=vl(t,{}),u=Me.none();function c(e,n){return Hu.getInt(e,n)-parseInt(V(e,"data-initial-"+n),10)}function a(e,n){m.trigger.startAdjust(),t.assign(e),U(e,"data-initial-"+n,parseInt(oe(e,n),10)),Ro(e,bl),te(e,"opacity","0.2"),r.go(o.parent())}function l(e){return In(e,o.view())}function f(e){return et(e,"table",l).filter(function(e){return function(e,n){return et(e,"[contenteditable]",n)}(e,l).exists(Ca)})}t.events.drag.bind(function(t){e(t.target(),"data-row").each(function(e){var n=Hu.getInt(t.target(),"top");te(t.target(),"top",n+t.yDelta()+"px")}),e(t.target(),"data-column").each(function(e){var n=Hu.getInt(t.target(),"left");te(t.target(),"left",n+t.xDelta()+"px")})}),r.events.stop.bind(function(){t.get().each(function(r){u.each(function(t){e(r,"data-row").each(function(e){var n=c(r,"top");Y(r,"data-initial-top"),m.trigger.adjustHeight(t,n,parseInt(e,10))}),e(r,"data-column").each(function(e){var n=c(r,"left");Y(r,"data-initial-left"),m.trigger.adjustWidth(t,n,parseInt(e,10))}),au(o,t,i,n)})})});var s=Ki(o.parent(),"mousedown",function(e){du(e.target())&&a(e.target(),"top"),mu(e.target())&&a(e.target(),"left")}),d=Ki(o.view(),"mouseover",function(e){f(e.target()).fold(function(){ee(e.target())&&su(o)},function(e){u=Me.some(e),au(o,e,i,n)})}),m=fa.create({adjustHeight:la(["table","delta","row"]),adjustWidth:la(["table","delta","column"]),startAdjust:la([])});return{destroy:function(){s.unbind(),d.unbind(),r.destroy(),su(o)},refresh:function(e){au(o,e,i,n)},on:r.on,off:r.off,hideBars:b(lu,o),showBars:b(fu,o),events:m.registry}}function xa(e,n){return bi(e.dom())/bi(n.dom())*100+"%"}function Ra(t,e){return lt.table(t,e).bind(function(e){var n=lt.cells(e);return C(n,function(e){return In(t,e)}).map(function(e){return{index:D(e),all:D(n)}})})}function Ta(e,n,t){var r=e.document.createRange();return function(t,e){e.fold(function(e){t.setStartBefore(e.dom())},function(e,n){t.setStart(e.dom(),n)},function(e){t.setStartAfter(e.dom())})}(r,n),function(t,e){e.fold(function(e){t.setEndBefore(e.dom())},function(e,n){t.setEnd(e.dom(),n)},function(e){t.setEndAfter(e.dom())})}(r,t),r}function Oa(e,n,t,r,o){var i=e.document.createRange();return i.setStart(n.dom(),t),i.setEnd(r.dom(),o),i}function Da(e){return{left:D(e.left),top:D(e.top),right:D(e.right),bottom:D(e.bottom),width:D(e.width),height:D(e.height)}}function Aa(e,n,t){return n(on.fromDom(t.startContainer),t.startOffset,on.fromDom(t.endContainer),t.endOffset)}function Ea(e,n){return function(e,n){var t=n.ltr();return t.collapsed?n.rtl().filter(function(e){return!1===e.collapsed}).map(function(e){return Bl.rtl(on.fromDom(e.endContainer),e.endOffset,on.fromDom(e.startContainer),e.startOffset)}).getOrThunk(function(){return Aa(0,Bl.ltr,t)}):Aa(0,Bl.ltr,t)}(0,function(o,e){return e.match({domRange:function(e){return{ltr:D(e),rtl:Me.none}},relative:function(e,n){return{ltr:Z(function(){return Ta(o,e,n)}),rtl:Z(function(){return Me.some(Ta(o,n,e))})}},exact:function(e,n,t,r){return{ltr:Z(function(){return Oa(o,e,n,t,r)}),rtl:Z(function(){return Me.some(Oa(o,t,r,e,n))})}}})}(e,n))}function Na(e,n,t){return n>=e.left&&n<=e.right&&t>=e.top&&t<=e.bottom}function ka(t,r,e,n,o){function i(e){var n=t.dom().createRange();return n.setStart(r.dom(),e),n.collapse(!0),n}var u=ht(r).length,c=function(e,n,t,r,o){if(0===o)return 0;if(n===r)return o-1;for(var i=r,u=1;u<o;u++){var c=e(u),a=Math.abs(n-c.left);if(t<=c.bottom){if(t<c.top||i<a)return u-1;i=a}}return 0}(function(e){return i(e).getBoundingClientRect()},e,n,o.right,u);return i(c)}function Ia(e,n){return n-e.left<e.right-n}function Ba(e,n,t){var r=e.dom().createRange();return r.selectNode(n.dom()),r.collapse(t),r}function Pa(n,e,t){var r=n.dom().createRange();r.selectNode(e.dom());var o=r.getBoundingClientRect(),i=Ia(o,t);return(!0===i?Ct:St)(e).map(function(e){return Ba(n,e,i)})}function Ma(e,n,t){var r=n.dom().getBoundingClientRect(),o=Ia(r,t);return Me.some(Ba(e,n,o))}function Wa(e,n,t,r){var o=e.dom().createRange();o.selectNode(n.dom());var i=o.getBoundingClientRect();return function(e,n,t,r){var o=e.dom().createRange();o.selectNode(n.dom());var i=o.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,t)),c=Math.max(i.top,Math.min(i.bottom,r));return Ml(e,n,u,c)}(e,n,Math.max(i.left,Math.min(i.right,t)),Math.max(i.top,Math.min(i.bottom,r)))}function _a(e,n){var t=en(e);return"input"===t?El.after(e):l(["br","img"],t)?0===n?El.before(e):El.after(e):El.on(e,n)}function La(e,n){var t=e.fold(El.before,_a,El.after),r=n.fold(El.before,_a,El.after);return kl.relative(t,r)}function ja(e,n,t,r){var o=_a(e,n),i=_a(t,r);return kl.relative(o,i)}function za(e,n,t,r){var o=function(e,n,t,r){var o=he(e).dom().createRange();return o.setStart(e.dom(),n),o.setEnd(t.dom(),r),o}(e,n,t,r),i=In(e,t)&&n===r;return o.collapsed&&!i}function Ha(e,n){Me.from(e.getSelection()).each(function(e){e.removeAllRanges(),e.addRange(n)})}function Fa(e,n,t,r,o){var i=Oa(e,n,t,r,o);Ha(e,i)}function Ua(u,e){return Ea(u,e).match({ltr:function(e,n,t,r){Fa(u,e,n,t,r)},rtl:function(e,n,t,r){var o=u.getSelection();if(o.setBaseAndExtent)o.setBaseAndExtent(e.dom(),n,t.dom(),r);else if(o.extend)try{!function(e,n,t,r,o,i){n.collapse(t.dom(),r),n.extend(o.dom(),i)}(0,o,e,n,t,r)}catch(i){Fa(u,t,r,e,n)}else Fa(u,t,r,e,n)}})}function qa(e,n,t,r,o){var i=ja(n,t,r,o);Ua(e,i)}function Va(e,n,t){var r=La(n,t);Ua(e,r)}function Ga(e){function n(e,n,t,r){return Oa(o,e,n,t,r)}var o=kl.getWin(e).dom(),t=function(e){return e.match({domRange:function(e){var n=on.fromDom(e.startContainer),t=on.fromDom(e.endContainer);return ja(n,e.startOffset,t,e.endOffset)},relative:La,exact:ja})}(e);return Ea(o,t).match({ltr:n,rtl:n})}function Ya(e){var n=on.fromDom(e.anchorNode),t=on.fromDom(e.focusNode);return za(n,e.anchorOffset,t,e.focusOffset)?Me.some(Dl.create(n,e.anchorOffset,t,e.focusOffset)):function(e){if(0<e.rangeCount){var n=e.getRangeAt(0),t=e.getRangeAt(e.rangeCount-1);return Me.some(Dl.create(on.fromDom(n.startContainer),n.startOffset,on.fromDom(t.endContainer),t.endOffset))}return Me.none()}(e)}function Ka(e,n){var t=function(e,n){var t=e.document.createRange();return Il(t,n),t}(e,n);Ha(e,t)}function Xa(e){return function(e){return Me.from(e.getSelection()).filter(function(e){return 0<e.rangeCount}).bind(Ya)}(e).map(function(e){return kl.exact(e.start(),e.soffset(),e.finish(),e.foffset())})}function $a(e,n){return function(e){var n=e.getClientRects(),t=0<n.length?n[0]:e.getBoundingClientRect();return 0<t.width||0<t.height?Me.some(t).map(Da):Me.none()}(Pl(e,n))}function Ja(e,n,t){return function(e,n,t){var r=on.fromDom(e.document);return Wl(r,n,t).map(function(e){return Dl.create(on.fromDom(e.startContainer),e.startOffset,on.fromDom(e.endContainer),e.endOffset)})}(e,n,t)}function Qa(e,n,t,r){return Ll(e,n,Tl(t),r)}function Za(e,n,t,r){return Ll(e,n,Ol(t),r)}function el(e,n){var t=kl.exact(n,0,n,0);return Ga(t)}function nl(e,n){return function(e){return 0===e.length?Me.none():Me.some(e[e.length-1])}(Be(n,"tr")).bind(function(e){return Zn(e,"td,th").map(function(e){return el(0,e)})})}function tl(e,n,t,r){return void 0===r&&(r=Yl),e.property().parent(n).map(function(e){return Gl(e,r)})}function rl(n){return function(e){return 0===n.property().children(e).length}}function ol(e,n){return function(e,n,t){return ef(e,n,rl(e),t)}(tf,e,n)}function il(e,n){return function(e,n,t){return nf(e,n,rl(e),t)}(tf,e,n)}function ul(e){return et(e,"tr")}function cl(e){return"br"===en(e)}function al(n,e,t,r){return function(e,n){return Se(e,n).filter(cl).orThunk(function(){return Se(e,n-1).filter(cl)})}(e,t).bind(function(e){return r.traverse(e).fold(function(){return lf(e,r.gather,n).map(r.relative)},function(e){return function(r){return ve(r).bind(function(n){var t=Ce(n);return af(t,r).map(function(e){return cf(n,t,r,e)})})}(e).map(function(e){return El.on(e.parent(),e.index())})})})}function ll(e){return mf.nu({left:e.left,top:e.top,right:e.right,bottom:e.bottom})}function fl(e,n){return Me.some(e.getRect(n))}function sl(n,e,t){return function(e,n,t){return Xn(function(e,n){return n(e)},$n,e,n,t)}(e,uc).fold(D(!1),function(e){return pf(n,e).exists(function(e){return function(e,n){return e.left()<n.left()||Math.abs(n.right()-e.left())<1||e.left()>n.right()}(t,e)})})}function dl(n,t,e){var r=n.move(e,5),o=wf(t,n,e,r,100).getOr(r);return function(e,n,t){return e.point(n)>t.getInnerHeight()?Me.some(e.point(n)-t.getInnerHeight()):e.point(n)<0?Me.some(-e.point(n)):Me.none()}(n,o,t).fold(function(){return t.situsFromPoint(o.left(),n.point(o))},function(e){return t.scrollBy(0,e),t.situsFromPoint(o.left(),n.point(o)-e)})}function ml(e,n){return function(e,n,t){return $n(e,n,t).isSome()}(e,function(e){return ve(e).exists(function(e){return In(e,n)})})}function gl(n,r,o,e,i){return et(e,"td,th",r).bind(function(t){return et(t,"table",r).bind(function(e){return ml(i,e)?Of(n,r,o).bind(function(n){return et(n.finish(),"td,th",r).map(function(e){return{start:D(t),finish:D(e),range:D(n)}})}):Me.none()})})}function pl(e,n){return et(e,"td,th",n)}var hl=function(n,t,e){function r(){l.stop(),u.isOn()&&(u.off(),i.trigger.stop())}var o=!1,i=fa.create({start:la([]),stop:la([])}),u=wa(),c=function(t,r){var o=null;return{cancel:function(){null!==o&&(f.clearTimeout(o),o=null)},throttle:function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];null!==o&&f.clearTimeout(o),o=f.setTimeout(function(){t.apply(null,e),o=null},r)}}}(r,200);u.events.move.bind(function(e){t.mutate(n,e.info())});function a(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];o&&t.apply(null,e)}}var l=t.sink(ha({forceDrop:r,drop:a(r),move:a(function(e){c.cancel(),u.onEvent(e,t)}),delayDrop:a(c.throttle)}),e);return{element:l.element,go:function(e){l.start(e),u.on(),i.trigger.start()},on:function(){o=!0},off:function(){o=!1},destroy:function(){l.destroy()},events:i.registry}},vl=function(e,n){void 0===n&&(n={});var t=n.mode!==undefined?n.mode:va;return hl(e,t,n)},bl=tu.resolve("resizer-bar-dragging"),wl=function(e,t){var r=go.height,n=Sa(e,t,r),o=fa.create({beforeResize:la(["table"]),afterResize:la(["table"]),startDrag:la([])});return n.events.adjustHeight.bind(function(e){o.trigger.beforeResize(e.table());var n=r.delta(e.delta(),e.table());Ku(e.table(),n,e.row(),r),o.trigger.afterResize(e.table())}),n.events.startAdjust.bind(function(e){o.trigger.startDrag()}),n.events.adjustWidth.bind(function(e){o.trigger.beforeResize(e.table());var n=t.delta(e.delta(),e.table());Yu(e.table(),n,e.column(),t),o.trigger.afterResize(e.table())}),{on:n.on,off:n.off,hideBars:n.hideBars,showBars:n.showBars,destroy:n.destroy,events:o.registry}},yl=function(e,n){return e.inline?aa(gc(e),function(){var e=on.fromTag("div");return re(e,{position:"static",height:"0",width:"0",padding:"0",margin:"0",border:"0"}),Mn(un(),e),e}()):ca(on.fromDom(e.getDoc()))},Cl=function(e,n){e.inline&&Wn(n.parent())},Sl=function(u){function c(e){return"TABLE"===e.nodeName}function r(e){var n=u.dom.getStyle(e,"width")||u.dom.getAttrib(e,"width");return Me.from(n).filter(function(e){return 0<e.length})}function e(){return i}var a,l,o=Me.none(),i=Me.none(),f=Me.none(),s=/(\d+(\.\d+)?)%/;return u.on("init",function(){var e=ho(bc.directionAt),n=yl(u);if(f=Me.some(n),function(e){var n=e.getParam("object_resizing",!0);return _e(n)?"table"===n:n}(u)&&function(e){return e.getParam("table_resize_bars",!0,"boolean")}(u)){var t=wl(n,e);t.on(),t.events.startDrag.bind(function(e){o=Me.some(u.selection.getRng())}),t.events.beforeResize.bind(function(e){var n=e.table().dom();Ii(u,n,bi(n),wi(n))}),t.events.afterResize.bind(function(e){var n=e.table(),t=n.dom();Ci(n),o.each(function(e){u.selection.setRng(e),u.focus()}),Bi(u,t,bi(t),wi(t)),u.undoManager.add()}),i=Me.some(t)}}),u.on("ObjectResizeStart",function(e){var n=e.target;if(c(n)){var t=r(n).map(function(e){return s.test(e)}).getOr(!1);t&&Ei(u)?function(e){te(on.fromDom(e),"width",bi(e).toString()+"px")}(n):!t&&function(e){return!0===e.getParam("table_responsive_width")}(u)&&function(e){var n=on.fromDom(e);ve(n).map(function(e){return xa(n,e)}).each(function(e){te(n,"width",e),p(Be(n,"tr"),function(n){p(Ce(n),function(e){te(e,"width",xa(e,n))})})})}(n),a=e.width,l=r(n).getOr("")}}),u.on("ObjectResized",function(e){var n=e.target;if(c(n)){var t=n;if(s.test(l)){var r=parseFloat(s.exec(l)[1]),o=e.width*r/a;u.dom.setStyle(t,"width",o+"%")}else{var i=[];Dc.each(t.rows,function(e){Dc.each(e.cells,function(e){var n=u.dom.getStyle(e,"width",!0);i.push({cell:e,width:n})})}),Dc.each(i,function(e){u.dom.setStyle(e.cell,"width",e.width),u.dom.setAttrib(e.cell,"width",null)})}}}),u.on("SwitchMode",function(){e().each(function(e){u.readonly?e.hideBars():e.showBars()})}),{lazyResize:e,lazyWire:function(){return f.getOr(ca(on.fromDom(u.getBody())))},destroy:function(){i.each(function(e){e.destroy()}),f.each(function(e){Cl(u,e)})}}},xl=Sr([{none:["current"]},{first:["current"]},{middle:["current","target"]},{last:["current"]}]),Rl=fn(fn({},xl),{none:function(e){return void 0===e&&(e=undefined),xl.none(e)}}),Tl=function(n,e){return Ra(n,e).fold(function(){return Rl.none(n)},function(e){return e.index()+1<e.all().length?Rl.middle(n,e.all()[e.index()+1]):Rl.last(n)})},Ol=function(n,e){return Ra(n,e).fold(function(){return Rl.none()},function(e){return 0<=e.index()-1?Rl.middle(n,e.all()[e.index()-1]):Rl.first(n)})},Dl={create:B("start","soffset","finish","foffset")},Al=Sr([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),El={before:Al.before,on:Al.on,after:Al.after,cata:function(e,n,t,r){return e.fold(n,t,r)},getStart:function(e){return e.fold(o,o,o)}},Nl=Sr([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),kl={domRange:Nl.domRange,relative:Nl.relative,exact:Nl.exact,exactFromRange:function(e){return Nl.exact(e.start(),e.soffset(),e.finish(),e.foffset())},getWin:function(e){return function(e){return on.fromDom(e.dom().ownerDocument.defaultView)}(function(e){return e.match({domRange:function(e){return on.fromDom(e.startContainer)},relative:function(e,n){return El.getStart(e)},exact:function(e,n,t,r){return e}})}(e))},range:Dl.create},Il=function(e,n){e.selectNodeContents(n.dom())},Bl=Sr([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),Pl=function(i,e){return Ea(i,e).match({ltr:function(e,n,t,r){var o=i.document.createRange();return o.setStart(e.dom(),n),o.setEnd(t.dom(),r),o},rtl:function(e,n,t,r){var o=i.document.createRange();return o.setStart(t.dom(),r),o.setEnd(e.dom(),n),o}})},Ml=function(e,n,t,r){return tn(n)?function(n,t,r,o){var e=n.dom().createRange();e.selectNode(t.dom());var i=e.getClientRects();return bo(i,function(e){return Na(e,r,o)?Me.some(e):Me.none()}).map(function(e){return ka(n,t,r,o,e)})}(e,n,t,r):function(n,e,t,r){var o=n.dom().createRange(),i=Ce(e);return bo(i,function(e){return o.selectNode(e.dom()),Na(o.getBoundingClientRect(),t,r)?Ml(n,e,t,r):Me.none()})}(e,n,t,r)},Wl=document.caretPositionFromPoint?function(t,e,n){return Me.from(t.dom().caretPositionFromPoint(e,n)).bind(function(e){if(null===e.offsetNode)return Me.none();var n=t.dom().createRange();return n.setStart(e.offsetNode,e.offset),n.collapse(),Me.some(n)})}:document.caretRangeFromPoint?function(e,n,t){return Me.from(e.dom().caretRangeFromPoint(n,t))}:function(t,r,o){return on.fromPoint(t,r,o).bind(function(e){function n(){return function(e,n,t){return(0===Ce(n).length?Ma:Pa)(e,n,t)}(t,e,r)}return 0===Ce(e).length?n():Wa(t,e,r,o).orThunk(n)})},_l=tinymce.util.Tools.resolve("tinymce.util.VK"),Ll=function(r,e,n,o,t){return n.fold(Me.none,Me.none,function(e,n){return Ct(n).map(function(e){return el(0,e)})},function(t){return lt.table(t,e).bind(function(e){var n=Er.noMenu(t);return r.undoManager.transact(function(){o.insertRowsAfter(e,n)}),nl(0,e)})})},jl=["table","li","dl"],zl={handle:function(n,t,r,o){if(n.keyCode===_l.TAB){var i=gc(t),u=function(e){var n=en(e);return In(e,i)||l(jl,n)},e=t.selection.getRng();if(e.collapsed){var c=on.fromDom(e.startContainer);lt.cell(c,u).each(function(e){n.preventDefault(),(n.shiftKey?Za:Qa)(t,u,e,r,o).each(function(e){t.selection.setRng(e)})})}}}},Hl={create:B("selection","kill")},Fl=function(e,n,t,r){return{start:D(El.on(e,n)),finish:D(El.on(t,r))}},Ul={convertToRange:function(e,n){var t=Pl(e,n);return Dl.create(on.fromDom(t.startContainer),t.startOffset,on.fromDom(t.endContainer),t.endOffset)},makeSitus:Fl},ql=function(t,e,r,n,o){return In(r,n)?Me.none():dr(r,n,e).bind(function(e){var n=e.boxes().getOr([]);return 0<n.length?(o(t,n,e.start(),e.finish()),Me.some(Hl.create(Me.some(Ul.makeSitus(r,0,r,wt(r))),!0))):Me.none()})},Vl={sync:function(t,r,e,n,o,i,u){return In(e,o)&&n===i?Me.none():et(e,"td,th",r).bind(function(n){return et(o,"td,th",r).bind(function(e){return ql(t,r,n,e,u)})})},detect:ql,update:function(e,n,t,r,o){return gr(r,e,n,o.firstSelectedSelector(),o.lastSelectedSelector()).map(function(e){return o.clearBeforeUpdate(t),o.selectRange(t,e.boxes(),e.start(),e.finish()),e.boxes()})}},Gl=B("item","mode"),Yl=function(e,n,t,r){return void 0===r&&(r=Kl),t.sibling(e,n).map(function(e){return Gl(e,r)})},Kl=function(e,n,t,r){void 0===r&&(r=Kl);var o=e.property().children(n);return t.first(o).map(function(e){return Gl(e,r)})},Xl=[{current:tl,next:Yl,fallback:Me.none()},{current:Yl,next:Kl,fallback:Me.some(tl)},{current:Kl,next:Kl,fallback:Me.some(Yl)}],$l=function(n,t,r,o,e){return void 0===e&&(e=Xl),y(e,function(e){return e.current===r}).bind(function(e){return e.current(n,t,o,e.next).orThunk(function(){return e.fallback.bind(function(e){return $l(n,t,e,o)})})})},Jl=function(){return{sibling:function(e,n){return e.query().prevSibling(n)},first:function(e){return 0<e.length?Me.some(e[e.length-1]):Me.none()}}},Ql=function(){return{sibling:function(e,n){return e.query().nextSibling(n)},first:function(e){return 0<e.length?Me.some(e[0]):Me.none()}}},Zl=function(n,e,t,r,o,i){return $l(n,e,r,o).bind(function(e){return i(e.item())?Me.none():t(e.item())?Me.some(e.item()):Zl(n,e.item(),t,e.mode(),o,i)})},ef=function(e,n,t,r){return Zl(e,n,t,Yl,Jl(),r)},nf=function(e,n,t,r){return Zl(e,n,t,Yl,Ql(),r)},tf=$t(),rf=B("element","offset"),of=(B("element","deltaOffset"),B("element","start","finish"),B("begin","end"),B("element","text"),Sr([{none:["message"]},{success:[]},{failedUp:["cell"]},{failedDown:["cell"]}])),uf=fn(fn({},of),{verify:function(t,n,e,r,o,i,u){return et(r,"td,th",u).bind(function(e){return et(n,"td,th",u).map(function(n){return In(e,n)?In(r,e)&&wt(e)===o?i(n):of.none("in same cell"):fr.sharedOne(ul,[e,n]).fold(function(){return function(e,n,t){var r=e.getRect(n),o=e.getRect(t);return o.right>r.left&&o.left<r.right}(t,n,e)?of.success():i(n)},function(e){return i(n)})})}).getOr(of.none("default"))},cata:function(e,n,t,r,o){return e.fold(n,t,r,o)}}),cf=(B("ancestor","descendants","element","index"),B("parent","children","element","index")),af=function(e,n){return C(e,b(In,n))},lf=function(e,n,t){return n(e,t).bind(function(e){return tn(e)&&0===ht(e).trim().length?lf(e,n,t):Me.some(e)})},ff=function(e,n,t,r){return(cl(n)?function(e,n,t){return t.traverse(n).orThunk(function(){return lf(n,t.gather,e)}).map(t.relative)}(e,n,r):al(e,n,t,r)).map(function(e){return{start:D(e),finish:D(e)}})},sf=function(e){return uf.cata(e,function(e){return Me.none()},function(){return Me.none()},function(e){return Me.some(rf(e,0))},function(e){return Me.some(rf(e,wt(e)))})},df=Xe(["left","top","right","bottom"],[]),mf={nu:df,moveUp:function(e,n){return df({left:e.left(),top:e.top()-n,right:e.right(),bottom:e.bottom()-n})},moveDown:function(e,n){return df({left:e.left(),top:e.top()+n,right:e.right(),bottom:e.bottom()+n})},moveBottomTo:function(e,n){var t=e.bottom()-e.top();return df({left:e.left(),top:n-t,right:e.right(),bottom:n})},moveTopTo:function(e,n){var t=e.bottom()-e.top();return df({left:e.left(),top:n,right:e.right(),bottom:n+t})},getTop:function(e){return e.top()},getBottom:function(e){return e.bottom()},translate:function(e,n,t){return df({left:e.left()+n,top:e.top()+t,right:e.right()+n,bottom:e.bottom()+t})},toString:function(e){return"("+e.left()+", "+e.top()+") -> ("+e.right()+", "+e.bottom()+")"}},gf=function(e,n,t){return nn(n)?fl(e,n).map(ll):tn(n)?function(e,n,t){return 0<=t&&t<wt(n)?e.getRangedRect(n,t,n,t+1):0<t?e.getRangedRect(n,t-1,n,t):Me.none()}(e,n,t).map(ll):Me.none()},pf=function(e,n){return nn(n)?fl(e,n).map(ll):tn(n)?e.getRangedRect(n,0,n,wt(n)).map(ll):Me.none()},hf=Sr([{none:[]},{retry:["caret"]}]),vf={point:mf.getTop,adjuster:function(e,n,t,r,o){var i=mf.moveUp(o,5);return Math.abs(t.top()-r.top())<1?hf.retry(i):t.bottom()<o.top()?hf.retry(i):t.bottom()===o.top()?hf.retry(mf.moveUp(o,1)):sl(e,n,o)?hf.retry(mf.translate(i,5,0)):hf.none()},move:mf.moveUp,gather:ol},bf={point:mf.getBottom,adjuster:function(e,n,t,r,o){var i=mf.moveDown(o,5);return Math.abs(t.bottom()-r.bottom())<1?hf.retry(i):t.top()>o.bottom()?hf.retry(i):t.top()===o.bottom()?hf.retry(mf.moveDown(o,1)):sl(e,n,o)?hf.retry(mf.translate(i,5,0)):hf.none()},move:mf.moveDown,gather:il},wf=function(t,r,o,i,u){return 0===u?Me.some(i):function(e,n,t){return e.elementFromPoint(n,t).filter(function(e){return"table"===en(e)}).isSome()}(t,i.left(),r.point(i))?function(e,n,t,r,o){return wf(e,n,t,n.move(r,5),o)}(t,r,o,i,u-1):t.situsFromPoint(i.left(),r.point(i)).bind(function(e){return e.start().fold(Me.none,function(n){return pf(t,n).bind(function(e){return r.adjuster(t,n,e,o,i).fold(Me.none,function(e){return wf(t,r,o,e,u-1)})}).orThunk(function(){return Me.some(i)})},Me.none)})},yf={tryUp:b(dl,vf),tryDown:b(dl,bf),ieTryUp:function(e,n){return e.situsFromPoint(n.left(),n.top()-5)},ieTryDown:function(e,n){return e.situsFromPoint(n.left(),n.bottom()+5)},getJumpSize:D(5)},Cf=me(),Sf=function(r,o,i,u,c,a){return 0===a?Me.none():Tf(r,o,i,u,c).bind(function(e){var n=r.fromSitus(e),t=uf.verify(r,i,u,n.finish(),n.foffset(),c.failure,o);return uf.cata(t,function(){return Me.none()},function(){return Me.some(e)},function(e){return In(i,e)&&0===u?xf(r,i,u,mf.moveUp,c):Sf(r,o,e,0,c,a-1)},function(e){return In(i,e)&&u===wt(e)?xf(r,i,u,mf.moveDown,c):Sf(r,o,e,wt(e),c,a-1)})})},xf=function(n,e,t,r,o){return gf(n,e,t).bind(function(e){return Rf(n,o,r(e,yf.getJumpSize()))})},Rf=function(e,n,t){return Cf.browser.isChrome()||Cf.browser.isSafari()||Cf.browser.isFirefox()||Cf.browser.isEdge()?n.otherRetry(e,t):Cf.browser.isIE()?n.ieRetry(e,t):Me.none()},Tf=function(n,e,t,r,o){return gf(n,t,r).bind(function(e){return Rf(n,o,e)})},Of=function(n,t,r){return function(o,i,u){return o.getSelection().bind(function(r){return ff(i,r.finish(),r.foffset(),u).fold(function(){return Me.some(rf(r.finish(),r.foffset()))},function(e){var n=o.fromSitus(e),t=uf.verify(o,r.finish(),r.foffset(),n.finish(),n.foffset(),u.failure,i);return sf(t)})})}(n,t,r).bind(function(e){return Sf(n,t,e.element(),e.offset(),r,20).map(n.fromSitus)})},Df=me(),Af=function(e,n,t,r,o,i){return Df.browser.isIE()?Me.none():i(r,n).orThunk(function(){return gl(e,n,t,r,o).map(function(e){var n=e.range();return Hl.create(Me.some(Ul.makeSitus(n.start(),n.soffset(),n.finish(),n.foffset())),!0)})})},Ef=function(e,n,t,r,o,i,u){return gl(e,t,r,o,i).bind(function(e){return Vl.detect(n,t,e.start(),e.finish(),u)})},Nf=function(e,r){return et(e,"tr",r).bind(function(t){return et(t,"table",r).bind(function(e){var n=Be(e,"tr");return In(t,n[0])?function(e,n,t){return ef(tf,e,n,t)}(e,function(e){return St(e).isSome()},r).map(function(e){var n=wt(e);return Hl.create(Me.some(Ul.makeSitus(e,n,e,n)),!0)}):Me.none()})})},kf=function(e,r){return et(e,"tr",r).bind(function(t){return et(t,"table",r).bind(function(e){var n=Be(e,"tr");return In(t,n[n.length-1])?function(e,n,t){return nf(tf,e,n,t)}(e,function(e){return Ct(e).isSome()},r).map(function(e){return Hl.create(Me.some(Ul.makeSitus(e,0,e,0)),!0)}):Me.none()})})};function If(n){return function(e){return e===n}}function Bf(c){return{elementFromPoint:function(e,n){return on.fromPoint(on.fromDom(c.document),e,n)},getRect:function(e){return e.dom().getBoundingClientRect()},getRangedRect:function(e,n,t,r){var o=kl.exact(e,n,t,r);return $a(c,o).map(jf)},getSelection:function(){return Xa(c).map(function(e){return Ul.convertToRange(c,e)})},fromSitus:function(e){var n=kl.relative(e.start(),e.finish());return Ul.convertToRange(c,n)},situsFromPoint:function(e,n){return Ja(c,e,n).map(function(e){return Fl(e.start(),e.soffset(),e.finish(),e.foffset())})},clearSelection:function(){!function(e){e.getSelection().removeAllRanges()}(c)},collapseSelection:function(u){void 0===u&&(u=!1),Xa(c).each(function(e){return e.fold(function(e){return e.collapse(u)},function(e,n){var t=u?e:n;Va(c,t,t)},function(e,n,t,r){var o=u?e:t,i=u?n:r;qa(c,o,i,o,i)})})},setSelection:function(e){qa(c,e.start(),e.soffset(),e.finish(),e.foffset())},setRelativeSelection:function(e,n){Va(c,e,n)},selectContents:function(e){Ka(c,e)},getInnerHeight:function(){return c.innerHeight},getScrollY:function(){return function(e){var n=e!==undefined?e.dom():f.document,t=n.body.scrollLeft||n.documentElement.scrollLeft,r=n.body.scrollTop||n.documentElement.scrollTop;return lo(t,r)}(on.fromDom(c.document)).top()},scrollBy:function(e,n){!function(e,n,t){(t!==undefined?t.dom():f.document).defaultView.scrollBy(e,n)}(e,n,on.fromDom(c.document))}}}function Pf(n,e){p(e,function(e){!function(e,n){Co(e)?e.dom().classList.remove(n):xo(e,n);To(e)}(n,e)})}var Mf={down:{traverse:ye,gather:il,relative:El.before,otherRetry:yf.tryDown,ieRetry:yf.ieTryDown,failure:uf.failedDown},up:{traverse:we,gather:ol,relative:El.before,otherRetry:yf.tryUp,ieRetry:yf.ieTryUp,failure:uf.failedUp}},Wf=If(38),_f=If(40),Lf={ltr:{isBackward:If(37),isForward:If(39)},rtl:{isBackward:If(39),isForward:If(37)},isUp:Wf,isDown:_f,isNavigation:function(e){return 37<=e&&e<=40}},jf=function(e){return{left:e.left(),top:e.top(),right:e.right(),bottom:e.bottom(),width:e.width(),height:e.height()}},zf=(me().browser.isSafari(),B("rows","cols")),Hf={mouse:function(e,n,t,r){var o=function c(o,i,n,u){function t(){r=Me.none()}var r=Me.none();return{mousedown:function(e){u.clear(i),r=pl(e.target(),n)},mouseover:function(e){r.each(function(r){u.clearBeforeUpdate(i),pl(e.target(),n).each(function(t){dr(r,t,n).each(function(e){var n=e.boxes().getOr([]);(1<n.length||1===n.length&&!In(r,t))&&(u.selectRange(i,n,e.start(),e.finish()),o.selectContents(t))})})})},mouseup:function(e){r.each(t)}}}(Bf(e),n,t,r);return{mousedown:o.mousedown,mouseover:o.mouseover,mouseup:o.mouseup}},keyboard:function(e,l,f,s){function d(){return s.clear(l),Me.none()}var m=Bf(e);return{keydown:function(e,n,t,r,o,i){var u=e.raw(),c=u.which,a=!0===u.shiftKey;return mr(l,s.selectedSelector()).fold(function(){return Lf.isDown(c)&&a?b(Ef,m,l,f,Mf.down,r,n,s.selectRange):Lf.isUp(c)&&a?b(Ef,m,l,f,Mf.up,r,n,s.selectRange):Lf.isDown(c)?b(Af,m,f,Mf.down,r,n,kf):Lf.isUp(c)?b(Af,m,f,Mf.up,r,n,Nf):Me.none},function(n){function e(e){return function(){return bo(e,function(e){return Vl.update(e.rows(),e.cols(),l,n,s)}).fold(function(){return pr(l,s.firstSelectedSelector(),s.lastSelectedSelector()).map(function(e){var n=Lf.isDown(c)||i.isForward(c)?El.after:El.before;return m.setRelativeSelection(El.on(e.first(),0),n(e.table())),s.clear(l),Hl.create(Me.none(),!0)})},function(e){return Me.some(Hl.create(Me.none(),!0))})}}return Lf.isDown(c)&&a?e([zf(1,0)]):Lf.isUp(c)&&a?e([zf(-1,0)]):i.isBackward(c)&&a?e([zf(0,-1),zf(-1,0)]):i.isForward(c)&&a?e([zf(0,1),zf(1,0)]):Lf.isNavigation(c)&&!1==a?d:Me.none})()},keyup:function(t,r,o,i,u){return mr(l,s.selectedSelector()).fold(function(){var e=t.raw(),n=e.which;return!1==(!0===e.shiftKey)?Me.none():Lf.isNavigation(n)?Vl.sync(l,f,r,o,i,u,s.selectRange):Me.none()},Me.none)}}},external:function(e,r,n,o){var i=Bf(e);return function(e,t){o.clearBeforeUpdate(r),dr(e,t,n).each(function(e){var n=e.boxes().getOr([]);o.selectRange(r,n,e.start(),e.finish()),i.selectContents(t),i.collapseSelection()})}}},Ff={byClass:function(o){function i(e){var n=Be(e,o.selectedSelector());p(n,t)}var u=function(n){return function(e){Ro(e,n)}}(o.selected()),t=function(n){return function(e){Pf(e,n)}}([o.selected(),o.lastSelected(),o.firstSelected()]);return{clearBeforeUpdate:i,clear:i,selectRange:function(e,n,t,r){i(e),p(n,u),Ro(t,o.firstSelected()),Ro(r,o.lastSelected())},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}},byAttr:function(o,i,n){function t(e){Y(e,o.selected()),Y(e,o.firstSelected()),Y(e,o.lastSelected())}function u(e){U(e,o.selected(),"1")}function c(e){r(e),n()}var r=function(e){var n=Be(e,o.selectedSelector());p(n,t)};return{clearBeforeUpdate:r,clear:c,selectRange:function(e,n,t,r){c(e),p(n,u),U(t,o.firstSelected(),"1"),U(r,o.lastSelected(),"1"),i(n,t,r)},selectedSelector:o.selectedSelector,firstSelectedSelector:o.firstSelectedSelector,lastSelectedSelector:o.lastSelectedSelector}}},Uf={getOtherCells:function(e,n,t){var r=ft(e),o=mt.generate(r);return Fo(o,n).map(function(e){var n=bu(o,t,!1);return{upOrLeftCells:function(e,t,n){var r=e.slice(0,t[t.length-1].row()+1),o=_o(r,n);return x(o,function(e){var n=e.cells().slice(0,t[t.length-1].column()+1);return g(n,function(e){return e.element()})})}(n,e,t),downOrRightCells:function(e,t,n){var r=e.slice(t[0].row()+t[0].rowspan()-1,e.length),o=_o(r,n);return x(o,function(e){var n=e.cells().slice(t[0].column()+t[0].colspan()-1,+e.cells().length);return g(n,function(e){return e.element()})})}(n,e,t)}})}},qf=function(e){return!1===Oo(on.fromDom(e.target),"ephox-snooker-resizer-bar")};function Vf(w,y,e){var C=Xe(["mousedown","mouseover","mouseup","keyup","keydown"],[]),S=Me.none(),a=Sc(w),x=Ff.byAttr(Cr,function(i,u,c){e.targets().each(function(o){lt.table(u).each(function(e){var n=on.fromDom(w.getDoc()),t=Wt.cellOperations(T,n,a),r=Uf.getOtherCells(e,o,t);xc(w,i,u,c,r)})})},function(){Rc(w)});w.on("init",function(e){var r=w.getWin(),o=gc(w),n=pc(w),t=Hf.mouse(r,o,n,x),c=Hf.keyboard(r,o,n,x),i=Hf.external(r,o,n,x);w.on("TableSelectorChange",function(e){i(e.start,e.finish)});function a(e,n){!function(e){return!0===e.raw().shiftKey}(e)||(n.kill()&&e.kill(),n.selection().each(function(e){var n=kl.relative(e.start(),e.finish()),t=Pl(r,n);w.selection.setRng(t)}))}function u(e){var n=v(e);if(n.raw().shiftKey&&Lf.isNavigation(n.raw().which)){var t=w.selection.getRng(),r=on.fromDom(t.startContainer),o=on.fromDom(t.endContainer);c.keyup(n,r,t.startOffset,o,t.endOffset).each(function(e){a(n,e)})}}function l(e){var n=v(e);y().each(function(e){e.hideBars()});var t=w.selection.getRng(),r=on.fromDom(w.selection.getStart()),o=on.fromDom(t.startContainer),i=on.fromDom(t.endContainer),u=bc.directionAt(r).isRtl()?Lf.rtl:Lf.ltr;c.keydown(n,o,t.startOffset,i,t.endOffset,u).each(function(e){a(n,e)}),y().each(function(e){e.showBars()})}function f(e){return e.hasOwnProperty("x")&&e.hasOwnProperty("y")}function s(e){return 0===e.button}function d(e){s(e)&&qf(e)&&t.mousedown(v(e))}function m(e){(function(e){return e.buttons===undefined||0!=(1&e.buttons)})(e)&&qf(e)&&t.mouseover(v(e))}function g(e){s(e)&&qf(e)&&t.mouseup(v(e))}var p,h,v=function(e){function n(){e.stopPropagation()}function t(){e.preventDefault()}var r=on.fromDom(e.target),o=O(t,n);return{target:D(r),x:D(f(e)?e.x:null),y:D(f(e)?e.y:null),stop:n,prevent:t,kill:o,raw:D(e)}},b=(p=R(on.fromDom(o)),h=R(0),{touchEnd:function(e){var n=on.fromDom(e.target);if("td"===en(n)||"th"===en(n)){var t=p.get(),r=h.get();In(t,n)&&e.timeStamp-r<300&&(e.preventDefault(),i(n,n))}p.set(n),h.set(e.timeStamp)}});w.on("mousedown",d),w.on("mouseover",m),w.on("mouseup",g),w.on("touchend",b.touchEnd),w.on("keyup",u),w.on("keydown",l),w.on("NodeChange",function(){var e=w.selection,n=on.fromDom(e.getStart()),t=on.fromDom(e.getEnd());fr.sharedOne(lt.table,[n,t]).fold(function(){x.clear(o)},T)}),S=Me.some(C({mousedown:d,mouseover:m,mouseup:g,keyup:u,keydown:l}))});return{clear:x.clear,destroy:function(){S.each(function(e){})}}}var Gf=function(n){return{get:function(){var e=gc(n);return hr(e,Cr.selectedSelector()).fold(function(){return n.selection.getStart()===undefined?Rr.none():Rr.single(n.selection)},function(e){return Rr.multiple(e)})}}},Yf=function(e,t){function n(){return oa(e).bind(function(n){return lt.table(n).map(function(e){return"caption"===en(n)?Er.notCell(n):Er.forMenu(t,e,n)})})}function r(){i.set(Z(n)()),p(u.get(),function(e){return e()})}function o(n,t){function r(){return i.get().fold(function(){n.setDisabled(!0)},function(e){n.setDisabled(t(e))})}return r(),u.set(u.get().concat([r])),function(){u.set(h(u.get(),function(e){return e!==r}))}}var i=R(Me.none()),u=R([]);return e.on("NodeChange TableSelectorChange",r),{onSetupTable:function(e){return o(e,function(e){return!1})},onSetupCellOrRow:function(e){return o(e,function(e){return"caption"===en(e.element())})},onSetupMergeable:function(e){return o(e,function(e){return e.mergable().isNone()})},onSetupUnmergeable:function(e){return o(e,function(e){return e.unmergable().isNone()})},resetTargets:r,targets:function(){return i.get()}}},Kf={addButtons:function(n,e){n.ui.registry.addMenuButton("table",{tooltip:"Table",icon:"table",fetch:function(e){return e("inserttable | cell row column | advtablesort | tableprops deletetable")}});function t(e){return function(){return n.execCommand(e)}}n.ui.registry.addButton("tableprops",{tooltip:"Table properties",onAction:t("mceTableProps"),icon:"table",onSetup:e.onSetupTable}),n.ui.registry.addButton("tabledelete",{tooltip:"Delete table",onAction:t("mceTableDelete"),icon:"table-delete-table",onSetup:e.onSetupTable}),n.ui.registry.addButton("tablecellprops",{tooltip:"Cell properties",onAction:t("mceTableCellProps"),icon:"table-cell-properties",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablemergecells",{tooltip:"Merge cells",onAction:t("mceTableMergeCells"),icon:"table-merge-cells",onSetup:e.onSetupMergeable}),n.ui.registry.addButton("tablesplitcells",{tooltip:"Split cell",onAction:t("mceTableSplitCells"),icon:"table-split-cells",onSetup:e.onSetupUnmergeable}),n.ui.registry.addButton("tableinsertrowbefore",{tooltip:"Insert row before",onAction:t("mceTableInsertRowBefore"),icon:"table-insert-row-above",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertrowafter",{tooltip:"Insert row after",onAction:t("mceTableInsertRowAfter"),icon:"table-insert-row-after",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tabledeleterow",{tooltip:"Delete row",onAction:t("mceTableDeleteRow"),icon:"table-delete-row",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablerowprops",{tooltip:"Row properties",onAction:t("mceTableRowProps"),icon:"table-row-properties",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertcolbefore",{tooltip:"Insert column before",onAction:t("mceTableInsertColBefore"),icon:"table-insert-column-before",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertcolafter",{tooltip:"Insert column after",onAction:t("mceTableInsertColAfter"),icon:"table-insert-column-after",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tabledeletecol",{tooltip:"Delete column",onAction:t("mceTableDeleteCol"),icon:"table-delete-column",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablecutrow",{tooltip:"Cut row",onAction:t("mceTableCutRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablecopyrow",{tooltip:"Copy row",onAction:t("mceTableCopyRow"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablepasterowbefore",{tooltip:"Paste row before",onAction:t("mceTablePasteRowBefore"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tablepasterowafter",{tooltip:"Paste row after",onAction:t("mceTablePasteRowAfter"),icon:"temporary-placeholder",onSetup:e.onSetupCellOrRow}),n.ui.registry.addButton("tableinsertdialog",{tooltip:"Insert table",onAction:t("mceInsertTable"),icon:"table"})},addToolbars:function(n){var e=function(e){return e.getParam("table_toolbar","tableprops tabledelete | tableinsertrowbefore tableinsertrowafter tabledeleterow | tableinsertcolbefore tableinsertcolafter tabledeletecol")}(n);0<e.length&&n.ui.registry.addContextToolbar("table",{predicate:function(e){return n.dom.is(e,"table")&&n.getBody().contains(e)},items:e,scope:"node",position:"node"})}},Xf={addMenuItems:function(r,e){function n(e){return function(){return r.execCommand(e)}}function t(e){var n=e.numRows,t=e.numColumns;r.undoManager.transact(function(){Zc(r,t,n)}),r.addVisual()}var o={text:"Table properties",onSetup:e.onSetupTable,onAction:n("mceTableProps")},i={text:"Delete table",icon:"table-delete-table",onSetup:e.onSetupTable,onAction:n("mceTableDelete")},u=[{type:"menuitem",text:"Insert row before",icon:"table-insert-row-above",onAction:n("mceTableInsertRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert row after",icon:"table-insert-row-after",onAction:n("mceTableInsertRowAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete row",icon:"table-delete-row",onAction:n("mceTableDeleteRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Row properties",icon:"table-row-properties",onAction:n("mceTableRowProps"),onSetup:e.onSetupCellOrRow},{type:"separator"},{type:"menuitem",text:"Cut row",onAction:n("mceTableCutRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Copy row",onAction:n("mceTableCopyRow"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row before",onAction:n("mceTablePasteRowBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Paste row after",onAction:n("mceTablePasteRowAfter"),onSetup:e.onSetupCellOrRow}],c={type:"nestedmenuitem",text:"Row",getSubmenuItems:function(){return u}},a=[{type:"menuitem",text:"Insert column before",icon:"table-insert-column-before",onAction:n("mceTableInsertColBefore"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Insert column after",icon:"table-insert-column-after",onAction:n("mceTableInsertColAfter"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Delete column",icon:"table-delete-column",onAction:n("mceTableDeleteCol"),onSetup:e.onSetupCellOrRow}],l={type:"nestedmenuitem",text:"Column",getSubmenuItems:function(){return a}},f=[{type:"menuitem",text:"Cell properties",icon:"table-cell-properties",onAction:n("mceTableCellProps"),onSetup:e.onSetupCellOrRow},{type:"menuitem",text:"Merge cells",icon:"table-merge-cells",onAction:n("mceTableMergeCells"),onSetup:e.onSetupMergeable},{type:"menuitem",text:"Split cell",icon:"table-split-cells",onAction:n("mceTableSplitCells"),onSetup:e.onSetupUnmergeable}],s={type:"nestedmenuitem",text:"Cell",getSubmenuItems:function(){return f}};!1===function(e){return e.getParam("table_grid",!0,"boolean")}(r)?r.ui.registry.addMenuItem("inserttable",{text:"Table",icon:"table",onAction:n("mceInsertTable")}):r.ui.registry.addNestedMenuItem("inserttable",{text:"Table",icon:"table",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"inserttable",onAction:t}]}}),r.ui.registry.addMenuItem("inserttabledialog",{text:"Insert table",icon:"table",onAction:n("mceInsertTable")}),r.ui.registry.addMenuItem("tableprops",o),r.ui.registry.addMenuItem("deletetable",i),r.ui.registry.addNestedMenuItem("row",c),r.ui.registry.addNestedMenuItem("column",l),r.ui.registry.addNestedMenuItem("cell",s),r.ui.registry.addContextMenu("table",{update:function(){return e.resetTargets(),e.targets().fold(function(){return""},function(e){return"caption"===en(e.element())?"tableprops deletetable":"cell row column | advtablesort | tableprops deletetable"})}})}},$f=function(t,n,e,r){return{insertTable:function(e,n){return Zc(t,e,n)},setClipboardRows:function(e){return function(e,n){var t=g(e,on.fromDom);n.set(Me.from(t))}(e,n)},getClipboardRows:function(){return function(e){return e.get().fold(function(){},function(e){return g(e,function(e){return e.dom()})})}(n)},resizeHandler:e,selectionTargets:r}};function Jf(n){var e=Gf(n),t=Yf(n,e),r=Sl(n),o=Vf(n,r.lazyResize,t),i=Tc(n,r.lazyWire),u=R(Me.none());return ua.registerCommands(n,i,o,e,u),Nr.registerEvents(n,e,i,o),Xf.addMenuItems(n,t),Kf.addButtons(n,t),Kf.addToolbars(n),n.on("PreInit",function(){n.serializer.addTempAttr(Cr.firstSelected()),n.serializer.addTempAttr(Cr.lastSelected())}),Cc(n)&&n.on("keydown",function(e){zl.handle(e,n,i,r.lazyWire)}),n.on("remove",function(){r.destroy(),o.destroy()}),$f(n,u,r,t)}!function Zf(){We.add("table",Jf)}()}(window);