{"_from": "@types/jquery@3.3.29", "_id": "@types/jquery@3.3.29", "_inBundle": false, "_integrity": "sha512-FhJvBninYD36v3k6c+bVk1DSZwh7B5Dpb/Pyk3HKVsiohn0nhbefZZ+3JXbWQhFyt0MxSl2jRDdGQPHeOHFXrQ==", "_location": "/@types/jquery", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/jquery@3.3.29", "name": "@types/jquery", "escapedName": "@types%2fjquery", "scope": "@types", "rawSpec": "3.3.29", "saveSpec": null, "fetchSpec": "3.3.29"}, "_requiredBy": ["/", "/@types/jqueryui", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@types/jquery/-/jquery-3.3.29.tgz", "_shasum": "680a2219ce3c9250483722fccf5570d1e2d08abd", "_spec": "@types/jquery@3.3.29", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/leonard-thieu"}, {"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "url": "https://github.com/choffmeister"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON><PERSON>ei"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/tasoili"}, {"name": "<PERSON>", "url": "https://github.com/jasons-novaleaf"}, {"name": "<PERSON>", "url": "https://github.com/seanski"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/Guuz"}, {"name": "<PERSON>", "url": "https://github.com/ksummerlin"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/basarat"}, {"name": "<PERSON>", "url": "https://github.com/nwolverson"}, {"name": "<PERSON>", "url": "https://github.com/derekcicerone"}, {"name": "<PERSON>", "url": "https://github.com/AndrewGaspar"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/seikichi"}, {"name": "<PERSON>", "url": "https://github.com/benjaminjackman"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/s093294"}, {"name": "<PERSON>", "url": "https://github.com/JoshStrobl"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON>", "url": "https://github.com/DickvdBrink"}, {"name": "<PERSON>", "url": "https://github.com/King2500"}, {"name": "<PERSON>", "url": "https://github.com/terrymun"}], "dependencies": {"@types/sizzle": "*"}, "deprecated": false, "description": "TypeScript definitions for jquery", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/jquery", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.3", "types": "index", "typesPublisherContentHash": "071233c6e9934a76dcae8dca364b8a702492270644476df618bccad27a3c7af1", "version": "3.3.29"}