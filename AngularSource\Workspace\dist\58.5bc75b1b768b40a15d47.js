(window.webpackJsonp=window.webpackJsonp||[]).push([[58],{I6gd:function(t,e,i){"use strict";i.r(e);var o=i("CcnG"),l=i("mrSG"),n=i("ZYCi"),a=i("447K"),r=i("wd/R"),s=i.n(r),u=i("6blF"),d=(i("0GgQ"),i("ik3b")),c=(i("EVdn"),function(t){function e(e,i){var o=t.call(this,i,e)||this;return o.commonService=e,o.element=i,o.jsonReader=new a.L,o.inputData=new a.G(o.commonService),o.alertingData=new a.G(o.commonService),o.updateRefreshRate=new a.G(o.commonService),o.updateFontSize=new a.G(o.commonService),o.baseURL=a.Wb.getBaseURL(),o.actionMethod="",o.actionPath="",o.requestParams=[],o.invalidComms="",o.screenVersion=new a.V(o.commonService),o.refreshRate=10,o.refreshStatus="N",o.comboOpen=!1,o.datePickerOpen=!1,o.comboChange=!1,o.screenName="Currency Monitor",o.versionNumber="1.1.0040",o.dateCompare="",o.fontValue="",o.fontLabel="",o.fontRequest="",o.currentFontSize="",o.tempFontSize="",o.dateChanged=!1,o.showBuildInProgress=!1,o.errorLocation=0,o.moduleId="Predict",o.tooltipEntityId=null,o.tooltipCurrencyCode=null,o.tooltipFacilityId=null,o.tooltipSelectedDate=null,o.tooltipOtherParams=[],o.selectedNodeId=null,o.treeLevelValue=null,o.lastSelectedTooltipParams=null,o.eventsCreated=!1,o.customTooltip=null,o.swtAlert=new a.bb(e),o}return l.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this,this.entityCombo.toolTip=a.x.call("getBundle","tip","entity","Select an Entity ID"),this.ccyCombo.toolTip=a.x.call("getBundle","tip","currency","Select Currency Group"),this.showDays.toolTip=a.x.call("getBundle","tip","showdays","Number of days to show"),this.optionsButton.label=a.x.call("getBundle","text","button-options","Options"),this.optionsButton.toolTip=a.x.call("getBundle","tip","button-options","Change options"),this.refreshButton.label=a.x.call("getBundle","text","button-refresh","Refresh window"),this.refreshButton.toolTip=a.x.call("getBundle","tip","button-refresh","Refresh"),this.closeButton.label=a.x.call("getBundle","text","button-close","Close"),this.closeButton.toolTip=a.x.call("getBundle","tip","button-close","Close window"),this.accountRadio.toolTip=a.x.call("getBundle","tip","acctbrkdown","Select Account to view Account Monitor"),this.accountRadio.label=a.x.call("getBundle","text","acctbrkdown","Account"),this.movementRadio.toolTip=a.x.call("getBundle","tip","mvmntbrkdown","Select Movement to view Movement Summary Detail"),this.movementRadio.label=a.x.call("getBundle","text","mvmntbrkdown","Movement"),this.bookRadio.toolTip=a.x.call("getBundle","tip","bookbrkdown","Select Book to view Book Monitor"),this.bookRadio.label=a.x.call("getBundle","text","bookbrkdown","Bookcode"),this.groupRadio.toolTip=a.x.call("getBundle","tip","grpmonitor","Select Group to view Group Monitor"),this.groupRadio.label=a.x.call("getBundle","text","grpmonitor","Group"),this.meatagroupRadio.toolTip=a.x.call("getBundle","tip","mtagrpmonitor","Select Metagroup to view Metagroup Monitor"),this.meatagroupRadio.label=a.x.call("getBundle","text","mtagrpmonitor","Metagroup")},e.prototype.onLoad=function(){var t=this;this.currencyGrid=this.displaycontainer.addChild(a.hb),this.totalsGrid=this.totalsContainer.addChild(a.Ub),this.totalsGrid.initialColumnsToSkip=1,this.currencyGrid.uniqueColumn="ccy",this.totalsGrid.selectable=!1,this.currencyGrid.columnWidthChanged.subscribe(function(e){t.resizeGrids(e)}),this.currencyGrid.columnOrderChanged.subscribe(function(e){t.resizeGrids(e)}),u.a.fromEvent(document.body,"click").subscribe(function(e){t.positionX=e.clientX,t.positionY=e.clientY}),this.currencyGrid.listenHorizontalScrollEvent=!0,this.totalsGrid.fireHorizontalScrollEvent=!0,this.totalsGrid.lockedColumnCount=2,this.currencyGrid.lockedColumnCount=2,this.initializeMenus(),this.optionsButton.enabled=!1,this.dateFormat=a.x.call("eval","dateFormat").toLowerCase(),this.systemDate=a.x.call("eval","dbDate"),"dd/mm/yyyy"==this.dateFormat?this.fromDate.toolTip=a.x.call("getBundle","tip","datefromDDMMYY","Enter value date ('dd/mm/yyyy')"):this.fromDate.toolTip=a.x.call("getBundle","tip","datefromMMDDYY","Enter value date ('dd/mm/yyyy')"),this.showDays.toolTip=a.x.call("getBundle","tip","showdays","Number of days to show"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.actionPath="currmonitorNew.do?",this.actionMethod="method=unspecified",this.currencyGrid.ITEM_CLICK.subscribe(function(e){t.itemClickFunction(e),t.cellLogic(e)}),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.systemDate=this.systemDate,this.inputData.send(this.requestParams),a.v.subscribe(function(e){t.export(e)})},e.prototype.resizeGrids=function(t){try{this.totalsGrid.setRefreshColumnWidths(this.currencyGrid.gridObj.getColumns())}catch(e){console.log("resizeGrids",e)}},e.prototype.getParamsFromParent=function(){return{sqlParams:this.lastSelectedTooltipParams,facilityId:this.tooltipFacilityId,selectedNodeId:this.selectedNodeId,treeLevelValue:this.treeLevelValue,tooltipCurrencyCode:this.tooltipCurrencyCode,tooltipEntityId:this.tooltipEntityId,tooltipSelectedDate:this.tooltipSelectedDate}},e.prototype.createTooltip=function(){var t=this;this.customTooltip&&this.customTooltip.close&&this.removeTooltip();try{this.customTooltip=a.Eb.createPopUp(parent,a.u,{}),this.customTooltip.enableResize=!1,this.customTooltip.width="410",this.customTooltip.height="450",this.customTooltip.enableResize=!1,this.customTooltip.title="Alert Summary Tooltip",this.customTooltip.showControls=!0,window.innerHeight<this.positionY+450&&(this.positionY=200),this.customTooltip.setWindowXY(this.positionX+20,this.positionY),this.customTooltip.showHeader=!1,this.customTooltip.parentDocument=this,this.customTooltip.processBox=this,this.customTooltip.display(),setTimeout(function(){t.eventsCreated||t.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe(function(e){t.lastSelectedTooltipParams=e.noode.data,a.x.call("openAlertInstanceSummary","openAlertInstSummary",t.selectedNodeId,t.treeLevelValue)})},0),setTimeout(function(){t.eventsCreated||t.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe(function(e){t.getScenarioFacility(e.noode.data.scenario_id),t.lastSelectedTooltipParams=e.noode.data,t.hostId=e.hostId,t.entityId=t.lastSelectedTooltipParams.ENTITY,t.currencyId=t.lastSelectedTooltipParams.CCY})},0),setTimeout(function(){t.eventsCreated||t.customTooltip.getChild().ITEM_CLICK.subscribe(function(e){t.selectedNodeId=e.noode.data.id,t.treeLevelValue=e.noode.data.treeLevelValue,t.customTooltip.getChild().linkToSpecificButton.enabled=!1})},0)}catch(e){console.log("SwtCommonGrid -> createTooltip -> error",e)}},e.prototype.getScenarioFacility=function(t){var e=this;this.requestParams=[],this.alertingData.cbStart=this.startOfComms.bind(this),this.alertingData.cbStop=this.endOfComms.bind(this),this.alertingData.cbFault=this.inputDataFault.bind(this),this.alertingData.encodeURL=!1,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getScenarioFacility",this.requestParams.scenarioId=t,this.alertingData.url=this.baseURL+this.actionPath+this.actionMethod,this.alertingData.cbResult=function(t){e.openGoToScreen(t)},this.alertingData.send(this.requestParams)},e.prototype.openGoToScreen=function(t){if(t&&t.ScenarioSummary&&t.ScenarioSummary.scenarioFacility){var e=t.ScenarioSummary.scenarioFacility,i=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.entity_id?this.lastSelectedTooltipParams.entity_id:this.tooltipEntityId,o=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.currency_code?this.lastSelectedTooltipParams.currency_code:this.tooltipCurrencyCode,l=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.match_id?this.lastSelectedTooltipParams.match_id:null,n=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.movement_id?this.lastSelectedTooltipParams.movement_id:null,r=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.sweep_id?this.lastSelectedTooltipParams.sweep_id:null;a.x.call("goTo",e,this.hostId,i,l,o,n,r,"")}},e.prototype.removeTooltip=function(){null!=this.customTooltip&&this.customTooltip.close()},e.prototype.itemClickFunction=function(t){var e=this;null==t.target||null==t.target.field||"alerting"!=t.target.field||"C"!=t.target.data.alerting&&"Y"!=t.target.data.alerting?this.removeTooltip():(this.tooltipCurrencyCode=t.target.data.ccy,this.tooltipEntityId=this.entityCombo.selectedLabel,this.tooltipFacilityId="CURRENCY_MONITOR_CCY_ROW",this.tooltipSelectedDate=null,this.tooltipOtherParams=[],setTimeout(function(){e.createTooltip()},100))},e.prototype.cellLogic=function(t){try{if(null!=t){var e=t.target.field,i=t.target.data.slickgrid_rowcontent[e]?t.target.data.slickgrid_rowcontent[e].clickable:"",o=t.target.data.ccy.substring(0,3);if(i){this.d1=new Date(this.fromDate.selectedDate),this.d2=new Date(this.fromDate.selectedDate),this.d2.setDate(this.d2.getDate()+parseInt(this.showDays.text)-1);var l="";if("dd/mm/yyyy"===this.dateFormat.toLowerCase()?(l=a.j.formatDate(this.d1,"DD/MM/YYYY"),a.j.formatDate(this.d2,"DD/MM/YYYY")):(l=a.j.formatDate(this.d1,"MM/DD/YYYY"),a.j.formatDate(this.d2,"MM/DD/YYYY")),"loro"==e)this.clickLoro(this.entityCombo.selectedLabel,o,l);else{var n=t.target.data.slickgrid_rowcontent[e].date;this.clickLink(this.entityCombo.selectedLabel,o,n,this.breakdown.selectedValue.toString())}}}}catch(t){console.log("e",t)}},e.prototype.clickLoro=function(t,e,i){a.x.call("clickLoro",t,e,i)},e.prototype.clickLink=function(t,e,i,o){a.x.call("clickLink",t,e,i,o)},e.prototype.startAutoRefresh=function(t){"Y"==t&&(this.updateData("no",!0),this.autoRefresh.start())},e.prototype.updateDatafromRefresh=function(t){var e=this;"showDays"==Object(a.ic.getFocus()).id||"startDate"==Object(a.ic.getFocus()).id?setTimeout(function(){e.updateData("yes")},0):"showDays"!=Object(a.ic.getFocus()).id&&"startDate"!=Object(a.ic.getFocus()).id&&setTimeout(function(){e.updateData("yes",!0)},0)},e.prototype.dataRefresh=function(t){var e=this;this.refreshStatus="Y",this.comboOpen||this.datePickerOpen||(("showDays"==Object(a.ic.getFocus()).id||"fromDate"==Object(a.ic.getFocus()).id)&&this.validateDate(null,this.fromDate)&&this.validateNumberOfDays(this.showDays)?(this.showDays.text!=this.prevNumberOfDays||this.fromDate.selectedDate.toDateString()!=this.prevChosenDate.toDateString()?setTimeout(function(){e.updateData("yes")},0):setTimeout(function(){e.updateData("yes",!0)},0),this.refreshButton.setFocus()):"showDays"!=Object(a.ic.getFocus()).id&&"fromDate"!=Object(a.ic.getFocus()).id&&this.validateDate(null,this.fromDate)&&this.validateNumberOfDays(this.showDays)&&this.updateData("yes",!0)),this.autoRefresh.stop()},e.prototype.updateData=function(t,e,i){void 0===e&&(e=!1),void 0===i&&(i=!1),this.d1=new Date(this.fromDate.selectedDate),this.d2=new Date(this.fromDate.selectedDate),this.d2.setDate(this.d2.getDate()+parseInt(this.showDays.text)-1);var o=s()(this.fromDate.text,this.dateFormat.toUpperCase()),l=o.add(Number(this.showDays.text)-1,"days");if(i)return this.fromDate.selectedDate=this.prevChosenDate,this.d1=this.prevChosenDate,this.showDays.text=this.prevNumberOfDays,this.d2.setDate(this.d2.getDate()+parseInt(this.showDays.text)-1),void this.showDays.setFocus();if(e||!this.checkDateRange(t,o,l,this.showDays,this.systemDate,this.dateFormat,this.updateData)){this.prevChosenDate=this.d1,this.requestParams=[];var n="",r="",u=null,d=this.ccyCombo.selectedItem.content,c=this.entityCombo.selectedItem.content;if(null==t)this.dateFlag="fromDatechange";else{this.dateFlag="toDatechange";Math.round((this.d2.getTime()-this.d1.getTime())/864e5+1)>14&&this.swtAlert.show("To Date is not on default days range","Warning")}this.d1&&this.d2&&("dd/mm/yyyy"===this.dateFormat.toLowerCase()?(n=a.j.formatDate(this.d1,"DD/MM/YYYY"),r=a.j.formatDate(this.d2,"DD/MM/YYYY"),u=/^(0[1-9]|[12][0-9]|3[01]|[1-9])\/(0[1-9]|1[012]|[1-9])\/(\d{4}|\d{2})$/):(n=a.j.formatDate(this.d1,"MM/DD/YYYY"),r=a.j.formatDate(this.d2,"MM/DD/YYYY"),u=/^(0[1-9]|1[012]|[1-9])\/(0[1-9]|[12][0-9]|3[01]|[1-9])\/(\d{4}|\d{2})$/),"true"==u.test(this.fromDate.text).toString()&&(this.requestParams["currencyMonitor.fromDateAsString"]=n,this.requestParams["currencyMonitor.toDateAsString"]=r,null!=t&&(this.requestParams.autoRefresh=t),this.requestParams.systemDate=this.systemDate,this.requestParams.dateFlag=this.dateFlag,this.requestParams.defaultDays=14)),this.requestParams["currencyMonitor.entityId"]=c,this.requestParams["currencyMonitor.currGrp"]=d,this.inputData.send(this.requestParams)}},e.prototype.validateDate=function(t,e){var i=this;try{var o=void 0,l=a.Wb.getPredictMessage("alert.enterValidDate",null);if(!e.text)return this.swtAlert.error(l,null,null,null,function(){i.setFocusDateField(e)}),this.autoRefresh.stop(),!1;if(!(o=s()(e.text,this.dateFormat.toUpperCase(),!0)).isValid())return this.swtAlert.error(l,null,null,null,function(){i.setFocusDateField(e)}),this.autoRefresh.stop(),!1;e.selectedDate=o.toDate()}catch(n){console.log("error in validateDate",n)}return!0},e.prototype.setFocusDateField=function(t){t.setFocus(),t.text=this.jsonReader.getScreenAttributes().from},e.prototype.formatDate=function(t,e){return e&&t?"dd/mm/yyyy"===e.toLowerCase()?t.getDate()+"/"+(t.getMonth()+1)+"/"+t.getFullYear():t.getMonth()+1+"/"+t.getDate()+"/"+t.getFullYear():""},e.prototype.validateNumberOfDays=function(t){var e=parseInt(t.text);return!(isNaN(e)||e<=0)||(this.showAlertForNumberOfDays(t),!1)},e.prototype.showAlertForNumberOfDays=function(t){this.swtAlert.show(" 'Show' value must be between 1 and 14. ","Error",a.c.OK,null,this.showDayFocus.bind(this))},e.prototype.showDayFocus=function(t){t.detail==a.c.OK&&(this.showDays.setFocusAndSelect(),null!=this.autoRefresh&&this.autoRefresh.stop())},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),1==this.showBuildInProgress?this.dataBuildingText.visible=!0:this.dataBuildingText.visible=!1,this.disableInterface(),this.fromDate.enabled=!1,this.showDays.enabled=!1},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableInterface(),this.fromDate.enabled=!0,this.showDays.enabled=!0},e.prototype.disableInterface=function(){this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1},e.prototype.enableInterface=function(){this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0},e.prototype.showAlertCurrencyAccess=function(){this.swtAlert.show(a.x.call("getBundle","text","alert.currencyAccess","Invalid: your role does not specify access to currencies/groups for this entity"),a.x.call("getBundle","text","alert-error","Error"))},e.prototype.inputDataResult=function(t){var e,i,o=this,l=null;this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON);if(this.lostConnectionText.visible=!1,this.jsonReader.getRequestReplyStatus()){if(JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)){this.fromDate.formatString=this.dateFormat,this.optionsButton.enabled||(this.optionsButton.enabled=!0),this.refreshRate=parseInt(this.jsonReader.getRefreshRate()),this.systemDate=this.jsonReader.getScreenAttributes().sysDateFrmSession,this.dateCompare=this.jsonReader.getScreenAttributes().dateComparing;var n=this.jsonReader.getScreenAttributes().lastRefTime;n=this.convertFromUnicodeToString(n),this.lastRefTime.text=n,this.currencyPattern=this.jsonReader.getScreenAttributes().currencyFormat,!0,this.fromDate.showToday=!1,this.d1=s()(this.jsonReader.getScreenAttributes().from,this.dateFormat.toUpperCase()).toDate(),this.prevChosenDate=this.d1,this.d2=s()(this.jsonReader.getScreenAttributes().to,this.dateFormat.toUpperCase()).toDate(),this.fromDate.selectedDate=this.d1;if(this.showDays.text=Math.round((this.d2.getTime()-this.d1.getTime())/864e5+1).toString(),this.prevNumberOfDays=this.showDays.text,this.updateDayLabel(),this.entityCombo.setComboData(this.jsonReader.getSelects(),!1),this.selectedEntity.text=this.entityCombo.selectedValue,"All"==this.entityCombo.selectedItem?(this.ccyCombo.selectedItem="All",this.selectedCcy.text="All",this.ccyCombo.enabled=!1):(this.ccyCombo.setComboData(this.jsonReader.getSelects(),!1),this.selectedCcy.text=this.ccyCombo.selectedValue,this.ccyCombo.enabled=!0),""==a.Z.trim(this.ccyCombo.selectedLabel)&&(null!=this.autoRefresh&&this.autoRefresh.stop(),this.comboOpen=!0,setTimeout(function(){o.showAlertCurrencyAccess()},0)),a.Z.isTrue(this.jsonReader.getScreenAttributes().databuilding))this.dataBuildingText.visible=!0;else{this.dataBuildingText.visible=!1,l=this.jsonReader.getColumnData();for(var r=0;r<l.column.length;r++)e=a.Wb.getAMLMessages(l.column[r].heading),l.column[r].heading=e;i={columns:l},this.currencyGrid.hideHorizontalScrollBar=!0,this.currencyGrid.screenID="",this.currencyGrid.componentID="13",this.currencyGrid.currencyFormat=this.currencyPattern,this.currencyGrid.CustomGrid(t.currencymonitor.grid.metadata),this.totalsGrid.CustomGrid(i),this.jsonReader.getRowSize()<1?this.dataExport.enabled=!1:this.dataExport.enabled=!0,this.currentFontSize=this.jsonReader.getScreenAttributes().currfontsize;for(r=0;r<this.currencyGrid.columnDefinitions.length;r++){var u=this.currencyGrid.columnDefinitions[r];if("alerting"==u.field){var c="./"+a.x.call("eval","alertOrangeImage"),h="./"+a.x.call("eval","alertRedImage");"Normal"==this.currentFontSize?u.properties={enabled:!1,columnName:"alerting",imageEnabled:c,imageCritEnabled:h,imageDisabled:"",_toolTipFlag:!0,style:" display: block; margin-left: auto; margin-right: auto;"}:u.properties={enabled:!1,columnName:"alerting",imageEnabled:c,imageCritEnabled:h,imageDisabled:"",_toolTipFlag:!0,style:"height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;"},this.currencyGrid.columnDefinitions[r].editor=null,this.currencyGrid.columnDefinitions[r].formatter=d.a}}this.currencyGrid.gridData=this.jsonReader.getGridData(),this.currencyGrid.setRowSize=this.jsonReader.getRowSize(),this.currencyGrid.colWidthURL(this.baseURL+"currmonitorNew.do?"),this.currencyGrid.saveWidths=!0,this.totalsGrid.gridData=this.jsonReader.getTotalsData(),this.currencyGrid.entityID=this.entityCombo.selectedLabel,"Normal"==this.currentFontSize?(this.currencyGrid.styleName="dataGridNormal",this.currencyGrid.rowHeight=18,this.totalsGrid.styleName="dataGridNormal",this.totalsGrid.rowHeight=18):"Small"==this.currentFontSize&&(this.currencyGrid.styleName="dataGridSmall",this.currencyGrid.rowHeight=15,this.totalsGrid.styleName="dataGridSmall",this.totalsGrid.rowHeight=15)}this.showBuildInProgress=!1}null==this.autoRefresh?(this.autoRefresh=new a.cc(1e3*this.refreshRate,0),this.autoRefresh.addEventListener("timer",this.dataRefresh.bind(this))):this.autoRefresh.delay(1e3*this.refreshRate),this.prevRecievedJSON=this.lastRecievedJSON}null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())},e.prototype.convertFromUnicodeToString=function(t){return t=t.replace(/\\u([\d\w]{4})/gi,function(t,e){return String.fromCharCode(parseInt(e,16))}),t=unescape(t)},e.prototype.inputDataFault=function(t){try{this.lostConnectionText.visible=!0,this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.error(t.fault.faultstring+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail),null!=this.autoRefresh&&(this.autoRefresh.running||this.autoRefresh.start())}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","inputDataFault",this.errorLocation)}},e.prototype.export=function(t){var e=[];new Object;e.push("Entity="+this.entityCombo.selectedLabel),e.push("Ccy="+this.ccyCombo.selectedLabel),this.d1=this.fromDate.selectedDate,this.d2=new Date(this.fromDate.selectedDate),this.d2.setDate(this.d2.getDate()+parseInt(this.showDays.text)-1);var i="",o="";"dd/mm/yyyy"==this.dateFormat.toLowerCase()?(i=a.j.formatDate(this.d1,"DD/MM/YYYY"),o=a.j.formatDate(this.d2,"DD/MM/YYYY")):(i=a.j.formatDate(this.d1,"MM/DD/YYYY"),o=a.j.formatDate(this.d2,"MM/DD/YYYY")),e.push("From Date="+i),e.push("To Date="+o),this.dataExport.convertData(this.lastRecievedJSON.currencymonitor.grid.metadata.columns,this.currencyGrid,this.totalsGrid.gridData,e,t,!0)},e.prototype.openedCombo=function(t){this.inputData.isBusy()&&(this.enableInterface(),this.inputData.cancel())},e.prototype.closedCombo=function(t){var e=this;this.comboOpen=!1,this.datePickerOpen=!1,0==this.comboChange&&this.validateDate(null,this.fromDate)&&this.validateNumberOfDays(this.showDays)&&(this.showDays.text!=this.prevNumberOfDays||this.fromDate.selectedDate.toDateString()!=this.prevChosenDate.toDateString()?setTimeout(function(){e.updateData("no")},0):setTimeout(function(){e.updateData("no",!0)},0)),this.comboChange=!1},e.prototype.changeCombo=function(t){var e=this;this.comboChange=!0,this.refreshButton.enabled=!1,setTimeout(function(){e.updateData("no",!0)},0)},e.prototype.validateStartDate=function(t,e){try{this.dateChanged=!0,"focusOut"===e?"closeButton"==Object(a.ic.getFocus()).id?this.closeHandler():this.validateDate(null,this.fromDate)&&(this.showDays.text==this.prevNumberOfDays&&this.fromDate.selectedDate.toDateString()==this.prevChosenDate.toDateString()||this.showDays.setFocus()):"change"===e&&this.validateDate(null,this.fromDate)&&this.showDays.setFocusAndSelect()}catch(t){console.log("error in ",t)}},e.prototype.keyDownInNumberOfDays=function(t){var e=this;13==t.keyCode&&this.validateNumberOfDays(this.showDays)&&this.validateDate(null,this.fromDate)&&(this.showDays.text!=this.prevNumberOfDays||this.fromDate.selectedDate.toDateString()!=this.prevChosenDate.toDateString()?setTimeout(function(){e.updateData("no")},0):setTimeout(function(){e.updateData("no",!0)},0),this.refreshButton.setFocus())},e.prototype.validateShowDaysValue=function(t){var e=this;try{var i=s()(this.fromDate.text,this.dateFormat.toUpperCase()),o=s()(this.prevChosenDate,this.dateFormat.toUpperCase());this.updateDayLabel();var l=""+this.showDays.text;(""==l||0!=l.indexOf("0")&&""!=l||0==l.indexOf("0")&&1==l.indexOf("0",1)||0==l.indexOf("0")&&-1==l.indexOf("0",1))&&this.validateNumberOfDays(this.showDays)&&this.validateDate(t,this.fromDate)&&(l==this.prevNumberOfDays&&0==i.diff(o)||(setTimeout(function(){e.updateData("no")},0),this.autoRefresh.stop())),this.datePickerOpen=!1}catch(n){console.log("errior in focus oyut",n)}},e.prototype.optionHandler=function(){a.x.call("openOptionsWindow"),null!=this.autoRefresh&&(this.autoRefresh.stop(),this.refreshStatus="N")},e.prototype.closeHandler=function(){a.x.call("close")},e.prototype.connError=function(t){this.swtAlert.show(""+this.invalidComms,a.x.call("getBundle","text","alert-error","Error"))},e.prototype.checkDateRange=function(t,e,i,o,l,n,r){var u=this;void 0===r&&(r=null);var d=!1,c=a.x.call("eval","nDaysPriorToToday"),h=s()(l,this.dateFormat.toUpperCase()).subtract(c,"days"),b=a.x.call("eval","nDaysAheadToToday"),m=s()(l,this.dateFormat.toUpperCase()).add(b,"days");if(e.diff(h)<0||i.diff(m)>0){d=!0;this.swtAlert.confirm("The data for this date range selection may not be available <br/>in the cache and will take time to be calculated. Do you want to continue?","",a.c.OK|a.c.CANCEL,null,function(e){u.checkDateRangeListener(e,t)},a.c.CANCEL)}return d},e.prototype.checkDateRangeListener=function(t,e){try{t.detail==a.c.OK?(this.showBuildInProgress=!0,this.updateData(e,!0)):this.updateData(e,!0,!0)}catch(i){}},e.prototype.calculateDays=function(t,e){return(e.getTime()-t.getTime())/864e5},e.prototype.updateDayLabel=function(){0==parseInt(this.showDays.text)||1==parseInt(this.showDays.text)?this.daysLabel.text=a.x.call("getBundle","text","day","day"):this.daysLabel.text=a.x.call("getBundle","text","days","days")},e.prototype.doHelp=function(){a.x.call("help")},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,"Currency Monitor",this.versionNumber,"");var t=new a.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=a.Eb.createPopUp(this,a.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.isModal=!0,this.showJSONPopup.display()},e}(a.yb)),h=[{path:"",component:c}],b=(n.l.forChild(h),function(){return function(){}}()),m=i("pMnS"),p=i("RChO"),g=i("t6HQ"),f=i("WFGK"),y=i("5FqG"),D=i("Ip0R"),w=i("gIcY"),R=i("t/Na"),v=i("sE5F"),C=i("OzfB"),I=i("T7CS"),S=i("S7LP"),x=i("6aHO"),T=i("WzUx"),L=i("A7o+"),k=i("zCE2"),B=i("Jg5P"),N=i("3R0m"),G=i("hhbb"),O=i("5rxC"),A=i("Fzqc"),M=i("21Lb"),F=i("hUWP"),J=i("3pJQ"),P=i("V9q+"),Y=i("VDKW"),E=i("kXfT"),_=i("BGbe");i.d(e,"CurrencyMonitorModuleNgFactory",function(){return W}),i.d(e,"RenderType_CurrencyMonitor",function(){return z}),i.d(e,"View_CurrencyMonitor_0",function(){return U}),i.d(e,"View_CurrencyMonitor_Host_0",function(){return H}),i.d(e,"CurrencyMonitorNgFactory",function(){return V});var W=o.Gb(b,[],function(t){return o.Qb([o.Rb(512,o.n,o.vb,[[8,[m.a,p.a,g.a,f.a,y.Cb,y.Pb,y.r,y.rc,y.s,y.Ab,y.Bb,y.Db,y.qd,y.Hb,y.k,y.Ib,y.Nb,y.Ub,y.yb,y.Jb,y.v,y.A,y.e,y.c,y.g,y.d,y.Kb,y.f,y.ec,y.Wb,y.bc,y.ac,y.sc,y.fc,y.lc,y.jc,y.Eb,y.Fb,y.mc,y.Lb,y.nc,y.Mb,y.dc,y.Rb,y.b,y.ic,y.Yb,y.Sb,y.kc,y.y,y.Qb,y.cc,y.hc,y.pc,y.oc,y.xb,y.p,y.q,y.o,y.h,y.j,y.w,y.Zb,y.i,y.m,y.Vb,y.Ob,y.Gb,y.Xb,y.t,y.tc,y.zb,y.n,y.qc,y.a,y.z,y.rd,y.sd,y.x,y.td,y.gc,y.l,y.u,y.ud,y.Tb,V]],[3,o.n],o.J]),o.Rb(4608,D.m,D.l,[o.F,[2,D.u]]),o.Rb(4608,w.c,w.c,[]),o.Rb(4608,w.p,w.p,[]),o.Rb(4608,R.j,R.p,[D.c,o.O,R.n]),o.Rb(4608,R.q,R.q,[R.j,R.o]),o.Rb(5120,R.a,function(t){return[t,new a.tb]},[R.q]),o.Rb(4608,R.m,R.m,[]),o.Rb(6144,R.k,null,[R.m]),o.Rb(4608,R.i,R.i,[R.k]),o.Rb(6144,R.b,null,[R.i]),o.Rb(4608,R.f,R.l,[R.b,o.B]),o.Rb(4608,R.c,R.c,[R.f]),o.Rb(4608,v.c,v.c,[]),o.Rb(4608,v.g,v.b,[]),o.Rb(5120,v.i,v.j,[]),o.Rb(4608,v.h,v.h,[v.c,v.g,v.i]),o.Rb(4608,v.f,v.a,[]),o.Rb(5120,v.d,v.k,[v.h,v.f]),o.Rb(5120,o.b,function(t,e){return[C.j(t,e)]},[D.c,o.O]),o.Rb(4608,I.a,I.a,[]),o.Rb(4608,S.a,S.a,[]),o.Rb(4608,x.a,x.a,[o.n,o.L,o.B,S.a,o.g]),o.Rb(4608,T.c,T.c,[o.n,o.g,o.B]),o.Rb(4608,T.e,T.e,[T.c]),o.Rb(4608,L.l,L.l,[]),o.Rb(4608,L.h,L.g,[]),o.Rb(4608,L.c,L.f,[]),o.Rb(4608,L.j,L.d,[]),o.Rb(4608,L.b,L.a,[]),o.Rb(4608,L.k,L.k,[L.l,L.h,L.c,L.j,L.b,L.m,L.n]),o.Rb(4608,T.i,T.i,[[2,L.k]]),o.Rb(4608,T.r,T.r,[T.L,[2,L.k],T.i]),o.Rb(4608,T.t,T.t,[]),o.Rb(4608,T.w,T.w,[]),o.Rb(1073742336,n.l,n.l,[[2,n.r],[2,n.k]]),o.Rb(1073742336,D.b,D.b,[]),o.Rb(1073742336,w.n,w.n,[]),o.Rb(1073742336,w.l,w.l,[]),o.Rb(1073742336,k.a,k.a,[]),o.Rb(1073742336,B.a,B.a,[]),o.Rb(1073742336,w.e,w.e,[]),o.Rb(1073742336,N.a,N.a,[]),o.Rb(1073742336,L.i,L.i,[]),o.Rb(1073742336,T.b,T.b,[]),o.Rb(1073742336,R.e,R.e,[]),o.Rb(1073742336,R.d,R.d,[]),o.Rb(1073742336,v.e,v.e,[]),o.Rb(1073742336,G.b,G.b,[]),o.Rb(1073742336,O.b,O.b,[]),o.Rb(1073742336,C.c,C.c,[]),o.Rb(1073742336,A.a,A.a,[]),o.Rb(1073742336,M.d,M.d,[]),o.Rb(1073742336,F.c,F.c,[]),o.Rb(1073742336,J.a,J.a,[]),o.Rb(1073742336,P.a,P.a,[[2,C.g],o.O]),o.Rb(1073742336,Y.b,Y.b,[]),o.Rb(1073742336,E.a,E.a,[]),o.Rb(1073742336,_.b,_.b,[]),o.Rb(1073742336,a.Tb,a.Tb,[]),o.Rb(1073742336,b,b,[]),o.Rb(256,R.n,"XSRF-TOKEN",[]),o.Rb(256,R.o,"X-XSRF-TOKEN",[]),o.Rb(256,"config",{},[]),o.Rb(256,L.m,void 0,[]),o.Rb(256,L.n,void 0,[]),o.Rb(256,"popperDefaults",{},[]),o.Rb(1024,n.i,function(){return[[{path:"",component:c}]]},[])])}),j=[[".canvasWithGreyBorder{border:1px solid #696969!important}"]],z=o.Hb({encapsulation:2,styles:j,data:{}});function U(t){return o.dc(0,[o.Zb(*********,1,{_container:0}),o.Zb(*********,2,{refreshButton:0}),o.Zb(*********,3,{optionsButton:0}),o.Zb(*********,4,{closeButton:0}),o.Zb(*********,5,{ccyCombo:0}),o.Zb(*********,6,{entityCombo:0}),o.Zb(*********,7,{fromDate:0}),o.Zb(*********,8,{showDays:0}),o.Zb(*********,9,{loadingImage:0}),o.Zb(*********,10,{dataBuildingText:0}),o.Zb(*********,11,{lostConnectionText:0}),o.Zb(*********,12,{lastRefTime:0}),o.Zb(*********,13,{selectedEntity:0}),o.Zb(*********,14,{selectedCcy:0}),o.Zb(*********,15,{daysLabel:0}),o.Zb(*********,16,{totalsContainer:0}),o.Zb(*********,17,{displaycontainer:0}),o.Zb(*********,18,{dataExport:0}),o.Zb(*********,19,{breakdown:0}),o.Zb(*********,20,{accountRadio:0}),o.Zb(*********,21,{movementRadio:0}),o.Zb(*********,22,{bookRadio:0}),o.Zb(*********,23,{meatagroupRadio:0}),o.Zb(*********,24,{groupRadio:0}),(t()(),o.Jb(24,0,null,null,121,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var o=!0,l=t.component;"creationComplete"===e&&(o=!1!==l.onLoad()&&o);return o},y.ad,y.hb)),o.Ib(25,4440064,null,0,a.yb,[o.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),o.Jb(26,0,null,0,119,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(27,4440064,null,0,a.ec,[o.r,a.i,o.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),o.Jb(28,0,null,0,78,"SwtCanvas",[["height","58"],["minWidth","900"],["width","100%"]],null,null,null,y.Nc,y.U)),o.Ib(29,4440064,null,0,a.db,[o.r,a.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),o.Jb(30,0,null,0,76,"Grid",[["height","100%"],["paddingLeft","5"],["width","100%"]],null,null,null,y.Cc,y.H)),o.Ib(31,4440064,null,0,a.z,[o.r,a.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),o.Jb(32,0,null,0,37,"GridRow",[["height","50%"],["width","100%"]],null,null,null,y.Bc,y.J)),o.Ib(33,4440064,null,0,a.B,[o.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(34,0,null,0,13,"GridItem",[["width","65%"]],null,null,null,y.Ac,y.I)),o.Ib(35,4440064,null,0,a.A,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(36,0,null,0,3,"GridItem",[["width","115"]],null,null,null,y.Ac,y.I)),o.Ib(37,4440064,null,0,a.A,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(38,0,null,0,1,"SwtLabel",[["textDictionaryId","entity.id"]],null,null,null,y.Yc,y.fb)),o.Ib(39,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),o.Jb(40,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(41,4440064,null,0,a.A,[o.r,a.i],null,null),(t()(),o.Jb(42,0,null,0,1,"SwtComboBox",[["dataLabel","entity"],["id","entityCombo"],["width","180"]],null,[[null,"change"],[null,"open"],[null,"close"],["window","mousewheel"]],function(t,e,i){var l=!0,n=t.component;"window:mousewheel"===e&&(l=!1!==o.Tb(t,43).mouseWeelEventHandler(i.target)&&l);"change"===e&&(l=!1!==n.changeCombo(i)&&l);"open"===e&&(l=!1!==n.openedCombo(i)&&l);"close"===e&&(l=!1!==n.closedCombo(i)&&l);return l},y.Pc,y.W)),o.Ib(43,4440064,[[6,4],["entityCombo",4]],0,a.gb,[o.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),o.Jb(44,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(45,4440064,null,0,a.A,[o.r,a.i],null,null),(t()(),o.Jb(46,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["paddingLeft","10"],["text"," "]],null,null,null,y.Yc,y.fb)),o.Ib(47,4440064,[[13,4],["selectedEntity",4]],0,a.vb,[o.r,a.i],{paddingLeft:[0,"paddingLeft"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),o.Jb(48,0,null,0,21,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(49,4440064,null,0,a.A,[o.r,a.i],null,null),(t()(),o.Jb(50,0,null,0,3,"GridItem",[["width","80"]],null,null,null,y.Ac,y.I)),o.Ib(51,4440064,null,0,a.A,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(52,0,null,0,1,"SwtLabel",[["textDictionaryId","currencyInterest.fromDate"]],null,null,null,y.Yc,y.fb)),o.Ib(53,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),o.Jb(54,0,null,0,3,"GridItem",[["minWidth","110"]],null,null,null,y.Ac,y.I)),o.Ib(55,4440064,null,0,a.A,[o.r,a.i],{minWidth:[0,"minWidth"]},null),(t()(),o.Jb(56,0,null,0,1,"SwtDateField",[["id","fromDate"],["toolTip","Enter start date (if applicable)"],["width","70"]],null,[[null,"open"],[null,"change"]],function(t,e,i){var o=!0,l=t.component;"open"===e&&(o=!1!==l.openedCombo(i)&&o);"change"===e&&(o=!1!==l.validateStartDate(i,"change")&&o);return o},y.Tc,y.ab)),o.Ib(57,4308992,[[7,4],["fromDate",4]],0,a.lb,[o.r,a.i,o.T],{toolTip:[0,"toolTip"],id:[1,"id"],width:[2,"width"]},{openEventOutPut:"open",changeEventOutPut:"change"}),(t()(),o.Jb(58,0,null,0,3,"GridItem",[["marginLeft","40"],["width","40"]],null,null,null,y.Ac,y.I)),o.Ib(59,4440064,null,0,a.A,[o.r,a.i],{width:[0,"width"],marginLeft:[1,"marginLeft"]},null),(t()(),o.Jb(60,0,null,0,1,"SwtLabel",[["textDictionaryId","text.showdays"]],null,null,null,y.Yc,y.fb)),o.Ib(61,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),o.Jb(62,0,null,0,7,"GridItem",[["marginLeft","10"]],null,null,null,y.Ac,y.I)),o.Ib(63,4440064,null,0,a.A,[o.r,a.i],{marginLeft:[0,"marginLeft"]},null),(t()(),o.Jb(64,0,null,0,1,"SwtNumericInput",[["id","showDays"],["maxChars","2"],["width","30"]],null,[[null,"keypress"],[null,"focusOut"]],function(t,e,i){var o=!0,l=t.component;"keypress"===e&&(o=!1!==l.keyDownInNumberOfDays(i)&&o);"focusOut"===e&&(o=!1!==l.validateShowDaysValue(i)&&o);return o},y.cd,y.jb)),o.Ib(65,4440064,[[8,4],["showDays",4]],0,a.Ab,[o.r,a.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},{onFocusOut_:"focusOut"}),(t()(),o.Jb(66,0,null,0,3,"GridItem",[["marginLeft","10"]],null,null,null,y.Ac,y.I)),o.Ib(67,4440064,null,0,a.A,[o.r,a.i],{marginLeft:[0,"marginLeft"]},null),(t()(),o.Jb(68,0,null,0,1,"SwtLabel",[["id","daysLabel"],["textDictionaryId","text.day"]],null,null,null,y.Yc,y.fb)),o.Ib(69,4440064,[[15,4],["daysLabel",4]],0,a.vb,[o.r,a.i],{id:[0,"id"],textDictionaryId:[1,"textDictionaryId"]},null),(t()(),o.Jb(70,0,null,0,36,"GridRow",[["height","50%"],["width","100%"]],null,null,null,y.Bc,y.J)),o.Ib(71,4440064,null,0,a.B,[o.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),o.Jb(72,0,null,0,13,"GridItem",[["width","65%"]],null,null,null,y.Ac,y.I)),o.Ib(73,4440064,null,0,a.A,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(74,0,null,0,3,"GridItem",[["width","115"]],null,null,null,y.Ac,y.I)),o.Ib(75,4440064,null,0,a.A,[o.r,a.i],{width:[0,"width"]},null),(t()(),o.Jb(76,0,null,0,1,"SwtLabel",[["textDictionaryId","currency.group"]],null,null,null,y.Yc,y.fb)),o.Ib(77,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"]},null),(t()(),o.Jb(78,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(79,4440064,null,0,a.A,[o.r,a.i],null,null),(t()(),o.Jb(80,0,null,0,1,"SwtComboBox",[["dataLabel","currencygroup"],["id","ccyCombo"],["width","180"]],null,[[null,"change"],[null,"open"],[null,"close"],["window","mousewheel"]],function(t,e,i){var l=!0,n=t.component;"window:mousewheel"===e&&(l=!1!==o.Tb(t,81).mouseWeelEventHandler(i.target)&&l);"change"===e&&(l=!1!==n.changeCombo(i)&&l);"open"===e&&(l=!1!==n.openedCombo(i)&&l);"close"===e&&(l=!1!==n.closedCombo(i)&&l);return l},y.Pc,y.W)),o.Ib(81,4440064,[[5,4],["ccyCombo",4]],0,a.gb,[o.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{open_:"open",close_:"close",change_:"change"}),(t()(),o.Jb(82,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(83,4440064,null,0,a.A,[o.r,a.i],null,null),(t()(),o.Jb(84,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["paddingLeft","10"]],null,null,null,y.Yc,y.fb)),o.Ib(85,4440064,[[14,4],["selectedCcy",4]],0,a.vb,[o.r,a.i],{paddingLeft:[0,"paddingLeft"],fontWeight:[1,"fontWeight"]},null),(t()(),o.Jb(86,0,null,0,20,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(87,4440064,null,0,a.A,[o.r,a.i],null,null),(t()(),o.Jb(88,0,null,0,3,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(89,4440064,null,0,a.A,[o.r,a.i],null,null),(t()(),o.Jb(90,0,null,0,1,"SwtLabel",[["paddingTop","2"],["textDictionaryId","currMonitor.breakdown"],["width","80"]],null,null,null,y.Yc,y.fb)),o.Ib(91,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"],width:[1,"width"],paddingTop:[2,"paddingTop"]},null),(t()(),o.Jb(92,0,null,0,14,"GridItem",[],null,null,null,y.Ac,y.I)),o.Ib(93,4440064,null,0,a.A,[o.r,a.i],null,null),(t()(),o.Jb(94,0,null,0,12,"SwtRadioButtonGroup",[["align","horizontal"],["id","breakdown"]],null,null,null,y.ed,y.lb)),o.Ib(95,4440064,[[19,4],["breakdown",4]],1,a.Hb,[R.c,o.r,a.i],{id:[0,"id"],align:[1,"align"]},null),o.Zb(*********,25,{radioItems:1}),(t()(),o.Jb(97,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","accountRadio"],["label","Account"],["selected","true"],["value","A"]],null,null,null,y.fd,y.mb)),o.Ib(98,4440064,[[25,4],[20,4],["accountRadio",4]],0,a.Ib,[o.r,a.i],{id:[0,"id"],groupName:[1,"groupName"],label:[2,"label"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),o.Jb(99,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","movementRadio"],["label","Movement"],["value","M"]],null,null,null,y.fd,y.mb)),o.Ib(100,4440064,[[25,4],[21,4],["movementRadio",4]],0,a.Ib,[o.r,a.i],{id:[0,"id"],groupName:[1,"groupName"],label:[2,"label"],value:[3,"value"]},null),(t()(),o.Jb(101,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","bookRadio"],["label","Book"],["value","B"]],null,null,null,y.fd,y.mb)),o.Ib(102,4440064,[[25,4],[22,4],["bookRadio",4]],0,a.Ib,[o.r,a.i],{id:[0,"id"],groupName:[1,"groupName"],label:[2,"label"],value:[3,"value"]},null),(t()(),o.Jb(103,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","groupRadio"],["label","Group"],["value","G"]],null,null,null,y.fd,y.mb)),o.Ib(104,4440064,[[25,4],[24,4],["groupRadio",4]],0,a.Ib,[o.r,a.i],{id:[0,"id"],groupName:[1,"groupName"],label:[2,"label"],value:[3,"value"]},null),(t()(),o.Jb(105,0,null,0,1,"SwtRadioItem",[["groupName","breakdown"],["id","meatagroupRadio"],["label","MetaGroup"],["value","MG"]],null,null,null,y.fd,y.mb)),o.Ib(106,4440064,[[25,4],[23,4],["meatagroupRadio",4]],0,a.Ib,[o.r,a.i],{id:[0,"id"],groupName:[1,"groupName"],label:[2,"label"],value:[3,"value"]},null),(t()(),o.Jb(107,0,null,0,5,"VBox",[["height","100%"],["minWidth","900"],["verticalGap","1"],["width","100%"]],null,null,null,y.od,y.vb)),o.Ib(108,4440064,null,0,a.ec,[o.r,a.i,o.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"]},null),(t()(),o.Jb(109,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","displaycontainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,y.Nc,y.U)),o.Ib(110,4440064,[[17,4],["displaycontainer",4]],0,a.db,[o.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),o.Jb(111,0,null,0,1,"SwtCanvas",[["border","false"],["height","40"],["id","totalsContainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,y.Nc,y.U)),o.Ib(112,4440064,[[16,4],["totalsContainer",4]],0,a.db,[o.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),o.Jb(113,0,null,0,32,"SwtCanvas",[["height","40"],["marginBottom","0"],["minWidth","900"],["width","100%"]],null,null,null,y.Nc,y.U)),o.Ib(114,4440064,null,0,a.db,[o.r,a.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],marginBottom:[3,"marginBottom"]},null),(t()(),o.Jb(115,0,null,0,30,"HBox",[["top","1"],["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(116,4440064,null,0,a.C,[o.r,a.i],{top:[0,"top"],width:[1,"width"]},null),(t()(),o.Jb(117,0,null,0,7,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(118,4440064,null,0,a.C,[o.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),o.Jb(119,0,null,0,1,"SwtButton",[["enabled","false"],["id","refreshButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,l=t.component;"click"===e&&(o=!1!==l.updateDatafromRefresh(i)&&o);return o},y.Mc,y.T)),o.Ib(120,4440064,[[2,4],["refreshButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),o.Jb(121,0,null,0,1,"SwtButton",[["enabled","false"],["id","optionsButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,l=t.component;"click"===e&&(o=!1!==l.optionHandler()&&o);return o},y.Mc,y.T)),o.Ib(122,4440064,[[3,4],["optionsButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),o.Jb(123,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var o=!0,l=t.component;"click"===e&&(o=!1!==l.closeHandler()&&o);return o},y.Mc,y.T)),o.Ib(124,4440064,[[4,4],["closeButton",4]],0,a.cb,[o.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),o.Jb(125,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingRight","10"],["width","100%"]],null,null,null,y.Dc,y.K)),o.Ib(126,4440064,null,0,a.C,[o.r,a.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingRight:[2,"paddingRight"]},null),(t()(),o.Jb(127,0,null,0,1,"SwtLabel",[["color","red"],["height","16"],["id","dataBuildingText"],["right","155"],["text","DATA BUILD IN PROGRESS"],["visible","false"]],null,null,null,y.Yc,y.fb)),o.Ib(128,4440064,[[10,4],["dataBuildingText",4]],0,a.vb,[o.r,a.i],{id:[0,"id"],right:[1,"right"],height:[2,"height"],visible:[3,"visible"],text:[4,"text"],color:[5,"color"]},null),(t()(),o.Jb(129,0,null,0,7,"HBox",[["horizontalAlign","right"],["paddingRight","10"]],null,null,null,y.Dc,y.K)),o.Ib(130,4440064,null,0,a.C,[o.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),o.Jb(131,0,null,0,1,"SwtLabel",[["color","red"],["height","16"],["id","lostConnectionText"],["right","155"],["text","CONNECTION ERROR"],["visible","false"]],null,[[null,"click"]],function(t,e,i){var o=!0,l=t.component;"click"===e&&(o=!1!==l.connError(i)&&o);return o},y.Yc,y.fb)),o.Ib(132,4440064,[[11,4],["lostConnectionText",4]],0,a.vb,[o.r,a.i],{id:[0,"id"],right:[1,"right"],height:[2,"height"],visible:[3,"visible"],text:[4,"text"],color:[5,"color"]},{onClick_:"click"}),(t()(),o.Jb(133,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["height","16"],["textDictionaryId","screen.lastRefresh"]],null,null,null,y.Yc,y.fb)),o.Ib(134,4440064,null,0,a.vb,[o.r,a.i],{textDictionaryId:[0,"textDictionaryId"],height:[1,"height"],fontWeight:[2,"fontWeight"]},null),(t()(),o.Jb(135,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["height","16"],["id","lastRefTime"],["styleName","labelLeftRefTime"]],null,null,null,y.Yc,y.fb)),o.Ib(136,4440064,[[12,4],["lastRefTime",4]],0,a.vb,[o.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],height:[2,"height"],fontWeight:[3,"fontWeight"]},null),(t()(),o.Jb(137,0,null,0,8,"HBox",[["horizontalAlign","right"],["paddingRight","10"]],null,null,null,y.Dc,y.K)),o.Ib(138,4440064,null,0,a.C,[o.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),o.Jb(139,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),o.Jb(140,0,null,null,1,"DataExport",[["id","dataExport"]],null,null,null,y.Sc,y.Z)),o.Ib(141,4440064,[[18,4],["dataExport",4]],0,a.kb,[a.i,o.r],{id:[0,"id"]},null),(t()(),o.Jb(142,0,null,0,1,"SwtHelpButton",[],null,[[null,"click"]],function(t,e,i){var o=!0,l=t.component;"click"===e&&(o=!1!==l.doHelp()&&o);return o},y.Wc,y.db)),o.Ib(143,4440064,null,0,a.rb,[o.r,a.i],null,{onClick_:"click"}),(t()(),o.Jb(144,0,null,0,1,"SwtLoadingImage",[],null,null,null,y.Zc,y.gb)),o.Ib(145,114688,[[9,4],["loadingImage",4]],0,a.xb,[o.r],null,null)],function(t,e){t(e,25,0,"100%","100%");t(e,27,0,"100%","100%","5","5","5","5");t(e,29,0,"100%","58","900");t(e,31,0,"100%","100%","5");t(e,33,0,"100%","50%");t(e,35,0,"65%");t(e,37,0,"115");t(e,39,0,"entity.id"),t(e,41,0);t(e,43,0,"entity","180","entityCombo"),t(e,45,0);t(e,47,0,"10"," ","normal"),t(e,49,0);t(e,51,0,"80");t(e,53,0,"currencyInterest.fromDate");t(e,55,0,"110");t(e,57,0,"Enter start date (if applicable)","fromDate","70");t(e,59,0,"40","40");t(e,61,0,"text.showdays");t(e,63,0,"10");t(e,65,0,"2","showDays","30");t(e,67,0,"10");t(e,69,0,"daysLabel","text.day");t(e,71,0,"100%","50%");t(e,73,0,"65%");t(e,75,0,"115");t(e,77,0,"currency.group"),t(e,79,0);t(e,81,0,"currencygroup","180","ccyCombo"),t(e,83,0);t(e,85,0,"10","normal"),t(e,87,0),t(e,89,0);t(e,91,0,"currMonitor.breakdown","80","2"),t(e,93,0);t(e,95,0,"breakdown","horizontal");t(e,98,0,"accountRadio","breakdown","Account","A","true");t(e,100,0,"movementRadio","breakdown","Movement","M");t(e,102,0,"bookRadio","breakdown","Book","B");t(e,104,0,"groupRadio","breakdown","Group","G");t(e,106,0,"meatagroupRadio","breakdown","MetaGroup","MG");t(e,108,0,"1","100%","100%","900");t(e,110,0,"displaycontainer","canvasWithGreyBorder","100%","100%","false");t(e,112,0,"totalsContainer","canvasWithGreyBorder","100%","40","false");t(e,114,0,"100%","40","900","0");t(e,116,0,"1","100%");t(e,118,0,"100%","5");t(e,120,0,"refreshButton","false",!0);t(e,122,0,"optionsButton","false",!0);t(e,124,0,"closeButton",!0);t(e,126,0,"right","100%","10");t(e,128,0,"dataBuildingText","155","16","false","DATA BUILD IN PROGRESS","red");t(e,130,0,"right","10");t(e,132,0,"lostConnectionText","155","16","false","CONNECTION ERROR","red");t(e,134,0,"screen.lastRefresh","16","normal");t(e,136,0,"lastRefTime","labelLeftRefTime","16","normal");t(e,138,0,"right","10");t(e,141,0,"dataExport"),t(e,143,0),t(e,145,0)},null)}function H(t){return o.dc(0,[(t()(),o.Jb(0,0,null,null,1,"currency-monitor",[],null,null,null,U,z)),o.Ib(1,4440064,null,0,c,[a.i,o.r],null,null)],function(t,e){t(e,1,0)},null)}var V=o.Fb("currency-monitor",c,H,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);