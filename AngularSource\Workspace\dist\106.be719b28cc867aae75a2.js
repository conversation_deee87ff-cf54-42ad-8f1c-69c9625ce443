(window.webpackJsonp=window.webpackJsonp||[]).push([[106],{"1d94":function(t,e,l){"use strict";l.r(e);var i=l("CcnG"),n=l("mrSG"),u=l("ZYCi"),o=l("wd/R"),a=l.n(o),d=l("447K"),s=function(t){function e(e,l){var i=t.call(this,l,e)||this;return i.commonService=e,i.element=l,i.jsonReader=new d.L,i.inputData=new d.G(i.commonService),i.baseURL=d.Wb.getBaseURL(),i.actionMethod="",i.actionPath="",i.requestParams=[],i.accountComboList=null,i.bookCodeComboList=null,i.currencyComboList=null,i.positionComboList=null,i.defaultEntity="",i.defaultCurrency="",i.lastSelectedCurrency="",i.lastSelectedEntity="",i.swtAlert=new d.bb(e),i}return n.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.preAdviceInputGrid=this.preAdviceGridContainer.addChild(d.hb),this.entity.text=d.Wb.getPredictMessage("movement.entity.id",null),this.positionLevel.text=d.Wb.getPredictMessage("movement.positionLevel",null),this.valueDate.text=d.Wb.getPredictMessage("movement.valueDate",null)+"*",this.postingDate.text=d.Wb.getPredictMessage("movement.postingDate",null),this.type.text=d.Wb.getPredictMessage("movement.movementType",null),this.productType.text=d.Wb.getPredictMessage("movement.productType",null),this.amount.text=d.Wb.getPredictMessage("movementDisplay.amount",null)+"*",this.account.text=d.Wb.getPredictMessage("movement.accountId",null)+"*",this.bookCode.text=d.Wb.getPredictMessage("movement.bookCode",null),this.reference.text=d.Wb.getPredictMessage("preadviceInput.reference",null),this.counterParty.text=d.Wb.getPredictMessage("movement.counterParty",null),this.matchingParty.text=d.Wb.getPredictMessage("movement.matchingParty",null),this.entity1.text=d.Wb.getPredictMessage("movement.entity.id",null),this.inputBy.text=d.Wb.getPredictMessage("movement.inputBy",null),this.counterPartyLabel.text="",this.matchingPartyLabel.text="",this.entityCombo.toolTip=d.Wb.getPredictMessage("tooltip.selectEntityid",null),this.positionCombo.toolTip=d.Wb.getPredictMessage("tooltip.selectPosLevel",null),this.currencyCombo.toolTip=d.Wb.getPredictMessage("tooltip.selectCurrencyCode",null),this.signCombo.toolTip=d.Wb.getPredictMessage("tooltip.selectAmountSign",null),this.accountCombo.toolTip=d.Wb.getPredictMessage("tooltip.selectAccountId",null),this.bookCodeCombo.toolTip=d.Wb.getPredictMessage("tooltip.selectBookcode",null),this.amountTxtInput.toolTip=d.Wb.getPredictMessage("tooltip.enterAmount",null),this.referenceTxtInput.toolTip=d.Wb.getPredictMessage("tooltip.enterMvmRef1",null),this.counterPartyTxtInput.toolTip=d.Wb.getPredictMessage("tooltip.counterPartId",null),this.counterPartyTxtInput1.toolTip=d.Wb.getPredictMessage("tooltip.enterCPartytext",null),this.matchingPartyTxtInput.toolTip=d.Wb.getPredictMessage("tooltip.enterMatchingParty",null),this.productTypeTxtInput.toolTip=d.Wb.getPredictMessage("tooltip.enterProductType",null),this.fieldSet.legendText=d.Wb.getPredictMessage("movementDisplay.predictStatus",null),this.fieldSet1.legendText=d.Wb.getPredictMessage("preAdviceInput.fieldSet1.legendText",null),this.fieldSet2.legendText=d.Wb.getPredictMessage("preAdviceInput.fieldSet2.legendText",null),this.includedRadio.label=d.Wb.getPredictMessage("movementDisplay.included",null),this.excludedRadio.label=d.Wb.getPredictMessage("movementDisplay.excluded",null),this.cancelledRadio.label=d.Wb.getPredictMessage("movementDisplay.cancelled",null),this.cashRadio.label=d.Wb.getPredictMessage("movementDisplay.cash",null),this.securitiesRadio.label=d.Wb.getPredictMessage("movementDisplay.securities",null),this.allRadio.label=d.Wb.getPredictMessage("tooltip.preadvice.allUsers",null),this.currentRadio.label=d.Wb.getPredictMessage("tooltip.preadvice.currentUser",null),this.counterPartyButton.toolTip=d.Wb.getPredictMessage("tooltip.clickSelCounterId",null),this.matchingPartyButton.toolTip=d.Wb.getPredictMessage("tooltip.clickSelMatchingParty",null),this.saveButton.label=d.Wb.getPredictMessage("button.save",null),this.saveButton.toolTip=d.Wb.getPredictMessage("tooltip.SaveChanges",null),this.importButton.label=d.Wb.getPredictMessage("button.import",null),this.importButton.toolTip=d.Wb.getPredictMessage("toolTip.import",null),this.mvtButton.label=d.Wb.getPredictMessage("button.move",null),this.mvtButton.toolTip=d.Wb.getPredictMessage("tooltip.move",null),this.refreshButton.label=d.Wb.getPredictMessage("button.Refresh",null),this.refreshButton.toolTip=d.Wb.getPredictMessage("tooltip.refresh",null),this.closeButton.label=d.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=d.Wb.getPredictMessage("tooltip.close",null),this.valueDateField.toolTip=d.Wb.getPredictMessage("tooltip.clickValueDate",null),this.postingDateField.toolTip=d.Wb.getPredictMessage("tooltip.enterPostingDate",null),this.entityCombo.required=!0,this.amountTxtInput.required=!0,this.accountCombo.required=!0},e.prototype.onLoad=function(){var t=this;this.requestParams=[],this.menuAccessId=d.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=display",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.movType=this.typeRadios.selectedValue,this.requestParams.entityId1="All",this.requestParams.inputBy=this.user.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.preAdviceInputGrid.onRowClick=function(e){t.cellClickEventHandler(e)},d.v.subscribe(function(e){t.export(e)})},e.prototype.export=function(t){this.dataExport.convertData(this.lastRecievedJSON.PreAdviceInput.preAdviceInputGrid.metadata.columns,this.preAdviceInputGrid,null,[],t,!1)},e.prototype.inputDataResult=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else{if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.dataExport.enabled=!0,this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON&&(this.displayedDate=this.jsonReader.getSingletons().displayedDate,this.dateFormat=this.jsonReader.getSingletons().dateFormat,this.valueDateField.formatString=this.dateFormat.toLowerCase(),this.postingDateField.formatString=this.dateFormat.toLowerCase(),this.entityCombo.setComboData(this.jsonReader.getSelects()),this.defaultEntity=this.jsonReader.getSingletons().defaultEntity,this.entityCombo.selectedLabel=this.defaultEntity,this.entityCombo1.setComboData(this.jsonReader.getSelects()),null!=this.lastSelectedEntity&&(this.entityCombo1.selectedLabel=this.lastSelectedEntity),this.positionCombo.setComboData(this.jsonReader.getSelects()),this.currencyCombo.setComboData(this.jsonReader.getSelects()),this.defaultCurrency=this.jsonReader.getSingletons().defaultCurrency,this.currencyCombo.selectedLabel=this.defaultCurrency,this.signCombo.setComboData(this.jsonReader.getSelects()),this.accountCombo.setComboData(this.jsonReader.getSelects()),this.bookCodeCombo.setComboData(this.jsonReader.getSelects()),this.valueDateField.text=this.displayedDate,this.postingDateField.text=this.displayedDate,this.entityDesc.text=this.entityCombo.selectedValue,this.positionName.text=this.positionCombo.selectedValue,this.accountLabel.text=null!=this.accountCombo.selectedValue?this.accountCombo.selectedValue:"",this.bookCodeLabel.text=this.bookCodeCombo.selectedValue,this.currencyPattern=this.jsonReader.getSingletons().currencyPattern,!this.jsonReader.isDataBuilding())){var e={columns:this.lastRecievedJSON.PreAdviceInput.preAdviceInputGrid.metadata.columns};this.preAdviceInputGrid.CustomGrid(e);var l=this.lastRecievedJSON.PreAdviceInput.preAdviceInputGrid.rows;l.size>0?(this.preAdviceInputGrid.gridData=l,this.preAdviceInputGrid.setRowSize=this.jsonReader.getRowSize(),this.dataExport.enabled=!0):(this.preAdviceInputGrid.gridData={size:0,row:[]},this.dataExport.enabled=!1),this.prevRecievedJSON=this.lastRecievedJSON}}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error");this.clearComponents()}},e.prototype.clearComponents=function(){this.productTypeTxtInput.text="",this.amountTxtInput.text="",this.referenceTxtInput.text="",this.counterPartyTxtInput.text="",this.counterPartyTxtInput1.text="",this.matchingPartyTxtInput.text="",this.typeRadios.selectedValue="C",this.bookCodeLabel.text="",this.counterPartyLabel.text="",this.matchingPartyLabel.text="",this.saveButton.label=d.Wb.getPredictMessage("button.save",null),this.saveButton.toolTip=d.Wb.getPredictMessage("tooltip.SaveChanges",null),this.predictStatus.selectedValue="I"},e.prototype.refreshComboList=function(){var t=this;this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.setComboLists(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=getLists",this.requestParams.entityId=this.entityCombo.selectedLabel,this.requestParams.movType=this.typeRadios.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.entityDesc.text=this.entityCombo.selectedValue},e.prototype.setComboLists=function(t){this.accountComboList=t.RefreshedComboList.selects.select[0].option,this.accountComboList&&(this.accountCombo.dataProvider=this.accountComboList,this.accountCombo.setComboData(this.accountComboList),this.accountLabel.text=null!=this.accountCombo.selectedValue?this.accountCombo.selectedValue:""),this.bookCodeComboList=t.RefreshedComboList.selects.select[1].option,this.bookCodeComboList&&(this.bookCodeCombo.dataProvider=this.bookCodeComboList,this.bookCodeCombo.setComboData(this.bookCodeComboList),this.bookCodeLabel.text=this.bookCodeCombo.selectedValue),this.currencyComboList=t.RefreshedComboList.selects.select[2].option,this.currencyComboList&&(this.currencyCombo.dataProvider=this.currencyComboList,this.currencyCombo.setComboData(this.currencyComboList),this.currencyCombo.selectedLabel=t.RefreshedComboList.defaultCurrency),this.positionCombo.setComboData(this.jsonReader.getSelects()),this.positionName.text=this.positionCombo.selectedValue,this.positionComboList=t.RefreshedComboList.selects.select[3].option,this.positionComboList&&(this.positionCombo.dataProvider=this.positionComboList,this.positionCombo.setComboData(this.positionComboList),this.positionName.text=this.positionCombo.selectedValue)},e.prototype.refreshAccountComboList=function(){var t=this;this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.setAccountList(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=getUpdatedAccountList",this.requestParams.entityId=this.entityCombo.selectedLabel,this.requestParams.currencyCode=this.currencyCombo.selectedLabel,this.requestParams.movType=this.typeRadios.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.setAccountList=function(t){this.accountComboList=t.RefreshedComboList.selects.select.option,this.accountComboList&&(this.accountCombo.dataProvider=this.accountComboList,this.accountCombo.setComboData(this.accountComboList),this.accountLabel.text=null!=this.accountCombo.selectedValue?this.accountCombo.selectedValue:"")},e.prototype.refreshLabel=function(){this.accountLabel.text=null!=this.accountCombo.selectedValue?this.accountCombo.selectedValue:"",this.bookCodeLabel.text=null!=this.bookCodeCombo.selectedValue?this.bookCodeCombo.selectedValue:""},e.prototype.preSearchParties=function(){d.x.call("openChildPartyWindow","partySearch",this.entityCombo.selectedLabel)},e.prototype.setSelectedPartieItemsForPreadvice=function(t,e){var l;"counterPartyButton"==(l=Object(d.ic.getFocus()).name.id)?(this.counterPartyTxtInput.text=t,this.counterPartyLabel.text=e):"matchingPartyButton"==l&&(this.matchingPartyTxtInput.text=t,this.matchingPartyLabel.text=e)},e.prototype.savePreAdvice=function(){var t=this;this.requestParams=[],""==this.entityCombo.selectedValue||null==this.entityCombo.selectedValue||""==this.amountTxtInput.text||""==this.accountCombo.selectedValue||null==this.accountCombo.selectedValue?this.swtAlert.error(d.Wb.getPredictMessage("alert.pleaseFillAllMandatoryFields",null)):(this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.displayPreAdvice(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=savePreAdvice",this.requestParams["movement.accountId"]=this.accountCombo.selectedLabel,this.requestParams["movement.amountAsString"]=this.amountTxtInput.text,this.requestParams["movement.bookCode"]=this.bookCodeCombo.selectedLabel,this.requestParams["movement.counterPartyId"]=this.counterPartyTxtInput.text,this.requestParams["movement.counterPartyText1"]=this.counterPartyTxtInput1.text,this.requestParams["movement.currencyCode"]=this.currencyCombo.selectedLabel,this.requestParams["movement.id.entityId"]=this.entityCombo.selectedLabel,this.requestParams["movement.matchingParty"]=this.matchingPartyTxtInput.text,this.requestParams["movement.matchStatus"]="",this.requestParams["movement.movementType"]=this.typeRadios.selectedValue,this.requestParams["movement.positionLevelAsString"]=this.positionCombo.selectedLabel,this.requestParams["movement.postingDateAsString"]=this.postingDateField.text,this.requestParams["movement.predictStatus"]=this.predictStatus.selectedValue,this.requestParams["movement.productType"]=this.productTypeTxtInput.text,this.requestParams["movement.reference1"]=this.referenceTxtInput.text,this.requestParams["movement.sign"]=this.signCombo.selectedLabel,this.requestParams["movement.valueDateAsString"]=this.valueDateField.text,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams))},e.prototype.displayPreAdvice=function(t){this.jsonReader.getRequestReplyStatus()&&this.swtAlert.info(t.preAdviceInput.status),this.updateData()},e.prototype.validateReserve=function(){var t=d.Wb.getPredictMessage("alert.validAmount",null);if(!validateCurrencyPlaces(this.amountTxtInput,this.currencyPattern,this.currencyCombo.selectedLabel))return this.swtAlert.warning(t),!1},e.prototype.validateDateField=function(t){var e=this;try{var l=void 0,i=d.Wb.getPredictMessage("alert.enterValidDate",null);if(!t.text)return this.swtAlert.error(i,null,null,null,function(){e.setFocusDateField(t)}),!1;if(!(l=a()(t.text,this.dateFormat.toUpperCase(),!0)).isValid())return this.swtAlert.error(i,null,null,null,function(){e.setFocusDateField(t)}),!1;t.selectedDate=l.toDate()}catch(n){}return!0},e.prototype.setFocusDateField=function(t){t.setFocus(),t.text=this.jsonReader.getSingletons().displayedDate},e.prototype.updateData=function(){var t=this;this.requestParams=[],this.entityCombo.enabled=!0,this.preAdviceInputGrid.selectedIndex=-1,this.mvtButton.enabled=!1,this.mvtButton.buttonMode=!1,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=display",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.movType=this.typeRadios.selectedValue,this.requestParams.entityId1=this.entityCombo1.selectedLabel,this.requestParams.inputBy=this.user.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.send(this.requestParams),this.lastSelectedEntity=this.entityCombo1.selectedLabel},e.prototype.cellClickEventHandler=function(t){this.preAdviceInputGrid.selectedIndex>=0?(this.mvtButton.enabled=!0,this.mvtButton.buttonMode=!0,this.selectedtRow=this.preAdviceInputGrid.selectedItem):(this.mvtButton.enabled=!1,this.mvtButton.buttonMode=!1,this.valueDateField.text=this.displayedDate,this.postingDateField.text=this.displayedDate,this.clearComponents(),this.refreshComboList())},e.prototype.openMvtDisplayScreen=function(){var t=this.preAdviceInputGrid.selectedItem.Movement.content,e=this.preAdviceInputGrid.selectedItem.entityid.content;d.x.call("showMvmnt","showMovementDetails",t,e,"")},e.prototype.openImportMvtScreen=function(){d.x.call("openImportWindow","importMvt")},e.prototype.getMvtType=function(t){var e=this;this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="preadviceinput.do?",this.actionMethod="method=getMvtType",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.accountId=t,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.cbResult=function(t){e.populateValues(t)},this.inputData.send(this.requestParams)},e.prototype.populateValues=function(t){this.mvtType=t.preAdviceInput.mvtType,this.entityCombo.selectedLabel=this.selectedtRow.entityid.content,this.valueDateField.text=null!=this.selectedtRow.Value_Date.content?this.selectedtRow.Value_Date.content:"",this.postingDateField.text=null!=this.selectedtRow.Post_Date.content?this.selectedtRow.Post_Date.content:"",this.typeRadios.selectedValue=null!=this.mvtType?this.mvtType:"",this.predictStatus.selectedValue=this.getPredictStatusCode(this.selectedtRow["Pred.Status"].content),this.productTypeTxtInput.text=null!=this.selectedtRow.Product_Type.content?this.selectedtRow.Product_Type.content:"",this.amountTxtInput.text=null!=this.selectedtRow.Amount.content?this.selectedtRow.Amount.content:"",this.currencyCombo.selectedLabel=null!=this.selectedtRow.Ccy.content?this.selectedtRow.Ccy.content:"",this.signCombo.selectedLabel=null!=this.selectedtRow.Sign.content?this.selectedtRow.Sign.content:"",this.refreshComboList(),this.bookCodeCombo.selectedLabel=null!=this.selectedtRow.Book_Code.content?this.selectedtRow.Book_Code.content:"",this.referenceTxtInput.text=null!=this.selectedtRow.Reference.content?this.selectedtRow.Reference.content:"",this.counterPartyTxtInput.text=null!=this.selectedtRow.CounterParty_ID.content?this.selectedtRow.CounterParty_ID.content:"",this.counterPartyTxtInput1.text=null!=this.selectedtRow.Cparty_Text.content?this.selectedtRow.Cparty_Text.content:"",this.matchingPartyTxtInput.text=null!=this.selectedtRow.Match_Party.content?this.selectedtRow.Match_Party.content:"",this.saveButton.label=d.Wb.getPredictMessage("button.update",null),this.saveButton.toolTip=d.Wb.getPredictMessage("tooltip.SaveUpdates",null)},e.prototype.getPredictStatusCode=function(t){var e="";switch(t){case"Included":e="I";break;case"Excluded":e="E";break;case"Cancelled":e="C"}return e},e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(t){this._invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},e.prototype.closeHandler=function(){d.x.call("close")},e}(d.yb),r=[{path:"",component:s}],c=(u.l.forChild(r),function(){return function(){}}()),h=l("pMnS"),b=l("RChO"),p=l("t6HQ"),m=l("WFGK"),g=l("5FqG"),w=l("Ip0R"),I=l("gIcY"),y=l("t/Na"),v=l("sE5F"),C=l("OzfB"),f=l("T7CS"),R=l("S7LP"),P=l("6aHO"),L=l("WzUx"),A=l("A7o+"),x=l("zCE2"),S=l("Jg5P"),J=l("3R0m"),D=l("hhbb"),T=l("5rxC"),B=l("Fzqc"),M=l("21Lb"),G=l("hUWP"),k=l("3pJQ"),W=l("V9q+"),F=l("VDKW"),Z=l("kXfT"),q=l("BGbe");l.d(e,"PreAdviceInputModuleNgFactory",function(){return _}),l.d(e,"RenderType_PreAdviceInput",function(){return N}),l.d(e,"View_PreAdviceInput_0",function(){return O}),l.d(e,"View_PreAdviceInput_Host_0",function(){return E}),l.d(e,"PreAdviceInputNgFactory",function(){return j});var _=i.Gb(c,[],function(t){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[h.a,b.a,p.a,m.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,j]],[3,i.n],i.J]),i.Rb(4608,w.m,w.l,[i.F,[2,w.u]]),i.Rb(4608,I.c,I.c,[]),i.Rb(4608,I.p,I.p,[]),i.Rb(4608,y.j,y.p,[w.c,i.O,y.n]),i.Rb(4608,y.q,y.q,[y.j,y.o]),i.Rb(5120,y.a,function(t){return[t,new d.tb]},[y.q]),i.Rb(4608,y.m,y.m,[]),i.Rb(6144,y.k,null,[y.m]),i.Rb(4608,y.i,y.i,[y.k]),i.Rb(6144,y.b,null,[y.i]),i.Rb(4608,y.f,y.l,[y.b,i.B]),i.Rb(4608,y.c,y.c,[y.f]),i.Rb(4608,v.c,v.c,[]),i.Rb(4608,v.g,v.b,[]),i.Rb(5120,v.i,v.j,[]),i.Rb(4608,v.h,v.h,[v.c,v.g,v.i]),i.Rb(4608,v.f,v.a,[]),i.Rb(5120,v.d,v.k,[v.h,v.f]),i.Rb(5120,i.b,function(t,e){return[C.j(t,e)]},[w.c,i.O]),i.Rb(4608,f.a,f.a,[]),i.Rb(4608,R.a,R.a,[]),i.Rb(4608,P.a,P.a,[i.n,i.L,i.B,R.a,i.g]),i.Rb(4608,L.c,L.c,[i.n,i.g,i.B]),i.Rb(4608,L.e,L.e,[L.c]),i.Rb(4608,A.l,A.l,[]),i.Rb(4608,A.h,A.g,[]),i.Rb(4608,A.c,A.f,[]),i.Rb(4608,A.j,A.d,[]),i.Rb(4608,A.b,A.a,[]),i.Rb(4608,A.k,A.k,[A.l,A.h,A.c,A.j,A.b,A.m,A.n]),i.Rb(4608,L.i,L.i,[[2,A.k]]),i.Rb(4608,L.r,L.r,[L.L,[2,A.k],L.i]),i.Rb(4608,L.t,L.t,[]),i.Rb(4608,L.w,L.w,[]),i.Rb(1073742336,u.l,u.l,[[2,u.r],[2,u.k]]),i.Rb(1073742336,w.b,w.b,[]),i.Rb(1073742336,I.n,I.n,[]),i.Rb(1073742336,I.l,I.l,[]),i.Rb(1073742336,x.a,x.a,[]),i.Rb(1073742336,S.a,S.a,[]),i.Rb(1073742336,I.e,I.e,[]),i.Rb(1073742336,J.a,J.a,[]),i.Rb(1073742336,A.i,A.i,[]),i.Rb(1073742336,L.b,L.b,[]),i.Rb(1073742336,y.e,y.e,[]),i.Rb(1073742336,y.d,y.d,[]),i.Rb(1073742336,v.e,v.e,[]),i.Rb(1073742336,D.b,D.b,[]),i.Rb(1073742336,T.b,T.b,[]),i.Rb(1073742336,C.c,C.c,[]),i.Rb(1073742336,B.a,B.a,[]),i.Rb(1073742336,M.d,M.d,[]),i.Rb(1073742336,G.c,G.c,[]),i.Rb(1073742336,k.a,k.a,[]),i.Rb(1073742336,W.a,W.a,[[2,C.g],i.O]),i.Rb(1073742336,F.b,F.b,[]),i.Rb(1073742336,Z.a,Z.a,[]),i.Rb(1073742336,q.b,q.b,[]),i.Rb(1073742336,d.Tb,d.Tb,[]),i.Rb(1073742336,c,c,[]),i.Rb(256,y.n,"XSRF-TOKEN",[]),i.Rb(256,y.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,A.m,void 0,[]),i.Rb(256,A.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,u.i,function(){return[[{path:"",component:s}]]},[])])}),V=[[""]],N=i.Hb({encapsulation:0,styles:V,data:{}});function O(t){return i.dc(0,[i.Zb(*********,1,{_container:0}),i.Zb(*********,2,{preAdviceGridContainer:0}),i.Zb(*********,3,{entity:0}),i.Zb(*********,4,{entityDesc:0}),i.Zb(*********,5,{positionLevel:0}),i.Zb(*********,6,{positionName:0}),i.Zb(*********,7,{valueDate:0}),i.Zb(*********,8,{postingDate:0}),i.Zb(*********,9,{type:0}),i.Zb(*********,10,{productType:0}),i.Zb(*********,11,{amount:0}),i.Zb(*********,12,{account:0}),i.Zb(*********,13,{accountLabel:0}),i.Zb(*********,14,{bookCode:0}),i.Zb(*********,15,{bookCodeLabel:0}),i.Zb(*********,16,{reference:0}),i.Zb(*********,17,{counterParty:0}),i.Zb(*********,18,{counterPartyLabel:0}),i.Zb(*********,19,{matchingParty:0}),i.Zb(*********,20,{matchingPartyLabel:0}),i.Zb(*********,21,{entity1:0}),i.Zb(*********,22,{inputBy:0}),i.Zb(*********,23,{entityCombo:0}),i.Zb(*********,24,{positionCombo:0}),i.Zb(*********,25,{currencyCombo:0}),i.Zb(*********,26,{signCombo:0}),i.Zb(*********,27,{entityCombo1:0}),i.Zb(*********,28,{bookCodeCombo:0}),i.Zb(*********,29,{accountCombo:0}),i.Zb(*********,30,{valueDateField:0}),i.Zb(*********,31,{postingDateField:0}),i.Zb(*********,32,{fieldSet:0}),i.Zb(*********,33,{fieldSet1:0}),i.Zb(*********,34,{fieldSet2:0}),i.Zb(*********,35,{predictStatus:0}),i.Zb(*********,36,{includedRadio:0}),i.Zb(*********,37,{excludedRadio:0}),i.Zb(*********,38,{cancelledRadio:0}),i.Zb(*********,39,{typeRadios:0}),i.Zb(*********,40,{cashRadio:0}),i.Zb(*********,41,{securitiesRadio:0}),i.Zb(*********,42,{user:0}),i.Zb(*********,43,{allRadio:0}),i.Zb(*********,44,{currentRadio:0}),i.Zb(*********,45,{productTypeTxtInput:0}),i.Zb(*********,46,{amountTxtInput:0}),i.Zb(*********,47,{referenceTxtInput:0}),i.Zb(*********,48,{counterPartyTxtInput:0}),i.Zb(*********,49,{counterPartyTxtInput1:0}),i.Zb(*********,50,{matchingPartyTxtInput:0}),i.Zb(*********,51,{accountButton:0}),i.Zb(*********,52,{bookCodeButton:0}),i.Zb(*********,53,{counterPartyButton:0}),i.Zb(*********,54,{matchingPartyButton:0}),i.Zb(*********,55,{saveButton:0}),i.Zb(*********,56,{importButton:0}),i.Zb(*********,57,{mvtButton:0}),i.Zb(*********,58,{refreshButton:0}),i.Zb(*********,59,{closeButton:0}),i.Zb(*********,60,{loadingImage:0}),i.Zb(*********,61,{dataExport:0}),(t()(),i.Jb(61,0,null,null,291,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,l){var i=!0,n=t.component;"creationComplete"===e&&(i=!1!==n.onLoad()&&i);return i},g.ad,g.hb)),i.Ib(62,4440064,null,0,d.yb,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),i.Jb(63,0,null,0,289,"VBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),i.Ib(64,4440064,null,0,d.ec,[i.r,d.i,i.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(t()(),i.Jb(65,0,null,0,229,"SwtFieldSet",[["id","fieldSet1"],["style","height: 292px; width: 100%; color:blue;"]],null,null,null,g.Vc,g.cb)),i.Ib(66,4440064,[[33,4],["fieldSet1",4]],0,d.ob,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(67,0,null,0,227,"Grid",[["height","100%"],["paddingLeft","5"],["width","100%"]],null,null,null,g.Cc,g.H)),i.Ib(68,4440064,null,0,d.z,[i.r,d.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(69,0,null,0,68,"GridRow",[],null,null,null,g.Bc,g.J)),i.Ib(70,4440064,null,0,d.B,[i.r,d.i],null,null),(t()(),i.Jb(71,0,null,0,51,"VBox",[["width","780"]],null,null,null,g.od,g.vb)),i.Ib(72,4440064,null,0,d.ec,[i.r,d.i,i.T],{width:[0,"width"]},null),(t()(),i.Jb(73,0,null,0,17,"GridRow",[["height","26"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(74,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(75,0,null,0,15,"GridItem",[["width","65%"]],null,null,null,g.Ac,g.I)),i.Ib(76,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(77,0,null,0,9,"GridItem",[["width","300"]],null,null,null,g.Ac,g.I)),i.Ib(78,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(79,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(80,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(81,0,null,0,1,"SwtLabel",[["id","entity"]],null,null,null,g.Yc,g.fb)),i.Ib(82,4440064,[[3,4],["entity",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(83,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(84,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(85,0,null,0,1,"SwtComboBox",[["dataLabel","entityList"],["id","entityCombo"],["width","200"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var n=!0,u=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,86).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==u.refreshComboList()&&n);return n},g.Pc,g.W)),i.Ib(86,4440064,[[23,4],["entityCombo",4]],0,d.gb,[i.r,d.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),i.Jb(87,0,null,0,3,"GridItem",[["paddingLeft","50"]],null,null,null,g.Ac,g.I)),i.Ib(88,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(89,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","entityDesc"]],null,null,null,g.Yc,g.fb)),i.Ib(90,4440064,[[4,4],["entityDesc",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(91,0,null,0,17,"GridRow",[["height","25"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(92,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(93,0,null,0,15,"GridItem",[["width","65%"]],null,null,null,g.Ac,g.I)),i.Ib(94,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(95,0,null,0,9,"GridItem",[["width","300"]],null,null,null,g.Ac,g.I)),i.Ib(96,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(97,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(98,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(99,0,null,0,1,"SwtLabel",[["id","positionLevel"]],null,null,null,g.Yc,g.fb)),i.Ib(100,4440064,[[5,4],["positionLevel",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(101,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(102,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(103,0,null,0,1,"SwtComboBox",[["dataLabel","PositionLevelList"],["enabled","false"],["id","positionCombo"],["width","60"]],null,[["window","mousewheel"]],function(t,e,l){var n=!0;"window:mousewheel"===e&&(n=!1!==i.Tb(t,104).mouseWeelEventHandler(l.target)&&n);return n},g.Pc,g.W)),i.Ib(104,4440064,[[24,4],["positionCombo",4]],0,d.gb,[i.r,d.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],enabled:[3,"enabled"]},null),(t()(),i.Jb(105,0,null,0,3,"GridItem",[["paddingLeft","50"]],null,null,null,g.Ac,g.I)),i.Ib(106,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(107,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","positionName"]],null,null,null,g.Yc,g.fb)),i.Ib(108,4440064,[[6,4],["positionName",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(109,0,null,0,13,"GridRow",[["height","25"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(110,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(111,0,null,0,11,"GridItem",[["width","95%"]],null,null,null,g.Ac,g.I)),i.Ib(112,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(113,0,null,0,9,"GridItem",[["paddingTop","2"],["width","535"]],null,null,null,g.Ac,g.I)),i.Ib(114,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],paddingTop:[1,"paddingTop"]},null),(t()(),i.Jb(115,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(116,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(117,0,null,0,1,"SwtLabel",[["id","valueDate"]],null,null,null,g.Yc,g.fb)),i.Ib(118,4440064,[[7,4],["valueDate",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(119,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(120,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(121,0,null,0,1,"SwtDateField",[["id","valueDateField"],["width","70"]],null,[[null,"change"]],function(t,e,l){var n=!0,u=t.component;"change"===e&&(n=!1!==u.validateDateField(i.Tb(t,122))&&n);return n},g.Tc,g.ab)),i.Ib(122,4308992,[[30,4],["valueDateField",4]],0,d.lb,[i.r,d.i,i.T],{id:[0,"id"],width:[1,"width"]},{changeEventOutPut:"change"}),(t()(),i.Jb(123,0,null,0,14,"VBox",[["width","100"]],null,null,null,g.od,g.vb)),i.Ib(124,4440064,null,0,d.ec,[i.r,d.i,i.T],{width:[0,"width"]},null),(t()(),i.Jb(125,0,null,0,12,"GridItem",[["horizontalAlign","right"]],null,null,null,g.Ac,g.I)),i.Ib(126,4440064,null,0,d.A,[i.r,d.i],{horizontalAlign:[0,"horizontalAlign"]},null),(t()(),i.Jb(127,0,null,0,10,"SwtFieldSet",[["id","fieldSet"],["style","color:blue;"]],null,null,null,g.Vc,g.cb)),i.Ib(128,4440064,[[32,4],["fieldSet",4]],0,d.ob,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(129,0,null,0,8,"SwtRadioButtonGroup",[["align","vertical"],["id","predictStatus"]],null,null,null,g.ed,g.lb)),i.Ib(130,4440064,[[35,4],["predictStatus",4]],1,d.Hb,[y.c,i.r,d.i],{id:[0,"id"],align:[1,"align"]},null),i.Zb(*********,62,{radioItems:1}),(t()(),i.Jb(132,0,null,0,1,"SwtRadioItem",[["groupName","predictStatus"],["id","includedRadio"],["selected","true"],["value","I"]],null,null,null,g.fd,g.mb)),i.Ib(133,4440064,[[62,4],[36,4],["includedRadio",4]],0,d.Ib,[i.r,d.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"],selected:[3,"selected"]},null),(t()(),i.Jb(134,0,null,0,1,"SwtRadioItem",[["groupName","predictStatus"],["id","excludedRadio"],["value","E"]],null,null,null,g.fd,g.mb)),i.Ib(135,4440064,[[62,4],[37,4],["excludedRadio",4]],0,d.Ib,[i.r,d.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),i.Jb(136,0,null,0,1,"SwtRadioItem",[["groupName","predictStatus"],["id","cancelledRadio"],["value","C"]],null,null,null,g.fd,g.mb)),i.Ib(137,4440064,[[62,4],[38,4],["cancelledRadio",4]],0,d.Ib,[i.r,d.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),i.Jb(138,0,null,0,28,"GridRow",[["height","25"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(139,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(140,0,null,0,26,"GridItem",[["width","95%"]],null,null,null,g.Ac,g.I)),i.Ib(141,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(142,0,null,0,14,"GridItem",[["width","535"]],null,null,null,g.Ac,g.I)),i.Ib(143,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(144,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(145,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(146,0,null,0,1,"SwtLabel",[["id","type"]],null,null,null,g.Yc,g.fb)),i.Ib(147,4440064,[[9,4],["type",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(148,0,null,0,8,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(149,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(150,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","typeRadios"]],null,[[null,"change"]],function(t,e,l){var i=!0,n=t.component;"change"===e&&(i=!1!==n.refreshAccountComboList()&&i);return i},g.ed,g.lb)),i.Ib(151,4440064,[[39,4],["typeRadios",4]],1,d.Hb,[y.c,i.r,d.i],{id:[0,"id"],align:[1,"align"]},{change_:"change"}),i.Zb(*********,63,{radioItems:1}),(t()(),i.Jb(153,0,null,0,1,"SwtRadioItem",[["groupName","typeRadios"],["id","cashRadio"],["selected","true"],["value","C"]],null,null,null,g.fd,g.mb)),i.Ib(154,4440064,[[63,4],[40,4],["cashRadio",4]],0,d.Ib,[i.r,d.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"],selected:[3,"selected"]},null),(t()(),i.Jb(155,0,null,0,1,"SwtRadioItem",[["groupName","typeRadios"],["id","securitiesRadio"],["value","U"]],null,null,null,g.fd,g.mb)),i.Ib(156,4440064,[[63,4],[41,4],["securitiesRadio",4]],0,d.Ib,[i.r,d.i],{id:[0,"id"],groupName:[1,"groupName"],value:[2,"value"]},null),(t()(),i.Jb(157,0,null,0,9,"GridItem",[["width","400"]],null,null,null,g.Ac,g.I)),i.Ib(158,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(159,0,null,0,3,"GridItem",[["width","110"]],null,null,null,g.Ac,g.I)),i.Ib(160,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(161,0,null,0,1,"SwtLabel",[["id","postingDate"]],null,null,null,g.Yc,g.fb)),i.Ib(162,4440064,[[8,4],["postingDate",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(163,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(164,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(165,0,null,0,1,"SwtDateField",[["id","postingDateField"],["width","70"]],null,[[null,"change"]],function(t,e,l){var n=!0,u=t.component;"change"===e&&(n=!1!==u.validateDateField(i.Tb(t,166))&&n);return n},g.Tc,g.ab)),i.Ib(166,4308992,[[31,4],["postingDateField",4]],0,d.lb,[i.r,d.i,i.T],{id:[0,"id"],width:[1,"width"]},{changeEventOutPut:"change"}),(t()(),i.Jb(167,0,null,0,29,"GridRow",[["height","27"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(168,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(169,0,null,0,17,"GridItem",[["width","535"]],null,null,null,g.Ac,g.I)),i.Ib(170,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(171,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(172,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(173,0,null,0,1,"SwtLabel",[["id","amount"]],null,null,null,g.Yc,g.fb)),i.Ib(174,4440064,[[11,4],["amount",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(175,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(176,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(177,0,null,0,1,"SwtComboBox",[["dataLabel","currencyList"],["id","currencyCombo"],["width","80"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var n=!0,u=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,178).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==u.refreshAccountComboList()&&n);return n},g.Pc,g.W)),i.Ib(178,4440064,[[25,4],["currencyCombo",4]],0,d.gb,[i.r,d.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),i.Jb(179,0,null,0,3,"GridItem",[["paddingLeft","10"]],null,null,null,g.Ac,g.I)),i.Ib(180,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(181,0,null,0,1,"SwtTextInput",[["editable","true"],["id","amountTxtInput"],["restrict","0-9mtb.,"],["textAlign","right"],["width","230"]],null,[[null,"focusOut"]],function(t,e,l){var i=!0,n=t.component;"focusOut"===e&&(i=!1!==n.validateReserve()&&i);return i},g.kd,g.sb)),i.Ib(182,4440064,[[46,4],["amountTxtInput",4]],0,d.Rb,[i.r,d.i],{restrict:[0,"restrict"],id:[1,"id"],textAlign:[2,"textAlign"],width:[3,"width"],editable:[4,"editable"]},{onFocusOut_:"focusOut"}),(t()(),i.Jb(183,0,null,0,3,"GridItem",[["paddingLeft","10"]],null,null,null,g.Ac,g.I)),i.Ib(184,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(185,0,null,0,1,"SwtComboBox",[["dataLabel","signList"],["id","signCombo"],["width","50"]],null,[["window","mousewheel"]],function(t,e,l){var n=!0;"window:mousewheel"===e&&(n=!1!==i.Tb(t,186).mouseWeelEventHandler(l.target)&&n);return n},g.Pc,g.W)),i.Ib(186,4440064,[[26,4],["signCombo",4]],0,d.gb,[i.r,d.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},null),(t()(),i.Jb(187,0,null,0,9,"GridItem",[["width","400"]],null,null,null,g.Ac,g.I)),i.Ib(188,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(189,0,null,0,3,"GridItem",[["width","110"]],null,null,null,g.Ac,g.I)),i.Ib(190,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(191,0,null,0,1,"SwtLabel",[["id","productType"]],null,null,null,g.Yc,g.fb)),i.Ib(192,4440064,[[10,4],["productType",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(193,0,null,0,3,"GridItem",[["width","160"]],null,null,null,g.Ac,g.I)),i.Ib(194,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(195,0,null,0,1,"SwtTextInput",[["editable","true"],["id","productTypeTxtInput"],["maxChars","16"],["width","200"]],null,null,null,g.kd,g.sb)),i.Ib(196,4440064,[[45,4],["productTypeTxtInput",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"],editable:[3,"editable"]},null),(t()(),i.Jb(197,0,null,0,15,"GridRow",[["height","26"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(198,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(199,0,null,0,13,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),i.Ib(200,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(201,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(202,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(203,0,null,0,1,"SwtLabel",[["id","account"]],null,null,null,g.Yc,g.fb)),i.Ib(204,4440064,[[12,4],["account",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(205,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(206,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(207,0,null,0,1,"SwtComboBox",[["dataLabel","collAcctList"],["editable","false"],["id","accountCombo"],["width","385"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var n=!0,u=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,208).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==u.refreshLabel()&&n);return n},g.Pc,g.W)),i.Ib(208,4440064,[[29,4],["accountCombo",4]],0,d.gb,[i.r,d.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],editable:[3,"editable"]},{change_:"change"}),(t()(),i.Jb(209,0,null,0,3,"GridItem",[["paddingLeft","15"]],null,null,null,g.Ac,g.I)),i.Ib(210,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(211,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","accountLabel"]],null,null,null,g.Yc,g.fb)),i.Ib(212,4440064,[[13,4],["accountLabel",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(213,0,null,0,15,"GridRow",[["height","26"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(214,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(215,0,null,0,13,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),i.Ib(216,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(217,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(218,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(219,0,null,0,1,"SwtLabel",[["id","bookCode"]],null,null,null,g.Yc,g.fb)),i.Ib(220,4440064,[[14,4],["bookCode",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(221,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(222,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(223,0,null,0,1,"SwtComboBox",[["dataLabel","collBookCode"],["editable","false"],["id","bookCodeCombo"],["width","200"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var n=!0,u=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,224).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==u.refreshLabel()&&n);return n},g.Pc,g.W)),i.Ib(224,4440064,[[28,4],["bookCodeCombo",4]],0,d.gb,[i.r,d.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],editable:[3,"editable"]},{change_:"change"}),(t()(),i.Jb(225,0,null,0,3,"GridItem",[["paddingLeft","10"]],null,null,null,g.Ac,g.I)),i.Ib(226,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(227,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","bookCodeLabel"]],null,null,null,g.Yc,g.fb)),i.Ib(228,4440064,[[15,4],["bookCodeLabel",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(229,0,null,0,11,"GridRow",[["height","26"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(230,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(231,0,null,0,9,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),i.Ib(232,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(233,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(234,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(235,0,null,0,1,"SwtLabel",[["id","reference"]],null,null,null,g.Yc,g.fb)),i.Ib(236,4440064,[[16,4],["reference",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(237,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(238,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(239,0,null,0,1,"SwtTextInput",[["editable","true"],["id","referenceTxtInput"],["maxChars","35"],["width","385"]],null,null,null,g.kd,g.sb)),i.Ib(240,4440064,[[47,4],["referenceTxtInput",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"],editable:[3,"editable"]},null),(t()(),i.Jb(241,0,null,0,19,"GridRow",[["height","26"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(242,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(243,0,null,0,17,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),i.Ib(244,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(245,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(246,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(247,0,null,0,1,"SwtLabel",[["id","counterParty"]],null,null,null,g.Yc,g.fb)),i.Ib(248,4440064,[[17,4],["counterParty",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(249,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(250,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(251,0,null,0,1,"SwtTextInput",[["editable","true"],["id","counterPartyTxtInput"],["width","200"]],null,null,null,g.kd,g.sb)),i.Ib(252,4440064,[[48,4],["counterPartyTxtInput",4]],0,d.Rb,[i.r,d.i],{id:[0,"id"],width:[1,"width"],editable:[2,"editable"]},null),(t()(),i.Jb(253,0,null,0,3,"GridItem",[["paddingLeft","10"]],null,null,null,g.Ac,g.I)),i.Ib(254,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(255,0,null,0,1,"SwtButton",[["id","counterPartyButton"],["label","..."],["width","30"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.preSearchParties()&&i);return i},g.Mc,g.T)),i.Ib(256,4440064,[[53,4],["counterPartyButton",4]],0,d.cb,[i.r,d.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(257,0,null,0,3,"GridItem",[["paddingLeft","10"]],null,null,null,g.Ac,g.I)),i.Ib(258,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(259,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","counterPartyLabel"]],null,null,null,g.Yc,g.fb)),i.Ib(260,4440064,[[18,4],["counterPartyLabel",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(261,0,null,0,9,"GridRow",[["height","26"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(262,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(263,0,null,0,7,"GridItem",[["width","70%"]],null,null,null,g.Ac,g.I)),i.Ib(264,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(265,0,null,0,1,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(266,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(267,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(268,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(269,0,null,0,1,"SwtTextInput",[["editable","true"],["id","counterPartyTxtInput1"],["maxChars","35"],["width","385"]],null,null,null,g.kd,g.sb)),i.Ib(270,4440064,[[49,4],["counterPartyTxtInput1",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"],editable:[3,"editable"]},null),(t()(),i.Jb(271,0,null,0,23,"GridRow",[["height","27"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(272,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(273,0,null,0,17,"GridItem",[["width","800"]],null,null,null,g.Ac,g.I)),i.Ib(274,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(275,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(276,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(277,0,null,0,1,"SwtLabel",[["id","matchingParty"]],null,null,null,g.Yc,g.fb)),i.Ib(278,4440064,[[19,4],["matchingParty",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(279,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),i.Ib(280,4440064,null,0,d.A,[i.r,d.i],null,null),(t()(),i.Jb(281,0,null,0,1,"SwtTextInput",[["editable","true"],["id","matchingPartyTxtInput"],["width","200"]],null,null,null,g.kd,g.sb)),i.Ib(282,4440064,[[50,4],["matchingPartyTxtInput",4]],0,d.Rb,[i.r,d.i],{id:[0,"id"],width:[1,"width"],editable:[2,"editable"]},null),(t()(),i.Jb(283,0,null,0,3,"GridItem",[["paddingLeft","10"]],null,null,null,g.Ac,g.I)),i.Ib(284,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(285,0,null,0,1,"SwtButton",[["id","matchingPartyButton"],["label","..."],["width","30"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.preSearchParties()&&i);return i},g.Mc,g.T)),i.Ib(286,4440064,[[54,4],["matchingPartyButton",4]],0,d.cb,[i.r,d.i],{id:[0,"id"],width:[1,"width"],label:[2,"label"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(287,0,null,0,3,"GridItem",[["paddingLeft","10"]],null,null,null,g.Ac,g.I)),i.Ib(288,4440064,null,0,d.A,[i.r,d.i],{paddingLeft:[0,"paddingLeft"]},null),(t()(),i.Jb(289,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","matchingPartyLabel"]],null,null,null,g.Yc,g.fb)),i.Ib(290,4440064,[[20,4],["matchingPartyLabel",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),i.Jb(291,0,null,0,3,"GridItem",[["width","6%"]],null,null,null,g.Ac,g.I)),i.Ib(292,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(293,0,null,0,1,"SwtButton",[["id","saveButton"],["width","60"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.savePreAdvice()&&i);return i},g.Mc,g.T)),i.Ib(294,4440064,[[55,4],["saveButton",4]],0,d.cb,[i.r,d.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(295,0,null,0,36,"SwtFieldSet",[["id","fieldSet2"],["style","height: 70%; width: 100%;  padding-bottom: 5px;color:blue;"]],null,null,null,g.Vc,g.cb)),i.Ib(296,4440064,[[34,4],["fieldSet2",4]],0,d.ob,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(297,0,null,0,34,"VBox",[["height","100%"],["paddingLeft","5"],["width","100%"]],null,null,null,g.od,g.vb)),i.Ib(298,4440064,null,0,d.ec,[i.r,d.i,i.T],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(299,0,null,0,28,"GridRow",[["height","30"],["top","3"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(300,4440064,null,0,d.B,[i.r,d.i],{top:[0,"top"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(301,0,null,0,26,"GridItem",[["height","100%"],["width","100%"]],null,null,null,g.Ac,g.I)),i.Ib(302,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(303,0,null,0,9,"GridItem",[["height","100%"],["width","73%"]],null,null,null,g.Ac,g.I)),i.Ib(304,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(305,0,null,0,3,"GridItem",[["height","100%"],["width","120"]],null,null,null,g.Ac,g.I)),i.Ib(306,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(307,0,null,0,1,"SwtLabel",[["id","entity1"]],null,null,null,g.Yc,g.fb)),i.Ib(308,4440064,[[21,4],["entity1",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(309,0,null,0,3,"GridItem",[["height","100%"],["width","90%"]],null,null,null,g.Ac,g.I)),i.Ib(310,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(311,0,null,0,1,"SwtComboBox",[["dataLabel","entityList1"],["id","entityCombo1"],["width","200"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var n=!0,u=t.component;"window:mousewheel"===e&&(n=!1!==i.Tb(t,312).mouseWeelEventHandler(l.target)&&n);"change"===e&&(n=!1!==u.updateData()&&n);return n},g.Pc,g.W)),i.Ib(312,4440064,[[27,4],["entityCombo1",4]],0,d.gb,[i.r,d.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),i.Jb(313,0,null,0,14,"GridItem",[["height","100%"],["width","27%"]],null,null,null,g.Ac,g.I)),i.Ib(314,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(315,0,null,0,3,"GridItem",[["height","100%"],["width","60"]],null,null,null,g.Ac,g.I)),i.Ib(316,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(317,0,null,0,1,"SwtLabel",[["id","inputBy"]],null,null,null,g.Yc,g.fb)),i.Ib(318,4440064,[[22,4],["inputBy",4]],0,d.vb,[i.r,d.i],{id:[0,"id"]},null),(t()(),i.Jb(319,0,null,0,8,"GridItem",[["height","100%"],["paddingLeft","10"],["width","85%"]],null,null,null,g.Ac,g.I)),i.Ib(320,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(321,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","user"]],null,[[null,"change"]],function(t,e,l){var i=!0,n=t.component;"change"===e&&(i=!1!==n.updateData()&&i);return i},g.ed,g.lb)),i.Ib(322,4440064,[[42,4],["user",4]],1,d.Hb,[y.c,i.r,d.i],{id:[0,"id"],align:[1,"align"]},{change_:"change"}),i.Zb(*********,64,{radioItems:1}),(t()(),i.Jb(324,0,null,0,1,"SwtRadioItem",[["groupName","user"],["id","allRadio"],["selected","true"],["value","A"],["width","90"]],null,null,null,g.fd,g.mb)),i.Ib(325,4440064,[[64,4],[43,4],["allRadio",4]],0,d.Ib,[i.r,d.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),i.Jb(326,0,null,0,1,"SwtRadioItem",[["groupName","user"],["id","currentRadio"],["value","C"],["width","100"]],null,null,null,g.fd,g.mb)),i.Ib(327,4440064,[[64,4],[44,4],["currentRadio",4]],0,d.Ib,[i.r,d.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),i.Jb(328,0,null,0,3,"GridRow",[["height","100%"],["top","3"],["width","100%"]],null,null,null,g.Bc,g.J)),i.Ib(329,4440064,null,0,d.B,[i.r,d.i],{top:[0,"top"],width:[1,"width"],height:[2,"height"]},null),(t()(),i.Jb(330,0,null,0,1,"SwtCanvas",[["border","false"],["height","100%"],["id","preAdviceGridContainer"],["styleName","canvasWithGreyBorder"],["width","100%"]],null,null,null,g.Nc,g.U)),i.Ib(331,4440064,[[2,4],["preAdviceGridContainer",4]],0,d.db,[i.r,d.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],height:[3,"height"],border:[4,"border"]},null),(t()(),i.Jb(332,0,null,0,20,"SwtCanvas",[["height","4.5%"],["width","100%"]],null,null,null,g.Nc,g.U)),i.Ib(333,4440064,null,0,d.db,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),i.Jb(334,0,null,0,18,"HBox",[["width","100%"]],null,null,null,g.Dc,g.K)),i.Ib(335,4440064,null,0,d.C,[i.r,d.i],{width:[0,"width"]},null),(t()(),i.Jb(336,0,null,0,7,"HBox",[["paddingLeft","5"],["width","90%"]],null,null,null,g.Dc,g.K)),i.Ib(337,4440064,null,0,d.C,[i.r,d.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),i.Jb(338,0,null,0,1,"SwtButton",[["id","importButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.openImportMvtScreen()&&i);return i},g.Mc,g.T)),i.Ib(339,4440064,[[56,4],["importButton",4]],0,d.cb,[i.r,d.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(340,0,null,0,1,"SwtButton",[["enabled","false"],["id","mvtButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.openMvtDisplayScreen()&&i);return i},g.Mc,g.T)),i.Ib(341,4440064,[[57,4],["mvtButton",4]],0,d.cb,[i.r,d.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(342,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.updateData()&&i);return i},g.Mc,g.T)),i.Ib(343,4440064,[[58,4],["refreshButton",4]],0,d.cb,[i.r,d.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(344,0,null,0,8,"HBox",[["horizontalAlign","right"],["paddingLeft","5"],["width","10%"]],null,null,null,g.Dc,g.K)),i.Ib(345,4440064,null,0,d.C,[i.r,d.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(t()(),i.Jb(346,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,l){var i=!0,n=t.component;"click"===e&&(i=!1!==n.closeHandler()&&i);return i},g.Mc,g.T)),i.Ib(347,4440064,[[59,4],["closeButton",4]],0,d.cb,[i.r,d.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),i.Jb(348,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),i.Jb(349,0,null,null,1,"DataExport",[["id","dataExport"]],null,null,null,g.Sc,g.Z)),i.Ib(350,4440064,[[61,4],["dataExport",4]],0,d.kb,[d.i,i.r],{id:[0,"id"]},null),(t()(),i.Jb(351,0,null,0,1,"SwtLoadingImage",[],null,null,null,g.Zc,g.gb)),i.Ib(352,114688,[[60,4],["loadingImage",4]],0,d.xb,[i.r],null,null)],function(t,e){t(e,62,0,"100%","100%");t(e,64,0,"100%","100%","5","5","5");t(e,66,0,"fieldSet1");t(e,68,0,"100%","100%","5"),t(e,70,0);t(e,72,0,"780");t(e,74,0,"100%","26");t(e,76,0,"65%");t(e,78,0,"300");t(e,80,0,"120");t(e,82,0,"entity"),t(e,84,0);t(e,86,0,"entityList","200","entityCombo");t(e,88,0,"50");t(e,90,0,"entityDesc","normal");t(e,92,0,"100%","25");t(e,94,0,"65%");t(e,96,0,"300");t(e,98,0,"120");t(e,100,0,"positionLevel"),t(e,102,0);t(e,104,0,"PositionLevelList","60","positionCombo","false");t(e,106,0,"50");t(e,108,0,"positionName","normal");t(e,110,0,"100%","25");t(e,112,0,"95%");t(e,114,0,"535","2");t(e,116,0,"120");t(e,118,0,"valueDate"),t(e,120,0);t(e,122,0,"valueDateField","70");t(e,124,0,"100");t(e,126,0,"right");t(e,128,0,"fieldSet");t(e,130,0,"predictStatus","vertical");t(e,133,0,"includedRadio","predictStatus","I","true");t(e,135,0,"excludedRadio","predictStatus","E");t(e,137,0,"cancelledRadio","predictStatus","C");t(e,139,0,"100%","25");t(e,141,0,"95%");t(e,143,0,"535");t(e,145,0,"120");t(e,147,0,"type"),t(e,149,0);t(e,151,0,"typeRadios","horizontal");t(e,154,0,"cashRadio","typeRadios","C","true");t(e,156,0,"securitiesRadio","typeRadios","U");t(e,158,0,"400");t(e,160,0,"110");t(e,162,0,"postingDate"),t(e,164,0);t(e,166,0,"postingDateField","70");t(e,168,0,"100%","27");t(e,170,0,"535");t(e,172,0,"120");t(e,174,0,"amount"),t(e,176,0);t(e,178,0,"currencyList","80","currencyCombo");t(e,180,0,"10");t(e,182,0,"0-9mtb.,","amountTxtInput","right","230","true");t(e,184,0,"10");t(e,186,0,"signList","50","signCombo");t(e,188,0,"400");t(e,190,0,"110");t(e,192,0,"productType");t(e,194,0,"160");t(e,196,0,"16","productTypeTxtInput","200","true");t(e,198,0,"100%","26");t(e,200,0,"70%");t(e,202,0,"120");t(e,204,0,"account"),t(e,206,0);t(e,208,0,"collAcctList","385","accountCombo","false");t(e,210,0,"15");t(e,212,0,"accountLabel","normal");t(e,214,0,"100%","26");t(e,216,0,"70%");t(e,218,0,"120");t(e,220,0,"bookCode"),t(e,222,0);t(e,224,0,"collBookCode","200","bookCodeCombo","false");t(e,226,0,"10");t(e,228,0,"bookCodeLabel","normal");t(e,230,0,"100%","26");t(e,232,0,"70%");t(e,234,0,"120");t(e,236,0,"reference"),t(e,238,0);t(e,240,0,"35","referenceTxtInput","385","true");t(e,242,0,"100%","26");t(e,244,0,"70%");t(e,246,0,"120");t(e,248,0,"counterParty"),t(e,250,0);t(e,252,0,"counterPartyTxtInput","200","true");t(e,254,0,"10");t(e,256,0,"counterPartyButton","30","...",!0);t(e,258,0,"10");t(e,260,0,"counterPartyLabel","normal");t(e,262,0,"100%","26");t(e,264,0,"70%");t(e,266,0,"120"),t(e,268,0);t(e,270,0,"35","counterPartyTxtInput1","385","true");t(e,272,0,"100%","27");t(e,274,0,"800");t(e,276,0,"120");t(e,278,0,"matchingParty"),t(e,280,0);t(e,282,0,"matchingPartyTxtInput","200","true");t(e,284,0,"10");t(e,286,0,"matchingPartyButton","30","...",!0);t(e,288,0,"10");t(e,290,0,"matchingPartyLabel","normal");t(e,292,0,"6%");t(e,294,0,"saveButton","60",!0);t(e,296,0,"fieldSet2");t(e,298,0,"100%","100%","5");t(e,300,0,"3","100%","30");t(e,302,0,"100%","100%");t(e,304,0,"73%","100%");t(e,306,0,"120","100%");t(e,308,0,"entity1");t(e,310,0,"90%","100%");t(e,312,0,"entityList1","200","entityCombo1");t(e,314,0,"27%","100%");t(e,316,0,"60","100%");t(e,318,0,"inputBy");t(e,320,0,"85%","100%","10");t(e,322,0,"user","horizontal");t(e,325,0,"allRadio","90","user","A","true");t(e,327,0,"currentRadio","100","user","C");t(e,329,0,"3","100%","100%");t(e,331,0,"preAdviceGridContainer","canvasWithGreyBorder","100%","100%","false");t(e,333,0,"100%","4.5%");t(e,335,0,"100%");t(e,337,0,"90%","5");t(e,339,0,"importButton",!0);t(e,341,0,"mvtButton","false",!0);t(e,343,0,"refreshButton",!0);t(e,345,0,"right","10%","5");t(e,347,0,"closeButton",!0);t(e,350,0,"dataExport"),t(e,352,0)},null)}function E(t){return i.dc(0,[(t()(),i.Jb(0,0,null,null,1,"app-pre-advice-input",[],null,null,null,O,N)),i.Ib(1,4440064,null,0,s,[d.i,i.r],null,null)],function(t,e){t(e,1,0)},null)}var j=i.Fb("app-pre-advice-input",s,E,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);