{"code": "(window.webpackJsonp=window.webpackJsonp||[]).push([[102],{\"2nix\":function(t,e,i){\"use strict\";i.r(e);var a=i(\"CcnG\"),o=i(\"mrSG\"),l=i(\"ZYCi\"),n=i(\"6blF\"),r=(i(\"0GgQ\"),i(\"447K\")),s=i(\"ik3b\"),c=i(\"8Y0L\"),d=i(\"755f\"),h=i(\"7EaE\"),u=(i(\"EVdn\"),i(\"R1Kr\")),m=function(t){function e(e,i){var a=t.call(this,i,e)||this;return a.commonService=e,a.element=i,a.jsonReader=new r.L,a.inputData=new r.G(a.commonService),a.inputDataForUpdateMMA=new r.G(a.commonService),a.widthData=new r.G(a.commonService),a.ordertData=new r.G(a.commonService),a.alertingData=new r.G(a.commonService),a.msdColsData=new r.G(a.commonService),a.cancelExport=new r.G(a.commonService),a.doExport=new r.G(a.commonService),a.lockMovements=new r.G(a.commonService),a.filterConfData=new r.G(a.commonService),a.updateRefreshRate=new r.G(a.commonService),a.baseURL=r.Wb.getBaseURL(),a.actionMethod=\"\",a.actionPath=\"\",a.requestParams=[],a.comboOpen=!1,a.comboChange=!1,a.obtainCellStatus=!1,a.versionNumber=\"1.1.037\",a.testDate=\"\",a.metaBaseURL=\"\",a.groupBaseURL=\"\",a.bookBaseURL=\"\",a.method=\"\",a.pageUrl=\"\",a.screenId=\"\",a.menuItemId=\"\",a.monitorTypeOnLoad=\"\",a.callFromParent=\"\",a.entityId1=\"\",a.currencyId1=\"\",a.datefr=\"\",a.methodName=\"\",a.access=\"\",a.initialinputscreen=\"\",a.initialscreen=\"\",a.confirmFlag=!1,a.matchFlag=!1,a.suspendFlag=!1,a.reconFlag=!1,a.selectedMovements=[],a.deselectedMovements=[],a.columnNamesArr=[],a.faultEvntFlag=!1,a.currentUser=r.x.call(\"eval\",\"currentUser\"),a.cGridSelMvmt=null,a.menuAccessId=r.x.call(\"eval\",\"menuAccessId\"),a.updateFontSize=new r.G(a.commonService),a.fontValue=\"\",a.fontLabel=\"\",a.fontRequest=\"\",a.currentFontSize=\"\",a.actionMethodName=\"\",a.buttonBarHideFlag=!0,a.isCalculationFinished=!0,a.noneLabel=r.Wb.getPredictMessage(\"msd.noneFilter\",null),a.adhocLabel=r.Wb.getPredictMessage(\"msd.adhocFilter\",null),a.currentFilterConf=\"\",a.dateBehaviour=\"\",a.lastFilterAction=null,a.lastUsedFilter=null,a.fromDeleteFilter=!1,a.dateFormat=\"\",a.screenVersion=new r.V(a.commonService),a.screenName=r.x.call(\"getBundle\",\"text\",\"label-movementSummaryDisplay\",\"Movement Summary Display\"),a.selectedList=\"\",a.selectedMvmts=[],a.count=1,a.tooltipMvtId=null,a.tooltipFacilityId=null,a.selectedNodeId=null,a.treeLevelValue=null,a.tooltipOtherParams=[],a.profilesList=[],a.columnsNewWidths=\"\",a.columnsNewOrders=\"\",a.columnDefinitionsTempArray=[],a.profileAddCols=[],a.openFlag=!1,a.formatIsoTime=\"yyyy-mm-dd hh24:mi:ss\",a.addColumnsGridData=[],a.msdDisplayColumnsData=[],a.multiMvtUpdateNbr=50,a.isPageChanged=!1,a.keepSelected=!1,a.lastSelectedTooltipParams=null,a.eventsCreated=!1,a.customTooltip=null,a.swtAlert=new r.bb(e),window.Main=a,a}return o.d(e,t),e.prototype.ngOnInit=function(){instanceElement=this,this.cGrid=this.dataGridContainer.addChild(r.hb),this.cGrid.id=\"msdGrid\",this.cGrid.allowMultipleSelection=!0,this.cGrid.paginationComponent=this.numStepper,this.cGrid.onPaginationChanged=this.numpager.bind(this),this.cGrid.lockedColumnCount=2,this.exportContainer.enabled=!1,this.cGrid.clientSideFilter=!1,this.cGrid.clientSideSort=!1},e.ngOnDestroy=function(){instanceElement=null,window.Main=null},e.prototype.onLoad=function(){var t=this;this.msdDisplayColumnsData=r.x.call(\"eval\",\"msdDisplayColsList\")?JSON.parse(r.x.call(\"eval\",\"msdDisplayColsList\")):[],this.multiMvtUpdateNbr=r.x.call(\"eval\",\"multiMvtUpdateNbr\")?Number(r.x.call(\"eval\",\"multiMvtUpdateNbr\")):20,console.log(\"\\ud83d\\ude80 ~ MovementSummaryDisplay ~ onLoad ~ this.multiMvtUpdateNbr:\",this.multiMvtUpdateNbr),this.filterConfData.cbResult=function(e){t.saveResult(e)},this.filterConfData.cbFault=this.saveFault.bind(this),this.filterConfData.encodeURL=!1,this.initializeMenus(),this.method=r.x.call(\"eval\",\"method\"),this.dateFormat=r.x.call(\"eval\",\"dateFormat\"),this.cGrid.onRowClick=function(e){t.isPageChanged&&null!=t.bottomGrid&&0!=t.bottomGrid.dataProvider.length?t.isPageChanged=!1:setTimeout(function(){t.cellLogic(e)},0)},n.a.fromEvent(document.body,\"click\").subscribe(function(e){t.positionX=e.clientX,t.positionY=e.clientY}),this.cGrid.ITEM_CLICK.subscribe(function(e){setTimeout(function(){t.itemClickFunction(e)},0)}),this.cGrid.columnWidthChanged.subscribe(function(e){t.columnWidthChange(e)}),this.cGrid.columnOrderChanged.subscribe(function(e){t.columnOrderChange(e)}),this.testDate=r.x.call(\"eval\",\"testDate\"),console.log(\"\\ud83d\\ude80 ~ MovementSummaryDisplay ~ onLoad ~ this.testDate:\",this.testDate),this.monitorTypeOnLoad=r.x.call(\"eval\",\"monitorType\"),this.callFromParent=r.x.call(\"eval\",\"calledFromParent\"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.currencyThreshold.enabled=!1,this.imgShowHideButtonBar.toolTip=r.Wb.getPredictMessage(\"label.showButtonBar\",null),this.methodName=r.x.call(\"eval\",\"methodName\"),console.log(\"\\ud83d\\ude80 ~ MovementSummaryDisplay ~ onLoad ~ this.methodName:\",this.methodName),this.actionMethodName=r.x.call(\"eval\",\"outstandingMethodName\"),\"search\"!=this.methodName&&\"E\"==r.x.call(\"eval\",\"initialscreen\")?(this.refreshButton=this.buttonBox.addChild(r.cb),this.refreshButton.id=\"refreshButton\",this.refreshButton.width=\"70\",this.refreshButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-refresh\",\"Refresh window\"),this.refreshButton.label=r.x.call(\"getBundle\",\"text\",\"button-refresh\",\"Refresh\"),this.refreshButton.click=function(e){t.keepSelected=!0,t.dataRefresh(null)},this.filterButton=this.buttonBox.addChild(r.cb),this.filterButton.id=\"filterButton\",this.filterButton.width=\"60\",this.filterButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-filter\",\"Search movements\"),this.filterButton.label=r.x.call(\"getBundle\",\"text\",\"button-filter\",\"Filter\"),this.filterButton.click=function(e){t.filter()},this.noteButton=this.buttonBox.addChild(r.cb),this.noteButton.id=\"noteButton\",this.noteButton.width=\"60\",this.noteButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-notes\",\"Movement notes\"),this.noteButton.label=r.x.call(\"getBundle\",\"text\",\"button-notes\",\"Notes\"),this.noteButton.enabled=!1,this.noteButton.click=function(e){t.openNotes()},this.movementButton=this.buttonBox.addChild(r.cb),this.movementButton.id=\"movementButton\",this.movementButton.width=\"70\",this.movementButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-movement\",\"Show selected movement in detail\"),this.movementButton.label=r.x.call(\"getBundle\",\"text\",\"button-movement\",\"Mvmnt\"),this.movementButton.enabled=!1,this.movementButton.click=function(e){t.openMovement()},this.messageButton=this.buttonBox.addChild(r.cb),this.messageButton.id=\"messageButton\",this.messageButton.width=\"70\",this.messageButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-message\",\"Messages on selected movement\"),this.messageButton.label=r.x.call(\"getBundle\",\"text\",\"button-message\",\"Message\"),this.messageButton.enabled=!1,this.messageButton.click=function(e){t.openMessages()},this.matchButton=this.buttonBox.addChild(r.cb),this.matchButton.id=\"matchButton\",this.matchButton.width=\"60\",this.matchButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-makeOfferedMatch\",\"Make Offered  Match\"),this.matchButton.label=r.x.call(\"getBundle\",\"text\",\"button-match\",\"Match\"),this.matchButton.click=function(e){t.match()},this.matchButton.enabled=!1,this.confirmButton=this.buttonBox.addChild(r.cb),this.confirmButton.id=\"confirmButton\",this.confirmButton.width=\"70\",this.confirmButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-confirm\",\"Confirm\"),this.confirmButton.label=r.x.call(\"getBundle\",\"text\",\"button-confirm\",\"Confirm match\"),this.confirmButton.click=function(e){t.confirm()},this.confirmButton.enabled=!1,this.suspendButton=this.buttonBox.addChild(r.cb),this.suspendButton.id=\"suspendButton\",this.suspendButton.width=\"70\",this.suspendButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-suspend\",\"Suspend match\"),this.suspendButton.label=r.x.call(\"getBundle\",\"text\",\"button-suspend\",\"Suspend\"),this.suspendButton.click=function(e){t.suspend()},this.suspendButton.enabled=!1,this.reconButton=this.buttonBox.addChild(r.cb),this.reconButton.id=\"reconButton\",this.reconButton.width=\"70\",this.reconButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-recon\",\"Reconcile Match\"),this.reconButton.label=r.x.call(\"getBundle\",\"text\",\"button-recon\",\"Recon\"),this.reconButton.click=function(e){t.reconcile()},this.reconButton.enabled=!1,this.removeButton=this.buttonBox.addChild(r.cb),this.removeButton.id=\"removeButton\",this.removeButton.width=\"70\",this.removeButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-remove\",\"Remove\"),this.removeButton.label=r.x.call(\"getBundle\",\"text\",\"button-remove\",\"Remove\"),this.removeButton.click=function(e){t.remove()},this.removeButton.enabled=!1,this.optionButton=this.buttonBox.addChild(r.cb),this.optionButton.id=\"optionButton\",this.optionButton.width=\"70\",this.optionButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-options\",\"Options\"),this.optionButton.label=r.x.call(\"getBundle\",\"text\",\"button-options\",\"Options\"),this.optionButton.click=function(e){t.fontSettingHandler()},console.log(\"\\ud83d\\ude80 ~ MovementSummaryDisplay ~ onLoad ~ this.updateButton:\"),this.updateButton=this.buttonBox.addChild(r.cb),this.updateButton.id=\"updateButton\",this.updateButton.width=\"70\",this.updateButton.enabled=!1,this.updateButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-update\",\"Change Options\"),this.updateButton.label=r.x.call(\"getBundle\",\"text\",\"button-update\",\"Options\"),this.updateButton.click=function(e){t.openMultiMvtActions()},this.closeButton=this.buttonBox.addChild(r.cb),this.closeButton.id=\"closeButton\",this.closeButton.width=\"60\",this.closeButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-close\",\"Close\"),this.closeButton.label=r.x.call(\"getBundle\",\"text\",\"button-close\",\"Close window\"),this.closeButton.click=function(e){t.closeHandler()}):(this.refreshButton=this.buttonBox.addChild(r.cb),this.refreshButton.width=\"70\",this.refreshButton.id=\"refreshButton\",this.refreshButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-refresh\",\"Refresh window\"),this.refreshButton.label=r.x.call(\"getBundle\",\"text\",\"button-refresh\",\"Refresh\"),this.refreshButton.click=function(e){t.keepSelected=!0,t.dataRefresh()},\"search\"==this.methodName&&\"E\"!=r.x.call(\"eval\",\"initialscreen\")&&\"bookmonitor\"!=r.x.call(\"eval\",\"initialscreen\")||(this.filterButton=this.buttonBox.addChild(r.cb),this.filterButton.id=\"filterButton\",this.filterButton.width=\"60\",this.filterButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-filter\",\"Search movements\"),this.filterButton.label=r.x.call(\"getBundle\",\"text\",\"button-filter\",\"Filter\"),this.filterButton.click=function(e){t.filter()}),this.noteButton=this.buttonBox.addChild(r.cb),this.noteButton.id=\"noteButton\",this.noteButton.width=\"60\",this.noteButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-notes\",\"Movement notes\"),this.noteButton.label=r.x.call(\"getBundle\",\"text\",\"button-notes\",\"Notes\"),this.noteButton.enabled=!1,this.noteButton.click=function(e){t.openNotes()},this.movementButton=this.buttonBox.addChild(r.cb),this.movementButton.id=\"movementButton\",this.movementButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-movement\",\"Show selected movement in detail\"),this.movementButton.label=r.x.call(\"getBundle\",\"text\",\"button-movement\",\"Mvmnt\"),this.movementButton.enabled=!1,this.movementButton.width=\"70\",this.movementButton.click=function(e){t.openMovement()},this.messageButton=this.buttonBox.addChild(r.cb),this.messageButton.id=\"messageButton\",this.messageButton.width=\"70\",this.messageButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-message\",\"Messages on selected movement\"),this.messageButton.label=r.x.call(\"getBundle\",\"text\",\"button-message\",\"Message\"),this.messageButton.enabled=!1,this.messageButton.click=function(e){t.openMessages()},this.optionButton=this.buttonBox.addChild(r.cb),this.optionButton.id=\"optionButton\",this.optionButton.width=\"70\",this.optionButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-options\",\"Change Options\"),this.optionButton.label=r.x.call(\"getBundle\",\"text\",\"button-options\",\"Options\"),this.optionButton.click=function(e){t.fontSettingHandler()},this.updateButton=this.buttonBox.addChild(r.cb),this.updateButton.id=\"updateButton\",this.updateButton.width=\"70\",this.updateButton.enabled=!1,this.updateButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-update\",\"Change Options\"),this.updateButton.label=r.x.call(\"getBundle\",\"text\",\"button-update\",\"Options\"),this.updateButton.click=function(e){t.openMultiMvtActions()},this.closeButton=this.buttonBox.addChild(r.cb),this.closeButton.id=\"closeButton\",this.closeButton.width=\"60\",this.closeButton.toolTip=r.x.call(\"getBundle\",\"tip\",\"button-close\",\"Close\"),this.closeButton.label=r.x.call(\"getBundle\",\"text\",\"button-close\",\"Close window\"),this.closeButton.click=function(e){t.closeHandler()}),this.actionPath=\"outstandingmovement.do?\",this.actionMethod=\"method=\"+this.actionMethodName,\"search\"==this.actionMethodName?(this.filterArea.removeAllChildren(),this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdSearch\")+\"&movementType=\"+r.x.call(\"eval\",\"movementTypeSearch\")+\"&sign=\"+r.x.call(\"eval\",\"signSearch\")+\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusSearch\")+\"&amountover=\"+r.x.call(\"eval\",\"amountoverSearch\")+\"&amountunder=\"+r.x.call(\"eval\",\"amountunderSearch\")+\"&archiveId=\"+r.x.call(\"eval\",\"archiveIdSearch\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&paymentChannelId=\"+r.x.call(\"eval\",\"paymentChannelIdSearch\")+\"&beneficiaryId=\"+r.x.call(\"eval\",\"beneficiaryIdSearch\")+\"&custodianId=\"+r.x.call(\"eval\",\"custodianIdSearch\")+\"&positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")+\"&accountId=\"+r.x.call(\"eval\",\"accountIdSearch\")+\"&group=\"+r.x.call(\"eval\",\"groupSearch\")+\"&metaGroup=\"+r.x.call(\"eval\",\"metaGroupSearch\")+\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeSearch\")+\"&valueFromDateAsString=\"+r.x.call(\"eval\",\"valueFromDateAsStringSearch\")+\"&valueToDateAsString=\"+r.x.call(\"eval\",\"valueToDateAsStringSearch\")+\"&timefrom=\"+r.x.call(\"eval\",\"timefromSearch\")+\"&timeto=\"+r.x.call(\"eval\",\"timetoSearch\")+\"&reference=\"+r.x.call(\"eval\",\"referenceSearch\")+\"&messageId=\"+r.x.call(\"eval\",\"messageIdSearch\")+\"&inputDateAsString=\"+r.x.call(\"eval\",\"inputDateAsStringSearch\")+\"&counterPartyId=\"+r.x.call(\"eval\",\"counterPartyIdSearch\")+\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusSearch\")+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenSearch\")+\"&accountClass=\"+r.x.call(\"eval\",\"accountClassSearch\")+\"&isAmountDiffer=\"+r.x.call(\"eval\",\"isAmountDifferSearch\")+\"&referenceFlag=\"+r.x.call(\"eval\",\"referenceFlagSearch\")+\"&matchingparty=\"+r.x.call(\"eval\",\"matchingparty\")+\"&uetr=\"+r.x.call(\"eval\",\"uetr\")+\"&producttype=\"+r.x.call(\"eval\",\"producttype\")+\"&postingDateFrom=\"+r.x.call(\"eval\",\"postingDateFrom\")+\"&postingDateTo=\"+r.x.call(\"eval\",\"postingDateTo\")+\"&selectedMovementsAmount=\"+r.x.call(\"eval\",\"selectedMovementsAmountSearch\")+\"&openFlag=\"+r.x.call(\"eval\",\"openFlag\")+\"&extBalStatus=\"+r.x.call(\"eval\",\"extBalStatusSearch\"),this.actionMethod+=\"&extraFilter=\"+r.x.call(\"eval\",\"extraFilter\"),null!=r.x.call(\"eval\",\"scenarioId\")&&(this.actionMethod+=\"&scenarioId=\"+r.x.call(\"eval\",\"scenarioId\"),this.actionMethod+=\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThreshold\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"))):\"getBookMonitorMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdBook\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeBook\")+\"&selectedTabIndex=\"+r.x.call(\"eval\",\"selectedTabIndexBook\")+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenBook\"):\"displayOpenMovements\"==this.actionMethodName?(this.actionMethod=this.actionMethod+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenOpenMovements\")+\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&date=\"+r.x.call(\"eval\",\"dateOpenMovements\")+\"&entityId=\"+r.x.call(\"eval\",\"entityIdOpenMovements\")+\"&menuAccessId=\"+this.menuAccessId+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdOpenMovements\"),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\"))):\"getCurrencyMonitorMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenCurrency\")+\"&entityId=\"+r.x.call(\"eval\",\"entityIdCurrency\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&callfromcurrency=y\":\"getAccountMonitorMvmnts\"==this.actionMethodName?(this.actionMethod=this.actionMethod+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenAccount\")+\"&entityId=\"+r.x.call(\"eval\",\"entityIdAccount\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\")+\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\")+\"&accountType=\"+r.x.call(\"eval\",\"accountTypeAccount\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdAccount\"),r.x.call(\"eval\",\"applyCurrencyThresholdIndAccount\")):\"getUnsettledYesterdayMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdUnsetteled\")+\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")+\"&totalCount=\"+r.x.call(\"eval\",\"totalCountUnsetteled\")+\"&roleId=\"+r.x.call(\"eval\",\"roleId\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdUnsetteled\"):\"getOpenUnexpectedMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdUnexpected\")+\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&totalCount=\"+r.x.call(\"eval\",\"totalCountUnexpected\")+\"&roleId=\"+r.x.call(\"eval\",\"roleId\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdUnexpected\"):\"getBackValuedMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdBackvalue\")+\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&totalCount=\"+r.x.call(\"eval\",\"totalCountBackvalue\")+\"&roleId=\"+r.x.call(\"eval\",\"roleId\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdBackvalue\"):\"getMvmntsfromWorkFlowMonitor\"==this.actionMethodName&&(this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdWorkflow\")+\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")+\"&totalCount=\"+r.x.call(\"eval\",\"totalCountWorkflow\")+\"&roleId=\"+r.x.call(\"eval\",\"roleId\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusWorkflow\")+\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusWorkflow\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdWorkflow\")+\"&tabIndicator=\"+r.x.call(\"eval\",\"tabIndicatorWorkflow\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&linkFlag=\"+r.x.call(\"eval\",\"linkFlagWorkflow\")),this.actionMethod+=\"&currentFilterConf=\"+r.Z.encode64(this.noneLabel),this.currentFilterConf=this.noneLabel,this.filterButton&&this.filterButton.setStyle(\"color\",\"#0B333C\",this.filterButton.domElement),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.cGrid.onFilterChanged=this.dataRefreshGrid.bind(this),this.cGrid.onSortChanged=this.dataRefreshGrid.bind(this),r.v.subscribe(function(e){t.export(e.type,e.startPage,e.noOfPages)}),this.exportContainer.exportCancelFunction=this.exportCancel.bind(this)},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,null);var t=new r.n(\"Show JSON\");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=r.Eb.createPopUp(this,r.M,{jsonData:this.lastRecievedJSON}),this.showJSONPopup.width=\"700\",this.showJSONPopup.title=r.Wb.getPredictMessage(\"screen.showJSON\",null),this.showJSONPopup.height=\"400\",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.display()},e.prototype.unlockMvmnt=function(t){if(\"search\"!=this.methodName&&\"E\"==this.initialinputscreen)for(var e=0;e<this.bottomGrid.dataProvider.length;e++){var i=\"\",a=r.x.call(\"checkLockOnServer\",this.bottomGrid.dataProvider[e].movement);this.currentUser.toLowerCase()!=a.toLowerCase()&&\"true\"!=a.toString()||(i=r.x.call(\"unlockMovementOnServer\",this.bottomGrid.dataProvider[e].movement).toString(),h.a.parseBooleanValue(i)||this.swtAlert.warning(r.Wb.getPredictMessage(\"label.movementCannotBeUnlocked\",null),\"Warning\"))}},e.prototype.refreshParent=function(){this.dataRefresh(null)},e.prototype.filterScreen=function(t,e){void 0===e&&(e=null),this.requestParams=[],\"Reset\"==t?(this.filterButton.setStyle(\"color\",\"#0B333C\",this.filterButton.domElement),this.currentFilterConf=this.noneLabel):\"Filter\"==t?this.currentFilterConf=this.adhocLabel:\"saveFilter\"!=t&&(this.filterButton.setStyle(\"color\",\"red\",this.filterButton.domElement),this.currentFilterConf==this.noneLabel&&(this.currentFilterConf=this.adhocLabel));var i=\"\",a=\"\",o=\"\";if(o=this.currencyThreshold.selected?\"Y\":\"N\",null!=this.bottomGrid){for(var l=0;l<this.bottomGrid.dataProvider.length;l++)a+=this.bottomGrid.dataProvider[l].movement+\",\";for(var n=0;n<this.bottomGrid.dataProvider.length;n++){var s=\"\",c=r.x.call(\"checkLockOnServer\",this.bottomGrid.dataProvider[n].movement);this.currentUser.toLowerCase()!=c.toLowerCase()&&\"true\"!=c.toString()||(s=r.x.call(\"unlockMovementOnServer\",this.bottomGrid.dataProvider[n].movement).toString(),h.a.parseBooleanValue(s)||this.swtAlert.warning(r.Wb.getPredictMessage(\"label.movementCannotBeUnlocked\",null),\"Warning\"))}}this.actionPath=\"outstandingmovement.do?\",\"saveFilter\"==t?(this.lastFilterAction=\"saveFilter\",i+=\"method=saveMSDFilterConfig\",this.requestParams.filterId=e,i+=\"&filterAccountType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),i+=\"&searchDateBehaviour=\"+this.dateBehaviour,i+=\"&dateFormat=\"+this.dateFormat,i+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")):\"deleteFilter\"==t?(this.lastFilterAction=\"deleteFilter\",i+=\"method=deleteFilter\",this.requestParams.filterId=e,i+=\"&filterAccountType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\")):i+=\"method=filterOutMovementSummary\",i+=\"&entityId=\"+this.entityCombo.selectedLabel,i+=\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\"),i+=\"&selectedList=\"+a,i+=\"&applyCurrencyThreshold=\"+o,i+=\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort,i+=\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter,i+=\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\"),i+=\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\"),i+=\"&amountover=\"+r.x.call(\"amountOver\"),i+=\"&amountunder=\"+r.x.call(\"amountUnder\"),i+=\"&group=\"+r.x.call(\"getGroup\"),i+=\"&metaGroup=\"+r.x.call(\"getMetagroup\"),i+=\"&initialscreen=\"+r.x.call(\"eval\",\"initialscreen\"),i+=\"&extraFilter=\"+r.x.call(\"eval\",\"extraFilter\"),\"\"==r.x.call(\"valueFromDate\")?i+=\"&valueFromDate=\"+r.x.call(\"eval\",\"tabPressed\"):i+=\"&valueFromDate=\"+r.x.call(\"valueFromDate\"),\"\"==r.x.call(\"valueToDate\")&&\"E\"!=this.initialinputscreen?i+=\"&valueToDate=\"+r.x.call(\"eval\",\"tabPressed\"):i+=\"&valueToDate=\"+r.x.call(\"valueToDate\"),i+=\"&timefrom=\"+r.x.call(\"timefrom\"),i+=\"&timeto=\"+r.x.call(\"timeto\"),i+=\"&inputDate=\"+r.x.call(\"inputDate\"),i+=\"&reference=\"+r.x.call(\"reference\"),i+=\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\"),i+=\"&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),i+=\"&refFlagFilterSearch=\"+r.x.call(\"referenceFlag\"),i+=\"&currentFilterConf=\"+r.Z.encode64(this.currentFilterConf),null!=r.x.call(\"eval\",\"workflow\")&&(i+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),\"E\"!=this.initialinputscreen?i+=\"&nodata=Y\":i+=\"&nodata=N\",\"saveFilter\"==t?(this.filterConfData.url=this.baseURL+this.actionPath+i,this.filterConfData.send(this.requestParams)):\"deleteFilter\"==t?(this.filterConfData.url=this.baseURL+this.actionPath+i,this.filterConfData.send(this.requestParams)):(this.inputData.url=this.baseURL+this.actionPath+i,this.inputData.send(this.requestParams))},e.prototype.openNotes=function(){var t=!1;if(this.cGrid.selectedIndices.length>0&&(t=!0),t){var e=this.cGrid.dataProvider[this.cGrid.selectedIndex].movement;r.x.call(\"movementNotesFromSearch\",\"notes\",e,\"\")}else if(1==this.bottomGrid.dataProvider.length&&0==this.cGrid.selectedIndices.length){for(var i=void 0,a=0;a<this.bottomGrid.dataProvider.length;a++)i=this.bottomGrid.dataProvider[a].movement;r.x.call(\"movementNotesFromSearch\",\"notes\",i,\"\")}},e.prototype.openMessages=function(){var t,e,i,a=!1;if(this.cGrid.selectedIndices.length>0&&(a=!0),a){var o=this.cGrid.dataProvider[this.cGrid.selectedIndex].movement;e=(t=r.x.call(\"setMsgButtonStatus\",o)).substring(0,t.indexOf(\"|\")),i=t.substring(t.indexOf(\"|\")+1,t.length),r.x.call(\"openMsgDisplayWindow\",o,e,i)}else if(1==this.bottomGrid.dataProvider.length&&0==this.cGrid.selectedIndices.length){for(var l=void 0,n=0;n<this.bottomGrid.dataProvider.length;n++)l=this.bottomGrid.dataProvider[n].movement;e=(t=r.x.call(\"setMsgButtonStatus\",l)).substring(0,t.indexOf(\"|\")),i=t.substring(t.indexOf(\"|\")+1,t.length),r.x.call(\"openMsgDisplayWindow\",l,e,i)}},e.prototype.openMovement=function(){var t,e=!1;if(this.cGrid.selectedIndices.length>0&&(e=!0),\"search\"==this.methodName&&(this.cGridSelMvmt=this.cGrid.dataProvider[this.cGrid.selectedIndex].movement),e){var i=this.cGrid.dataProvider[this.cGrid.selectedIndex].movement;t=this.entityCombo.selectedLabel.toString(),r.x.call(\"showMvmnt\",\"showMovementDetails\",i,t,\"\")}else if(1==this.bottomGrid.dataProvider.length&&0==this.cGrid.selectedIndices.length){for(var a=void 0,o=0;o<this.bottomGrid.dataProvider.length;o++)a=this.bottomGrid.dataProvider[o].movement;t=this.entityCombo.selectedLabel.toString(),r.x.call(\"showMvmnt\",\"showMovementDetails\",a,t,\"\")}},e.prototype.cellLogic=function(t){var e=this;try{var i=\"\",a=!1,o=!1,l=!1,n=!0,s=[],c=[],d=[],u=0,m=void 0,v=0,p=[],b=[],g=this.jsonReader.getScreenAttributes().currencyFormat,f=\"\";try{if(\"search\"!=this.methodName&&\"E\"==this.initialinputscreen){this.selectedMovements=[];for(var x=0;x<this.bottomGrid.dataProvider.length;x++)this.selectedMovements.push(\"\"+this.bottomGrid.dataProvider[x].movement);if(this.cGrid.selectedIndices.length>0&&\"\"==r.x.call(\"eval\",\"archiveIdSearch\"))for(var S=0;S<this.cGrid.selectedIndices.length;S++){for(var y=!1,M=0;M<this.selectedMovements.length;M++)this.selectedMovements[M].toString()==this.cGrid.dataProvider[this.cGrid.selectedIndices[S]].movement&&(y=!0);if(!y){new Object;var P=\"\";if(P=r.x.call(\"lockMovementOnServer\",this.cGrid.dataProvider[this.cGrid.selectedIndices[S]].movement).toString(),!h.a.parseBooleanValue(P)&&this.currentUser.toLowerCase()!=P.toLowerCase()){this.swtAlert.warning(\"Movement \"+this.cGrid.dataProvider[this.cGrid.selectedIndices[S]].movement+\" is in use by \"+P,\"Warning\");for(var G=this.cGrid.selectedIndices,C=0;C<G.length;C++)G[C]==this.cGrid.selectedIndices[S]&&(G.splice(C,1),S-=1);this.cGrid.selectedIndices=[],this.cGrid.selectedIndices=G}}}for(var I=[],B=[],T=0;T<this.cGrid.selectedIndices.length;T++)I.push(\"\"+this.cGrid.dataProvider[this.cGrid.selectedIndices[T]].movement),B.push(this.cGrid.dataProvider[this.cGrid.selectedIndices[T]].account);for(var D=0;D<this.selectedMovements.length;D++){for(var A=!1,w=!1,F=0;F<this.cGrid.dataProvider.length;F++)this.selectedMovements[D]==this.cGrid.dataProvider[F].movement&&(w=!0);w||(A=!0);for(var R=0;R<I.length;R++)w&&this.selectedMovements[D]==I[R]&&(A=!0);if(!A){new Object;var k=\"\",L=r.x.call(\"checkLockOnServer\",this.selectedMovements[D]);this.currentUser.toLowerCase()!=L.toLowerCase()&&\"true\"!=L.toString()||(k=r.x.call(\"unlockMovementOnServer\",this.selectedMovements[D]).toString(),h.a.parseBooleanValue(k)||this.swtAlert.warning(r.Wb.getPredictMessage(\"label.movementCannotBeUnlocked\",null),\"Warning\"))}}new Object;var O=\"\";if(I.length>0)for(R=0;R<I.length;R++)O=r.x.call(\"accountAccessConfirm\",I[R].toString(),this.entityCombo.selectedLabel.toString()).toString(),h.a.parseBooleanValue(O)||(n=!1)}this.selectedMovements=[];for(var N=0;N<this.cGrid.selectedIndices.length;N++)this.selectedMovements.push(\"\"+this.cGrid.dataProvider[this.cGrid.selectedIndices[N]].movement);if(\"search\"!=this.methodName&&\"displayOpenMovements\"==this.actionMethodName){var E=0;this.bottomGrid.selectedIndex>=0?(this.removeButton.enabled=!0,this.removeButton.buttonMode=!0):(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1);for(var j=0;j<this.bottomGrid.dataProvider.length;j++){for(var U=!0,W=0;W<this.cGrid.dataProvider.length;W++)this.bottomGrid.dataProvider[j].movement==this.cGrid.dataProvider[W].movement&&(U=!1);U&&E++}if(this.bottomGrid.dataProvider.length-E!=this.cGrid.selectedItems.length||1==this.cGrid.selectedItems.length){for(var q=0;q<this.bottomGrid.dataProvider.length;q++){for(var _={pos:{content:this.bottomGrid.dataProvider[q].pos},value:{content:this.bottomGrid.dataProvider[q].value},amount:{content:this.bottomGrid.dataProvider[q].amount},sign:{content:this.bottomGrid.dataProvider[q].sign},ccy:{content:this.bottomGrid.dataProvider[q].ccy},ref1:{content:this.bottomGrid.dataProvider[q].ref1},account:{content:this.bottomGrid.dataProvider[q].account},input:{content:this.bottomGrid.dataProvider[q].input},cparty:{content:this.bottomGrid.dataProvider[q].cparty},pred:{content:this.bottomGrid.dataProvider[q].pred},ext:{content:this.bottomGrid.dataProvider[q].ext},status:{content:this.bottomGrid.dataProvider[q].status},matchid:{content:this.bottomGrid.dataProvider[q].matchid},source:{content:this.bottomGrid.dataProvider[q].source},format:{content:this.bottomGrid.dataProvider[q].format},notes:{content:this.bottomGrid.dataProvider[q].notes},beneficiary:{content:this.bottomGrid.dataProvider[q].beneficiary},ref2:{content:this.bottomGrid.dataProvider[q].ref2},ref3:{content:this.bottomGrid.dataProvider[q].ref3},movement:{content:this.bottomGrid.dataProvider[q].movement},book:{content:this.bottomGrid.dataProvider[q].book},custodian:{content:this.bottomGrid.dataProvider[q].custodian},xref:{content:this.bottomGrid.dataProvider[q].xref},update_date:{content:this.bottomGrid.dataProvider[q].update_date},matchingparty:{content:this.bottomGrid.dataProvider[q].matchingparty},producttype:{content:this.bottomGrid.dataProvider[q].producttype},postingdate:{content:this.bottomGrid.dataProvider[q].postingdate},extra_text1:{content:this.bottomGrid.dataProvider[q].extra_text1},alerting:{content:this.bottomGrid.dataProvider[q].alerting},ilmfcast:{content:this.bottomGrid.dataProvider[q].ilmfcast},uetr:{content:this.bottomGrid.dataProvider[q].uetr}},Y=0;Y<this.profileAddCols.length;Y++){var V=this.profileAddCols[Y].toString().replace(\"[\",\"\").replace(\"]\",\"\"),J={content:this.bottomGrid.dataProvider[q][V]};_[V]=J}s.push(_),d.push(_),\"\"!=this.bottomGrid.dataProvider[q].matchid&&c.push(\"\"+this.bottomGrid.dataProvider[q].matchid)}for(var z=[],H=0;H<s.length;H++)for(var Z=0;Z<this.cGrid.dataProvider.length;Z++)if(s[H].movement.content==this.cGrid.dataProvider[Z].movement){for(var K=!0,X=0;X<this.cGrid.selectedIndices.length;X++)this.cGrid.dataProvider[this.cGrid.selectedIndices[X]].movement==s[H].movement.content&&(K=!1);K&&z.push(\"\"+s[H].movement.content)}var Q=[];if(z.length>0){for(var $=function(t){for(var e=0;e<d.length;e++)if(z[t]==d[e].movement.content){var i=tt.bottomGrid.getFilteredItems().find(function(e){return e.movement==z[t]});null!=i&&Q.push(i);var a=tt.selectedMvmts.indexOf(d[e].movement.content.toString(),0);a>-1&&tt.selectedMvmts.splice(a,1)}},tt=this,et=0;et<z.length;et++)$(et);try{Q.length>0&&(this.bottomGrid.dataviewObj.beginUpdate(),this.bottomGrid.angularGridInstance.gridService.deleteItems(Q),this.bottomGrid.dataviewObj.endUpdate())}catch(t){console.log(\"error in delete item\",t)}}for(var it=!1,at=[],ot=[],lt=0;lt<this.cGrid.selectedIndices.length;lt++)if(at.push(\"\"+this.cGrid.dataProvider[this.cGrid.selectedIndices[lt]].movement),\"\"!=this.cGrid.dataProvider[this.cGrid.selectedIndices[lt]].matchid&&c.push(\"\"+this.cGrid.dataProvider[this.cGrid.selectedIndices[lt]].matchid),c.length>1)for(var nt=0;nt<c.length-1;nt++)if(c[nt]!=c[nt+1]){it=!0;break}if(it){var rt=[],st=0;q=0;for(q=0;q<this.bottomGrid.dataProvider.length;q++)for(st=0;st<this.cGrid.dataProvider.length;st++)this.bottomGrid.dataProvider[q].movement==this.cGrid.dataProvider[st].movement&&(rt.push(st),ot.push(this.cGrid.dataProvider[st].movement));this.cGrid.selectedIndices=rt;for(var ct=at.filter(function(t){return!ot.includes(t)}),dt=0,ht=ct;dt<ht.length;dt++){var ut=ht[dt];A=r.x.call(\"unlockMovementOnServer\",ut)}this.swtAlert.warning(r.Wb.getPredictMessage(\"label.selectedOnlyMatchedItems\",null),\"Warning\")}else for(var mt=0;mt<this.cGrid.selectedIndices.length;mt++){for(var vt=!0,pt=0;pt<s.length;pt++)if(this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].movement==s[pt].movement.content){vt=!1;break}if(vt){var bt=void 0;bt={pos:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].pos},value:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].value},amount:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].amount},sign:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].sign},ccy:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].ccy},ref1:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].ref1},account:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].account},input:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].input},cparty:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].cparty},pred:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].pred},ext:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].ext},status:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].status},matchid:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].matchid},source:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].source},format:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].format},notes:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].notes},beneficiary:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].beneficiary},ref2:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].ref2},ref3:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].ref3},movement:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].movement},book:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].book},custodian:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].custodian},xref:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].xref},update_date:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].update_date},matchingparty:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].matchingparty},producttype:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].producttype},postingdate:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].postingdate},extra_text1:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].extra_text1},alerting:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].alerting},positionlevel:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].slickgrid_rowcontent.pos.positionlevel},id:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].movement},ilmfcast:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].ilmfcast},uetr:{content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].uetr}};for(var gt=0;gt<this.profileAddCols.length;gt++){V=this.profileAddCols[gt].toString().trim(),J={content:this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]][V]};bt[V]=J}this.bottomGrid.appendRow(bt,!0),this.bottomGrid.dataProvider[this.bottomGrid.dataProvider.length-1].slickgrid_rowcontent.amount.negative=this.cGrid.dataProvider[this.cGrid.selectedIndices[mt]].slickgrid_rowcontent.amount.negative,this.bottomGrid.selectedIndex=-1}}}if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0){var ft,xt;this.bottomGrid.dataset=this.bottomGrid.dataProvider;for(var St=0,yt=\"\",Mt=0,Pt=0,Gt=[],Ct=[],It=0;It<this.bottomGrid.dataProvider.length;It++){if(this.bottomGrid.dataProvider[It].id=It,this.bottomGrid.dataProvider[It].slickgrid_rowcontent&&this.bottomGrid.dataProvider[It].slickgrid_rowcontent.id&&(this.bottomGrid.dataProvider[It].slickgrid_rowcontent.id.content=It),this.bottomGrid.dataProvider[It].num=It,this.bottomGrid.dataProvider[It].slickgrid_rowcontent&&this.bottomGrid.dataProvider[It].slickgrid_rowcontent.num&&(this.bottomGrid.dataProvider[It].slickgrid_rowcontent.num.content=It),ft=this.bottomGrid.dataProvider[It].positionlevel?parseInt(this.bottomGrid.dataProvider[It].positionlevel):parseInt(this.bottomGrid.dataProvider[It].slickgrid_rowcontent.pos.positionlevel),Ct.push(ft),xt=this.bottomGrid.dataProvider[It].sign,yt=this.bottomGrid.dataProvider[It].amount,\"currencyPat1\"==g){b=yt.split(\",\"),yt=\"\";for(var Bt=0;Bt<b.length;Bt++)yt+=b[Bt]}else{b=yt.split(\".\"),yt=\"\";for(var Tt=0;Tt<b.length;Tt++)yt+=b[Tt];yt=yt.replace(\",\",\".\")}switch(St=parseFloat(yt),\"D\"==xt?u-=St:u+=St,ft){case 1:null==Gt[0]&&(Gt[0]=0),Gt[0]=\"D\"==xt?Gt[0]-St:Gt[0]+St;break;case 2:null==Gt[1]&&(Gt[1]=0),Gt[1]=\"D\"==xt?Gt[1]-St:Gt[1]+St;break;case 3:null==Gt[2]&&(Gt[2]=0),Gt[2]=\"D\"==xt?Gt[2]-St:Gt[2]+St;break;case 4:null==Gt[3]&&(Gt[3]=0),Gt[3]=\"D\"==xt?Gt[3]-St:Gt[3]+St;break;case 5:null==Gt[4]&&(Gt[4]=0),Gt[4]=\"D\"==xt?Gt[4]-St:Gt[4]+St;break;case 6:null==Gt[5]&&(Gt[5]=0),Gt[5]=\"D\"==xt?Gt[5]-St:Gt[5]+St;break;case 7:null==Gt[6]&&(Gt[6]=0),Gt[6]=\"D\"==xt?Gt[6]-St:Gt[6]+St;break;case 8:null==Gt[7]&&(Gt[7]=0),Gt[7]=\"D\"==xt?Gt[7]-St:Gt[7]+St;break;case 9:null==Gt[8]&&(Gt[8]=0),Gt[8]=\"D\"==xt?Gt[8]-St:Gt[8]+St}}var Dt=0;for(lt=0;lt<Gt.length;lt++)null!=Gt[lt]&&(0==Dt?(Dt=-1,Mt=Gt[lt],Pt=Gt[lt]):(\"Y\",Gt[lt]<Pt&&(Pt=Gt[lt]),Gt[lt]>Mt&&(Mt=Gt[lt])));var At={precision:\"\",rounding:\"\"};At.precision=\"2\",At.rounding=\"nearest\";for(var wt=0,Ft=!1,Rt=0;Rt<Ct.length;Rt++)if(Ct[Rt]&&Ct[Rt+1]&&Ct[Rt]!==Ct[Rt+1]){Ft=!0;break}if(0==(wt=Mt-Pt))this.diffText.text=Ft?\"currencyPat1\"==g?\"0.00\":\"0,00\":\"\";else{var kt=this.addZeroes(wt.toString());if(-1==kt.indexOf(\".\"))kt+=\"00\";else{var Lt;Lt=kt.split(\".\"),kt=Lt[0]+Lt[1]}this.diffText.text=Ft?r.x.call(\"expandAmtDifference\",kt,g,\"\"):\"\"}}}else for(var Ot,Nt=0;Nt<this.cGrid.selectedIndices.length;Nt++){if(m=this.cGrid.dataProvider[this.cGrid.selectedIndices[Nt]].sign,Ot=this.cGrid.dataProvider[this.cGrid.selectedIndices[Nt]].amount,\"currencyPat1\"==g){p=Ot.split(\",\"),Ot=\"\";for(Bt=0;Bt<p.length;Bt++)Ot+=p[Bt]}else{p=Ot.split(\".\"),Ot=\"\";for(Tt=0;Tt<p.length;Tt++)Ot+=p[Tt];Ot=Ot.replace(\",\",\".\")}v=parseFloat(Ot),\"D\"==m?u-=v:u+=v}if(null!=this.bottomGrid)for(var Et=0;Et<this.bottomGrid.dataProvider.length;Et++)\"CONFIRMED\"==(i=this.bottomGrid.dataProvider[Et].status).split(\" \")[0]?a=!0:\"SUSPENDED\"==i.split(\" \")[0]?o=!0:\"OUTSTANDING\"==i.split(\" \")[0]&&(l=!0);if(n&&null!=this.bottomGrid&&(this.reconButton.enabled=!0,this.reconButton.buttonMode=!0,0==l?(0==a?(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0):(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),0==o?(this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0):(this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1)):(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0,this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0),this.matchButton.enabled=!0,this.matchButton.buttonMode=!0),this.cGrid.selectedIndices.length>1)this.messageButton.enabled=!1,this.messageButton.buttonMode=!1,this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.reconButton&&(this.reconButton.enabled=!0,this.reconButton.buttonMode=!1);else if(1==this.cGrid.selectedIndices.length){var jt=this.cGrid.dataProvider[this.cGrid.selectedIndex].movement,Ut=r.x.call(\"setMsgButtonStatus\",jt);\"0\"==Ut.substring(0,Ut.indexOf(\"|\"))||\"\"!=r.x.call(\"eval\",\"archiveIdSearch\")?(this.messageButton.enabled=!1,this.messageButton.buttonMode=!1):(this.messageButton.enabled=!0,this.messageButton.buttonMode=!0);Ut.substring(Ut.indexOf(\"|\")+1,Ut.length);if(this.movementButton.enabled=!0,this.movementButton.buttonMode=!0,this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,null!=this.bottomGrid){this.bottomGrid.dataProvider.length>1&&(this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.messageButton.enabled=!1,this.messageButton.buttonMode=!1),this.bottomGrid.selectedIndex<0&&(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1);var Wt=this.cGrid.dataProvider[this.cGrid.selectedIndex].status,qt=Wt.split(\" \")[0];\"CONFIRMED\"!=qt&&this.confirmButton&&(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0),\"SUSPENDED\"!=qt&&this.suspendButton&&(this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0),this.matchButton&&(this.matchButton.enabled=!0,this.matchButton.buttonMode=!0)}}else if(this.messageButton.enabled=!1,this.messageButton.buttonMode=!1,this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,null!=this.bottomGrid){if(this.bottomGrid.dataProvider.length>=1){for(var _t=0;_t<this.bottomGrid.dataProvider.length;_t++)\"CONFIRMED\"==(i=this.bottomGrid.dataProvider[_t].status).split(\" \")[0]?a=!0:\"SUSPENDED\"==i.split(\" \")[0]?o=!0:\"OUTSTANDING\"==i.split(\" \")[0]&&(l=!0);0==l?(0==a?(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0):(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),0==o?(this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0):(this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1)):(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0,this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0),this.matchButton.enabled=!0,this.matchButton.buttonMode=!0}else this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.matchButton.enabled=!1,this.matchButton.buttonMode=!1;1==this.bottomGrid.dataProvider.length?(this.movementButton.enabled=!0,this.movementButton.buttonMode=!0,this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,this.messageButton.enabled=!0,this.messageButton.buttonMode=!0):0==this.bottomGrid.dataProvider.length&&(this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.messageButton.enabled=!1,this.messageButton.buttonMode=!1)}var Yt=!1,Vt=null,Jt=!1;if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0){for(lt=0;lt<this.bottomGrid.dataProvider.length;lt++)if(this.bottomGrid.dataProvider[lt].matchid.toString().length>=0&&!0,null==Vt&&\"\"!=this.bottomGrid.dataProvider[lt].matchid)Vt=this.bottomGrid.dataProvider[lt].matchid;else if(\"\"!=this.bottomGrid.dataProvider[lt].matchid&&Vt!=this.bottomGrid.dataProvider[lt].matchid){Yt=!0;break}if(Jt?this.matchButton.toolTip=r.Wb.getPredictMessage(\"tooltip.makeOfferedMatch\",null):!Yt&&this.bottomGrid.dataProvider.length>0?this.matchButton.toolTip=r.Wb.getPredictMessage(\"tooltip.displayMatch\",null):this.matchButton.toolTip=\"\",Yt){this.matchButton.enabled=!1,this.matchButton.buttonMode=!1,this.matchButton.toolTip=\"\";A=r.x.call(\"unlockMovementOnServer\",this.bottomGrid.dataProvider[lt].movement);this.swtAlert.warning(r.Wb.getPredictMessage(\"label.selectedOnlyMatchedItems\",null),\"Warning\")}else this.bottomGrid.dataProvider.length>=1&&(this.matchButton.enabled=!0,this.matchButton.buttonMode=!0)}var zt=!1;if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0){for(var Ht=0;Ht<this.bottomGrid.dataProvider.length;Ht++)this.bottomGrid.dataProvider[Ht].matchid.length>0&&(zt=!0);zt?(this.reconButton.enabled=!1,this.reconButton.buttonMode=!1):0!=this.bottomGrid.dataProvider.length&&(this.reconButton.enabled=!0,this.reconButton.buttonMode=!0)}new Object;var Zt=\"\";if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0)for(var Kt=0;Kt<this.bottomGrid.dataProvider.length;Kt++)Zt=r.x.call(\"accountAccessConfirm\",this.bottomGrid.dataProvider[Kt].movement.toString(),this.entityCombo.selectedLabel.toString()).toString(),h.a.parseBooleanValue(Zt)||(n=!1);n||(this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.matchButton.enabled=!1,this.matchButton.buttonMode=!1);var Xt=!1;if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0){for(Et=0;Et<this.bottomGrid.dataProvider.length;Et++)if(\"OUTSTANDING\"==(i=this.bottomGrid.dataProvider[Et].status).split(\" \")[0]){Xt=!0;break}n&&!Yt&&this.bottomGrid.dataProvider.length>0||!n&&!Xt&&!Yt?this.matchButton.enabled=!0:this.matchButton.enabled=!1}var Qt=this.addZeroes(u.toString());if(-1==Qt.indexOf(\".\"))Qt+=\"00\";else{var $t;$t=Qt.split(\".\"),Qt=$t[0]+$t[1]}if(f=r.x.call(\"expandAmtDifference\",Qt,g,\"\"),u<0&&f.length>0&&\"-\"!=f.charAt(0)?this.totalSelectedValue.text=\"-\"+r.x.call(\"expandAmtDifference\",Qt,g,\"\"):this.totalSelectedValue.text=r.x.call(\"expandAmtDifference\",Qt,g,\"\"),this.cGrid.selectedIndices.length>0){this.selectedList=\"\";for(var te=0;te<this.cGrid.selectedIndices.length;te++)(\"\"==this.selectedList||-1==this.selectedList.indexOf(this.cGrid.dataProvider[this.cGrid.selectedIndices[te]].movement)&&-1==this.selectedMvmts.indexOf(this.cGrid.dataProvider[this.cGrid.selectedIndices[te]].movement))&&(this.selectedMvmts.push(\"\"+this.cGrid.dataProvider[this.cGrid.selectedIndices[te]].movement),this.selectedList+=this.cGrid.dataProvider[this.cGrid.selectedIndices[te]].movement+\",\");this.selectedMvmts=this.selectedMvmts.filter(function(t,e,i){if(i.indexOf(t)===e)return t})}if(null!=this.bottomGrid){var ee=\"\";for(mt=0;mt<this.bottomGrid.dataProvider.length;mt++)ee+=this.bottomGrid.dataProvider[mt].movement+\",\";r.x.call(\"setSelectedMovementForLock\",ee)}var ie=[];this.bottomGrid&&this.bottomGrid.dataProvider&&this.bottomGrid.dataProvider.length>0?ie=this.bottomGrid.dataProvider:this.cGrid&&this.cGrid.selectedIndices&&this.cGrid.selectedIndices.length>0&&(ie=this.cGrid.selectedIndices.map(function(t){return e.cGrid.dataProvider[t]})),ie.length>=2&&ie.length<=this.multiMvtUpdateNbr&&n?(this.updateButton.enabled=!0,this.checkMvtAccess()):this.updateButton.enabled=!1}catch(t){console.log(\"errror\",t)}}catch(t){console.log(\"error in \",t)}},e.prototype.addZeroes=function(t){t.split(\".\")[1];return Number(t).toFixed(2)},e.prototype.paginationURLConstructor=function(){this.keepSelected=!0;var t=\"\",e=\"\",i=this.jsonReader.getScreenAttributes().access;e=this.currencyThreshold.selected?\"Y\":\"N\";var a=\"\";if(this.initialinputscreen=this.jsonReader.getScreenAttributes().initialinputscreen,\"search\"!=this.methodName&&\"E\"==this.initialinputscreen)for(var o=0;o<this.bottomGrid.dataProvider.length;o++)a+=this.bottomGrid.dataProvider[o].movement+\",\";else this.selectedMvmts=[];if(t+=\"&selectedList=\",t+=a,t+=\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort,t+=\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter,t+=\"&archiveId=\"+r.x.call(\"eval\",\"archiveIdSearch\"),t+=\"&initialinputscreen=\"+this.initialinputscreen,t+=\"&fromFlex=true\",\"search\"==this.methodName||\"E\"!=this.initialinputscreen){if(t+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),\"E\"==this.initialscreen&&(t+=\"&method=displayOpenMovements\"),\"readOnly\"==i&&(t+=\"&method=next\"),\"readOnly\"!=i&&(\"currencymonitor\"!=this.initialinputscreen&&\"entitymonitor\"!=this.initialinputscreen||(t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&balanceType=\",t+=\"&locationId=\"+r.x.call(\"eval\",\"locationIdCurrency\"),t+=\"&method=getCurrencyMonitorMvmnts\"),\"currencymonitor\"!=this.initialinputscreen&&\"entitymonitor\"!=this.initialinputscreen&&(\"accountmonitor\"==this.initialinputscreen&&(t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\"),t+=\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\"),t+=\"&accountType=\"+r.x.call(\"eval\",\"accountTypeAccount\"),t+=\"&method=getAccountMonitorMvmnts\"),\"accountmonitor\"!=this.initialinputscreen&&(\"accountbreakdownmonitor\"==this.initialinputscreen&&(t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\"),t+=\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\"),t+=\"&method=getAccountMonitorMvmnts\"),\"accountbreakdownmonitor\"!=this.initialinputscreen&&(\"bookmonitor\"==this.initialinputscreen&&(t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeBook\"),t+=\"&method=getBookMonitorMvmnts\"),\"bookmonitor\"!=this.initialinputscreen&&(\"mvmntsfromWorkFlowMonitor\"==this.initialinputscreen&&(t+=\"&method=getMvmntsfromWorkFlowMonitor\",t+=\"&linkFlag=\"+r.x.call(\"eval\",\"linkFlagWorkflow\"),t+=\"&tabIndicator=\"+r.x.call(\"eval\",\"tabIndicatorWorkflow\")),\"mvmntsfromWorkFlowMonitor\"!=this.initialinputscreen&&(\"unSettledYesterday\"==this.initialinputscreen&&(t+=\"&method=getUnsettledYesterdayMvmnts\"),\"unSettledYesterday\"!=this.initialinputscreen&&(\"backValuedMvmnts\"==this.initialinputscreen&&(t+=\"&method=getBackValuedMvmnts\",t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")),\"backValuedMvmnts\"!=this.initialinputscreen&&(\"openUnexpectedMvmnts\"==this.initialinputscreen&&(t+=\"&method=getOpenUnexpectedMvmnts\",t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")),\"openUnexpectedMvmnts\"!=this.initialinputscreen))))))))){t+=\"&method=searchnext\";t+=\"&filterCriteria=\"+(\"entityId=\"+r.x.call(\"eval\",\"entityIdSearch\")+\"|movementType=\"+r.x.call(\"eval\",\"movementTypeSearch\")+\"|sign=\"+r.x.call(\"eval\",\"signSearch\")+\"|predictStatus=\"+r.x.call(\"eval\",\"predictStatusSearch\")+\"|amountover=\"+r.x.call(\"eval\",\"amountoverSearch\")+\"|amountunder=\"+r.x.call(\"eval\",\"amountunderSearch\")+\"|currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"|paymentChannelId=\"+r.x.call(\"eval\",\"paymentChannelIdSearch\")+\"|beneficiaryId=\"+r.x.call(\"eval\",\"beneficiaryIdSearch\")+\"|custodianId=\"+r.x.call(\"eval\",\"custodianIdSearch\")+\"|positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")+\"|accountId=\"+r.x.call(\"eval\",\"accountIdSearch\")+\"|group=\"+r.x.call(\"eval\",\"groupSearch\")+\"|metaGroup=\"+r.x.call(\"eval\",\"metaGroupSearch\")+\"|bookCode=\"+r.x.call(\"eval\",\"bookCodeSearch\")+\"|valueFromDateAsString=\"+r.x.call(\"eval\",\"valueFromDateAsStringSearch\")+\"|valueToDateAsString=\"+r.x.call(\"eval\",\"valueToDateAsStringSearch\")+\"|timefrom=\"+r.x.call(\"eval\",\"timefromSearch\")+\"|timeto=\"+r.x.call(\"eval\",\"timetoSearch\")+\"|reference=\"+r.x.call(\"eval\",\"referenceSearch\")+\"|messageId=\"+r.x.call(\"eval\",\"messageIdSearch\")+\"|inputDateAsString=\"+r.x.call(\"eval\",\"inputDateAsStringSearch\")+\"|counterPartyId=\"+r.x.call(\"eval\",\"counterPartyIdSearch\")+\"|matchStatus=\"+r.x.call(\"eval\",\"matchStatusSearch\")+\"|initialinputscreen=\"+this.jsonReader.getScreenAttributes().initialinputscreen+\"|accountClass=\"+r.x.call(\"eval\",\"accountClassSearch\")+\"|producttype=\"+r.x.call(\"eval\",\"producttype\")+\"|uetr=\"+r.x.call(\"eval\",\"uetr\")+\"|matchingparty=\"+r.x.call(\"eval\",\"matchingparty\")+\"|postingDateFrom=\"+r.x.call(\"eval\",\"postingDateFrom\")+\"|postingDateTo=\"+r.x.call(\"eval\",\"postingDateTo\")+\"|positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")),t+=\"&extraFilter=\"+r.x.call(\"eval\",\"extraFilter\"),t+=\"&referenceFlag=\"+r.x.call(\"eval\",\"referenceFlagSearch\"),t+=\"&openFlag=\"+r.x.call(\"eval\",\"openFlag\"),null!=r.x.call(\"eval\",\"scenarioId\")&&(t+=\"&scenarioId=\"+r.x.call(\"eval\",\"scenarioId\"),t+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"))}t+=\"&extBalStatus=\"+r.x.call(\"eval\",\"extBalStatusSearch\")}return\"search\"!=this.methodName&&(\"filterscreen\"==this.initialinputscreen&&(t+=\"&amountover=\"+r.x.call(\"amountOver\"),t+=\"&amountunder=\"+r.x.call(\"amountUnder\"),t+=\"&group=\"+r.x.call(\"getGroup\"),t+=\"&metaGroup=\"+r.x.call(\"getMetagroup\"),\"\"==r.x.call(\"valueFromDate\")?t+=\"&valueFromDate=\"+r.x.call(\"eval\",\"tabPressed\"):t+=\"&valueFromDate=\"+r.x.call(\"valueFromDate\"),t+=\"&valueToDate=\"+r.x.call(\"valueToDate\"),t+=\"&timefrom=\"+r.x.call(\"timefrom\"),t+=\"&timeto=\"+r.x.call(\"timeto\"),t+=\"&inputDate=\"+r.x.call(\"inputDate\"),t+=\"&reference=\"+r.x.call(\"reference\"),t+=\"&refFlagFilterSearch=\"+r.x.call(\"referenceFlag\"),t+=\"&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),t+=\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\"),t+=\"&method=nextfilterOutMovementSummary\",null!=r.x.call(\"eval\",\"workflow\")&&(t+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\"))),\"filterscreen\"!=this.initialinputscreen&&(t+=\"&method=displayOpenMovements\",t+=\"&initialinputscreen=\"+this.initialscreen,t+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"))),t+=\"&menuAccessId=\"+this.menuAccessId,t+=\"&applyCurrencyThreshold=\"+e,t+=\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\"),t+=\"&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),t+=\"&isAmountDiffer=\"+this.diffText.text,t+=\"&selectedMovementsAmount=\",t+=\"&currentFilterConf=\"+r.t.encode64(this.currentFilterConf),t+=\"&extraFilter=\"+r.x.call(\"eval\",\"extraFilter\")},e.prototype.numpager=function(t){if(this.isPageChanged=!0,this.jsonReader.getScreenAttributes().pages.currentPage!=this.numStepper.value)if(isNaN(this.numStepper.value))this.numStepper.value=this.jsonReader.getScreenAttributes().pages.currentPage,this.swtAlert.warning(\"Please enter a valid page number\");else{var e=this.paginationURLConstructor();this.pageUrl=\"maxPage=\"+this.jsonReader.getScreenAttributes().pages.maxPage+\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusWorkflow\")+\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusWorkflow\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&totalCount=\"+this.jsonReader.getRowSize()+\"&roleId=\"+r.x.call(\"eval\",\"roleId\")+\"&date=\"+r.x.call(\"eval\",\"dateOpenMovements\")+\"&pageNoValue=\"+this.numStepper.value+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&entityId=\"+r.x.call(\"eval\",\"entityIdOpenMovements\")+\"&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),null!=r.x.call(\"eval\",\"workflow\")&&(this.pageUrl+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),this.inputData.url=this.baseURL+this.actionPath+this.pageUrl+e,this.inputData.send(this.requestParams)}},e.prototype.dataRefresh=function(t){void 0===t&&(t=null),this.actionPath=\"outstandingmovement.do?\",this.requestParams=[],\"search\"==this.method||\"E\"!=this.initialinputscreen?(this.actionMethod=\"method=searchrefreshMvmntsSummaryDisplay\",this.actionMethod+=\"&openFlag=\"+r.x.call(\"eval\",\"openFlag\"),null!=r.x.call(\"eval\",\"scenarioId\")&&(this.actionMethod+=\"&scenarioId=\"+r.x.call(\"eval\",\"scenarioId\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"))):(this.actionMethod=\"method=refreshMvmntsSummaryDisplay\",this.actionMethod+=\"&openFlag=\"+r.x.call(\"eval\",\"openFlag\"));var e=\"\";if(\"search\"!=this.methodName&&\"E\"==this.initialinputscreen)for(var i=0;i<this.bottomGrid.dataProvider.length;i++)e+=this.bottomGrid.dataProvider[i].movement+\",\";this.actionMethod+=\"filterCombo\"==t?\"&currentPage=1\":\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage,this.actionMethod+=\"&selectedList=\"+e,this.actionMethod+=\"&applyCurrencyThresholdInd=1\",this.actionMethod+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),this.currencyThreshold.selected?this.actionMethod+=\"&applyCurrencyThreshold=N\":this.actionMethod+=\"&applyCurrencyThreshold=Y\",this.actionMethod+=\"&entityId=\"+this.entityCombo.selectedLabel,this.actionMethod+=\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\"),this.actionMethod+=\"&date=\"+r.x.call(\"eval\",\"dateStr\"),this.actionMethod+=\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort,this.actionMethod+=\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter,this.actionMethod+=\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\");var a=this.jsonReader.getScreenAttributes().access;if(this.initialinputscreen=this.jsonReader.getScreenAttributes().initialinputscreen,\"filterscreen\"==this.initialinputscreen&&\"E\"==r.x.call(\"eval\",\"initialscreen\")&&(this.initialinputscreen=\"E\"),this.actionMethod+=\"&initialinputscreen=\"+this.initialinputscreen,\"E\"==this.initialinputscreen&&(this.actionMethod+=\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),(\"search\"==this.methodName||\"E\"!=this.initialinputscreen)&&(this.actionMethod+=\"&maxPage=\"+this.jsonReader.getScreenAttributes().pages.maxPage,this.actionMethod+=\"&totalCount=\"+this.jsonReader.getRowSize(),this.actionMethod+=\"&extBalStatus=\"+r.x.call(\"eval\",\"extBalStatusSearch\"),\"readOnly\"!=a&&(\"currencymonitor\"!=this.initialinputscreen&&\"entitymonitor\"!=this.initialinputscreen||(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&balanceType=\",this.actionMethod+=\"&locationId=\"+r.x.call(\"eval\",\"locationIdCurrency\")),\"currencymonitor\"!=this.initialinputscreen&&\"entitymonitor\"!=this.initialinputscreen&&(\"accountmonitor\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\"),this.actionMethod+=\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\"),this.actionMethod+=\"&accountType=\"+r.x.call(\"eval\",\"accountTypeAccount\")),\"accountmonitor\"!=this.initialinputscreen&&(\"accountbreakdownmonitor\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\"),this.actionMethod+=\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\")),\"accountbreakdownmonitor\"!=this.initialinputscreen&&(\"bookmonitor\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeBook\")),\"bookmonitor\"!=this.initialinputscreen&&(\"mvmntsfromWorkFlowMonitor\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&tabIndicator=\"+r.x.call(\"eval\",\"tabIndicatorWorkflow\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),this.actionMethod+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\"),this.actionMethod+=\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusWorkflow\"),this.actionMethod+=\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusWorkflow\"),this.actionMethod+=\"&linkFlag=\"+r.x.call(\"eval\",\"linkFlagWorkflow\")),\"mvmntsfromWorkFlowMonitor\"!=this.initialinputscreen&&(\"unSettledYesterday\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\",this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),this.actionMethod+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\")),\"unSettledYesterday\"!=this.initialinputscreen&&(\"backValuedMvmnts\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),this.actionMethod+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\")),\"backValuedMvmnts\"!=this.initialinputscreen&&(\"openUnexpectedMvmnts\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),this.actionMethod+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\")),\"openUnexpectedMvmnts\"!=this.initialinputscreen)))))))))){this.actionMethod+=\"&archiveId=\"+r.x.call(\"eval\",\"archiveIdSearch\"),this.actionMethod+=\"&entityId=\"+r.x.call(\"eval\",\"entityIdSearch\")+\"&movementType=\"+r.x.call(\"eval\",\"movementTypeSearch\")+\"&sign=\"+r.x.call(\"eval\",\"signSearch\")+\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusSearch\")+\"&amountover=\"+r.x.call(\"eval\",\"amountoverSearch\")+\"&amountunder=\"+r.x.call(\"eval\",\"amountunderSearch\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&paymentChannelId=\"+r.x.call(\"eval\",\"paymentChannelIdSearch\")+\"&beneficiaryId=\"+r.x.call(\"eval\",\"beneficiaryIdSearch\")+\"&custodianId=\"+r.x.call(\"eval\",\"custodianIdSearch\")+\"&positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")+\"&accountId=\"+r.x.call(\"eval\",\"accountIdSearch\")+\"&group=\"+r.x.call(\"eval\",\"groupSearch\")+\"&metaGroup=\"+r.x.call(\"eval\",\"metaGroupSearch\")+\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeSearch\")+\"&valueFromDateAsString=\"+r.x.call(\"eval\",\"valueFromDateAsStringSearch\")+\"&valueToDateAsString=\"+r.x.call(\"eval\",\"valueToDateAsStringSearch\")+\"&timefrom=\"+r.x.call(\"eval\",\"timefromSearch\")+\"&timeto=\"+r.x.call(\"eval\",\"timetoSearch\")+\"&reference=\"+r.x.call(\"eval\",\"referenceSearch\")+\"&messageId=\"+r.x.call(\"eval\",\"messageIdSearch\")+\"&inputDateAsString=\"+r.x.call(\"eval\",\"inputDateAsStringSearch\")+\"&counterPartyId=\"+r.x.call(\"eval\",\"counterPartyIdSearch\")+\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusSearch\")+\"&initialinputscreen=\",r.x.call(\"eval\",\"referenceFlagSearch\"),r.x.call(\"eval\",\"accountClassSearch\");var o;o=\"entityId=\"+r.x.call(\"eval\",\"entityIdSearch\")+\"|movementType=\"+r.x.call(\"eval\",\"movementTypeSearch\")+\"|sign=\"+r.x.call(\"eval\",\"signSearch\")+\"|predictStatus=\"+r.x.call(\"eval\",\"predictStatusSearch\")+\"|amountover=\"+r.x.call(\"eval\",\"amountoverSearch\")+\"|amountunder=\"+r.x.call(\"eval\",\"amountunderSearch\")+\"|currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"|paymentChannelId=\"+r.x.call(\"eval\",\"paymentChannelIdSearch\")+\"|beneficiaryId=\"+r.x.call(\"eval\",\"beneficiaryIdSearch\")+\"|custodianId=\"+r.x.call(\"eval\",\"custodianIdSearch\")+\"|positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")+\"|accountId=\"+r.x.call(\"eval\",\"accountIdSearch\")+\"|group=\"+r.x.call(\"eval\",\"groupSearch\")+\"|metaGroup=\"+r.x.call(\"eval\",\"metaGroupSearch\")+\"|bookCode=\"+r.x.call(\"eval\",\"bookCodeSearch\")+\"|valueFromDateAsString=\"+r.x.call(\"eval\",\"valueFromDateAsStringSearch\")+\"|valueToDateAsString=\"+r.x.call(\"eval\",\"valueToDateAsStringSearch\")+\"|timefrom=\"+r.x.call(\"eval\",\"timefromSearch\")+\"|timeto=\"+r.x.call(\"eval\",\"timetoSearch\")+\"|reference=\"+r.x.call(\"eval\",\"referenceSearch\")+\"|messageId=\"+r.x.call(\"eval\",\"messageIdSearch\")+\"|inputDateAsString=\"+r.x.call(\"eval\",\"inputDateAsStringSearch\")+\"|counterPartyId=\"+r.x.call(\"eval\",\"counterPartyIdSearch\")+\"|fintrade=\"+r.x.call(\"eval\",\"fintradeSearch\")+\"|matchStatus=\"+r.x.call(\"eval\",\"matchStatusSearch\")+\"|initialinputscreen=\"+this.jsonReader.getScreenAttributes().initialinputscreen+\"|accountClass=\"+r.x.call(\"eval\",\"accountClassSearch\")+\"|producttype=\"+r.x.call(\"eval\",\"producttype\")+\"|uetr=\"+r.x.call(\"eval\",\"uetr\")+\"|matchingparty=\"+r.x.call(\"eval\",\"matchingparty\")+\"|postingDateFrom=\"+r.x.call(\"eval\",\"postingDateFrom\")+\"|postingDateTo=\"+r.x.call(\"eval\",\"postingDateTo\")+\"|positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\"),this.actionMethod+=\"&filterCriteria=\"+o,this.actionMethod+=\"&filterFromSerach=\"+r.x.call(\"eval\",\"filterFromSerachSearch\"),this.actionMethod+=\"&matchingparty=\"+r.x.call(\"eval\",\"matchingparty\")+\"&producttype=\"+r.x.call(\"eval\",\"producttype\")+\"&uetr=\"+r.x.call(\"eval\",\"uetr\")+\"&postingDateFrom=\"+r.x.call(\"eval\",\"postingDateFrom\")+\"&postingDateTo=\"+r.x.call(\"eval\",\"postingDateTo\")}this.actionMethod+=\"&tableScrollbarLeft=&tableScrollbarTop=&scrollbarLeft=&scrollbarTop=&isAmountDiffer=&selectedMovementsAmount=&menuAccessId=\"+this.menuAccessId+\"&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),this.actionMethod+=\"&extraFilter=\"+r.x.call(\"eval\",\"extraFilter\"),this.requestParams.currentFilterConf=r.t.encode64(this.currentFilterConf),this.keepSelected=!0,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.inputDataResult=function(t){var e=this,i=null,a=0,o=\"\";if(this.inputData.isBusy())this.inputData.cbStop();else{if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.entityId=this.entityCombo.selectedLabel,this.currentFilterConf=this.jsonReader.getScreenAttributes().currentFilterConf,\"\"!=this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\")?this.updateValues(r.t.decode64(this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"))):this.initializeValues(),\"Y\"==this.jsonReader.getScreenAttributes().nodata)return void this.dataRefresh(null);this.lastRefTimeLabel.visible=!0,this.lastRefTime.visible=!0,this.lastRefTimeLabel.text=r.x.call(\"getBundle\",\"text\",\"label-lastRefresh\",\"Last Refresh:\"),this.lastRefTimeLabel.setStyle(\"color\",\"black\",this.lastRefTimeLabel.domElement),this.lastRefTime.text=this.jsonReader.getScreenAttributes().lastRefTime,this.totalOverPagesValue.text=this.jsonReader.getScreenAttributes().totalOverPages,this.totalInPageValue.text=this.jsonReader.getScreenAttributes().totalInPage;this.jsonReader.getScreenAttributes().currencyFormat;if(0!=this.jsonReader.getRowSize()?this.exportContainer.enabled=!0:this.exportContainer.enabled=!1,this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON){this.profilesList=this.jsonReader.getSelects().select[1].option,this.currentProfile=this.jsonReader.getScreenAttributes().currentProfile,this.savedProfileId=this.jsonReader.getScreenAttributes().currentProfile,this.useAddColsCheck=this.jsonReader.getScreenAttributes().useAddCols,this.profileAddCols=this.jsonReader.getScreenAttributes().profileAddColsList?this.jsonReader.getScreenAttributes().profileAddColsList.replace(\"[\",\"\").replace(\"]\",\"\").split(\",\"):[],this.addColumnsGridData=this.jsonReader.getScreenAttributes().addColsForFilter?JSON.parse(this.jsonReader.getScreenAttributes().addColsForFilter):\"\";var l=this.jsonReader.getScreenAttributes().pages.maxPage;if(this.entityCombo.setComboData(this.jsonReader.getSelects()),this.filterComboMSD.setComboData(this.jsonReader.getSelects()),this.currentFilterConf==this.adhocLabel&&(this.filterComboMSD.setPrompt(this.adhocLabel),this.filterComboMSD.selectedIndex=-1,this.filterComboMSD.text=\"\"),this.lastSelectedIndex=this.filterComboMSD.selectedIndex,this.enableDisableFilterCombo(),this.selectedEntity.text=this.entityCombo.selectedValue,this.currencyThreshold.selected=this.jsonReader.getScreenAttributes().currencythreshold,this.initialscreen=this.jsonReader.getScreenAttributes().initialinputscreen,this.initialinputscreen=this.jsonReader.getScreenAttributes().initialinputscreen,this.currentFontSize=this.jsonReader.getScreenAttributes().currfontsize,!this.jsonReader.isDataBuilding()){this.cGrid.entityID=this.entityCombo.selectedLabel,this.cGrid.saveColumnOrder=!0,this.cGrid.saveWidths=!0;var n=r.x.call(\"eval\",\"methodName\"),c={columns:this.jsonReader.getColumnData()},d=c.columns.column.find(function(t){return\"value\"===t.dataelement});if(d){var u=this.getDataProvider(this.jsonReader.getGridData().row,\"value\").sort(function(t,i){if(\"(EMPTY)\"===t.value)return-1;if(\"(EMPTY)\"===i.value)return 1;if(\"(NOT EMPTY)\"===t.value)return-1;if(\"(NOT EMPTY)\"===i.value)return 1;if(!t.value)return 1;if(!i.value)return-1;var a=function(t){return\"string\"!=typeof t?\"\":t.includes(\" \")?e.convertToIsoWithTime(t):e.convertToIso(t)},o=a(t.value),l=a(i.value);return o.localeCompare(l)});d.FilterType=\"MultipleSelect\",d.dataProvider=u,d.FilterInputSearch=!0}var m=c.columns.column.find(function(t){return\"account\"===t.dataelement});if(m){var v=this.getDataProvider(this.jsonReader.getGridData().row,\"account\",!1);m.FilterType=\"MultipleSelect\",m.dataProvider=v,m.FilterInputSearch=!0}var p=c.columns.column.find(function(t){return\"ccy\"===t.dataelement});if(p){var b=this.getDataProvider(this.jsonReader.getGridData().row,\"ccy\",!1);p.FilterType=\"MultipleSelect\",p.dataProvider=b,p.FilterInputSearch=!1}if(\"All\"==this.entityCombo.selectedLabel){var g=r.x.call(\"eval\",\"entityList\")?JSON.parse(r.x.call(\"eval\",\"entityList\")):[],f=c.columns.column.find(function(t){return\"entity\"===t.dataelement}),x=this.prepareDataProvider(g);f.FilterType=\"MultipleSelect\",f.dataProvider=x,f.FilterInputSearch=!1}else{if(f=c.columns.column.find(function(t){return\"entity\"===t.dataelement})){x=this.getDataProvider(this.jsonReader.getGridData().row,\"entity\",!1);f.FilterType=\"MultipleSelect\",f.dataProvider=x,f.FilterInputSearch=!0}}var S=c.columns.column.find(function(t){return\"book\"===t.dataelement});if(S){var y=this.getDataProvider(this.jsonReader.getGridData().row,\"book\");S.FilterType=\"MultipleSelect\",S.dataProvider=y,S.FilterInputSearch=!0}var M=c.columns.column.find(function(t){return\"cparty\"===t.dataelement});if(M){var P=this.getDataProvider(this.jsonReader.getGridData().row,\"cparty\");M.FilterType=\"MultipleSelect\",M.dataProvider=P,M.FilterInputSearch=!0}var G=c.columns.column.find(function(t){return\"beneficiary\"===t.dataelement});if(G){var C=this.getDataProvider(this.jsonReader.getGridData().row,\"beneficiary\");G.FilterType=\"MultipleSelect\",G.dataProvider=C,G.FilterInputSearch=!0}var I=c.columns.column.find(function(t){return\"custodian\"===t.dataelement});if(I){var B=this.getDataProvider(this.jsonReader.getGridData().row,\"custodian\");I.FilterType=\"MultipleSelect\",I.dataProvider=B,I.FilterInputSearch=!0}var T=c.columns.column.find(function(t){return\"matchingparty\"===t.dataelement});if(T){var D=this.getDataProvider(this.jsonReader.getGridData().row,\"matchingparty\");T.FilterType=\"MultipleSelect\",T.dataProvider=D,T.FilterInputSearch=!0}var A=c.columns.column.find(function(t){return\"producttype\"===t.dataelement});if(A){var w=this.getDataProvider(this.jsonReader.getGridData().row,\"producttype\");A.FilterType=\"MultipleSelect\",A.dataProvider=w,A.FilterInputSearch=!0}var F=c.columns.column.find(function(t){return\"postingdate\"===t.dataelement});if(F){u=this.getDataProvider(this.jsonReader.getGridData().row,\"postingdate\").sort(function(t,i){if(\"(EMPTY)\"===t.value)return-1;if(\"(EMPTY)\"===i.value)return 1;if(\"(NOT EMPTY)\"===t.value)return-1;if(\"(NOT EMPTY)\"===i.value)return 1;if(!t.value)return 1;if(!i.value)return-1;var a=function(t){return\"string\"!=typeof t?\"\":t.includes(\" \")?e.convertToIsoWithTime(t):e.convertToIso(t)},o=a(t.value),l=a(i.value);return o.localeCompare(l)});F.FilterType=\"MultipleSelect\",F.dataProvider=u,F.FilterInputSearch=!0}var R=c.columns.column.find(function(t){return\"matchid\"===t.dataelement});if(R){var k=this.getDataProvider(this.jsonReader.getGridData().row,\"matchid\");R.FilterType=\"MultipleSelect\",R.dataProvider=k,R.FilterInputSearch=!0}var L=c.columns.column.find(function(t){return\"extra_text1\"===t.dataelement});if(L){var O=this.getDataProvider(this.jsonReader.getGridData().row,\"extra_text1\");L.FilterType=\"MultipleSelect\",L.dataProvider=O,L.FilterInputSearch=!0}var N=c.columns.column.find(function(t){return\"uetr\"===t.dataelement});if(N){var E=this.getDataProvider(this.jsonReader.getGridData().row,\"uetr\");N.FilterType=\"MultipleSelect\",N.dataProvider=E,N.FilterInputSearch=!0}var j=c.columns.column.find(function(t){return\"movement\"===t.dataelement});if(j){var U=this.getDataProvider(this.jsonReader.getGridData().row,\"movement\",!1);j.FilterType=\"MultipleSelect\",j.dataProvider=U,j.FilterInputSearch=!0}var W=c.columns.column.find(function(t){return\"status\"===t.dataelement});if(W){var q=this.prepareDataProvider([\"CONFIRMED\",\"OFFERED\",\"OUTSTANDING\",\"SUSPENDED\",\"RECONCILED\"]);W.FilterType=\"MultipleSelect\",W.dataProvider=q,W.FilterInputSearch=!1}var _=r.x.call(\"eval\",\"posLevelList\")?JSON.parse(r.x.call(\"eval\",\"posLevelList\")):[],Y=c.columns.column.find(function(t){return\"pos\"===t.dataelement}),V=this.prepareDataProvider(_);Y.FilterType=\"MultipleSelect\",Y.dataProvider=V,Y.FilterInputSearch=!1;var J=c.columns.column.find(function(t){return\"sign\"===t.dataelement}),z=this.prepareDataProvider([\"D\",\"C\"]);J.FilterType=\"MultipleSelect\",J.dataProvider=z,J.FilterInputSearch=!1;var H=c.columns.column.find(function(t){return\"pred\"===t.dataelement}),Z=this.prepareDataProvider([\"I\",\"E\",\"C\"]);H.FilterType=\"MultipleSelect\",H.dataProvider=Z,H.FilterInputSearch=!1;var K=c.columns.column.find(function(t){return\"ext\"===t.dataelement}),X=this.prepareDataProvider([\"(EMPTY)\",\"(NOT EMPTY)\",\"I\",\"E\"]);K.FilterType=\"MultipleSelect\",K.dataProvider=X,K.FilterInputSearch=!1;var Q=c.columns.column.find(function(t){return\"ilmfcast\"===t.dataelement}),$=this.prepareDataProvider([\"(EMPTY)\",\"(NOT EMPTY)\",\"I\",\"E\"]);Q.FilterType=\"MultipleSelect\",Q.dataProvider=$,Q.FilterInputSearch=!1;var tt=c.columns.column.find(function(t){return\"ref1\"===t.dataelement});tt&&(tt.FilterType=\"InputSearch\");var et=c.columns.column.find(function(t){return\"ref2\"===t.dataelement});et&&(et.FilterType=\"InputSearch\");var it=c.columns.column.find(function(t){return\"ref3\"===t.dataelement});it&&(it.FilterType=\"InputSearch\");var at=c.columns.column.find(function(t){return\"xref\"===t.dataelement});if(at&&(at.FilterType=\"InputSearch\"),l<=1){var ot=c.columns.column.find(function(t){return\"source\"===t.dataelement}),lt=r.x.call(\"eval\",\"sourcelList\")?JSON.parse(r.x.call(\"eval\",\"sourcelList\")):[];if(ot){var nt=c.columns.column.find(function(t){return\"source\"===t.dataelement}),rt=this.prepareDataProvider(lt);nt.FilterType=\"MultipleSelect\",nt.dataProvider=rt,nt.FilterInputSearch=!1}if(ht=c.columns.column.find(function(t){return\"format\"===t.dataelement})){var st=this.getDataProvider(this.jsonReader.getGridData().row,\"format\");ht.FilterType=\"MultipleSelect\",ht.dataProvider=st,ht.FilterInputSearch=!0}}else{ot=c.columns.column.find(function(t){return\"source\"===t.dataelement}),lt=r.x.call(\"eval\",\"sourcelList\")?JSON.parse(r.x.call(\"eval\",\"sourcelList\")):[];if(ot){var ct=c.columns.column.find(function(t){return\"source\"===t.dataelement});rt=this.prepareDataProvider(lt);ct.FilterType=\"MultipleSelect\",ct.dataProvider=rt,ct.FilterInputSearch=!1}var dt=r.x.call(\"eval\",\"formatList\")?JSON.parse(r.x.call(\"eval\",\"formatList\")):[],ht=c.columns.column.find(function(t){return\"format\"===t.dataelement});if(ot){st=this.prepareDataProvider(dt);ht.FilterType=\"MultipleSelect\",ht.dataProvider=st,ht.FilterInputSearch=!1}}if(\"N\"!=this.useAddColsCheck){var ut=function(t){0;var e=t.split(\"*\")[0],i=c.columns.column.find(function(t){return t.dataelement===e});if(i){var a=mt.getDataProvider(mt.jsonReader.getGridData().row,e);i.FilterType=\"MultipleSelect\",i.dataProvider=a,i.FilterInputSearch=!0}},mt=this;for(var vt in this.addColumnsGridData)ut(vt)}this.cGrid.CustomGrid(c);for(var pt=0;pt<this.cGrid.columnDefinitions.length;pt++){var bt=this.cGrid.columnDefinitions[pt];if(\"alerting\"==bt.field){var gt=\"./\"+r.x.call(\"eval\",\"alertOrangeImage\"),ft=\"./\"+r.x.call(\"eval\",\"alertRedImage\");\"Normal\"==this.currentFontSize?bt.properties={enabled:!1,columnName:\"alerting\",imageEnabled:gt,imageCritEnabled:ft,imageDisabled:\"\",_toolTipFlag:!0,style:\" display: block; margin-left: auto; margin-right: auto;\"}:bt.properties={enabled:!1,columnName:\"alerting\",imageEnabled:gt,imageCritEnabled:ft,imageDisabled:\"\",_toolTipFlag:!0,style:\"height:15px; width:15px; display: block; margin-left: auto; margin-right: auto;\"},this.cGrid.columnDefinitions[pt].editor=null,this.cGrid.columnDefinitions[pt].formatter=s.a}}if(\"search\"!=n&&\"displayOpenMovements\"==this.actionMethodName?this.dataGridContainer.height=\"65%\":(this.dataGridContainer.height=\"100%\",this.keepSelected=!1),this.cGrid.gridData=this.jsonReader.getGridData(),this.keepSelected)try{if(this.selectedMvmts.length>0){var xt=[],St=Array.of(this.jsonReader.getGridData().row);St=St[0];var yt=function(t){if(St){var i=St.findIndex(function(i){return i.movement.content==e.selectedMvmts[t]});-1!=i&&xt.push(i)}};for(pt=0;pt<this.selectedMvmts.length;pt++)yt(pt);setTimeout(function(){xt.length>0?e.cGrid.selectedIndices=xt:e.isPageChanged=!1},0)}else this.isPageChanged=!1}catch(Ge){console.log(\"error in set selecteedindeices after grid recreate\",Ge)}else this.cGrid.selectedIndices=[],this.cGrid.selectedIndex=-1,this.isPageChanged=!1;this.cGrid.setRowSize=this.jsonReader.getRowSize();this.jsonReader.getScreenAttributes().selectedFilter;this.numStepper.value=this.jsonReader.getScreenAttributes().pages.currentPage;var Mt=this.jsonReader.getScreenAttributes().selectedSort,Pt=Mt.substr(0,Mt.indexOf(\"|\")),Gt=[],Ct=[],It=void 0;for(It in this.jsonReader.getColumnData().column.dataelement){var Bt=this.jsonReader.getColumnData().column.columnNumber[It],Tt=this.jsonReader.getColumnData().column.dataelement[It];Gt[It]=Bt,Ct[It]=Tt}for(var Dt=0;Dt<Gt.length;Dt++)Gt[Dt]==Pt&&(this.cGrid.sortedGridColumn=Ct[Dt]);var At=!0,wt=(new Object,\"\");if(this.cGrid.selectedIndices.length>0)for(var Ft=0;Ft<this.cGrid.selectedIndices.length;Ft++)null!=this.cGrid.dataProvider[this.cGrid.selectedIndices[Ft]]&&(wt=r.x.call(\"accountAccessConfirm\",this.cGrid.dataProvider[this.cGrid.selectedIndices[Ft]].movement.toString(),this.entityCombo.selectedLabel.toString()).toString(),h.a.parseBooleanValue(wt)||(At=!1));if(i=Mt.substr(Mt.indexOf(\"|\")+1,Mt.length-3),!(!h.a.parseBooleanValue(i)&&\"true|\"!=i),0==this.cGrid.selectedIndices.length&&(this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.messageButton.enabled=!1,this.messageButton.buttonMode=!1),\"search\"!=n&&\"displayOpenMovements\"==this.actionMethodName){this.dataGridContainer2.visible=!0,this.dataGridContainer2.includeInLayout=!0,this.dataGridContainer2.height=\"20%\",null==this.bottomGrid&&(this.bottomGrid=this.dataGridContainer2.addChild(r.hb),this.bottomGrid.onRowClick=function(t){e.cellLogic(t)},this.bottomGrid.lockedColumnCount=2,this.bottomGrid.uniqueColumn=\"movement\",this.bottomGrid.id=\"bottom\");var Rt={columns:this.jsonReader.getColumnData()};if(this.bottomGrid.CustomGrid(Rt),this.bottomGrid.ITEM_CLICK.subscribe(function(t){e.itemClickFunction2(t)}),this.jsonReader.getBottomGridData().row){var kt=[],Lt=Array.of(this.jsonReader.getBottomGridData().row);Lt[0].length>1&&(Lt=Lt[0]);for(var Ot=0;Ot<Lt.length;Ot++)for(var Nt=0;Nt<this.cGrid.dataProvider.length;Nt++)Lt[Ot].movement.content==this.cGrid.dataProvider[Nt].movement&&kt.push(Nt);this.bottomGrid.gridData=this.jsonReader.getBottomGridData(),this.bottomGrid.setRowSize=this.jsonReader.getBottomGridData().size}else this.bottomGrid.gridData={row:[],size:0};for(var Et=[],jt=0;jt<this.bottomGrid.dataProvider.length;jt++){for(pt=0;pt<this.profileAddCols.length;pt++){this.profileAddCols[pt];Et.push({col:this.bottomGrid.dataProvider[jt].col})}Et.push({pos:this.bottomGrid.dataProvider[jt].pos,value:this.bottomGrid.dataProvider[jt].value,amount:this.bottomGrid.dataProvider[jt].amount,sign:this.bottomGrid.dataProvider[jt].sign,ccy:this.bottomGrid.dataProvider[jt].ccy,ref1:this.bottomGrid.dataProvider[jt].ref1,account:this.bottomGrid.dataProvider[jt].account,input:this.bottomGrid.dataProvider[jt].input,cparty:this.bottomGrid.dataProvider[jt].cparty,pred:this.bottomGrid.dataProvider[jt].pred,ext:this.bottomGrid.dataProvider[jt].ext,status:this.bottomGrid.dataProvider[jt].status,matchid:this.bottomGrid.dataProvider[jt].matchid,source:this.bottomGrid.dataProvider[jt].source,format:this.bottomGrid.dataProvider[jt].format,notes:this.bottomGrid.dataProvider[jt].notes,beneficiary:this.bottomGrid.dataProvider[jt].beneficiary,ref2:this.bottomGrid.dataProvider[jt].ref2,ref3:this.bottomGrid.dataProvider[jt].ref3,movement:this.bottomGrid.dataProvider[jt].movement,book:this.bottomGrid.dataProvider[jt].book,custodian:this.bottomGrid.dataProvider[jt].custodian,xref:this.bottomGrid.dataProvider[jt].xref,update_date:this.bottomGrid.dataProvider[jt].update_date,matchingparty:this.bottomGrid.dataProvider[jt].matchingparty,producttype:this.bottomGrid.dataProvider[jt].producttype,postingdate:this.bottomGrid.dataProvider[jt].postingdate,extra_text1:this.bottomGrid.dataProvider[jt].extra_text1,alerting:this.bottomGrid.dataProvider[jt].alerting,ilmfcast:this.bottomGrid.dataProvider[jt].ilmfcast,uetr:this.bottomGrid.dataProvider[jt].uetr})}if(this.bottomGrid.dataProvider.length>0){for(var Ut=void 0,Wt=void 0,qt=0,_t=\"\",Yt=0,Vt=0,Jt=[],zt=[],Ht=this.jsonReader.getScreenAttributes().currencyFormat,Zt=0;Zt<this.bottomGrid.dataProvider.length;Zt++){if(Ut=parseInt(this.bottomGrid.dataProvider[Zt].positionlevel),Wt=this.bottomGrid.dataProvider[Zt].sign,_t=this.bottomGrid.dataProvider[Zt].amount,\"currencyPat1\"==Ht){Jt=_t.split(\",\"),_t=\"\";for(var Kt=0;Kt<Jt.length;Kt++)_t+=Jt[Kt]}else{Jt=_t.split(\".\"),_t=\"\";for(var Xt=0;Xt<Jt.length;Xt++)_t+=Jt[Xt];_t=_t.replace(\",\",\".\")}switch(qt=parseFloat(_t),\"D\"==Wt?a-=qt:a+=qt,Ut){case 1:null==zt[0]&&(zt[0]=0),zt[0]=\"D\"==Wt?zt[0]-qt:zt[0]+qt;break;case 2:null==zt[1]&&(zt[1]=0),zt[1]=\"D\"==Wt?zt[1]-qt:zt[1]+qt;break;case 3:null==zt[2]&&(zt[2]=0),zt[2]=\"D\"==Wt?zt[2]-qt:zt[2]+qt;break;case 4:null==zt[3]&&(zt[3]=0),zt[3]=\"D\"==Wt?zt[3]-qt:zt[3]+qt;break;case 5:null==zt[4]&&(zt[4]=0),zt[4]=\"D\"==Wt?zt[4]-qt:zt[4]+qt;break;case 6:null==zt[5]&&(zt[5]=0),zt[5]=\"D\"==Wt?zt[5]-qt:zt[5]+qt;break;case 7:null==zt[6]&&(zt[6]=0),zt[6]=\"D\"==Wt?zt[6]-qt:zt[6]+qt;break;case 8:null==zt[7]&&(zt[7]=0),zt[7]=\"D\"==Wt?zt[7]-qt:zt[7]+qt;break;case 9:null==zt[8]&&(zt[8]=0),zt[8]=\"D\"==Wt?zt[8]-qt:zt[8]+qt}}var Qt=0;for(pt=0;pt<zt.length;pt++)null!=zt[pt]&&(0==Qt?(Qt=-1,Yt=zt[pt],Vt=zt[pt]):(\"Y\",zt[pt]<Vt&&(Vt=zt[pt]),zt[pt]>Yt&&(Yt=zt[pt])))}var $t=\"\",te=!1,ee=!1,ie=!1;if(null!=this.bottomGrid)if(this.bottomGrid.dataProvider.length>0){for(var ae=0;ae<this.bottomGrid.dataProvider.length;ae++)if(\"CONFIRMED\"==($t=this.bottomGrid.dataProvider[ae].status).split(\" \")[0])te=!0;else if(\"SUSPENDED\"==$t.split(\" \")[0])ee=!0;else if(\"OUTSTANDING\"==$t.split(\" \")[0])ie=!0;else{r.x.call(\"unlockMovementOnServer\",this.bottomGrid.dataProvider[ae].movement),this.bottomGrid.dataProvider=null}At&&(0==ie?(0==te?(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0):(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1),0==ee?(this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0):(this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1)):(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0,this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0),this.matchButton.enabled=!0,this.matchButton.buttonMode=!0)}else this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.matchButton.enabled=!1,this.matchButton.buttonMode=!1;this.selectedMovements=[];for(var oe=0;oe<this.bottomGrid.dataProvider.length;oe++)this.selectedMovements.push(\"\"+this.bottomGrid.dataProvider[oe].movement);var le=!1;for(Zt=0;Zt<this.bottomGrid.dataProvider.length;Zt++)this.bottomGrid.dataProvider[Zt].matchid.toString().length>0&&(le=!0);le?(this.reconButton.enabled=!1,this.reconButton.buttonMode=!1):0!=this.bottomGrid.dataProvider.length&&(this.reconButton.enabled=!0,this.reconButton.buttonMode=!0)}else{var ne=void 0,re=void 0,se=0,ce=[],de=(Jt=[],this.jsonReader.getScreenAttributes().currencyFormat),he=\"\";for(oe=0;oe<this.cGrid.selectedIndices.length;oe++){if(ne=this.cGrid.dataProvider[this.cGrid.selectedIndices[oe]].sign,re=this.cGrid.dataProvider[this.cGrid.selectedIndices[oe]].amount,\"currencyPat1\"==de){ce=re.split(\",\"),re=\"\";for(Kt=0;Kt<ce.length;Kt++)re+=ce[Kt]}else{ce=re.split(\".\"),re=\"\";for(Xt=0;Xt<ce.length;Xt++)re+=ce[Xt];re=re.replace(\",\",\".\")}se=parseFloat(re),\"D\"==ne?a-=se:a+=se}}var ue=!1,me=null;if(null!=this.bottomGrid){for(var ve=0;ve<this.bottomGrid.dataProvider.length;ve++)if(null==me&&this.bottomGrid.dataProvider[ve].matchid.toString().length>0)me=this.bottomGrid.dataProvider[ve].matchid;else if(this.bottomGrid.dataProvider[ve].matchid.toString().length>0&&me!=this.bottomGrid.dataProvider[ve].matchid){ue=!0;break}if(ue){this.matchButton.enabled=!1,this.matchButton.buttonMode=!1;r.x.call(\"unlockMovementOnServer\",this.bottomGrid.dataProvider[ve].movement),this.swtAlert.warning(r.Wb.getPredictMessage(\"label.selectedOnlyMatchedItems\",null),\"Warning\")}else this.bottomGrid.dataProvider.length>=1&&(this.matchButton.enabled=!0,this.matchButton.buttonMode=!0)}if(l>1?(this.paginationData.visible=!0,this.numStepper.minimum=1,this.numStepper.maximum=this.jsonReader.getScreenAttributes().pages.maxPage):this.paginationData.visible=!1,this.exportContainer.maxPages=r.x.call(\"eval\",\"exportMaxPages\"),this.exportContainer.totalPages=l,this.exportContainer.currentPage=this.jsonReader.getScreenAttributes().pages.currentPage,\"search\"==n&&null!=this.cGrid&&null!=this.cGrid.dataProvider&&null!=this.cGridSelMvmt){for(var pe=!0,be=0;this.cGrid.dataProvider.length>be;be++)if(this.cGrid.dataProvider[be].movement==this.cGridSelMvmt){this.cGrid.selectedIndex=be,pe=!1;var ge=this.cGrid.dataProvider[this.cGrid.selectedIndex].movement,fe=r.x.call(\"setMsgButtonStatus\",ge);\"0\"==fe.substring(0,fe.indexOf(\"|\"))?(this.messageButton.enabled=!1,this.messageButton.buttonMode=!1):(this.messageButton.enabled=!0,this.messageButton.buttonMode=!0),this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,this.movementButton.enabled=!0,this.movementButton.buttonMode=!0;break}pe&&(this.cGridSelMvmt=null)}}this.currentFontSize=this.jsonReader.getScreenAttributes().currfontsize,\"Normal\"==this.currentFontSize?(this.selectedFont=0,this.cGrid.styleName=\"dataGridNormal\",this.cGrid.rowHeight=18,null!=this.bottomGrid&&(this.bottomGrid.styleName=\"dataGridNormal\",this.bottomGrid.rowHeight=18)):\"Small\"==this.currentFontSize&&(this.selectedFont=1,this.cGrid.styleName=\"dataGridSmall\",this.cGrid.rowHeight=15,null!=this.bottomGrid&&(this.bottomGrid.styleName=\"dataGridSmall\",this.bottomGrid.rowHeight=15))}}else this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+\"\\n\"+this.jsonReader.getRequestReplyLocation(),\"Error\"),null!=this.lastmonitortype&&this.updateData();null!=this.bottomGrid&&(1==this.bottomGrid.dataProvider.length?(this.movementButton.enabled=!0,this.movementButton.buttonMode=!0,this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,this.messageButton.enabled=!0,this.messageButton.buttonMode=!0):0==this.bottomGrid.dataProvider.length&&(this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.messageButton.enabled=!1,this.messageButton.buttonMode=!1),0!=this.bottomGrid.dataProvider.length&&-1!=this.bottomGrid.selectedIndex||(this.removeButton.enabled=!1,this.removeButton.buttonMode=!1));new Object;var xe=\"\";if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0)for(Zt=0;Zt<this.bottomGrid.dataProvider.length;Zt++)xe=r.x.call(\"accountAccessConfirm\",this.bottomGrid.dataProvider[Zt].movement.toString(),this.entityCombo.selectedLabel.toString()).toString(),h.a.parseBooleanValue(xe)||(At=!1);At||(this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.matchButton.enabled=!1,this.matchButton.buttonMode=!1);var Se=!1;if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0){for(ve=0;ve<this.bottomGrid.dataProvider.length;ve++){if(\"OUTSTANDING\"==($t=this.bottomGrid.dataProvider[ve].status).split(\" \")[0]){Se=!0;break}}At||Se||ue||(this.matchButton.enabled=!0)}}null==this.initReceivedJSON&&null!=this.lastRecievedJSON&&(this.initReceivedJSON=this.lastRecievedJSON),this.currencyThreshold.enabled||(this.currencyThreshold.enabled=!0),setTimeout(function(){e.cGrid.selectedIndices.length<=0&&(e.messageButton.enabled=!1,e.messageButton.buttonMode=!1,e.movementButton.enabled=!1,e.movementButton.buttonMode=!1,e.noteButton.enabled=!1,e.noteButton.buttonMode=!1,null!=e.bottomGrid&&(e.reconButton.enabled=!1,e.reconButton.buttonMode=!1,e.confirmButton.enabled=!1,e.suspendButton.buttonMode=!1,e.suspendButton.enabled=!1,e.matchButton.buttonMode=!1,e.matchButton.enabled=!1))},0);var ye=this.jsonReader.getScreenAttributes().currencyFormat,Me=this.addZeroes(a.toString());if(-1==Me.indexOf(\".\"))Me+=\"00\";else{var Pe;Pe=Me.split(\".\"),Me=Pe[0]+Pe[1]}o=r.x.call(\"expandAmtDifference\",Me,ye,he),a<0&&o.length>0&&\"-\"!=o.charAt(0)?this.totalSelectedValue.text=\"-\"+r.x.call(\"expandAmtDifference\",Me,ye,he):this.totalSelectedValue.text=r.x.call(\"expandAmtDifference\",Me,ye,he),setTimeout(function(){if(null!=e.bottomGrid&&e.bottomGrid.dataProvider.length>0)for(var t=0;t<e.bottomGrid.dataProvider.length;t++)r.x.call(\"lockMovementOnServer\",e.bottomGrid.dataProvider[t].movement)},0)},e.prototype.inputDataFault=function(t){this.lastRefTime.visible=!1,this.lastRefTimeLabel.text=\"Connection Error\",this.lastRefTimeLabel.setStyle(\"color\",\"Red\",this.lastRefTimeLabel.domElement),this.invalidComms=t},e.prototype.connError=function(t){this.swtAlert.error(\"\"+this.invalidComms,r.x.call(\"getBundle\",\"text\",\"alert-error\",\"Error\"))},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.disableInterface()},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.enableInterface()},e.prototype.disableInterface=function(){this.paginationData.enabled=!1,this.refreshButton.enabled=!1,this.refreshButton.buttonMode=!1},e.prototype.enableInterface=function(){this.paginationData.enabled=!0,this.refreshButton.enabled=!0,this.refreshButton.buttonMode=!0},e.prototype.currencyThresholdChange=function(t){if(\"filterscreen\"!=this.jsonReader.getScreenAttributes().initialinputscreen){this.actionPath=\"outstandingmovement.do?\",\"search\"==this.method||\"E\"!=this.initialinputscreen?(this.actionMethod=\"method=searchrefreshMvmntsSummaryDisplay\",null!=r.x.call(\"eval\",\"scenarioId\")&&(this.actionMethod+=\"&scenarioId=\"+r.x.call(\"eval\",\"scenarioId\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),this.actionMethod+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"))):(this.actionMethod=\"method=refreshMvmntsSummaryDisplay\",this.actionMethod+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\")),this.actionMethod+=\"&currentPage=1\",this.actionMethod+=\"&applyCurrencyThresholdInd=1\",this.currencyThreshold.selected?this.actionMethod+=\"&applyCurrencyThreshold=N\":this.actionMethod+=\"&applyCurrencyThreshold=Y\",this.actionMethod+=\"&openFlag=\"+r.x.call(\"eval\",\"openFlag\");var e=\"\";if(\"search\"!=this.methodName&&\"E\"==this.initialinputscreen)for(var i=0;i<this.bottomGrid.dataProvider.length;i++)e+=this.bottomGrid.dataProvider[i].movement+\",\";this.actionMethod+=\"&selectedList=\"+e,this.actionMethod+=\"&entityId=\"+this.entityCombo.selectedLabel,this.actionMethod+=\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\"),this.actionMethod+=\"&date=\"+r.x.call(\"eval\",\"dateStr\"),this.actionMethod+=\"&initialinputscreen=\"+this.jsonReader.getScreenAttributes().initialinputscreen,this.actionMethod+=\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\");var a=this.jsonReader.getScreenAttributes().access;if(this.initialinputscreen=this.jsonReader.getScreenAttributes().initialinputscreen,\"E\"==this.initialinputscreen&&(this.actionMethod+=\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),\"search\"!=this.methodName&&\"E\"==this.initialinputscreen||(this.actionMethod+=\"&maxPage=\"+this.jsonReader.getScreenAttributes().pages.maxPage,this.actionMethod+=\"&totalCount=\"+this.jsonReader.getRowSize()+\"&extBalStatus=\"+r.x.call(\"eval\",\"extBalStatusSearch\"),\"readOnly\"!=a&&(\"currencymonitor\"!=this.initialinputscreen&&\"entitymonitor\"!=this.initialinputscreen||(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&balanceType=\",this.actionMethod+=\"&locationId=\"+r.x.call(\"eval\",\"locationIdCurrency\")),\"currencymonitor\"!=this.initialinputscreen&&\"entitymonitor\"!=this.initialinputscreen&&(\"accountmonitor\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\"),this.actionMethod+=\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\"),this.actionMethod+=\"&accountType=\"+r.x.call(\"eval\",\"accountTypeAccount\")),\"accountmonitor\"!=this.initialinputscreen&&(\"accountbreakdownmonitor\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\"),this.actionMethod+=\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\")),\"accountbreakdownmonitor\"!=this.initialinputscreen&&(\"bookmonitor\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeBook\")),\"bookmonitor\"!=this.initialinputscreen&&(\"mvmntsfromWorkFlowMonitor\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&tabIndicator=\"+r.x.call(\"eval\",\"tabIndicatorWorkflow\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),this.actionMethod+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\"),this.actionMethod+=\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusWorkflow\"),this.actionMethod+=\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusWorkflow\"),this.actionMethod+=\"&linkFlag=\"+r.x.call(\"eval\",\"linkFlagWorkflow\")),\"mvmntsfromWorkFlowMonitor\"!=this.initialinputscreen&&(\"unSettledYesterday\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),this.actionMethod+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\")),\"unSettledYesterday\"!=this.initialinputscreen&&(\"backValuedMvmnts\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),this.actionMethod+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\")),\"backValuedMvmnts\"!=this.initialinputscreen&&(\"openUnexpectedMvmnts\"==this.initialinputscreen&&(this.actionMethod+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),this.actionMethod+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\")),\"openUnexpectedMvmnts\"!=this.initialinputscreen&&(this.actionMethod+=\"&archiveId=\"+r.x.call(\"eval\",\"archiveIdSearch\"),this.actionMethod+=\"&filterFromSerach=\"+r.x.call(\"eval\",\"filterFromSerachSearch\"),this.actionMethod+=\"&entityId=\"+r.x.call(\"eval\",\"entityIdSearch\")+\"&movementType=\"+r.x.call(\"eval\",\"movementTypeSearch\")+\"&sign=\"+r.x.call(\"eval\",\"signSearch\")+\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusSearch\")+\"&amountover=\"+r.x.call(\"eval\",\"amountoverSearch\")+\"&amountunder=\"+r.x.call(\"eval\",\"amountunderSearch\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&paymentChannelId=\"+r.x.call(\"eval\",\"paymentChannelIdSearch\")+\"&beneficiaryId=\"+r.x.call(\"eval\",\"beneficiaryIdSearch\")+\"&custodianId=\"+r.x.call(\"eval\",\"custodianIdSearch\")+\"&positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")+\"&accountId=\"+r.x.call(\"eval\",\"accountIdSearch\")+\"&group=\"+r.x.call(\"eval\",\"groupSearch\")+\"&metaGroup=\"+r.x.call(\"eval\",\"metaGroupSearch\")+\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeSearch\")+\"&valueFromDateAsString=\"+r.x.call(\"eval\",\"valueFromDateAsStringSearch\")+\"&valueToDateAsString=\"+r.x.call(\"eval\",\"valueToDateAsStringSearch\")+\"&timefrom=\"+r.x.call(\"eval\",\"timefromSearch\")+\"&timeto=\"+r.x.call(\"eval\",\"timetoSearch\")+\"&reference=\"+r.x.call(\"eval\",\"referenceSearch\")+\"&messageId=\"+r.x.call(\"eval\",\"messageIdSearch\")+\"&inputDateAsString=\"+r.x.call(\"eval\",\"inputDateAsStringSearch\")+\"&counterPartyId=\"+r.x.call(\"eval\",\"counterPartyIdSearch\")+\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusSearch\")+\"&initialinputscreen=\")))))))))),this.actionMethod+=\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter+\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort,this.actionMethod+=\"&tableScrollbarLeft=&tableScrollbarTop=&scrollbarLeft=&scrollbarTop=&isAmountDiffer=&selectedMovementsAmount=&menuAccessId=\"+this.menuAccessId+\"&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),-1==this.filterComboMSD.selectedIndex?this.requestParams.currentFilterConf=r.t.encode64(this.adhocLabel):this.requestParams.currentFilterConf=r.t.encode64(this.filterComboMSD.selectedLabel),r.x.call(\"eval\",\"uetr\")&&(this.actionMethod+=\"&uetr=\"+r.x.call(\"eval\",\"uetr\")),this.actionMethod+=\"&extraFilter=\"+r.x.call(\"eval\",\"extraFilter\"),\"search\"!=this.methodName&&\"E\"==this.initialinputscreen)for(i=0;i<this.bottomGrid.dataProvider.length;i++)e+=this.bottomGrid.dataProvider[i].movement+\",\";this.actionMethod+=\"&selectedList=\"+e,this.keepSelected=!0,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}else this.filterScreen(\"Filter\")},e.prototype.openedCombo=function(t){this.comboOpen=!0,this.inputData.isBusy()&&(this.enableInterface(),this.inputData.cancel(),\"SwtComboBox\"==t.currentTarget&&(t.currentTarget.interruptComms=!0))},e.prototype.closedCombo=function(t){this.comboOpen=!1,null!=t.triggerEvent&&\"mouseDownOutside\"==t.triggerEvent.type&&\"SwtComboBox\"==t.currentTarget&&t.currentTarget.interruptComms&&(t.currentTarget.interruptComms=!1,this.updateData())},e.prototype.updateData=function(){this.requestParams=[],this.actionMethod=\"\";this.entityCombo.selectedLabel;this.actionPath=\"outstandingmovement.do?\";var t=r.x.call(\"eval\",\"outstandingMethodName\");this.actionMethod=\"method=\"+t,\"search\"==t?this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdSearch\")+\"&movementType=\"+r.x.call(\"eval\",\"movementTypeSearch\")+\"&sign=\"+r.x.call(\"eval\",\"signSearch\")+\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusSearch\")+\"&amountover=\"+r.x.call(\"eval\",\"amountoverSearch\")+\"&amountunder=\"+r.x.call(\"eval\",\"amountunderSearch\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&paymentChannelId=\"+r.x.call(\"eval\",\"paymentChannelIdSearch\")+\"&beneficiaryId=\"+r.x.call(\"eval\",\"beneficiaryIdSearch\")+\"&custodianId=\"+r.x.call(\"eval\",\"custodianIdSearch\")+\"&positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")+\"&accountId=\"+r.x.call(\"eval\",\"accountIdSearch\")+\"&group=\"+r.x.call(\"eval\",\"groupSearch\")+\"&metaGroup=\"+r.x.call(\"eval\",\"metaGroupSearch\")+\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeSearch\")+\"&valueFromDateAsString=\"+r.x.call(\"eval\",\"valueFromDateAsStringSearch\")+\"&valueToDateAsString=\"+r.x.call(\"eval\",\"valueToDateAsStringSearch\")+\"&timefrom=\"+r.x.call(\"eval\",\"timefromSearch\")+\"&timeto=\"+r.x.call(\"eval\",\"timetoSearch\")+\"&reference=\"+r.x.call(\"eval\",\"referenceSearch\")+\"&messageId=\"+r.x.call(\"eval\",\"messageIdSearch\")+\"&inputDateAsString=\"+r.x.call(\"eval\",\"inputDateAsStringSearch\")+\"&counterPartyId=\"+r.x.call(\"eval\",\"counterPartyIdSearch\")+\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusSearch\")+\"&initialinputscreen=&accountClass=\"+r.x.call(\"eval\",\"accountClassSearch\")+\"&isAmountDiffer=\"+r.x.call(\"eval\",\"isAmountDifferSearch\")+\"&referenceFlag=\"+r.x.call(\"eval\",\"referenceFlagSearch\")+\"&selectedMovementsAmount=\"+r.x.call(\"eval\",\"selectedMovementsAmountSearch\"):\"getBookMonitorMvmnts\"==t&&(this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdBook\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeBook\")+\"&selectedTabIndex=\"+r.x.call(\"eval\",\"selectedTabIndexBook\")+\"&initialinputscreen=\"+this.jsonReader.getScreenAttributes().initialinputscreen),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.closeHandler=function(){if(\"search\"!=this.methodName&&\"E\"==this.initialinputscreen)for(var t=0;t<this.bottomGrid.dataProvider.length;t++){var e=\"\",i=r.x.call(\"checkLockOnServer\",this.bottomGrid.dataProvider[t].movement);this.currentUser.toLowerCase()!=i.toLowerCase()&&\"true\"!=i.toString()||(e=r.x.call(\"unlockMovementOnServer\",this.bottomGrid.dataProvider[t].movement).toString(),h.a.parseBooleanValue(e)||this.swtAlert.warning(r.Wb.getPredictMessage(\"label.movementCannotBeUnlocked\",null),\"Warning\"))}r.x.call(\"close\")},e.prototype.exportTimerEventHandler=function(t){},e.prototype.export=function(t,e,i){var a=this.currencyThreshold.selected?\"Yes\":\"No\",o=this.jsonReader.getScreenAttributes().initialinputscreen,l=r.x.call(\"eval\",\"accountIdAccount\"),n=r.x.call(\"eval\",\"balanceTypeAccount\"),s=r.x.call(\"eval\",\"bookCodeBook\"),c=r.x.call(\"eval\",\"roleId\"),d=r.x.call(\"eval\",\"workflow\"),h=null==this.jsonReader.getScreenAttributes().pages.accountTypeAccount?\"\":this.jsonReader.getScreenAttributes().pages.accountTypeAccount;this.requestParams.applyCurrencyThreshold=a,this.requestParams.selectedFilter=(this.jsonReader.getScreenAttributes().selectedFilter?this.jsonReader.getScreenAttributes().selectedFilter:\"\").replace(/&/g,\"amp;\").replace(/\\+/g,\"plus;\"),this.requestParams.selectedSort=this.jsonReader.getScreenAttributes().selectedSort,this.requestParams.exportType=t,this.requestParams.pageCount=i,this.requestParams.currentPage=e,this.requestParams.initialinputscreen=o,this.requestParams.totalOverPagesValue=this.totalOverPagesValue.text,this.requestParams.totalInPageValue=this.totalInPageValue.text,\"filterscreen\"==o?(this.requestParams.accountId=l,this.requestParams.balanceType=n,this.requestParams.accountType=h.replace(/&/g,\"amp;\").replace(/\\+/g,\"plus;\"),this.requestParams.selectedFilter=(this.jsonReader.getScreenAttributes().selectedFilter?\"\":this.jsonReader.getScreenAttributes().selectedFilter).replace(/&/g,\"amp;\").replace(/\\+/g,\"plus;\"),this.requestParams.selectedSort=this.jsonReader.getScreenAttributes().selectedSort,null!=d&&(this.requestParams.workflow=d),this.requestParams.totalFlag=r.x.call(\"eval\",\"totalFlagOpenMovements\")):\"S\"==o?this.requestParams.accountType=h.replace(/&/g,\"amp;\").replace(/\\+/g,\"plus;\"):\"R\"==o?(this.requestParams.accountType=h.replace(/&/g,\"amp;\").replace(/\\+/g,\"plus;\"),this.requestParams.accountId=this.jsonReader.getScreenAttributes().pages.accountIdSearch):\"currencymonitor\"==o||\"entitymonitor\"==o?(this.requestParams.valueDate=r.x.call(\"eval\",\"valueDate\"),this.requestParams.accountId=l,this.requestParams.balanceType=n):\"accountmonitor\"==o||\"accountbreakdownmonitor\"==o?(this.requestParams.valueDate=r.x.call(\"eval\",\"valueDate\"),this.requestParams.accountId=l,this.requestParams.balanceType=n,this.requestParams.accountType=h.replace(/&/g,\"amp;\").replace(/\\+/g,\"plus;\")):\"bookmonitor\"==o?(this.requestParams.valueDate=r.x.call(\"eval\",\"valueDate\"),this.requestParams.bookCode=s):\"mvmntsfromWorkFlowMonitor\"==o?(this.requestParams.tabIndicator=r.x.call(\"eval\",\"tabIndicatorWorkflow\"),this.requestParams.currGrp=r.x.call(\"eval\",\"currGrp\"),this.requestParams.roleId=c,this.requestParams.matchStatus=r.x.call(\"eval\",\"matchStatusWorkflow\"),this.requestParams.predictStatus=r.x.call(\"eval\",\"predictStatusWorkflow\")):\"unSettledYesterday\"==o||\"backValuedMvmnts\"==o||\"openUnexpectedMvmnts\"==o?(this.requestParams.currGrp=r.x.call(\"eval\",\"currGrp\"),this.requestParams.roleId=c,this.requestParams.balanceType=r.x.call(\"eval\",\"balanceTypeAccount\")):\"E\"==o?(this.requestParams.currentFilterConf=r.t.encode64(this.filterComboMSD.selectedLabel),this.requestParams.accountType=h.replace(/&/g,\"amp;\").replace(/\\+/g,\"plus;\"),null!=d&&(this.requestParams.workflow=d),this.requestParams.totalFlag=r.x.call(\"eval\",\"totalFlagOpenMovements\")):\"X\"==o&&(this.requestParams.accountType=h.replace(/&/g,\"amp;\").replace(/\\+/g,\"plus;\"),this.requestParams.totalFlag=r.x.call(\"eval\",\"totalFlagOpenMovements\"),null!=r.x.call(\"eval\",\"scenarioId\")&&(this.requestParams.scenarioId=r.x.call(\"eval\",\"scenarioId\"),this.requestParams.currGrp=r.x.call(\"eval\",\"currGrp\"))),this.requestParams.filterAcctType=this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),this.requestParams.tokenForDownload=(new Date).getTime(),this.requestParams.screen=r.Wb.getPredictMessage(\"movementSummDisplay.title.Window\",null);var u=Object.assign({},this.requestParams);r.x.call(\"sendParams\",u)},e.prototype.closePopup=function(t){void 0===t&&(t=null);try{this.exportContainer.closeCancelPopup()}catch(i){}if(!r.Z.isEmpty(t))if(\"mem\"==t){var e=r.Z.substitute(r.x.call(\"getBundle\",\"text\",\"label-serverHasRunOutOfMemory\",\"Server has run out of memory. Please contact your system administrator.  The following parameters should be reviewed, \")+\"movementsummarydisplay.PageSize:\"+r.x.call(\"eval\",\"msdPageSize\")+r.x.call(\"getBundle\",\"text\",\"label-maxNumberPages\",\", maximum number of pages :\")+r.x.call(\"eval\",\"msdMaxPageSize\"));this.swtAlert.show(e,\"OutOfMemoryError\")}else\"err\"==t&&this.swtAlert.show(r.x.call(\"getBundle\",\"text\",\"alert-contactAdmin\",\"An Error occurred while exporting, Please contact your System Administrator\"),r.x.call(\"getBundle\",\"text\",\"alert-error\",\"Error\"))},e.prototype.exportCancel=function(t){var e=this;this.dataRefresh(t),this.cancelExport.cbStart=this.startOfComms.bind(this),this.cancelExport.cbStop=this.endOfComms.bind(this),this.cancelExport.cbResult=function(t){e.ExportCanceled(t)},this.actionPath=\"outstandingmovement.do?\",this.actionMethod=\"method=cancelILMExport\",this.cancelExport.url=this.baseURL+this.actionPath+this.actionMethod,this.cancelExport.encodeURL=!1,this.cancelExport.cbFault=this.inputDataFault.bind(this),this.requestParams.cancelExport=\"true\",this.cancelExport.send(this.requestParams)},e.prototype.ExportCanceled=function(t){},e.prototype.checkSelectedMovementsAmounts=function(){for(var t=!0,e=parseFloat(this.bottomGrid.dataProvider[0].amount),i=this.bottomGrid.dataProvider[0].sign,a=1;a<this.bottomGrid.dataProvider.length;a++){var o=parseFloat(this.bottomGrid.dataProvider[a].amount),l=this.bottomGrid.dataProvider[a].sign;e==o&&i==l||(t=!1)}return t},e.prototype.alertListener=function(t){var e=!1;if(t.detail==r.c.OK&&(e=!0),e){for(var i=\"\",a=0;a<this.bottomGrid.dataProvider.length;a++)i+=this.bottomGrid.dataProvider[a].movement+\",\";var o=this.entityCombo.selectedLabel;this.actionPath=\"outstandingmovement.do?\";var l,n=\"N\",s=\"1\";if(this.currencyThreshold.selected||(n=\"Y\",s=\"1\"),this.confirmFlag)this.diffText.text=\"\",this.requestParams=[],\"filterscreen\"==(l=this.jsonReader.getScreenAttributes().initialinputscreen)&&(l=\"E\"),this.actionMethod=\"method=confirmMatch&initialinputscreen=\"+l+\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&selectedCurrencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&selectedList=\"+i+\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\")+\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter+\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&applyCurrencyThreshold=\"+n+\"&applyCurrencyThresholdInd=\"+s+\"&totalCount=\"+this.jsonReader.getRowSize()+\"&tableScrollbarLeft=&entityId=\"+o+\"&tableScrollbarTop=&scrollbarLeft=&scrollbarTop=&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),this.actionMethod+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams[\"movement.id.entityId\"]=o,-1==this.filterComboMSD.selectedIndex?this.requestParams.currentFilterConf=r.t.encode64(this.adhocLabel):this.requestParams.currentFilterConf=r.t.encode64(this.filterComboMSD.selectedLabel),this.requestParams.openMovementFlagSearch=r.x.call(\"openMovementFlag\"),this.inputData.send(this.requestParams),this.selectedMvmts=[],this.cGrid.selectedIndex=-1,this.cGrid.selectedIndices=[];if(this.matchFlag){e=!1;for(var c=!1,d=\"\",h=0;h<this.bottomGrid.dataProvider.length;h++){if(this.bottomGrid.dataProvider[h].matchid.toString().length<=0){c=!0;break}d=this.bottomGrid.dataProvider[h].matchid}this.diffText.text=\"\";var u=\"\";for(a=0;a<this.bottomGrid.dataProvider.length;a++)u+=this.bottomGrid.dataProvider[a].movement+\",\";var m=this.entityCombo.selectedLabel;this.actionPath=\"movementmatchdisplay.do?\";this.currencyThreshold.selected||(\"Y\",\"1\"),this.actionMethod=\"method=offeredMatch&selectedList=\"+u+\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\")+\"&selectedCurrencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&entityCode=\"+m+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&tableScrollbarLeft=&tableScrollbarTop=&scrollbarLeft=&scrollbarTop=\",null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),c||(this.actionMethod+=\"&matchId=\"+d),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,r.x.call(\"offeredMatches\",this.inputData.url),this.cGrid.selectedIndices=[],this.bottomGrid.dataProvider=[];var v=[];for(a=0;a<this.bottomGrid.dataProvider.length;a++)v.push(this.bottomGrid.angularGridInstance.gridService.getDataItemByRowNumber(a));if(this.bottomGrid.angularGridInstance.gridService.deleteItems(v),this.selectedMvmts=[],this.cGrid.selectedIndex=-1,this.cGrid.selectedIndices=[],c){this.actionPath=\"outstandingmovement.do?\",this.actionMethod=\"method=refreshMvmntsSummaryDisplay\";var p=\"\";if(\"search\"!=this.methodName&&\"E\"==this.initialinputscreen)for(a=0;a<this.bottomGrid.dataProvider.length;a++)p+=this.bottomGrid.dataProvider[a].movement+\",\";this.actionMethod+=\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage,this.actionMethod+=\"&selectedList=\"+p,this.actionMethod+=\"&applyCurrencyThresholdInd=1\",this.currencyThreshold.selected?this.actionMethod+=\"&applyCurrencyThreshold=N\":this.actionMethod+=\"&applyCurrencyThreshold=Y\",this.actionMethod+=\"&entityId=\"+this.entityCombo.selectedLabel,this.actionMethod+=\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\"),this.actionMethod+=\"&date=\"+r.x.call(\"eval\",\"dateStr\"),this.actionMethod+=\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort,this.actionMethod+=\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter,this.actionMethod+=\"&initialinputscreen=\"+this.jsonReader.getScreenAttributes().initialinputscreen,this.actionMethod+=\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\");this.jsonReader.getScreenAttributes().access;this.initialinputscreen=this.jsonReader.getScreenAttributes().initialinputscreen,\"E\"==this.initialinputscreen&&(this.actionMethod+=\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")),this.actionMethod+=\"&tableScrollbarLeft=&tableScrollbarTop=&scrollbarLeft=&scrollbarTop=&isAmountDiffer=&selectedMovementsAmount=\",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams.currentFilterConf=r.t.encode64(this.filterComboMSD.selectedLabel),this.requestParams.openMovementFlagSearch=r.x.call(\"openMovementFlag\"),this.inputData.send(this.requestParams)}}if(this.suspendFlag)this.diffText.text=\"\",\"filterscreen\"==(l=this.jsonReader.getScreenAttributes().initialinputscreen)&&(l=\"E\"),this.actionMethod=\"method=suspendMatch&initialinputscreen=\"+l+\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&selectedCurrencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&selectedList=\"+i+\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\")+\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter+\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&applyCurrencyThreshold=\"+n+\"&applyCurrencyThresholdInd=\"+s+\"&totalCount=\"+this.jsonReader.getRowSize()+\"&tableScrollbarLeft=&tableScrollbarTop=&scrollbarLeft=&entityId=\"+o+\"&scrollbarTop=&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),this.actionMethod+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams[\"movement.id.entityId\"]=o,-1==this.filterComboMSD.selectedIndex?this.requestParams.currentFilterConf=r.t.encode64(this.adhocLabel):this.requestParams.currentFilterConf=r.t.encode64(this.filterComboMSD.selectedLabel),this.requestParams.openMovementFlagSearch=r.x.call(\"openMovementFlag\"),this.inputData.send(this.requestParams),this.selectedMvmts=[],this.cGrid.selectedIndex=-1,this.cGrid.selectedIndices=[];if(this.reconFlag)this.diffText.text=\"\",\"filterscreen\"==(l=this.jsonReader.getScreenAttributes().initialinputscreen)&&(l=\"E\"),this.actionMethod=\"method=reconMatch&initialinputscreen=\"+l+\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&selectedCurrencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&selectedList=\"+i+\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\")+\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter+\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&applyCurrencyThreshold=\"+n+\"&applyCurrencyThresholdInd=\"+s+\"&totalCount=\"+this.jsonReader.getRowSize()+\"&tableScrollbarLeft=&tableScrollbarTop=&entityId=\"+o+\"&scrollbarLeft=&scrollbarTop=&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),this.actionMethod+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams[\"movement.id.entityId\"]=o,-1==this.filterComboMSD.selectedIndex?this.requestParams.currentFilterConf=r.t.encode64(this.adhocLabel):this.requestParams.currentFilterConf=r.t.encode64(this.filterComboMSD.selectedLabel),this.requestParams.openMovementFlagSearch=r.x.call(\"openMovementFlag\"),this.inputData.send(this.requestParams),this.selectedMvmts=[],this.cGrid.selectedIndex=-1,this.cGrid.selectedIndices=[]}},e.prototype.confirmResultevent=function(t){this.lastRecievedJSON=t},e.prototype.confirm=function(){this.confirmFlag=!0,this.matchFlag=!1,this.suspendFlag=!1,this.reconFlag=!1;var t=!0;if(this.checkMvtStatus())if(this.checkSelectedMovementsAmounts()){if(t){for(var e=\"\",i=0;i<this.bottomGrid.dataProvider.length;i++)e+=this.bottomGrid.dataProvider[i].movement+\",\";var a=this.entityCombo.selectedLabel;this.actionPath=\"outstandingmovement.do?\";var o=\"N\",l=\"1\";0==this.currencyThreshold.selected&&(o=\"Y\",l=\"1\");var n=this.jsonReader.getScreenAttributes().initialinputscreen;\"filterscreen\"==n&&(n=\"E\"),this.actionMethod=\"method=confirmMatch&initialinputscreen=\"+n+\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&selectedCurrencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&selectedList=\"+e+\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\")+\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter+\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&applyCurrencyThreshold=\"+o+\"&applyCurrencyThresholdInd=\"+l+\"&totalCount=\"+this.jsonReader.getRowSize()+\"&tableScrollbarLeft=&tableScrollbarTop=&entityId=\"+a+\"&scrollbarLeft=&scrollbarTop=\",this.actionMethod+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams[\"movement.id.entityId\"]=a,-1==this.filterComboMSD.selectedIndex?this.requestParams.currentFilterConf=r.t.encode64(this.adhocLabel):this.requestParams.currentFilterConf=r.t.encode64(this.filterComboMSD.selectedLabel),this.requestParams.openMovementFlagSearch=r.x.call(\"openMovementFlag\"),this.inputData.send(this.requestParams),this.cGrid.selectedIndex=-1,this.cGrid.selectedIndices=[],this.selectedMvmts=[]}}else{t=!1;var s=r.Wb.getPredictMessage(\"label.amountSelectedMovementsDiffer\",null);this.swtAlert.warning(s,\"Microsoft Internet Explorer\",r.c.OK|r.c.CANCEL,this,this.alertListener.bind(this),null)}},e.prototype.reconcile=function(){this.confirmFlag=!1,this.matchFlag=!1,this.suspendFlag=!1,this.reconFlag=!0;var t=!0;if(this.checkMvtStatus())if(this.checkSelectedMovementsAmounts()){if(t){for(var e=\"\",i=0;i<this.bottomGrid.dataProvider.length;i++)e+=this.bottomGrid.dataProvider[i].movement+\",\";var a=this.entityCombo.selectedLabel;this.actionPath=\"outstandingmovement.do?\";var o=\"N\",l=\"1\";this.currencyThreshold.selected||(o=\"Y\",l=\"1\");var n=this.jsonReader.getScreenAttributes().initialinputscreen;\"filterscreen\"==n&&(n=\"E\"),this.actionMethod=\"method=reconMatch&initialinputscreen=\"+n+\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&selectedCurrencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&selectedList=\"+e+\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\")+\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter+\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&applyCurrencyThreshold=\"+o+\"&applyCurrencyThresholdInd=\"+l+\"&totalCount=\"+this.jsonReader.getRowSize()+\"&tableScrollbarLeft=&tableScrollbarTop=&scrollbarLeft=&entityId=\"+a+\"&scrollbarTop=\",this.actionMethod+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams[\"movement.id.entityId\"]=a,-1==this.filterComboMSD.selectedIndex?this.requestParams.currentFilterConf=r.t.encode64(this.adhocLabel):this.requestParams.currentFilterConf=r.t.encode64(this.filterComboMSD.selectedLabel),this.requestParams.openMovementFlagSearch=r.x.call(\"openMovementFlag\"),this.inputData.send(this.requestParams),this.cGrid.selectedIndex=-1,this.cGrid.selectedIndices=[],this.selectedMvmts=[]}}else{t=!1;this.swtAlert.question(\"The amounts of the selected movements differ. Do you want to continue?\",\"Microsoft Internet Explorer\",r.c.OK|r.c.CANCEL,this,this.alertListener.bind(this),null)}},e.prototype.suspend=function(){this.confirmFlag=!1,this.matchFlag=!1,this.suspendFlag=!0,this.reconFlag=!1;var t=!0;if(this.checkMvtStatus())if(this.checkSelectedMovementsAmounts()){if(t){for(var e=\"\",i=0;i<this.bottomGrid.dataProvider.length;i++)e+=this.bottomGrid.dataProvider[i].movement+\",\";var a=this.entityCombo.selectedLabel;this.actionPath=\"outstandingmovement.do?\";var o=\"N\",l=\"1\";this.currencyThreshold.selected||(o=\"Y\",l=\"1\");var n=this.jsonReader.getScreenAttributes().initialinputscreen;\"filterscreen\"==n&&(n=\"E\"),this.actionMethod=\"method=suspendMatch&initialinputscreen=\"+n+\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&selectedCurrencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&selectedList=\"+e+\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\")+\"&selectedFilter=\"+this.jsonReader.getScreenAttributes().selectedFilter+\"&selectedSort=\"+this.jsonReader.getScreenAttributes().selectedSort+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&applyCurrencyThreshold=\"+o+\"&applyCurrencyThresholdInd=\"+l+\"&totalCount=\"+this.jsonReader.getRowSize()+\"&tableScrollbarLeft=&entityId=\"+a+\"&tableScrollbarTop=&scrollbarLeft=&scrollbarTop=\",this.actionMethod+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.requestParams[\"movement.id.entityId\"]=a,-1==this.filterComboMSD.selectedIndex?this.requestParams.currentFilterConf=r.t.encode64(this.adhocLabel):this.requestParams.currentFilterConf=r.t.encode64(this.filterComboMSD.selectedLabel),this.requestParams.openMovementFlagSearch=r.x.call(\"openMovementFlag\"),this.inputData.send(this.requestParams),this.cGrid.selectedIndex=-1,this.cGrid.selectedIndices=[],this.selectedMvmts=[]}}else{t=!1;this.swtAlert.warning(\"The amounts of the selected movements differ. Do you want to continue?\",\"Microsoft Internet Explorer\",r.c.OK|r.c.CANCEL,this,this.alertListener.bind(this),null)}},e.prototype.checkMvtStatus=function(){var t,e=!0;return t=r.x.call(\"checkMovementStatus\",this.selectedMovements,this.entityCombo.selectedLabel).toString(),h.a.parseBooleanValue(t)||(e=!1,this.swtAlert.warning(\"This movement has been changed\",\"\",r.c.OK,this,this.refreshScreen.bind(this),null)),e},e.prototype.refreshScreen=function(t){this.cGrid.selectedIndex=-1;for(var e=0;e<this.bottomGrid.dataProvider.length;e++){var i=r.x.call(\"checkLockOnServer\",this.bottomGrid.dataProvider[e].movement);this.currentUser.toLowerCase()!=i.toLowerCase()&&\"true\"!=i.toString()||r.x.call(\"unlockMovementOnServer\",this.bottomGrid.dataProvider[e].movement)}if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0){this.bottomGrid.dataProvider=[];for(var a=\"\",o=0;o<this.bottomGrid.dataProvider.length;o++)a+=this.bottomGrid.dataProvider[o].movement+\",\";r.x.call(\"setSelectedMovementForLock\",a)}this.dataRefresh(null)},e.prototype.match=function(){this.confirmFlag=!1,this.matchFlag=!0,this.suspendFlag=!1,this.reconFlag=!1;this.jsonReader.getScreenAttributes().pages.currentPage;if(this.checkMvtStatus()){for(var t=!1,e=!1,i=!1,a=null,o=0;o<this.bottomGrid.dataProvider.length;o++)if(this.bottomGrid.dataProvider[o].matchid.toString().length<=0&&(e=!0),this.bottomGrid.dataProvider[o].matchid.toString().length>0&&(i=!0),e&&i&&(t=!0),null==a&&this.bottomGrid.dataProvider[o].matchid.toString().length>0)a=this.bottomGrid.dataProvider[o].matchid;else if(this.bottomGrid.dataProvider[o].matchid.toString().length>0&&a!=this.bottomGrid.dataProvider[o].matchid){!0;break}if(t){this.swtAlert.warning(\"Matching these movements will change composition and status of an existing match. Are you sure that you wish to continue?\",\"Microsoft Internet Explorer\",r.c.OK|r.c.CANCEL,this,this.alertListenerMatch.bind(this),null)}else{for(var l=!1,n=\"\",s=0;s<this.bottomGrid.dataProvider.length;s++){if(this.bottomGrid.dataProvider[s].matchid.toString().length<=0){l=!0;break}n=this.bottomGrid.dataProvider[s].matchid}for(var c=\"\",d=0;d<this.bottomGrid.dataProvider.length;d++)c+=this.bottomGrid.dataProvider[d].movement+\",\";var h=this.entityCombo.selectedLabel;this.actionPath=\"movementmatchdisplay.do?\";this.currencyThreshold.selected||(\"Y\",\"1\"),this.actionMethod=\"method=offeredMatch&selectedList=\"+c+\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\")+\"&selectedCurrencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&entityCode=\"+h+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&tableScrollbarLeft=&tableScrollbarTop=&scrollbarLeft=&scrollbarTop=\",null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),l||(this.actionMethod+=\"&matchId=\"+n),l||this.unlockMvmnt(null),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.cGrid.selectedIndices=[],this.bottomGrid.dataProvider=[];var u=[];for(d=0;d<this.bottomGrid.dataProvider.length;d++)u.push(this.bottomGrid.angularGridInstance.gridService.getDataItemByRowNumber(d));this.bottomGrid.angularGridInstance.gridService.deleteItems(u),this.selectedMvmts=[],r.x.call(\"offeredMatches\",this.inputData.url),this.cGrid.selectedIndices.length<=0&&(this.messageButton.enabled=!1,this.messageButton.buttonMode=!1,this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.matchButton.enabled=!1,this.matchButton.buttonMode=!1,this.removeButton.enabled=!1,this.removeButton.buttonMode=!1),this.dataRefresh()}}},e.prototype.remove=function(){try{for(var t=0,e=\"\",i=0;i<this.selectedMovements.length;i++)this.selectedMovements[i]==this.bottomGrid.dataProvider[this.bottomGrid.selectedIndex].movement&&this.selectedMovements.splice(i,1);var a=[],o=void 0,l=r.x.call(\"checkLockOnServer\",this.bottomGrid.dataProvider[this.bottomGrid.selectedIndex].movement);this.currentUser.toLowerCase()!=l.toLowerCase()&&\"true\"!=l.toString()||(o=r.x.call(\"unlockMovementOnServer\",this.bottomGrid.dataProvider[this.bottomGrid.selectedIndex].movement));var n=o.toString(),s=this.selectedMvmts.indexOf(this.bottomGrid.dataProvider[this.bottomGrid.selectedIndex].movement.toString(),0);s>-1&&this.selectedMvmts.splice(s,1),this.bottomGrid.angularGridInstance.gridService.deleteItem(this.bottomGrid.angularGridInstance.gridService.getDataItemByRowNumber(this.bottomGrid.selectedIndex));for(var c=\"\",d=0;d<this.bottomGrid.dataProvider.length;d++)c+=this.bottomGrid.dataProvider[d].movement+\",\";if(r.x.call(\"setSelectedMovementForLock\",c),h.a.parseBooleanValue(n)){for(var u=0;u<this.bottomGrid.dataProvider.length;u++)for(var m=0;m<this.cGrid.dataProvider.length;m++)this.bottomGrid.dataProvider[u].movement==this.cGrid.dataProvider[m].movement&&a.push(m);this.cGrid.selectedIndices=a,this.removeButton.enabled=!1,this.removeButton.buttonMode=!1,this.cGrid.selectedIndices.length<=0&&(this.messageButton.enabled=!1,this.messageButton.buttonMode=!1,this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.matchButton.enabled=!1,this.matchButton.buttonMode=!1,null!=this.bottomGrid&&(1==this.bottomGrid.dataProvider.length?(this.movementButton.enabled=!0,this.movementButton.buttonMode=!0,this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,this.messageButton.enabled=!0,this.messageButton.buttonMode=!0,this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0,this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0,this.matchButton.enabled=!0,this.matchButton.buttonMode=!0):this.bottomGrid.dataProvider.length>1&&(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0,this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0,this.matchButton.enabled=!0,this.matchButton.buttonMode=!0))),null!=this.bottomGrid&&(1==this.bottomGrid.dataProvider.length?(this.movementButton.enabled=!0,this.movementButton.buttonMode=!0,this.noteButton.enabled=!0,this.noteButton.buttonMode=!0,this.messageButton.enabled=!0,this.messageButton.buttonMode=!0):0==this.bottomGrid.dataProvider.length&&(this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.messageButton.enabled=!1,this.messageButton.buttonMode=!1,this.matchButton.enabled=!1,this.matchButton.buttonMode=!1));var v=!1;if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0){for(var p=0;p<this.bottomGrid.dataProvider.length;p++)this.bottomGrid.dataProvider[p].matchid.toString().length>0&&(v=!0);v?(this.reconButton.enabled=!1,this.reconButton.buttonMode=!1):0!=this.bottomGrid.dataProvider.length&&(this.reconButton.enabled=!0,this.reconButton.buttonMode=!0)}var b=!1,g=null;if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0){for(var f=0;f<this.bottomGrid.dataProvider.length;f++)if(null==g&&this.bottomGrid.dataProvider[f].matchid.toString().length>0)g=this.bottomGrid.dataProvider[f].matchid;else if(this.bottomGrid.dataProvider[f].matchid.toString().length>0&&g!=this.bottomGrid.dataProvider[f].matchid){b=!0;break}if(b){this.matchButton.enabled=!1,this.matchButton.buttonMode=!1;r.x.call(\"unlockMovementOnServer\",this.bottomGrid.dataProvider[f].movement),this.swtAlert.warning(r.Wb.getPredictMessage(\"label.selectedOnlyMatchedItems\",null),\"Warning\")}else this.matchButton.enabled=!0,this.matchButton.buttonMode=!0}}else this.swtAlert.warning(r.Wb.getPredictMessage(\"label.movementCannotBeUnlocked\",null),\"Warning\");var x=!0,S=\"\";if(null!=this.bottomGrid&&this.bottomGrid.dataProvider.length>0)for(var y=0;y<this.bottomGrid.dataProvider.length;y++)S=r.x.call(\"accountAccessConfirm\",this.bottomGrid.dataProvider[y].movement.toString(),this.entityCombo.selectedLabel.toString()).toString(),h.a.parseBooleanValue(S)||(x=!1);if(x?0!=this.bottomGrid.dataProvider.length&&(this.confirmButton.enabled=!0,this.confirmButton.buttonMode=!0,this.suspendButton.enabled=!0,this.suspendButton.buttonMode=!0,this.matchButton.enabled=!0,this.matchButton.buttonMode=!0):0!=this.bottomGrid.dataProvider.length&&(this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.matchButton.enabled=!1,this.matchButton.buttonMode=!1,this.reconButton.enabled=!1,this.reconButton.buttonMode=!1),this.bottomGrid.setRowSize=this.bottomGrid.dataProvider.length,this.bottomGrid.dataProvider.length>0)for(var M=void 0,P=0,G=\"\",C=[],I=this.jsonReader.getScreenAttributes().currencyFormat,B=\"\",T=0;T<this.bottomGrid.dataProvider.length;T++){if(M=this.bottomGrid.dataProvider[T].sign,G=this.bottomGrid.dataProvider[T].amount,\"currencyPat1\"==I){C=G.split(\",\"),G=\"\";for(var D=0;D<C.length;D++)G+=C[D]}else{C=G.split(\".\"),G=\"\";for(var A=0;A<C.length;A++)G+=C[A];G=G.replace(\",\",\".\")}P=parseFloat(G),\"D\"==M?t-=P:t+=P}var w=this.jsonReader.getScreenAttributes().currencyFormat,F=this.addZeroes(t.toString());if(-1==F.indexOf(\".\"))F+=\"00\";else{var R;R=F.split(\".\"),F=R[0]+R[1]}e=r.x.call(\"expandAmtDifference\",F,w,B),t<0&&e.length>0&&\"-\"!=e.charAt(0)?this.totalSelectedValue.text=\"-\"+r.x.call(\"expandAmtDifference\",F,w,B):this.totalSelectedValue.text=r.x.call(\"expandAmtDifference\",F,w,B),this.bottomGrid.selectedIndex=-1}catch(k){console.log(\"e\",k)}},e.prototype.filter=function(){var t=\"\";try{t+=\"&amountover=\"+r.x.call(\"amountOver\"),t+=\"&amountunder=\"+r.x.call(\"amountUnder\"),t+=\"&group=\"+r.x.call(\"getGroup\"),t+=\"&metaGroup=\"+r.x.call(\"getMetagroup\"),\"\"==r.x.call(\"valueFromDate\")?(t+=\"&valueFromDate=\"+r.x.call(\"eval\",\"tabPressed\"),t+=\"&valueToDate=\"+r.x.call(\"eval\",\"tabPressed\")):(t+=\"&valueFromDate=\"+r.x.call(\"valueFromDate\"),t+=\"&valueToDate=\"+r.x.call(\"valueToDate\")),t+=\"&timefrom=\"+r.x.call(\"timefrom\"),t+=\"&timeto=\"+r.x.call(\"timeto\"),t+=\"&inputDate=\"+r.x.call(\"inputDate\"),t+=\"&reference=\"+r.x.call(\"reference\"),t+=\"&refFlagFilterSearch=\"+r.x.call(\"referenceFlag\"),t+=\"&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),t+=\"&currentFilterConf=\"+r.Z.encode64(this.filterComboMSD.selectedLabel),t+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),t+=\"&initialinputscreen=\"+this.jsonReader.getScreenAttributes().initialinputscreen,r.x.call(\"searchmovements\",\"filterOutMov\",this.entityCombo.selectedLabel,r.x.call(\"eval\",\"currencyCode\"),this.jsonReader.getScreenAttributes().selectedSort,this.jsonReader.getScreenAttributes().selectedFilter,r.x.call(\"eval\",\"posLvlId\"),r.x.call(\"eval\",\"workflow\"),t)}catch(e){console.log(\"error in filter\",e)}},e.prototype.helphandler=function(){r.x.call(\"help\")},e.prototype.alertListenerMatch=function(t){var e=!1;if(t.detail==r.c.OK&&(e=!0),e)if(this.checkSelectedMovementsAmounts()){for(var i=!1,a=\"\",o=0;o<this.bottomGrid.dataProvider.length;o++){if(this.bottomGrid.dataProvider[o].matchid.toString.length<=0){i=!0;break}a=this.bottomGrid.dataProvider[o].matchid}for(var l=\"\",n=0;n<this.bottomGrid.dataProvider.length;n++)l+=this.bottomGrid.dataProvider[n].movement+\",\";var s=this.entityCombo.selectedLabel;this.actionPath=\"movementmatchdisplay.do?\";this.currencyThreshold.selected||(\"Y\",\"1\"),this.actionMethod=\"method=offeredMatch&selectedList=\"+l+\"&selectedTab=\"+r.x.call(\"eval\",\"tabPressed\")+\"&selectedCurrencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&entityCode=\"+s+\"&currentPage=\"+this.jsonReader.getScreenAttributes().pages.currentPage+\"&tableScrollbarLeft=&tableScrollbarTop=&scrollbarLeft=&scrollbarTop=\",null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),i||(this.actionMethod+=\"&matchId=\"+a),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,r.x.call(\"offeredMatches\",this.inputData.url),this.cGrid.selectedIndices=[],this.bottomGrid.dataProvider=[];var c=[];for(n=0;n<this.bottomGrid.dataProvider.length;n++)c.push(this.bottomGrid.angularGridInstance.gridService.getDataItemByRowNumber(n));this.bottomGrid.angularGridInstance.gridService.deleteItems(c),this.selectedMvmts=[],i&&(this.cGrid.selectedIndices.length<=0&&(this.messageButton.enabled=!1,this.messageButton.buttonMode=!1,this.movementButton.enabled=!1,this.movementButton.buttonMode=!1,this.noteButton.enabled=!1,this.noteButton.buttonMode=!1,this.reconButton.enabled=!1,this.reconButton.buttonMode=!1,this.confirmButton.enabled=!1,this.confirmButton.buttonMode=!1,this.suspendButton.enabled=!1,this.suspendButton.buttonMode=!1,this.matchButton.enabled=!1,this.matchButton.buttonMode=!1,this.removeButton.enabled=!1,this.removeButton.buttonMode=!1),this.dataRefresh(null))}else{e=!1;this.swtAlert.question(\"The amounts of the selected movements differ. Do you want to continue?\",\"\",r.c.OK|r.c.CANCEL,this,this.alertListener.bind(this),null)}},e.prototype.fontSettingHandler=function(){var t=this;this.win=r.Eb.createPopUp(this,c.a,{title:\"Options\",fontSizeValue:this.selectedFont}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width=\"550\",this.win.height=\"170\",this.win.showControls=!0,this.win.id=\"optionsWindow\",this.win.onClose.subscribe(function(e){t.submitFontSize(e)}),this.win.display()},e.prototype.submitFontSize=function(t){this.fontValue=t.fontSize.value,this.fontLabel=t.fontSize.label,\"N\"==this.fontValue?(this.selectedFont=0,this.cGrid.styleName=\"dataGridNormal\",this.cGrid.rowHeight=18,null!=this.bottomGrid&&(this.bottomGrid.styleName=\"dataGridNormal\",this.bottomGrid.rowHeight=18),this.fontRequest=r.x.call(\"getUpdateFontSize\",this.fontLabel)):\"S\"==this.fontValue&&(this.selectedFont=1,this.cGrid.styleName=\"dataGridSmall\",this.cGrid.rowHeight=15,null!=this.bottomGrid&&(this.bottomGrid.styleName=\"dataGridSmall\",this.bottomGrid.rowHeight=15),this.fontRequest=r.x.call(\"getUpdateFontSize\",this.fontLabel)),null!=this.fontRequest&&\"\"!=this.fontRequest&&(this.updateFontSize.url=this.baseURL+this.fontRequest,this.updateFontSize.send())},e.prototype.showHideButtonBar=function(){this.buttonBarHideFlag?(this.totalsPanel.includeInLayout=!0,this.totalsPanel.visible=!0,this.imgShowHideButtonBar.styleName=\"minusIcon\"):(this.totalsPanel.visible=!1,this.imgShowHideButtonBar.styleName=\"plusIcon\",this.totalsPanel.includeInLayout=!1),this.cGrid.resizeGrid(),null!=this.bottomGrid&&this.bottomGrid.resizeGrid(),this.imgShowHideButtonBar.toolTip=this.buttonBarHideFlag?\"Hide Button Bar\":\"Show Button Bar\",this.buttonBarHideFlag=!this.buttonBarHideFlag},e.prototype.saveFilter=function(t){for(var e=[],i=0;i<this.filterComboMSD.dataProvider.length;i++)e.push(this.filterComboMSD.dataProvider[i].content.toString());var a=e.indexOf(this.noneLabel);a>-1&&e.splice(a,1);this.win=r.Eb.createPopUp(this,d.a,{title:\"Save Filter\",selectedComboItem:this.filterComboMSD.selectedIndex>0?this.filterComboMSD.selectedLabel:\"\",saveProfileCollection:e,filterCombo:this.filterComboMSD}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width=\"360\",this.win.height=\"220\",this.win.showControls=!0,this.win.id=\"filterWindow\",this.win.onClose.subscribe(function(t){}),this.win.display()},e.prototype.deleteFilterClickHandler=function(t){this.swtAlert.warning(\"Are you sure you want to delete the filter \"+this.filterComboMSD.selectedLabel+\"?\",r.x.call(\"getBundle\",\"text\",\"alert-warning\",\"Warning\"),r.c.OK|r.c.CANCEL,this,this.deleteAlertListener.bind(this),null)},e.prototype.deleteAlertListener=function(t){t.detail==r.c.OK&&this.deleteFilter()},e.prototype.deleteFilter=function(){this.fromDeleteFilter=!0,this.currentFilterConf=this.filterComboMSD.selectedLabel,this.filterScreen(\"deleteFilter\",this.currentFilterConf)},e.prototype.doChangeCombo=function(){this.count%2==0&&this.changeFilter(),this.count++},e.prototype.changeFilter=function(){this.keepSelected=!0,this.cGrid.selectedIndices.length>0&&(this.lastFilterAction=\"changeFilter\"),-1==this.filterComboMSD.selectedIndex?this.currentFilterConf=this.adhocLabel:(this.lastSelectedIndex=this.filterComboMSD.selectedIndex,this.currentFilterConf=this.filterComboMSD.selectedItem.content,this.dataRefresh(\"filterCombo\")),this.enableDisableFilterCombo()},e.prototype.enableDisableFilterCombo=function(){this.currentFilterConf==this.noneLabel?(this.deleteFilterImage.enabled=!1,this.saveFilterImage.enabled=!1,this.filterButton&&this.filterButton.setStyle(\"color\",\"#0B333C\",this.filterButton.domElement)):(this.currentFilterConf==this.adhocLabel?this.deleteFilterImage.enabled=!1:this.deleteFilterImage.enabled=!0,this.saveFilterImage.enabled=!0,this.filterButton&&this.filterButton.setStyle(\"color\",\"red\",this.filterButton.domElement))},e.prototype.saveResult=function(t){var e=new r.L;e.setInputJSON(t),e.getRequestReplyStatus()?null!=this.lastFilterAction&&(\"saveFilter\"==this.lastFilterAction?this.swtAlert.show(\"The filter was successfully saved\",\"Warning\"):\"deleteFilter\"==this.lastFilterAction&&(this.currentFilterConf=this.noneLabel,this.swtAlert.show(\"The filter was successfully deleted\")),this.dataRefresh(null)):this.swtAlert.error(\"Error occurred, Please contact your System Administrator: \\n\"+e.getRequestReplyMessage(),\"Error\")},e.prototype.saveFault=function(t){this.swtAlert.error(\"Unable to save to server\\nPossible loss of connection\",\"Error\")},e.prototype.updateValues=function(t){var e,i=\"All\"!=(e=t.split(\"|\"))[2]?e[2]:\"\",a=\"All\"!=e[3]?e[3]:\"\",o=\"All\"!=e[4]?e[4]:\"\",l=\"All\"!=e[5]?e[5]:\"\",n=\"All\"!=e[12]?e[12]:\"\",s=\"All\"!=e[13]?e[13]:\"\",c=\"All\"!=e[15]?e[15]:\"\",d=\"All\"!=e[16]?e[16]:\"\",h=\"All\"!=e[14]?e[14]:\"\",u=r.Z.encode64(e[18]),m=this.jsonReader.getScreenAttributes().openMovementFlagSearch;r.x.call(\"updateValues\",i,a,o,l,n,s,c,d,h,u,m)},e.prototype.initializeValues=function(){var t=r.x.call(\"eval\",\"tabPressed\"),e=r.x.call(\"eval\",\"tabPressed\"),i=r.Z.encode64(\"<refparams><include ref1='Y' ref2='Y' ref3='Y' ref4='Y' like='N' ></include><exclude ref1='Y' ref2='Y' ref3='Y' ref4='Y' like='N' ></exclude></refparams>\");r.x.call(\"initializeValues\",\"\",\"\",t,e,\"\",\"\",\"\",\"\",\"\",i,\"Y\")},e.prototype.doHelp=function(){r.x.call(\"help\")},e.prototype.dataRefreshGrid=function(){this.keepSelected=!0;var t=\"\",e=this.jsonReader.getScreenAttributes().access,i=r.x.call(\"eval\",\"initialscreen\"),a=r.x.call(\"eval\",\"method\"),o=\"\",l=this.jsonReader.getScreenAttributes().selectedSort;o=this.currencyThreshold.selected?\"Y\":\"N\";var n=this.cGrid.getMsdSortedGridColumn()?this.cGrid.getMsdSortedGridColumn()+\"|\":l,s=this.cGrid.getFilteredGridColumns(),c=\"\",d=\"\",h=[],u=Object.keys(this.addColumnsGridData).length;if(s&&\"Y\"==this.useAddColsCheck){h=s.split(\"|\");for(var m=0;m<h.length;m++)m<h.length-u?d=d+h[m]+\"|\":c=c+h[m]+\"|\"}-1!=(s=\"\"!=d?d.slice(0,-1):s).indexOf(\"||\")&&(s=s.replace(\"||\",\"|(EMPTY)|\")),\"|\"==s.charAt(s.length-1)&&(s=s+=\"(EMPTY)\"),\"|\"==s.charAt(0)&&(s=\"(EMPTY)\"+s);var v=\"\";if(\"search\"!=this.methodName&&\"E\"==this.initialinputscreen)for(var p=0;p<this.bottomGrid.dataProvider.length;p++)v+=this.bottomGrid.dataProvider[p].movement+\",\",this.selectedMvmts.push(\"\"+this.bottomGrid.dataProvider[p].movement);else this.selectedMvmts=[];if(t+=\"&selectedList=\"+v,t+=\"&entityId=\"+this.entityCombo.selectedLabel,t+=\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\"),t+=\"&date=\"+r.x.call(\"eval\",\"dateStr\"),t+=\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\"),t+=\"&totalCount=\"+this.jsonReader.getRowSize(),t+=\"&applyCurrencyThreshold=\"+o,t+=\"&filterAcctType=\"+this.jsonReader.getScreenAttributes().filterAcctType.toString().replace(/\\%/g,\"per;\"),t+=\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialscreen\"),t+=\"&openFlag=\"+r.x.call(\"eval\",\"openFlag\"),t+=\"&openMovementFlagSearch=\"+r.x.call(\"openMovementFlag\"),\"E\"==i&&(t+=\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")),null!=r.x.call(\"eval\",\"workflow\")&&(t+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\")),\"search\"!=a&&\"E\"==i&&(t=\"method=displayOpenMovements\"+t),\"search\"==a||\"E\"!=i){if(\"readOnly\"==e&&(t=\"method=displayOpenMovements\"+t+\"&menuAccessId=\"+r.x.call(\"eval\",\"menuAccessId\")),\"readOnly\"!=e&&(\"currencymonitor\"!=i&&\"entitymonitor\"!=i||(t=\"method=getCurrencyMonitorMvmnts\"+t,t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&balanceType=\",t+=\"&locationId=\"+r.x.call(\"eval\",\"locationIdCurrency\")),\"currencymonitor\"!=i&&\"entitymonitor\"!=i&&(\"accountmonitor\"==i&&(t=\"method=getAccountMonitorMvmnts\"+t,t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\"),t+=\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\"),t+=\"&accountType=\"+r.x.call(\"eval\",\"accountTypeAccount\")),\"accountmonitor\"!=i&&(\"accountbreakdownmonitor\"==i&&(t=\"method=getAccountMonitorMvmnts\"+t,t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\"),t+=\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\")),\"accountbreakdownmonitor\"!=i&&(\"bookmonitor\"==i&&(t=\"method=getBookMonitorMvmnts\"+t,t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeBook\")),\"bookmonitor\"!=i&&(\"mvmntsfromWorkFlowMonitor\"==i&&(t=\"method=getMvmntsfromWorkFlowMonitor\"+t,t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&tabIndicator=\"+r.x.call(\"eval\",\"tabIndicatorWorkflow\"),t+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),t+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\"),t+=\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusWorkflow\"),t+=\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusWorkflow\"),t+=\"&linkFlag=\"+r.x.call(\"eval\",\"linkFlagWorkflow\")),\"mvmntsfromWorkFlowMonitor\"!=i&&(\"unSettledYesterday\"==i&&(t=\"method=getUnsettledYesterdayMvmnts\"+t,t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),t+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\")),\"unSettledYesterday\"!=i&&(\"backValuedMvmnts\"==i&&(t=\"method=getBackValuedMvmnts\"+t,t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),t+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\")),\"backValuedMvmnts\"!=i&&(\"openUnexpectedMvmnts\"==i&&(t=\"method=getOpenUnexpectedMvmnts\"+t,t+=\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\"),t+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"),t+=\"&roleId=\"+r.x.call(\"eval\",\"roleId\")),\"openUnexpectedMvmnts\"!=i))))))))){t=\"method=search\"+t,t+=\"&archiveId=\"+r.x.call(\"eval\",\"archiveIdSearch\"),t+=\"&referenceFlag=\"+r.x.call(\"eval\",\"referenceFlagSearch\");t+=\"&filterCriteria=\"+(\"entityId=\"+r.x.call(\"eval\",\"entityIdSearch\")+\"|movementType=\"+r.x.call(\"eval\",\"movementTypeSearch\")+\"|sign=\"+r.x.call(\"eval\",\"signSearch\")+\"|predictStatus=\"+r.x.call(\"eval\",\"predictStatusSearch\")+\"|amountover=\"+r.x.call(\"eval\",\"amountoverSearch\")+\"|amountunder=\"+r.x.call(\"eval\",\"amountunderSearch\")+\"|currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"|paymentChannelId=\"+r.x.call(\"eval\",\"paymentChannelIdSearch\")+\"|beneficiaryId=\"+r.x.call(\"eval\",\"beneficiaryIdSearch\")+\"|custodianId=\"+r.x.call(\"eval\",\"custodianIdSearch\")+\"|positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")+\"|accountId=\"+r.x.call(\"eval\",\"accountIdSearch\")+\"|group=\"+r.x.call(\"eval\",\"groupSearch\")+\"|metaGroup=\"+r.x.call(\"eval\",\"metaGroupSearch\")+\"|bookCode=\"+r.x.call(\"eval\",\"bookCodeSearch\")+\"|valueFromDateAsString=\"+r.x.call(\"eval\",\"valueFromDateAsStringSearch\")+\"|valueToDateAsString=\"+r.x.call(\"eval\",\"valueToDateAsStringSearch\")+\"|timefrom=\"+r.x.call(\"eval\",\"timefromSearch\")+\"|timeto=\"+r.x.call(\"eval\",\"timetoSearch\")+\"|reference=\"+r.x.call(\"eval\",\"referenceSearch\")+\"|messageId=\"+r.x.call(\"eval\",\"messageIdSearch\")+\"|inputDateAsString=\"+r.x.call(\"eval\",\"inputDateAsStringSearch\")+\"|counterPartyId=\"+r.x.call(\"eval\",\"counterPartyIdSearch\")+\"|fintrade=\"+r.x.call(\"eval\",\"fintradeSearch\")+\"|matchStatus=\"+r.x.call(\"eval\",\"matchStatusSearch\")+\"|initialinputscreen=\"+this.jsonReader.getScreenAttributes().initialinputscreen+\"|accountClass=\"+r.x.call(\"eval\",\"accountClassSearch\")+\"|producttype=\"+r.x.call(\"eval\",\"producttype\")+\"|uetr=\"+r.x.call(\"eval\",\"uetr\")+\"|matchingparty=\"+r.x.call(\"eval\",\"matchingparty\")+\"|postingDateFrom=\"+r.x.call(\"eval\",\"postingDateFrom\")+\"|postingDateTo=\"+r.x.call(\"eval\",\"postingDateTo\")+\"|positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")),t+=\"&filterFromSerach=true\",t+=\"&filterFromSerach=true\",t+=\"&extraFilter=\"+r.x.call(\"eval\",\"extraFilter\")}t+=\"&extBalStatus=\"+r.x.call(\"eval\",\"extBalStatusSearch\"),t+=\"&extraFilter=\"+r.x.call(\"eval\",\"extraFilter\")}\"X\"==i&&(null!=r.x.call(\"eval\",\"scenarioId\")&&(t+=\"&scenarioId=\"+r.x.call(\"eval\",\"scenarioId\"),t+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")),t+=\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\"));var b=\"\"!=c?s+\"|\"+c:s,g=/^(All\\|)+All\\|?$/.test(b)?\"All\":r.Z.encode64(this.gridColsToXmlFilter(s,c));t+=\"&currentPage=1\",t+=\"&selectedSort=\"+r.Z.encode64(this.getSortAddColumns(n.slice(0,-1))),t+=\"&addColsSort=\"+r.Z.encode64(this.getSortAddColumns(n.slice(0,-1))),t+=\"&selectedFilter=\"+g,t+=\"&addColsFilter=\"+r.Z.encode64(this.getFilterAddColumns(c)),t+=\"&currentFilterConf=\"+r.t.encode64(this.currentFilterConf),this.inputData.url=this.baseURL+\"outstandingmovement.do?\"+t;this.inputData.send([])},e.prototype.replaceAt=function(t,e,i){return t.substring(0,e)+i+t.substring(e+1)},e.prototype.getParamsFromParent=function(){return{sqlParams:this.lastSelectedTooltipParams,facilityId:this.tooltipFacilityId,selectedNodeId:this.selectedNodeId,treeLevelValue:this.treeLevelValue,tooltipMvtId:this.tooltipMvtId,tooltipOtherParams:this.tooltipOtherParams}},e.prototype.createTooltip=function(t){var e=this;this.customTooltip&&this.customTooltip.close&&this.removeTooltip();try{this.customTooltip=r.Eb.createPopUp(parent,r.u,{}),this.customTooltip.enableResize=!1,this.customTooltip.width=\"420\",this.customTooltip.height=\"450\",this.customTooltip.enableResize=!1,this.customTooltip.title=\"Alert Summary Tooltip\",this.customTooltip.showControls=!0,window.innerHeight<this.positionY+450&&(this.positionY=120),this.customTooltip.setWindowXY(this.positionX+20,this.positionY),this.customTooltip.showHeader=!1,this.customTooltip.parentDocument=this,this.customTooltip.processBox=this,this.customTooltip.display(),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().DISPLAY_LIST_CLICK.subscribe(function(t){e.lastSelectedTooltipParams=t.noode.data,r.x.call(\"openAlertInstanceSummary\",\"openAlertInstSummary\",e.selectedNodeId,e.treeLevelValue)})},0),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().LINK_TO_SPECIF_CLICK.subscribe(function(t){e.getScenarioFacility(t.noode.data.scenario_id),e.lastSelectedTooltipParams=t.noode.data,e.hostId=t.hostId})},0),setTimeout(function(){e.eventsCreated||e.customTooltip.getChild().ITEM_CLICK.subscribe(function(t){e.selectedNodeId=t.noode.data.id,e.treeLevelValue=t.noode.data.treeLevelValue,e.customTooltip.getChild().linkToSpecificButton.enabled=!1,1==t.noode.data.count&&0==t.noode.isBranch&&(e.customTooltip.getChild().linkToSpecificButton.enabled=!0)})},0)}catch(i){console.log(\"SwtCommonGrid -> createTooltip -> error\",i)}},e.prototype.getScenarioFacility=function(t){var e=this;this.requestParams=[],this.alertingData.cbStart=this.startOfComms.bind(this),this.alertingData.cbStop=this.endOfComms.bind(this),this.alertingData.cbFault=this.inputDataFault.bind(this),this.alertingData.encodeURL=!1,this.actionPath=\"scenarioSummary.do?\",this.actionMethod=\"method=getScenarioFacility\",this.requestParams.scenarioId=t,this.alertingData.url=this.baseURL+this.actionPath+this.actionMethod,this.alertingData.cbResult=function(t){e.openGoToScreen(t)},this.alertingData.send(this.requestParams)},e.prototype.openGoToScreen=function(t){if(t&&t.ScenarioSummary&&t.ScenarioSummary.scenarioFacility){var e=t.ScenarioSummary.scenarioFacility,i=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.entity_id?this.lastSelectedTooltipParams.entity_id:this.entityCombo.selectedLabel,a=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.currency_code?this.lastSelectedTooltipParams.currency_code:\"All\",o=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.match_id?this.lastSelectedTooltipParams.match_id:null,l=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.movement_id?this.lastSelectedTooltipParams.movement_id:this.tooltipMvtId,n=null!=this.lastSelectedTooltipParams&&null!=this.lastSelectedTooltipParams.sweep_id?this.lastSelectedTooltipParams.sweep_id:null;r.x.call(\"goTo\",e,this.hostId,i,o,a,l,n,\"\")}},e.prototype.removeTooltip=function(){null!=this.customTooltip&&this.customTooltip.close()},e.prototype.itemClickFunction=function(t){var e=this;null==t.selectedCellTarget||null==t.selectedCellTarget.field||\"alerting\"!=t.selectedCellTarget.field||\"C\"!=t.selectedCellTarget.data.alerting&&\"Y\"!=t.selectedCellTarget.data.alerting?this.removeTooltip():(this.tooltipMvtId=t.selectedCellTarget.data.movement,this.tooltipOtherParams=[],this.tooltipOtherParams.currencythresholdFlag=this.currencyThreshold.selected?\"Y\":\"N\",this.tooltipFacilityId=\"MOVEMENT_SUMMARY_DISPLAY_ROW\",this.tooltipOtherParams=[],setTimeout(function(){e.createTooltip(null)},100))},e.prototype.itemClickFunction2=function(t){var e=this;null==t.target||null==t.target.field||\"alerting\"!=t.target.field||\"C\"!=t.target.data.alerting&&\"Y\"!=t.target.data.alerting?this.removeTooltip():(this.tooltipMvtId=t.target.data.movement,this.tooltipFacilityId=\"MOVEMENT_SUMMARY_DISPLAY_ROW\",this.tooltipOtherParams=[],setTimeout(function(){e.createTooltip(null)},100))},e.prototype.addColumn=function(t,e){r.x.call(\"addColumns\",\"addColumnsScreen\",t,e)},e.prototype.columnWidthChange=function(t){this.columnsNewWidths=\"\";for(var e=this.cGrid.gridObj.getColumns(),i=0;i<e.length;i++)\"dummy\"!=e[i].id&&(this.columnsNewWidths=this.columnsNewWidths+e[i].field+\"=\"+e[i].width+\",\");this.columnsNewWidths=this.columnsNewWidths.substring(0,this.columnsNewWidths.length-1),this.requestParams=[],this.widthData.encodeURL=!1,this.actionMethod=\"method=saveColumnWidth\",this.actionPath=\"outstandingmovement.do?\",this.requestParams.width=this.columnsNewWidths,this.requestParams.entityid=this.entityCombo.selectedLabel,this.requestParams.currentProfile=this.currentProfile&&\"Y\"==this.useAddColsCheck?this.currentProfile:\"<None>\",this.widthData.cbStart=this.startOfComms.bind(this),this.widthData.cbStop=this.endOfComms.bind(this),this.widthData.cbResult=function(t){},this.widthData.url=this.baseURL+this.actionPath+this.actionMethod,this.widthData.send(this.requestParams,null)},e.prototype.columnOrderChange=function(t){this.currentProfile;this.columnsNewOrders=\"\",this.columnDefinitionsTempArray=t;for(var e=0;e<this.columnDefinitionsTempArray.length;e++)\"dummy\"!=this.columnDefinitionsTempArray[e].id&&(this.columnsNewOrders=this.columnsNewOrders+this.columnDefinitionsTempArray[e].field+\",\");this.columnsNewOrders=this.columnsNewOrders.substring(0,this.columnsNewOrders.length-1),this.requestParams=[],this.ordertData.encodeURL=!1,this.actionMethod=\"method=saveColumnOrder\",this.actionPath=\"outstandingmovement.do?\",this.requestParams.order=this.columnsNewOrders,this.requestParams.entityid=this.entityCombo.selectedLabel,this.requestParams.currentProfile=this.currentProfile&&\"Y\"==this.useAddColsCheck?this.currentProfile:\"<None>\",this.ordertData.cbStart=this.startOfComms.bind(this),this.ordertData.cbStop=this.endOfComms.bind(this),this.ordertData.cbResult=function(t){},this.ordertData.url=this.baseURL+this.actionPath+this.actionMethod,this.ordertData.send(this.requestParams,null)},e.prototype.saveLastProfile=function(t,e){var i=this;this.requestParams=[],this.currentProfile=t,this.useAddColsCheck=e,this.msdColsData.encodeURL=!1,this.actionMethod=\"method=saveLastProfile\",this.actionPath=\"outstandingmovement.do?\",this.requestParams.entityId=this.entityCombo.selectedLabel,this.requestParams.lastProfile=t,this.requestParams.useAdditionalCols=e,this.requestParams.currentFilterConf=this.currentFilterConf,this.msdColsData.cbStart=this.startOfComms.bind(this),this.msdColsData.cbStop=this.endOfComms.bind(this),this.msdColsData.cbResult=function(t){i.updateGridData()},this.msdColsData.url=this.baseURL+this.actionPath+this.actionMethod,this.msdColsData.send(this.requestParams,null)},e.prototype.updateGridData=function(){var t=this;this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath=\"outstandingmovement.do?\",this.actionMethod=\"method=\"+this.actionMethodName,this.cGrid.resetFilter(),\"search\"==this.actionMethodName?(this.filterArea.removeAllChildren(),this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdSearch\")+\"&movementType=\"+r.x.call(\"eval\",\"movementTypeSearch\")+\"&sign=\"+r.x.call(\"eval\",\"signSearch\")+\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusSearch\")+\"&amountover=\"+r.x.call(\"eval\",\"amountoverSearch\")+\"&amountunder=\"+r.x.call(\"eval\",\"amountunderSearch\")+\"&archiveId=\"+r.x.call(\"eval\",\"archiveIdSearch\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&paymentChannelId=\"+r.x.call(\"eval\",\"paymentChannelIdSearch\")+\"&beneficiaryId=\"+r.x.call(\"eval\",\"beneficiaryIdSearch\")+\"&custodianId=\"+r.x.call(\"eval\",\"custodianIdSearch\")+\"&positionlevel=\"+r.x.call(\"eval\",\"positionlevelSearch\")+\"&accountId=\"+r.x.call(\"eval\",\"accountIdSearch\")+\"&group=\"+r.x.call(\"eval\",\"groupSearch\")+\"&metaGroup=\"+r.x.call(\"eval\",\"metaGroupSearch\")+\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeSearch\")+\"&valueFromDateAsString=\"+r.x.call(\"eval\",\"valueFromDateAsStringSearch\")+\"&valueToDateAsString=\"+r.x.call(\"eval\",\"valueToDateAsStringSearch\")+\"&timefrom=\"+r.x.call(\"eval\",\"timefromSearch\")+\"&timeto=\"+r.x.call(\"eval\",\"timetoSearch\")+\"&reference=\"+r.x.call(\"eval\",\"referenceSearch\")+\"&messageId=\"+r.x.call(\"eval\",\"messageIdSearch\")+\"&inputDateAsString=\"+r.x.call(\"eval\",\"inputDateAsStringSearch\")+\"&counterPartyId=\"+r.x.call(\"eval\",\"counterPartyIdSearch\")+\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusSearch\")+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenSearch\")+\"&accountClass=\"+r.x.call(\"eval\",\"accountClassSearch\")+\"&isAmountDiffer=\"+r.x.call(\"eval\",\"isAmountDifferSearch\")+\"&referenceFlag=\"+r.x.call(\"eval\",\"referenceFlagSearch\")+\"&matchingparty=\"+r.x.call(\"eval\",\"matchingparty\")+\"&producttype=\"+r.x.call(\"eval\",\"producttype\")+\"&uetr=\"+r.x.call(\"eval\",\"uetr\")+\"&postingDateFrom=\"+r.x.call(\"eval\",\"postingDateFrom\")+\"&postingDateTo=\"+r.x.call(\"eval\",\"postingDateTo\")+\"&selectedMovementsAmount=\"+r.x.call(\"eval\",\"selectedMovementsAmountSearch\")+\"&openFlag=\"+r.x.call(\"eval\",\"openFlag\")+\"&extBalStatus=\"+r.x.call(\"eval\",\"extBalStatusSearch\"),null!=r.x.call(\"eval\",\"scenarioId\")&&(this.actionMethod+=\"&scenarioId=\"+r.x.call(\"eval\",\"scenarioId\"),this.actionMethod+=\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThreshold\"),this.actionMethod+=\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\"))):\"getBookMonitorMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdBook\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&bookCode=\"+r.x.call(\"eval\",\"bookCodeBook\")+\"&selectedTabIndex=\"+r.x.call(\"eval\",\"selectedTabIndexBook\")+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenBook\"):\"displayOpenMovements\"==this.actionMethodName?(this.actionMethod=this.actionMethod+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenOpenMovements\")+\"&totalFlag=\"+r.x.call(\"eval\",\"totalFlagOpenMovements\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&date=\"+r.x.call(\"eval\",\"dateOpenMovements\")+\"&entityId=\"+r.x.call(\"eval\",\"entityIdOpenMovements\")+\"&menuAccessId=\"+this.menuAccessId+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdOpenMovements\"),null!=r.x.call(\"eval\",\"workflow\")&&(this.actionMethod+=\"&workflow=\"+r.x.call(\"eval\",\"workflow\"))):\"getCurrencyMonitorMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenCurrency\")+\"&entityId=\"+r.x.call(\"eval\",\"entityIdCurrency\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&callfromcurrency=y\":\"getAccountMonitorMvmnts\"==this.actionMethodName?(this.actionMethod=this.actionMethod+\"&initialinputscreen=\"+r.x.call(\"eval\",\"initialinputscreenAccount\")+\"&entityId=\"+r.x.call(\"eval\",\"entityIdAccount\")+\"&currencyCode=\"+r.x.call(\"eval\",\"currencyCode\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&balanceType=\"+r.x.call(\"eval\",\"balanceTypeAccount\")+\"&accountId=\"+r.x.call(\"eval\",\"accountIdAccount\")+\"&accountType=\"+r.x.call(\"eval\",\"accountTypeAccount\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdAccount\"),r.x.call(\"eval\",\"applyCurrencyThresholdIndAccount\")):\"getUnsettledYesterdayMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdUnsetteled\")+\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")+\"&totalCount=\"+r.x.call(\"eval\",\"totalCountUnsetteled\")+\"&roleId=\"+r.x.call(\"eval\",\"roleId\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdUnsetteled\"):\"getOpenUnexpectedMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdUnexpected\")+\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&totalCount=\"+r.x.call(\"eval\",\"totalCountUnexpected\")+\"&roleId=\"+r.x.call(\"eval\",\"roleId\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdUnexpected\"):\"getBackValuedMvmnts\"==this.actionMethodName?this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdBackvalue\")+\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&totalCount=\"+r.x.call(\"eval\",\"totalCountBackvalue\")+\"&roleId=\"+r.x.call(\"eval\",\"roleId\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdBackvalue\"):\"getMvmntsfromWorkFlowMonitor\"==this.actionMethodName&&(this.actionMethod=this.actionMethod+\"&entityId=\"+r.x.call(\"eval\",\"entityIdWorkflow\")+\"&currGrp=\"+r.x.call(\"eval\",\"currGrp\")+\"&totalCount=\"+r.x.call(\"eval\",\"totalCountWorkflow\")+\"&roleId=\"+r.x.call(\"eval\",\"roleId\")+\"&posLvlId=\"+r.x.call(\"eval\",\"posLvlId\")+\"&predictStatus=\"+r.x.call(\"eval\",\"predictStatusWorkflow\")+\"&matchStatus=\"+r.x.call(\"eval\",\"matchStatusWorkflow\")+\"&applyCurrencyThreshold=\"+r.x.call(\"eval\",\"applyCurrencyThresholdWorkflow\")+\"&tabIndicator=\"+r.x.call(\"eval\",\"tabIndicatorWorkflow\")+\"&valueDate=\"+r.x.call(\"eval\",\"valueDate\")+\"&linkFlag=\"+r.x.call(\"eval\",\"linkFlagWorkflow\")),this.actionMethod+=\"&currentFilterConf=\"+r.Z.encode64(this.noneLabel),this.actionMethod+=\"&extraFilter=\"+r.x.call(\"eval\",\"extraFilter\"),this.currentFilterConf=this.noneLabel;var e=\"\";if(\"search\"!=this.methodName&&\"E\"==this.initialinputscreen)for(var i=0;i<this.bottomGrid.dataProvider.length;i++)e+=this.bottomGrid.dataProvider[i].movement+\",\";this.actionMethod+=\"&selectedList=\"+e,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.keepSelected=!0,this.inputData.send(this.requestParams)},e.prototype.updateProfileCombo=function(t,e){this.profilesList=t},e.prototype.getFilterAddColumns=function(t){try{var e=\"\",i=0;if(\"\"!=t&&\"Y\"==this.useAddColsCheck){var a=t.split(\"|\");for(var o in this.addColumnsGridData){if(\"\"!=a[i]&&\"All\"!=a[i]&&null!=a[i]){var l=o.split(\"*\")[1],n=this.addColumnsGridData[o];e=\"UPDATE_DATE\"==n||\"VALUE_DATE\"==n||\"INPUT_DATE\"==n||\"ORIG_VALUE_DATE\"==n||\"TO_MATCH_DATE\"==n||\"TO_MATCH_DATE\"==n||\"POSTING_DATE\"==n||\"SETTLEMENT_DATE\"==n||\"TIMEEXPECTED_SETTLEMENT_DATE\"==n||\"TIMEORIGINAL_EXPECTED_SETTLEMNT_DT\"==n?e+l+\".\"+n+\"=TO_DATE ('\"+a[i]+\"' , '\"+this.formatIsoTime+\"')AND \":\"required_release_time\"==n||\"cutoff_time\"==n?e+l+\".\"+n+\"='\"+a[i].substring(11,16)+\"' AND \":e+l+\".\"+n+\"='\"+a[i]+\"' AND \"}i++}}var r=e.lastIndexOf(\"AND \");return e.substring(0,r)}catch(s){console.log(\"error\",s)}},e.prototype.gridColsToXmlFilter=function(t,e){this.cGrid.columnDefinitions.sort(function(t,e){return t.columnnumber-e.columnnumber});var i=\"<filters>\",a=0,o=\"\";if(\"\"!=t&&\"All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All|All\"!=t)for(var l=t.split(\"|\"),n=0;n<this.cGrid.columnDefinitions.length;n++)if(\"All\"!=l[n]&&null!=l[n]){if(\"MultipleSelect\"==this.cGrid.columnDefinitions[n].FilterType){o=l[n].includes(\"#$#\")?\"In\":\"Equals\"}else o=\"Like\";for(var r=this.getColumnName(this.cGrid.columnDefinitions[n].field),s=\"\",c=\"\",d=0,h=this.msdDisplayColumnsData;d<h.length;d++){var m=h[d];if(r==m.columnName){s=m.tableName,c=m.dataType;break}}i+=\"<filter>\",i+=\"<column_name><![CDATA[\"+r+\"]]></column_name>\",i+=\"<table_name><![CDATA[\"+s+\"]]></table_name>\",i+=\"<type><![CDATA[\"+c+\"]]></type>\",i+=\"<operator><![CDATA[\"+o+\"]]></operator>\",i+=\"<values>\";for(var v=l[n].split(\"#$#\"),p=0;p<v.length;p++)if(\"\"==v[p]&&(v[p]=\"(EMPTY)\"),\"MATCH_STATUS\"==r&&\"(NOT EMPTY)\"!=v[p]&&\"(EMPTY)\"!=v[p])i+=\"<value><![CDATA[\"+this.getMatchStatusCode(v[p].split(\" \")[0])+\"]]></value>\";else if(\"date\"==c){i+=\"<value><![CDATA[\"+(\"(EMPTY)\"!=v[p]?this.convertToIso(v[p]):v[p])+\"]]></value>\"}else if(\"datetime\"==c){i+=\"<value><![CDATA[\"+(\"(EMPTY)\"!=v[p]?this.convertToIsoWithTime(v[p]):v[p])+\"]]></value>\"}else if(\"num\"==c&&\"AMOUNT\"==r){var b=this.jsonReader.getScreenAttributes().currencyFormat;\"(EMPTY)\"!=v[p]&&(\"currencyPat2\"==b?v[p]=Number(v[p].replace(/\\./g,\"\").replace(/,/g,\".\")):\"currencyPat1\"==b&&(v[p]=Number(v[p].replace(/,/g,\"\")))),i+=\"<value><![CDATA[\"+v[p]+\"]]></value>\"}else i+=\"<value><![CDATA[\"+v[p]+\"]]></value>\";i+=\"</values>\",i+=\"</filter>\"}if(\"\"!=e&&\"Y\"==this.useAddColsCheck){var g=e.split(\"|\");for(var f in this.addColumnsGridData){if(\"All\"!=g[a]&&null!=g[a]){o=g[a].includes(\"#$#\")?\"In\":\"Equals\";var x=f.split(\"*\")[1],S=this.addColumnsGridData[f],y=this.getColumnType(f.split(\"*\")[0]);i+=\"<filter>\",i+=\"<column_name><![CDATA[\"+S+\"]]></column_name>\",i+=\"<table_name><![CDATA[\"+x+\"]]></table_name>\",i+=\"<type><![CDATA[\"+y+\"]]></type>\",i+=\"<operator><![CDATA[\"+o+\"]]></operator>\",i+=\"<values>\";for(v=g[a].split(\"#$#\"),p=0;p<v.length;p++)if(\"\"==v[p]&&(v[p]=\"(EMPTY)\"),\"MATCH_STATUS\"==S)i+=\"<value><![CDATA[\"+this.getMatchStatusCode(v[p].split(\" \")[0])+\"]]></value>\";else if(\"date\"==y){i+=\"<value><![CDATA[\"+(\"(EMPTY)\"!=v[p]?this.convertToIso(v[p]):v[p])+\"]]></value>\"}else if(\"datetime\"==y){i+=\"<value><![CDATA[\"+(\"(EMPTY)\"!=v[p]?this.convertToIsoWithTime(v[p]):v[p])+\"]]></value>\"}else if(\"num\"==y&&\"AMOUNT\"==S){b=this.jsonReader.getScreenAttributes().currencyFormat;\"(EMPTY)\"!=v[p]&&(\"currencyPat2\"==b?v[p]=Number(v[p].replace(/\\./g,\"\").replace(/,/g,\".\")):\"currencyPat1\"==b&&(v[p]=Number(v[p].replace(/,/g,\"\")))),i+=\"<value><![CDATA[\"+v[p]+\"]]></value>\"}else i+=\"<value><![CDATA[\"+v[p]+\"]]></value>\";i+=\"</values>\",i+=\"</filter>\"}a++}}return i+=\"</filters>\",i=u.pd.xml(i)},e.prototype.getSortAddColumns=function(t){var e=this.jsonReader.getColumnData().column.length-1,i=Object.keys(this.addColumnsGridData).length,a=\"\";if(t.split(\"|\")[0]>e-i)for(var o=e;o>e-i;o--){var l=this.jsonReader.getColumnData().column[o].dataelement,n=this.jsonReader.getColumnData().column[o].columnNumber;for(var r in this.addColumnsGridData){var s=this.addColumnsGridData[r];r.split(\"*\")[1];r.split(\"*\")[0]==l&&t.split(\"|\")[0]==n&&(a=\"true\"==t.split(\"|\")[1]?s+\" DESC\":s+\" ASC\")}}else for(o=0;o<this.cGrid.columnDefinitions.length;o++)for(var c=this.cGrid.columnDefinitions[o].columnnumber,d=this.getColumnName(this.cGrid.columnDefinitions[o].field),h=0,u=this.msdDisplayColumnsData;h<u.length;h++){var m=u[h];d==m.columnName&&t.split(\"|\")[0]==c&&(m.tableName,m.dataType,a=\"true\"==t.split(\"|\")[1]?d+\" DESC\":d+\" ASC\")}return a},e.prototype.getColumnType=function(t){for(var e=\"\",i=0;i<this.cGrid.columnDefinitions.length;i++){this.cGrid.columnDefinitions[i];this.cGrid.columnDefinitions[i].field==t&&(e=this.cGrid.columnDefinitions[i].columnType)}return e},e.prototype.getTypeMapping=function(t){var e=null;switch(t){case\"str\":e=\"VARCHAR2\";break;case\"num\":e=\"NUMBER\";break;case\"date\":e=\"Date\";break;case\"amt\":e=\"AMOUNT\"}return e},e.prototype.getColumnName=function(t){var e=null;switch(t){case\"pos\":e=\"All\"==this.entityCombo.selectedLabel?\"POSITION_LEVEL\":\"POSITION_LEVEL_NAME\";break;case\"value\":e=\"VALUE_DATE\";break;case\"amount\":e=\"AMOUNT\";break;case\"sign\":e=\"SIGN\";break;case\"entity\":e=\"ENTITY_ID\";break;case\"ccy\":e=\"CURRENCY_CODE\";break;case\"ref1\":e=\"REFERENCE1\";break;case\"account\":e=\"ACCOUNT_ID\";break;case\"input\":e=\"INPUT_DATE\";break;case\"cparty\":e=\"COUNTERPARTY_ID\";break;case\"pred\":e=\"PREDICT_STATUS\";break;case\"status\":e=\"MATCH_STATUS\";break;case\"ext\":e=\"EXT_BAL_STATUS\";break;case\"matchid\":e=\"MATCH_ID\";break;case\"source\":e=\"INPUT_SOURCE\";break;case\"format\":e=\"MESSAGE_FORMAT\";break;case\"beneficiary\":e=\"BENEFICIARY_ID\";break;case\"ref2\":e=\"REFERENCE2\";break;case\"ref3\":e=\"REFERENCE3\";break;case\"xref\":e=\"REFERENCE4\";break;case\"movement\":e=\"MOVEMENT_ID\";break;case\"book\":e=\"BOOKCODE\";break;case\"custodian\":e=\"CUSTODIAN_ID\";break;case\"update_date\":e=\"UPDATE_DATE\";break;case\"matchingparty\":e=\"MATCHING_PARTY\";break;case\"producttype\":e=\"PRODUCT_TYPE\";break;case\"postingdate\":e=\"POSTING_DATE\";break;case\"extra_text1\":e=\"EXTRA_TEXT1\";break;case\"ilmfcast\":e=\"ILM_FCAST_STATUS\";break;case\"uetr\":e=\"UETR\"}return e},e.prototype.getDbTable=function(t){var e=\"\";switch(t){case\"Account\":e=\"P_ACCOUNT\";break;case\"Movement\":e=\"P_MOVEMENT\";break;case\"Movement_Ext\":e=\"P_MOVEMENT_EXT\"}return e},e.prototype.getDataProvider=function(t,e,i){if(void 0===i&&(i=!0),!t)return[];Array.isArray(t)||(t=[t]);var a=[];return t.forEach(function(t){var o=t[e]&&t[e].content?t[e].content:\"\";if(!o)i&&!a.some(function(t){return\"(EMPTY)\"===t.value})&&a.push({value:\"(EMPTY)\",label:\"(EMPTY)\"});else{i&&!a.some(function(t){return\"(NOT EMPTY)\"===t.value})&&a.push({value:\"(NOT EMPTY)\",label:\"(NOT EMPTY)\"});var l=\"status\"===e?o.split(\" \")[0]:o;a.some(function(t){return t.value===l})||a.push({value:l,label:l})}}),a.sort(function(t,e){return t.label.localeCompare(e.label)}),a},e.prototype.prepareDataProvider=function(t){for(var e=[],i=0;i<t.length;i++)e.push({value:t[i],label:t[i]});return e},e.prototype.convertToIso=function(t){var e=\"\";if(\"dd/MM/yyyy\"==this.dateFormat){var i=t.split(\"/\"),a=i[0],o=i[1];e=i[2]+\"-\"+o+\"-\"+a}else{var l=t.split(\"/\");o=l[0],a=l[1];e=l[2]+\"-\"+o+\"-\"+a}return e},e.prototype.convertToIsoWithTime=function(t){var e=\"\",i=t.split(\" \"),a=i[0],o=i[1];if(\"dd/MM/yyyy\"==this.dateFormat){var l=a.split(\"/\"),n=l[0],r=l[1];e=l[2]+\"-\"+r+\"-\"+n+\" \"+o}else{var s=a.split(\"/\");r=s[0],n=s[1];e=s[2]+\"-\"+r+\"-\"+n+\" \"+o}return e},e.prototype.getMatchStatusCode=function(t){var e=\"\";switch(t){case\"AUTHORISE\":e=\"A\";break;case\"CONFIRMED\":e=\"C\";break;case\"OFFERED\":e=\"M\";break;case\"OUTSTANDING\":e=\"L\";break;case\"RECONCILED\":e=\"E\";break;case\"REFERRED\":e=\"R\";break;case\"SUSPENDED\":e=\"S\"}return e},e.prototype.getMvtIdsList=function(){var t=this,e=0;try{e=10;var i=[];return this.bottomGrid&&this.bottomGrid.dataProvider&&this.bottomGrid.dataProvider.length>0?i=this.bottomGrid.dataProvider:this.cGrid&&this.cGrid.selectedIndices&&this.cGrid.selectedIndices.length>0&&(i=this.cGrid.selectedIndices.map(function(e){return t.cGrid.dataProvider[e]})),0===i.length?\"\":(e=20,i.map(function(t){return t.movement}).join(\",\"))}catch(a){return r.Wb.logError(a,\"Predict\",this.commonService.getQualifiedClassName(this),\"getMvtIdsList\",e),\"\"}},e.prototype.openMultiMvtActions=function(){r.x.call(\"openMultiMvtActions\",\"openMultiMvtActions\",this.getMvtIdsList(),this.entityCombo.selectedLabel)},e.prototype.checkMvtAccess=function(){var t=this;console.log(\"\\ud83d\\ude80 ~ MovementSummaryDisplay ~ checkMvtAccess ~ checkMvtAccess:\");var e=[];this.inputDataForUpdateMMA.cbStart=this.startOfComms.bind(this),this.inputDataForUpdateMMA.cbStop=this.endOfComms.bind(this),this.inputDataForUpdateMMA.cbResult=function(e){t.getMvtAccess(e)},this.inputDataForUpdateMMA.cbFault=this.inputDataFault.bind(this),this.inputDataForUpdateMMA.encodeURL=!1;e.currencyCode=r.x.call(\"eval\",\"currencyCode\"),e.entityId=this.entityCombo.selectedLabel,this.inputDataForUpdateMMA.url=this.baseURL+\"multipleMvtActions.do?method=checkMvtAccess\",this.inputDataForUpdateMMA.send(e)},e.prototype.getMvtAccess=function(t){var e,i=new r.L;i.setInputJSON(t),i.getRequestReplyStatus()?(e=t.multiMvtActions.checkFlag,console.log(\"\\ud83d\\ude80 ~ MovementSummaryDisplay ~ getMvtAccess ~ access:\",e),this.updateButton.enabled=1==e):this.swtAlert.error(i.getRequestReplyMessage()+\"\\n\"+i.getRequestReplyLocation(),\"Error\")},e}(r.yb),v=[{path:\"\",component:m}],p=(l.l.forChild(v),function(){return function(){}}()),b=i(\"pMnS\"),g=i(\"RChO\"),f=i(\"t6HQ\"),x=i(\"WFGK\"),S=i(\"5FqG\"),y=i(\"Ip0R\"),M=i(\"gIcY\"),P=i(\"t/Na\"),G=i(\"sE5F\"),C=i(\"OzfB\"),I=i(\"T7CS\"),B=i(\"S7LP\"),T=i(\"6aHO\"),D=i(\"WzUx\"),A=i(\"A7o+\"),w=i(\"zCE2\"),F=i(\"Jg5P\"),R=i(\"3R0m\"),k=i(\"hhbb\"),L=i(\"5rxC\"),O=i(\"Fzqc\"),N=i(\"21Lb\"),E=i(\"hUWP\"),j=i(\"3pJQ\"),U=i(\"V9q+\"),W=i(\"VDKW\"),q=i(\"kXfT\"),_=i(\"BGbe\");i.d(e,\"MovementSummaryDisplayModuleNgFactory\",function(){return Y}),i.d(e,\"RenderType_MovementSummaryDisplay\",function(){return J}),i.d(e,\"View_MovementSummaryDisplay_0\",function(){return z}),i.d(e,\"View_MovementSummaryDisplay_Host_0\",function(){return H}),i.d(e,\"MovementSummaryDisplayNgFactory\",function(){return Z});var Y=a.Gb(p,[],function(t){return a.Qb([a.Rb(512,a.n,a.vb,[[8,[b.a,g.a,f.a,x.a,S.Cb,S.Pb,S.r,S.rc,S.s,S.Ab,S.Bb,S.Db,S.qd,S.Hb,S.k,S.Ib,S.Nb,S.Ub,S.yb,S.Jb,S.v,S.A,S.e,S.c,S.g,S.d,S.Kb,S.f,S.ec,S.Wb,S.bc,S.ac,S.sc,S.fc,S.lc,S.jc,S.Eb,S.Fb,S.mc,S.Lb,S.nc,S.Mb,S.dc,S.Rb,S.b,S.ic,S.Yb,S.Sb,S.kc,S.y,S.Qb,S.cc,S.hc,S.pc,S.oc,S.xb,S.p,S.q,S.o,S.h,S.j,S.w,S.Zb,S.i,S.m,S.Vb,S.Ob,S.Gb,S.Xb,S.t,S.tc,S.zb,S.n,S.qc,S.a,S.z,S.rd,S.sd,S.x,S.td,S.gc,S.l,S.u,S.ud,S.Tb,Z]],[3,a.n],a.J]),a.Rb(4608,y.m,y.l,[a.F,[2,y.u]]),a.Rb(4608,M.c,M.c,[]),a.Rb(4608,M.p,M.p,[]),a.Rb(4608,P.j,P.p,[y.c,a.O,P.n]),a.Rb(4608,P.q,P.q,[P.j,P.o]),a.Rb(5120,P.a,function(t){return[t,new r.tb]},[P.q]),a.Rb(4608,P.m,P.m,[]),a.Rb(6144,P.k,null,[P.m]),a.Rb(4608,P.i,P.i,[P.k]),a.Rb(6144,P.b,null,[P.i]),a.Rb(4608,P.f,P.l,[P.b,a.B]),a.Rb(4608,P.c,P.c,[P.f]),a.Rb(4608,G.c,G.c,[]),a.Rb(4608,G.g,G.b,[]),a.Rb(5120,G.i,G.j,[]),a.Rb(4608,G.h,G.h,[G.c,G.g,G.i]),a.Rb(4608,G.f,G.a,[]),a.Rb(5120,G.d,G.k,[G.h,G.f]),a.Rb(5120,a.b,function(t,e){return[C.j(t,e)]},[y.c,a.O]),a.Rb(4608,I.a,I.a,[]),a.Rb(4608,B.a,B.a,[]),a.Rb(4608,T.a,T.a,[a.n,a.L,a.B,B.a,a.g]),a.Rb(4608,D.c,D.c,[a.n,a.g,a.B]),a.Rb(4608,D.e,D.e,[D.c]),a.Rb(4608,A.l,A.l,[]),a.Rb(4608,A.h,A.g,[]),a.Rb(4608,A.c,A.f,[]),a.Rb(4608,A.j,A.d,[]),a.Rb(4608,A.b,A.a,[]),a.Rb(4608,A.k,A.k,[A.l,A.h,A.c,A.j,A.b,A.m,A.n]),a.Rb(4608,D.i,D.i,[[2,A.k]]),a.Rb(4608,D.r,D.r,[D.L,[2,A.k],D.i]),a.Rb(4608,D.t,D.t,[]),a.Rb(4608,D.w,D.w,[]),a.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),a.Rb(1073742336,y.b,y.b,[]),a.Rb(1073742336,M.n,M.n,[]),a.Rb(1073742336,M.l,M.l,[]),a.Rb(1073742336,w.a,w.a,[]),a.Rb(1073742336,F.a,F.a,[]),a.Rb(1073742336,M.e,M.e,[]),a.Rb(1073742336,R.a,R.a,[]),a.Rb(1073742336,A.i,A.i,[]),a.Rb(1073742336,D.b,D.b,[]),a.Rb(1073742336,P.e,P.e,[]),a.Rb(1073742336,P.d,P.d,[]),a.Rb(1073742336,G.e,G.e,[]),a.Rb(1073742336,k.b,k.b,[]),a.Rb(1073742336,L.b,L.b,[]),a.Rb(1073742336,C.c,C.c,[]),a.Rb(1073742336,O.a,O.a,[]),a.Rb(1073742336,N.d,N.d,[]),a.Rb(1073742336,E.c,E.c,[]),a.Rb(1073742336,j.a,j.a,[]),a.Rb(1073742336,U.a,U.a,[[2,C.g],a.O]),a.Rb(1073742336,W.b,W.b,[]),a.Rb(1073742336,q.a,q.a,[]),a.Rb(1073742336,_.b,_.b,[]),a.Rb(1073742336,r.Tb,r.Tb,[]),a.Rb(1073742336,p,p,[]),a.Rb(256,P.n,\"XSRF-TOKEN\",[]),a.Rb(256,P.o,\"X-XSRF-TOKEN\",[]),a.Rb(256,\"config\",{},[]),a.Rb(256,A.m,void 0,[]),a.Rb(256,A.n,void 0,[]),a.Rb(256,\"popperDefaults\",{},[]),a.Rb(1024,l.i,function(){return[[{path:\"\",component:m}]]},[])])}),V=[[\"\"]],J=a.Hb({encapsulation:0,styles:V,data:{}});function z(t){return a.dc(0,[a.Zb(402653184,1,{_container:0}),a.Zb(402653184,2,{filterContainer:0}),a.Zb(402653184,3,{filterArea:0}),a.Zb(402653184,4,{buttonBox:0}),a.Zb(402653184,5,{entityCombo:0}),a.Zb(402653184,6,{selectedEntity:0}),a.Zb(402653184,7,{paginationData:0}),a.Zb(402653184,8,{numStepper:0}),a.Zb(402653184,9,{currencyThreshold:0}),a.Zb(402653184,10,{filterComboMSD:0}),a.Zb(402653184,11,{saveFilterImage:0}),a.Zb(402653184,12,{deleteFilterImage:0}),a.Zb(402653184,13,{lastRefTime:0}),a.Zb(402653184,14,{exportContainer:0}),a.Zb(402653184,15,{helpIcon:0}),a.Zb(402653184,16,{loadingImage:0}),a.Zb(402653184,17,{headerContainer:0}),a.Zb(402653184,18,{dataGridContainer:0}),a.Zb(402653184,19,{dataGridContainer2:0}),a.Zb(402653184,20,{totalsPanel:0}),a.Zb(402653184,21,{totalSelectedValue:0}),a.Zb(402653184,22,{totalInPageValue:0}),a.Zb(402653184,23,{totalOverPagesValue:0}),a.Zb(402653184,24,{lastRefTimeLabel:0}),a.Zb(402653184,25,{diffText:0}),a.Zb(402653184,26,{imgShowHideButtonBar:0}),(t()(),a.Jb(26,0,null,null,91,\"SwtModule\",[[\"height\",\"100%\"],[\"width\",\"100%\"]],null,[[null,\"creationComplete\"]],function(t,e,i){var a=!0,o=t.component;\"creationComplete\"===e&&(a=!1!==o.onLoad()&&a);return a},S.ad,S.hb)),a.Ib(27,4440064,null,0,r.yb,[a.r,r.i],{width:[0,\"width\"],height:[1,\"height\"]},{creationComplete:\"creationComplete\"}),(t()(),a.Jb(28,0,null,0,89,\"VBox\",[[\"height\",\"100%\"],[\"paddingLeft\",\"5\"],[\"paddingRight\",\"5\"],[\"paddingTop\",\"5\"],[\"verticalGap\",\"0\"],[\"width\",\"100%\"]],null,null,null,S.od,S.vb)),a.Ib(29,4440064,null,0,r.ec,[a.r,r.i,a.T],{verticalGap:[0,\"verticalGap\"],width:[1,\"width\"],height:[2,\"height\"],paddingTop:[3,\"paddingTop\"],paddingLeft:[4,\"paddingLeft\"],paddingRight:[5,\"paddingRight\"]},null),(t()(),a.Jb(30,0,null,0,33,\"SwtCanvas\",[[\"height\",\"56\"],[\"id\",\"headerContainer\"],[\"minWidth\",\"1200\"],[\"width\",\"100%\"]],null,null,null,S.Nc,S.U)),a.Ib(31,4440064,[[17,4],[\"headerContainer\",4]],0,r.db,[a.r,r.i],{id:[0,\"id\"],width:[1,\"width\"],height:[2,\"height\"],minWidth:[3,\"minWidth\"]},null),(t()(),a.Jb(32,0,null,0,31,\"HBox\",[[\"id\",\"filterContainer\"],[\"width\",\"100%\"]],null,null,null,S.Dc,S.K)),a.Ib(33,4440064,[[2,4],[\"filterContainer\",4]],0,r.C,[a.r,r.i],{id:[0,\"id\"],width:[1,\"width\"]},null),(t()(),a.Jb(34,0,null,0,7,\"HBox\",[[\"width\",\"30%\"]],null,null,null,S.Dc,S.K)),a.Ib(35,4440064,null,0,r.C,[a.r,r.i],{width:[0,\"width\"]},null),(t()(),a.Jb(36,0,null,0,1,\"SwtLabel\",[[\"text\",\"Entity\"]],null,null,null,S.Yc,S.fb)),a.Ib(37,4440064,null,0,r.vb,[a.r,r.i],{text:[0,\"text\"]},null),(t()(),a.Jb(38,0,null,0,1,\"SwtComboBox\",[[\"dataLabel\",\"entity\"],[\"enabled\",\"false\"],[\"id\",\"entityCombo\"],[\"width\",\"135\"]],null,[[\"window\",\"mousewheel\"]],function(t,e,i){var o=!0;\"window:mousewheel\"===e&&(o=!1!==a.Tb(t,39).mouseWeelEventHandler(i.target)&&o);return o},S.Pc,S.W)),a.Ib(39,4440064,[[5,4],[\"entityCombo\",4]],0,r.gb,[a.r,r.i],{dataLabel:[0,\"dataLabel\"],width:[1,\"width\"],id:[2,\"id\"],enabled:[3,\"enabled\"]},null),(t()(),a.Jb(40,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"],[\"id\",\"selectedEntity\"]],null,null,null,S.Yc,S.fb)),a.Ib(41,4440064,[[6,4],[\"selectedEntity\",4]],0,r.vb,[a.r,r.i],{id:[0,\"id\"],fontWeight:[1,\"fontWeight\"]},null),(t()(),a.Jb(42,0,null,0,3,\"HBox\",[[\"visible\",\"false\"],[\"width\",\"18%\"]],null,null,null,S.Dc,S.K)),a.Ib(43,4440064,[[7,4],[\"paginationData\",4]],0,r.C,[a.r,r.i],{width:[0,\"width\"],visible:[1,\"visible\"]},null),(t()(),a.Jb(44,0,null,0,1,\"SwtCommonGridPagination\",[],null,null,null,S.Qc,S.Y)),a.Ib(45,2211840,[[8,4],[\"numStepper\",4]],0,r.ib,[P.c,a.r],null,null),(t()(),a.Jb(46,0,null,0,17,\"HBox\",[[\"width\",\"55%\"]],null,null,null,S.Dc,S.K)),a.Ib(47,4440064,null,0,r.C,[a.r,r.i],{width:[0,\"width\"]},null),(t()(),a.Jb(48,0,null,0,15,\"VBox\",[[\"verticalGap\",\"0\"],[\"width\",\"100%\"]],null,null,null,S.od,S.vb)),a.Ib(49,4440064,null,0,r.ec,[a.r,r.i,a.T],{verticalGap:[0,\"verticalGap\"],width:[1,\"width\"]},null),(t()(),a.Jb(50,0,null,0,3,\"HBox\",[[\"height\",\"20\"],[\"horizontalAlign\",\"right\"],[\"width\",\"100%\"]],null,null,null,S.Dc,S.K)),a.Ib(51,4440064,null,0,r.C,[a.r,r.i],{horizontalAlign:[0,\"horizontalAlign\"],width:[1,\"width\"],height:[2,\"height\"]},null),(t()(),a.Jb(52,0,null,0,1,\"SwtCheckBox\",[[\"id\",\"currencyThreshold\"],[\"label\",\"Apply Currency Threshold\"]],null,[[null,\"change\"]],function(t,e,i){var a=!0,o=t.component;\"change\"===e&&(a=!1!==o.currencyThresholdChange(i)&&a);return a},S.Oc,S.V)),a.Ib(53,4440064,[[9,4],[\"currencyThreshold\",4]],0,r.eb,[a.r,r.i],{id:[0,\"id\"],label:[1,\"label\"]},{change_:\"change\"}),(t()(),a.Jb(54,0,null,0,9,\"HBox\",[[\"horizontalAlign\",\"right\"],[\"horizontalGap\",\"2\"],[\"id\",\"filterArea\"],[\"width\",\"100%\"]],null,null,null,S.Dc,S.K)),a.Ib(55,4440064,[[3,4],[\"filterArea\",4]],0,r.C,[a.r,r.i],{id:[0,\"id\"],horizontalGap:[1,\"horizontalGap\"],horizontalAlign:[2,\"horizontalAlign\"],width:[3,\"width\"]},null),(t()(),a.Jb(56,0,null,0,1,\"SwtLabel\",[[\"paddingTop\",\"1\"],[\"text\",\"Current Filter\"]],null,null,null,S.Yc,S.fb)),a.Ib(57,4440064,null,0,r.vb,[a.r,r.i],{paddingTop:[0,\"paddingTop\"],text:[1,\"text\"]},null),(t()(),a.Jb(58,0,null,0,1,\"SwtComboBox\",[[\"dataLabel\",\"filterList\"],[\"id\",\"filterComboMSD\"],[\"prompt\",\"Ad Hoc\"],[\"width\",\"332\"]],null,[[null,\"change\"],[\"window\",\"mousewheel\"]],function(t,e,i){var o=!0,l=t.component;\"window:mousewheel\"===e&&(o=!1!==a.Tb(t,59).mouseWeelEventHandler(i.target)&&o);\"change\"===e&&(o=!1!==l.changeFilter()&&o);return o},S.Pc,S.W)),a.Ib(59,4440064,[[10,4],[\"filterComboMSD\",4]],0,r.gb,[a.r,r.i],{dataLabel:[0,\"dataLabel\"],prompt:[1,\"prompt\"],width:[2,\"width\"],id:[3,\"id\"]},{change_:\"change\"}),(t()(),a.Jb(60,0,null,0,1,\"SwtButton\",[[\"enabled\",\"false\"],[\"id\",\"saveFilterImage\"],[\"marginTop\",\"3\"],[\"styleName\",\"fileSaveIcon\"],[\"toolTip\",\"Save filter\"]],null,[[null,\"click\"]],function(t,e,i){var a=!0,o=t.component;\"click\"===e&&(a=!1!==o.saveFilter(i)&&a);return a},S.Mc,S.T)),a.Ib(61,4440064,[[11,4],[\"saveFilterImage\",4]],0,r.cb,[a.r,r.i],{id:[0,\"id\"],toolTip:[1,\"toolTip\"],styleName:[2,\"styleName\"],enabled:[3,\"enabled\"],marginTop:[4,\"marginTop\"]},{onClick_:\"click\"}),(t()(),a.Jb(62,0,null,0,1,\"SwtButton\",[[\"enabled\",\"false\"],[\"id\",\"deleteFilterImage\"],[\"marginLeft\",\"8\"],[\"marginTop\",\"3\"],[\"styleName\",\"fileDeleteIcon\"],[\"toolTip\",\"Delete filter\"]],null,[[null,\"click\"]],function(t,e,i){var a=!0,o=t.component;\"click\"===e&&(a=!1!==o.deleteFilterClickHandler(i)&&a);return a},S.Mc,S.T)),a.Ib(63,4440064,[[12,4],[\"deleteFilterImage\",4]],0,r.cb,[a.r,r.i],{id:[0,\"id\"],toolTip:[1,\"toolTip\"],styleName:[2,\"styleName\"],enabled:[3,\"enabled\"],marginTop:[4,\"marginTop\"],marginLeft:[5,\"marginLeft\"]},{onClick_:\"click\"}),(t()(),a.Jb(64,0,null,0,1,\"SwtCanvas\",[[\"height\",\"82%\"],[\"id\",\"dataGridContainer\"],[\"minWidth\",\"1200\"],[\"width\",\"100%\"]],null,null,null,S.Nc,S.U)),a.Ib(65,4440064,[[18,4],[\"dataGridContainer\",4]],0,r.db,[a.r,r.i],{id:[0,\"id\"],width:[1,\"width\"],height:[2,\"height\"],minWidth:[3,\"minWidth\"]},null),(t()(),a.Jb(66,0,null,0,1,\"SwtCanvas\",[[\"height\",\"30%\"],[\"id\",\"dataGridContainer2\"],[\"includeInLayout\",\"false\"],[\"minWidth\",\"1200\"],[\"visible\",\"false\"],[\"width\",\"100%\"]],null,null,null,S.Nc,S.U)),a.Ib(67,4440064,[[19,4],[\"dataGridContainer2\",4]],0,r.db,[a.r,r.i],{id:[0,\"id\"],width:[1,\"width\"],height:[2,\"height\"],minWidth:[3,\"minWidth\"],includeInLayout:[4,\"includeInLayout\"],visible:[5,\"visible\"]},null),(t()(),a.Jb(68,0,null,0,3,\"HBox\",[[\"height\",\"11\"],[\"horizontalAlign\",\"right\"]],null,null,null,S.Dc,S.K)),a.Ib(69,4440064,null,0,r.C,[a.r,r.i],{horizontalAlign:[0,\"horizontalAlign\"],height:[1,\"height\"]},null),(t()(),a.Jb(70,0,null,0,1,\"SwtButton\",[[\"id\",\"imgShowHideButtonBar\"],[\"styleName\",\"plusIcon\"]],null,[[null,\"click\"]],function(t,e,i){var a=!0,o=t.component;\"click\"===e&&(a=!1!==o.showHideButtonBar()&&a);return a},S.Mc,S.T)),a.Ib(71,4440064,[[26,4],[\"imgShowHideButtonBar\",4]],0,r.cb,[a.r,r.i],{id:[0,\"id\"],styleName:[1,\"styleName\"]},{onClick_:\"click\"}),(t()(),a.Jb(72,0,null,0,19,\"SwtCanvas\",[[\"height\",\"5%\"],[\"id\",\"totalsPanel\"],[\"includeInLayout\",\"false\"],[\"marginBottom\",\"0\"],[\"minWidth\",\"1200\"],[\"visible\",\"false\"],[\"width\",\"100%\"]],null,null,null,S.Nc,S.U)),a.Ib(73,4440064,[[20,4],[\"totalsPanel\",4]],0,r.db,[a.r,r.i],{id:[0,\"id\"],width:[1,\"width\"],height:[2,\"height\"],minWidth:[3,\"minWidth\"],includeInLayout:[4,\"includeInLayout\"],visible:[5,\"visible\"],marginBottom:[6,\"marginBottom\"]},null),(t()(),a.Jb(74,0,null,0,17,\"HBox\",[[\"height\",\"100%\"],[\"width\",\"100%\"]],null,null,null,S.Dc,S.K)),a.Ib(75,4440064,null,0,r.C,[a.r,r.i],{width:[0,\"width\"],height:[1,\"height\"]},null),(t()(),a.Jb(76,0,null,0,5,\"HBox\",[[\"height\",\"100%\"],[\"width\",\"100%\"]],null,null,null,S.Dc,S.K)),a.Ib(77,4440064,null,0,r.C,[a.r,r.i],{width:[0,\"width\"],height:[1,\"height\"]},null),(t()(),a.Jb(78,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"],[\"text\",\"Total of selected:\"]],null,null,null,S.Yc,S.fb)),a.Ib(79,4440064,null,0,r.vb,[a.r,r.i],{text:[0,\"text\"],fontWeight:[1,\"fontWeight\"]},null),(t()(),a.Jb(80,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"]],null,null,null,S.Yc,S.fb)),a.Ib(81,4440064,[[21,4],[\"totalSelectedValue\",4]],0,r.vb,[a.r,r.i],{fontWeight:[0,\"fontWeight\"]},null),(t()(),a.Jb(82,0,null,0,9,\"HBox\",[[\"horizontalAlign\",\"right\"]],null,null,null,S.Dc,S.K)),a.Ib(83,4440064,null,0,r.C,[a.r,r.i],{horizontalAlign:[0,\"horizontalAlign\"]},null),(t()(),a.Jb(84,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"],[\"text\",\"Total in this page:\"]],null,null,null,S.Yc,S.fb)),a.Ib(85,4440064,null,0,r.vb,[a.r,r.i],{text:[0,\"text\"],fontWeight:[1,\"fontWeight\"]},null),(t()(),a.Jb(86,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"]],null,null,null,S.Yc,S.fb)),a.Ib(87,4440064,[[22,4],[\"totalInPageValue\",4]],0,r.vb,[a.r,r.i],{fontWeight:[0,\"fontWeight\"]},null),(t()(),a.Jb(88,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"],[\"paddingLeft\",\"15\"],[\"text\",\"Total over all pages:\"]],null,null,null,S.Yc,S.fb)),a.Ib(89,4440064,null,0,r.vb,[a.r,r.i],{paddingLeft:[0,\"paddingLeft\"],text:[1,\"text\"],fontWeight:[2,\"fontWeight\"]},null),(t()(),a.Jb(90,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"],[\"paddingLeft\",\"15\"]],null,null,null,S.Yc,S.fb)),a.Ib(91,4440064,[[23,4],[\"totalOverPagesValue\",4]],0,r.vb,[a.r,r.i],{paddingLeft:[0,\"paddingLeft\"],fontWeight:[1,\"fontWeight\"]},null),(t()(),a.Jb(92,0,null,0,25,\"SwtCanvas\",[[\"height\",\"40\"],[\"minWidth\",\"1200\"],[\"width\",\"100%\"]],null,null,null,S.Nc,S.U)),a.Ib(93,4440064,null,0,r.db,[a.r,r.i],{width:[0,\"width\"],height:[1,\"height\"],minWidth:[2,\"minWidth\"]},null),(t()(),a.Jb(94,0,null,0,23,\"HBox\",[[\"width\",\"100%\"]],null,null,null,S.Dc,S.K)),a.Ib(95,4440064,null,0,r.C,[a.r,r.i],{width:[0,\"width\"]},null),(t()(),a.Jb(96,0,null,0,1,\"HBox\",[[\"id\",\"buttonBox\"],[\"width\",\"100%\"]],null,null,null,S.Dc,S.K)),a.Ib(97,4440064,[[4,4],[\"buttonBox\",4]],0,r.C,[a.r,r.i],{id:[0,\"id\"],width:[1,\"width\"]},null),(t()(),a.Jb(98,0,null,0,19,\"HBox\",[[\"horizontalAlign\",\"right\"],[\"horizontalGap\",\"3\"]],null,null,null,S.Dc,S.K)),a.Ib(99,4440064,null,0,r.C,[a.r,r.i],{horizontalGap:[0,\"horizontalGap\"],horizontalAlign:[1,\"horizontalAlign\"]},null),(t()(),a.Jb(100,0,null,0,5,\"VBox\",[[\"height\",\"100%\"],[\"marginRight\",\"5\"],[\"verticalGap\",\"0\"]],null,null,null,S.od,S.vb)),a.Ib(101,4440064,null,0,r.ec,[a.r,r.i,a.T],{verticalGap:[0,\"verticalGap\"],height:[1,\"height\"],marginRight:[2,\"marginRight\"]},null),(t()(),a.Jb(102,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"],[\"text\",\"Difference: \"],[\"width\",\"70\"]],null,null,null,S.Yc,S.fb)),a.Ib(103,4440064,null,0,r.vb,[a.r,r.i],{width:[0,\"width\"],text:[1,\"text\"],fontWeight:[2,\"fontWeight\"]},null),(t()(),a.Jb(104,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"],[\"marginTop\",\"-15\"]],null,null,null,S.Yc,S.fb)),a.Ib(105,4440064,[[25,4],[\"diffText\",4]],0,r.vb,[a.r,r.i],{marginTop:[0,\"marginTop\"],fontWeight:[1,\"fontWeight\"]},null),(t()(),a.Jb(106,0,null,0,5,\"VBox\",[[\"height\",\"100%\"],[\"verticalGap\",\"0\"]],null,null,null,S.od,S.vb)),a.Ib(107,4440064,null,0,r.ec,[a.r,r.i,a.T],{verticalGap:[0,\"verticalGap\"],height:[1,\"height\"]},null),(t()(),a.Jb(108,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"],[\"text\",\"Last Refresh:\"]],null,null,null,S.Yc,S.fb)),a.Ib(109,4440064,[[24,4],[\"lastRefTimeLabel\",4]],0,r.vb,[a.r,r.i],{text:[0,\"text\"],fontWeight:[1,\"fontWeight\"]},null),(t()(),a.Jb(110,0,null,0,1,\"SwtLabel\",[[\"fontWeight\",\"normal\"],[\"marginTop\",\"-15\"]],null,null,null,S.Yc,S.fb)),a.Ib(111,4440064,[[13,4],[\"lastRefTime\",4]],0,r.vb,[a.r,r.i],{marginTop:[0,\"marginTop\"],fontWeight:[1,\"fontWeight\"]},null),(t()(),a.Jb(112,0,null,0,1,\"DataExportMultiPage\",[[\"id\",\"exportContainer\"]],null,null,null,S.yc,S.F)),a.Ib(113,4440064,[[14,4],[\"exportContainer\",4]],0,r.q,[r.i,a.r],{id:[0,\"id\"]},null),(t()(),a.Jb(114,0,null,0,1,\"SwtHelpButton\",[[\"id\",\"helpIcon\"]],null,[[null,\"click\"]],function(t,e,i){var a=!0,o=t.component;\"click\"===e&&(a=!1!==o.doHelp()&&a);return a},S.Wc,S.db)),a.Ib(115,4440064,[[15,4],[\"helpIcon\",4]],0,r.rb,[a.r,r.i],{id:[0,\"id\"]},{onClick_:\"click\"}),(t()(),a.Jb(116,0,null,0,1,\"SwtLoadingImage\",[],null,null,null,S.Zc,S.gb)),a.Ib(117,114688,[[16,4],[\"loadingImage\",4]],0,r.xb,[a.r],null,null)],function(t,e){t(e,27,0,\"100%\",\"100%\");t(e,29,0,\"0\",\"100%\",\"100%\",\"5\",\"5\",\"5\");t(e,31,0,\"headerContainer\",\"100%\",\"56\",\"1200\");t(e,33,0,\"filterContainer\",\"100%\");t(e,35,0,\"30%\");t(e,37,0,\"Entity\");t(e,39,0,\"entity\",\"135\",\"entityCombo\",\"false\");t(e,41,0,\"selectedEntity\",\"normal\");t(e,43,0,\"18%\",\"false\"),t(e,45,0);t(e,47,0,\"55%\");t(e,49,0,\"0\",\"100%\");t(e,51,0,\"right\",\"100%\",\"20\");t(e,53,0,\"currencyThreshold\",\"Apply Currency Threshold\");t(e,55,0,\"filterArea\",\"2\",\"right\",\"100%\");t(e,57,0,\"1\",\"Current Filter\");t(e,59,0,\"filterList\",\"Ad Hoc\",\"332\",\"filterComboMSD\");t(e,61,0,\"saveFilterImage\",\"Save filter\",\"fileSaveIcon\",\"false\",\"3\");t(e,63,0,\"deleteFilterImage\",\"Delete filter\",\"fileDeleteIcon\",\"false\",\"3\",\"8\");t(e,65,0,\"dataGridContainer\",\"100%\",\"82%\",\"1200\");t(e,67,0,\"dataGridContainer2\",\"100%\",\"30%\",\"1200\",\"false\",\"false\");t(e,69,0,\"right\",\"11\");t(e,71,0,\"imgShowHideButtonBar\",\"plusIcon\");t(e,73,0,\"totalsPanel\",\"100%\",\"5%\",\"1200\",\"false\",\"false\",\"0\");t(e,75,0,\"100%\",\"100%\");t(e,77,0,\"100%\",\"100%\");t(e,79,0,\"Total of selected:\",\"normal\");t(e,81,0,\"normal\");t(e,83,0,\"right\");t(e,85,0,\"Total in this page:\",\"normal\");t(e,87,0,\"normal\");t(e,89,0,\"15\",\"Total over all pages:\",\"normal\");t(e,91,0,\"15\",\"normal\");t(e,93,0,\"100%\",\"40\",\"1200\");t(e,95,0,\"100%\");t(e,97,0,\"buttonBox\",\"100%\");t(e,99,0,\"3\",\"right\");t(e,101,0,\"0\",\"100%\",\"5\");t(e,103,0,\"70\",\"Difference: \",\"normal\");t(e,105,0,\"-15\",\"normal\");t(e,107,0,\"0\",\"100%\");t(e,109,0,\"Last Refresh:\",\"normal\");t(e,111,0,\"-15\",\"normal\");t(e,113,0,\"exportContainer\");t(e,115,0,\"helpIcon\"),t(e,117,0)},null)}function H(t){return a.dc(0,[(t()(),a.Jb(0,0,null,null,1,\"app-movement-summary-display\",[],null,null,null,z,J)),a.Ib(1,4440064,null,0,m,[r.i,a.r],null,null)],function(t,e){t(e,1,0)},null)}var Z=a.Fb(\"app-movement-summary-display\",m,H,{maxChars:\"maxChars\",restrict:\"restrict\",id:\"id\",dropShadowEnabled:\"dropShadowEnabled\",cornerRadius:\"cornerRadius\",borderThickness:\"borderThickness\",borderStyle:\"borderStyle\",borderColor:\"borderColor\",backGroundColor:\"backGroundColor\",right:\"right\",left:\"left\",bottom:\"bottom\",top:\"top\",horizontalGap:\"horizontalGap\",verticalGap:\"verticalGap\",textAlign:\"textAlign\",toolTip:\"toolTip\",toolTipPreviousValue:\"toolTipPreviousValue\",textDictionaryId:\"tooltipDictionaryId\",name:\"name\",styleName:\"styleName\",horizontalAlign:\"horizontalAlign\",verticalAlign:\"verticalAlign\",width:\"width\",showScrollBar:\"showScrollBar\",height:\"height\",minHeight:\"minHeight\",minWidth:\"minWidth\",maxHeight:\"maxHeight\",maxWidth:\"maxWidth\",includeInLayout:\"includeInLayout\",visible:\"visible\",enabled:\"enabled\",paddingTop:\"paddingTop\",paddingBottom:\"paddingBottom\",paddingLeft:\"paddingLeft\",paddingRight:\"paddingRight\",marginTop:\"marginTop\",marginBottom:\"marginBottom\",marginLeft:\"marginLeft\",marginRight:\"marginRight\",contextMenu:\"contextMenu\"},{onClick_:\"click\",dbClick_:\"dbClick\",doubleClick_:\"doubleClick\",itemDoubleClick_:\"itemDoubleClick\",onKeyDown_:\"keyDown\",onKeyUp_:\"keyUp\",mouseUp_:\"mouseUp\",mouseOver_:\"mouseOver\",mouseDown_:\"mouseDown\",mouseEnter_:\"mouseEnter\",mouseLeave_:\"mouseLeave\",mouseOut_:\"mouseOut\",mouseIn_:\"mouseIn\",mouseMove_:\"mouseMove\",focus_:\"focus\",focusIn_:\"focusIn\",onFocusOut_:\"focusOut\",keyFocusChange_:\"keyFocusChange\",change_:\"change\",onSpyChange:\"onSpyChange\",onSpyNoChange:\"onSpyNoChange\",scroll_:\"scroll\",creationComplete:\"creationComplete\",preinitialize:\"preinitialize\"},[])}}]);", "extractedComments": []}