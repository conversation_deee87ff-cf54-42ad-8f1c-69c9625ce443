<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quality Calculation & Matching Logic</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .mermaid {
            text-align: center;
        }
        .description {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #dc3545;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Quality Calculation & Matching Logic Detail</h1>
        
        <div class="mermaid">
flowchart TD
    A[Quality Calculation Start<br/>FN_UPDATE_P_B_MATQUAL_POSLEVEL] --> B[Input Parameters:<br/>• Source & Target Details<br/>• Value Dates<br/>• Amounts & Tolerance<br/>• Account/Party IDs<br/>• Position Levels]
    
    B --> C[Initialize Quality Variables<br/>A=5, B=4, C=3, D=2, E=1]
    
    C --> D[Check Value Date Match]
    D --> E{Source Value Date =<br/>Target Value Date?}
    E -->|Yes| F[Date Quality = A (5)]
    E -->|No| G{Within Tolerance<br/>Date Range?}
    G -->|Yes| H[Date Quality = B (4)]
    G -->|No| I[Date Quality = E (1)]
    
    F --> J[Check Amount Match]
    H --> J
    I --> J
    
    J --> K{Source Amount =<br/>Target Amount?}
    K -->|Yes| L[Amount Quality = A (5)]
    K -->|No| M{Within Tolerance<br/>Percentage?}
    M -->|Yes| N[Amount Quality = B (4)]
    M -->|No| O[Amount Quality = E (1)]
    
    L --> P[Check Account Match]
    N --> P
    O --> P
    
    P --> Q{Account Linking Check}
    Q -->|Same Account| R[Account Quality = A (5)]
    Q -->|Linked Accounts| S[Account Quality = B (4)]
    Q -->|Different Accounts| T[Account Quality = E (1)]
    
    R --> U[Check Counterparty Match]
    S --> U
    T --> U
    
    U --> V{Counterparty Match?}
    V -->|Exact Match| W[Counterparty Quality = A (5)]
    V -->|Partial Match| X[Counterparty Quality = B (4)]
    V -->|No Match| Y[Counterparty Quality = E (1)]
    
    W --> Z[Check Beneficiary Match]
    X --> Z
    Y --> Z
    
    Z --> AA{Beneficiary Match?}
    AA -->|Exact Match| BB[Beneficiary Quality = A (5)]
    AA -->|Partial Match| CC[Beneficiary Quality = B (4)]
    AA -->|No Match| DD[Beneficiary Quality = E (1)]
    
    BB --> EE[Check Custodian Match]
    CC --> EE
    DD --> EE
    
    EE --> FF{Custodian Match?}
    FF -->|Exact Match| GG[Custodian Quality = A (5)]
    FF -->|Partial Match| HH[Custodian Quality = B (4)]
    FF -->|No Match| II[Custodian Quality = E (1)]
    
    GG --> JJ[Check Book Code Match]
    HH --> JJ
    II --> JJ
    
    JJ --> KK{Book Code Match?}
    KK -->|Exact Match| LL[Book Code Quality = A (5)]
    KK -->|No Match| MM[Book Code Quality = E (1)]
    
    LL --> NN[Get Quality Matrix from P_MATCH_QUALITY]
    MM --> NN
    
    NN --> OO[Calculate Combined Quality<br/>Based on Position Level & Matrix]
    
    OO --> PP{Quality Matrix Logic}
    PP --> QQ[Apply Quality Mask & Computed Values]
    QQ --> RR[Return Final Quality Score 1-5]
    
    RR --> SS[Quality Score Usage]
    SS --> TT{Quality Score ≥ Minimum?}
    TT -->|Yes| UU[Accept Target for Matching]
    TT -->|No| VV[Reject Target]
    
    UU --> WW[Insert into P_B_TARGET_MOVEMENTS]
    WW --> XX[Continue Target Processing]
    
    VV --> YY[Skip Target]
    YY --> XX
    
    XX --> ZZ[Amount Total Processing]
    ZZ --> AAA[Check Amount Total Flags<br/>by Position Level]
    
    AAA --> BBB{Amount Total<br/>Required?}
    BBB -->|Yes| CCC[SP_MATCHING_AMOUNT_TOTAL<br/>Calculate Position Totals]
    BBB -->|No| DDD[Skip Amount Total]
    
    CCC --> EEE[Inner Target Processing]
    DDD --> EEE
    
    EEE --> FFF[Select Best Quality Targets<br/>by Position Level]
    FFF --> GGG[Apply Match Actions<br/>from P_MATCH_ACTION]
    
    GGG --> HHH{Match Action Logic}
    HHH -->|Action = 'A'| III[Auto Match]
    HHH -->|Action = 'B'| JJJ[Offer Match]
    HHH -->|Action = 'C'| KKK[Confirm Match]
    HHH -->|Action = 'D'| LLL[Decline Match]
    HHH -->|Action = 'E'| MMM[Exception Match]
    HHH -->|Action = 'N'| NNN[No Action]
    
    III --> OOO[Update Match Status]
    JJJ --> OOO
    KKK --> OOO
    LLL --> PPP[Remove from Processing]
    MMM --> OOO
    NNN --> PPP
    
    OOO --> QQQ[Check Match Completion]
    PPP --> QQQ
    
    QQQ --> RRR{All Positions<br/>Processed?}
    RRR -->|Yes| SSS[Finalize Match<br/>Update P_MATCH table]
    RRR -->|No| TTT[Continue Processing]
    
    SSS --> UUU[Clean Up Temporary Tables<br/>Remove Locks]
    TTT --> XX
    
    UUU --> VVV[End Quality Processing]
    
    style A fill:#e1f5fe
    style VVV fill:#ffebee
    style TT fill:#fff3e0
    style BBB fill:#f3e5f5
    style HHH fill:#e8f5e8
    style RRR fill:#fff8e1
    style UU fill:#e8f5e8
    style VV fill:#ffebee
        </div>
        
        <div class="description">
            <h3>Description:</h3>
            <p>This flowchart details the quality calculation and matching logic:</p>
            <ul>
                <li><strong>Quality Scoring System:</strong> A=5 (Perfect), B=4 (Good), C=3 (Fair), D=2 (Poor), E=1 (Bad)</li>
                <li><strong>Quality Factors:</strong> Value Date, Amount, Account, Counterparty, Beneficiary, Custodian, Book Code</li>
                <li><strong>Quality Matrix:</strong> Position-level specific quality requirements from P_MATCH_QUALITY</li>
                <li><strong>Match Actions:</strong> Auto (A), Offer (B), Confirm (C), Decline (D), Exception (E), None (N)</li>
                <li><strong>Amount Total Logic:</strong> Position-specific amount calculations and validations</li>
            </ul>
        </div>
    </div>

    <script>
        mermaid.initialize({ startOnLoad: true });
    </script>
</body>
</html>
