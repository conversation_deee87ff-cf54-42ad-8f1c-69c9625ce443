<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quality Calculation & Matching Logic</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.9.0/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            max-width: 100%;
            overflow-x: auto;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .diagram-container {
            width: 100%;
            height: 800px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: auto;
            background: white;
            position: relative;
        }
        .mermaid {
            min-width: 1200px;
            min-height: 800px;
            transform-origin: top left;
        }
        .zoom-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 1000;
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }
        .zoom-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 5px 10px;
            margin: 0 2px;
            border-radius: 3px;
            cursor: pointer;
        }
        .zoom-btn:hover {
            background: #c82333;
        }
        .description {
            margin-top: 30px;
            padding: 15px;
            background-color: #f8f9fa;
            border-left: 4px solid #dc3545;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Quality Calculation & Matching Logic Detail</h1>

        <div class="diagram-container">
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomIn()">Zoom In</button>
                <button class="zoom-btn" onclick="zoomOut()">Zoom Out</button>
                <button class="zoom-btn" onclick="resetZoom()">Reset</button>
            </div>
            <div class="mermaid" id="diagram">
flowchart TD
    A[Quality Calculation Start] --> B[Input Parameters]
    B --> C[Initialize Quality Variables A=5 B=4 C=3 D=2 E=1]

    C --> D[Check Value Date Match]
    D --> E{Source Date = Target Date?}
    E -->|Yes| F[Date Quality = A]
    E -->|No| G{Within Tolerance?}
    G -->|Yes| H[Date Quality = B]
    G -->|No| I[Date Quality = E]

    F --> J[Check Amount Match]
    H --> J
    I --> J

    J --> K{Source Amount = Target Amount?}
    K -->|Yes| L[Amount Quality = A]
    K -->|No| M{Within Tolerance?}
    M -->|Yes| N[Amount Quality = B]
    M -->|No| O[Amount Quality = E]

    L --> P[Check Account Match]
    N --> P
    O --> P

    P --> Q{Account Check}
    Q -->|Same Account| R[Account Quality = A]
    Q -->|Linked Accounts| S[Account Quality = B]
    Q -->|Different| T[Account Quality = E]

    R --> U[Check Counterparty]
    S --> U
    T --> U

    U --> V{Counterparty Match?}
    V -->|Exact| W[Counterparty Quality = A]
    V -->|Partial| X[Counterparty Quality = B]
    V -->|None| Y[Counterparty Quality = E]

    W --> Z[Check Beneficiary]
    X --> Z
    Y --> Z

    Z --> AA{Beneficiary Match?}
    AA -->|Exact| BB[Beneficiary Quality = A]
    AA -->|Partial| CC[Beneficiary Quality = B]
    AA -->|None| DD[Beneficiary Quality = E]

    BB --> EE[Check Custodian]
    CC --> EE
    DD --> EE

    EE --> FF{Custodian Match?}
    FF -->|Exact| GG[Custodian Quality = A]
    FF -->|Partial| HH[Custodian Quality = B]
    FF -->|None| II[Custodian Quality = E]

    GG --> JJ[Check Book Code]
    HH --> JJ
    II --> JJ

    JJ --> KK{Book Code Match?}
    KK -->|Exact| LL[Book Code Quality = A]
    KK -->|None| MM[Book Code Quality = E]

    LL --> NN[Get Quality Matrix]
    MM --> NN

    NN --> OO[Calculate Combined Quality]
    OO --> PP[Apply Quality Matrix Logic]
    PP --> QQ[Return Final Quality Score]

    QQ --> RR{Quality Score OK?}
    RR -->|Yes| SS[Accept Target]
    RR -->|No| TT[Reject Target]

    SS --> UU[Insert into P_B_TARGET_MOVEMENTS]
    UU --> VV[Amount Total Processing]

    TT --> WW[Skip Target]
    WW --> VV

    VV --> XX{Amount Total Required?}
    XX -->|Yes| YY[Calculate Position Totals]
    XX -->|No| ZZ[Skip Amount Total]

    YY --> AAA[Inner Target Processing]
    ZZ --> AAA

    AAA --> BBB[Apply Match Actions]
    BBB --> CCC{Match Action}
    CCC -->|A| DDD[Auto Match]
    CCC -->|B| EEE[Offer Match]
    CCC -->|C| FFF[Confirm Match]
    CCC -->|D| GGG[Decline Match]
    CCC -->|E| HHH[Exception Match]
    CCC -->|N| III[No Action]

    DDD --> JJJ[Update Match Status]
    EEE --> JJJ
    FFF --> JJJ
    GGG --> KKK[Remove from Processing]
    HHH --> JJJ
    III --> KKK

    JJJ --> LLL{All Positions Done?}
    KKK --> LLL

    LLL -->|Yes| MMM[Finalize Match]
    LLL -->|No| NNN[Continue Processing]

    MMM --> OOO[Clean Up Tables]
    NNN --> VV

    OOO --> PPP[End Quality Processing]

    style A fill:#e1f5fe
    style PPP fill:#ffebee
    style RR fill:#fff3e0
    style XX fill:#f3e5f5
    style CCC fill:#e8f5e8
    style LLL fill:#fff8e1
            </div>
        </div>
        
        <div class="description">
            <h3>Description:</h3>
            <p>This flowchart details the quality calculation and matching logic:</p>
            <ul>
                <li><strong>Quality Scoring System:</strong> A=5 (Perfect), B=4 (Good), C=3 (Fair), D=2 (Poor), E=1 (Bad)</li>
                <li><strong>Quality Factors:</strong> Value Date, Amount, Account, Counterparty, Beneficiary, Custodian, Book Code</li>
                <li><strong>Quality Matrix:</strong> Position-level specific quality requirements from P_MATCH_QUALITY</li>
                <li><strong>Match Actions:</strong> Auto (A), Offer (B), Confirm (C), Decline (D), Exception (E), None (N)</li>
                <li><strong>Amount Total Logic:</strong> Position-specific amount calculations and validations</li>
            </ul>
        </div>
    </div>

    <script>
        let currentZoom = 1;

        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true
            }
        });

        function zoomIn() {
            currentZoom += 0.2;
            applyZoom();
        }

        function zoomOut() {
            currentZoom = Math.max(0.2, currentZoom - 0.2);
            applyZoom();
        }

        function resetZoom() {
            currentZoom = 1;
            applyZoom();
        }

        function applyZoom() {
            const diagram = document.getElementById('diagram');
            diagram.style.transform = `scale(${currentZoom})`;
        }
    </script>
</body>
</html>
