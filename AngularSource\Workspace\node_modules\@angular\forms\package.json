{"_from": "@angular/forms@7.2.4", "_id": "@angular/forms@7.2.4", "_inBundle": false, "_integrity": "sha512-DAtOrdlTRsgvmZrsvczCAkY8dhTwZb5DXBmPuSXh0UR9lvEiCgNHGbwEiIiIkAHpw1wSeXZrq0qyy/oJRvf18g==", "_location": "/@angular/forms", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/forms@7.2.4", "name": "@angular/forms", "escapedName": "@angular%2fforms", "scope": "@angular", "rawSpec": "7.2.4", "saveSpec": null, "fetchSpec": "7.2.4"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@angular/forms/-/forms-7.2.4.tgz", "_shasum": "be89cf83ad16fa3c813c12e4cff85da5409cf7a0", "_spec": "@angular/forms@7.2.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "description": "Angular - directives and services for creating forms", "es2015": "./fesm2015/forms.js", "esm2015": "./esm2015/forms.js", "esm5": "./esm5/forms.js", "fesm2015": "./fesm2015/forms.js", "fesm5": "./fesm5/forms.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/forms.umd.js", "module": "./fesm5/forms.js", "name": "@angular/forms", "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/http", "@angular/platform-server", "@angular/platform-webworker", "@angular/platform-webworker-dynamic", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/service-worker"]}, "peerDependencies": {"rxjs": "^6.0.0", "@angular/core": "7.2.4", "@angular/common": "7.2.4", "@angular/platform-browser": "7.2.4"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "sideEffects": false, "typings": "./forms.d.ts", "version": "7.2.4"}