{"_from": "@angular/flex-layout@7.0.0-beta.23", "_id": "@angular/flex-layout@7.0.0-beta.23", "_inBundle": false, "_integrity": "sha512-jH2i3i/M7SbK6scVlj2urVL5OhzwavbQ7KwvUjyc/UwccKnnzuOuWEGCINLja/aoaUO3I35LluCLv6a6VN0olA==", "_location": "/@angular/flex-layout", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/flex-layout@7.0.0-beta.23", "name": "@angular/flex-layout", "escapedName": "@angular%2fflex-layout", "scope": "@angular", "rawSpec": "7.0.0-beta.23", "saveSpec": null, "fetchSpec": "7.0.0-beta.23"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@angular/flex-layout/-/flex-layout-7.0.0-beta.23.tgz", "_shasum": "eff267584284bf534932deac513827d1b059c92e", "_spec": "@angular/flex-layout@7.0.0-beta.23", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "bugs": {"url": "https://github.com/angular/flex-layout/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.7.1"}, "deprecated": "This package has been deprecated. Please see https://blog.angular.io/modern-css-in-angular-layouts-4a259dca9127", "description": "Angular Flex-Layout", "es2015": "./esm2015/flex-layout.js", "homepage": "https://github.com/angular/flex-layout#readme", "keywords": ["angular", "flex-layout", "flexbox css", "media query", "breakpoints"], "license": "MIT", "main": "./bundles/flex-layout.umd.js", "module": "./esm5/flex-layout.es5.js", "name": "@angular/flex-layout", "peerDependencies": {"@angular/cdk": "^7.0.0-rc.0", "@angular/core": ">=7.0.0-rc.0", "@angular/common": ">=7.0.0-rc.0", "rxjs": "^6.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/angular/flex-layout.git"}, "sideEffects": false, "typings": "./flex-layout.d.ts", "version": "7.0.0-beta.23"}