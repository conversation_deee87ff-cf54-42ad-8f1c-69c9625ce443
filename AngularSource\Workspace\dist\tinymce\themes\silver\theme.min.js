/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.1.5 (2019-12-19)
 */
!function(v){"use strict";function Z(){}function i(e,o){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e(o.apply(null,n))}}function l(n){return n}var nn=function(n){return function(){return n}};function d(o){for(var r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var e=r.concat(n);return o.apply(null,e)}}function b(e){return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return!e.apply(null,n)}}function r(n){return function(){throw new Error(n)}}var u=nn(!1),a=nn(!0),n=tinymce.util.Tools.resolve("tinymce.ThemeManager"),N=function(){return(N=Object.assign||function(n){for(var t,e=1,o=arguments.length;e<o;e++)for(var r in t=arguments[e])Object.prototype.hasOwnProperty.call(t,r)&&(n[r]=t[r]);return n}).apply(this,arguments)};function c(n,t){var e={};for(var o in n)Object.prototype.hasOwnProperty.call(n,o)&&t.indexOf(o)<0&&(e[o]=n[o]);if(null!=n&&"function"==typeof Object.getOwnPropertySymbols){var r=0;for(o=Object.getOwnPropertySymbols(n);r<o.length;r++)t.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(n,o[r])&&(e[o[r]]=n[o[r]])}return e}function g(){for(var n=0,t=0,e=arguments.length;t<e;t++)n+=arguments[t].length;var o=Array(n),r=0;for(t=0;t<e;t++)for(var i=arguments[t],u=0,a=i.length;u<a;u++,r++)o[r]=i[u];return o}function t(){return s}var e,s=(e={fold:function(n,t){return n()},is:u,isSome:u,isNone:a,getOr:m,getOrThunk:f,getOrDie:function(n){throw new Error(n||"error: getOrDie called on none.")},getOrNull:nn(null),getOrUndefined:nn(undefined),or:m,orThunk:f,map:t,each:Z,bind:t,exists:u,forall:a,filter:t,equals:o,equals_:o,toArray:function(){return[]},toString:nn("none()")},Object.freeze&&Object.freeze(e),e);function o(n){return n.isNone()}function f(n){return n()}function m(n){return n}function p(t){return function(n){return function(n){if(null===n)return"null";var t=typeof n;return"object"==t&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==t&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":t}(n)===t}}function h(n,t){if(fn(n)){for(var e=0,o=n.length;e<o;++e)if(!0!==t(n[e]))return!1;return!0}return!1}function y(n,t){return pn.call(n,t)}function x(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return!0}return!1}function w(n,t){for(var e=[],o=0;o<n.length;o+=t){var r=gn.call(n,o,o+t);e.push(r)}return e}function S(n,t){for(var e=n.length,o=new Array(e),r=0;r<e;r++){var i=n[r];o[r]=t(i,r)}return o}function C(n,t){for(var e=[],o=0,r=n.length;o<r;o++){var i=n[o];t(i,o)&&e.push(i)}return e}function k(n,t,e){return function(n,t){for(var e=n.length-1;0<=e;e--){t(n[e],e)}}(n,function(n){e=t(e,n)}),e}function O(n,t,e){return bn(n,function(n){e=t(e,n)}),e}function E(n,t){for(var e=0,o=n.length;e<o;e++){var r=n[e];if(t(r,e))return on.some(r)}return on.none()}function T(n,t){for(var e=0,o=n.length;e<o;e++){if(t(n[e],e))return on.some(e)}return on.none()}function H(n){for(var t=[],e=0,o=n.length;e<o;++e){if(!fn(n[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+n);hn.apply(t,n[e])}return t}function B(n,t){var e=S(n,t);return H(e)}function D(n,t){for(var e=0,o=n.length;e<o;++e){if(!0!==t(n[e],e))return!1}return!0}function A(n){var t=gn.call(n,0);return t.reverse(),t}function _(n,t){return C(n,function(n){return!vn(t,n)})}function M(n){return[n]}function F(n){return 0===n.length?on.none():on.some(n[n.length-1])}function P(n,e){return kn(n,function(n,t){return{k:t,v:e(n,t)}})}function I(n,t){for(var e=wn(n),o=0,r=e.length;o<r;o++){var i=e[o],u=n[i];if(t(u,i,n))return on.some(u)}return on.none()}function R(n){return On(n,function(n){return n})}function V(n,t){return En(n,t)?on.from(n[t]):on.none()}function z(u){return function(){for(var n=new Array(arguments.length),t=0;t<n.length;t++)n[t]=arguments[t];if(0===n.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<n.length;o++){var r=n[o];for(var i in r)Bn.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}}function L(e){var o,r=!1;return function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return r||(r=!0,o=e.apply(null,n)),o}}function j(n){return _n.defaultedThunk(nn(n))}function U(t){return function(n){return En(n,t)?on.from(n[t]):on.none()}}function W(n,t){return U(t)(n)}function G(n,t){var e={};return e[n]=t,e}function X(n,t){return function(n,e){var o={};return Cn(n,function(n,t){vn(e,t)||(o[t]=n)}),o}(n,t)}function Y(n,t){return function(t,e){return function(n){return En(n,t)?n[t]:e}}(n,t)}function q(n,t){return G(n,t)}function K(n){return function(n){var t={};return bn(n,function(n){t[n.key]=n.value}),t}(n)}function J(n,t){var e=function(n){var t=[],e=[];return bn(n,function(n){n.fold(function(n){t.push(n)},function(n){e.push(n)})}),{errors:t,values:e}}(n);return 0<e.errors.length?function(n){return an.error(H(n))}(e.errors):function(n,t){return 0===n.length?an.value(t):an.value(Dn(t,An.apply(undefined,n)))}(e.values,t)}function $(n,t){return function(n,t){return En(n,t)&&n[t]!==undefined&&null!==n[t]}(n,t)}var Q,tn,en=function(e){function n(){return r}function t(n){return n(e)}var o=nn(e),r={fold:function(n,t){return t(e)},is:function(n){return e===n},isSome:a,isNone:u,getOr:o,getOrThunk:o,getOrDie:o,getOrNull:o,getOrUndefined:o,or:n,orThunk:n,map:function(n){return en(n(e))},each:function(n){n(e)},bind:t,exists:t,forall:t,filter:function(n){return n(e)?r:s},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(n){return n.is(e)},equals_:function(n,t){return n.fold(u,function(n){return t(e,n)})}};return r},on={some:en,none:t,from:function(n){return null===n||n===undefined?s:en(n)}},rn=function(e){return{is:function(n){return e===n},isValue:a,isError:u,getOr:nn(e),getOrThunk:nn(e),getOrDie:nn(e),or:function(n){return rn(e)},orThunk:function(n){return rn(e)},fold:function(n,t){return t(e)},map:function(n){return rn(n(e))},mapError:function(n){return rn(e)},each:function(n){n(e)},bind:function(n){return n(e)},exists:function(n){return n(e)},forall:function(n){return n(e)},toOption:function(){return on.some(e)}}},un=function(e){return{is:u,isValue:u,isError:a,getOr:l,getOrThunk:function(n){return n()},getOrDie:function(){return r(String(e))()},or:function(n){return n},orThunk:function(n){return n()},fold:function(n,t){return n(e)},map:function(n){return un(e)},mapError:function(n){return un(n(e))},each:Z,bind:function(n){return un(e)},exists:u,forall:a,toOption:on.none}},an={value:rn,error:un,fromOption:function(n,t){return n.fold(function(){return un(t)},rn)}},cn=p("string"),sn=p("object"),fn=p("array"),ln=p("boolean"),dn=p("function"),mn=p("number"),gn=Array.prototype.slice,pn=Array.prototype.indexOf,hn=Array.prototype.push,vn=function(n,t){return-1<y(n,t)},bn=function(n,t){for(var e=0,o=n.length;e<o;e++){t(n[e],e)}},yn=function(n){return 0===n.length?on.none():on.some(n[0])},xn=dn(Array.from)?Array.from:function(n){return gn.call(n)},wn=Object.keys,Sn=Object.hasOwnProperty,Cn=function(n,t){for(var e=wn(n),o=0,r=e.length;o<r;o++){var i=e[o];t(n[i],i)}},kn=function(n,o){var r={};return Cn(n,function(n,t){var e=o(n,t);r[e.k]=e.v}),r},On=function(n,e){var o=[];return Cn(n,function(n,t){o.push(e(n,t))}),o},En=function(n,t){return Sn.call(n,t)},Tn=function(u){if(!fn(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return bn(u,function(n,o){var t=wn(n);if(1!==t.length)throw new Error("one and only one name per case");var r=t[0],i=n[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!fn(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){var n=arguments.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+n);for(var e=new Array(n),t=0;t<e.length;t++)e[t]=arguments[t];return{fold:function(){if(arguments.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+arguments.length);return arguments[o].apply(null,e)},match:function(n){var t=wn(n);if(a.length!==t.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+t.join(","));if(!D(a,function(n){return vn(t,n)}))throw new Error("Not all branches were specified when using match. Specified: "+t.join(", ")+"\nRequired: "+a.join(", "));return n[r].apply(null,e)},log:function(n){v.console.log(n,{constructors:a,constructor:r,params:e})}}}}),e},Bn=Object.prototype.hasOwnProperty,Dn=z(function(n,t){return sn(n)&&sn(t)?Dn(n,t):t}),An=z(function(n,t){return t}),_n=Tn([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Mn=_n.strict,Fn=_n.asOption,In=_n.defaultedThunk,Rn=_n.mergeWithThunk,Vn=(Tn([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(n){return U(n)}),Nn=function(n,t){return W(n,t)};(tn=Q=Q||{})[tn.Error=0]="Error",tn[tn.Value=1]="Value";function Hn(n,t,e){return n.stype===Q.Error?t(n.serror):e(n.svalue)}function Pn(n){return{stype:Q.Value,svalue:n}}function zn(n){return{stype:Q.Error,serror:n}}function Ln(n){return i(qt,H)(n)}function jn(n){return sn(n)&&100<wn(n).length?" removed due to size":JSON.stringify(n,null,2)}function Un(n,t){return qt([{path:n,getErrorInfo:t}])}function Wn(n,t,e){return W(t,e).fold(function(){return function(n,t,e){return Un(n,function(){return'Could not find valid *strict* value for "'+t+'" in '+jn(e)})}(n,e,t)},Xt)}function Gn(n,t,e){var o=W(n,t).fold(function(){return e(n)},l);return Xt(o)}function Xn(u,a,n,c){return n.fold(function(o,e,n,r){function i(n){var t=r.extract(u.concat([o]),c,n);return $t(t,function(n){return G(e,c(n))})}function t(n){return n.fold(function(){var n=G(e,c(on.none()));return Xt(n)},function(n){var t=r.extract(u.concat([o]),c,n);return $t(t,function(n){return G(e,c(on.some(n)))})})}return n.fold(function(){return Kt(Wn(u,a,o),i)},function(n){return Kt(Gn(a,o,n),i)},function(){return Kt(function(n,t){return Xt(W(n,t))}(a,o),t)},function(n){return Kt(function(t,n,e){var o=W(t,n).map(function(n){return!0===n?e(t):n});return Xt(o)}(a,o,n),t)},function(n){var t=n(a),e=$t(Gn(a,o,nn({})),function(n){return Dn(t,n)});return Kt(e,i)})},function(n,t){var e=t(a);return Xt(G(n,c(e)))})}function Yn(o){return{extract:function(t,n,e){return Jt(o(e,n),function(n){return function(n,t){return Un(n,function(){return t})}(t,n)})},toString:function(){return"val"},toDsl:function(){return te.itemOf(o)}}}function qn(n){var i=re(n),u=k(n,function(t,n){return n.fold(function(n){return Dn(t,q(n,!0))},nn(t))},{});return{extract:function(n,t,e){var o=ln(e)?[]:function(t){var n=wn(t);return C(n,function(n){return $(t,n)})}(e),r=C(o,function(n){return!$(u,n)});return 0===r.length?i.extract(n,t,e):function(n,t){return Un(n,function(){return"There are unsupported fields: ["+t.join(", ")+"] specified"})}(n,r)},toString:i.toString,toDsl:i.toDsl}}function Kn(r){return{extract:function(e,o,n){var t=S(n,function(n,t){return r.extract(e.concat(["["+t+"]"]),o,n)});return ne(t)},toString:function(){return"array("+r.toString()+")"},toDsl:function(){return te.arrOf(r)}}}function Jn(i,u){return{extract:function(e,o,r){var n=wn(r),t=function(n,t){return Kn(Yn(i)).extract(n,l,t)}(e,n);return Kt(t,function(n){var t=S(n,function(n){return oe.field(n,n,Mn(),u)});return re(t).extract(e,o,r)})},toString:function(){return"setOf("+u.toString()+")"},toDsl:function(){return te.setOf(i,u)}}}function $n(t,e,o,n,r){return Nn(n,r).fold(function(){return function(n,t,e){return Un(n,function(){return'The chosen schema: "'+e+'" did not exist in branches: '+jn(t)})}(t,n,r)},function(n){return n.extract(t.concat(["branch: "+r]),e,o)})}function Qn(n,r){return{extract:function(t,e,o){return Nn(o,n).fold(function(){return function(n,t){return Un(n,function(){return'Choice schema did not contain choice key: "'+t+'"'})}(t,n)},function(n){return $n(t,e,o,r,n)})},toString:function(){return"chooseOn("+n+"). Possible values: "+wn(r)},toDsl:function(){return te.choiceOf(n,r)}}}function Zn(t){return Yn(function(n){return t(n).fold(qt,Xt)})}function nt(t,n){return Jn(function(n){return Wt(t(n))},n)}function tt(n,t,e){return Gt(function(n,t,e,o){var r=t.extract([n],e,o);return Qt(r,function(n){return{input:o,errors:n}})}(n,t,l,e))}function et(n){return n.fold(function(n){throw new Error(le(n))},l)}function ot(n,t,e){return et(tt(n,t,e))}function rt(n,t){return Qn(n,t)}function it(n,t){return Qn(n,P(t,re))}function ut(e,o){return Yn(function(n){var t=typeof n;return e(n)?Xt(n):qt("Expected type: "+o+" but got: "+t)})}function at(t){return Zn(function(n){return vn(t,n)?an.value(n):an.error('Unsupported value: "'+n+'", choose one of "'+t.join(", ")+'".')})}function ct(n){return ce(n,n,Mn(),ie())}function st(n,t){return ce(n,n,Mn(),t)}function ft(n){return st(n,ge)}function lt(n,t){return ce(n,n,Mn(),at(t))}function dt(n){return st(n,he)}function mt(n,t){return ce(n,n,Mn(),re(t))}function gt(n,t){return ce(n,n,Mn(),ue(t))}function pt(n,t){return ce(n,n,Mn(),Kn(t))}function ht(n){return ce(n,n,Fn(),ie())}function vt(n,t){return ce(n,n,Fn(),t)}function bt(n){return vt(n,me)}function yt(n){return vt(n,ge)}function xt(n){return vt(n,he)}function wt(n,t){return vt(n,re(t))}function St(n,t){return ce(n,n,j(t),ie())}function Ct(n,t,e){return ce(n,n,j(t),e)}function kt(n,t){return Ct(n,t,me)}function Ot(n,t){return Ct(n,t,ge)}function Et(n,t,e){return Ct(n,t,at(e))}function Tt(n,t){return Ct(n,t,pe)}function Bt(n,t){return Ct(n,t,he)}function Dt(n,t,e){return Ct(n,t,re(e))}function At(n,t){return ae(n,t)}function _t(n,t,e){return 0!=(n.compareDocumentPosition(t)&e)}function Mt(n,t){var e=function(n,t){for(var e=0;e<n.length;e++){var o=n[e];if(o.test(t))return o}return undefined}(n,t);if(!e)return{major:0,minor:0};function o(n){return Number(t.replace(e,"$"+n))}return ke(o(1),o(2))}function Ft(n,t){return function(){return t===n}}function It(n,t){return function(){return t===n}}function Rt(n,t){var e=String(t).toLowerCase();return E(n,function(n){return n.search(e)})}function Vt(n,t){return-1!==n.indexOf(t)}function Nt(t){return function(n){return Vt(n,t)}}function Ht(){return Le.get()}function Pt(n,t){var e=n.dom();if(e.nodeType!==Ge)return!1;var o=e;if(o.matches!==undefined)return o.matches(t);if(o.msMatchesSelector!==undefined)return o.msMatchesSelector(t);if(o.webkitMatchesSelector!==undefined)return o.webkitMatchesSelector(t);if(o.mozMatchesSelector!==undefined)return o.mozMatchesSelector(t);throw new Error("Browser lacks native selectors")}function zt(n){return n.nodeType!==Ge&&n.nodeType!==Xe||0===n.childElementCount}function Lt(n,t){var e=t===undefined?v.document:t.dom();return zt(e)?[]:S(e.querySelectorAll(n),we.fromDom)}function jt(n,t){return n.dom()===t.dom()}function Ut(n,t){return jt(n.element(),t.event().target())}var Wt=function(n){return n.fold(zn,Pn)},Gt=function(n){return Hn(n,an.error,an.value)},Xt=Pn,Yt=function(n){var t=[],e=[];return bn(n,function(n){Hn(n,function(n){return e.push(n)},function(n){return t.push(n)})}),{values:t,errors:e}},qt=zn,Kt=function(n,t){return n.stype===Q.Value?t(n.svalue):n},Jt=function(n,t){return n.stype===Q.Error?t(n.serror):n},$t=function(n,t){return n.stype===Q.Value?{stype:Q.Value,svalue:t(n.svalue)}:n},Qt=function(n,t){return n.stype===Q.Error?{stype:Q.Error,serror:t(n.serror)}:n},Zt=function(n,t){var e=Yt(n);return 0<e.errors.length?Ln(e.errors):function(n,t){return 0<n.length?Xt(Dn(t,An.apply(undefined,n))):Xt(t)}(e.values,t)},ne=function(n){var t=Yt(n);return 0<t.errors.length?Ln(t.errors):Xt(t.values)},te=Tn([{setOf:["validator","valueType"]},{arrOf:["valueType"]},{objOf:["fields"]},{itemOf:["validator"]},{choiceOf:["key","branches"]},{thunk:["description"]},{func:["args","outputSchema"]}]),ee=Tn([{field:["name","presence","type"]},{state:["name"]}]),oe=Tn([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),re=function(o){return{extract:function(n,t,e){return function(t,e,n,o){var r=S(n,function(n){return Xn(t,e,n,o)});return Zt(r,{})}(n,e,o,t)},toString:function(){return"obj{\n"+S(o,function(n){return n.fold(function(n,t,e,o){return n+" -> "+o.toString()},function(n,t){return"state("+n+")"})}).join("\n")+"}"},toDsl:function(){return te.objOf(S(o,function(n){return n.fold(function(n,t,e,o){return ee.field(n,e,o)},function(n,t){return ee.state(n)})}))}}},ie=nn(Yn(Xt)),ue=i(Kn,re),ae=oe.state,ce=oe.field,se=Yn(Xt),fe=function(o){return{extract:function(n,t,e){return o().extract(n,t,e)},toString:function(){return o().toString()},toDsl:function(){return o().toDsl()}}},le=function(n){return"Errors: \n"+function(n){var t=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n;return S(t,function(n){return"Failed path: ("+n.path.join(" > ")+")\n"+n.getErrorInfo()})}(n.errors)+"\n\nInput object: "+jn(n.input)},de=nn(se),me=ut(mn,"number"),ge=ut(cn,"string"),pe=ut(ln,"boolean"),he=ut(dn,"function"),ve=function(t){function n(n,t){for(var e=n.next();!e.done;){if(!t(e.value))return!1;e=n.next()}return!0}if(Object(t)!==t)return!0;switch({}.toString.call(t).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(t).every(function(n){return ve(t[n])});case"Map":return n(t.keys(),ve)&&n(t.values(),ve);case"Set":return n(t.keys(),ve);default:return!1}},be=Yn(function(n){return ve(n)?Xt(n):qt("Expected value to be acceptable for sending via postMessage")}),ye=function(n){function t(){return e}var e=n;return{get:t,set:function(n){e=n},clone:function(){return ye(t())}}},xe=function(n){if(null===n||n===undefined)throw new Error("Node cannot be null or undefined");return{dom:nn(n)}},we={fromHtml:function(n,t){var e=(t||v.document).createElement("div");if(e.innerHTML=n,!e.hasChildNodes()||1<e.childNodes.length)throw v.console.error("HTML does not have a single root node",n),new Error("HTML must have a single root node");return xe(e.childNodes[0])},fromTag:function(n,t){var e=(t||v.document).createElement(n);return xe(e)},fromText:function(n,t){var e=(t||v.document).createTextNode(n);return xe(e)},fromDom:xe,fromPoint:function(n,t,e){var o=n.dom();return on.from(o.elementFromPoint(t,e)).map(xe)}},Se=function(n,t){return _t(n,t,v.Node.DOCUMENT_POSITION_CONTAINED_BY)},Ce=function(){return ke(0,0)},ke=function(n,t){return{major:n,minor:t}},Oe={nu:ke,detect:function(n,t){var e=String(t).toLowerCase();return 0===n.length?Ce():Mt(n,e)},unknown:Ce},Ee="Firefox",Te=function(n){var t=n.current;return{current:t,version:n.version,isEdge:Ft("Edge",t),isChrome:Ft("Chrome",t),isIE:Ft("IE",t),isOpera:Ft("Opera",t),isFirefox:Ft(Ee,t),isSafari:Ft("Safari",t)}},Be={unknown:function(){return Te({current:undefined,version:Oe.unknown()})},nu:Te,edge:nn("Edge"),chrome:nn("Chrome"),ie:nn("IE"),opera:nn("Opera"),firefox:nn(Ee),safari:nn("Safari")},De="Windows",Ae="Android",_e="Solaris",Me="FreeBSD",Fe=function(n){var t=n.current;return{current:t,version:n.version,isWindows:It(De,t),isiOS:It("iOS",t),isAndroid:It(Ae,t),isOSX:It("OSX",t),isLinux:It("Linux",t),isSolaris:It(_e,t),isFreeBSD:It(Me,t)}},Ie={unknown:function(){return Fe({current:undefined,version:Oe.unknown()})},nu:Fe,windows:nn(De),ios:nn("iOS"),android:nn(Ae),linux:nn("Linux"),osx:nn("OSX"),solaris:nn(_e),freebsd:nn(Me)},Re=function(n,e){return Rt(n,e).map(function(n){var t=Oe.detect(n.versionRegexes,e);return{current:n.name,version:t}})},Ve=function(n,e){return Rt(n,e).map(function(n){var t=Oe.detect(n.versionRegexes,e);return{current:n.name,version:t}})},Ne=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,He=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(n){return Vt(n,"edge/")&&Vt(n,"chrome")&&Vt(n,"safari")&&Vt(n,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Ne],search:function(n){return Vt(n,"chrome")&&!Vt(n,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(n){return Vt(n,"msie")||Vt(n,"trident")}},{name:"Opera",versionRegexes:[Ne,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Nt("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Nt("firefox")},{name:"Safari",versionRegexes:[Ne,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(n){return(Vt(n,"safari")||Vt(n,"mobile/"))&&Vt(n,"applewebkit")}}],Pe=[{name:"Windows",search:Nt("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(n){return Vt(n,"iphone")||Vt(n,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Nt("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Nt("os x"),versionRegexes:[/.*?os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Nt("linux"),versionRegexes:[]},{name:"Solaris",search:Nt("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Nt("freebsd"),versionRegexes:[]}],ze={browsers:nn(He),oses:nn(Pe)},Le=ye(function(n,t){var e=ze.browsers(),o=ze.oses(),r=Re(e,n).fold(Be.unknown,Be.nu),i=Ve(o,n).fold(Ie.unknown,Ie.nu);return{browser:r,os:i,deviceType:function(n,t,e,o){var r=n.isiOS()&&!0===/ipad/i.test(e),i=n.isiOS()&&!r,u=n.isiOS()||n.isAndroid(),a=u||o("(pointer:coarse)"),c=r||!i&&u&&o("(min-device-width:768px)"),s=i||u&&!c,f=t.isSafari()&&n.isiOS()&&!1===/safari/i.test(e),l=!s&&!c&&!f;return{isiPad:nn(r),isiPhone:nn(i),isTablet:nn(c),isPhone:nn(s),isTouch:nn(a),isAndroid:n.isAndroid,isiOS:n.isiOS,isWebView:nn(f),isDesktop:nn(l)}}(i,r,n,t)}}(v.navigator.userAgent,function(n){return v.window.matchMedia(n).matches})),je=(v.Node.ATTRIBUTE_NODE,v.Node.CDATA_SECTION_NODE,v.Node.COMMENT_NODE,v.Node.DOCUMENT_NODE),Ue=(v.Node.DOCUMENT_TYPE_NODE,v.Node.DOCUMENT_FRAGMENT_NODE,v.Node.ELEMENT_NODE),We=v.Node.TEXT_NODE,Ge=(v.Node.PROCESSING_INSTRUCTION_NODE,v.Node.ENTITY_REFERENCE_NODE,v.Node.ENTITY_NODE,v.Node.NOTATION_NODE,Ue),Xe=je,Ye=Ht().browser.isIE()?function(n,t){return Se(n.dom(),t.dom())}:function(n,t){var e=n.dom(),o=t.dom();return e!==o&&e.contains(o)};function qe(n,t,e,o,r){return n(e,o)?on.some(e):dn(r)&&r(e)?on.none():t(e,o,r)}function Ke(n){return n.dom().nodeName.toLowerCase()}function Je(t){return function(n){return function(n){return n.dom().nodeType}(n)===t}}"undefined"!=typeof v.window?v.window:Function("return this;")();function $e(n){var t=Pr(n)?n.dom().parentNode:n.dom();return t!==undefined&&null!==t&&t.ownerDocument.body.contains(t)}function Qe(n,t,e){for(var o=n.dom(),r=dn(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=we.fromDom(o);if(t(i))return on.some(i);if(r(i))break}return on.none()}function Ze(n,t,e){return qe(function(n,t){return t(n)},Qe,n,t,e)}function no(n,r){var i=function(n){for(var t=0;t<n.childNodes.length;t++){var e=we.fromDom(n.childNodes[t]);if(r(e))return on.some(e);var o=i(n.childNodes[t]);if(o.isSome())return o}return on.none()};return i(n.dom())}function to(n){if(!$(n,"can")&&!$(n,"abort")&&!$(n,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(n,null,2)+" does not have can, abort, or run!");return ot("Extracting event.handler",qn([St("can",nn(!0)),St("abort",nn(!1)),St("run",Z)]),n)}function eo(e){var n=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return O(t,function(n,t){return n&&o(t).apply(undefined,e)},!0)}}(e,function(n){return n.can}),t=function(t,o){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return O(t,function(n,t){return n||o(t).apply(undefined,e)},!1)}}(e,function(n){return n.abort});return to({can:n,abort:t,run:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];bn(e,function(n){n.run.apply(undefined,t)})}})}function oo(n,t){Fi(n,n.element(),t,{})}function ro(n,t,e){Fi(n,n.element(),t,e)}function io(n){oo(n,di())}function uo(n,t,e){Fi(n,t,e,{})}function ao(n,t,e,o){n.getSystem().triggerEvent(e,t,o.event())}function co(n){return K(n)}function so(n,t){return{key:n,value:to({abort:t})}}function fo(n){return{key:n,value:to({run:function(n,t){t.event().prevent()}})}}function lo(n,t){return{key:n,value:to({run:t})}}function mo(n,e,o){return{key:n,value:to({run:function(n,t){e.apply(undefined,[n,t].concat(o))}})}}function go(n){return function(e){return{key:n,value:to({run:function(n,t){Ut(n,t)&&e(n,t)}})}}}function po(n,t,e){return function(e,o){return lo(e,function(n,t){n.getSystem().getByUid(o).each(function(n){ao(n,n.element(),e,t)})})}(n,t.partUids[e])}function ho(n,r){return lo(n,function(t,n){var e=n.event(),o=t.getSystem().getByDom(e.target()).fold(function(){return jr(e.target(),function(n){return t.getSystem().getByDom(n).toOption()},nn(!1)).getOr(t)},function(n){return n});r(t,o,n)})}function vo(n){return lo(n,function(n,t){t.cut()})}function bo(n,t){return go(n)(t)}function yo(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(t.length!==e.length)throw new Error('Wrong number of arguments to struct. Expected "['+t.length+']", got '+e.length+" arguments");var o={};return bn(t,function(n,t){o[n]=nn(e[t])}),o}}function xo(n){return n.slice(0).sort()}function wo(t,n){if(!fn(n))throw new Error("The "+t+" fields must be an array. Was: "+n+".");bn(n,function(n){if(!cn(n))throw new Error("The value "+n+" in the "+t+" fields was not a string.")})}function So(r,i){var u=r.concat(i);if(0===u.length)throw new Error("You must specify at least one required or optional field.");return wo("required",r),wo("optional",i),function(n){var e=xo(n);E(e,function(n,t){return t<e.length-1&&n===e[t+1]}).each(function(n){throw new Error("The field: "+n+" occurs more than once in the combined fields: ["+e.join(", ")+"].")})}(u),function(t){var e=wn(t);D(r,function(n){return vn(e,n)})||function(n,t){throw new Error("All required keys ("+xo(n).join(", ")+") were not specified. Specified keys were: "+xo(t).join(", ")+".")}(r,e);var n=C(e,function(n){return!vn(u,n)});0<n.length&&function(n){throw new Error("Unsupported keys for object: "+xo(n).join(", "))}(n);var o={};return bn(r,function(n){o[n]=nn(t[n])}),bn(i,function(n){o[n]=nn(Object.prototype.hasOwnProperty.call(t,n)?on.some(t[n]):on.none())}),o}}function Co(n){return we.fromDom(n.dom().ownerDocument)}function ko(n){return we.fromDom(n.dom().ownerDocument.documentElement)}function Oo(n){return we.fromDom(n.dom().ownerDocument.defaultView)}function Eo(n){return on.from(n.dom().parentNode).map(we.fromDom)}function To(n){return on.from(n.dom().offsetParent).map(we.fromDom)}function Bo(n){return S(n.dom().childNodes,we.fromDom)}function Do(n,t){var e=n.dom().childNodes;return on.from(e[t]).map(we.fromDom)}function Ao(t,e){Eo(t).each(function(n){n.dom().insertBefore(e.dom(),t.dom())})}function _o(n,t){(function(n){return on.from(n.dom().nextSibling).map(we.fromDom)})(n).fold(function(){Eo(n).each(function(n){Hi(n,t)})},function(n){Ao(n,t)})}function Mo(t,e){(function(n){return Do(n,0)})(t).fold(function(){Hi(t,e)},function(n){t.dom().insertBefore(e.dom(),n.dom())})}function Fo(t,n){bn(n,function(n){Hi(t,n)})}function Io(n){n.dom().textContent="",bn(Bo(n),function(n){Pi(n)})}function Ro(n){var t=Bo(n);0<t.length&&function(t,n){bn(n,function(n){Ao(t,n)})}(n,t),Pi(n)}function Vo(n){return n.dom().innerHTML}function No(n,t){var e=Co(n).dom(),o=we.fromDom(e.createDocumentFragment()),r=function(n,t){var e=(t||v.document).createElement("div");return e.innerHTML=n,Bo(we.fromDom(e))}(t,e);Fo(o,r),Io(n),Hi(n,o)}function Ho(n,t,e){if(!(cn(e)||ln(e)||mn(e)))throw v.console.error("Invalid call to Attr.set. Key ",t,":: Value ",e,":: Element ",n),new Error("Attribute value was not simple");n.setAttribute(t,e+"")}function Po(n,t,e){Ho(n.dom(),t,e)}function zo(n,t){var e=n.dom().getAttribute(t);return null===e?undefined:e}function Lo(n,t){var e=n.dom();return!(!e||!e.hasAttribute)&&e.hasAttribute(t)}function jo(n,t){n.dom().removeAttribute(t)}function Uo(n){return function(n,t){return we.fromDom(n.dom().cloneNode(t))}(n,!1)}function Wo(n){return function(n){var t=we.fromTag("div"),e=we.fromDom(n.dom().cloneNode(!0));return Hi(t,e),Vo(t)}(Uo(n))}function Go(n){return Wo(n)}function Xo(n){var t=(new Date).getTime();return n+"_"+Math.floor(1e9*Math.random())+ ++ji+String(t)}function Yo(n){return Xo(n)}function qo(t){function n(n){return function(){throw new Error("The component must be in a context to send: "+n+"\n"+Go(t().element())+" is not in context.")}}return{debugInfo:nn("fake"),triggerEvent:n("triggerEvent"),triggerFocus:n("triggerFocus"),triggerEscape:n("triggerEscape"),build:n("build"),addToWorld:n("addToWorld"),removeFromWorld:n("removeFromWorld"),addToGui:n("addToGui"),removeFromGui:n("removeFromGui"),getByUid:n("getByUid"),getByDom:n("getByDom"),broadcast:n("broadcast"),broadcastOn:n("broadcastOn"),broadcastEvent:n("broadcastEvent"),isConnected:nn(!1)}}function Ko(n,t){var e=n.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:$i(i)}},n}function Jo(n){return q(Qi,n)}function $o(o){return function(n,t){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:$i(i.slice(1))}},n}(function(n){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return o.apply(undefined,[n.getApis()].concat([n].concat(t)))},o)}function Qo(n,r){var i={};return Cn(n,function(n,o){Cn(n,function(n,t){var e=Y(t,[])(i);i[t]=e.concat([r(o,n)])})}),i}function Zo(n){return{classes:n.classes!==undefined?n.classes:[],attributes:n.attributes!==undefined?n.attributes:{},styles:n.styles!==undefined?n.styles:{}}}function nr(n){return n.cHandler}function tr(n,t){return{name:nn(n),handler:nn(t)}}function er(n,t,e){var o=N(N({},e),function(n,t){var e={};return bn(n,function(n){e[n.name()]=n.handlers(t)}),e}(t,n));return Qo(o,tr)}function or(n){var i=function(n){return dn(n)?{can:nn(!0),abort:nn(!1),run:n}:n}(n);return function(n,t){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[n,t].concat(e);i.abort.apply(undefined,r)?t.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}}function rr(n,t,e){var o=t[e];return o?function(u,a,n,c){var t=n.slice(0);try{var e=t.sort(function(n,t){var e=n[a](),o=t[a](),r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(c,null,2));return r<i?-1:i<r?1:0});return an.value(e)}catch(o){return an.error([o])}}("Event: "+e,"name",n,o).map(function(n){var t=S(n,function(n){return n.handler()});return eo(t)}):function(n,t){return an.error(["The event ("+n+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(S(t,function(n){return n.name()}),null,2)])}(e,n)}function ir(n){return tt("custom.definition",re([ce("dom","dom",Mn(),re([ct("tag"),St("styles",{}),St("classes",[]),St("attributes",{}),ht("value"),ht("innerHtml")])),ct("components"),ct("uid"),St("events",{}),St("apis",{}),ce("eventOrder","eventOrder",function(n){return _n.mergeWithThunk(nn(n))}({"alloy.execute":["disabling","alloy.base.behaviour","toggling","typeaheadevents"],"alloy.focus":["alloy.base.behaviour","focusing","keying"],"alloy.system.init":["alloy.base.behaviour","disabling","toggling","representing"],input:["alloy.base.behaviour","representing","streaming","invalidating"],"alloy.system.detached":["alloy.base.behaviour","representing","item-events","tooltipping"],mousedown:["focusing","alloy.base.behaviour","item-type-events"],touchstart:["focusing","alloy.base.behaviour","item-type-events"],mouseover:["item-type-events","tooltipping"]}),de()),ht("domModification")]),n)}function ur(n,t){var e=zo(n,t);return e===undefined||""===e?[]:e.split(" ")}function ar(n){return n.dom().classList!==undefined}function cr(n,t){return function(n,t,e){var o=ur(n,t).concat([e]);return Po(n,t,o.join(" ")),!0}(n,"class",t)}function sr(n,t){return function(n,t,e){var o=C(ur(n,t),function(n){return n!==e});return 0<o.length?Po(n,t,o.join(" ")):jo(n,t),!1}(n,"class",t)}function fr(n,t){ar(n)?n.dom().classList.add(t):cr(n,t)}function lr(n){0===(ar(n)?n.dom().classList:function(n){return ur(n,"class")}(n)).length&&jo(n,"class")}function dr(n,t){ar(n)?n.dom().classList.remove(t):sr(n,t),lr(n)}function mr(n,t){return ar(n)&&n.dom().classList.contains(t)}function gr(t,n){bn(n,function(n){fr(t,n)})}function pr(t,n){bn(n,function(n){dr(t,n)})}function hr(n){return n.style!==undefined&&dn(n.style.getPropertyValue)}function vr(n,t,e){if(!cn(e))throw v.console.error("Invalid call to CSS.set. Property ",t,":: Value ",e,":: Element ",n),new Error("CSS value must be a string: "+e);hr(n)&&n.style.setProperty(t,e)}function br(n,t){hr(n)&&n.style.removeProperty(t)}function yr(n,t,e){var o=n.dom();vr(o,t,e)}function xr(n,t){var e=n.dom();Cn(t,function(n,t){vr(e,t,n)})}function wr(n,t){var e=n.dom(),o=v.window.getComputedStyle(e).getPropertyValue(t),r=""!==o||$e(n)?o:ou(e,t);return null===r?undefined:r}function Sr(n,t){var e=n.dom(),o=ou(e,t);return on.from(o).filter(function(n){return 0<n.length})}function Cr(n,t,e){var o=we.fromTag(n);return yr(o,t,e),Sr(o,t).isSome()}function kr(n,t){var e=n.dom();br(e,t),Lo(n,"style")&&""===function(n){return n.replace(/^\s+|\s+$/g,"")}(zo(n,"style"))&&jo(n,"style")}function Or(n){return n.dom().offsetWidth}function Er(n){return n.dom().value}function Tr(n,t){if(t===undefined)throw new Error("Value.set was undefined");n.dom().value=t}function Br(n){var t=we.fromTag(n.tag);!function(n,t){var e=n.dom();Cn(t,function(n,t){Ho(e,t,n)})}(t,n.attributes),gr(t,n.classes),xr(t,n.styles),n.innerHtml.each(function(n){return No(t,n)});var e=n.domChildren;return Fo(t,e),n.value.each(function(n){Tr(t,n)}),n.uid,Yi(t,n.uid),t}function Dr(n,t){return function(t,n){var e=S(n,function(n){return wt(n.name(),[ct("config"),St("state",Zi)])}),o=tt("component.behaviours",re(e),t.behaviours).fold(function(n){throw new Error(le(n)+"\nComplete spec:\n"+JSON.stringify(t,null,2))},function(n){return n});return{list:n,data:P(o,function(n){var t=n.map(function(n){return{config:n.config,state:n.state.init(n.config)}});return function(){return t}})}}(n,t)}function Ar(n){var t=function(n){var t=Y("behaviours",{})(n),e=C(wn(t),function(n){return t[n]!==undefined});return S(e,function(n){return t[n].me})}(n);return Dr(n,t)}function _r(n,t,e){var o=function(n){return N(N({},n.dom),{uid:n.uid,domChildren:S(n.components,function(n){return n.element()})})}(n),r=function(n){return n.domModification.fold(function(){return Zo({})},Zo)}(n),i={"alloy.base.modification":r};return function(n,t){return N(N({},n),{attributes:N(N({},n.attributes),t.attributes),styles:N(N({},n.styles),t.styles),classes:n.classes.concat(t.classes)})}(o,0<t.length?function(t,n,e,o){var r=N({},n);bn(e,function(n){r[n.name()]=n.exhibit(t,o)});function i(n){return k(n,function(n,t){return N(N({},t.modification),n)},{})}var u=Qo(r,function(n,t){return{name:n,modification:t}}),a=k(u.classes,function(n,t){return t.modification.concat(n)},[]),c=i(u.attributes),s=i(u.styles);return Zo({classes:a,attributes:c,styles:s})}(e,i,t,o):r)}function Mr(n,t,e){var o={"alloy.base.behaviour":function(n){return n.events}(n)};return function(n,t,e,o){var r=er(n,e,o);return eu(r,t)}(e,n.eventOrder,t,o).getOrDie()}function Fr(n){var t=Ki(n),e=t.events,o=c(t,["events"]),r=function(n){var t=Y("components",[])(n);return S(t,uu)}(o),i=N(N({},o),{events:N(N({},Li),e),components:r});return an.value(function(e){function n(){return l}var o=ye(Ji),t=et(ir(e)),r=Ar(e),i=function(n){return n.list}(r),u=function(n){return n.data}(r),a=_r(t,i,u),c=Br(a),s=Mr(t,i,u),f=ye(t.components),l={getSystem:o.get,config:function(n){var t=u;return(dn(t[n.name()])?t[n.name()]:function(){throw new Error("Could not find "+n.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(n){return dn(u[n.name()])},spec:nn(e),readState:function(n){return u[n]().map(function(n){return n.state.readState()}).getOr("not enabled")},getApis:function(){return t.apis},connect:function(n){o.set(n)},disconnect:function(){o.set(qo(n))},element:nn(c),syncComponents:function(){var n=Bo(c),t=B(n,function(n){return o.get().getByDom(n).fold(function(){return[]},function(n){return[n]})});f.set(t)},components:f.get,events:nn(s)};return l}(i))}function Ir(n){var t=we.fromText(n);return ru({element:t})}var Rr,Vr,Nr,Hr=Je(Ue),Pr=Je(We),zr=L(function(){return Lr(we.fromDom(v.document))}),Lr=function(n){var t=n.dom().body;if(null===t||t===undefined)throw new Error("Body is not available yet");return we.fromDom(t)},jr=function(n,t,e){return Ze(n,function(n){return t(n).isSome()},e).bind(t)},Ur=nn("touchstart"),Wr=nn("touchmove"),Gr=nn("touchend"),Xr=nn("touchcancel"),Yr=nn("mousedown"),qr=nn("mousemove"),Kr=nn("mouseout"),Jr=nn("mouseup"),$r=nn("mouseover"),Qr=nn("focusin"),Zr=nn("focusout"),ni=nn("keydown"),ti=nn("keyup"),ei=nn("input"),oi=nn("change"),ri=nn("click"),ii=nn("transitionend"),ui=nn("selectstart"),ai={tap:nn("alloy.tap")},ci=nn("alloy.focus"),si=nn("alloy.blur.post"),fi=nn("alloy.paste.post"),li=nn("alloy.receive"),di=nn("alloy.execute"),mi=nn("alloy.focus.item"),gi=ai.tap,pi=nn("alloy.longpress"),hi=nn("alloy.sandbox.close"),vi=nn("alloy.typeahead.cancel"),bi=nn("alloy.system.init"),yi=nn("alloy.system.touchmove"),xi=nn("alloy.system.touchend"),wi=nn("alloy.system.scroll"),Si=nn("alloy.system.resize"),Ci=nn("alloy.system.attached"),ki=nn("alloy.system.detached"),Oi=nn("alloy.system.dismissRequested"),Ei=nn("alloy.system.repositionRequested"),Ti=nn("alloy.focusmanager.shifted"),Bi=nn("alloy.slotcontainer.visibility"),Di=nn("alloy.change.tab"),Ai=nn("alloy.dismiss.tab"),_i=nn("alloy.highlight"),Mi=nn("alloy.dehighlight"),Fi=function(n,t,e,o){var r=N({target:t},o);n.getSystem().triggerEvent(e,t,P(r,nn))},Ii=go(Ci()),Ri=go(ki()),Vi=go(bi()),Ni=(Rr=di(),function(n){return lo(Rr,n)}),Hi=(yo("element","offset"),function(n,t){n.dom().appendChild(t.dom())}),Pi=function(n){var t=n.dom();null!==t.parentNode&&t.parentNode.removeChild(t)},zi=co([(Vr=ci(),Nr=function(n,t){var e=t.event().originator(),o=t.event().target();return!function(n,t,e){return jt(t,n.element())&&!jt(t,e)}(n,e,o)||(v.console.warn(ci()+" did not get interpreted by the desired target. \nOriginator: "+Go(e)+"\nTarget: "+Go(o)+"\nCheck the "+ci()+" event handlers"),!1)},{key:Vr,value:to({can:Nr})})]),Li=/* */Object.freeze({events:zi}),ji=0,Ui=nn("alloy-id-"),Wi=nn("data-alloy-id"),Gi=Ui(),Xi=Wi(),Yi=function(n,t){Object.defineProperty(n.dom(),Xi,{value:t,writable:!0})},qi=function(n){var t=Hr(n)?n.dom()[Xi]:null;return on.from(t)},Ki=l,Ji=qo(),$i=function(n){return S(n,function(n){return function(n,t){return function(n,t,e){return""===t||!(n.length<t.length)&&n.substr(e,e+t.length)===t}(n,t,n.length-t.length)}(n,"/*")?n.substring(0,n.length-"/*".length):n})},Qi=Xo("alloy-premade"),Zi={init:function(){return nu({readState:function(){return"No State required"}})}},nu=function(n){return n},tu=function(n,t){return function(n,t){return{cHandler:n,purpose:nn(t)}}(d.apply(undefined,[n.handler].concat(t)),n.purpose())},eu=function(n,i){var t=On(n,function(o,r){return(1===o.length?an.value(o[0].handler()):rr(o,i,r)).map(function(n){var t=or(n),e=1<o.length?C(i[r],function(t){return x(o,function(n){return n.name()===t})}).join(" > "):o[0].name();return q(r,function(n,t){return{handler:n,purpose:nn(t)}}(t,e))})});return J(t,{})},ou=function(n,t){return hr(n)?n.style.getPropertyValue(t):""},ru=function(n){var t=ot("external.component",qn([ct("element"),ht("uid")]),n),e=ye(qo());t.uid.each(function(n){Yi(t.element,n)});var o={getSystem:e.get,config:on.none,hasConfigured:nn(!1),connect:function(n){e.set(n)},disconnect:function(){e.set(qo(function(){return o}))},getApis:function(){return{}},element:nn(t.element),spec:nn(n),readState:nn("No state"),syncComponents:Z,components:nn([]),events:nn({})};return Jo(o)},iu=Yo,uu=function(t){return function(n){return Nn(n,Qi)}(t).fold(function(){var n=t.hasOwnProperty("uid")?t:N({uid:iu("")},t);return Fr(n).getOrDie()},function(n){return n})},au=Jo;function cu(o,r){function n(n){var t=r(n);if(t<=0||null===t){var e=wr(n,o);return parseFloat(e)||0}return t}function i(r,n){return O(n,function(n,t){var e=wr(r,t),o=e===undefined?0:parseInt(e,10);return isNaN(o)?n:n+o},0)}return{set:function(n,t){if(!mn(t)&&!t.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+t);var e=n.dom();hr(e)&&(e.style[o]=t+"px")},get:n,getOuter:n,aggregate:i,max:function(n,t,e){var o=i(n,e);return o<t?t-o:0}}}function su(n){return Mu.get(n)}function fu(n){return Mu.getOuter(n)}function lu(n,t){return n!==undefined?n:t!==undefined?t:0}function du(n){var t=n.dom().ownerDocument,e=t.body,o=t.defaultView,r=t.documentElement;if(e===n.dom())return Iu(e.offsetLeft,e.offsetTop);var i=lu(o.pageYOffset,r.scrollTop),u=lu(o.pageXOffset,r.scrollLeft),a=lu(r.clientTop,e.clientTop),c=lu(r.clientLeft,e.clientLeft);return Ru(n).translate(u-c,i-a)}function mu(n){return Vu.get(n)}function gu(n){return Vu.getOuter(n)}function pu(n){var t=n!==undefined?n.dom():v.document,e=t.body.scrollLeft||t.documentElement.scrollLeft,o=t.body.scrollTop||t.documentElement.scrollTop;return Iu(e,o)}function hu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function vu(n){var t=n===undefined?v.window:n,e=t.document,o=pu(we.fromDom(e)),r=t.visualViewport;if(r!==undefined)return hu(Math.max(r.pageLeft,o.left()),Math.max(r.pageTop,o.top()),r.width,r.height);var i=e.documentElement,u=i.clientWidth,a=i.clientHeight;return hu(o.left(),o.top(),u,a)}function bu(o){var n=we.fromDom(v.document),r=pu(n);return function(n,t){var e=t.owner(n),o=Nu(t,e);return on.some(o)}(o,Hu).fold(d(du,o),function(n){var t=Ru(o),e=k(n,function(n,t){var e=Ru(t);return{left:n.left+e.left(),top:n.top+e.top()}},{left:0,top:0});return Iu(e.left+t.left()+r.left(),e.top+t.top()+r.top())})}function yu(n,t,e,o){return{x:nn(n),y:nn(t),width:nn(e),height:nn(o),right:nn(n+e),bottom:nn(t+o)}}function xu(n){var t=du(n),e=gu(n),o=fu(n);return yu(t.left(),t.top(),e,o)}function wu(n){var t=bu(n),e=gu(n),o=fu(n);return yu(t.left(),t.top(),e,o)}function Su(){return vu(v.window)}function Cu(n,t,e){return Qe(n,function(n){return Pt(n,t)},e)}function ku(n,t){return function(n,t){var e=t===undefined?v.document:t.dom();return zt(e)?on.none():on.from(e.querySelector(n)).map(we.fromDom)}(t,n)}function Ou(n,t,e){return qe(Pt,Cu,n,t,e)}function Eu(){var t=Xo("aria-owns");return{id:nn(t),link:function(n){Po(n,"aria-owns",t)},unlink:function(n){jo(n,"aria-owns")}}}function Tu(t,n){return function(n){return Ze(n,function(n){if(!Hr(n))return!1;var t=zo(n,"id");return t!==undefined&&-1<t.indexOf("aria-owns")}).bind(function(n){var t=zo(n,"id"),e=Co(n);return ku(e,'[aria-owns="'+t+'"]')})}(n).exists(function(n){return Lu(t,n)})}function Bu(n){for(var t=[],e=function(n){t.push(n)},o=0;o<n.length;o++)n[o].each(e);return t}function Du(n,t){for(var e=0;e<n.length;e++){var o=t(n[e],e);if(o.isSome())return o}return on.none()}var Au,_u,Mu=cu("height",function(n){var t=n.dom();return $e(n)?t.getBoundingClientRect().height:t.offsetHeight}),Fu=function(e,o){return{left:nn(e),top:nn(o),translate:function(n,t){return Fu(e+n,o+t)}}},Iu=Fu,Ru=function(n){var t=n.dom(),e=t.ownerDocument.body;return e===t?Iu(e.offsetLeft,e.offsetTop):$e(n)?function(n){var t=n.getBoundingClientRect();return Iu(t.left,t.top)}(t):Iu(0,0)},Vu=cu("width",function(n){return n.dom().offsetWidth}),Nu=(Ht().browser.isSafari(),function(o,n){return o.view(n).fold(nn([]),function(n){var t=o.owner(n),e=Nu(o,t);return[n].concat(e)})}),Hu=/* */Object.freeze({view:function(n){return(n.dom()===v.document?on.none():on.from(n.dom().defaultView.frameElement)).map(we.fromDom)},owner:function(n){return Co(n)}}),Pu=yo("point","width","height"),zu=yo("x","y","width","height"),Lu=function(t,n){return function(n,t,e){return Ze(n,t,e).isSome()}(n,function(n){return jt(n,t.element())},nn(!1))||Tu(t,n)},ju="unknown";(_u=Au=Au||{})[_u.STOP=0]="STOP",_u[_u.NORMAL=1]="NORMAL",_u[_u.LOGGING=2]="LOGGING";function Uu(t,n,e){switch(Nn(Pa.get(),t).orThunk(function(){var n=wn(Pa.get());return Du(n,function(n){return-1<t.indexOf(n)?on.some(Pa.get()[n]):on.none()})}).getOr(Au.NORMAL)){case Au.NORMAL:return e(La());case Au.LOGGING:var o=function(t,e){var o=[],r=(new Date).getTime();return{logEventCut:function(n,t,e){o.push({outcome:"cut",target:t,purpose:e})},logEventStopped:function(n,t,e){o.push({outcome:"stopped",target:t,purpose:e})},logNoParent:function(n,t,e){o.push({outcome:"no-parent",target:t,purpose:e})},logEventNoHandlers:function(n,t){o.push({outcome:"no-handlers-left",target:t})},logEventResponse:function(n,t,e){o.push({outcome:"response",purpose:e,target:t})},write:function(){var n=(new Date).getTime();vn(["mousemove","mouseover","mouseout",bi()],t)||v.console.log(t,{event:t,time:n-r,target:e.dom(),sequence:S(o,function(n){return vn(["cut","stopped","response"],n.outcome)?"{"+n.purpose+"} "+n.outcome+" at ("+Go(n.target)+")":n.outcome})})}}}(t,n),r=e(o);return o.write(),r;case Au.STOP:return!0}}function Wu(n,t,e){return Uu(n,t,e)}function Gu(){return mt("markers",[ct("backgroundMenu")].concat(ja()).concat(Ua()))}function Xu(n){return mt("markers",S(n,ct))}function Yu(n,t,e){return function(){var n=new Error;if(n.stack===undefined)return;var t=n.stack.split("\n");E(t,function(t){return 0<t.indexOf("alloy")&&!x(za,function(n){return-1<t.indexOf(n)})}).getOr(ju)}(),ce(t,t,e,Zn(function(e){return an.value(function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];return e.apply(undefined,n)})}))}function qu(n){return Yu(0,n,j(Z))}function Ku(n){return Yu(0,n,j(on.none))}function Ju(n){return Yu(0,n,Mn())}function $u(n){return Yu(0,n,Mn())}function Qu(n,t){return At(n,nn(t))}function Zu(n){return At(n,l)}function na(n){return n.x()}function ta(n,t){return n.x()+n.width()/2-t.width()/2}function ea(n,t){return n.x()+n.width()-t.width()}function oa(n,t){return n.y()-t.height()}function ra(n){return n.y()+n.height()}function ia(n,t){return n.y()+n.height()/2-t.height()/2}function ua(n,t,e){return Ya(na(n),ra(n),e.southeast(),Ka(),"layout-se")}function aa(n,t,e){return Ya(ea(n,t),ra(n),e.southwest(),Ja(),"layout-sw")}function ca(n,t,e){return Ya(na(n),oa(n,t),e.northeast(),$a(),"layout-ne")}function sa(n,t,e){return Ya(ea(n,t),oa(n,t),e.northwest(),Qa(),"layout-nw")}function fa(n,t,e){return Ya(function(n){return n.x()+n.width()}(n),ia(n,t),e.east(),tc(),"layout-e")}function la(n,t,e){return Ya(function(n,t){return n.x()-t.width()}(n,t),ia(n,t),e.west(),ec(),"layout-w")}function da(){return[ua,aa,ca,sa,rc,oc]}function ma(){return[aa,ua,sa,ca,rc,oc]}function ga(e,o,r){return Vi(function(n,t){r(n,e,o)})}function pa(n,t,e,o,r,i){var u=qn(n),a=wt(t,[function(n,t){return vt(n,qn(t))}("config",n)]);return ic(u,a,t,e,o,r,i)}function ha(r,i,u){return function(n,t,e){var o=e.toString(),r=o.indexOf(")")+1,i=o.indexOf("("),u=o.substring(i+1,r-1).split(/,\s*/);return n.toFunctionAnnotation=function(){return{name:t,parameters:$i(u.slice(0,1).concat(u.slice(3)))}},n}(function(e){for(var n=[],t=1;t<arguments.length;t++)n[t-1]=arguments[t];var o=[e].concat(n);return e.config({name:nn(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(n){var t=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,n.config,n.state].concat(t))})},u,i)}function va(n){return{key:n,value:undefined}}function ba(n){return K(n)}function ya(n){var t=ot("Creating behaviour: "+n.name,uc,n);return pa(t.fields,t.name,t.active,t.apis,t.extra,t.state)}function xa(n){var t=ot("Creating behaviour: "+n.name,ac,n);return function(n,t,e,o,r,i){var u=n,a=wt(t,[vt("config",n)]);return ic(u,a,t,e,o,r,i)}(it(t.branchKey,t.branches),t.name,t.active,t.apis,t.extra,t.state)}function wa(n){n.dom().focus()}function Sa(n){var t=n!==undefined?n.dom():v.document;return on.from(t.activeElement).map(we.fromDom)}function Ca(t){return Sa(Co(t)).filter(function(n){return t.dom().contains(n.dom())})}function ka(n,e){var o=Co(e),t=Sa(o).bind(function(t){function n(n){return jt(t,n)}return n(e)?on.some(e):no(e,n)}),r=n(e);return t.each(function(t){Sa(o).filter(function(n){return jt(n,t)}).fold(function(){wa(t)},Z)}),r}function Oa(n,t,e){function r(n){return Nn(e,n).getOr([])}function o(n,t,e){var o=_(mc,e);return{offset:function(){return Iu(n,t)},classesOn:function(){return B(e,r)},classesOff:function(){return B(o,r)}}}return{southeast:function(){return o(-n,t,["top","alignLeft"])},southwest:function(){return o(n,t,["top","alignRight"])},south:function(){return o(-n/2,t,["top","alignCentre"])},northeast:function(){return o(-n,-t,["bottom","alignLeft"])},northwest:function(){return o(n,-t,["bottom","alignRight"])},north:function(){return o(-n/2,-t,["bottom","alignCentre"])},east:function(){return o(n,-t/2,["valignCentre","left"])},west:function(){return o(-n,-t/2,["valignCentre","right"])},innerNorthwest:function(){return o(-n,t,["top","alignRight"])},innerNortheast:function(){return o(n,t,["top","alignLeft"])},innerNorth:function(){return o(-n/2,t,["top","alignCentre"])},innerSouthwest:function(){return o(-n,-t,["bottom","alignRight"])},innerSoutheast:function(){return o(n,-t,["bottom","alignLeft"])},innerSouth:function(){return o(-n/2,-t,["bottom","alignCentre"])},innerWest:function(){return o(n,-t/2,["valignCentre","right"])},innerEast:function(){return o(-n,-t/2,["valignCentre","left"])}}}function Ea(){return Oa(0,0,{})}function Ta(n,t,e,o,r,i){var u=t.x()-e,a=t.y()-o,c=r-(u+t.width()),s=i-(a+t.height()),f=on.some(u),l=on.some(a),d=on.some(c),m=on.some(s),g=on.none();return function(n,t,e,o,r,i,u,a,c){return n.fold(t,e,o,r,i,u,a,c)}(t.direction(),function(){return pc(n,f,l,g,g)},function(){return pc(n,g,l,d,g)},function(){return pc(n,f,g,g,m)},function(){return pc(n,g,g,d,m)},function(){return pc(n,f,l,g,g)},function(){return pc(n,f,g,g,m)},function(){return pc(n,f,l,g,g)},function(){return pc(n,g,l,d,g)})}function Ba(n,t){var e=d(bu,t),o=n.fold(e,e,function(){var n=pu();return bu(t).translate(-n.left(),-n.top())}),r=gu(t),i=fu(t);return yu(o.left(),o.top(),r,i)}function Da(n){return n}function Aa(t,e){return function(n){return"rtl"===xc(n)?e:t}}function _a(){return wt("layouts",[ct("onLtr"),ct("onRtl")])}function Ma(t,n,e,o){var r=n.layouts.map(function(n){return n.onLtr(t)}).getOr(e),i=n.layouts.map(function(n){return n.onRtl(t)}).getOr(o);return Aa(r,i)(t)}function Fa(n,t,e){var o=n.document.createRange();return function(e,n){n.fold(function(n){e.setStartBefore(n.dom())},function(n,t){e.setStart(n.dom(),t)},function(n){e.setStartAfter(n.dom())})}(o,t),function(e,n){n.fold(function(n){e.setEndBefore(n.dom())},function(n,t){e.setEnd(n.dom(),t)},function(n){e.setEndAfter(n.dom())})}(o,e),o}function Ia(n,t,e,o,r){var i=n.document.createRange();return i.setStart(t.dom(),e),i.setEnd(o.dom(),r),i}function Ra(n){return{left:nn(n.left),top:nn(n.top),right:nn(n.right),bottom:nn(n.bottom),width:nn(n.width),height:nn(n.height)}}function Va(n,t,e){return t(we.fromDom(e.startContainer),e.startOffset,we.fromDom(e.endContainer),e.endOffset)}function Na(n,t){return function(n,t){var e=t.ltr();return e.collapsed?t.rtl().filter(function(n){return!1===n.collapsed}).map(function(n){return Bc.rtl(we.fromDom(n.endContainer),n.endOffset,we.fromDom(n.startContainer),n.startOffset)}).getOrThunk(function(){return Va(0,Bc.ltr,e)}):Va(0,Bc.ltr,e)}(0,function(r,n){return n.match({domRange:function(n){return{ltr:nn(n),rtl:on.none}},relative:function(n,t){return{ltr:L(function(){return Fa(r,n,t)}),rtl:L(function(){return on.some(Fa(r,t,n))})}},exact:function(n,t,e,o){return{ltr:L(function(){return Ia(r,n,t,e,o)}),rtl:L(function(){return on.some(Ia(r,e,o,n,t))})}}})}(n,t))}function Ha(n,t,e){return t>=n.left&&t<=n.right&&e>=n.top&&e<=n.bottom}var Pa=ye({}),za=["alloy/data/Fields","alloy/debugging/Debugging"],La=nn({logEventCut:Z,logEventStopped:Z,logNoParent:Z,logEventNoHandlers:Z,logEventResponse:Z,write:Z}),ja=nn([ct("menu"),ct("selectedMenu")]),Ua=nn([ct("item"),ct("selectedItem")]),Wa=(nn(re(Ua().concat(ja()))),nn(re(Ua()))),Ga=mt("initSize",[ct("numColumns"),ct("numRows")]),Xa=nn(Ga),Ya=yo("x","y","bubble","direction","label"),qa=Tn([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Ka=qa.southeast,Ja=qa.southwest,$a=qa.northeast,Qa=qa.northwest,Za=qa.south,nc=qa.north,tc=qa.east,ec=qa.west,oc=function(n,t,e){return Ya(ta(n,t),oa(n,t),e.north(),nc(),"layout-n")},rc=function(n,t,e){return Ya(ta(n,t),ra(n),e.south(),Za(),"layout-s")},ic=function(e,n,o,r,t,i,u){function a(n){return $(n,o)?n[o]():on.none()}var c=P(t,function(n,t){return ha(o,n,t)}),s=P(i,function(n,t){return Ko(n,t)}),f=N(N(N({},s),c),{revoke:d(va,o),config:function(n){var t=ot(o+"-config",e,n);return{key:o,value:{config:t,me:f,configAsRaw:L(function(){return ot(o+"-config",e,n)}),initialConfig:n,state:u}}},schema:function(){return n},exhibit:function(n,e){return a(n).bind(function(t){return Nn(r,"exhibit").map(function(n){return n(e,t.config,t.state)})}).getOr(Zo({}))},name:function(){return o},handlers:function(n){return a(n).map(function(n){return Y("events",function(n,t){return{}})(r)(n.config,n.state)}).getOr({})}});return f},uc=qn([ct("fields"),ct("name"),St("active",{}),St("apis",{}),St("state",Zi),St("extra",{})]),ac=qn([ct("branchKey"),ct("branches"),ct("name"),St("active",{}),St("apis",{}),St("state",Zi),St("extra",{})]),cc=nn(undefined),sc=/* */Object.freeze({events:function(t){return co([lo(li(),function(r,i){var u=t.channels,n=function(n,t){return t.universal()?n:C(n,function(n){return vn(t.channels(),n)})}(wn(u),i);bn(n,function(n){var t=u[n],e=t.schema,o=ot("channel["+n+"] data\nReceiver: "+Go(r.element()),e,i.data());t.onReceive(r,o)})})])}}),fc=[st("channels",nt(an.value,qn([Ju("onReceive"),St("schema",de())])))],lc=ya({fields:fc,name:"receiving",active:sc}),dc=/* */Object.freeze({exhibit:function(n,t){return Zo({classes:[],styles:t.useFixed()?{}:{position:"relative"}})}}),mc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],gc=So(["x","y","width","height","maxHeight","maxWidth","direction","classes","label","candidateYforTest"],[]),pc=yo("position","left","top","right","bottom"),hc=Tn([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),vc=function(n,t,e){var o=Iu(t,e);return n.fold(nn(o),nn(o),function(){var n=pu();return o.translate(-n.left(),-n.top())})},bc=hc.relative,yc=hc.fixed,xc=function(n){return"rtl"===wr(n,"direction")?"rtl":"ltr"},wc=[ct("hotspot"),ht("bubble"),St("overrides",{}),_a(),Qu("placement",function(n,t,e){var o=t.hotspot,r=Ba(e,o.element()),i=Ma(n.element(),t,da(),ma());return on.some(Da({anchorBox:r,bubble:t.bubble.getOr(Ea()),overrides:t.overrides,layouts:i,placer:on.none()}))})],Sc=[ct("x"),ct("y"),St("height",0),St("width",0),St("bubble",Ea()),St("overrides",{}),_a(),Qu("placement",function(n,t,e){var o=vc(e,t.x,t.y),r=yu(o.left(),o.top(),t.width,t.height),i=Ma(n.element(),t,[ua,aa,ca,sa,rc,oc,fa,la],[aa,ua,sa,ca,rc,oc,fa,la]);return on.some(Da({anchorBox:r,bubble:t.bubble,overrides:t.overrides,layouts:i,placer:on.none()}))})],Cc={create:yo("start","soffset","finish","foffset")},kc=Tn([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),Oc=(kc.before,kc.on,kc.after,function(n){return n.fold(l,l,l)}),Ec=Tn([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),Tc={domRange:Ec.domRange,relative:Ec.relative,exact:Ec.exact,exactFromRange:function(n){return Ec.exact(n.start(),n.soffset(),n.finish(),n.foffset())},getWin:function(n){var t=function(n){return n.match({domRange:function(n){return we.fromDom(n.startContainer)},relative:function(n,t){return Oc(n)},exact:function(n,t,e,o){return n}})}(n);return Oo(t)},range:Cc.create},Bc=Tn([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]);function Dc(n){return rf.get(n)}function Ac(n){return rf.getOption(n)}function _c(e,o,n,t,r){function i(n){var t=e.dom().createRange();return t.setStart(o.dom(),n),t.collapse(!0),t}var u=Dc(o).length,a=function(n,t,e,o,r){if(0===r)return 0;if(t===o)return r-1;for(var i=o,u=1;u<r;u++){var a=n(u),c=Math.abs(t-a.left);if(e<=a.bottom){if(e<a.top||i<c)return u-1;i=c}}return 0}(function(n){return i(n).getBoundingClientRect()},n,t,r.right,u);return i(a)}function Mc(n){return function(n){return Ac(n).filter(function(n){return 0!==n.trim().length||-1<n.indexOf("\xa0")}).isSome()}(n)||vn(af,Ke(n))}function Fc(n){return no(n,Mc)}function Ic(n){return cf(n,Mc)}function Rc(n,t){return t-n.left<n.right-t}function Vc(n,t,e){var o=n.dom().createRange();return o.selectNode(t.dom()),o.collapse(e),o}function Nc(t,n,e){var o=t.dom().createRange();o.selectNode(n.dom());var r=o.getBoundingClientRect(),i=Rc(r,e);return(!0===i?Fc:Ic)(n).map(function(n){return Vc(t,n,i)})}function Hc(n,t,e){var o=t.dom().getBoundingClientRect(),r=Rc(o,e);return on.some(Vc(n,t,r))}function Pc(n,t,e,o){var r=n.dom().createRange();r.selectNode(t.dom());var i=r.getBoundingClientRect();return function(n,t,e,o){var r=n.dom().createRange();r.selectNode(t.dom());var i=r.getBoundingClientRect(),u=Math.max(i.left,Math.min(i.right,e)),a=Math.max(i.top,Math.min(i.bottom,o));return uf(n,t,u,a)}(n,t,Math.max(i.left,Math.min(i.right,e)),Math.max(i.top,Math.min(i.bottom,o)))}function zc(n,t){return Lt(t,n)}function Lc(n,t,e,o){var r=function(n,t,e,o){var r=Co(n).dom().createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r}(n,t,e,o),i=jt(n,e)&&t===o;return r.collapsed&&!i}function jc(n){var t=we.fromDom(n.anchorNode),e=we.fromDom(n.focusNode);return Lc(t,n.anchorOffset,e,n.focusOffset)?on.some(Cc.create(t,n.anchorOffset,e,n.focusOffset)):function(n){if(0<n.rangeCount){var t=n.getRangeAt(0),e=n.getRangeAt(n.rangeCount-1);return on.some(Cc.create(we.fromDom(t.startContainer),t.startOffset,we.fromDom(e.endContainer),e.endOffset))}return on.none()}(n)}function Uc(n,t){return function(n){var t=n.getClientRects(),e=0<t.length?t[0]:n.getBoundingClientRect();return 0<e.width||0<e.height?on.some(e).map(Ra):on.none()}(function(i,n){return Na(i,n).match({ltr:function(n,t,e,o){var r=i.document.createRange();return r.setStart(n.dom(),t),r.setEnd(e.dom(),o),r},rtl:function(n,t,e,o){var r=i.document.createRange();return r.setStart(e.dom(),o),r.setEnd(n.dom(),t),r}})}(n,t))}function Wc(n){return n.fold(function(n){return n},function(n,t,e){return n.translate(-t,-e)})}function Gc(n){return n.fold(function(n){return n},function(n,t,e){return n})}function Xc(n){return O(n,function(n,t){return n.translate(t.left(),t.top())},Iu(0,0))}function Yc(n){var t=S(n,Gc);return Xc(t)}function qc(n,t,e){var o=Co(n.element()),r=pu(o),i=function(o,n,t){var e=Oo(t.root).dom();return on.from(e.frameElement).map(we.fromDom).filter(function(n){var t=Co(n),e=Co(o.element());return jt(t,e)}).map(du)}(n,0,e).getOr(r);return df(i,r.left(),r.top())}function Kc(n,t){return Pr(n)?pf(n,t):function(n,t){var e=Bo(n);if(0===e.length)return sf(n,t);if(t<e.length)return sf(e[t],0);var o=e[e.length-1],r=Pr(o)?Dc(o).length:Bo(o).length;return sf(o,r)}(n,t)}function Jc(n,t){return t.getSelection.getOrThunk(function(){return function(){return function(n){return on.from(n.getSelection()).filter(function(n){return 0<n.rangeCount}).bind(jc)}(n)}})().map(function(n){var t=Kc(n.start(),n.soffset()),e=Kc(n.finish(),n.foffset());return Tc.range(t.element(),t.offset(),e.element(),e.offset())})}function $c(n){return n.x()+n.width()}function Qc(n,t){return n.x()-t.width()}function Zc(n,t){return n.y()-t.height()+n.height()}function ns(n){return n.y()}function ts(n,t,e){return Ya($c(n),ns(n),e.southeast(),Ka(),"link-layout-se")}function es(n,t,e){return Ya(Qc(n,t),ns(n),e.southwest(),Ja(),"link-layout-sw")}function os(n,t,e){return Ya($c(n),Zc(n,t),e.northeast(),$a(),"link-layout-ne")}function rs(n,t,e){return Ya(Qc(n,t),Zc(n,t),e.northwest(),Qa(),"link-layout-nw")}function is(n,t,e,o){var r=n+t;return o<r?e:r<e?o:r}function us(n,t,e){return n<=t?t:e<=n?e:n}function as(n,t,e,o){var r=n.x(),i=n.y(),u=n.bubble().offset().left(),a=n.bubble().offset().top(),c=o.y(),s=o.bottom(),f=o.x(),l=o.right(),d=i+a,m=function(n,t,e,o,r){var i=r.x(),u=r.y(),a=r.width(),c=r.height(),s=i<=n,f=u<=t,l=s&&f,d=n+e<=i+a&&t+o<=u+c,m=Math.abs(Math.min(e,s?i+a-n:i-(n+e))),g=Math.abs(Math.min(o,f?u+c-t:u-(t+o)));return{originInBounds:l,sizeInBounds:d,limitX:us(n,r.x(),r.right()),limitY:us(t,r.y(),r.bottom()),deltaW:m,deltaH:g}}(r+u,d,t,e,o),g=m.originInBounds,p=m.sizeInBounds,h=m.limitX,v=m.limitY,b=m.deltaW,y=m.deltaH,x=nn(v+y-c),w=nn(s-v),S=function(n,t,e,o){return n.fold(t,t,o,o,t,o,e,e)}(n.direction(),w,w,x),C=nn(h+b-f),k=nn(l-h),O=function(n,t,e,o){return n.fold(t,o,t,o,e,e,t,o)}(n.direction(),k,k,C),E=gc({x:h,y:v,width:b,height:y,maxHeight:S,maxWidth:O,direction:n.direction(),classes:{on:n.bubble().classesOn(),off:n.bubble().classesOff()},label:n.label(),candidateYforTest:d});return g&&p?wf.fit(E):wf.nofit(E,b,y)}function cs(n,t,e,o){kr(t,"max-height"),kr(t,"max-width");var r=function(n){return{width:nn(gu(n)),height:nn(fu(n))}}(t);return function(n,e,u,a,c){function o(n,o,r,i){var t=n(e,u,a);return as(t,s,f,c).fold(wf.fit,function(n,t,e){return i<e||r<t?wf.nofit(n,t,e):wf.nofit(o,r,i)})}var s=u.width(),f=u.height();return O(n,function(n,t){var e=d(o,t);return n.fold(wf.fit,e)},wf.nofit(gc({x:e.x(),y:e.y(),width:u.width(),height:u.height(),maxHeight:u.height(),maxWidth:u.width(),direction:Ka(),classes:{on:[],off:[]},label:"none",candidateYforTest:e.y()}),-1,-1)).fold(l,l)}(o.preference(),n,r,e,o.bounds())}function ss(n,t,e){function o(n){return n+"px"}var r=function(n,r){return n.fold(function(){return pc("absolute",on.some(r.x()),on.some(r.y()),on.none(),on.none())},function(n,t,e,o){return Ta("absolute",r,n,t,e,o)},function(n,t,e,o){return Ta("fixed",r,n,t,e,o)})}(e.origin(),t);!function(n,t){var e=n.dom();Cn(t,function(n,t){n.fold(function(){br(e,t)},function(n){vr(e,t,n)})})}(n,{position:on.some(r.position()),left:r.left().map(o),top:r.top().map(o),right:r.right().map(o),bottom:r.bottom().map(o)})}function fs(n,t){!function(n,t){var e=Mu.max(n,t,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]);yr(n,"max-height",e+"px")}(n,Math.floor(t))}function ls(n,t,e){return n[t]===undefined?e:n[t]}function ds(n,t,e,o,r,i){var u=ls(i,"maxHeightFunction",Sf()),a=ls(i,"maxWidthFunction",Z),c=n.anchorBox(),s=n.origin(),f=kf({bounds:function(o,n){return n.fold(function(){return o.fold(Su,Su,yu)},function(e){return o.fold(e,e,function(){var n=e(),t=vc(o,n.x(),n.y());return yu(t.left(),t.top(),n.width(),n.height())})})}(s,r),origin:s,preference:o,maxHeightFunction:u,maxWidthFunction:a});Of(c,t,e,f)}function ms(n,t,e,o,r){var i=function(n,t){return xf(n,t)}(e.anchorBox,t);ds(i,r.element(),e.bubble,e.layouts,o,e.overrides)}function gs(n,t){Hi(n.element(),t.element())}function ps(t,n){var e=t.components();!function(n){bn(n.components(),function(n){return Pi(n.element())}),Io(n.element()),n.syncComponents()}(t);var o=_(e,n);bn(o,function(n){_f(n),t.getSystem().removeFromWorld(n)}),bn(n,function(n){n.getSystem().isConnected()?gs(t,n):(t.getSystem().addToWorld(n),gs(t,n),$e(t.element())&&Mf(n)),t.syncComponents()})}function hs(n,t){Ff(n,t,Hi)}function vs(n){_f(n),Pi(n.element()),n.getSystem().removeFromWorld(n)}function bs(t){var n=Eo(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()});vs(t),n.each(function(n){n.syncComponents()})}function ys(n){var t=n.components();bn(t,vs),Io(n.element()),n.syncComponents()}function xs(n,t){If(n,t,Hi)}function ws(t){var n=Bo(t.element());bn(n,function(n){t.getByDom(n).each(_f)}),Pi(t.element())}function Ss(t,n,e,o){e.get().each(function(n){ys(t)});var r=n.getAttachPoint(t);hs(r,t);var i=t.getSystem().build(o);return hs(t,i),e.set(i),i}function Cs(n,t,e,o){var r=Ss(n,t,e,o);return t.onOpen(n,r),r}function ks(t,e,o){o.get().each(function(n){ys(t),bs(t),e.onClose(t,n),o.clear()})}function Os(n,t,e){return e.isOpen()}function Es(n){var t,e=ot("Dismissal",Wf,n);return(t={})[Lf()]={schema:qn([ct("target")]),onReceive:function(t,n){zf.isOpen(t)&&(zf.isPartOf(t,n.target)||e.isExtraPart(t,n.target)||e.fireEventInstead.fold(function(){return zf.close(t)},function(n){return oo(t,n.event)}))}},t}function Ts(n){var t,e=ot("Reposition",Gf,n);return(t={})[jf()]={onReceive:function(t){zf.isOpen(t)&&e.fireEventInstead.fold(function(){return e.doReposition(t)},function(n){return oo(t,n.event)})}},t}function Bs(n,t,e){t.store.manager.onLoad(n,t,e)}function Ds(n,t,e){t.store.manager.onUnload(n,t,e)}function As(){var n=ye(null);return nu({set:n.set,get:n.get,isNotSet:function(){return null===n.get()},clear:function(){n.set(null)},readState:function(){return{mode:"memory",value:n.get()}}})}function _s(){var i=ye({}),u=ye({});return nu({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(n){return Nn(i.get(),n).orThunk(function(){return Nn(u.get(),n)})},update:function(n){var t=i.get(),e=u.get(),o={},r={};bn(n,function(t){o[t.value]=t,Nn(t,"meta").each(function(n){Nn(n,"text").each(function(n){r[n]=t})})}),i.set(N(N({},t),o)),u.set(N(N({},e),r))},clear:function(){i.set({}),u.set({})}})}function Ms(n,t,e,o){var r=t.store;e.update([o]),r.setValue(n,o),t.onSetValue(n,o)}function Fs(t,n){return Dt(t,{},S(n,function(n){return function(t,e){return ce(t,t,Fn(),Yn(function(n){return qt("The field: "+t+" is forbidden. "+e)}))}(n.name(),"Cannot configure "+n.name()+" for "+t)}).concat([At("dump",l)]))}function Is(n){return n.dump}function Rs(n,t){return N(N({},n.dump),ba(t))}function Vs(n,t,e,o){return e.uiType===el?function(n,t,e,o){return n.exists(function(n){return n!==e.owner})?ol.single(!0,nn(e)):Nn(o,e.name).fold(function(){throw new Error("Unknown placeholder component: "+e.name+"\nKnown: ["+wn(o)+"]\nNamespace: "+n.getOr("none")+"\nSpec: "+JSON.stringify(e,null,2))},function(n){return n.replace()})}(n,0,e,o):ol.single(!1,nn(e))}function Ns(t,e,n,o){var r=P(o,function(n,t){return function(n,t){var e=!1;return{name:nn(n),required:function(){return t.fold(function(n,t){return n},function(n,t){return n})},used:function(){return e},replace:function(){if(!0===e)throw new Error("Trying to use the same placeholder more than once: "+n);return e=!0,t}}}(t,n)}),i=function(t,e,n,o){return B(n,function(n){return rl(t,e,n,o)})}(t,e,n,r);return Cn(r,function(n){if(!1===n.used()&&n.required())throw new Error("Placeholder: "+n.name()+" was not found in components list\nNamespace: "+t.getOr("none")+"\nComponents: "+JSON.stringify(e.components,null,2))}),i}function Hs(n){return n.fold(on.some,on.none,on.some,on.some)}function Ps(n){function t(n){return n.name}return n.fold(t,t,t,t)}function zs(e,o){return function(n){var t=ot("Converting part type",o,n);return e(t)}}function Ls(n,t,e,o){return Dn(t.defaults(n,e,o),e,{uid:n.partUids[t.name]},t.overrides(n,e,o))}function js(r,n){var t={};return bn(n,function(n){Hs(n).each(function(e){var o=El(r,e.pname);t[e.name]=function(n){var t=ot("Part: "+e.name+" in "+r,re(e.schema),n);return N(N({},o),{config:n,validated:t})}})}),t}function Us(n,t,e){return{uiType:al(),owner:n,name:t,config:e,validated:{}}}function Ws(n){return B(n,function(n){return n.fold(on.none,on.some,on.none,on.none).map(function(n){return mt(n.name,n.schema.concat([Zu(kl())]))}).toArray()})}function Gs(n){return S(n,Ps)}function Xs(n,t,e){return function(n,e,t){var i={},o={};return bn(t,function(n){n.fold(function(o){i[o.pname]=il(!0,function(n,t,e){return o.factory.sketch(Ls(n,o,t,e))})},function(n){var t=e.parts[n.name];o[n.name]=nn(n.factory.sketch(Ls(e,n,t[kl()]),t))},function(o){i[o.pname]=il(!1,function(n,t,e){return o.factory.sketch(Ls(n,o,t,e))})},function(r){i[r.pname]=ul(!0,function(t,n,e){var o=t[r.name];return S(o,function(n){return r.factory.sketch(Dn(r.defaults(t,n,e),n,r.overrides(t,n)))})})})}),{internals:nn(i),externals:nn(o)}}(0,t,e)}function Ys(n,t,e){return Ns(on.some(n),t,t.components,e)}function qs(n,t,e){var o=t.partUids[e];return n.getSystem().getByUid(o).toOption()}function Ks(n,t,e){return qs(n,t,e).getOrDie("Could not find part: "+e)}function Js(n,t,e){var o={},r=t.partUids,i=n.getSystem();return bn(e,function(n){o[n]=nn(i.getByUid(r[n]))}),o}function $s(n,t){var e=n.getSystem();return P(t.partUids,function(n,t){return nn(e.getByUid(n))})}function Qs(n){return wn(n.partUids)}function Zs(n,t,e){var o={},r=t.partUids,i=n.getSystem();return bn(e,function(n){o[n]=nn(i.getByUid(r[n]).getOrDie())}),o}function nf(t,n){var e=Gs(n);return K(S(e,function(n){return{key:n,value:t+"-"+n}}))}function tf(t){return ce("partUids","partUids",Rn(function(n){return nf(n.uid,t)}),de())}function ef(n,t,e,o,r){var i=function(n,t){return(0<n.length?[mt("parts",n)]:[]).concat([ct("uid"),St("dom",{}),St("components",[]),Zu("originalSpec"),St("debug.sketcher",{})]).concat(t)}(o,r);return ot(n+" [SpecSchema]",qn(i.concat(t)),e)}function of(n,t,e,o,r){var i=Bl(r),u=Ws(e),a=tf(e),c=ef(n,t,i,u,[a]),s=Xs(0,c,e);return o(c,Ys(n,c,s.internals()),i,s.externals())}var rf=function rI(e,o){var t=function(n){return e(n)?on.from(n.dom().nodeValue):on.none()};return{get:function(n){if(!e(n))throw new Error("Can only get "+o+" value of a "+o+" node");return t(n).getOr("")},getOption:t,set:function(n,t){if(!e(n))throw new Error("Can only set raw "+o+" value of a "+o+" node");n.dom().nodeValue=t}}}(Pr,"text"),uf=function(n,t,e,o){return Pr(t)?function(t,e,o,r){var n=t.dom().createRange();n.selectNode(e.dom());var i=n.getClientRects();return Du(i,function(n){return Ha(n,o,r)?on.some(n):on.none()}).map(function(n){return _c(t,e,o,r,n)})}(n,t,e,o):function(t,n,e,o){var r=t.dom().createRange(),i=Bo(n);return Du(i,function(n){return r.selectNode(n.dom()),Ha(r.getBoundingClientRect(),e,o)?uf(t,n,e,o):on.none()})}(n,t,e,o)},af=["img","br"],cf=function(n,i){var u=function(n){for(var t=Bo(n),e=t.length-1;0<=e;e--){var o=t[e];if(i(o))return on.some(o);var r=u(o);if(r.isSome())return r}return on.none()};return u(n)},sf=(document.caretPositionFromPoint||document.caretRangeFromPoint,yo("element","offset")),ff=Tn([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),lf=ff.screen,df=ff.absolute,mf=function(n,t,e,o){var r=n,i=t,u=e,a=o;n<0&&(r=0,u=e+n),t<0&&(i=0,a=o+t);var c=lf(Iu(r,i));return on.some(Pu(c,u,a))},gf=function(n,i,u,a,c){return n.map(function(n){var t=[i,n.point()],e=function(n,t,e,o){return n.fold(t,e,o)}(a,function(){return Yc(t)},function(){return Yc(t)},function(){return function(n){var t=S(n,Wc);return Xc(t)}(t)}),o=zu(e.left(),e.top(),n.width(),n.height()),r=Ma(c,u,u.showAbove?[ca,sa,ua,aa,oc,rc]:[ua,aa,ca,sa,rc,rc],u.showAbove?[sa,ca,aa,ua,oc,rc]:[aa,ua,sa,ca,rc,oc]);return Da({anchorBox:o,bubble:u.bubble.getOr(Ea()),overrides:u.overrides,layouts:r,placer:on.none()})})},pf=yo("element","offset"),hf=[ht("getSelection"),ct("root"),ht("bubble"),_a(),St("overrides",{}),St("showAbove",!1),Qu("placement",function(n,t,e){var o=Oo(t.root).dom(),r=qc(n,0,t),i=Jc(o,t).bind(function(n){return Uc(o,Tc.exactFromRange(n)).orThunk(function(){var t=we.fromText("\ufeff");return Ao(n.start(),t),Uc(o,Tc.exact(t,0,t,1)).map(function(n){return Pi(t),n})}).bind(function(n){return mf(n.left(),n.top(),n.width(),n.height())})}),u=Jc(o,t).bind(function(n){return Hr(n.start())?on.some(n.start()):Eo(n.start())}).getOr(n.element());return gf(i,r,t,e,u)})],vf=[ct("node"),ct("root"),ht("bubble"),_a(),St("overrides",{}),St("showAbove",!1),Qu("placement",function(r,i,u){var a=qc(r,0,i);return i.node.bind(function(n){var t=n.dom().getBoundingClientRect(),e=mf(t.left,t.top,t.width,t.height),o=i.node.getOr(r.element());return gf(e,a,i,u,o)})})],bf=[ct("item"),_a(),St("overrides",{}),Qu("placement",function(n,t,e){var o=Ba(e,t.item.element()),r=Ma(n.element(),t,[ts,es,os,rs],[es,ts,rs,os]);return on.some(Da({anchorBox:o,bubble:Ea(),overrides:t.overrides,layouts:r,placer:on.none()}))})],yf=it("anchor",{selection:hf,node:vf,hotspot:wc,submenu:bf,makeshift:Sc}),xf=yo("anchorBox","origin"),wf=Tn([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),Sf=nn(function(n,t){fs(n,t),xr(n,{"overflow-x":"hidden","overflow-y":"auto"})}),Cf=nn(function(n,t){fs(n,t)}),kf=So(["bounds","origin","preference","maxHeightFunction","maxWidthFunction"],[]),Of=function(n,t,e,o){var r=cs(n,t,e,o);ss(t,r,o),function(n,t){var e=t.classes();pr(n,e.off),gr(n,e.on)}(t,r),function(n,t,e){e.maxHeightFunction()(n,t.maxHeight())}(t,r,o),function(n,t,e){e.maxWidthFunction()(n,t.maxWidth())}(t,r,o)},Ef=function(n,t,e,o,r,i){var u=i.map(xu);return Tf(n,t,e,o,r,u)},Tf=function(r,i,n,t,u,a){var c=ot("positioning anchor.info",yf,t);ka(function(){yr(u.element(),"position","fixed");var n=Sr(u.element(),"visibility");yr(u.element(),"visibility","hidden");var t=i.useFixed()?function(){var n=v.document.documentElement;return yc(0,0,n.clientWidth,n.clientHeight)}():function(n){var t=du(n.element()),e=n.element().dom().getBoundingClientRect();return bc(t.left(),t.top(),e.width,e.height)}(r),e=c.placement,o=a.map(nn).or(i.getBounds);e(r,c,t).each(function(n){n.placer.getOr(ms)(r,t,n,o,u)}),n.fold(function(){kr(u.element(),"visibility")},function(n){yr(u.element(),"visibility",n)}),Sr(u.element(),"left").isNone()&&Sr(u.element(),"top").isNone()&&Sr(u.element(),"right").isNone()&&Sr(u.element(),"bottom").isNone()&&Sr(u.element(),"position").is("fixed")&&kr(u.element(),"position")},u.element())},Bf=/* */Object.freeze({position:function(n,t,e,o,r){Ef(n,t,e,o,r,on.none())},positionWithin:Ef,positionWithinBounds:Tf,getMode:function(n,t,e){return t.useFixed()?"fixed":"absolute"}}),Df=[St("useFixed",u),ht("getBounds")],Af=ya({fields:Df,name:"positioning",active:dc,apis:Bf}),_f=function(n){oo(n,ki());var t=n.components();bn(t,_f)},Mf=function(n){var t=n.components();bn(t,Mf),oo(n,Ci())},Ff=function(n,t,e){n.getSystem().addToWorld(t),e(n.element(),t.element()),$e(n.element())&&Mf(t),n.syncComponents()},If=function(n,t,e){e(n,t.element());var o=Bo(t.element());bn(o,function(n){t.getByDom(n).each(Mf)})},Rf=function(n,t,e){var o=t.getAttachPoint(n);yr(n.element(),"position",Af.getMode(o)),function(t,n,e,o){Sr(t.element(),n).fold(function(){jo(t.element(),e)},function(n){Po(t.element(),e,n)}),yr(t.element(),n,o)}(n,"visibility",t.cloakVisibilityAttr,"hidden")},Vf=function(n,t,e){!function(t){return x(["top","left","right","bottom"],function(n){return Sr(t,n).isSome()})}(n.element())&&kr(n.element(),"position"),function(n,t,e){if(Lo(n.element(),e)){var o=zo(n.element(),e);yr(n.element(),t,o)}else kr(n.element(),t)}(n,"visibility",t.cloakVisibilityAttr)},Nf=/* */Object.freeze({cloak:Rf,decloak:Vf,open:Cs,openWhileCloaked:function(n,t,e,o,r){Rf(n,t),Cs(n,t,e,o),r(),Vf(n,t)},close:ks,isOpen:Os,isPartOf:function(t,e,n,o){return Os(0,0,n)&&n.get().exists(function(n){return e.isPartOf(t,n,o)})},getState:function(n,t,e){return e.get()},setContent:function(n,t,e,o){return e.get().map(function(){return Ss(n,t,e,o)})}}),Hf=/* */Object.freeze({events:function(e,o){return co([lo(hi(),function(n,t){ks(n,e,o)})])}}),Pf=[qu("onOpen"),qu("onClose"),ct("isPartOf"),ct("getAttachPoint"),St("cloakVisibilityAttr","data-precloak-visibility")],zf=ya({fields:Pf,name:"sandboxing",active:Hf,apis:Nf,state:/* */Object.freeze({init:function(){var t=ye(on.none()),n=nn("not-implemented");return nu({readState:n,isOpen:function(){return t.get().isSome()},clear:function(){t.set(on.none())},set:function(n){t.set(on.some(n))},get:function(n){return t.get()}})}})}),Lf=nn("dismiss.popups"),jf=nn("reposition.popups"),Uf=nn("mouse.released"),Wf=qn([St("isExtraPart",nn(!1)),wt("fireEventInstead",[St("event",Oi())])]),Gf=qn([St("isExtraPart",nn(!1)),wt("fireEventInstead",[St("event",Ei())]),dt("doReposition")]),Xf=/* */Object.freeze({onLoad:Bs,onUnload:Ds,setValue:function(n,t,e,o){t.store.manager.setValue(n,t,e,o)},getValue:function(n,t,e){return t.store.manager.getValue(n,t,e)},getState:function(n,t,e){return e}}),Yf=/* */Object.freeze({events:function(e,o){var n=e.resetOnDom?[Ii(function(n,t){Bs(n,e,o)}),Ri(function(n,t){Ds(n,e,o)})]:[ga(e,o,Bs)];return co(n)}}),qf=/* */Object.freeze({memory:As,dataset:_s,manual:function(){return nu({readState:function(){}})},init:function(n){return n.store.manager.state(n)}}),Kf=[ht("initialValue"),ct("getFallbackEntry"),ct("getDataKey"),ct("setValue"),Qu("manager",{setValue:Ms,getValue:function(n,t,e){var o=t.store,r=o.getDataKey(n);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(n){return n})},onLoad:function(t,e,o){e.store.initialValue.each(function(n){Ms(t,e,o,n)})},onUnload:function(n,t,e){e.clear()},state:_s})],Jf=[ct("getValue"),St("setValue",Z),ht("initialValue"),Qu("manager",{setValue:function(n,t,e,o){t.store.setValue(n,o),t.onSetValue(n,o)},getValue:function(n,t,e){return t.store.getValue(n)},onLoad:function(t,e,n){e.store.initialValue.each(function(n){e.store.setValue(t,n)})},onUnload:Z,state:Zi.init})],$f=[ht("initialValue"),Qu("manager",{setValue:function(n,t,e,o){e.set(o),t.onSetValue(n,o)},getValue:function(n,t,e){return e.get()},onLoad:function(n,t,e){t.store.initialValue.each(function(n){e.isNotSet()&&e.set(n)})},onUnload:function(n,t,e){e.clear()},state:As})],Qf=[Ct("store",{mode:"memory"},it("mode",{memory:$f,manual:Jf,dataset:Kf})),qu("onSetValue"),St("resetOnDom",!1)],Zf=ya({fields:Qf,name:"representing",active:Yf,apis:Xf,extra:{setValueFrom:function(n,t){var e=Zf.getValue(t);Zf.setValue(n,e)}},state:qf}),nl=Fs,tl=Rs,el="placeholder",ol=Tn([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),rl=function(i,u,a,c){return Vs(i,0,a,c).fold(function(n,t){var e=t(u,a.config,a.validated),o=Nn(e,"components").getOr([]),r=B(o,function(n){return rl(i,u,n,c)});return[N(N({},e),{components:r})]},function(n,t){var e=t(u,a.config,a.validated);return a.validated.preprocess.getOr(l)(e)})},il=ol.single,ul=ol.multiple,al=nn(el),cl=Tn([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),sl=St("factory",{sketch:l}),fl=St("schema",[]),ll=ct("name"),dl=ce("pname","pname",In(function(n){return"<alloy."+Xo(n.name)+">"}),de()),ml=At("schema",function(){return[ht("preprocess")]}),gl=St("defaults",nn({})),pl=St("overrides",nn({})),hl=re([sl,fl,ll,dl,gl,pl]),vl=re([sl,fl,ll,gl,pl]),bl=re([sl,fl,ll,dl,gl,pl]),yl=re([sl,ml,ll,ct("unit"),dl,gl,pl]),xl=zs(cl.required,hl),wl=zs(cl.external,vl),Sl=zs(cl.optional,bl),Cl=zs(cl.group,yl),kl=nn("entirety"),Ol=/* */Object.freeze({required:xl,external:wl,optional:Sl,group:Cl,asNamedPart:Hs,name:Ps,asCommon:function(n){return n.fold(l,l,l,l)},original:kl}),El=function(n,t){return{uiType:al(),owner:n,name:t}},Tl=/* */Object.freeze({generate:js,generateOne:Us,schemas:Ws,names:Gs,substitutes:Xs,components:Ys,defaultUids:nf,defaultUidsSchema:tf,getAllParts:$s,getAllPartNames:Qs,getPart:qs,getPartOrDie:Ks,getParts:Js,getPartsOrDie:Zs}),Bl=function(n){return n.hasOwnProperty("uid")?n:N(N({},n),{uid:Yo("uid")})};function Dl(n){var t=ot("Sketcher for "+n.name,Kl,n),e=P(t.apis,$o),o=P(t.extraApis,function(n,t){return Ko(n,t)});return N(N({name:nn(t.name),partFields:nn([]),configFields:nn(t.configFields),sketch:function(n){return function(n,t,e,o){var r=Bl(o);return e(ef(n,t,r,[],[]),r)}(t.name,t.configFields,t.factory,n)}},e),o)}function Al(n){var t=ot("Sketcher for "+n.name,Jl,n),e=js(t.name,t.partFields),o=P(t.apis,$o),r=P(t.extraApis,function(n,t){return Ko(n,t)});return N(N({name:nn(t.name),partFields:nn(t.partFields),configFields:nn(t.configFields),sketch:function(n){return of(t.name,t.configFields,t.partFields,t.factory,n)},parts:nn(e)},o),r)}function _l(n){return"input"===Ke(n)&&"radio"!==zo(n,"type")||"textarea"===Ke(n)}function Ml(e,o,n,r){var t=zc(e.element(),"."+o.highlightClass);bn(t,function(t){x(r,function(n){return n.element()===t})||(dr(t,o.highlightClass),e.getSystem().getByDom(t).each(function(n){o.onDehighlight(e,n),oo(n,Mi())}))})}function Fl(n,t,e,o){Ml(n,t,0,[o]),nd(n,t,e,o)||(fr(o.element(),t.highlightClass),t.onHighlight(n,o),oo(o,_i()))}function Il(e,t,n,o){var r=zc(e.element(),"."+t.itemClass);return T(r,function(n){return mr(n,t.highlightClass)}).bind(function(n){var t=is(n,o,0,r.length-1);return e.getSystem().getByDom(r[t]).toOption()})}function Rl(n,t,e){var o=A(n.slice(0,t)),r=A(n.slice(t+1));return E(o.concat(r),e)}function Vl(n,t,e){var o=A(n.slice(0,t));return E(o,e)}function Nl(n,t,e){var o=n.slice(0,t),r=n.slice(t+1);return E(r.concat(o),e)}function Hl(n,t,e){var o=n.slice(t+1);return E(o,e)}function Pl(e){return function(n){var t=n.raw();return vn(e,t.which)}}function zl(n){return function(t){return D(n,function(n){return n(t)})}}function Ll(n){return!0===n.raw().shiftKey}function jl(n){return!0===n.raw().ctrlKey}function Ul(n,t){return{matches:n,classification:t}}function Wl(n,t,e){t.exists(function(t){return e.exists(function(n){return jt(n,t)})})||ro(n,Ti(),{prevFocus:t,newFocus:e})}function Gl(){function r(n){return Ca(n.element())}return{get:r,set:function(n,t){var e=r(n);n.getSystem().triggerFocus(t,n.element());var o=r(n);Wl(n,e,o)}}}function Xl(){function r(n){return ad.getHighlighted(n).map(function(n){return n.element()})}return{get:r,set:function(t,n){var e=r(t);t.getSystem().getByDom(n).fold(Z,function(n){ad.highlight(t,n)});var o=r(t);Wl(t,e,o)}}}var Yl,ql,Kl=qn([ct("name"),ct("factory"),ct("configFields"),St("apis",{}),St("extraApis",{})]),Jl=qn([ct("name"),ct("factory"),ct("configFields"),ct("partFields"),St("apis",{}),St("extraApis",{})]),$l=/* */Object.freeze({getCurrent:function(n,t,e){return t.find(n)}}),Ql=[ct("find")],Zl=ya({fields:Ql,name:"composing",apis:$l}),nd=function(n,t,e,o){return mr(o.element(),t.highlightClass)},td=function(n,t,e,o){var r=zc(n.element(),"."+t.itemClass);return on.from(r[o]).fold(function(){return an.error("No element found with index "+o)},n.getSystem().getByDom)},ed=function(t,n,e){return ku(t.element(),"."+n.itemClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},od=function(t,n,e){var o=zc(t.element(),"."+n.itemClass);return(0<o.length?on.some(o[o.length-1]):on.none()).bind(function(n){return t.getSystem().getByDom(n).toOption()})},rd=function(t,n,e){var o=zc(t.element(),"."+n.itemClass);return Bu(S(o,function(n){return t.getSystem().getByDom(n).toOption()}))},id=/* */Object.freeze({dehighlightAll:function(n,t,e){return Ml(n,t,0,[])},dehighlight:function(n,t,e,o){nd(n,t,e,o)&&(dr(o.element(),t.highlightClass),t.onDehighlight(n,o),oo(o,Mi()))},highlight:Fl,highlightFirst:function(t,e,o){ed(t,e).each(function(n){Fl(t,e,o,n)})},highlightLast:function(t,e,o){od(t,e).each(function(n){Fl(t,e,o,n)})},highlightAt:function(t,e,o,n){td(t,e,o,n).fold(function(n){throw new Error(n)},function(n){Fl(t,e,o,n)})},highlightBy:function(t,e,o,n){var r=rd(t,e);E(r,n).each(function(n){Fl(t,e,o,n)})},isHighlighted:nd,getHighlighted:function(t,n,e){return ku(t.element(),"."+n.highlightClass).bind(function(n){return t.getSystem().getByDom(n).toOption()})},getFirst:ed,getLast:od,getPrevious:function(n,t,e){return Il(n,t,0,-1)},getNext:function(n,t,e){return Il(n,t,0,1)},getCandidates:rd}),ud=[ct("highlightClass"),ct("itemClass"),qu("onHighlight"),qu("onDehighlight")],ad=ya({fields:ud,name:"highlighting",apis:id}),cd=b(Ll);(ql=Yl=Yl||{}).OnFocusMode="onFocus",ql.OnEnterOrSpaceMode="onEnterOrSpace",ql.OnApiMode="onApi";function sd(n,t,e,i,u){function a(t,e,n,o,r){return function(n,t){return E(n,function(n){return n.matches(t)}).map(function(n){return n.classification})}(n(t,e,o,r),e.event()).bind(function(n){return n(t,e,o,r)})}var o={schema:function(){return n.concat([St("focusManager",Gl()),Ct("focusInside","onFocus",Zn(function(n){return vn(["onFocus","onEnterOrSpace","onApi"],n)?an.value(n):an.error("Invalid value for focusInside")})),Qu("handler",o),Qu("state",t),Qu("sendFocusIn",u)])},processKey:a,toEvents:function(o,r){var n=o.focusInside!==Yl.OnFocusMode?on.none():u(o).map(function(e){return lo(ci(),function(n,t){e(n,o,r),t.stop()})});return co(n.toArray().concat([lo(ni(),function(n,t){a(n,t,e,o,r).fold(function(){!function(t,e){var n=Pl([32].concat([13]))(e.event());o.focusInside===Yl.OnEnterOrSpaceMode&&n&&Ut(t,e)&&u(o).each(function(n){n(t,o,r),e.stop()})}(n,t)},function(n){t.stop()})}),lo(ti(),function(n,t){a(n,t,i,o,r).each(function(n){t.stop()})})]))}};return o}function fd(n){function i(n,t){var e=n.visibilitySelector.bind(function(n){return Ou(t,n)}).getOr(t);return 0<su(e)}function t(t,e){(function(n,t){var e=zc(n.element(),t.selector),o=C(e,function(n){return i(t,n)});return on.from(o[t.firstTabstop])})(t,e).each(function(n){e.focusManager.set(t,n)})}function u(t,n,e,o,r){return r(n,e,function(n){return function(n,t){return i(n,t)&&n.useTabstopAt(t)}(o,n)}).fold(function(){return o.cyclic?on.some(!0):on.none()},function(n){return o.focusManager.set(t,n),on.some(!0)})}function a(t,n,e,o){var r=zc(t.element(),e.selector);return function(n,t){return t.focusManager.get(n).bind(function(n){return Ou(n,t.selector)})}(t,e).bind(function(n){return T(r,d(jt,n)).bind(function(n){return u(t,r,n,e,o)})})}var e=[ht("onEscape"),ht("onEnter"),St("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),St("firstTabstop",0),St("useTabstopAt",nn(!0)),ht("visibilitySelector")].concat([n]),o=nn([Ul(zl([Ll,Pl([9])]),function(n,t,e,o){var r=e.cyclic?Rl:Vl;return a(n,0,e,r)}),Ul(Pl([9]),function(n,t,e,o){var r=e.cyclic?Nl:Hl;return a(n,0,e,r)}),Ul(Pl([27]),function(t,e,n,o){return n.onEscape.bind(function(n){return n(t,e)})}),Ul(zl([cd,Pl([13])]),function(t,e,n,o){return n.onEnter.bind(function(n){return n(t,e)})})]),r=nn([]);return sd(e,Zi.init,o,r,function(){return on.some(t)})}function ld(n,t,e){return _l(e)&&Pl([32])(t.event())?on.none():function(n,t,e){return uo(n,e,di()),on.some(!0)}(n,0,e)}function dd(n,t){return on.some(!0)}function md(n,t,e){return e.execute(n,t,n.element())}function gd(n){var e=ye(on.none());return nu({readState:function(){return e.get().map(function(n){return{numRows:n.numRows(),numColumns:n.numColumns()}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(n,t){e.set(on.some({numRows:nn(n),numColumns:nn(t)}))},getNumRows:function(){return e.get().map(function(n){return n.numRows()})},getNumColumns:function(){return e.get().map(function(n){return n.numColumns()})}})}function pd(i){return function(n,t,e,o){var r=i(n.element());return Em(r,n,t,e,o)}}function hd(n,t){var e=Aa(n,t);return pd(e)}function vd(n,t){var e=Aa(t,n);return pd(e)}function bd(r){return function(n,t,e,o){return Em(r,n,t,e,o)}}function yd(n){return!function(n){return n.offsetWidth<=0&&n.offsetHeight<=0}(n.dom())}function xd(n,t,e){var o=d(jt,t),r=zc(n,e);return function(t,n){return T(t,n).map(function(n){return Am({index:n,candidates:t})})}(C(r,yd),o)}function wd(n,t){return T(n,function(n){return jt(t,n)})}function Sd(e,n,o,t){return t(Math.floor(n/o),n%o).bind(function(n){var t=n.row()*o+n.column();return 0<=t&&t<e.length?on.some(e[t]):on.none()})}function Cd(r,n,i,u,a){return Sd(r,n,u,function(n,t){var e=n===i-1?r.length-n*u:u,o=is(t,a,0,e-1);return on.some({row:nn(n),column:nn(o)})})}function kd(i,n,u,a,c){return Sd(i,n,a,function(n,t){var e=is(n,c,0,u-1),o=e===u-1?i.length-e*a:a,r=us(t,0,o-1);return on.some({row:nn(e),column:nn(r)})})}function Od(t,e,n){ku(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Ed(r){return function(n,t,e,o){return xd(n,t,e.selector).bind(function(n){return r(n.candidates(),n.index(),o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}}function Td(n,t,e,o){return e.captureTab?on.some(!0):on.none()}function Bd(n,t,e,r){var i=function(n,t,e){var o=is(t,r,0,e.length-1);return o===n?on.none():function(n){return"button"===Ke(n)&&"disabled"===zo(n,"disabled")}(e[o])?i(n,o,e):on.from(e[o])};return xd(n,e,t).bind(function(n){var t=n.index(),e=n.candidates();return i(t,t,e)})}function Dd(t,e,o){return function(n,t){return t.focusManager.get(n).bind(function(n){return Ou(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})}function Ad(t,e){e.getInitial(t).orThunk(function(){return ku(t.element(),e.selector)}).each(function(n){e.focusManager.set(t,n)})}function _d(n,t,e){return Bd(n,e.selector,t,-1)}function Md(n,t,e){return Bd(n,e.selector,t,1)}function Fd(o){return function(n,t,e){return o(n,t,e).bind(function(){return e.executeOnMove?Dd(n,t,e):on.some(!0)})}}function Id(n,t,e,o){return e.onEscape(n,t)}function Rd(n,t,e){return on.from(n[t]).bind(function(n){return on.from(n[e]).map(function(n){return jm({rowIndex:t,columnIndex:e,cell:n})})})}function Vd(n,t,e,o){var r=n[t].length,i=is(e,o,0,r-1);return Rd(n,t,i)}function Nd(n,t,e,o){var r=is(e,o,0,n.length-1),i=n[r].length,u=us(t,0,i-1);return Rd(n,r,u)}function Hd(n,t,e,o){var r=n[t].length,i=us(e+o,0,r-1);return Rd(n,t,i)}function Pd(n,t,e,o){var r=us(e+o,0,n.length-1),i=n[r].length,u=us(t,0,i-1);return Rd(n,r,u)}function zd(t,e){e.previousSelector(t).orThunk(function(){var n=e.selectors;return ku(t.element(),n.cell)}).each(function(n){e.focusManager.set(t,n)})}function Ld(n,t){return function(r,e,i){var u=i.cycles?n:t;return Ou(e,i.selectors.row).bind(function(n){var t=zc(n,i.selectors.cell);return wd(t,e).bind(function(e){var o=zc(r,i.selectors.row);return wd(o,n).bind(function(n){var t=function(n,t){return S(n,function(n){return zc(n,t.selectors.cell)})}(o,i);return u(t,n,e).map(function(n){return n.cell()})})})})}}function jd(t,e,o){return o.focusManager.get(t).bind(function(n){return o.execute(t,e,n)})}function Ud(t,e){ku(t.element(),e.selector).each(function(n){e.focusManager.set(t,n)})}function Wd(n,t,e){return Bd(n,e.selector,t,-1)}function Gd(n,t,e){return Bd(n,e.selector,t,1)}function Xd(n,t,e,o){var r=n.getSystem().build(o);Ff(n,r,e)}function Yd(n,t,e,o){var r=dg(n);E(r,function(n){return jt(o.element(),n.element())}).each(bs)}function qd(t,n,e,o,r){var i=dg(t);return on.from(i[o]).map(function(n){return Yd(t,0,0,n),r.each(function(n){Xd(t,0,function(n,t){!function(n,t,e){Do(n,e).fold(function(){Hi(n,t)},function(n){Ao(n,t)})}(n,t,o)},n)}),n})}function Kd(n,t){return{key:n,value:{config:{},me:function(n,t){var e=co(t);return ya({fields:[ct("enabled")],name:n,active:{events:nn(e)}})}(n,t),configAsRaw:nn({}),initialConfig:{},state:Zi}}}function Jd(n,t){t.ignore||(wa(n.element()),t.onFocus(n))}function $d(n,t,e){var o=t.aria;o.update(n,o,e.get())}function Qd(t,n,e){n.toggleClass.each(function(n){e.get()?fr(t.element(),n):dr(t.element(),n)})}function Zd(n,t,e){bg(n,t,e,!e.get())}function nm(n,t,e){e.set(!0),Qd(n,t,e),$d(n,t,e)}function tm(n,t,e){e.set(!1),Qd(n,t,e),$d(n,t,e)}function em(n,t,e){bg(n,t,e,t.selected)}function om(){function n(n,t){t.stop(),io(n)}return[lo(ri(),n),lo(gi(),n),vo(Ur()),vo(Yr())]}function rm(n){return co(H([n.map(function(e){return Ni(function(n,t){e(n),t.stop()})}).toArray(),om()]))}function im(n){(Ca(n.element()).isNone()||vg.isFocused(n))&&(vg.isFocused(n)||vg.focus(n),ro(n,kg,{item:n}))}function um(n){ro(n,Og,{item:n})}function am(n,t){var e={};Cn(n,function(n,t){bn(n,function(n){e[n]=t})});var o=t,r=function(n){return kn(n,function(n,t){return{k:n,v:t}})}(t),i=P(r,function(n,t){return[t].concat(Ng(e,o,r,t))});return P(e,function(n){return Nn(i,n).getOr([n])})}function cm(n){return n.x()}function sm(n,t){return n.x()+n.width()/2-t.width()/2}function fm(n,t){return n.x()+n.width()-t.width()}function lm(n){return n.y()}function dm(n,t){return n.y()+n.height()-t.height()}function mm(n,t,e){return Ya(cm(n),dm(n,t),e.innerSoutheast(),Ka(),"layout-se")}function gm(n,t,e){return Ya(fm(n,t),dm(n,t),e.innerSouthwest(),Ja(),"layout-sw")}function pm(n,t,e){return Ya(cm(n),lm(n),e.innerNortheast(),$a(),"layout-ne")}function hm(n,t,e){return Ya(fm(n,t),lm(n),e.innerNorthwest(),Qa(),"layout-nw")}function vm(n){var t=function e(n){return n.uid!==undefined}(n)&&$(n,"uid")?n.uid:Yo("memento");return{get:function(n){return n.getSystem().getByUid(t).getOrDie()},getOpt:function(n){return n.getSystem().getByUid(t).toOption()},asSpec:function(){return N(N({},n),{uid:t})}}}function bm(n){return on.from(n()["temporary-placeholder"]).getOr("!not found!")}function ym(n,t){return on.from(t()[n]).getOrThunk(function(){return bm(t)})}var xm,wm=fd(At("cyclic",nn(!1))),Sm=fd(At("cyclic",nn(!0))),Cm=[St("execute",ld),St("useSpace",!1),St("useEnter",!0),St("useControlEnter",!1),St("useDown",!1)],km=sd(Cm,Zi.init,function(n,t,e,o){var r=e.useSpace&&!_l(n.element())?[32]:[],i=e.useEnter?[13]:[],u=e.useDown?[40]:[],a=r.concat(i).concat(u);return[Ul(Pl(a),md)].concat(e.useControlEnter?[Ul(zl([jl,Pl([13])]),md)]:[])},function(n,t,e,o){return e.useSpace&&!_l(n.element())?[Ul(Pl([32]),dd)]:[]},function(){return on.none()}),Om=/* */Object.freeze({flatgrid:gd,init:function(n){return n.state(n)}}),Em=function(t,e,n,o,r){return o.focusManager.get(e).bind(function(n){return t(e.element(),n,o,r)}).map(function(n){return o.focusManager.set(e,n),!0})},Tm=bd,Bm=bd,Dm=bd,Am=So(["index","candidates"],[]),_m=[ct("selector"),St("execute",ld),Ku("onEscape"),St("captureTab",!1),Xa()],Mm=Ed(function(n,t,e,o){return Cd(n,t,e,o,-1)}),Fm=Ed(function(n,t,e,o){return Cd(n,t,e,o,1)}),Im=Ed(function(n,t,e,o){return kd(n,t,e,o,-1)}),Rm=Ed(function(n,t,e,o){return kd(n,t,e,o,1)}),Vm=nn([Ul(Pl([37]),hd(Mm,Fm)),Ul(Pl([39]),vd(Mm,Fm)),Ul(Pl([38]),Tm(Im)),Ul(Pl([40]),Bm(Rm)),Ul(zl([Ll,Pl([9])]),Td),Ul(zl([cd,Pl([9])]),Td),Ul(Pl([27]),function(n,t,e,o){return e.onEscape(n,t)}),Ul(Pl([32].concat([13])),function(t,e,o,n){return function(n,t){return t.focusManager.get(n).bind(function(n){return Ou(n,t.selector)})}(t,o).bind(function(n){return o.execute(t,e,n)})})]),Nm=nn([Ul(Pl([32]),dd)]),Hm=sd(_m,gd,Vm,Nm,function(){return on.some(Od)}),Pm=[ct("selector"),St("getInitial",on.none),St("execute",ld),Ku("onEscape"),St("executeOnMove",!1),St("allowVertical",!0)],zm=nn([Ul(Pl([32]),dd)]),Lm=sd(Pm,Zi.init,function(n,t,e,o){var r=[37].concat(e.allowVertical?[38]:[]),i=[39].concat(e.allowVertical?[40]:[]);return[Ul(Pl(r),Fd(hd(_d,Md))),Ul(Pl(i),Fd(vd(_d,Md))),Ul(Pl([13]),Dd),Ul(Pl([32]),Dd),Ul(Pl([27]),Id)]},zm,function(){return on.some(Ad)}),jm=So(["rowIndex","columnIndex","cell"],[]),Um=[mt("selectors",[ct("row"),ct("cell")]),St("cycles",!0),St("previousSelector",on.none),St("execute",ld)],Wm=Ld(function(n,t,e){return Vd(n,t,e,-1)},function(n,t,e){return Hd(n,t,e,-1)}),Gm=Ld(function(n,t,e){return Vd(n,t,e,1)},function(n,t,e){return Hd(n,t,e,1)}),Xm=Ld(function(n,t,e){return Nd(n,e,t,-1)},function(n,t,e){return Pd(n,e,t,-1)}),Ym=Ld(function(n,t,e){return Nd(n,e,t,1)},function(n,t,e){return Pd(n,e,t,1)}),qm=nn([Ul(Pl([37]),hd(Wm,Gm)),Ul(Pl([39]),vd(Wm,Gm)),Ul(Pl([38]),Tm(Xm)),Ul(Pl([40]),Bm(Ym)),Ul(Pl([32].concat([13])),function(t,e,o){return Ca(t.element()).bind(function(n){return o.execute(t,e,n)})})]),Km=nn([Ul(Pl([32]),dd)]),Jm=sd(Um,Zi.init,qm,Km,function(){return on.some(zd)}),$m=[ct("selector"),St("execute",ld),St("moveOnTab",!1)],Qm=nn([Ul(Pl([38]),Dm(Wd)),Ul(Pl([40]),Dm(Gd)),Ul(zl([Ll,Pl([9])]),function(n,t,e){return e.moveOnTab?Dm(Wd)(n,t,e):on.none()}),Ul(zl([cd,Pl([9])]),function(n,t,e){return e.moveOnTab?Dm(Gd)(n,t,e):on.none()}),Ul(Pl([13]),jd),Ul(Pl([32]),jd)]),Zm=nn([Ul(Pl([32]),dd)]),ng=sd($m,Zi.init,Qm,Zm,function(){return on.some(Ud)}),tg=[Ku("onSpace"),Ku("onEnter"),Ku("onShiftEnter"),Ku("onLeft"),Ku("onRight"),Ku("onTab"),Ku("onShiftTab"),Ku("onUp"),Ku("onDown"),Ku("onEscape"),St("stopSpaceKeyup",!1),ht("focusIn")],eg=sd(tg,Zi.init,function(n,t,e){return[Ul(Pl([32]),e.onSpace),Ul(zl([cd,Pl([13])]),e.onEnter),Ul(zl([Ll,Pl([13])]),e.onShiftEnter),Ul(zl([Ll,Pl([9])]),e.onShiftTab),Ul(zl([cd,Pl([9])]),e.onTab),Ul(Pl([38]),e.onUp),Ul(Pl([40]),e.onDown),Ul(Pl([37]),e.onLeft),Ul(Pl([39]),e.onRight),Ul(Pl([32]),e.onSpace),Ul(Pl([27]),e.onEscape)]},function(n,t,e){return e.stopSpaceKeyup?[Ul(Pl([32]),dd)]:[]},function(n){return n.focusIn}),og=wm.schema(),rg=Sm.schema(),ig=Lm.schema(),ug=Hm.schema(),ag=Jm.schema(),cg=km.schema(),sg=ng.schema(),fg=eg.schema(),lg=xa({branchKey:"mode",branches:/* */Object.freeze({acyclic:og,cyclic:rg,flow:ig,flatgrid:ug,matrix:ag,execution:cg,menu:sg,special:fg}),name:"keying",active:{events:function(n,t){return n.handler.toEvents(n,t)}},apis:{focusIn:function(t,e,o){e.sendFocusIn(e).fold(function(){t.getSystem().triggerFocus(t.element(),t.element())},function(n){n(t,e,o)})},setGridSize:function(n,t,e,o,r){$(e,"setGridSize")?e.setGridSize(o,r):v.console.error("Layout does not support setGridSize")}},state:Om}),dg=function(n,t){return n.components()},mg=ya({fields:[],name:"replacing",apis:/* */Object.freeze({append:function(n,t,e,o){Xd(n,0,Hi,o)},prepend:function(n,t,e,o){Xd(n,0,Mo,o)},remove:Yd,replaceAt:qd,replaceBy:function(t,n,e,o,r){var i=dg(t);return T(i,o).bind(function(n){return qd(t,0,0,n,r)})},set:function(t,n,e,o){ka(function(){var n=S(o,t.getSystem().build);ps(t,n)},t.element())},contents:dg})}),gg=/* */Object.freeze({focus:Jd,blur:function(n,t){t.ignore||function(n){n.dom().blur()}(n.element())},isFocused:function(n){return function(n){var t=Co(n).dom();return n.dom()===t.activeElement}(n.element())}}),pg=/* */Object.freeze({exhibit:function(n,t){var e=t.ignore?{}:{attributes:{tabindex:"-1"}};return Zo(e)},events:function(e){return co([lo(ci(),function(n,t){Jd(n,e),t.stop()})].concat(e.stopMousedown?[lo(Yr(),function(n,t){t.event().prevent()})]:[]))}}),hg=[qu("onFocus"),St("stopMousedown",!1),St("ignore",!1)],vg=ya({fields:hg,name:"focusing",active:pg,apis:gg}),bg=function(n,t,e,o){(o?nm:tm)(n,t,e)},yg=/* */Object.freeze({onLoad:em,toggle:Zd,isOn:function(n,t,e){return e.get()},on:nm,off:tm,set:bg}),xg=/* */Object.freeze({exhibit:function(n,t,e){return Zo({})},events:function(n,t){var e=function(t,e,o){return Ni(function(n){o(n,t,e)})}(n,t,Zd),o=ga(n,t,em);return co(H([n.toggleOnExecute?[e]:[],[o]]))}}),wg=function(n,t,e){Po(n.element(),"aria-expanded",e)},Sg=[St("selected",!1),ht("toggleClass"),St("toggleOnExecute",!0),Ct("aria",{mode:"none"},it("mode",{pressed:[St("syncWithExpanded",!1),Qu("update",function(n,t,e){Po(n.element(),"aria-pressed",e),t.syncWithExpanded&&wg(n,t,e)})],checked:[Qu("update",function(n,t,e){Po(n.element(),"aria-checked",e)})],expanded:[Qu("update",wg)],selected:[Qu("update",function(n,t,e){Po(n.element(),"aria-selected",e)})],none:[Qu("update",Z)]}))],Cg=ya({fields:Sg,name:"toggling",active:xg,apis:yg,state:(xm=!1,{init:function(){var t=ye(xm);return{get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(xm)},readState:function(){return t.get()}}}})}),kg="alloy.item-hover",Og="alloy.item-focus",Eg=nn(kg),Tg=nn(Og),Bg=[ct("data"),ct("components"),ct("dom"),St("hasSubmenu",!1),ht("toggling"),nl("itemBehaviours",[Cg,vg,lg,Zf]),St("ignoreFocus",!1),St("domModification",{}),Qu("builder",function(n){return{dom:n.dom,domModification:N(N({},n.domModification),{attributes:N(N(N({role:n.toggling.isSome()?"menuitemcheckbox":"menuitem"},n.domModification.attributes),{"aria-haspopup":n.hasSubmenu}),n.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:tl(n.itemBehaviours,[n.toggling.fold(Cg.revoke,function(n){return Cg.config(N({aria:{mode:"checked"}},n))}),vg.config({ignore:n.ignoreFocus,stopMousedown:n.ignoreFocus,onFocus:function(n){um(n)}}),lg.config({mode:"execution"}),Zf.config({store:{mode:"memory",initialValue:n.data}}),Kd("item-type-events",g(om(),[lo($r(),im),lo(mi(),vg.focus)]))]),components:n.components,eventOrder:n.eventOrder}}),St("eventOrder",{})],Dg=[ct("dom"),ct("components"),Qu("builder",function(n){return{dom:n.dom,components:n.components,events:co([function(n){return lo(n,function(n,t){t.stop()})}(mi())])}})],Ag=nn([xl({name:"widget",overrides:function(t){return{behaviours:ba([Zf.config({store:{mode:"manual",getValue:function(n){return t.data},setValue:function(){}}})])}}})]),_g=[ct("uid"),ct("data"),ct("components"),ct("dom"),St("autofocus",!1),St("ignoreFocus",!1),nl("widgetBehaviours",[Zf,vg,lg]),St("domModification",{}),tf(Ag()),Qu("builder",function(e){function o(n){return qs(n,e,"widget").map(function(n){return lg.focusIn(n),n})}function n(n,t){return _l(t.event().target())||e.autofocus&&t.setSource(n.element()),on.none()}var t=Xs(0,e,Ag()),r=Ys("item-widget",e,t.internals());return{dom:e.dom,components:r,domModification:e.domModification,events:co([Ni(function(n,t){o(n).each(function(n){t.stop()})}),lo($r(),im),lo(mi(),function(n,t){e.autofocus?o(n):vg.focus(n)})]),behaviours:tl(e.widgetBehaviours,[Zf.config({store:{mode:"memory",initialValue:e.data}}),vg.config({ignore:e.ignoreFocus,onFocus:function(n){um(n)}}),lg.config({mode:"special",focusIn:e.autofocus?function(n){o(n)}:cc(),onLeft:n,onRight:n,onEscape:function(n,t){return vg.isFocused(n)||e.autofocus?(e.autofocus&&t.setSource(n.element()),on.none()):(vg.focus(n),on.some(!0))}})])}})],Mg=it("type",{widget:_g,item:Bg,separator:Dg}),Fg=nn([Cl({factory:{sketch:function(n){var t=ot("menu.spec item",Mg,n);return t.builder(t)}},name:"items",unit:"item",defaults:function(n,t){return t.hasOwnProperty("uid")?t:N(N({},t),{uid:Yo("item")})},overrides:function(n,t){return{type:t.type,ignoreFocus:n.fakeFocus,domModification:{classes:[n.markers.item]}}}})]),Ig=nn([ct("value"),ct("items"),ct("dom"),ct("components"),St("eventOrder",{}),Fs("menuBehaviours",[ad,Zf,Zl,lg]),Ct("movement",{mode:"menu",moveOnTab:!0},it("mode",{grid:[Xa(),Qu("config",function(n,t){return{mode:"flatgrid",selector:"."+n.markers.item,initSize:{numColumns:t.initSize.numColumns,numRows:t.initSize.numRows},focusManager:n.focusManager}})],matrix:[Qu("config",function(n,t){return{mode:"matrix",selectors:{row:t.rowSelector,cell:"."+n.markers.item},focusManager:n.focusManager}}),ct("rowSelector")],menu:[St("moveOnTab",!0),Qu("config",function(n,t){return{mode:"menu",selector:"."+n.markers.item,moveOnTab:t.moveOnTab,focusManager:n.focusManager}})]})),st("markers",Wa()),St("fakeFocus",!1),St("focusManager",Gl()),qu("onHighlight")]),Rg=nn("alloy.menu-focus"),Vg=Al({name:"Menu",configFields:Ig(),partFields:Fg(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,markers:n.markers,behaviours:Rs(n.menuBehaviours,[ad.config({highlightClass:n.markers.selectedItem,itemClass:n.markers.item,onHighlight:n.onHighlight}),Zf.config({store:{mode:"memory",initialValue:n.value}}),Zl.config({find:on.some}),lg.config(n.movement.config(n,n.movement))]),events:co([lo(Tg(),function(t,e){var n=e.event();t.getSystem().getByDom(n.target()).each(function(n){ad.highlight(t,n),e.stop(),ro(t,Rg(),{menu:t,item:n})})}),lo(Eg(),function(n,t){var e=t.event().item();ad.highlight(n,e)})]),components:t,eventOrder:n.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Ng=function(e,o,r,n){return Nn(r,n).bind(function(n){return Nn(e,n).bind(function(n){var t=Ng(e,o,r,n);return on.some([n].concat(t))})}).getOr([])},Hg=function(n){return"prepared"===n.type?on.some(n.menu):on.none()},Pg={init:function(){function r(n,e,o){return f(n).bind(function(t){return function(e){return I(i.get(),function(n,t){return n===e})}(n).bind(function(n){return e(n).map(function(n){return{triggeredMenu:t,triggeringItem:n,triggeringPath:o}})})})}var i=ye({}),u=ye({}),a=ye({}),c=ye(on.none()),s=ye({}),f=function(n){return t(n).bind(Hg)},t=function(n){return Nn(u.get(),n)},e=function(n){return Nn(i.get(),n)};return{setMenuBuilt:function(n,t){var e;u.set(N(N({},u.get()),((e={})[n]={type:"prepared",menu:t},e)))},setContents:function(n,t,e,o){c.set(on.some(n)),i.set(e),u.set(t),s.set(o);var r=am(o,e);a.set(r)},expand:function(e){return Nn(i.get(),e).map(function(n){var t=Nn(a.get(),e).getOr([]);return[n].concat(t)})},refresh:function(n){return Nn(a.get(),n)},collapse:function(n){return Nn(a.get(),n).bind(function(n){return 1<n.length?on.some(n.slice(1)):on.none()})},lookupMenu:t,lookupItem:e,otherMenus:function(n){var t=s.get();return _(wn(t),n)},getPrimary:function(){return c.get().bind(f)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(on.none())},isClear:function(){return c.get().isNone()},getTriggeringPath:function(n,o){var t=C(e(n).toArray(),function(n){return f(n).isSome()});return Nn(a.get(),n).bind(function(n){var e=A(t.concat(n));return function(n){for(var t=[],e=0;e<n.length;e++){var o=n[e];if(!o.isSome())return on.none();t.push(o.getOrDie())}return on.some(t)}(B(e,function(n,t){return r(n,o,e.slice(0,t+1)).fold(function(){return c.get().is(n)?[]:[on.none()]},function(n){return[on.some(n)]})}))})}}},extractPreparedMenu:Hg},zg=nn("collapse-item"),Lg=Dl({name:"TieredMenu",configFields:[$u("onExecute"),$u("onEscape"),Ju("onOpenMenu"),Ju("onOpenSubmenu"),Ju("onRepositionMenu"),qu("onCollapseMenu"),St("highlightImmediately",!0),mt("data",[ct("primary"),ct("menus"),ct("expansions")]),St("fakeFocus",!1),qu("onHighlight"),qu("onHover"),Gu(),ct("dom"),St("navigateOnHover",!0),St("stayInDom",!1),Fs("tmenuBehaviours",[lg,ad,Zl,mg]),St("eventOrder",{})],apis:{collapseMenu:function(n,t){n.collapseMenu(t)},highlightPrimary:function(n,t){n.highlightPrimary(t)},repositionMenus:function(n,t){n.repositionMenus(t)}},factory:function(a,n){function e(n){var t=function(o,r,n){return P(n,function(n,t){function e(){return Vg.sketch(N(N({dom:n.dom},n),{value:t,items:n.items,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:a.fakeFocus?Xl():Gl()}))}return t===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})}(n,a.data.primary,a.data.menus),e=o();return g.setContents(a.data.primary,t,a.data.expansions,e),g.getPrimary()}function c(n){return Zf.getValue(n).value}function u(t,n){ad.highlight(t,n),ad.getHighlighted(n).orThunk(function(){return ad.getFirst(n)}).each(function(n){uo(t,n.element(),mi())})}function s(t,n){return Bu(S(n,function(n){return t.lookupMenu(n).bind(function(n){return"prepared"===n.type?on.some(n.menu):on.none()})}))}function f(t,n,e){var o=s(n,n.otherMenus(e));bn(o,function(n){pr(n.element(),[a.markers.backgroundMenu]),a.stayInDom||mg.remove(t,n)})}function l(n,o){var t=function(o){return r.get().getOrThunk(function(){var e={},n=zc(o.element(),"."+a.markers.item),t=C(n,function(n){return"true"===zo(n,"aria-haspopup")});return bn(t,function(n){o.getSystem().getByDom(n).each(function(n){var t=c(n);e[t]=n})}),r.set(on.some(e)),e})}(n);Cn(t,function(n,t){var e=vn(o,t);Po(n.element(),"aria-expanded",e)})}function d(o,r,i){return on.from(i[0]).bind(function(n){return r.lookupMenu(n).bind(function(n){if("notbuilt"===n.type)return on.none();var t=n.menu,e=s(r,i.slice(1));return bn(e,function(n){fr(n.element(),a.markers.backgroundMenu)}),$e(t.element())||mg.append(o,au(t)),pr(t.element(),[a.markers.backgroundMenu]),u(o,t),f(o,r,i),on.some(t)})})}var m,t,r=ye(on.none()),g=Pg.init(),o=function(n){return P(a.data.menus,function(n,t){return B(n.items,function(n){return"separator"===n.type?[]:[n.data.value]})})};(t=m=m||{})[t.HighlightSubmenu=0]="HighlightSubmenu",t[t.HighlightParent=1]="HighlightParent";function i(r,i,u){void 0===u&&(u=m.HighlightSubmenu);var n=c(i);return g.expand(n).bind(function(o){return l(r,o),on.from(o[0]).bind(function(e){return g.lookupMenu(e).bind(function(n){var t=function(n,t,e){if("notbuilt"!==e.type)return e.menu;var o=n.getSystem().build(e.nbMenu());return g.setMenuBuilt(t,o),o}(r,e,n);return $e(t.element())||mg.append(r,au(t)),a.onOpenSubmenu(r,i,t,A(o)),u===m.HighlightSubmenu?(ad.highlightFirst(t),d(r,g,o)):(ad.dehighlightAll(t),on.some(i))})})})}function p(t,e){var n=c(e);return g.collapse(n).bind(function(n){return l(t,n),d(t,g,n).map(function(n){return a.onCollapseMenu(t,e,n),n})})}function h(e){return function(t,n){return Ou(n.getSource(),"."+a.markers.item).bind(function(n){return t.getSystem().getByDom(n).toOption().bind(function(n){return e(t,n).map(function(){return!0})})})}}function v(n){return ad.getHighlighted(n).bind(ad.getHighlighted)}var b=co([lo(Rg(),function(e,o){var n=o.event().item();g.lookupItem(c(n)).each(function(){var n=o.event().menu();ad.highlight(e,n);var t=c(o.event().item());g.refresh(t).each(function(n){return f(e,g,n)})})}),Ni(function(t,n){var e=n.event().target();t.getSystem().getByDom(e).each(function(n){0===c(n).indexOf("collapse-item")&&p(t,n),i(t,n,m.HighlightSubmenu).fold(function(){a.onExecute(t,n)},function(){})})}),Ii(function(t,n){e(t).each(function(n){mg.append(t,au(n)),a.onOpenMenu(t,n),a.highlightImmediately&&u(t,n)})})].concat(a.navigateOnHover?[lo(Eg(),function(n,t){var e=t.event().item();!function(t,n){var e=c(n);g.refresh(e).bind(function(n){return l(t,n),d(t,g,n)})}(n,e),i(n,e,m.HighlightParent),a.onHover(n,e)})]:[])),y={collapseMenu:function(t){v(t).each(function(n){p(t,n)})},highlightPrimary:function(t){g.getPrimary().each(function(n){u(t,n)})},repositionMenus:function(o){g.getPrimary().bind(function(t){return v(o).bind(function(n){var t=c(n),e=R(g.getMenus()),o=Bu(S(e,Pg.extractPreparedMenu));return g.getTriggeringPath(t,function(n){return function(n,t,e){return Du(t,function(n){if(!n.getSystem().isConnected())return on.none();var t=ad.getCandidates(n);return E(t,function(n){return c(n)===e})})}(0,o,n)})}).map(function(n){return{primary:t,triggeringPath:n}})}).fold(function(){(function(n){return on.from(n.components()[0]).filter(function(n){return"menu"===zo(n.element(),"role")})})(o).each(function(n){a.onRepositionMenu(o,n,[])})},function(n){var t=n.primary,e=n.triggeringPath;a.onRepositionMenu(o,t,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:Rs(a.tmenuBehaviours,[lg.config({mode:"special",onRight:h(function(n,t){return _l(t.element())?on.none():i(n,t,m.HighlightSubmenu)}),onLeft:h(function(n,t){return _l(t.element())?on.none():p(n,t)}),onEscape:h(function(n,t){return p(n,t).orThunk(function(){return a.onEscape(n,t).map(function(){return n})})}),focusIn:function(t,n){g.getPrimary().each(function(n){uo(t,n.element(),mi())})}}),ad.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),Zl.config({find:function(n){return ad.getHighlighted(n)}}),mg.config({})]),eventOrder:a.eventOrder,apis:y,events:b}},extraApis:{tieredData:function(n,t,e){return{primary:n,menus:t,expansions:e}},singleData:function(n,t){return{primary:n,menus:q(n,t),expansions:{}}},collapseItem:function(n){return{value:Xo(zg()),meta:{text:n}}}}}),jg=Dl({name:"InlineView",configFields:[ct("lazySink"),qu("onShow"),qu("onHide"),xt("onEscape"),Fs("inlineBehaviours",[zf,Zf,lc]),wt("fireDismissalEventInstead",[St("event",Oi())]),wt("fireRepositionEventInstead",[St("event",Ei())]),St("getRelated",on.none),St("eventOrder",on.none)],factory:function(i,n){function t(e){zf.isOpen(e)&&Zf.getValue(e).each(function(n){switch(n.mode){case"menu":zf.getState(e).each(function(n){Lg.repositionMenus(n)});break;case"position":var t=i.lazySink(e).getOrDie();Af.positionWithinBounds(t,n.anchor,e,n.getBounds())}})}var o=function(n,t,e,o){r(n,t,e,function(){return o.map(function(n){return xu(n)})})},r=function(n,t,e,o){var r=i.lazySink(n).getOrDie();zf.openWhileCloaked(n,e,function(){return Af.positionWithinBounds(r,t,n,o())}),Zf.setValue(n,on.some({mode:"position",anchor:t,getBounds:o}))},u=function(n,t,e,o){var r=function(n,t,r,e,i){function u(){return n.lazySink(t)}function a(n){return function(n){return 2===n.length}(n)?o:{}}var o="horizontal"===e.type?{layouts:{onLtr:function(){return da()},onRtl:function(){return ma()}}}:{};return Lg.sketch({dom:{tag:"div"},data:e.data,markers:e.menu.markers,onEscape:function(){return zf.close(t),n.onEscape.map(function(n){return n(t)}),on.some(!0)},onExecute:function(){return on.some(!0)},onOpenMenu:function(n,t){Af.positionWithinBounds(u().getOrDie(),r,t,i())},onOpenSubmenu:function(n,t,e,o){var r=u().getOrDie();Af.position(r,N({anchor:"submenu",item:t},a(o)),e)},onRepositionMenu:function(n,t,e){var o=u().getOrDie();Af.positionWithinBounds(o,r,t,i()),bn(e,function(n){var t=a(n.triggeringPath);Af.position(o,N({anchor:"submenu",item:n.triggeringItem},t),n.triggeredMenu)})}})}(i,n,t,e,o);zf.open(n,r),Zf.setValue(n,on.some({mode:"menu",menu:r}))},e={setContent:function(n,t){zf.setContent(n,t)},showAt:function(n,t,e){o(n,t,e,on.none())},showWithin:o,showWithinBounds:r,showMenuAt:function(n,t,e){u(n,t,e,function(){return on.none()})},showMenuWithinBounds:u,hide:function(n){Zf.setValue(n,on.none()),zf.close(n)},getContent:function(n){return zf.getState(n)},reposition:t,isOpen:zf.isOpen};return{uid:i.uid,dom:i.dom,behaviours:Rs(i.inlineBehaviours,[zf.config({isPartOf:function(n,t,e){return Lu(t,e)||function(n,t){return i.getRelated(n).exists(function(n){return Lu(n,t)})}(n,e)},getAttachPoint:function(n){return i.lazySink(n).getOrDie()},onOpen:function(n){i.onShow(n)},onClose:function(n){i.onHide(n)}}),Zf.config({store:{mode:"memory",initialValue:on.none()}}),lc.config({channels:N(N({},Es(N({isExtraPart:nn(!1)},i.fireDismissalEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})))),Ts(N(N({isExtraPart:nn(!1)},i.fireRepositionEventInstead.map(function(n){return{fireEventInstead:{event:n.event}}}).getOr({})),{doReposition:t})))})]),eventOrder:i.eventOrder,apis:e}},apis:{showAt:function(n,t,e,o){n.showAt(t,e,o)},showWithin:function(n,t,e,o,r){n.showWithin(t,e,o,r)},showWithinBounds:function(n,t,e,o,r){n.showWithinBounds(t,e,o,r)},showMenuAt:function(n,t,e,o){n.showMenuAt(t,e,o)},showMenuWithinBounds:function(n,t,e,o,r){n.showMenuWithinBounds(t,e,o,r)},hide:function(n,t){n.hide(t)},isOpen:function(n,t){return n.isOpen(t)},getContent:function(n,t){return n.getContent(t)},setContent:function(n,t,e){n.setContent(t,e)},reposition:function(n,t){n.reposition(t)}}}),Ug=function(n,t,e){return Ya(sm(n,t),lm(n),e.innerNorth(),nc(),"layout-n")},Wg=function(n,t,e){return Ya(sm(n,t),dm(n,t),e.innerSouth(),Za(),"layout-s")},Gg=Dl({name:"Button",factory:function(n){function e(t){return Nn(n.dom,"attributes").bind(function(n){return Nn(n,t)})}var t=rm(n.action),o=n.dom.tag;return{uid:n.uid,dom:n.dom,components:n.components,events:t,behaviours:tl(n.buttonBehaviours,[vg.config({}),lg.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==o)return{role:e("role").getOr("button")};var n=e("type").getOr("button"),t=e("role").map(function(n){return{role:n}}).getOr({});return N({type:n},t)}()},eventOrder:n.eventOrder}},configFields:[St("uid",undefined),ct("dom"),St("components",[]),nl("buttonBehaviours",[vg,lg]),ht("action"),ht("role"),St("eventOrder",{})]}),Xg={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},Yg=Dl({name:"Notification",factory:function(t){function e(n){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+n+"%"}}}}function o(n){return{dom:{tag:"div",classes:["tox-text"],innerHtml:n+"%"}}}var r=vm({dom:{tag:"p",innerHtml:t.translationProvider(t.text)},behaviours:ba([mg.config({})])}),i=vm({dom:{tag:"div",classes:t.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(0)]},o(0)],behaviours:ba([mg.config({})])}),n={updateProgress:function(n,t){n.getSystem().isConnected()&&i.getOpt(n).each(function(n){mg.set(n,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[e(t)]},o(t)])})},updateText:function(n,t){if(n.getSystem().isConnected()){var e=r.get(n);mg.set(e,[Ir(t)])}}},u=H([t.icon.toArray(),t.level.toArray(),t.level.bind(function(n){return on.from(Xg[n])}).toArray()]);return{uid:t.uid,dom:{tag:"div",attributes:{role:"alert"},classes:t.level.map(function(n){return["tox-notification","tox-notification--in","tox-notification--"+n]}).getOr(["tox-notification","tox-notification--in"])},components:[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:function(n,t){return Du(n,function(n){return on.from(t()[n])}).getOrThunk(function(){return bm(t)})}(u,t.iconProvider)}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[r.asSpec()],behaviours:ba([mg.config({})])}].concat(t.progress?[i.asSpec()]:[]).concat(Gg.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:ym("close",t.iconProvider),attributes:{"aria-label":t.translationProvider("Close")}}}],action:function(n){t.onAction(n)}})),apis:n}},configFields:[ht("level"),ct("progress"),ct("icon"),ct("onAction"),ct("text"),ct("iconProvider"),ct("translationProvider")],apis:{updateProgress:function(n,t,e){n.updateProgress(t,e)},updateText:function(n,t,e){n.updateText(t,e)}}}),qg=tinymce.util.Tools.resolve("tinymce.util.Delay");function Kg(n,u,o){var a=u.backstage;return{open:function(n,t){function e(){t(),jg.hide(i)}var r=uu(Yg.sketch({text:n.text,level:vn(["success","error","warning","warn","info"],n.type)?n.type:undefined,progress:!0===n.progressBar,icon:on.from(n.icon),onAction:e,iconProvider:a.shared.providers.icons,translationProvider:a.shared.providers.translate})),i=uu(jg.sketch({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:u.backstage.shared.getSink,fireDismissalEventInstead:{}}));return o.add(i),0<n.timeout&&qg.setTimeout(function(){e()},n.timeout),{close:e,moveTo:function(n,t){jg.showAt(i,{anchor:"makeshift",x:n,y:t},au(r))},moveRel:function(n,t){if("banner"!==t){var e=function(n){switch(n){case"bc-bc":return Wg;case"tc-tc":return Ug;case"tc-bc":return oc;case"bc-tc":default:return rc}}(t),o={anchor:"node",root:zr(),node:on.some(we.fromDom(n)),layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}};jg.showAt(i,o,au(r))}else jg.showAt(i,u.backstage.shared.anchors.banner(),au(r))},text:function(n){Yg.updateText(r,n)},settings:n,getEl:function(){return r.element().dom()},progressBar:{value:function(n){Yg.updateProgress(r,n)}}}},close:function(n){n.close()},reposition:function(n){!function(n){bn(n,function(n){return n.moveTo(0,0)})}(n),function(e){0<e.length&&(yn(e).each(function(n){return n.moveRel(null,"banner")}),bn(e,function(n,t){0<t&&n.moveRel(e[t-1].getEl(),"bc-tc")}))}(n)},getArgs:function(n){return n.settings}}}function Jg(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null!==r&&v.clearTimeout(r),r=v.setTimeout(function(){e.apply(null,n),r=null},o)}}}function $g(n,t,e,o,r){var i=new Ap(t,r||n.getRoot());return Fp(n,t,on.some(e),o,i.prev,on.none())}function Qg(t,e){return Ip(we.fromDom(t.selection.getNode())).getOrThunk(function(){var n=we.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',t.getDoc());return Hi(n,we.fromDom(e.extractContents())),e.insertNode(n.dom()),Eo(n).each(function(n){return n.dom().normalize()}),Ic(n).map(function(n){t.selection.setCursorLocation(n.dom(),function(n){return"img"===Ke(n)?1:Ac(n).fold(function(){return Bo(n).length},function(n){return n.length})}(n))}),n})}function Zg(n,t){return n.toString().substring(t.length).replace(/\u00A0/g," ").replace(/\uFEFF/g,"")}function np(n,u,a,c){return void 0===c&&(c=0),function(n){return n.collapsed&&3===n.startContainer.nodeType}(u)?$g(n,u.startContainer,u.startOffset,function(e,o,r,n){var i=n.getOr(r.length);return function(n,t,e,o){var r;for(r=t-1;0<=r;r--){var i=n.charAt(r);if(Rp.test(i))return on.none();if(i===e)break}return-1===r||t-r<o?on.none():on.some(n.substring(r+1,t))}(r,i,a,1).fold(function(){return r.match(Rp)?e.abort():e.kontinue()},function(n){var t=u.cloneRange();return t.setStart(o,i-n.length-1),t.setEnd(u.endContainer,u.endOffset),r.length<c?e.abort():e.finish({text:Zg(t,a),range:t,triggerChar:a})})}).fold(on.none,on.none,on.some):on.none()}function tp(e,n,o,t){return void 0===t&&(t=0),Ip(we.fromDom(n.startContainer)).fold(function(){return np(e,n,o,t)},function(n){var t=e.createRng();return t.selectNode(n.dom()),on.some({range:t,text:Zg(t,o),triggerChar:o})})}function ep(n,t){return{element:n,offset:t}}function op(t,e){var n=e(),o=t.selection.getRng();return function(t,e,n){return Du(n.triggerChars,function(n){return tp(t,e,n)})}(t.dom,o,n).bind(function(n){return zp(t,e,n)})}function rp(n){var t=n.ui.registry.getAll().popups,e=P(t,function(n){return function(n){return tt("Autocompleter",Up,n)}(n).fold(function(n){throw new Error(le(n))},function(n){return n})}),o=function(n){var t={};return bn(n,function(n){t[n]={}}),wn(t)}(On(e,function(n){return n.ch})),r=R(e);return{dataset:e,triggerChars:o,lookupByChar:function(t){return C(r,function(n){return n.ch===t})}}}function ip(n,o,t){var r=zc(n.element(),"."+t);if(0<r.length){var e=T(r,function(n){var t=n.dom().getBoundingClientRect().top,e=r[0].dom().getBoundingClientRect().top;return Math.abs(t-e)>o}).getOr(r.length);return on.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return on.none()}function up(n,t){return ba([Kd(n,t)])}function ap(n,t,e){n.getSystem().broadcastOn([Zp],{})}function cp(n){var t=we.fromHtml(n),e=Bo(t),o=function(n){var t=n.dom().attributes!==undefined?n.dom().attributes:[];return O(t,function(n,t){var e;return"class"===t.name?n:N(N({},n),((e={})[t.name]=t.value,e))},{})}(t),r=function(n){return Array.prototype.slice.call(n.dom().classList,0)}(t),i=0===e.length?{}:{innerHtml:Vo(t)};return N({tag:Ke(t),classes:r,attributes:o},i)}function sp(n){return Nn(ch,n).getOr(ih)}function fp(n){return{dom:{tag:"div",classes:[fh],innerHtml:n}}}function lp(n){return{dom:{tag:"div",classes:[lh]},components:[Ir(rh.translate(n))]}}function dp(n,t){return{dom:{tag:"div",classes:[lh]},components:[{dom:{tag:n.tag,attributes:{style:n.styleAttr}},components:[Ir(rh.translate(t))]}]}}function mp(n){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:ph(n)}}}function gp(n){return{dom:{tag:"div",classes:[fh,"tox-collection__item-checkmark"],innerHtml:ym("checkmark",n)}}}function pp(n,t,e,o,r){var i=e?n.checkMark.orThunk(function(){return t.or(on.some("")).map(fp)}):on.none(),u=n.ariaLabel.map(function(n){return{attributes:{title:rh.translate(n)}}}).getOr({});return{dom:An({tag:"div",classes:[ih,uh].concat(r?["tox-collection__item-icon-rtl"]:[])},u),optComponents:[i,n.htmlContent.fold(function(){return n.textContent.map(o)},function(n){return on.some(function(n){return{dom:{tag:"div",classes:[lh],innerHtml:n}}}(n))}),n.shortcutContent.map(mp),n.caret]}}function hp(n,t,e,o){void 0===o&&(o=on.none());var r=rh.isRtl()&&n.iconContent.exists(function(n){return vn(vh,n)}),i=n.iconContent.map(function(n){return rh.isRtl()&&vn(hh,n)?n+"-rtl":n}).map(function(n){return function(n,t,e){return on.from(t()[n]).or(e).getOrThunk(function(){return bm(t)})}(n,t.icons,o)}),u=on.from(n.meta).fold(function(){return lp},function(n){return En(n,"style")?d(dp,n.style):lp});return"color"===n.presets?function(n,t,e,o){var r,i,u;return{dom:(r=ah,i=e.getOr(""),u=n.map(function(n){return' title="'+o.translate(n)+'"'}).getOr(""),cp("custom"===t?'<button class="'+r+' tox-swatches__picker-btn"'+u+">"+i+"</button>":"remove"===t?'<div class="'+r+' tox-swatch--remove"'+u+">"+i+"</div>":'<div class="'+r+'" style="background-color: '+t+'" data-mce-color="'+t+'"'+u+"></div>")),optComponents:[]}}(n.ariaLabel,n.value,i,t):pp(n,i,e,u,r)}function vp(n,t,e){t.disabled&&yh(n,t)}function bp(n,t){return!0===t.useNative&&vn(bh,Ke(n.element()))}function yp(n){Po(n.element(),"disabled","disabled")}function xp(n){jo(n.element(),"disabled")}function wp(n){Po(n.element(),"aria-disabled","true")}function Sp(n){Po(n.element(),"aria-disabled","false")}function Cp(t,n,e){n.disableClass.each(function(n){dr(t.element(),n)}),(bp(t,n)?xp:Sp)(t),n.onEnabled(t)}function kp(n,t){return bp(n,t)?function(n){return Lo(n.element(),"disabled")}(n):function(n){return"true"===zo(n.element(),"aria-disabled")}(n)}function Op(n,t){var e=n.getApi(t);return function(n){n(e)}}function Ep(e,o){return Ii(function(n){Op(e,n)(function(n){var t=e.onSetup(n);null!==t&&t!==undefined&&o.set(t)})})}function Tp(t,e){return Ri(function(n){return Op(t,n)(e.get())})}var Bp,Dp,Ap=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),_p=Tn([{aborted:[]},{edge:["element"]},{success:["info"]}]),Mp=Tn([{abort:[]},{kontinue:[]},{finish:["info"]}]),Fp=function(t,e,n,o,r,i){function u(){return i.fold(_p.aborted,_p.edge)}function a(){var n=r();return n?Fp(t,n,on.none(),o,r,on.some(e)):u()}if(function(n,t){return n.isBlock(t)||vn(["BR","IMG","HR","INPUT"],t.nodeName)||"false"===n.getContentEditable(t)}(t,e))return u();if(function(n){return n.nodeType===v.Node.TEXT_NODE}(e)){var c=e.textContent;return o(Mp,e,c,n).fold(_p.aborted,function(){return a()},_p.success)}return a()},Ip=function(n){return Ou(n,"[data-mce-autocompleter]")},Rp=/[\u00a0 \t\r\n]/,Vp=function(e,n){n.on("keypress compositionend",e.onKeypress.throttle),n.on("remove",e.onKeypress.cancel);function o(n,t){ro(n,ni(),{raw:t})}n.on("keydown",function(t){function n(){return e.getView().bind(ad.getHighlighted)}8===t.which&&e.onKeypress.throttle(t),e.isActive()&&(27===t.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===t.which?(n().each(io),t.preventDefault()):40===t.which?(n().fold(function(){e.getView().each(ad.highlightFirst)},function(n){o(n,t)}),t.preventDefault(),t.stopImmediatePropagation()):37!==t.which&&38!==t.which&&39!==t.which||n().each(function(n){o(n,t),t.preventDefault(),t.stopImmediatePropagation()}):13!==t.which&&38!==t.which&&40!==t.which||e.cancelIfNecessary())}),n.on("NodeChange",function(n){e.isActive()&&!e.isProcessingAction()&&Ip(we.fromDom(n.element)).isNone()&&e.cancelIfNecessary()})},Np=tinymce.util.Tools.resolve("tinymce.util.Promise"),Hp=function(n){if(function(n){return n.nodeType===v.Node.TEXT_NODE}(n))return ep(n,n.data.length);var t=n.childNodes;return 0<t.length?Hp(t[t.length-1]):ep(n,t.length)},Pp=function(n,t){var e=n.childNodes;return 0<e.length&&t<e.length?Pp(e[t],0):0<e.length&&function(n){return n.nodeType===v.Node.ELEMENT_NODE}(n)&&e.length===t?Hp(e[e.length-1]):ep(n,t)},zp=function(t,n,e,o){void 0===o&&(o={});var r=n(),i=t.selection.getRng().startContainer.nodeValue,u=C(r.lookupByChar(e.triggerChar),function(n){return e.text.length>=n.minChars&&n.matches.getOrThunk(function(){return function(e){function o(n,t,e,o){var r=o.getOr(e.length);return 0===r?n.kontinue():n.finish(/\s/.test(e.charAt(r-1)))}return function(n){var t=Pp(n.startContainer,n.startOffset);return $g(e,t.element,t.offset,o).fold(nn(!0),nn(!0),l)}}(t.dom)})(e.range,i,e.text)});if(0===u.length)return on.none();var a=Np.all(S(u,function(t){return t.fetch(e.text,t.maxResults,o).then(function(n){return{matchText:e.text,items:n,columns:t.columns,onAction:t.onAction}})}));return on.some({lookupData:a,context:e})},Lp=re([ft("type"),yt("text")]),jp=re([At("type",function(){return"autocompleteitem"}),At("active",function(){return!1}),At("disabled",function(){return!1}),St("meta",{}),ft("value"),yt("text"),yt("icon")]),Up=re([ft("type"),ft("ch"),kt("minChars",1),St("columns",1),kt("maxResults",10),xt("matches"),dt("fetch"),dt("onAction")]),Wp=[Tt("disabled",!1),yt("text"),yt("shortcut"),ce("value","value",In(function(){return Xo("menuitem-value")}),de()),St("meta",{})],Gp=re([ft("type"),Bt("onSetup",function(){return Z}),Bt("onAction",Z),yt("icon")].concat(Wp)),Xp=re([ft("type"),dt("getSubmenuItems"),Bt("onSetup",function(){return Z}),yt("icon")].concat(Wp)),Yp=re([ft("type"),Tt("active",!1),Bt("onSetup",function(){return Z}),dt("onAction")].concat(Wp)),qp=re([ft("type"),Tt("active",!1),yt("icon")].concat(Wp)),Kp=re([ft("type"),lt("fancytype",["inserttable","colorswatch"]),Bt("onAction",Z)]),Jp=function(n){return up(Xo("unnamed-events"),n)},$p=[ct("lazySink"),ct("tooltipDom"),St("exclusive",!0),St("tooltipComponents",[]),St("delay",300),Et("mode","normal",["normal","follow-highlight"]),St("anchor",function(n){return{anchor:"hotspot",hotspot:n,layouts:{onLtr:nn([rc,oc,ua,ca,aa,sa]),onRtl:nn([rc,oc,ua,ca,aa,sa])}}}),qu("onHide"),qu("onShow")],Qp=/* */Object.freeze({init:function(){function e(){o.get().each(function(n){v.clearTimeout(n)})}var o=ye(on.none()),t=ye(on.none()),n=nn("not-implemented");return nu({getTooltip:function(){return t.get()},isShowing:function(){return t.get().isSome()},setTooltip:function(n){t.set(on.some(n))},clearTooltip:function(){t.set(on.none())},clearTimer:e,resetTimer:function(n,t){e(),o.set(on.some(v.setTimeout(function(){n()},t)))},readState:n})}}),Zp=Xo("tooltip.exclusive"),nh=Xo("tooltip.show"),th=Xo("tooltip.hide"),eh=/* */Object.freeze({hideAllExclusive:ap,setComponents:function(n,t,e,o){e.getTooltip().each(function(n){n.getSystem().isConnected()&&mg.set(n,o)})}}),oh=ya({fields:$p,name:"tooltipping",active:/* */Object.freeze({events:function(o,r){function e(t){r.getTooltip().each(function(n){bs(n),o.onHide(t,n),r.clearTooltip()}),r.clearTimer()}return co(H([[lo(nh,function(n){r.resetTimer(function(){!function(t){if(!r.isShowing()){ap(t);var n=o.lazySink(t).getOrDie(),e=t.getSystem().build({dom:o.tooltipDom,components:o.tooltipComponents,events:co("normal"===o.mode?[lo($r(),function(n){oo(t,nh)}),lo(Kr(),function(n){oo(t,th)})]:[]),behaviours:ba([mg.config({})])});r.setTooltip(e),hs(n,e),o.onShow(t,e),Af.position(n,o.anchor(t),e)}}(n)},o.delay)}),lo(th,function(n){r.resetTimer(function(){e(n)},o.delay)}),lo(li(),function(n,t){vn(t.channels(),Zp)&&e(n)}),Ri(function(n){e(n)})],"normal"===o.mode?[lo(Qr(),function(n){oo(n,nh)}),lo(si(),function(n){oo(n,th)}),lo($r(),function(n){oo(n,nh)}),lo(Kr(),function(n){oo(n,th)})]:[lo(_i(),function(n,t){oo(n,nh)}),lo(Mi(),function(n){oo(n,th)})]]))}}),state:Qp,apis:eh}),rh=tinymce.util.Tools.resolve("tinymce.util.I18n"),ih="tox-menu-nav__js",uh="tox-collection__item",ah="tox-swatch",ch={normal:ih,color:ah},sh="tox-collection__item--enabled",fh="tox-collection__item-icon",lh="tox-collection__item-label",dh="tox-collection__item-caret",mh="tox-collection__item--active",gh=tinymce.util.Tools.resolve("tinymce.Env"),ph=function(n){var e=gh.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},t=n.split("+"),o=S(t,function(n){var t=n.toLowerCase().trim();return En(e,t)?e[t]:n});return gh.mac?o.join(""):o.join("+")},hh=["list-num-default","list-num-lower-alpha","list-num-lower-greek","list-num-lower-roman","list-num-upper-alpha","list-num-upper-roman"],vh=["list-bull-circle","list-bull-default","list-bull-square"],bh=["input","button","textarea","select"],yh=function(t,n,e){n.disableClass.each(function(n){fr(t.element(),n)}),(bp(t,n)?yp:wp)(t),n.onDisabled(t)},xh=/* */Object.freeze({enable:Cp,disable:yh,isDisabled:kp,onLoad:vp,set:function(n,t,e,o){(o?yh:Cp)(n,t,e)}}),wh=/* */Object.freeze({exhibit:function(n,t,e){return Zo({classes:t.disabled?t.disableClass.map(M).getOr([]):[]})},events:function(e,n){return co([so(di(),function(n,t){return kp(n,e)}),ga(e,n,vp)])}}),Sh=[St("disabled",!1),St("useNative",!0),ht("disableClass"),qu("onDisabled"),qu("onEnabled")],Ch=ya({fields:Sh,name:"disabling",active:wh,apis:xh}),kh=function(n){return Ch.config({disabled:n,disableClass:"tox-collection__item--state-disabled"})},Oh=function(n){return Ch.config({disabled:n})},Eh=function(n){return Ch.config({disabled:n,disableClass:"tox-tbtn--disabled"})},Th=function(n){return Ch.config({disabled:n,disableClass:"tox-tbtn--disabled",useNative:!1})};(Dp=Bp=Bp||{})[Dp.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",Dp[Dp.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";function Bh(n){return B(n,function(n){return n.toArray()})}function Dh(n,t,e){var o=ye(Z);return{type:"item",dom:t.dom,components:Bh(t.optComponents),data:n.data,eventOrder:Ih,hasSubmenu:n.triggersSubmenu,itemBehaviours:ba([Kd("item-events",[function(e,o){return Ni(function(n,t){Op(e,n)(e.onAction),e.triggersSubmenu||o!==Fh.CLOSE_ON_EXECUTE||(oo(n,hi()),t.stop())})}(n,e),Ep(n,o),Tp(n,o)]),kh(n.disabled),mg.config({})].concat(n.itemBehaviours))}}function Ah(n){return{value:n.value,meta:An({text:n.text.getOr("")},n.meta)}}function _h(n,t){var e=function(n){return Rh.DOM.encode(n)}(rh.translate(n));if(0<t.length){var o=new RegExp(function(n){return n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}(t),"gi");return e.replace(o,function(n){return'<span class="tox-autocompleter-highlight">'+n+"</span>"})}return e}function Mh(t,e,n){function o(n){return ro(n,Hh,{row:t,col:e})}function r(n,t){t.stop(),o(n)}var i;return uu({dom:{tag:"div",attributes:(i={role:"button"},i["aria-labelledby"]=n,i)},behaviours:ba([Kd("insert-table-picker-cell",[lo($r(),vg.focus),lo(di(),o),lo(ri(),r),lo(gi(),r)]),Cg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),vg.config({onFocus:function(n){return ro(n,Nh,{row:t,col:e})}})])})}var Fh=Bp,Ih={"alloy.execute":["disabling","alloy.base.behaviour","toggling","item-events"]},Rh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Vh=nn(js("item-widget",Ag())),Nh=Xo("cell-over"),Hh=Xo("cell-execute");function Ph(n){return{value:nn(n)}}function zh(n){return Jh.test(n)||$h.test(n)}function Lh(n){var t=function(n){var t=n.value().replace(Jh,function(n,t,e,o){return t+t+e+e+o+o});return{value:nn(t)}}(n),e=$h.exec(t.value());return null===e?["FFFFFF","FF","FF","FF"]:e}function jh(n){var t=n.toString(16);return 1===t.length?"0"+t:t}function Uh(n){var t=jh(n.red())+jh(n.green())+jh(n.blue());return Ph(t)}function Wh(n,t,e,o){return{red:nn(n),green:nn(t),blue:nn(e),alpha:nn(o)}}function Gh(n){var t=parseInt(n,10);return t.toString()===n&&0<=t&&t<=255}function Xh(n){var t,e,o,r=(n.hue()||0)%360,i=n.saturation()/100,u=n.value()/100;if(i=Zh(0,Qh(i,1)),u=Zh(0,Qh(u,1)),0===i)return t=e=o=nv(255*u),Wh(t,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),f=u-c;switch(Math.floor(a)){case 0:t=c,e=s,o=0;break;case 1:t=s,e=c,o=0;break;case 2:t=0,e=c,o=s;break;case 3:t=0,e=s,o=c;break;case 4:t=s,e=0,o=c;break;case 5:t=c,e=0,o=s;break;default:t=e=o=0}return t=nv(255*(t+f)),e=nv(255*(e+f)),o=nv(255*(o+f)),Wh(t,e,o,1)}function Yh(n){var t=Lh(n),e=parseInt(t[1],16),o=parseInt(t[2],16),r=parseInt(t[3],16);return Wh(e,o,r,1)}function qh(n,t,e,o){var r=parseInt(n,10),i=parseInt(t,10),u=parseInt(e,10),a=parseFloat(o);return Wh(r,i,u,a)}function Kh(n){return"rgba("+n.red()+","+n.green()+","+n.blue()+","+n.alpha()+")"}var Jh=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,$h=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,Qh=Math.min,Zh=Math.max,nv=Math.round,tv=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,ev=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,ov=nn(Wh(255,0,0,1)),rv=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),iv="tinymce-custom-colors";function uv(n){var t=[],u=v.document.createElement("canvas");u.height=1,u.width=1;for(var a=u.getContext("2d"),c=function(n,t){var e=t/255;return("0"+Math.round(n*e+255*(1-e)).toString(16)).slice(-2).toUpperCase()},e=function(n){if(/^[0-9A-Fa-f]{6}$/.test(n))return"#"+n.toUpperCase();a.clearRect(0,0,u.width,u.height),a.fillStyle="#FFFFFF",a.fillStyle=n,a.fillRect(0,0,1,1);var t=a.getImageData(0,0,1,1).data,e=t[0],o=t[1],r=t[2],i=t[3];return"#"+c(e,i)+c(o,i)+c(r,i)},o=0;o<n.length;o+=2)t.push({text:n[o+1],value:e(n[o]),type:"choiceitem"});return t}function av(n){return n.getParam("color_map")}function cv(n,e){var o;return n.dom.getParents(n.selection.getStart(),function(n){var t;(t=n.style["forecolor"===e?"color":"background-color"])&&(o=o||t)}),o}function sv(n){return Math.max(5,Math.ceil(Math.sqrt(n)))}function fv(n){var t=Iv(n),e=sv(t.length);return Mv(n,e)}function lv(t,e,n,o){"custom"===n?jv(t)(function(n){n.each(function(n){Vv(n),t.execCommand("mceApplyTextcolor",e,n),o(n)})},"#000000"):"remove"===n?(o(""),t.execCommand("mceRemoveTextcolor",e)):(o(n),t.execCommand("mceApplyTextcolor",e,n))}function dv(n,t){return n.concat(Rv().concat(function(n){var t="choiceitem",e={type:t,text:"Remove color",icon:"color-swatch-remove-color",value:"remove"};return n?[e,{type:t,text:"Custom color",icon:"color-picker",value:"custom"}]:[e]}(t)))}function mv(t,e){return function(n){n(dv(t,e))}}function gv(n,t,e){var o,r;o="forecolor"===t?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color",r=e,n.setIconFill(o,r),n.setIconStroke(o,r)}function pv(o,e,r,n,i){o.ui.registry.addSplitButton(e,{tooltip:n,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){return on.from(cv(o,r)).bind(function(n){return function(n){if("transparent"===n)return on.some(Wh(0,0,0,0));var t=tv.exec(n);if(null!==t)return on.some(qh(t[1],t[2],t[3],"1"));var e=ev.exec(n);return null!==e?on.some(qh(e[1],e[2],e[3],e[4])):on.none()}(n).map(function(n){var t=Uh(n).value();return Vt(e.toLowerCase(),t)})}).getOr(!1)},columns:fv(o),fetch:mv(Iv(o),Fv(o)),onAction:function(n){null!==i.get()&&lv(o,r,i.get(),function(){})},onItemAction:function(n,t){lv(o,r,t,function(n){i.set(n),Lv(o,{name:e,color:n})})},onSetup:function(t){null!==i.get()&&gv(t,e,i.get());function n(n){n.name===e&&gv(t,n.name,n.color)}return o.on("TextColorChange",n),function(){o.off("TextColorChange",n)}}})}function hv(t,n,e,o){t.ui.registry.addNestedMenuItem(n,{text:o,icon:"forecolor"===n?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(n){lv(t,e,n.value,Z)}}]}})}function vv(e,o){return function(n){var t=w(n,o);return S(t,function(n){return{dom:e,components:n}})}}function bv(n,e){var o=[],r=[];return bn(n,function(n,t){e(n,t)?(0<r.length&&o.push(r),r=[],En(n.dom,"innerHtml")&&r.push(n)):r.push(n)}),0<r.length&&o.push(r),S(o,function(n){return{dom:{tag:"div",classes:["tox-collection__group"]},components:n}})}function yv(t,e,n){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===t?["tox-collection--list"]:["tox-collection--grid"])},components:[Vg.parts().items({preprocess:function(n){return"auto"!==t&&1<t?vv({tag:"div",classes:["tox-collection__group"]},t)(n):bv(n,function(n,t){return"separator"===e[t].type})}})]}}function xv(n){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:function(n){return"color"===n?"tox-swatches":"tox-menu"}(n),tieredMenu:"tox-tiered-menu"}}function wv(n){var t=xv(n);return{backgroundMenu:t.backgroundMenu,selectedMenu:t.selectedMenu,menu:t.menu,selectedItem:t.selectedItem,item:sp(n)}}function Sv(n,t,e){var o=xv(e);return{dom:{tag:"div",classes:H([[o.tieredMenu]])},markers:wv(e)}}function Cv(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function kv(n){return v.console.error(le(n)),v.console.log(n),on.none()}function Ov(n,t,e,o,r){var i=function(e){return{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Vg.parts().items({preprocess:function(n){return bv(n,function(n,t){return"separator"===e[t].type})}})]}}(e);return{value:n,dom:i.dom,components:i.components,items:e}}function Ev(n,t,e,o,r){var i;return"color"===r?{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Vg.parts().items({preprocess:"auto"!==n?vv({tag:"div",classes:["tox-swatches__row"]},n):l})]}]}}(o)).dom,components:i.components,items:e}:"normal"===r&&"auto"===o?{value:n,dom:(i=yv(o,e)).dom,components:i.components,items:e}:"normal"===r&&1===o?{value:n,dom:(i=yv(1,e)).dom,components:i.components,items:e}:"normal"===r?{value:n,dom:(i=yv(o,e)).dom,components:i.components,items:e}:"listpreview"!==r||"auto"===o?{value:n,dom:function(n,t,e){var o=xv(e);return{tag:"div",classes:H([[o.menu,"tox-menu-"+t+"-column"],n?[o.hasIcons]:[]])}}(t,o,r),components:Wv,items:e}:{value:n,dom:(i=function(n){return{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Vg.parts().items({preprocess:vv({tag:"div",classes:["tox-collection__group"]},n)})]}}(o)).dom,components:i.components,items:e}}function Tv(n,t,e,o,r,i,u,a){var c=function(n){return x(n,Cv)}(t),s=Gv(t,e,o,"color"!==r?"normal":"color",i,u,a);return Ev(n,c,s,o,r)}function Bv(n,t){var e=wv(t);return 1===n?{mode:"menu",moveOnTab:!0}:"auto"===n?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===t?"tox-swatches__row":"tox-collection__group")}}var Dv="choiceitem",Av=[{type:Dv,text:"Light Green",value:"#BFEDD2"},{type:Dv,text:"Light Yellow",value:"#FBEEB8"},{type:Dv,text:"Light Red",value:"#F8CAC6"},{type:Dv,text:"Light Purple",value:"#ECCAFA"},{type:Dv,text:"Light Blue",value:"#C2E0F4"},{type:Dv,text:"Green",value:"#2DC26B"},{type:Dv,text:"Yellow",value:"#F1C40F"},{type:Dv,text:"Red",value:"#E03E2D"},{type:Dv,text:"Purple",value:"#B96AD9"},{type:Dv,text:"Blue",value:"#3598DB"},{type:Dv,text:"Dark Turquoise",value:"#169179"},{type:Dv,text:"Orange",value:"#E67E23"},{type:Dv,text:"Dark Red",value:"#BA372A"},{type:Dv,text:"Dark Purple",value:"#843FA1"},{type:Dv,text:"Dark Blue",value:"#236FA1"},{type:Dv,text:"Light Gray",value:"#ECF0F1"},{type:Dv,text:"Medium Gray",value:"#CED4D9"},{type:Dv,text:"Gray",value:"#95A5A6"},{type:Dv,text:"Dark Gray",value:"#7E8C8D"},{type:Dv,text:"Navy Blue",value:"#34495E"},{type:Dv,text:"Black",value:"#000000"},{type:Dv,text:"White",value:"#ffffff"}],_v=function iI(t){void 0===t&&(t=10);var n,e=rv.getItem(iv),o=cn(e)?JSON.parse(e):[],r=t-(n=o).length<0?n.slice(0,t):n,i=function(n){r.splice(n,1)};return{add:function(n){(function(n,t){var e=y(n,t);return-1===e?on.none():on.some(e)})(r,n).each(i),r.unshift(n),r.length>t&&r.pop(),rv.setItem(iv,JSON.stringify(r))},state:function(){return r.slice(0)}}}(10),Mv=function(n,t){return n.getParam("color_cols",t,"number")},Fv=function(n){return!1!==n.getParam("custom_colors")},Iv=function(n){var t=av(n);return t!==undefined?uv(t):Av},Rv=function(){return S(_v.state(),function(n){return{type:Dv,text:n,value:n}})},Vv=function(n){_v.add(n)},Nv=function(n){return n.fire("SkinLoaded")},Hv=function(n){return n.fire("ResizeEditor")},Pv=function(n,t){return n.fire("ScrollContent",t)},zv=function(n,t){return n.fire("ResizeContent",t)},Lv=function(n,t){return n.fire("TextColorChange",t)},jv=function(i){return function(n,t){var e,o={colorpicker:t},r=(e=n,function(n){var t=n.getData();e(on.from(t.colorpicker)),n.close()});i.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:o,onAction:function(n,t){"hex-valid"===t.name&&(t.value?n.enable("ok"):n.disable("ok"))},onSubmit:r,onClose:function(){},onCancel:function(){n(on.none())}})}},Uv={register:function(n){!function(e){e.addCommand("mceApplyTextcolor",function(n,t){!function(n,t,e){n.undoManager.transact(function(){n.focus(),n.formatter.apply(t,{value:e}),n.nodeChanged()})}(e,n,t)}),e.addCommand("mceRemoveTextcolor",function(n){!function(n,t){n.undoManager.transact(function(){n.focus(),n.formatter.remove(t,{value:null},null,!0),n.nodeChanged()})}(e,n)})}(n);var t=ye(null),e=ye(null);pv(n,"forecolor","forecolor","Text color",t),pv(n,"backcolor","hilitecolor","Background color",e),hv(n,"forecolor","forecolor","Text color"),hv(n,"backcolor","hilitecolor","Background color")},getColors:dv,getFetch:mv,colorPickerDialog:jv,getCurrentColor:cv,getColorCols:fv,calcCols:sv},Wv=[Vg.parts().items({})],Gv=function(n,e,o,r,i,u,a){return Bu(S(n,function(t){return"choiceitem"===t.type?function(n){return tt("choicemenuitem",qp,n)}(t).fold(kv,function(n){return on.some(function(t,n,e,o,r,i,u){var a=hp({presets:e,textContent:n?t.text:on.none(),htmlContent:on.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:n?t.shortcut:on.none(),checkMark:n?on.some(gp(u.icons)):on.none(),caret:on.none(),value:t.value},u,!0);return Dn(Dh({data:Ah(t),disabled:t.disabled,getApi:function(t){return{setActive:function(n){Cg.set(t,n)},isActive:function(){return Cg.isOn(t)},isDisabled:function(){return Ch.isDisabled(t)},setDisabled:function(n){return Ch.set(t,n)}}},onAction:function(n){return o(t.value)},onSetup:function(n){return n.setActive(r),function(){}},triggersSubmenu:!1,itemBehaviours:[]},a,i),{toggling:{toggleClass:sh,toggleOnExecute:!1,selected:t.active}})}(n,1===o,r,e,u(t.value),i,a))}):on.none()}))};var Xv,Yv,qv={inserttable:function uI(o){var n=Xo("size-label"),i=function(n,t,e){for(var o=[],r=0;r<t;r++){for(var i=[],u=0;u<e;u++)i.push(Mh(r,u,n));o.push(i)}return o}(n,10,10),u=vm({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:n}},components:[Ir("0x0")],behaviours:ba([mg.config({})])});return{type:"widget",data:{value:Xo("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Vh().widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:function(n){return B(n,function(n){return S(n,au)})}(i).concat(u.asSpec()),behaviours:ba([Kd("insert-table-picker",[ho(Nh,function(n,t,e){var o=e.event().row(),r=e.event().col();!function(n,t,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)Cg.set(n[i][u],i<=t&&u<=e)}(i,o,r,10,10),mg.set(u.get(n),[function(n,t){return Ir(t+1+"x"+(n+1))}(o,r)])}),ho(Hh,function(n,t,e){o.onAction({numRows:e.event().row()+1,numColumns:e.event().col()+1}),oo(n,hi())})]),lg.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function aI(t,n){var e=Uv.getColors(n.colorinput.getColors(),n.colorinput.hasCustomColors()),o=n.colorinput.getColorCols(),r=Tv(Xo("menu-value"),e,function(n){t.onAction({value:n})},o,"color",Fh.CLOSE_ON_EXECUTE,function(){return!1},n.shared.providers),i=Dn(N(N({},r),{markers:wv("color"),movement:Bv(o,"color")}));return{type:"widget",data:{value:Xo("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[Vh().widget(Vg.sketch(i))]}}},Kv=function(t,e,n,o,r,i,u,a){void 0===a&&(a=!0);var c=hp({presets:o,textContent:on.none(),htmlContent:n?t.text.map(function(n){return _h(n,e)}):on.none(),ariaLabel:t.text,iconContent:t.icon,shortcutContent:on.none(),checkMark:on.none(),caret:on.none(),value:t.value},u.providers,a,t.icon);return Dh({data:Ah(t),disabled:t.disabled,getApi:function(){return{}},onAction:function(n){return r(t.value,t.meta)},onSetup:function(){return function(){}},triggersSubmenu:!1,itemBehaviours:function(n,t){return V(n,"tooltipWorker").map(function(e){return[oh.config({lazySink:t.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(n){return{anchor:"submenu",item:n,overrides:{maxHeightFunction:Cf}}},mode:"follow-highlight",onShow:function(t,n){e(function(n){oh.setComponents(t,[ru({element:we.fromDom(n)})])})}})]}).getOr([])}(t.meta,u)},c,i)},Jv=function(n){var t=n.text.fold(function(){return{}},function(n){return{innerHtml:n}});return{type:"separator",dom:N({tag:"div",classes:[uh,"tox-collection__group-heading"]},t),components:[]}},$v=function(n,t,e,o){void 0===o&&(o=!0);var r=hp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:on.none(),ariaLabel:n.text,caret:on.none(),checkMark:on.none(),shortcutContent:n.shortcut},e,o);return Dh({data:Ah(n),getApi:function(t){return{isDisabled:function(){return Ch.isDisabled(t)},setDisabled:function(n){return Ch.set(t,n)}}},disabled:n.disabled,onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,t)},Qv=function(n,t,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i=r?function(n){return{dom:{tag:"div",classes:[dh],innerHtml:ym("chevron-down",n)}}}(e.icons):function(n){return{dom:{tag:"div",classes:[dh],innerHtml:ym("chevron-right",n)}}}(e.icons),u=hp({presets:"normal",iconContent:n.icon,textContent:n.text,htmlContent:on.none(),ariaLabel:n.text,caret:on.some(i),checkMark:on.none(),shortcutContent:n.shortcut},e,o);return Dh({data:Ah(n),getApi:function(t){return{isDisabled:function(){return Ch.isDisabled(t)},setDisabled:function(n){return Ch.set(t,n)}}},disabled:n.disabled,onAction:Z,onSetup:n.onSetup,triggersSubmenu:!0,itemBehaviours:[]},u,t)},Zv=function(n,t,e){var o=hp({iconContent:on.none(),textContent:n.text,htmlContent:on.none(),ariaLabel:n.text,checkMark:on.some(gp(e.icons)),caret:on.none(),shortcutContent:n.shortcut,presets:"normal",meta:n.meta},e,!0);return Dn(Dh({data:Ah(n),disabled:n.disabled,getApi:function(t){return{setActive:function(n){Cg.set(t,n)},isActive:function(){return Cg.isOn(t)},isDisabled:function(){return Ch.isDisabled(t)},setDisabled:function(n){return Ch.set(t,n)}}},onAction:n.onAction,onSetup:n.onSetup,triggersSubmenu:!1,itemBehaviours:[]},o,t),{toggling:{toggleClass:sh,toggleOnExecute:!1,selected:n.active}})},nb=function(t,e){return function(n,t){return Object.prototype.hasOwnProperty.call(n,t)?on.some(n[t]):on.none()}(qv,t.fancytype).map(function(n){return n(t,e)})};(Yv=Xv=Xv||{})[Yv.ContentFocus=0]="ContentFocus",Yv[Yv.UiFocus=1]="UiFocus";function tb(n){return n.icon!==undefined||"togglemenuitem"===n.type||"choicemenuitem"===n.type}function eb(n){return x(n,tb)}function ob(n,t,e,o,r){function i(n){return r?N(N({},n),{shortcut:on.none(),icon:n.text.isSome()?on.none():n.icon}):n}var u=e.shared.providers;switch(n.type){case"menuitem":return function(n){return tt("menuitem",Gp,n)}(n).fold(kv,function(n){return on.some($v(i(n),t,u,o))});case"nestedmenuitem":return function(n){return tt("nestedmenuitem",Xp,n)}(n).fold(kv,function(n){return on.some(Qv(i(n),t,u,o,r))});case"togglemenuitem":return function(n){return tt("togglemenuitem",Yp,n)}(n).fold(kv,function(n){return on.some(Zv(i(n),t,u))});case"separator":return function(n){return tt("separatormenuitem",Lp,n)}(n).fold(kv,function(n){return on.some(Jv(n))});case"fancymenuitem":return function(n){return tt("fancymenuitem",Kp,n)}(n).fold(kv,function(n){return nb(i(n),e)});default:return v.console.error("Unknown item in general menu",n),on.none()}}function rb(n,t,e,o,r,i){var u=1===o,a=!u||eb(n);return Bu(S(n,function(n){return"separator"===n.type?function(n){return tt("Autocompleter.Separator",Lp,n)}(n).fold(kv,function(n){return on.some(Jv(n))}):function(n){return tt("Autocompleter.Item",jp,n)}(n).fold(kv,function(n){return on.some(Kv(n,t,u,"normal",e,r,i,a))})}))}function ib(n,t,e,o,r){var i=eb(t),u=Bu(S(t,function(n){function t(n){return ob(n,e,o,function(n){return r?!n.hasOwnProperty("text"):i}(n),r)}return"nestedmenuitem"===n.type&&n.getSubmenuItems().length<=0?t(An(n,{disabled:!0})):t(n)}));return(r?Ov:Ev)(n,i,u,1,"normal")}function ub(n){return Lg.singleData(n.value,n)}function ab(n){function t(){n.stopPropagation()}function e(){n.preventDefault()}var o=we.fromDom(n.target),r=i(e,t);return function(n,t,e,o,r,i,u){return{target:nn(n),x:nn(t),y:nn(e),stop:o,prevent:r,kill:i,raw:nn(u)}}(o,n.clientX,n.clientY,t,e,r,n)}function cb(n,t,e,o,r){var i=function(t,e){return function(n){t(n)&&e(ab(n))}}(e,o);return n.dom().addEventListener(t,i,r),{unbind:d(mb,n,t,i,r)}}function sb(n,t,e){return function(n,t,e,o){return cb(n,t,e,o,!1)}(n,t,gb,e)}function fb(n,t,e){return function(n,t,e,o){return cb(n,t,e,o,!0)}(n,t,gb,e)}function lb(n,t,e){return Ou(n,t,e).isSome()}var db=function(u,a){function e(){return s.get().isSome()}function c(){e()&&jg.hide(l)}function i(n,t,e,o){n.matchLength=t.text.length;var r=Du(e,function(n){return on.from(n.columns)}).getOr(1);jg.showAt(l,{anchor:"node",root:we.fromDom(u.getBody()),node:on.from(n.element)},Vg.sketch(function(n,t,e,o){var r=e===Xv.ContentFocus?Xl():Gl(),i=Bv(t,o),u=wv(o);return{dom:n.dom,components:n.components,items:n.items,value:n.value,markers:{selectedItem:u.selectedItem,item:u.item},movement:i,fakeFocus:e===Xv.ContentFocus,focusManager:r,menuBehaviours:Jp("auto"!==t?[]:[Ii(function(o,n){ip(o,4,u.item).each(function(n){var t=n.numColumns,e=n.numRows;lg.setGridSize(o,e,t)})})])}}(Ev("autocompleter-value",!0,o,r,"normal"),r,Xv.ContentFocus,"normal"))),jg.getContent(l).each(ad.highlightFirst)}var s=ye(on.none()),f=ye(!1),l=uu(jg.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:ba([Kd("dismissAutocompleter",[lo(Oi(),function(){return d()})])]),lazySink:a.getSink})),d=function(){if(e()){var n=s.get().map(function(n){return n.element});Ip(n.getOr(we.fromDom(u.selection.getNode()))).each(Ro),c(),s.set(on.none()),f.set(!1)}},o=L(function(){return rp(u)}),m=function(n){(function(t){return s.get().map(function(n){return tp(u.dom,u.selection.getRng(),n.triggerChar).bind(function(n){return zp(u,o,n,t)})}).getOrThunk(function(){return op(u,o)})})(n).fold(d,function(r){!function(n){if(!e()){var t=Qg(u,n.range);s.set(on.some({triggerChar:n.triggerChar,element:t,matchLength:n.text.length})),f.set(!1)}}(r.context),r.lookupData.then(function(o){s.get().map(function(n){var t=r.context;if(n.triggerChar===t.triggerChar){var e=function(t,n){var e=Du(n,function(n){return on.from(n.columns)}).getOr(1);return B(n,function(i){var n=i.items;return rb(n,i.matchText,function(o,r){var n=u.selection.getRng();tp(u.dom,n,t).fold(function(){return v.console.error("Lost context. Cursor probably moved")},function(n){var t=n.range,e={hide:function(){d()},reload:function(n){c(),m(n)}};f.set(!0),i.onAction(e,t,o,r),f.set(!1)})},e,Fh.BUBBLE_TO_SANDBOX,a)})}(t.triggerChar,o);0<e.length?i(n,t,o,e):10<=t.text.length-n.matchLength?d():c()}})})})},n={onKeypress:Jg(function(n){27!==n.which&&m()},50),cancelIfNecessary:d,isMenuOpen:function(){return jg.isOpen(l)},isActive:e,isProcessingAction:f.get,getView:function(){return jg.getContent(l)}};Vp(n,u)},mb=function(n,t,e,o){n.dom().removeEventListener(t,e,o)},gb=nn(!0),pb=ab;function hb(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},schedule:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];r=v.setTimeout(function(){e.apply(null,n),r=null},o)}}}function vb(n){var t=n.raw();return t.touches===undefined||1!==t.touches.length?on.none():on.some(t.touches[0])}function bb(e){var o=ye(on.none()),r=ye(!1),i=hb(function(n){e.triggerEvent(pi(),n),r.set(!0)},400),u=K([{key:Ur(),value:function(e){return vb(e).each(function(n){i.cancel();var t={x:nn(n.clientX),y:nn(n.clientY),target:e.target};i.schedule(e),r.set(!1),o.set(on.some(t))}),on.none()}},{key:Wr(),value:function(n){return i.cancel(),vb(n).each(function(t){o.get().each(function(n){!function(n,t){var e=Math.abs(n.clientX-t.x()),o=Math.abs(n.clientY-t.y());return 5<e||5<o}(t,n)||o.set(on.none())})}),on.none()}},{key:Gr(),value:function(t){i.cancel();return o.get().filter(function(n){return jt(n.target(),t.target())}).map(function(n){return r.get()?(t.prevent(),!1):e.triggerEvent(gi(),t)})}}]);return{fireIfReady:function(t,n){return Nn(u,n).bind(function(n){return n(t)})}}}function yb(t,n){var e=ot("Getting GUI events settings",kb,n),o=bb(e),r=S(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(n){return sb(t,n,function(t){o.fireIfReady(t,n).each(function(n){n&&t.kill()}),e.triggerEvent(n,t)&&t.kill()})}),i=ye(on.none()),u=sb(t,"paste",function(t){o.fireIfReady(t,"paste").each(function(n){n&&t.kill()}),e.triggerEvent("paste",t)&&t.kill(),i.set(on.some(v.setTimeout(function(){e.triggerEvent(fi(),t)},0)))}),a=sb(t,"keydown",function(n){e.triggerEvent("keydown",n)?n.kill():!0===e.stopBackspace&&function(n){return 8===n.raw().which&&!vn(["input","textarea"],Ke(n.target()))&&!lb(n.target(),'[contenteditable="true"]')}(n)&&n.prevent()}),c=function(n,t){return Cb?fb(n,"focus",t):sb(n,"focusin",t)}(t,function(n){e.triggerEvent("focusin",n)&&n.kill()}),s=ye(on.none()),f=function(n,t){return Cb?fb(n,"blur",t):sb(n,"focusout",t)}(t,function(n){e.triggerEvent("focusout",n)&&n.kill(),s.set(on.some(v.setTimeout(function(){e.triggerEvent(si(),n)},0)))});return{unbind:function(){bn(r,function(n){n.unbind()}),a.unbind(),c.unbind(),f.unbind(),u.unbind(),i.get().each(v.clearTimeout),s.get().each(v.clearTimeout)}}}function xb(n,t){var e=Nn(n,"target").map(function(n){return n()}).getOr(t);return ye(e)}function wb(n,o,t,e,r,i){var u=n(o,e),a=function(n,t){var e=ye(!1),o=ye(!1);return{stop:function(){e.set(!0)},cut:function(){o.set(!0)},isStopped:e.get,isCut:o.get,event:nn(n),setSource:t.set,getSource:t.get}}(t,r);return u.fold(function(){return i.logEventNoHandlers(o,e),Ob.complete()},function(t){var e=t.descHandler();return nr(e)(a),a.isStopped()?(i.logEventStopped(o,t.element(),e.purpose()),Ob.stopped()):a.isCut()?(i.logEventCut(o,t.element(),e.purpose()),Ob.complete()):Eo(t.element()).fold(function(){return i.logNoParent(o,t.element(),e.purpose()),Ob.complete()},function(n){return i.logEventResponse(o,t.element(),e.purpose()),Ob.resume(n)})})}function Sb(n,t,e){var o=function(n){var t=ye(!1);return{stop:function(){t.set(!0)},cut:Z,isStopped:t.get,isCut:nn(!1),event:nn(n),setSource:r("Cannot set source of a broadcasted event"),getSource:r("Cannot get source of a broadcasted event")}}(t);return bn(n,function(n){var t=n.descHandler();nr(t)(o)}),o.isStopped()}var Cb=Ht().browser.isFirefox(),kb=qn([dt("triggerEvent"),St("stopBackspace",!0)]),Ob=Tn([{stopped:[]},{resume:["element"]},{complete:[]}]),Eb=function(t,e,o,n,r,i){return wb(t,e,o,n,r,i).fold(function(){return!0},function(n){return Eb(t,e,o,n,r,i)},function(){return!1})},Tb=function(n,t,e,o,r){var i=xb(e,o);return Eb(n,t,e,o,i,r)},Bb=yo("element","descHandler"),Db=function(n,t){return{id:nn(n),descHandler:nn(t)}};function Ab(){var i={};return{registerId:function(o,r,n){Cn(n,function(n,t){var e=i[t]!==undefined?i[t]:{};e[r]=tu(n,o),i[t]=e})},unregisterId:function(e){Cn(i,function(n,t){n.hasOwnProperty(e)&&delete n[e]})},filterByType:function(n){return Nn(i,n).map(function(n){return On(n,function(n,t){return Db(t,n)})}).getOr([])},find:function(n,t,e){var o=Vn(t)(i);return jr(e,function(n){return function(e,o){return qi(o).fold(function(){return on.none()},function(n){var t=Vn(n);return e.bind(t).map(function(n){return Bb(o,n)})})}(o,n)},n)}}}function _b(){function o(n){var t=n.element();return qi(t).fold(function(){return function(n,t){var e=Xo(Gi+n);return Yi(t,e),e}("uid-",n.element())},function(n){return n})}var r=Ab(),i={},u=function(n){qi(n.element()).each(function(n){delete i[n],r.unregisterId(n)})};return{find:function(n,t,e){return r.find(n,t,e)},filter:function(n){return r.filterByType(n)},register:function(n){var t=o(n);$(i,t)&&function(n,t){var e=i[t];if(e!==n)throw new Error('The tagId "'+t+'" is already used by: '+Go(e.element())+"\nCannot use it for: "+Go(n.element())+"\nThe conflicting element is"+($e(e.element())?" ":" not ")+"already in the DOM");u(n)}(n,t);var e=[n];r.registerId(e,t,n.events()),i[t]=n},unregister:u,getById:function(n){return Vn(n)(i)}}}function Mb(e){function o(t){return Eo(e.element()).fold(function(){return!0},function(n){return jt(t,n)})}function r(n,t){return u.find(o,n,t)}function i(e){var n=u.filter(li());bn(n,function(n){var t=n.descHandler();nr(t)(e)})}var u=_b(),n=yb(e.element(),{triggerEvent:function(t,e){return Wu(t,e.target(),function(n){return function(n,t,e,o){var r=e.target();return Tb(n,t,e,r,o)}(r,t,e,n)})}}),a={debugInfo:nn("real"),triggerEvent:function(t,e,o){Wu(t,e,function(n){Tb(r,t,o,e,n)})},triggerFocus:function(t,e){qi(t).fold(function(){wa(t)},function(n){Wu(ci(),t,function(n){!function(n,t,e,o,r){var i=xb(e,o);wb(n,t,e,o,i,r)}(r,ci(),{originator:nn(e),kill:Z,prevent:Z,target:nn(t)},t,n)})})},triggerEscape:function(n,t){a.triggerEvent("keydown",n.element(),t.event())},getByUid:function(n){return g(n)},getByDom:function(n){return p(n)},build:uu,addToGui:function(n){s(n)},removeFromGui:function(n){f(n)},addToWorld:function(n){t(n)},removeFromWorld:function(n){c(n)},broadcast:function(n){l(n)},broadcastOn:function(n,t){d(n,t)},broadcastEvent:function(n,t){m(n,t)},isConnected:nn(!0)},t=function(n){n.connect(a),Pr(n.element())||(u.register(n),bn(n.components(),t),a.triggerEvent(bi(),n.element(),{target:nn(n.element())}))},c=function(n){Pr(n.element())||(bn(n.components(),c),u.unregister(n)),n.disconnect()},s=function(n){hs(e,n)},f=function(n){bs(n)},l=function(n){i({universal:nn(!0),data:nn(n)})},d=function(n,t){i({universal:nn(!1),channels:nn(n),data:nn(t)})},m=function(n,t){var e=u.filter(n);return Sb(e,t)},g=function(n){return u.getById(n).fold(function(){return an.error(new Error('Could not find component with uid: "'+n+'" in system.'))},an.value)},p=function(n){var t=qi(n).getOr("not found");return g(t)};return t(e),{root:nn(e),element:e.element,destroy:function(){n.unbind(),Pi(e.element())},add:s,remove:f,getByUid:g,getByDom:p,addToWorld:t,removeFromWorld:c,broadcast:l,broadcastOn:d,broadcastEvent:m}}function Fb(n){return n.getParam("height",Math.max(n.getElement().offsetHeight,200))}function Ib(n){return n.getParam("width",Rh.DOM.getStyle(n.getElement(),"width"))}function Rb(n){return on.from(n.settings.min_width).filter(mn)}function Vb(n){return on.from(n.settings.min_height).filter(mn)}function Nb(n){return on.from(n.getParam("max_width")).filter(mn)}function Hb(n){return on.from(n.getParam("max_height")).filter(mn)}function Pb(n){return!1!==n.getParam("menubar",!0,"boolean")}function zb(n){var t=n.getParam("toolbar",!0),e=!0===t,o=cn(t),r=fn(t)&&0<t.length;return!Xb(n)&&(r||o||e)}function Lb(t){var n=wn(t.settings),e=C(n,function(n){return/^toolbar([1-9])$/.test(n)}),o=S(e,function(n){return t.getParam(n,!1,"string")}),r=C(o,function(n){return"string"==typeof n});return 0<r.length?on.some(r):on.none()}var jb,Ub,Wb=Dl({name:"Container",factory:function(n){var t=n.dom,e=t.attributes,o=c(t,["attributes"]);return{uid:n.uid,dom:N({tag:"div",attributes:N({role:"presentation"},e)},o),components:n.components,behaviours:Is(n.containerBehaviours),events:n.events,domModification:n.domModification,eventOrder:n.eventOrder}},configFields:[St("components",[]),Fs("containerBehaviours",[]),St("events",{}),St("domModification",{}),St("eventOrder",{})]}),Gb=tinymce.util.Tools.resolve("tinymce.EditorManager"),Xb=function(n){return Lb(n).fold(function(){return 0<n.getParam("toolbar",[],"string[]").length},function(){return!0})};(Ub=jb=jb||{})["default"]="",Ub.floating="floating",Ub.sliding="sliding",Ub.scrolling="scrolling";function Yb(n){return n.getParam("toolbar_drawer","","string")}function qb(n){var t=function(n){return n.getParam("fixed_toolbar_container","","string")}(n);return 0<t.length&&n.inline?ku(zr(),t):on.none()}function Kb(n){return n.inline&&qb(n).isSome()}function Jb(n){return n.inline&&!Pb(n)&&!zb(n)&&!Xb(n)}function $b(n){return(n.getParam("toolbar_sticky",!1,"boolean")||n.inline)&&!Kb(n)&&!Jb(n)}function Qb(n){return n.touches===undefined||1!==n.touches.length?on.none():on.some(n.touches[0])}function Zb(n){return ba([vg.config({onFocus:!1===n.selectOnFocus?Z:function(n){var t=n.element(),e=Er(t);t.dom().setSelectionRange(0,e.length)}})])}function ny(n){return{tag:n.tag,attributes:N({type:"text"},n.inputAttributes),styles:n.inputStyles,classes:n.inputClasses}}var ty,ey,oy,ry,iy=function(e){var o=ye(on.none()),r=ye(!1),i=Jg(function(n){e.fire("longpress",N(N({},n),{type:"longpress"})),r.set(!0)},400);e.on("touchstart",function(e){Qb(e).each(function(n){i.cancel();var t={x:nn(n.clientX),y:nn(n.clientY),target:nn(e.target)};i.throttle(e),r.set(!1),o.set(on.some(t))})},!0),e.on("touchmove",function(n){i.cancel(),Qb(n).each(function(t){o.get().each(function(n){!function(n,t){var e=Math.abs(n.clientX-t.x()),o=Math.abs(n.clientY-t.y());return 5<e||5<o}(t,n)||(o.set(on.none()),r.set(!1),e.fire("longpresscancel"))})})},!0),e.on("touchend touchcancel",function(t){i.cancel(),"touchcancel"!==t.type&&o.get().filter(function(n){return n.target().isEqualNode(t.target)}).each(function(){r.get()?t.preventDefault():e.fire("tap",{touches:t.touches}).isDefaultPrevented()&&t.preventDefault()})},!0)},uy=Xo("form-component-change"),ay=Xo("form-close"),cy=Xo("form-cancel"),sy=Xo("form-action"),fy=Xo("form-submit"),ly=Xo("form-block"),dy=Xo("form-unblock"),my=Xo("form-tabchange"),gy=Xo("form-resize"),py=nn([St("prefix","form-field"),Fs("fieldBehaviours",[Zl,Zf])]),hy=nn([Sl({schema:[ct("dom")],name:"label"}),Sl({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[ct("text")],name:"aria-descriptor"}),xl({factory:{sketch:function(n){var t=X(n,["factory"]);return n.factory.sketch(t)}},schema:[ct("factory")],name:"field"})]),vy=Al({name:"FormField",configFields:py(),partFields:hy(),factory:function(r,n,t,e){var o=Rs(r.fieldBehaviours,[Zl.config({find:function(n){return qs(n,r,"field")}}),Zf.config({store:{mode:"manual",getValue:function(n){return Zl.getCurrent(n).bind(Zf.getValue)},setValue:function(n,t){Zl.getCurrent(n).each(function(n){Zf.setValue(n,t)})}}})]),i=co([Ii(function(n,t){var o=Js(n,r,["label","field","aria-descriptor"]);o.field().each(function(e){var t=Xo(r.prefix);o.label().each(function(n){Po(n.element(),"for",t),Po(e.element(),"id",t)}),o["aria-descriptor"]().each(function(n){var t=Xo(r.prefix);Po(n.element(),"id",t),Po(e.element(),"aria-describedby",t)})})})]),u={getField:function(n){return qs(n,r,"field")},getLabel:function(n){return qs(n,r,"label")}};return{uid:r.uid,dom:r.dom,components:n,behaviours:o,events:i,apis:u}},apis:{getField:function(n,t){return n.getField(t)},getLabel:function(n,t){return n.getLabel(t)}}}),by=nn([ht("data"),St("inputAttributes",{}),St("inputStyles",{}),St("tag","input"),St("inputClasses",[]),qu("onSetValue"),St("styles",{}),St("eventOrder",{}),Fs("inputBehaviours",[Zf,vg]),St("selectOnFocus",!0)]),yy=Dl({name:"Input",configFields:by(),factory:function(n,t){return{uid:n.uid,dom:ny(n),components:[],behaviours:function(n){return N(N({},Zb(n)),Rs(n.inputBehaviours,[Zf.config({store:{mode:"manual",initialValue:n.data.getOr(undefined),getValue:function(n){return Er(n.element())},setValue:function(n,t){Er(n.element())!==t&&Tr(n.element(),t)}},onSetValue:n.onSetValue})]))}(n),eventOrder:n.eventOrder}}}),xy={},wy={exports:xy};ty=undefined,ey=xy,oy=wy,ry=undefined,function(n){"object"==typeof ey&&void 0!==oy?oy.exports=n():"function"==typeof ty&&ty.amd?ty([],n):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=n()}(function(){return function f(i,u,a){function c(t,n){if(!u[t]){if(!i[t]){var e="function"==typeof ry&&ry;if(!n&&e)return e(t,!0);if(s)return s(t,!0);var o=new Error("Cannot find module '"+t+"'");throw o.code="MODULE_NOT_FOUND",o}var r=u[t]={exports:{}};i[t][0].call(r.exports,function(n){return c(i[t][1][n]||n)},r,r.exports,f,i,u,a)}return u[t].exports}for(var s="function"==typeof ry&&ry,n=0;n<a.length;n++)c(a[n]);return c}({1:[function(n,t,e){var o,r,i=t.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(n){if(o===setTimeout)return setTimeout(n,0);if((o===u||!o)&&setTimeout)return o=setTimeout,setTimeout(n,0);try{return o(n,0)}catch(t){try{return o.call(null,n,0)}catch(t){return o.call(this,n,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:u}catch(n){o=u}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(n){r=a}}();var s,f=[],l=!1,d=-1;function m(){l&&s&&(l=!1,s.length?f=s.concat(f):d=-1,f.length&&g())}function g(){if(!l){var n=c(m);l=!0;for(var t=f.length;t;){for(s=f,f=[];++d<t;)s&&s[d].run();d=-1,t=f.length}s=null,l=!1,function e(n){if(r===clearTimeout)return clearTimeout(n);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(n);try{return r(n)}catch(t){try{return r.call(null,n)}catch(t){return r.call(this,n)}}}(n)}}function p(n,t){this.fun=n,this.array=t}function h(){}i.nextTick=function(n){var t=new Array(arguments.length-1);if(1<arguments.length)for(var e=1;e<arguments.length;e++)t[e-1]=arguments[e];f.push(new p(n,t)),1!==f.length||l||c(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(n){return[]},i.binding=function(n){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(n){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(n,l,t){(function(t){function o(){}function i(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],f(n,this)}function r(o,r){for(;3===o._state;)o=o._value;0!==o._state?(o._handled=!0,i._immediateFn(function(){var n=1===o._state?r.onFulfilled:r.onRejected;if(null!==n){var t;try{t=n(o._value)}catch(e){return void a(r.promise,e)}u(r.promise,t)}else(1===o._state?u:a)(r.promise,o._value)})):o._deferreds.push(r)}function u(n,t){try{if(t===n)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if(t instanceof i)return n._state=3,n._value=t,void c(n);if("function"==typeof e)return void f(function o(n,t){return function(){n.apply(t,arguments)}}(e,t),n)}n._state=1,n._value=t,c(n)}catch(r){a(n,r)}}function a(n,t){n._state=2,n._value=t,c(n)}function c(n){2===n._state&&0===n._deferreds.length&&i._immediateFn(function(){n._handled||i._unhandledRejectionFn(n._value)});for(var t=0,e=n._deferreds.length;t<e;t++)r(n,n._deferreds[t]);n._deferreds=null}function s(n,t,e){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.promise=e}function f(n,t){var e=!1;try{n(function(n){e||(e=!0,u(t,n))},function(n){e||(e=!0,a(t,n))})}catch(o){if(e)return;e=!0,a(t,o)}}var n,e;n=this,e=setTimeout,i.prototype["catch"]=function(n){return this.then(null,n)},i.prototype.then=function(n,t){var e=new this.constructor(o);return r(this,new s(n,t,e)),e},i.all=function(n){var c=Array.prototype.slice.call(n);return new i(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},i.resolve=function(t){return t&&"object"==typeof t&&t.constructor===i?t:new i(function(n){n(t)})},i.reject=function(e){return new i(function(n,t){t(e)})},i.race=function(r){return new i(function(n,t){for(var e=0,o=r.length;e<o;e++)r[e].then(n,t)})},i._immediateFn="function"==typeof t?function(n){t(n)}:function(n){e(n,0)},i._unhandledRejectionFn=function(n){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",n)},i._setImmediateFn=function(n){i._immediateFn=n},i._setUnhandledRejectionFn=function(n){i._unhandledRejectionFn=n},void 0!==l&&l.exports?l.exports=i:n.Promise||(n.Promise=i)}).call(this,n("timers").setImmediate)},{timers:3}],3:[function(c,n,s){(function(n,t){var o=c("process/browser.js").nextTick,e=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function a(n,t){this._id=n,this._clearFn=t}s.setTimeout=function(){return new a(e.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new a(e.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(n){n.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(n,t){clearTimeout(n._idleTimeoutId),n._idleTimeout=t},s.unenroll=function(n){clearTimeout(n._idleTimeoutId),n._idleTimeout=-1},s._unrefActive=s.active=function(n){clearTimeout(n._idleTimeoutId);var t=n._idleTimeout;0<=t&&(n._idleTimeoutId=setTimeout(function(){n._onTimeout&&n._onTimeout()},t))},s.setImmediate="function"==typeof n?n:function(n){var t=u++,e=!(arguments.length<2)&&r.call(arguments,1);return i[t]=!0,o(function(){i[t]&&(e?n.apply(null,e):n.call(null),s.clearImmediate(t))}),t},s.clearImmediate="function"==typeof t?t:function(n){delete i[n]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(n,t,e){var o=n("promise-polyfill"),r="undefined"!=typeof window?window:Function("return this;")();t.exports={boltExport:r.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});function Sy(n){v.setTimeout(function(){throw n},0)}function Cy(n){var t=Ke(n);return vn(Hy,t)}function ky(n,t){var e=t.getRoot(n).getOr(n.element());dr(e,t.invalidClass),t.notify.each(function(t){Cy(n.element())&&Po(n.element(),"aria-invalid",!1),t.getContainer(n).each(function(n){No(n,t.validHtml)}),t.onValid(n)})}function Oy(t,n,e,o){var r=n.getRoot(t).getOr(t.element());fr(r,n.invalidClass),n.notify.each(function(n){Cy(t.element())&&Po(t.element(),"aria-invalid",!0),n.getContainer(t).each(function(n){No(n,o)}),n.onInvalid(t,o)})}function Ey(t,n,e){return n.validator.fold(function(){return Ny(an.value(!0))},function(n){return n.validate(t)})}function Ty(t,e,n){return e.notify.each(function(n){n.onValidate(t)}),Ey(t,e).map(function(n){return t.getSystem().isConnected()?n.fold(function(n){return Oy(t,e,0,n),an.error(n)},function(n){return ky(t,e),an.value(n)}):an.error("No longer in system")})}function By(n,t,e,o){var r=Xy(n,t,e,o);return vy.sketch(r)}function Dy(n,t){return vy.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}})}var Ay,_y,My=wy.exports.boltExport,Fy=function(n){var e=on.none(),t=[],o=function(n){r()?u(n):t.push(n)},r=function(){return e.isSome()},i=function(n){bn(n,u)},u=function(t){e.each(function(n){v.setTimeout(function(){t(n)},0)})};return n(function(n){e=on.some(n),i(t),t=[]}),{get:o,map:function(e){return Fy(function(t){o(function(n){t(e(n))})})},isReady:r}},Iy={nu:Fy,pure:function(t){return Fy(function(n){n(t)})}},Ry=function(e){function n(n){e().then(n,Sy)}return{map:function(n){return Ry(function(){return e().then(n)})},bind:function(t){return Ry(function(){return e().then(function(n){return t(n).toPromise()})})},anonBind:function(n){return Ry(function(){return e().then(function(){return n.toPromise()})})},toLazy:function(){return Iy.nu(n)},toCached:function(){var n=null;return Ry(function(){return null===n&&(n=e()),n})},toPromise:e,get:n}},Vy=function(n){return Ry(function(){return new My(n)})},Ny=function(n){return Ry(function(){return My.resolve(n)})},Hy=["input","textarea"],Py=/* */Object.freeze({markValid:ky,markInvalid:Oy,query:Ey,run:Ty,isInvalid:function(n,t){var e=t.getRoot(n).getOr(n.element());return mr(e,t.invalidClass)}}),zy=/* */Object.freeze({events:function(t,n){return t.validator.map(function(n){return co([lo(n.onEvent,function(n){Ty(n,t).get(l)})].concat(n.validateOnLoad?[Ii(function(n){Ty(n,t).get(Z)})]:[]))}).getOr({})}}),Ly=[ct("invalidClass"),St("getRoot",on.none),wt("notify",[St("aria","alert"),St("getContainer",on.none),St("validHtml",""),qu("onValid"),qu("onInvalid"),qu("onValidate")]),wt("validator",[ct("validate"),St("onEvent","input"),St("validateOnLoad",!0)])],jy=ya({fields:Ly,name:"invalidating",active:zy,apis:Py,extra:{validation:function(e){return function(n){var t=Zf.getValue(n);return Ny(e(t))}}}}),Uy=/* */Object.freeze({exhibit:function(n,t){return Zo({attributes:K([{key:t.tabAttr,value:"true"}])})}}),Wy=[St("tabAttr","data-alloy-tabstop")],Gy=ya({fields:Wy,name:"tabstopping",active:Uy}),Xy=function(n,t,e,o){return{dom:Yy(e),components:n.toArray().concat([t]),fieldBehaviours:ba(o)}},Yy=function(n){return{tag:"div",classes:["tox-form__group"].concat(n)}},qy=/* */Object.freeze({getCoupled:function(n,t,e,o){return e.getOrCreate(n,t,o)}}),Ky=[st("others",nt(an.value,de()))],Jy=ya({fields:Ky,name:"coupling",apis:qy,state:/* */Object.freeze({init:function(n){var i={},t=nn({});return nu({readState:t,getOrCreate:function(e,o,r){var n=wn(o.others);if(n)return Nn(i,r).getOrThunk(function(){var n=Nn(o.others,r).getOrDie("No information found for coupled component: "+r)(e),t=e.getSystem().build(n);return i[r]=t});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(n,null,2))}})}})}),$y=nn("sink"),Qy=nn(Sl({name:$y(),overrides:nn({dom:{tag:"div"},behaviours:ba([Af.config({useFixed:a})]),events:co([vo(ni()),vo(Yr()),vo(ri())])})}));(_y=Ay=Ay||{})[_y.HighlightFirst=0]="HighlightFirst",_y[_y.HighlightNone=1]="HighlightNone";function Zy(n,t){var e=n.getHotspot(t).getOr(t),o=n.getAnchorOverrides();return n.layouts.fold(function(){return{anchor:"hotspot",hotspot:e,overrides:o}},function(n){return{anchor:"hotspot",hotspot:e,overrides:o,layouts:n}})}function nx(n,t,e,o,r,i,u){return function(n,t,r,e,i,o,u){var a=function(n,t,e){return(0,n.fetch)(e).map(t)}(n,t,e),c=Sw(e,n);return a.map(function(n){return n.bind(function(n){return on.from(Lg.sketch(N(N({},o.menu()),{uid:Yo(""),data:n,highlightImmediately:u===Ay.HighlightFirst,onOpenMenu:function(n,t){var e=c().getOrDie();Af.position(e,r,t),zf.decloak(i)},onOpenSubmenu:function(n,t,e){var o=c().getOrDie();Af.position(o,{anchor:"submenu",item:t},e),zf.decloak(i)},onRepositionMenu:function(n,t,e){var o=c().getOrDie();Af.position(o,r,t),bn(e,function(n){Af.position(o,{anchor:"submenu",item:n.triggeringItem},n.triggeredMenu)})},onEscape:function(){return vg.focus(e),zf.close(i),on.some(!0)}})))})})}(n,t,Zy(n,e),e,o,r,u).map(function(n){return n.fold(function(){zf.isOpen(o)&&zf.close(o)},function(n){zf.cloak(o),zf.open(o,n),i(o)}),o})}function tx(n,t,e,o,r,i,u){return zf.close(o),Ny(o)}function ex(n,t,e,o,r,i){var u=Jy.getCoupled(e,"sandbox");return(zf.isOpen(u)?tx:nx)(n,t,e,u,o,r,i)}function ox(n,t,e){var o=Zl.getCurrent(t).getOr(t),r=mu(n.element());e?yr(o.element(),"min-width",r+"px"):function(n,t){Vu.set(n,t)}(o.element(),r)}function rx(n){zf.getState(n).each(function(n){Lg.repositionMenus(n)})}function ix(o,r,i){var u=Eu(),n=Sw(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id(),role:"listbox"}},behaviours:tl(o.sandboxBehaviours,[Zf.config({store:{mode:"memory",initialValue:r}}),zf.config({onOpen:function(n,t){var e=Zy(o,r);u.link(r.element()),o.matchWidth&&ox(e.hotspot,t,o.useMinWidth),o.onOpen(e,n,t),i!==undefined&&i.onOpen!==undefined&&i.onOpen(n,t)},onClose:function(n,t){u.unlink(r.element()),i!==undefined&&i.onClose!==undefined&&i.onClose(n,t)},isPartOf:function(n,t,e){return Lu(t,e)||Lu(r,e)},getAttachPoint:function(){return n().getOrDie()}}),Zl.config({find:function(n){return zf.getState(n).bind(function(n){return Zl.getCurrent(n)})}}),lc.config({channels:N(N({},Es({isExtraPart:nn(!1)})),Ts({isExtraPart:nn(!1),doReposition:rx}))})])}}function ux(n){var t=Jy.getCoupled(n,"sandbox");rx(t)}function ax(){return[St("sandboxClasses",[]),nl("sandboxBehaviours",[Zl,lc,zf,Zf])]}function cx(e,t,o){function r(n,t){ro(n,Bw,{value:t})}var n=vy.parts().field({factory:yy,inputClasses:["tox-textfield"],onSetValue:function(n){return jy.run(n).get(function(){})},inputBehaviours:ba([Gy.config({}),jy.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(n){return Eo(n.element())},notify:{onValid:function(n){var t=Zf.getValue(n);ro(n,Tw,{color:t})}},validator:{validateOnLoad:!1,validate:function(n){var t=Zf.getValue(n);if(0===t.length)return Ny(an.value(!0));var e=we.fromTag("span");yr(e,"background-color",t);var o=Sr(e,"background-color").fold(function(){return an.error("blah")},function(n){return an.value(t)});return Ny(o)}}})]),selectOnFocus:!1}),i=e.label.map(function(n){return Dy(n,t.providers)}),u=vm(function(e,o){return Ow.sketch({dom:e.dom,components:e.components,toggleClass:"mce-active",dropdownBehaviours:ba([Ew.config({}),Gy.config({})]),layouts:e.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:o.getSink,fetch:function(t){return Vy(function(n){return e.fetch(n)}).map(function(n){return on.from(ub(Dn(Tv(Xo("menu-value"),n,function(n){e.onItemAction(t,n)},e.columns,e.presets,Fh.CLOSE_ON_EXECUTE,function(){return!1},o.providers),{movement:Bv(e.columns,e.presets)})))})},parts:{menu:Sv(0,0,e.presets)}})}({dom:{tag:"span",attributes:{"aria-label":t.providers.translate("Color swatch")}},layouts:on.some({onRtl:function(){return[ua]},onLtr:function(){return[aa]}}),components:[],fetch:Uv.getFetch(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:function(n,e){u.getOpt(n).each(function(t){"custom"===e?o.colorPicker(function(n){n.fold(function(){return oo(t,Dw)},function(n){r(t,n),Vv(n)})},"#ffffff"):r(t,"remove"===e?"":e)})}},t));return vy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:i.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[n,u.asSpec()]}]),fieldBehaviours:ba([Kd("form-field-events",[lo(Tw,function(n,t){u.getOpt(n).each(function(n){yr(n.element(),"background-color",t.event().color())}),ro(n,uy,{name:e.name})}),lo(Bw,function(t,e){vy.getField(t).each(function(n){Zf.setValue(n,e.event().value()),Zl.getCurrent(t).each(vg.focus)})}),lo(Dw,function(t,n){vy.getField(t).each(function(n){Zl.getCurrent(t).each(vg.focus)})})])])})}function sx(n,t,e){return{hue:nn(n),saturation:nn(t),value:nn(e)}}function fx(t){return Sl({name:t+"-edge",overrides:function(n){return n.model.manager.edgeActions[t].fold(function(){return{}},function(o){return{events:co([mo(Ur(),o,[n]),mo(Yr(),o,[n]),mo(qr(),function(n,t,e){e.mouseIsDown.get()&&o(n,e)},[n])])}})}})}function lx(n){var t=n.event().raw();if(function(n){return-1!==n.type.indexOf("touch")}(t)){var e=t;return e.touches!==undefined&&1===e.touches.length?on.some(e.touches[0]).map(function(n){return Iu(n.clientX,n.clientY)}):on.none()}var o=t;return o.clientX!==undefined?on.some(o).map(function(n){return Iu(n.clientX,n.clientY)}):on.none()}function dx(n){return n.model.minX}function mx(n){return n.model.minY}function gx(n){return n.model.minX-1}function px(n){return n.model.minY-1}function hx(n){return n.model.maxX}function vx(n){return n.model.maxY}function bx(n){return n.model.maxX+1}function yx(n){return n.model.maxY+1}function xx(n,t,e){return t(n)-e(n)}function wx(n){return xx(n,hx,dx)}function Sx(n){return xx(n,vx,mx)}function Cx(n){return wx(n)/2}function kx(n){return Sx(n)/2}function Ox(n){return n.stepSize}function Ex(n){return n.snapToGrid}function Tx(n){return n.snapStart}function Bx(n){return n.rounded}function Dx(n,t){return n[t+"-edge"]!==undefined}function Ax(n){return Dx(n,"left")}function _x(n){return Dx(n,"right")}function Mx(n){return Dx(n,"top")}function Fx(n){return Dx(n,"bottom")}function Ix(n){return n.model.value.get()}function Rx(n){return{x:nn(n)}}function Vx(n){return{y:nn(n)}}function Nx(n,t){return{x:nn(n),y:nn(t)}}function Hx(n,t){ro(n,jw(),{value:t})}function Px(n,t,e,o){return n<t?n:e<n?e:n===t?t-1:Math.max(t,n-o)}function zx(n,t,e,o){return e<n?n:n<t?t:n===e?e+1:Math.min(e,n+o)}function Lx(n,t,e){return Math.max(t,Math.min(e,n))}function jx(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.step,u=n.snap,a=n.snapStart,c=n.rounded,s=n.hasMinEdge,f=n.hasMaxEdge,l=n.minBound,d=n.maxBound,m=n.screenRange,g=s?t-1:t,p=f?e+1:e;if(r<l)return g;if(d<r)return p;var h=function(n,t,e){return Math.min(e,Math.max(n,t))-t}(r,l,d),v=Lx(h/m*o+t,g,p);return u&&t<=v&&v<=e?function(u,e,a,c,n){return n.fold(function(){var n=u-e,t=Math.round(n/c)*c;return Lx(e+t,e-1,a+1)},function(n){var t=(u-n)%c,e=Math.round(t/c),o=Math.floor((u-n)/c),r=Math.floor((a-n)/c),i=n+Math.min(r,o+e)*c;return Math.max(n,i)})}(v,t,e,i,a):c?Math.round(v):v}function Ux(n){var t=n.min,e=n.max,o=n.range,r=n.value,i=n.hasMinEdge,u=n.hasMaxEdge,a=n.maxBound,c=n.maxOffset,s=n.centerMinEdge,f=n.centerMaxEdge;return r<t?i?0:s:e<r?u?a:f:(r-t)/o*c}function Wx(n){return n.element().dom().getBoundingClientRect()}function Gx(n,t){return n[t]}function Xx(n){var t=Wx(n);return Gx(t,Uw)}function Yx(n){var t=Wx(n);return Gx(t,"right")}function qx(n){var t=Wx(n);return Gx(t,"top")}function Kx(n){var t=Wx(n);return Gx(t,"bottom")}function Jx(n){var t=Wx(n);return Gx(t,"width")}function $x(n){var t=Wx(n);return Gx(t,"height")}function Qx(n,t,e){return(n+t)/2-e}function Zx(n,t){var e=Wx(n),o=Wx(t),r=Gx(e,Uw),i=Gx(e,"right"),u=Gx(o,Uw);return Qx(r,i,u)}function nw(n,t){var e=Wx(n),o=Wx(t),r=Gx(e,"top"),i=Gx(e,"bottom"),u=Gx(o,"top");return Qx(r,i,u)}function tw(n,t){ro(n,jw(),{value:t})}function ew(n){return{x:nn(n)}}function ow(n,t,e){var o={min:dx(t),max:hx(t),range:wx(t),value:e,step:Ox(t),snap:Ex(t),snapStart:Tx(t),rounded:Bx(t),hasMinEdge:Ax(t),hasMaxEdge:_x(t),minBound:Xx(n),maxBound:Yx(n),screenRange:Jx(n)};return jx(o)}function rw(e){return function(n,t){return function(n,t,e){var o=(0<n?zx:Px)(Ix(e).x(),dx(e),hx(e),Ox(e));return tw(t,ew(o)),on.some(o)}(e,n,t).map(function(){return!0})}}function iw(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=Jx(t),u=o.bind(function(n){return on.some(Zx(n,t))}).getOr(0),a=r.bind(function(n){return on.some(Zx(n,t))}).getOr(i),c={min:dx(n),max:hx(n),range:wx(n),value:e,hasMinEdge:Ax(n),hasMaxEdge:_x(n),minBound:Xx(t),minOffset:0,maxBound:Yx(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return Ux(c)}(t,i,e,o,r);return Xx(t)-Xx(n)+u}function uw(n,t){ro(n,jw(),{value:t})}function aw(n){return{y:nn(n)}}function cw(n,t,e){var o={min:mx(t),max:vx(t),range:Sx(t),value:e,step:Ox(t),snap:Ex(t),snapStart:Tx(t),rounded:Bx(t),hasMinEdge:Mx(t),hasMaxEdge:Fx(t),minBound:qx(n),maxBound:Kx(n),screenRange:$x(n)};return jx(o)}function sw(e){return function(n,t){return function(n,t,e){var o=(0<n?zx:Px)(Ix(e).y(),mx(e),vx(e),Ox(e));return uw(t,aw(o)),on.some(o)}(e,n,t).map(function(){return!0})}}function fw(n,t,e,o,r,i){var u=function(t,n,e,o,r){var i=$x(t),u=o.bind(function(n){return on.some(nw(n,t))}).getOr(0),a=r.bind(function(n){return on.some(nw(n,t))}).getOr(i),c={min:mx(n),max:vx(n),range:Sx(n),value:e,hasMinEdge:Mx(n),hasMaxEdge:Fx(n),minBound:qx(t),minOffset:0,maxBound:Kx(t),maxOffset:i,centerMinEdge:u,centerMaxEdge:a};return Ux(c)}(t,i,e,o,r);return qx(t)-qx(n)+u}function lw(n,t){ro(n,jw(),{value:t})}function dw(n,t){return{x:nn(n),y:nn(t)}}function mw(e,o){return function(n,t){return function(n,t,e,o){var r=0<n?zx:Px,i=t?Ix(o).x():r(Ix(o).x(),dx(o),hx(o),Ox(o)),u=t?r(Ix(o).y(),mx(o),vx(o),Ox(o)):Ix(o).y();return lw(e,dw(i,u)),on.some(i)}(e,o,n,t).map(function(){return!0})}}function gw(n){return"<alloy.field."+n+">"}function pw(n){return function(n){return CS[n]}(n)}function hw(n,t,e){return Zf.config(Dn({store:{mode:"manual",getValue:t,setValue:e}},n.map(function(n){return{store:{initialValue:n}}}).getOr({})))}function vw(n,t,e){return hw(n,function(n){return t(n.element())},function(n,t){return e(n.element(),t)})}function bw(e,t){function o(n,t){t.stop()}function r(n){return function(t,e){bn(n,function(n){n(t,e)})}}function i(n,t){if(!Ch.isDisabled(n)){var e=t.event().raw();a(n,e.dataTransfer.files)}}function u(n,t){var e=t.event().raw().target.files;a(n,e)}var a=function(n,t){Zf.setValue(n,function(n){var t=new RegExp("("+".jpg,.jpeg,.png,.gif".split(/\s*,\s*/).join("|")+")$","i");return C(xn(n),function(n){return t.test(n.name)})}(t)),ro(n,uy,{name:e.name})},c=vm({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:ba([Kd("input-file-events",[vo(ri()),vo(gi())])])}),n=e.label.map(function(n){return Dy(n,t)}),s=vy.parts().field({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:ba([DS([]),xS(),Ch.config({}),Cg.config({toggleClass:"dragenter",toggleOnExecute:!1}),Kd("dropzone-events",[lo("dragenter",r([o,Cg.toggle])),lo("dragleave",r([o,Cg.toggle])),lo("dragover",o),lo("drop",r([o,i])),lo(oi(),u)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:t.translate("Drop an image here")}},Gg.sketch({dom:{tag:"button",innerHtml:t.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(n){c.get(n).element().dom().click()},buttonBehaviours:ba([Gy.config({})])})]}]}}}});return By(n,s,["tox-form__group--stretched"],[])}function yw(n){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:n},behaviours:ba([vg.config({ignore:!0}),Gy.config({})])}}function xw(n,t){ro(n,ni(),{raw:{which:9,shiftKey:t}})}function ww(n,t){var e=RS&&n.sandboxed,o=N(N({},n.label.map(function(n){return{title:n}}).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),r=function(o){var r=ye("");return{getValue:function(n){return r.get()},setValue:function(n,t){if(o)Po(n.element(),"srcdoc",t);else{Po(n.element(),"src","javascript:''");var e=n.element().dom().contentWindow.document;e.open(),e.write(t),e.close()}r.set(t)}}}(e),i=n.label.map(function(n){return Dy(n,t)}),u=vy.parts().field({factory:{sketch:function(n){return IS({uid:n.uid,dom:{tag:"iframe",attributes:o},behaviours:ba([Gy.config({}),vg.config({}),TS(on.none(),r.getValue,r.setValue)])})}}});return By(i,u,["tox-form__group--stretched"],[])}var Sw=function(t,n){return t.getSystem().getByUid(n.uid+"-"+$y()).map(function(n){return function(){return an.value(n)}}).getOrThunk(function(){return n.lazySink.fold(function(){return function(){return an.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(n){return function(){return n(t)}})})},Cw=nn([ct("dom"),ct("fetch"),qu("onOpen"),Ku("onExecute"),St("getHotspot",on.some),St("getAnchorOverrides",nn({})),St("layouts",on.none()),Fs("dropdownBehaviours",[Cg,Jy,lg,vg]),ct("toggleClass"),St("eventOrder",{}),ht("lazySink"),St("matchWidth",!1),St("useMinWidth",!1),ht("role")].concat(ax())),kw=nn([wl({schema:[Gu()],name:"menu",defaults:function(n){return{onExecute:n.onExecute}}}),Qy()]),Ow=Al({name:"Dropdown",configFields:Cw(),partFields:kw(),factory:function(t,n,e,o){function r(n){zf.getState(n).each(function(n){Lg.highlightPrimary(n)})}function i(n,t){return io(n),on.some(!0)}var u,a,c={expand:function(n){Cg.isOn(n)||ex(t,function(n){return n},n,o,Z,Ay.HighlightNone).get(Z)},open:function(n){Cg.isOn(n)||ex(t,function(n){return n},n,o,Z,Ay.HighlightFirst).get(Z)},isOpen:Cg.isOn,close:function(n){Cg.isOn(n)&&ex(t,function(n){return n},n,o,Z,Ay.HighlightFirst).get(Z)},repositionMenus:function(n){Cg.isOn(n)&&ux(n)}};return{uid:t.uid,dom:t.dom,components:n,behaviours:Rs(t.dropdownBehaviours,[Cg.config({toggleClass:t.toggleClass,aria:{mode:"expanded"}}),Jy.config({others:{sandbox:function(n){return ix(t,n,{onOpen:function(){Cg.on(n)},onClose:function(){Cg.off(n)}})}}}),lg.config({mode:"special",onSpace:i,onEnter:i,onDown:function(n,t){if(Ow.isOpen(n)){var e=Jy.getCoupled(n,"sandbox");r(e)}else Ow.open(n);return on.some(!0)},onEscape:function(n,t){return Ow.isOpen(n)?(Ow.close(n),on.some(!0)):on.none()}}),vg.config({})]),events:rm(on.some(function(n){ex(t,function(n){return n},n,o,r,Ay.HighlightFirst).get(Z)})),eventOrder:N(N({},t.eventOrder),(u={},u[di()]=["disabling","toggling","alloy.base.behaviour"],u)),apis:c,domModification:{attributes:N(N({"aria-haspopup":"true"},t.role.fold(function(){return{}},function(n){return{role:n}})),"button"===t.dom.tag?{type:(a="type",Nn(t.dom,"attributes").bind(function(n){return Nn(n,a)})).getOr("button")}:{})}}},apis:{open:function(n,t){return n.open(t)},expand:function(n,t){return n.expand(t)},close:function(n,t){return n.close(t)},isOpen:function(n,t){return n.isOpen(t)},repositionMenus:function(n,t){return n.repositionMenus(t)}}}),Ew=ya({fields:[],name:"unselecting",active:/* */Object.freeze({events:function(n){return co([so(ui(),nn(!0))])},exhibit:function(n,t){return Zo({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),Tw=Xo("color-input-change"),Bw=Xo("color-swatch-change"),Dw=Xo("color-picker-cancel"),Aw=nn(Xo("rgb-hex-update")),_w=nn(Xo("slider-update")),Mw=nn(Xo("palette-update")),Fw=Sl({schema:[ct("dom")],name:"label"}),Iw=fx("top-left"),Rw=fx("top"),Vw=fx("top-right"),Nw=fx("right"),Hw=fx("bottom-right"),Pw=fx("bottom"),zw=fx("bottom-left"),Lw=[Fw,fx("left"),Nw,Rw,Pw,Iw,Vw,zw,Hw,xl({name:"thumb",defaults:nn({dom:{styles:{position:"absolute"}}}),overrides:function(n){return{events:co([po(Ur(),n,"spectrum"),po(Wr(),n,"spectrum"),po(Gr(),n,"spectrum"),po(Yr(),n,"spectrum"),po(qr(),n,"spectrum"),po(Jr(),n,"spectrum")])}}}),xl({schema:[At("mouseIsDown",function(){return ye(!1)})],name:"spectrum",overrides:function(e){function o(t,n){return r.getValueFromEvent(n).map(function(n){return r.setValueFrom(t,e,n)})}var r=e.model.manager;return{behaviours:ba([lg.config({mode:"special",onLeft:function(n){return r.onLeft(n,e)},onRight:function(n){return r.onRight(n,e)},onUp:function(n){return r.onUp(n,e)},onDown:function(n){return r.onDown(n,e)}}),vg.config({})]),events:co([lo(Ur(),o),lo(Wr(),o),lo(Yr(),o),lo(qr(),function(n,t){e.mouseIsDown.get()&&o(n,t)})])}}})],jw=nn("slider.change.value"),Uw="left",Ww=rw(-1),Gw=rw(1),Xw=on.none,Yw=on.none,qw={"top-left":on.none(),top:on.none(),"top-right":on.none(),right:on.some(function(n,t){Hx(n,Rx(bx(t)))}),"bottom-right":on.none(),bottom:on.none(),"bottom-left":on.none(),left:on.some(function(n,t){Hx(n,Rx(gx(t)))})},Kw=/* */Object.freeze({setValueFrom:function(n,t,e){var o=ow(n,t,e),r=ew(o);return tw(n,r),o},setToMin:function(n,t){var e=dx(t);tw(n,ew(e))},setToMax:function(n,t){var e=hx(t);tw(n,ew(e))},findValueOfOffset:ow,getValueFromEvent:function(n){return lx(n).map(function(n){return n.left()})},findPositionOfValue:iw,setPositionFromValue:function(n,t,e,o){var r=Ix(e),i=iw(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=mu(t.element())/2;yr(t.element(),"left",i-u+"px")},onLeft:Ww,onRight:Gw,onUp:Xw,onDown:Yw,edgeActions:qw}),Jw=on.none,$w=on.none,Qw=sw(-1),Zw=sw(1),nS={"top-left":on.none(),top:on.some(function(n,t){Hx(n,Vx(px(t)))}),"top-right":on.none(),right:on.none(),"bottom-right":on.none(),bottom:on.some(function(n,t){Hx(n,Vx(yx(t)))}),"bottom-left":on.none(),left:on.none()},tS=/* */Object.freeze({setValueFrom:function(n,t,e){var o=cw(n,t,e),r=aw(o);return uw(n,r),o},setToMin:function(n,t){var e=mx(t);uw(n,aw(e))},setToMax:function(n,t){var e=vx(t);uw(n,aw(e))},findValueOfOffset:cw,getValueFromEvent:function(n){return lx(n).map(function(n){return n.top()})},findPositionOfValue:fw,setPositionFromValue:function(n,t,e,o){var r=Ix(e),i=fw(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),u=su(t.element())/2;yr(t.element(),"top",i-u+"px")},onLeft:Jw,onRight:$w,onUp:Qw,onDown:Zw,edgeActions:nS}),eS=mw(-1,!1),oS=mw(1,!1),rS=mw(-1,!0),iS=mw(1,!0),uS={"top-left":on.some(function(n,t){Hx(n,Nx(gx(t),px(t)))}),top:on.some(function(n,t){Hx(n,Nx(Cx(t),px(t)))}),"top-right":on.some(function(n,t){Hx(n,Nx(bx(t),px(t)))}),right:on.some(function(n,t){Hx(n,Nx(bx(t),kx(t)))}),"bottom-right":on.some(function(n,t){Hx(n,Nx(bx(t),yx(t)))}),bottom:on.some(function(n,t){Hx(n,Nx(Cx(t),yx(t)))}),"bottom-left":on.some(function(n,t){Hx(n,Nx(gx(t),yx(t)))}),left:on.some(function(n,t){Hx(n,Nx(gx(t),kx(t)))})},aS=/* */Object.freeze({setValueFrom:function(n,t,e){var o=ow(n,t,e.left()),r=cw(n,t,e.top()),i=dw(o,r);return lw(n,i),i},setToMin:function(n,t){var e=dx(t),o=mx(t);lw(n,dw(e,o))},setToMax:function(n,t){var e=hx(t),o=vx(t);lw(n,dw(e,o))},getValueFromEvent:function(n){return lx(n)},setPositionFromValue:function(n,t,e,o){var r=Ix(e),i=iw(n,o.getSpectrum(n),r.x(),o.getLeftEdge(n),o.getRightEdge(n),e),u=fw(n,o.getSpectrum(n),r.y(),o.getTopEdge(n),o.getBottomEdge(n),e),a=mu(t.element())/2,c=su(t.element())/2;yr(t.element(),"left",i-a+"px"),yr(t.element(),"top",u-c+"px")},onLeft:eS,onRight:oS,onUp:rS,onDown:iS,edgeActions:uS}),cS=Al({name:"Slider",configFields:[St("stepSize",1),St("onChange",Z),St("onChoose",Z),St("onInit",Z),St("onDragStart",Z),St("onDragEnd",Z),St("snapToGrid",!1),St("rounded",!0),ht("snapStart"),st("model",it("mode",{x:[St("minX",0),St("maxX",100),At("value",function(n){return ye(n.mode.minX)}),ct("getInitialValue"),Qu("manager",Kw)],y:[St("minY",0),St("maxY",100),At("value",function(n){return ye(n.mode.minY)}),ct("getInitialValue"),Qu("manager",tS)],xy:[St("minX",0),St("maxX",100),St("minY",0),St("maxY",100),At("value",function(n){return ye({x:nn(n.mode.minX),y:nn(n.mode.minY)})}),ct("getInitialValue"),Qu("manager",aS)]})),Fs("sliderBehaviours",[lg,Zf]),At("mouseIsDown",function(){return ye(!1)})],partFields:Lw,factory:function(i,n,t,e){function u(n){return Ks(n,i,"thumb")}function a(n){return Ks(n,i,"spectrum")}function o(n){return qs(n,i,"left-edge")}function r(n){return qs(n,i,"right-edge")}function c(n){return qs(n,i,"top-edge")}function s(n){return qs(n,i,"bottom-edge")}function f(n,t){v.setPositionFromValue(n,t,i,{getLeftEdge:o,getRightEdge:r,getTopEdge:c,getBottomEdge:s,getSpectrum:a})}function l(n,t){h.value.set(t);var e=u(n);return f(n,e),i.onChange(n,e,t),on.some(!0)}function d(e){var n=i.mouseIsDown.get();i.mouseIsDown.set(!1),n&&qs(e,i,"thumb").each(function(n){var t=h.value.get();i.onChoose(e,n,t)})}function m(n,t){t.stop(),i.mouseIsDown.set(!0),i.onDragStart(n,u(n))}function g(n,t){t.stop(),i.onDragEnd(n,u(n)),d(n)}var p,h=i.model,v=h.manager;return{uid:i.uid,dom:i.dom,components:n,behaviours:Rs(i.sliderBehaviours,[lg.config({mode:"special",focusIn:function(n){return qs(n,i,"spectrum").map(lg.focusIn).map(nn(!0))}}),Zf.config({store:{mode:"manual",getValue:function(n){return h.value.get()}}}),lc.config({channels:(p={},p[Uf()]={onReceive:d},p)})]),events:co([lo(jw(),function(n,t){l(n,t.event().value())}),Ii(function(n,t){var e=h.getInitialValue();h.value.set(e);var o=u(n);f(n,o);var r=a(n);i.onInit(n,o,r,h.value.get())}),lo(Ur(),m),lo(Gr(),g),lo(Yr(),m),lo(Jr(),g)]),apis:{resetToMin:function(n){v.setToMin(n,i)},resetToMax:function(n){v.setToMax(n,i)},changeValue:l,refresh:f},domModification:{styles:{position:"relative"}}}},apis:{resetToMin:function(n,t){n.resetToMin(t)},resetToMax:function(n,t){n.resetToMax(t)},refresh:function(n,t){n.refresh(t)}}}),sS=function(n,t){var e=cS.parts().spectrum({dom:{tag:"div",classes:[t("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=cS.parts().thumb({dom:{tag:"div",classes:[t("hue-slider-thumb")],attributes:{role:"presentation"}}});return cS.sketch({dom:{tag:"div",classes:[t("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:nn({y:nn(0)})},components:[e,o],sliderBehaviours:ba([vg.config({})]),onChange:function(n,t,e){ro(n,_w(),{value:e})}})},fS=[Fs("formBehaviours",[Zf])],lS=function(o,n,t){return{uid:o.uid,dom:o.dom,components:n,behaviours:Rs(o.formBehaviours,[Zf.config({store:{mode:"manual",getValue:function(n){var t=$s(n,o);return P(t,function(n,t){return n().bind(function(n){return function(n,t){return n.fold(function(){return an.error(t)},an.value)}(Zl.getCurrent(n),"missing current")}).map(Zf.getValue)})},setValue:function(e,n){Cn(n,function(t,n){qs(e,o,n).each(function(n){Zl.getCurrent(n).each(function(n){Zf.setValue(n,t)})})})}}})]),apis:{getField:function(n,t){return qs(n,o,t).bind(Zl.getCurrent)}}}},dS={getField:$o(function(n,t,e){return n.getField(t,e)}),sketch:function(n){var e,t=(e=[],{field:function(n,t){return e.push(n),Us("form",gw(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=S(r,function(n){return xl({name:n,pname:gw(n)})});return of("form",fS,i,lS,o)}},mS=Xo("valid-input"),gS=Xo("invalid-input"),pS=Xo("validating-input"),hS="colorcustom.rgb.",vS=function(d,m,g,p){function h(n,t,e,o,r){var i=d(hS+"range"),u=[vy.parts().label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),vy.parts().field({data:r,factory:yy,inputAttributes:N({type:"text"},"hex"===t?{"aria-live":"polite"}:{}),inputClasses:[m("textfield")],inputBehaviours:ba([function(t,o){return jy.config({invalidClass:m("invalid"),notify:{onValidate:function(n){ro(n,pS,{type:t})},onValid:function(n){ro(n,mS,{type:t,value:Zf.getValue(n)})},onInvalid:function(n){ro(n,gS,{type:t,value:Zf.getValue(n)})}},validator:{validate:function(n){var t=Zf.getValue(n),e=o(t)?an.value(!0):an.error(d("aria.input.invalid"));return Ny(e)},validateOnLoad:!1}})}(t,n),Gy.config({})]),onSetValue:function(n){jy.isInvalid(n)&&jy.run(n).get(Z)}})],a="hex"!==t?[vy.parts()["aria-descriptor"]({text:i})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:u.concat(a)}}function v(n,t){var e=t.red(),o=t.green(),r=t.blue();Zf.setValue(n,{red:e,green:o,blue:r})}function b(n,t){y.getOpt(n).each(function(n){yr(n.element(),"background-color","#"+t.value())})}var y=vm({dom:{tag:"div",classes:[m("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}});return Dl({factory:function(){function r(n){return u[n]().get()}function i(n,t){u[n]().set(t)}function t(n,t){var e=t.event();"hex"!==e.type()?i(e.type(),on.none()):p(n)}function o(e,n,t){var o=parseInt(t,10);i(n,on.some(o)),r("red").bind(function(e){return r("green").bind(function(t){return r("blue").map(function(n){return Wh(e,t,n,1)})})}).each(function(n){var t=function(t,n){var e=Uh(n);return dS.getField(t,"hex").each(function(n){vg.isFocused(n)||Zf.setValue(t,{hex:e.value()})}),e}(e,n);b(e,t)})}function e(n,t){var e=t.event();!function(n){return"hex"===n.type()}(e)?o(n,e.type(),e.value()):function(n,t){g(n);var e=Ph(t);i("hex",on.some(t));var o=Yh(e);v(n,o),a(o),ro(n,Aw(),{hex:e}),b(n,e)}(n,e.value())}function n(n){return{label:d(hS+n+".label"),description:d(hS+n+".description")}}var u={red:nn(ye(on.some(255))),green:nn(ye(on.some(255))),blue:nn(ye(on.some(255))),hex:nn(ye(on.some("ffffff")))},a=function(n){var t=n.red(),e=n.green(),o=n.blue();i("red",on.some(t)),i("green",on.some(e)),i("blue",on.some(o))},c=n("red"),s=n("green"),f=n("blue"),l=n("hex");return Dn(dS.sketch(function(n){return{dom:{tag:"form",classes:[m("rgb-form")],attributes:{"aria-label":d("aria.color.picker")}},components:[n.field("red",vy.sketch(h(Gh,"red",c.label,c.description,255))),n.field("green",vy.sketch(h(Gh,"green",s.label,s.description,255))),n.field("blue",vy.sketch(h(Gh,"blue",f.label,f.description,255))),n.field("hex",vy.sketch(h(zh,"hex",l.label,l.description,"ffffff"))),y.asSpec()],formBehaviours:ba([jy.config({invalidClass:m("form-invalid")}),Kd("rgb-form-events",[lo(mS,e),lo(gS,t),lo(pS,t)])])}}),{apis:{updateHex:function(n,t){Zf.setValue(n,{hex:t.value()}),function(n,t){var e=Yh(t);v(n,e),a(e)}(n,t),b(n,t)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(n,t,e){n.updateHex(t,e)}},extraApis:{}})},bS=function(n,o){function r(n,t){var e=n.width,o=n.height,r=n.getContext("2d");if(null!==r){r.fillStyle=t,r.fillRect(0,0,e,o);var i=r.createLinearGradient(0,0,e,0);i.addColorStop(0,"rgba(255,255,255,1)"),i.addColorStop(1,"rgba(255,255,255,0)"),r.fillStyle=i,r.fillRect(0,0,e,o);var u=r.createLinearGradient(0,0,0,o);u.addColorStop(0,"rgba(0,0,0,0)"),u.addColorStop(1,"rgba(0,0,0,1)"),r.fillStyle=u,r.fillRect(0,0,e,o)}}var i=cS.parts().spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),u=cS.parts().thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}});return Dl({factory:function(n){var t=nn({x:nn(0),y:nn(0)}),e=ba([Zl.config({find:on.some}),vg.config({})]);return cS.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:t},rounded:!1,components:[i,u],onChange:function(n,t,e){ro(n,Mw(),{value:e})},onInit:function(n,t,e,o){r(e.element().dom(),Kh(ov()))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setRgba:function(n,t,e){!function(n,t){var e=n.components()[0].element().dom();r(e,Kh(t))}(t,e)}},extraApis:{}})},yS=function(l,d){return Dl({name:"ColourPicker",configFields:[ct("dom"),St("onValidHex",Z),St("onInvalidHex",Z)],factory:function(n){function t(n,e){u.getOpt(n).each(function(n){var t=Yh(e);s.paletteRgba().set(t),i.setRgba(n,t)})}function e(n,t){f.getOpt(n).each(function(n){r.updateHex(n,t)})}function a(t,e,n){bn(n,function(n){n(t,e)})}var o,c,r=vS(l,d,n.onValidHex,n.onInvalidHex),i=bS(l,d),s={paletteRgba:nn(ye(ov()))},u=vm(i.sketch({})),f=vm(r.sketch({}));return{uid:n.uid,dom:n.dom,components:[u.asSpec(),sS(l,d),f.asSpec()],behaviours:ba([Kd("colour-picker-events",[lo(Mw(),(c=[e],function(n,t){var e=t.event().value(),o=function(n){var t,e=0,o=0,r=n.red()/255,i=n.green()/255,u=n.blue()/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));return a===c?sx(0,0,100*(o=a)):(e=60*((e=r===a?3:u===a?1:5)-(r===a?i-u:u===a?r-i:u-r)/(c-a)),t=(c-a)/c,o=c,sx(Math.round(e),Math.round(100*t),Math.round(100*o)))}(s.paletteRgba().get()),r=sx(o.hue(),e.x(),100-e.y()),i=Xh(r),u=Uh(i);a(n,u,c)})),lo(_w(),(o=[t,e],function(n,t){var e=function(n){var t=sx((100-n)/100*360,100,100),e=Xh(t);return Uh(e)}(t.event().value().y());a(n,e,o)}))]),Zl.config({find:function(n){return f.getOpt(n)}}),lg.config({mode:"acyclic"})])}}})},xS=function(){return Zl.config({find:on.some})},wS=function(n){return Zl.config({find:n.getOpt})},SS=function(n){return Zl.config({find:function(t){return Do(t.element(),n).bind(function(n){return t.getSystem().getByDom(n).toOption()})}})},CS={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},kS=tinymce.util.Tools.resolve("tinymce.Resource"),OS=re([St("preprocess",l),St("postprocess",l)]),ES=function(r,n){var i=ot("RepresentingConfigs.memento processors",OS,n);return Zf.config({store:{mode:"manual",getValue:function(n){var t=r.get(n),e=Zf.getValue(t);return i.postprocess(e)},setValue:function(n,t){var e=i.preprocess(t),o=r.get(n);Zf.setValue(o,e)}}})},TS=hw,BS=function(n){return vw(n,Vo,No)},DS=function(n){return Zf.config({store:{mode:"memory",initialValue:n}})},AS=Xo("alloy-fake-before-tabstop"),_S=Xo("alloy-fake-after-tabstop"),MS=function(n){return lb(n,["."+AS,"."+_S].join(","),nn(!1))},FS=function(n,t){var e=t.element();mr(e,AS)?xw(n,!0):mr(e,_S)&&xw(n,!1)},IS=function(n){return{dom:{tag:"div",classes:["tox-navobj"]},components:[yw([AS]),n,yw([_S])],behaviours:ba([SS(1)])}},RS=!(Ht().browser.isIE()||Ht().browser.isEdge());function VS(n,t){return PS(v.document.createElement("canvas"),n,t)}function NS(n){var t=VS(n.width,n.height);return HS(t).drawImage(n,0,0),t}function HS(n){return n.getContext("2d")}function PS(n,t,e){return n.width=t,n.height=e,n}function zS(n){return n.naturalWidth||n.width}function LS(n){return n.naturalHeight||n.height}var jS,US,WS=window.Promise?window.Promise:(jS=GS.immediateFn||"function"==typeof window.setImmediate&&window.setImmediate||function(n){v.setTimeout(n,1)},US=Array.isArray||function(n){return"[object Array]"===Object.prototype.toString.call(n)},GS.prototype["catch"]=function(n){return this.then(null,n)},GS.prototype.then=function(e,o){var r=this;return new GS(function(n,t){YS.call(r,new $S(e,o,n,t))})},GS.all=function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];var c=Array.prototype.slice.call(1===n.length&&US(n[0])?n[0]:n);return new GS(function(r,i){if(0===c.length)return r([]);var u=c.length;function a(t,n){try{if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if("function"==typeof e)return void e.call(n,function(n){a(t,n)},i)}c[t]=n,0==--u&&r(c)}catch(o){i(o)}}for(var n=0;n<c.length;n++)a(n,c[n])})},GS.resolve=function(t){return t&&"object"==typeof t&&t.constructor===GS?t:new GS(function(n){n(t)})},GS.reject=function(e){return new GS(function(n,t){t(e)})},GS.race=function(r){return new GS(function(n,t){for(var e=0,o=r;e<o.length;e++)o[e].then(n,t)})},GS);function GS(n){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof n)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],QS(n,XS(qS,this),XS(KS,this))}function XS(n,t){return function(){return n.apply(t,arguments)}}function YS(o){var r=this;null!==this._state?jS(function(){var n=r._state?o.onFulfilled:o.onRejected;if(null!==n){var t;try{t=n(r._value)}catch(e){return void o.reject(e)}o.resolve(t)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function qS(n){try{if(n===this)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var t=n.then;if("function"==typeof t)return void QS(XS(t,n),XS(qS,this),XS(KS,this))}this._state=!0,this._value=n,JS.call(this)}catch(e){KS.call(this,e)}}function KS(n){this._state=!1,this._value=n,JS.call(this)}function JS(){for(var n=0,t=this._deferreds;n<t.length;n++){var e=t[n];YS.call(this,e)}this._deferreds=[]}function $S(n,t,e,o){this.onFulfilled="function"==typeof n?n:null,this.onRejected="function"==typeof t?t:null,this.resolve=e,this.reject=o}function QS(n,t,e){var o=!1;try{n(function(n){o||(o=!0,t(n))},function(n){o||(o=!0,e(n))})}catch(r){if(o)return;o=!0,e(r)}}function ZS(e){return new WS(function(n,t){(function p(n){var t=n.split(","),e=/data:([^;]+)/.exec(t[0]);if(!e)return on.none();for(var o=e[1],r=t[1],i=v.atob(r),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var f=1024*s,l=Math.min(1024+f,u),d=new Array(l-f),m=f,g=0;m<l;++g,++m)d[g]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return on.some(new v.Blob(c,{type:o}))})(e).fold(function(){t("uri is not base64: "+e)},n)})}function nC(n,o,r){return o=o||"image/png",v.HTMLCanvasElement.prototype.toBlob?new WS(function(t,e){n.toBlob(function(n){n?t(n):e()},o,r)}):ZS(n.toDataURL(o,r))}function tC(n){return function t(a){return new WS(function(n,t){var e=v.URL.createObjectURL(a),o=new v.Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)};function i(){r(),n(o)}function u(){r(),t("Unable to load data of type "+a.type+": "+e)}o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&i()})}(n).then(function(n){!function e(n){v.URL.revokeObjectURL(n.src)}(n);var t=VS(zS(n),LS(n));return HS(t).drawImage(n,0,0),t})}function eC(n,t,e){var o=t.type;function r(t,e){return n.then(function(n){return function o(n,t,e){return t=t||"image/png",n.toDataURL(t,e)}(n,t,e)})}return{getType:nn(o),toBlob:function i(){return WS.resolve(t)},toDataURL:function u(){return e},toBase64:function a(){return e.split(",")[1]},toAdjustedBlob:function c(t,e){return n.then(function(n){return nC(n,t,e)})},toAdjustedDataURL:r,toAdjustedBase64:function s(n,t){return r(n,t).then(function(n){return n.split(",")[1]})},toCanvas:function f(){return n.then(NS)}}}function oC(t){return function n(e){return new WS(function(n){var t=new v.FileReader;t.onloadend=function(){n(t.result)},t.readAsDataURL(e)})}(t).then(function(n){return eC(tC(t),t,n)})}function rC(t,n){return nC(t,n).then(function(n){return eC(WS.resolve(t),n,t.toDataURL())})}function iC(n,t,e){var o="string"==typeof n?parseFloat(n):n;return e<o?o=e:o<t&&(o=t),o}var uC=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10];function aC(n,t){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=t[u+5*i];for(u=0;u<5;u++){for(var a=e=0;a<5;a++)e+=n[u+5*a]*o[a];r[u+5*i]=e}}return r}function cC(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=HS(n);var r=function B(n,t){for(var e,o,r,i,u=n.data,a=t[0],c=t[1],s=t[2],f=t[3],l=t[4],d=t[5],m=t[6],g=t[7],p=t[8],h=t[9],v=t[10],b=t[11],y=t[12],x=t[13],w=t[14],S=t[15],C=t[16],k=t[17],O=t[18],E=t[19],T=0;T<u.length;T+=4)e=u[T],o=u[T+1],r=u[T+2],i=u[T+3],u[T]=e*a+o*c+r*s+i*f+l,u[T+1]=e*d+o*m+r*g+i*p+h,u[T+2]=e*v+o*b+r*y+i*x+w,u[T+3]=e*S+o*C+r*k+i*O+E;return n}(o.getImageData(0,0,n.width,n.height),e);return o.putImageData(r,0,0),rC(n,t)}(n,t.getType(),e)})}function sC(t,e){return t.toCanvas().then(function(n){return function u(n,t,e){var o=HS(n);var r=o.getImageData(0,0,n.width,n.height),i=o.getImageData(0,0,n.width,n.height);return i=function w(n,t,e){function o(n,t,e){return e<n?n=e:n<t&&(n=t),n}for(var r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=n.data,a=t.data,c=n.width,s=n.height,f=0;f<s;f++)for(var l=0;l<c;l++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(l+h-i,0,c-1),b=4*(o(f+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(f*c+l);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return t}(r,i,e),o.putImageData(i,0,0),rC(n,t)}(n,t.getType(),e)})}function fC(e){return function(n,t){return cC(n,e([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t))}}function lC(n,t,e,o){return cC(n,function r(n,t,e,o){return aC(n,[t=iC(t,0,2),0,0,0,0,0,e=iC(e,0,2),0,0,0,0,0,o=iC(o,0,2),0,0,0,0,0,1,0,0,0,0,0,1])}([1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1],t,e,o))}var dC=function cI(t){return function(n){return cC(n,t)}}([-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1]),mC=fC(function sI(n,t){return aC(n,[1,0,0,0,t=iC(255*t,-255,255),0,1,0,0,t,0,0,1,0,t,0,0,0,1,0,0,0,0,0,1])}),gC=fC(function fI(n,t){var e;return t=iC(t,-1,1),aC(n,[(e=(t*=100)<0?127+t/100*127:127*(e=0===(e=t%1)?uC[t]:uC[Math.floor(t)]*(1-e)+uC[Math.floor(t)+1]*e)+127)/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),pC=function lI(t){return function(n){return sC(n,t)}}([0,-1,0,-1,5,-1,0,-1,0]),hC=function dI(c){return function(t,e){return t.toCanvas().then(function(n){return function(n,t,e){var o=HS(n),r=new Array(256);for(var i=0;i<r.length;i++)r[i]=c(i,e);var u=function a(n,t){for(var e=n.data,o=0;o<e.length;o+=4)e[o]=t[e[o]],e[o+1]=t[e[o+1]],e[o+2]=t[e[o+2]];return n}(o.getImageData(0,0,n.width,n.height),r);return o.putImageData(u,0,0),rC(n,t)}(n,t.getType(),e)})}}(function(n,t){return 255*Math.pow(n/255,1-t)});function vC(n,t,e){var o=zS(n),r=LS(n),i=t/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=function s(u,a,c){return new WS(function(n){var t=zS(u),e=LS(u),o=Math.floor(t*a),r=Math.floor(e*c),i=VS(o,r);HS(i).drawImage(u,0,0,t,e,0,0,o,r),n(i)})}(n,i,u);return a?c.then(function(n){return vC(n,t,e)}):c}function bC(t,e){return t.toCanvas().then(function(n){return function a(n,t,e){var o=VS(n.width,n.height),r=HS(o),i=0,u=0;90!==(e=e<0?360+e:e)&&270!==e||PS(o,o.height,o.width);90!==e&&180!==e||(i=o.width);270!==e&&180!==e||(u=o.height);return r.translate(i,u),r.rotate(e*Math.PI/180),r.drawImage(n,0,0),rC(o,t)}(n,t.getType(),e)})}function yC(t,e){return t.toCanvas().then(function(n){return function i(n,t,e){var o=VS(n.width,n.height),r=HS(o);"v"===e?(r.scale(1,-1),r.drawImage(n,0,-o.height)):(r.scale(-1,1),r.drawImage(n,-o.width,0));return rC(o,t)}(n,t.getType(),e)})}function xC(t,e,o,r,i){return t.toCanvas().then(function(n){return function a(n,t,e,o,r,i){var u=VS(r,i);return HS(u).drawImage(n,-e,-o),rC(u,t)}(n,t.getType(),e,o,r,i)})}function wC(n){return dC(n)}function SC(n){return pC(n)}function CC(n,t){return hC(n,t)}function kC(n,t){return mC(n,t)}function OC(n,t){return gC(n,t)}function EC(n,t){return yC(n,t)}function TC(n,t,e){return function r(t,e,o){return t.toCanvas().then(function(n){return vC(n,e,o).then(function(n){return rC(n,t.getType())})})}(n,t,e)}function BC(n,t){return bC(n,t)}function DC(n,t){return N({dom:{tag:"span",innerHtml:n,classes:["tox-icon","tox-tbtn__icon-wrap"]}},t)}function AC(n,t){return DC(ym(n,t),{})}function _C(n,t){return DC(ym(n,t),{behaviours:ba([mg.config({})])})}function MC(n,t,e){return{dom:{tag:"span",innerHtml:e.translate(n),classes:[t+"__select-label"]},behaviours:ba([mg.config({})])}}function FC(n,t,o){function e(n,t){var e=Zf.getValue(n);return vg.focus(e),ro(e,"keydown",{raw:t.event().raw()}),Ow.close(e),on.some(!0)}var r=ye(Z),i=n.text.map(function(n){return vm(MC(n,t,o.providers))}),u=n.icon.map(function(n){return vm(_C(n,o.providers.icons))}),a=n.role.fold(function(){return{}},function(n){return{role:n}}),c=n.tooltip.fold(function(){return{}},function(n){var t=o.providers.translate(n);return{title:t,"aria-label":t}});return vm(Ow.sketch(N(N({},a),{dom:{tag:"button",classes:[t,t+"--select"].concat(S(n.classes,function(n){return t+"--"+n})),attributes:N({},c)},components:Bh([u.map(function(n){return n.asSpec()}),i.map(function(n){return n.asSpec()}),on.some({dom:{tag:"div",classes:[t+"__select-chevron"],innerHtml:ym("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:ba(g(n.dropdownBehaviours,[Oh(n.disabled),Ew.config({}),mg.config({}),Kd("dropdown-events",[Ep(n,r),Tp(n,r)]),Kd("menubutton-update-display-text",[lo(rk,function(t,e){i.bind(function(n){return n.getOpt(t)}).each(function(n){mg.set(n,[Ir(o.providers.translate(e.event().text()))])})}),lo(ik,function(t,e){u.bind(function(n){return n.getOpt(t)}).each(function(n){mg.set(n,[_C(e.event().icon(),o.providers.icons)])})})])])),eventOrder:Dn(ok,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:ba([lg.config({mode:"special",onLeft:e,onRight:e})]),lazySink:o.getSink,toggleClass:t+"--active",parts:{menu:Sv(0,n.columns,n.presets)},fetch:function(){return Vy(n.fetch)}}))).asSpec()}function IC(n){return"separator"===n.type}function RC(n,e){var t=O(n,function(n,t){return function(n){return cn(n)}(t)?""===t?n:"|"===t?0<n.length&&!IC(n[n.length-1])?n.concat([uk]):n:En(e,t.toLowerCase())?n.concat([e[t.toLowerCase()]]):n:n.concat([t])},[]);return 0<t.length&&IC(t[t.length-1])&&t.pop(),t}function VC(n,t){return function(n){return En(n,"getSubmenuItems")}(n)?function(n,t){var e=n.getSubmenuItems(),o=ak(e,t);return{item:n,menus:Dn(o.menus,q(n.value,o.items)),expansions:Dn(o.expansions,q(n.value,n.value))}}(n,t):{item:n,menus:{},expansions:{}}}function NC(n,e,o,t){var r=Xo("primary-menu"),i=ak(n,o.shared.providers.menuItems());if(0===i.items.length)return on.none();var u=ib(r,i.items,e,o,t),a=P(i.menus,function(n,t){return ib(t,n,e,o,!1)}),c=Dn(a,q(r,u));return on.from(Lg.tieredData(r,c,i.expansions))}function HC(e){return{isDisabled:function(){return Ch.isDisabled(e)},setDisabled:function(n){return Ch.set(e,n)},setActive:function(n){var t=e.element();n?(fr(t,"tox-tbtn--enabled"),Po(t,"aria-pressed",!0)):(dr(t,"tox-tbtn--enabled"),jo(t,"aria-pressed"))},isActive:function(){return mr(e.element(),"tox-tbtn--enabled")}}}function PC(n,t,e,o){return FC({text:n.text,icon:n.icon,tooltip:n.tooltip,role:o,fetch:function(t){n.fetch(function(n){t(NC(n,Fh.CLOSE_ON_EXECUTE,e,!1))})},onSetup:n.onSetup,getApi:HC,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Gy.config({})]},t,e.shared)}function zC(t,o,r){return function(n){n(S(t,function(n){var t=n.text.fold(function(){return{}},function(n){return{text:n}});return N(N({type:n.type},t),{onAction:function(e){return function(n){var t=!n.isActive();n.setActive(t),e.storage.set(t),r.shared.getSink().each(function(n){o().getOpt(n).each(function(n){wa(n.element()),ro(n,sy,{name:e.name,value:e.storage.get()})})})}}(n),onSetup:function(t){return function(n){n.setActive(t.storage.get())}}(n)})}))}}function LC(n,t,e,o,r){void 0===e&&(e=[]);var i=t.fold(function(){return{}},function(n){return{action:n}}),u=N({buttonBehaviours:ba([Oh(n.disabled),Gy.config({}),Kd("button press",[fo("click"),fo("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},i),a=Dn(u,{dom:o});return Dn(a,{components:r})}function jC(n,t,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:n.tooltip.map(function(n){return{"aria-label":e.translate(n),title:e.translate(n)}}).getOr({})},i=n.icon.map(function(n){return AC(n,e.icons)}),u=Bh([i]);return LC(n,t,o,r,u)}function UC(n,t,e,o){void 0===o&&(o=[]);var r=jC(n,on.some(t),e,o);return Gg.sketch(r)}function WC(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(n.text),u=n.icon?n.icon.map(function(n){return AC(n,e.icons)}):on.none(),a=u.isSome()?Bh([u]):[],c=u.isSome()?{}:{innerHtml:i},s=g(n.primary||n.borderless?["tox-button"]:["tox-button","tox-button--secondary"],u.isSome()?["tox-button--icon"]:[],n.borderless?["tox-button--naked"]:[],r),f=N(N({tag:"button",classes:s},c),{attributes:{title:i}});return LC(n,t,o,f,a)}function GC(n,t,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=WC(n,on.some(t),e,o,r);return Gg.sketch(i)}function XC(t,e){return function(n){"custom"===e?ro(n,sy,{name:t,value:{}}):"submit"===e?oo(n,fy):"cancel"===e?oo(n,cy):v.console.error("Unknown button type: ",e)}}function YC(n,t,e){if(function(n,t){return"menu"===t}(0,t)){var o=n,r=N(N({},n),{fetch:zC(o.items,function(){return i},e)}),i=vm(PC(r,"tox-tbtn",e,on.none()));return i.asSpec()}if(function(n,t){return"custom"===t||"cancel"===t||"submit"===t}(0,t)){var u=XC(n.name,t),a=N(N({},n),{borderless:!1});return GC(a,u,e.shared.providers,[])}v.console.error("Unknown footer button type: ",t)}function qC(n,t){var e=XC(n.name,"custom");return function(n,t){return By(n,t,[],[])}(on.none(),vy.parts().field(N({factory:Gg},WC(n,on.some(e),t,[DS(""),xS()]))))}function KC(n,t){return xl({factory:vy,name:n,overrides:function(o){return{fieldBehaviours:ba([Kd("coupled-input-behaviour",[lo(ei(),function(e){(function(n,t,e){return qs(n,t,e).bind(Zl.getCurrent)})(e,o,t).each(function(t){qs(e,o,"lock").each(function(n){Cg.isOn(n)&&o.onLockedChange(e,t,n)})})})])])}}})}function JC(n){var t=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(n);if(null===t)return an.error(n);var e=parseFloat(t[1]),o=t[2];return an.value({value:e,unit:o})}function $C(n,t){function e(n){return Object.prototype.hasOwnProperty.call(o,n)}var o={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1};return n.unit===t?on.some(n.value):e(n.unit)&&e(t)?o[n.unit]===o[t]?on.some(n.value):on.some(n.value/o[n.unit]*o[t]):on.none()}function QC(n){return on.none()}function ZC(n,t){return function(n,t,e){return n.isSome()&&t.isSome()?on.some(e(n.getOrDie(),t.getOrDie())):on.none()}(JC(n).toOption(),JC(t).toOption(),function(n,t){return $C(n,t.unit).map(function(n){return t.value/n}).map(function(n){return function(t,e){return function(n){return $C(n,e).map(function(n){return{value:n*t,unit:e}})}}(n,t.unit)}).getOr(QC)}).getOr(QC)}function nk(o,t){function n(n){return{dom:{tag:"div",classes:["tox-form__group"]},components:n}}function e(e){return vy.parts().field({factory:yy,inputClasses:["tox-textfield"],inputBehaviours:ba([Ch.config({disabled:o.disabled}),Gy.config({}),Kd("size-input-events",[lo(Qr(),function(n,t){ro(n,i,{isField1:e})}),lo(oi(),function(n,t){ro(n,uy,{name:o.name})})])]),selectOnFocus:!1})}function r(n){return{dom:{tag:"label",classes:["tox-label"],innerHtml:t.translate(n)}}}var a=QC,i=Xo("ratio-event"),u=fk.parts().lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:t.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:ym("lock",t.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:ym("unlock",t.icons)}}],buttonBehaviours:ba([Oh(o.disabled),Gy.config({})])}),c=fk.parts().field1(n([vy.parts().label(r("Width")),e(!0)])),s=fk.parts().field2(n([vy.parts().label(r("Height")),e(!1)]));return fk.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,n([r("&nbsp;"),u])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(n,t,e){JC(Zf.getValue(n)).each(function(n){a(n).each(function(n){Zf.setValue(t,function(n){var t,e={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},o=n.value.toFixed((t=n.unit)in e?e[t]:1);return-1!==o.indexOf(".")&&(o=o.replace(/\.?0*$/,"")),o+n.unit}(n))})})},coupledFieldBehaviours:ba([Ch.config({disabled:o.disabled,onDisabled:function(n){fk.getField1(n).bind(vy.getField).each(Ch.disable),fk.getField2(n).bind(vy.getField).each(Ch.disable),fk.getLock(n).each(Ch.disable)},onEnabled:function(n){fk.getField1(n).bind(vy.getField).each(Ch.enable),fk.getField2(n).bind(vy.getField).each(Ch.enable),fk.getLock(n).each(Ch.enable)}}),Kd("size-input-events2",[lo(i,function(n,t){var e=t.event().isField1(),o=e?fk.getField1(n):fk.getField2(n),r=e?fk.getField2(n):fk.getField1(n),i=o.map(Zf.getValue).getOr(""),u=r.map(Zf.getValue).getOr("");a=ZC(i,u)})])])})}function tk(r,c){function n(n,t,e,o){return vm(GC({name:n,text:n,disabled:e,primary:o,icon:on.none(),borderless:!1},t,c))}function t(n,t,e,o){return vm(UC({name:n,icon:on.some(n),tooltip:on.some(t),disabled:o,primary:!1,borderless:!1},e,c))}function u(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(Ch)&&Ch.disable(t)})}function a(n,e){n.map(function(n){var t=n.get(e);t.hasConfigured(Ch)&&Ch.enable(t)})}function i(n,t,e){ro(n,t,e)}function e(n){return oo(n,pk.disable())}function o(n){return oo(n,pk.enable())}function s(n,t){e(n),i(n,lk.transform(),{transform:t}),o(n)}function f(n){return function(){Q.getOpt(n).each(function(n){mg.set(n,[J])})}}function l(n,t){e(n),i(n,lk.transformApply(),{transform:t,swap:f(n)}),o(n)}function d(){return n("Back",function(n){return i(n,lk.back(),{swap:f(n)})},!1,!1)}function m(){return vm({dom:{tag:"div",classes:["tox-spacer"]},behaviours:ba([Ch.config({})])})}function g(){return n("Apply",function(n){return i(n,lk.apply(),{swap:f(n)})},!0,!0)}function p(){return function(n){var t=r.getRect();return function(n,t,e,o,r){return xC(n,t,e,o,r)}(n,t.x,t.y,t.w,t.h)}}function h(t,e){return function(n){return t(n,e)}}function v(n,t){!function(n,t){e(n),i(n,lk.tempTransform(),{transform:t}),o(n)}(n,t)}function b(n,t,e,o,r){var i=cS.parts().label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(n)}}),u=cS.parts().spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=cS.parts().thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return vm(cS.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:nn({x:nn(o)})},components:[i,u,a],sliderBehaviours:ba([vg.config({})]),onChoose:t}))}function y(n,t,e,o,r){return[d(),function(n,r,t,e,o){return b(n,function(n,t,e){var o=h(r,e.x()/100);s(n,o)},t,e,o)}(n,t,e,o,r),g()]}function x(n,t,e,o,r){var i=y(n,t,e,o,r);return Wb.sketch({dom:k,components:i.map(function(n){return n.asSpec()}),containerBehaviours:ba([Kd("image-tools-filter-panel-buttons-events",[lo(pk.disable(),function(n,t){u(i,n)}),lo(pk.enable(),function(n,t){a(i,n)})])])})}function w(t,e,o){return function(n){return function(n,t,e,o){return lC(n,t,e,o)}(n,t,e,o)}}function S(n){return b(n,function(a,n,t){var e=j.getOpt(a),o=W.getOpt(a),r=U.getOpt(a);e.each(function(u){o.each(function(i){r.each(function(n){var t=Zf.getValue(u).x()/100,e=Zf.getValue(n).x()/100,o=Zf.getValue(i).x()/100,r=w(t,e,o);s(a,r)})})})},0,100,200)}function C(t,e,o){return function(n){i(n,lk.swap(),{transform:e,swap:function(){Q.getOpt(n).each(function(n){mg.set(n,[t]),o(n)})}})}}var k={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},O=Z,E=[d(),m(),n("Apply",function(n){var t=p();l(n,t),r.hideCrop()},!1,!0)],T=Wb.sketch({dom:k,components:E.map(function(n){return n.asSpec()}),containerBehaviours:ba([Kd("image-tools-crop-buttons-events",[lo(pk.disable(),function(n,t){u(E,n)}),lo(pk.enable(),function(n,t){a(E,n)})])])}),B=vm(nk({name:"size",label:on.none(),constrain:!0,disabled:!1},c)),D=[d(),m(),B,m(),n("Apply",function(o){B.getOpt(o).each(function(n){var t=Zf.getValue(n),e=function(t,e){return function(n){return TC(n,t,e)}}(parseInt(t.width,10),parseInt(t.height,10));l(o,e)})},!1,!0)],A=Wb.sketch({dom:k,components:D.map(function(n){return n.asSpec()}),containerBehaviours:ba([Kd("image-tools-resize-buttons-events",[lo(pk.disable(),function(n,t){u(D,n)}),lo(pk.enable(),function(n,t){a(D,n)})])])}),_=h(EC,"h"),M=h(EC,"v"),F=h(BC,-90),I=h(BC,90),R=[d(),m(),t("flip-horizontally","Flip horizontally",function(n){v(n,_)},!1),t("flip-vertically","Flip vertically",function(n){v(n,M)},!1),t("rotate-left","Rotate counterclockwise",function(n){v(n,F)},!1),t("rotate-right","Rotate clockwise",function(n){v(n,I)},!1),m(),g()],V=Wb.sketch({dom:k,components:R.map(function(n){return n.asSpec()}),containerBehaviours:ba([Kd("image-tools-fliprotate-buttons-events",[lo(pk.disable(),function(n,t){u(R,n)}),lo(pk.enable(),function(n,t){a(R,n)})])])}),N=[d(),m(),g()],H=Wb.sketch({dom:k,components:N.map(function(n){return n.asSpec()})}),P=x("Brightness",kC,-100,0,100),z=x("Contrast",OC,-100,0,100),L=x("Gamma",CC,-100,0,100),j=S("R"),U=S("G"),W=S("B"),G=[d(),j,U,W,g()],X=Wb.sketch({dom:k,components:G.map(function(n){return n.asSpec()})}),Y=on.some(SC),q=on.some(wC),K=[t("crop","Crop",C(T,on.none(),function(n){r.showCrop()}),!1),t("resize","Resize",C(A,on.none(),function(n){B.getOpt(n).each(function(n){var t=r.getMeasurements(),e=t.width,o=t.height;Zf.setValue(n,{width:e,height:o})})}),!1),t("orientation","Orientation",C(V,on.none(),O),!1),t("brightness","Brightness",C(P,on.none(),O),!1),t("sharpen","Sharpen",C(H,Y,O),!1),t("contrast","Contrast",C(z,on.none(),O),!1),t("color-levels","Color levels",C(X,on.none(),O),!1),t("gamma","Gamma",C(L,on.none(),O),!1),t("invert","Invert",C(H,q,O),!1)],J=Wb.sketch({dom:k,components:K.map(function(n){return n.asSpec()})}),$=Wb.sketch({dom:{tag:"div"},components:[J],containerBehaviours:ba([mg.config({})])}),Q=vm($);return{memContainer:Q,getApplyButton:function(n){return Q.getOpt(n).map(function(n){var t=n.components()[0];return t.components()[t.components().length-1]})}}}var ek=Xo("toolbar.button.execute"),ok={"alloy.execute":["disabling","alloy.base.behaviour","toggling","toolbar-button-events"]},rk=Xo("update-menu-text"),ik=Xo("update-menu-icon"),uk={type:"separator"},ak=function(n,r){var t=RC(cn(n)?n.split(" "):n,r);return k(t,function(n,t){var e=function(n){if(IC(n))return n;var t=Nn(n,"value").getOrThunk(function(){return Xo("generated-menu-item")});return Dn({value:t},n)}(t),o=VC(e,r);return{menus:Dn(n.menus,o.menus),items:[o.item].concat(n.items),expansions:Dn(n.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},ck=nn([St("field1Name","field1"),St("field2Name","field2"),Ju("onLockedChange"),Xu(["lockClass"]),St("locked",!1),nl("coupledFieldBehaviours",[Zl,Zf])]),sk=nn([KC("field1","field2"),KC("field2","field1"),xl({factory:Gg,schema:[ct("dom")],name:"lock",overrides:function(n){return{buttonBehaviours:ba([Cg.config({selected:n.locked,toggleClass:n.markers.lockClass,aria:{mode:"pressed"}})])}}})]),fk=Al({name:"FormCoupledInputs",configFields:ck(),partFields:sk(),factory:function(o,n,t,e){return{uid:o.uid,dom:o.dom,components:n,behaviours:tl(o.coupledFieldBehaviours,[Zl.config({find:on.some}),Zf.config({store:{mode:"manual",getValue:function(n){var t,e=Zs(n,o,["field1","field2"]);return(t={})[o.field1Name]=Zf.getValue(e.field1()),t[o.field2Name]=Zf.getValue(e.field2()),t},setValue:function(n,t){var e=Zs(n,o,["field1","field2"]);$(t,o.field1Name)&&Zf.setValue(e.field1(),t[o.field1Name]),$(t,o.field2Name)&&Zf.setValue(e.field2(),t[o.field2Name])}}})]),apis:{getField1:function(n){return qs(n,o,"field1")},getField2:function(n){return qs(n,o,"field2")},getLock:function(n){return qs(n,o,"lock")}}}},apis:{getField1:function(n,t){return n.getField1(t)},getField2:function(n,t){return n.getField2(t)},getLock:function(n,t){return n.getLock(t)}}}),lk={undo:nn(Xo("undo")),redo:nn(Xo("redo")),zoom:nn(Xo("zoom")),back:nn(Xo("back")),apply:nn(Xo("apply")),swap:nn(Xo("swap")),transform:nn(Xo("transform")),tempTransform:nn(Xo("temp-transform")),transformApply:nn(Xo("transform-apply"))},dk=nn("save-state"),mk=nn("disable"),gk=nn("enable"),pk={formActionEvent:sy,saveState:dk,disable:mk,enable:gk},hk=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),vk=tinymce.util.Tools.resolve("tinymce.geom.Rect"),bk=tinymce.util.Tools.resolve("tinymce.util.Observable"),yk=tinymce.util.Tools.resolve("tinymce.util.Tools"),xk=tinymce.util.Tools.resolve("tinymce.util.VK");function wk(n){var t,e;if(n.changedTouches)for(t="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<t.length;e++)n[t[e]]=n.changedTouches[0][t[e]]}function Sk(n,r){var i,u,t,a,c,f,l,d=r.document||v.document;r=r||{};var m=d.getElementById(r.handle||n);t=function(n){var t,e,o=function s(n){var t,e,o,r,i,u,a,c=Math.max;return t=n.documentElement,e=n.body,o=c(t.scrollWidth,e.scrollWidth),r=c(t.clientWidth,e.clientWidth),i=c(t.offsetWidth,e.offsetWidth),u=c(t.scrollHeight,e.scrollHeight),a=c(t.clientHeight,e.clientHeight),{width:o<i?r:o,height:u<c(t.offsetHeight,e.offsetHeight)?a:u}}(d);wk(n),n.preventDefault(),u=n.button,t=m,f=n.screenX,l=n.screenY,e=v.window.getComputedStyle?v.window.getComputedStyle(t,null).getPropertyValue("cursor"):t.runtimeStyle.cursor,i=hk("<div></div>").css({position:"absolute",top:0,left:0,width:o.width,height:o.height,zIndex:2147483647,opacity:1e-4,cursor:e}).appendTo(d.body),hk(d).on("mousemove touchmove",c).on("mouseup touchend",a),r.start(n)},c=function(n){if(wk(n),n.button!==u)return a(n);n.deltaX=n.screenX-f,n.deltaY=n.screenY-l,n.preventDefault(),r.drag(n)},a=function(n){wk(n),hk(d).off("mousemove touchmove",c).off("mouseup touchend",a),i.remove(),r.stop&&r.stop(n)},this.destroy=function(){hk(m).off()},hk(m).on("mousedown touchstart",t)}function Ck(t){function u(n,s){c.getOpt(n).each(function(n){var e=l.get(),o=mu(n.element()),r=su(n.element()),i=s.dom().naturalWidth*e,u=s.dom().naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),t={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};xr(s,t),f.getOpt(n).each(function(n){xr(n.element(),t)}),d.get().each(function(n){var t=m.get();n.setRect({x:t.x*e+a,y:t.y*e+c,w:t.w*e,h:t.h*e}),n.setClampRect({x:a,y:c,w:i,h:u}),n.setViewPortRect({x:0,y:0,w:o,h:r})})})}function e(n,t){var i=we.fromTag("img");return Po(i,"src",t),function(e){return new Np(function(n){var t=function(){e.removeEventListener("load",t),n(e)};e.complete?n(e):e.addEventListener("load",t)})}(i.dom()).then(function(){return c.getOpt(n).map(function(n){var t=ru({element:i});mg.replaceAt(n,1,on.some(t));var e=a.get(),o={x:0,y:0,w:i.dom().naturalWidth,h:i.dom().naturalHeight};a.set(o);var r=vk.inflate(o,-20,-20);return m.set(r),e.w===o.w&&e.h===o.h||function(n,u){c.getOpt(n).each(function(n){var t=mu(n.element()),e=su(n.element()),o=u.dom().naturalWidth,r=u.dom().naturalHeight,i=Math.min(t/o,e/r);1<=i?l.set(1):l.set(i)})}(n,i),u(n,i),i})})}var f=vm({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),l=ye(1),d=ye(on.none()),m=ye({x:0,y:0,w:1,h:1}),a=ye({x:0,y:0,w:1,h:1}),n=Wb.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[f.asSpec(),{dom:{tag:"img",attributes:{src:t}}},{dom:{tag:"div"},behaviours:ba([Kd("image-panel-crop-events",[Ii(function(n){c.getOpt(n).each(function(n){var t=n.element().dom(),e=Tk({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},t,function(){});e.toggleVisibility(!1),e.on("updateRect",function(n){var t=n.rect,e=l.get(),o={x:Math.round(t.x/e),y:Math.round(t.y/e),w:Math.round(t.w/e),h:Math.round(t.h/e)};m.set(o)}),d.set(on.some(e))})})])])}],containerBehaviours:ba([mg.config({}),Kd("image-panel-events",[Ii(function(n){e(n,t)})])])}),c=vm(n);return{memContainer:c,updateSrc:e,zoom:function(n,t){var e=l.get(),o=0<t?Math.min(2,e+.1):Math.max(.1,e-.1);l.set(o),c.getOpt(n).each(function(n){var t=n.components()[1].element();u(n,t)})},showCrop:function(){d.get().each(function(n){n.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(n){n.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var n=a.get();return{width:n.w,height:n.h}}}}function kk(n,t,e,o,r){return UC({name:n,icon:on.some(t),disabled:e,tooltip:on.some(n),primary:!1,borderless:!1},o,r)}function Ok(n,t){t?Ch.enable(n):Ch.disable(n)}var Ek=0,Tk=function(s,e,f,o,r){var l,t,i,u="tox-",a="tox-crid-"+Ek++,c=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}];i=["top","right","bottom","left"];var d=function(n,t){return{x:t.x+n.x,y:t.y+n.y,w:t.w,h:t.h}},m=function(n,t){return{x:t.x-n.x,y:t.y-n.y,w:t.w,h:t.h}};function g(n,t,e,o){var r,i,u,a,c;r=t.x,i=t.y,u=t.w,a=t.h,r+=e*n.deltaX,i+=o*n.deltaY,(u+=e*n.deltaW)<20&&(u=20),(a+=o*n.deltaH)<20&&(a=20),c=s=vk.clamp({x:r,y:i,w:u,h:a},f,"move"===n.name),c=m(f,c),l.fire("updateRect",{rect:c}),v(c)}function p(t){function n(n,t){t.h<0&&(t.h=0),t.w<0&&(t.w=0),hk("#"+a+"-"+n,o).css({left:t.x,top:t.y,width:t.w,height:t.h})}yk.each(c,function(n){hk("#"+a+"-"+n.name,o).css({left:t.w*n.xMul+t.x,top:t.h*n.yMul+t.y})}),n("top",{x:e.x,y:e.y,w:e.w,h:t.y-e.y}),n("right",{x:t.x+t.w,y:t.y,w:e.w-t.x-t.w+e.x,h:t.h}),n("bottom",{x:e.x,y:t.y+t.h,w:e.w,h:e.h-t.y-t.h+e.y}),n("left",{x:e.x,y:t.y,w:t.x-e.x,h:t.h}),n("move",t)}function h(n){p(s=n)}function v(n){h(d(f,n))}return function b(){hk('<div id="'+a+'" class="'+u+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),yk.each(i,function(n){hk("#"+a,o).append('<div id="'+a+"-"+n+'"class="'+u+'croprect-block" style="display: none" data-mce-bogus="all">')}),yk.each(c,function(n){hk("#"+a,o).append('<div id="'+a+"-"+n.name+'" class="'+u+"croprect-handle "+u+"croprect-handle-"+n.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+n.label+'" aria-grabbed="false" title="'+n.label+'">')}),t=yk.map(c,function n(t){var e;return new Sk(a,{document:o.ownerDocument,handle:a+"-"+t.name,start:function(){e=s},drag:function(n){g(t,e,n.deltaX,n.deltaY)}})}),p(s),hk(o).on("focusin focusout",function(n){hk(n.target).attr("aria-grabbed","focus"===n.type?"true":"false")}),hk(o).on("keydown",function(t){var i;function n(n,t,e,o,r){n.stopPropagation(),n.preventDefault(),g(i,e,o,r)}switch(yk.each(c,function(n){if(t.target.id===a+"-"+n.name)return i=n,!1}),t.keyCode){case xk.LEFT:n(t,0,s,-10,0);break;case xk.RIGHT:n(t,0,s,10,0);break;case xk.UP:n(t,0,s,0,-10);break;case xk.DOWN:n(t,0,s,0,10);break;case xk.ENTER:case xk.SPACEBAR:t.preventDefault(),r()}})}(),l=yk.extend({toggleVisibility:function y(n){var t;t=yk.map(c,function(n){return"#"+a+"-"+n.name}).concat(yk.map(i,function(n){return"#"+a+"-"+n})).join(","),n?hk(t,o).show():hk(t,o).hide()},setClampRect:function x(n){f=n,p(s)},setRect:h,getInnerRect:function(){return m(f,s)},setInnerRect:v,setViewPortRect:function w(n){e=n,p(s)},destroy:function n(){yk.each(t,function(n){n.destroy()}),t=[]}},bk)};function Bk(n){var t=ye(n),e=ye(on.none()),o=function s(){var e=[],o=-1;function n(){return 0<o}function t(){return-1!==o&&o<e.length-1}return{data:e,add:function r(n){var t;return t=e.splice(++o),e.push(n),{state:n,removed:t}},undo:function i(){if(n())return e[--o]},redo:function u(){if(t())return e[++o]},canUndo:n,canRedo:t}}();function r(n){t.set(n)}function i(n){v.URL.revokeObjectURL(n.url)}function u(n){var t=a(n);return r(t),function(n){yk.each(n,i)}(o.add(t).removed),t.url}o.add(n);var a=function(n){return{blob:n,url:v.URL.createObjectURL(n)}},c=function(){e.get().each(i),e.set(on.none())};return{getBlobState:function(){return t.get()},setBlobState:r,addBlobState:u,getTempState:function(){return e.get().fold(function(){return t.get()},function(n){return n})},updateTempState:function(n){var t=a(n);return c(),e.set(on.some(t)),t.url},addTempState:function(n){var t=a(n);return e.set(on.some(t)),t.url},applyTempState:function(t){return e.get().fold(function(){},function(n){u(n.blob),t()})},destroyTempState:c,undo:function(){var n=o.undo();return r(n),n.url},redo:function(){var n=o.redo();return r(n),n.url},getHistoryStates:function(){return{undoEnabled:o.canUndo(),redoEnabled:o.canRedo()}}}}function Dk(n,t){function i(n){var t=s.getHistoryStates();m.updateButtonUndoStates(n,t.undoEnabled,t.redoEnabled),ro(n,pk.formActionEvent,{name:pk.saveState(),value:t.undoEnabled})}function u(n){return n.toBlob()}function a(n){ro(n,pk.formActionEvent,{name:pk.disable(),value:{}})}function r(t,n,e,o,r){return a(t),function(n){return oC(n)}(n).then(e).then(u).then(o).then(function(n){return l(t,n).then(function(n){return i(t),r(),f(t),n})})["catch"](function(n){return v.console.log(n),f(t),n})}function c(n,t,e){var o=s.getBlobState().blob;r(n,o,t,function(n){return s.updateTempState(n)},e)}var s=Bk(n.currentState),f=function(n){e.getApplyButton(n).each(function(n){Ch.enable(n)}),ro(n,pk.formActionEvent,{name:pk.enable(),value:{}})},l=function(n,t){return a(n),o.updateSrc(n,t)},d=function(n){var t=s.getBlobState().url;return s.destroyTempState(),i(n),t},o=Ck(n.currentState.url),m=function(n){var o=vm(kk("Undo","undo",!0,function(n){ro(n,lk.undo(),{direction:1})},n)),r=vm(kk("Redo","redo",!0,function(n){ro(n,lk.redo(),{direction:1})},n));return{container:Wb.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),kk("Zoom in","zoom-in",!1,function(n){ro(n,lk.zoom(),{direction:1})},n),kk("Zoom out","zoom-out",!1,function(n){ro(n,lk.zoom(),{direction:-1})},n)]}),updateButtonUndoStates:function(n,t,e){o.getOpt(n).each(function(n){Ok(n,t)}),r.getOpt(n).each(function(n){Ok(n,e)})}}}(t),e=tk(o,t);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[e.memContainer.asSpec(),o.memContainer.asSpec(),m.container],behaviours:ba([Zf.config({store:{mode:"manual",getValue:function(){return s.getBlobState()}}}),Kd("image-tools-events",[lo(lk.undo(),function(t,n){var e=s.undo();l(t,e).then(function(n){f(t),i(t)})}),lo(lk.redo(),function(t,n){var e=s.redo();l(t,e).then(function(n){f(t),i(t)})}),lo(lk.zoom(),function(n,t){var e=t.event().direction();o.zoom(n,e)}),lo(lk.back(),function(n,t){!function(t){var n=d(t);l(t,n).then(function(n){f(t)})}(n),t.event().swap()(),o.hideCrop()}),lo(lk.apply(),function(n,t){s.applyTempState(function(){d(n),t.event().swap()()})}),lo(lk.transform(),function(n,t){return c(n,t.event().transform(),Z)}),lo(lk.tempTransform(),function(n,t){return function(n,t){var e=s.getTempState().blob;r(n,e,t,function(n){return s.addTempState(n)},Z)}(n,t.event().transform())}),lo(lk.transformApply(),function(n,t){return function(e,n,t){var o=s.getBlobState().blob;r(e,o,n,function(n){var t=s.addBlobState(n);return d(e),t},t)}(n,t.event().transform(),t.event().swap())}),lo(lk.swap(),function(t,n){!function(n){m.updateButtonUndoStates(n,!1,!1)}(t);var e=n.event().transform(),o=n.event().swap();e.fold(function(){o()},function(n){c(t,n,o)})})]),xS()])}}function Ak(e,t){var n=e.label.map(function(n){return Dy(n,t)}),o=[Ch.config({disabled:e.disabled}),lg.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(n){return oo(n,fy),on.some(!0)}}),Kd("textfield-change",[lo(ei(),function(n,t){ro(n,uy,{name:e.name})}),lo(fi(),function(n,t){ro(n,uy,{name:e.name})})]),Gy.config({})],r=e.validation.map(function(o){return jy.config({getRoot:function(n){return Eo(n.element())},invalidClass:"tox-invalid",validator:{validate:function(n){var t=Zf.getValue(n),e=o.validator(t);return Ny(!0===e?an.value(t):an.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(nn({}),function(n){return{placeholder:t.translate(n)}}),u=e.inputMode.fold(nn({}),function(n){return{inputmode:n}}),a=N(N({},i),u),c=vy.parts().field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:ba(H([o,r])),selectOnFocus:!1,factory:yy}),s=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),f=[Ch.config({disabled:e.disabled,onDisabled:function(n){vy.getField(n).each(Ch.disable)},onEnabled:function(n){vy.getField(n).each(Ch.enable)}})];return By(n,c,s,f)}function _k(n){var t=ye(null);return nu({readState:function(){return{timer:null!==t.get()?"set":"unset"}},setTimer:function(n){t.set(n)},cancel:function(){var n=t.get();null!==n&&n.cancel()}})}function Mk(n,t,e){var o=Zf.getValue(e);Zf.setValue(t,o),CE(t)}function Fk(n,t){var e=n.element(),o=Er(e),r=e.dom();"number"!==zo(e,"type")&&t(r,o)}function Ik(n,t,e){if(n.selectsOver){var o=Zf.getValue(t),r=n.getDisplayText(o),i=Zf.getValue(e);return 0===n.getDisplayText(i).indexOf(r)?on.some(function(){Mk(0,t,e),function(n,e){Fk(n,function(n,t){return n.setSelectionRange(e,t.length)})}(t,r.length)}):on.none()}return on.none()}function Rk(n){return BE(Vy(n))}function Vk(n){return{type:"menuitem",value:n.url,text:n.title,meta:{attach:n.attach},onAction:function(){}}}function Nk(n,t){return{type:"menuitem",value:t,text:n,meta:{attach:undefined},onAction:function(){}}}function Hk(n,t){return function(n){return S(n,Vk)}(function(t,n){return C(n,function(n){return n.type===t})}(n,t))}function Pk(n,t){var e=n.toLowerCase();return C(t,function(n){var t=n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.text;return Vt(t.toLowerCase(),e)||Vt(n.value.toLowerCase(),e)})}function zk(e,n,o){var t=Zf.getValue(n),r=t.meta.text!==undefined?t.meta.text:t.value;return o.getLinkInformation().fold(function(){return[]},function(n){var t=Pk(r,function(n){return S(n,function(n){return Nk(n,n)})}(o.getHistory(e)));return"file"===e?function(n){return O(n,function(n,t){return 0===n.length||0===t.length?n.concat(t):n.concat(AE,t)},[])}([t,Pk(r,function(n){return Hk("header",n.targets)}(n)),Pk(r,H([function(n){return on.from(n.anchorTop).map(function(n){return Nk("<top>",n)}).toArray()}(n),function(n){return Hk("anchor",n.targets)}(n),function(n){return on.from(n.anchorBottom).map(function(n){return Nk("<bottom>",n)}).toArray()}(n)]))]):t})}function Lk(r,o,i){function u(n){var t=Zf.getValue(n);i.addToHistory(t.value,r.filetype)}var n,t,e,a,c,s=o.shared.providers,f=vy.parts().field({factory:TE,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":_E,type:"url"},minChars:0,responseTime:0,fetch:function(n){var t=zk(r.filetype,n,i),e=NC(t,Fh.BUBBLE_TO_SANDBOX,o,!1);return Ny(e)},getHotspot:function(n){return h.getOpt(n)},onSetValue:function(n,t){n.hasConfigured(jy)&&jy.run(n).get(Z)},typeaheadBehaviours:ba(H([i.getValidationHandler().map(function(e){return jy.config({getRoot:function(n){return Eo(n.element())},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(n,t){d.getOpt(n).each(function(n){Po(n.element(),"title",s.translate(t))})}},validator:{validate:function(n){var t=Zf.getValue(n);return DE(function(o){e({type:r.filetype,url:t.value},function(n){if("invalid"===n.status){var t=an.error(n.message);o(t)}else{var e=an.value(n.message);o(e)}})})},validateOnLoad:!1}})}).toArray(),[Ch.config({disabled:r.disabled}),Gy.config({}),Kd("urlinput-events",H(["file"===r.filetype?[lo(ei(),function(n){ro(n,uy,{name:r.name})})]:[],[lo(oi(),function(n){ro(n,uy,{name:r.name}),u(n)}),lo(fi(),function(n){ro(n,uy,{name:r.name}),u(n)})]]))]])),eventOrder:(n={},n[ei()]=["streaming","urlinput-events","invalidating"],n),model:{getDisplayText:function(n){return n.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:o.shared.getSink,parts:{menu:Sv(0,0,"normal")},onExecute:function(n,t,e){ro(t,fy,{})},onItemExecute:function(n,t,e,o){u(n),ro(n,uy,{name:r.name})}}),l=r.label.map(function(n){return Dy(n,s)}),d=vm((t="invalid",e=on.some(_E),void 0===(a="warning")&&(a=t),void 0===c&&(c=t),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+t],innerHtml:ym(a,s.icons),attributes:N({title:s.translate(c),"aria-live":"polite"},e.fold(function(){return{}},function(n){return{id:n}}))}})),m=vm({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[d.asSpec()]}),g=i.getUrlPicker(r.filetype),p=Xo("browser.url.event"),h=vm({dom:{tag:"div",classes:["tox-control-wrap"]},components:[f,m.asSpec()],behaviours:ba([Ch.config({disabled:r.disabled})])}),v=vm(GC({name:r.name,icon:on.some("browse"),text:r.label.getOr(""),disabled:r.disabled,primary:!1,borderless:!0},function(n){return oo(n,p)},s,[],["tox-browse-url"]));return vy.sketch({dom:Yy([]),components:l.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:H([[h.asSpec()],g.map(function(){return v.asSpec()}).toArray()])}]),fieldBehaviours:ba([Ch.config({disabled:r.disabled,onDisabled:function(n){vy.getField(n).each(Ch.disable),v.getOpt(n).each(Ch.disable)},onEnabled:function(n){vy.getField(n).each(Ch.enable),v.getOpt(n).each(Ch.enable)}}),Kd("url-input-events",[lo(p,function(o){Zl.getCurrent(o).each(function(t){var e=Zf.getValue(t);g.each(function(n){n(e).get(function(n){Zf.setValue(t,n),ro(o,uy,{name:r.name})})})})})])])})}function jk(u,t){function n(o){return function(t,e){Ou(e.event().target(),"[data-collection-item-value]").each(function(n){o(t,e,n,zo(n,"data-collection-item-value"))})}}var e=u.label.map(function(n){return Dy(n,t)}),o=n(function(n,t,e,o){t.stop(),ro(n,sy,{name:u.name,value:o})}),r=[lo($r(),n(function(n,t,e){wa(e)})),lo(ri(),o),lo(gi(),o),lo(Qr(),n(function(n,t,e){ku(n.element(),"."+mh).each(function(n){dr(n,mh)}),fr(e,mh)})),lo(Zr(),n(function(n){ku(n.element(),"."+mh).each(function(n){dr(n,mh)})})),Ni(n(function(n,t,e,o){ro(n,sy,{name:u.name,value:o})}))],i=vy.parts().field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:l},behaviours:ba([mg.config({}),Zf.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,n){!function(n,t){var e=S(t,function(n){var t=rh.translate(n.text),e=1===u.columns?'<div class="tox-collection__item-label">'+t+"</div>":"",o='<div class="tox-collection__item-icon">'+n.icon+"</div>",r={_:" "," - ":" ","-":" "},i=t.replace(/\_| \- |\-/g,function(n){return r[n]});return'<div class="tox-collection__item" tabindex="-1" data-collection-item-value="'+function(n){return'"'===n?"&quot;":n}(n.value)+'" title="'+i+'" aria-label="'+i+'">'+o+e+"</div>"}),o=1<u.columns&&"auto"!==u.columns?w(e,u.columns):[e],r=S(o,function(n){return'<div class="tox-collection__group">'+n.join("")+"</div>"});No(n.element(),r.join(""))}(o,n),"auto"===u.columns&&ip(o,5,"tox-collection__item").each(function(n){var t=n.numRows,e=n.numColumns;lg.setGridSize(o,t,e)}),oo(o,gy)}}),Gy.config({}),lg.config(function(n,t){return 1===n?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===n?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===t?".tox-swatches__row":".tox-collection__group",cell:"color"===t?"."+ah:"."+uh}}}(u.columns,"normal")),Kd("collection-events",r)])});return By(e,i,["tox-form__group--collection"],[])}function Uk(r){return function(t,e,o){return Nn(e,"name").fold(function(){return r(e,o)},function(n){return t.field(n,r(e,o))})}}function Wk(t,n,e){var o=Dn(e,{shared:{interpreter:function(n){return IE(t,n,o)}}});return IE(t,n,o)}function Gk(n){return{colorPicker:function(e){return function(n,t){Uv.colorPickerDialog(e)(n,t)}}(n),hasCustomColors:function(n){return function(){return Fv(n)}}(n),getColors:function(n){return function(){return Iv(n)}}(n),getColorCols:function(n){return function(){return Uv.getColorCols(n)}}(n)}}function Xk(e){return function(n){return on.from(n.getParam("style_formats")).filter(fn)}(e).map(function(n){var t=function(t,n){function e(n){bn(n,function(n){t.formatter.has(n.name)||t.formatter.register(n.name,n.format)})}var o=PE(n);return t.formatter?e(o.customFormats):t.on("init",function(){e(o.customFormats)}),o.formats}(e,n);return function(n){return n.getParam("style_formats_merge",!1,"boolean")}(e)?HE.concat(t):t}).getOr(HE)}function Yk(n,t,e){var o={type:"formatter",isSelected:t(n.format),getStylePreview:e(n.format)};return Dn(n,o)}function qk(r,n,i,u){var o=function(n){return S(n,function(n){var t=wn(n);if($(n,"items")){var e=o(n.items);return Dn(function(n){var t={type:"submenu",isSelected:nn(!1),getStylePreview:function(){return on.none()}};return Dn(n,t)}(n),{getStyleItems:function(){return e}})}return $(n,"format")?function(n){return Yk(n,i,u)}(n):1===t.length&&vn(t,"title")?Dn(n,{type:"separator"}):function(n){var t=Xo(n.title),e={type:"formatter",format:t,isSelected:i(t),getStylePreview:u(t)},o=Dn(n,e);return r.formatter.register(t,o),o}(n)})};return o(n)}function Kk(t){return function(n){if(n&&1===n.nodeType){if(n.contentEditable===t)return!0;if(n.getAttribute("data-mce-contenteditable")===t)return!0}return!1}}function Jk(n,t,e,o,r){return{type:n,title:t,url:e,level:o,attach:r}}function $k(n){return n.innerText||n.textContent}function Qk(n){return function(n){return n&&"A"===n.nodeName&&(n.id||n.name)!==undefined}(n)&&UE(n)}function Zk(n){return n&&/^(H[1-6])$/.test(n.nodeName)}function nO(n){return Zk(n)&&UE(n)}function tO(n){var t=function(n){return n.id?n.id:Xo("h")}(n);return Jk("header",$k(n),"#"+t,function(n){return Zk(n)?parseInt(n.nodeName.substr(1),10):0}(n),function(){n.id=t})}function eO(n){var t=n.id||n.name,e=$k(n);return Jk("anchor",e||"#"+t,"#"+t,0,Z)}function oO(n){return function(n,t){return S(zc(we.fromDom(t),n),function(n){return n.dom()})}("h1,h2,h3,h4,h5,h6,a:not([href])",n)}function rO(n){return 0<zE(n.title).length}function iO(n){return cn(n)&&/^https?/.test(n)}function uO(n){return sn(n)&&I(n,function(n){return!function(n){return fn(n)&&n.length<=5&&D(n,iO)}(n)}).isNone()}function aO(){var n,t=v.localStorage.getItem(GE);if(null===t)return{};try{n=JSON.parse(t)}catch(e){if(e instanceof SyntaxError)return v.console.log("Local storage "+GE+" was not valid JSON",e),{};throw e}return uO(n)?n:(v.console.log("Local storage "+GE+" was not valid format",n),{})}function cO(n){var t=aO();return Object.prototype.hasOwnProperty.call(t,n)?t[n]:[]}function sO(t,n){if(iO(t)){var e=aO(),o=Object.prototype.hasOwnProperty.call(e,n)?e[n]:[],r=C(o,function(n){return n!==t});e[n]=[t].concat(r).slice(0,5),function(n){if(!uO(n))throw new Error("Bad format for history:\n"+JSON.stringify(n));v.localStorage.setItem(GE,JSON.stringify(n))}(e)}}function fO(n){return!!n}function lO(n){return P(yk.makeMap(n,/[, ]/),fO)}function dO(n,t,e){var o=function(n,t){return XE.call(n,t)?on.some(n[t]):on.none()}(n,t).getOr(e);return cn(o)?on.some(o):on.none()}function mO(n){return on.some(n.file_picker_callback).filter(dn)}function gO(n,t){var e=function(n){var t=on.some(n.file_picker_types).filter(fO),e=on.some(n.file_browser_callback_types).filter(fO),o=t.or(e).map(lO);return mO(n).fold(function(){return!1},function(n){return o.fold(function(){return!0},function(n){return 0<wn(n).length&&n})})}(n);return ln(e)?e?mO(n):on.none():e[t]?mO(n):on.none()}function pO(t){return{getHistory:cO,addToHistory:sO,getLinkInformation:function(){return function(n){return!1===n.settings.typeahead_urls?on.none():on.some({targets:WE(n.getBody()),anchorTop:dO(n.settings,"anchor_top","#top").getOrUndefined(),anchorBottom:dO(n.settings,"anchor_bottom","#bottom").getOrUndefined()})}(t)},getValidationHandler:function(){return function(n){return on.from(n.settings.file_picker_validator_handler).filter(dn).orThunk(function(){return on.from(n.settings.filepicker_validator_handler).filter(dn)})}(t)},getUrlPicker:function(n){return function(r,i){return gO(r.settings,i).map(function(o){return function(t){return Vy(function(e){var n=yk.extend({filetype:i},on.from(t.meta).getOr({}));o.call(r,function(n,t){if(!cn(n))throw new Error("Expected value to be string");if(t!==undefined&&!sn(t))throw new Error("Expected meta to be a object");e({value:n,meta:t})},t.value,n)})}})}(t,n)}}}function hO(n,t,e,o){var r=ye(!1),i={shared:{providers:{icons:function(){return t.ui.registry.getAll().icons},menuItems:function(){return t.ui.registry.getAll().menuItems},translate:rh.translate},interpreter:function(n){return function(n,t){return IE(FE,n,t)}(n,i)},anchors:NE(t,e,o),getSink:function(){return an.value(n)}},urlinput:pO(t),styleselect:function(e){function o(n){return function(){return e.formatter.match(n)}}function r(t){return function(){var n=e.formatter.get(t);return n!==undefined?on.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styleAttr:e.formatter.getCssText(t)}):on.none()}}var i=function(n){var t=n.items;return t!==undefined&&0<t.length?B(t,i):[n.format]},u=ye([]),a=ye([]),c=ye([]),s=ye([]),f=ye(!1);e.on("init",function(){var n=Xk(e),t=qk(e,n,o,r);u.set(t),a.set(B(t,i))}),e.on("addStyleModifications",function(n){var t=qk(e,n.items,o,r);c.set(t),f.set(n.replace),s.set(B(t,i))});return{getData:function(){var n=f.get()?[]:u.get(),t=c.get();return n.concat(t)},getFlattenedKeys:function(){var n=f.get()?[]:a.get(),t=s.get();return n.concat(t)}}}(t),colorinput:Gk(t),dialog:function(n){return{isDraggableModal:function(n){return function(){return function(n){return n.getParam("draggable_modal",!1,"boolean")}(n)}}(n)}}(t),isContextMenuOpen:function(){return r.get()},setContextMenuState:function(n){return r.set(n)}};return i}function vO(n,t,o){var e=function(n,e){return O(n,function(t,n){return e(n,t.len).fold(nn(t),function(n){return{len:n.finish(),list:t.list.concat([n])}})},{len:0,list:[]}).list}(n,function(n,t){var e=o(n);return on.some({element:nn(n),start:nn(t),finish:nn(t+e),width:nn(e)})}),r=C(e,function(n){return n.finish()<=t}),i=k(r,function(n,t){return n+t.width()},0),u=e.slice(r.length);return{within:nn(r),extra:nn(u),withinWidth:nn(i)}}function bO(n){return S(n,function(n){return n.element()})}function yO(n,t,e,o){var r=function(n,t,e){var o=vO(t,n,e);return 0===o.extra().length?on.some(o):on.none()}(n,t,e).getOrThunk(function(){return vO(t,n-e(o),e)}),i=r.within(),u=r.extra(),a=r.withinWidth();return 1===u.length&&u[0].width()<=e(o)?function(n,t,e){var o=bO(n.concat(t));return $E(o,[],e)}(i,u,a):1<=u.length?function(n,t,e,o){var r=bO(n).concat([e]);return $E(r,bO(t),o)}(i,u,o,a):function(n,t,e){return $E(bO(n),[],e)}(i,0,a)}function xO(n,t){var e=S(t,function(n){return au(n)});JE.setGroups(n,e)}function wO(n,t,e,o){var r=Ks(n,t,"primary"),i=qs(n,t,"overflow-button"),u=Jy.getCoupled(n,"overflowGroup");yr(r.element(),"visibility","hidden");var a=function(n,t){return n.bind(function(t){return Ca(t.element()).bind(function(n){return t.getSystem().getByDom(n).toOption()})}).orThunk(function(){return t.filter(vg.isFocused)})}(e,i);e.each(function(n){JE.setGroups(n,[])});var c=t.builtGroups.get();xO(r,c.concat([u]));var s=mu(r.element()),f=yO(s,c,function(n){return mu(n.element())},u);0===f.extra().length?(mg.remove(r,u),e.each(function(n){JE.setGroups(n,[])})):(xO(r,f.within()),e.each(function(n){xO(n,f.extra())})),kr(r.element(),"visibility"),Or(r.element()),e.each(function(t){i.each(function(n){return Cg.set(n,o(t))}),a.each(vg.focus)})}function SO(o,n,t,e,r){var i="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:n,behaviours:Rs(o.splitToolbarBehaviours,[Jy.config({others:N(N({},r.coupling),{overflowGroup:function(t){return nT.sketch(N(N({},e["overflow-group"]()),{items:[Gg.sketch(N(N({},e["overflow-button"]()),{action:function(n){oo(t,i)}}))]}))}})}),Kd("toolbar-toggle-events",[lo(i,function(n){r.apis.toggle(n)})])]),apis:N({setGroups:function(n,t){!function(n,t){var e=S(t,n.getSystem().build);o.builtGroups.set(e)}(n,t),r.apis.refresh(n)},getMoreButton:function(n){return function(n){return qs(n,o,"overflow-button")}(n)}},r.apis),domModification:{attributes:{role:"group"}}}}function CO(n){return n.getSystem().isConnected()}function kO(n,t,e){var o=t.lazySink(n).getOrDie(),r=t.getAnchor(n),i=t.getOverflowBounds.map(function(n){return n()});Af.positionWithinBounds(o,r,e,i)}function OO(t,e){var n=zf.getState(Jy.getCoupled(t,"sandbox"));wO(t,e,n,CO),n.each(function(n){return kO(t,e,n)})}function EO(t,e){zf.getState(Jy.getCoupled(t,"sandbox")).each(function(n){return kO(t,e,n)})}function TO(t,n){return n.getAnimationRoot.fold(function(){return t.element()},function(n){return n(t)})}function BO(n){return n.dimension.property}function DO(n,t){return n.dimension.getDimension(t)}function AO(n,t){var e=TO(n,t);pr(e,[t.shrinkingClass,t.growingClass])}function _O(n,t){dr(n.element(),t.openClass),fr(n.element(),t.closedClass),yr(n.element(),BO(t),"0px"),Or(n.element())}function MO(n,t){dr(n.element(),t.closedClass),fr(n.element(),t.openClass),kr(n.element(),BO(t))}function FO(n,t,e,o){e.setCollapsed(),yr(n.element(),BO(t),DO(t,n.element())),Or(n.element()),AO(n,t),_O(n,t),t.onStartShrink(n),t.onShrunk(n)}function IO(n,t,e,o){var r=o.getOrThunk(function(){return DO(t,n.element())});e.setCollapsed(),yr(n.element(),BO(t),r),Or(n.element());var i=TO(n,t);dr(i,t.growingClass),fr(i,t.shrinkingClass),_O(n,t),t.onStartShrink(n)}function RO(n,t,e){var o=DO(t,n.element());("0px"===o?FO:IO)(n,t,e,on.some(o))}function VO(n,t,e){var o=TO(n,t),r=mr(o,t.shrinkingClass),i=DO(t,n.element());MO(n,t);var u=DO(t,n.element());(r?function(){yr(n.element(),BO(t),i),Or(n.element())}:function(){_O(n,t)})(),dr(o,t.shrinkingClass),fr(o,t.growingClass),MO(n,t),yr(n.element(),BO(t),u),e.setExpanded(),t.onStartGrow(n)}function NO(n,t,e){var o=TO(n,t);return!0===mr(o,t.growingClass)}function HO(n,t,e){var o=TO(n,t);return!0===mr(o,t.shrinkingClass)}function PO(n){return cT.hasGrown(n)}function zO(n,t){var e=n.outerContainer;!function(n,t){var e=n.outerContainer.element();t&&(n.mothership.broadcastOn([Lf()],{target:e}),n.uiMothership.broadcastOn([Lf()],{target:e})),n.mothership.broadcastOn([gT],{readonly:t}),n.uiMothership.broadcastOn([gT],{readonly:t})}(n,t),Lt("*",e.element()).forEach(function(n){e.getSystem().getByDom(n).each(function(n){n.hasConfigured(Ch)&&Ch.set(n,t)})})}function LO(n,t){n.on("init",function(){n.readonly&&zO(t,!0)}),n.on("SwitchMode",function(){return zO(t,n.readonly)}),function(n){return n.getParam("readonly",!1,"boolean")}(n)&&n.setMode("readonly")}function jO(e){var n;return lc.config({channels:(n={},n[gT]={schema:pT,onReceive:function(n,t){e(n).each(function(n){!function(t,e){Lt("*",t.element()).forEach(function(n){t.getSystem().getByDom(n).each(function(n){n.hasConfigured(Ch)&&Ch.set(n,e)})})}(n,t.readonly)})}},n)})}function UO(n){var t=n.title.fold(function(){return{}},function(n){return{attributes:{title:n}}});return{dom:N({tag:"div",classes:["tox-toolbar__group"]},t),components:[nT.parts().items({})],items:n.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:ba([Gy.config({}),vg.config({})])}}function WO(n){return nT.sketch(UO(n))}function GO(e,n,t){var o=Ii(function(n){var t=S(e.initGroups,WO);JE.setGroups(n,t)});return ba([lg.config({mode:n,onEscape:e.onEscape,selector:".tox-toolbar__group"}),Kd("toolbar-events",[o]),jO(t)])}function XO(n,t){var e=n.cyclicKeying?"cyclic":"acyclic";return{uid:n.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":UO({title:on.none(),items:[]}),"overflow-button":jC({name:"more",icon:on.some("more-drawer"),disabled:!1,tooltip:on.some("More..."),primary:!1,borderless:!1},on.none(),n.backstage.shared.providers)},splitToolbarBehaviours:GO(n,e,t)}}function YO(r){var n=XO(r,rT.getOverflow),t=rT.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return rT.sketch(N(N({},n),{lazySink:r.getSink,getAnchor:function(){return r.backstage.shared.anchors.toolbarOverflow()},getOverflowBounds:function(){var n=r.moreDrawerData.lazyHeader().element(),t=wu(n),e=ko(n),o=wu(e);return yu(t.x()+4,o.y(),t.width()-8,o.height())},parts:N(N({},n.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"]}}}),components:[t],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))}function qO(n){var t=dT.parts().primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=dT.parts().overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=XO(n,dT.getOverflow);return dT.sketch(N(N({},o),{components:[t,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(n){n.getSystem().broadcastOn([mT()],{type:"opened"})},onClosed:function(n){n.getSystem().broadcastOn([mT()],{type:"closed"})}}))}function KO(n){var t=n.cyclicKeying?"cyclic":"acyclic";return JE.sketch({uid:n.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(n.type===jb.scrolling?["tox-toolbar--scrolling"]:[])},components:[JE.parts().groups({})],toolbarBehaviours:GO(n,t,nn(on.none()))})}function JO(n){return tt("toolbarbutton",vT,n)}function $O(n){return tt("menubutton",yT,n)}function QO(n){return tt("ToggleButton",ST,n)}function ZO(t){return{isDisabled:function(){return Ch.isDisabled(t)},setDisabled:function(n){return Ch.set(t,n)}}}function nE(t){return{setActive:function(n){Cg.set(t,n)},isActive:function(){return Cg.isOn(t)},isDisabled:function(){return Ch.isDisabled(t)},setDisabled:function(n){return Ch.set(t,n)}}}function tE(n,t){return n.map(function(n){return{"aria-label":t.translate(n),title:t.translate(n)}}).getOr({})}function eE(t,e,n,o,r,i){function u(n){return rh.isRtl()&&vn(jT,n)?n+"-rtl":n}var a,c=rh.isRtl()&&t.exists(function(n){return vn(UT,n)});return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]).concat(c?["tox-tbtn__icon-rtl"]:[]),attributes:tE(n,i)},components:Bh([t.map(function(n){return AC(u(n),i.icons)}),e.map(function(n){return MC(n,"tox-tbtn",i)})]),eventOrder:(a={},a[Yr()]=["focusing","alloy.base.behaviour","common-button-display-events"],a),buttonBehaviours:ba([Kd("common-button-display-events",[lo(Yr(),function(n,t){t.event().prevent(),oo(n,LT)})])].concat(o.map(function(n){return RT.config({channel:n,initialData:{icon:t,text:e},renderComponents:function(n,t){return Bh([n.icon.map(function(n){return AC(u(n),i.icons)}),n.text.map(function(n){return MC(n,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}}function oE(n,t,e){var o=ye(Z),r=eE(n.icon,n.text,n.tooltip,on.none(),on.none(),e);return Gg.sketch({dom:r.dom,components:r.components,eventOrder:ok,buttonBehaviours:ba([Kd("toolbar-button-events",[function(e){return Ni(function(t,n){Op(e,t)(function(n){ro(t,ek,{buttonApi:n}),e.onAction(n)})})}({onAction:n.onAction,getApi:t.getApi}),Ep(t,o),Tp(t,o)]),Th(n.disabled)].concat(t.toolbarButtonBehaviours))})}function rE(t,n){function e(e){return{isDisabled:function(){return Ch.isDisabled(e)},setDisabled:function(n){return Ch.set(e,n)},setIconFill:function(n,t){ku(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){Po(n,"fill",t)})},setIconStroke:function(n,t){ku(e.element(),'svg path[id="'+n+'"], rect[id="'+n+'"]').each(function(n){Po(n,"stroke",t)})},setActive:function(t){Po(e.element(),"aria-pressed",t),ku(e.element(),"span").each(function(n){e.getSystem().getByDom(n).each(function(n){return Cg.set(n,t)})})},isActive:function(){return ku(e.element(),"span").exists(function(n){return e.getSystem().getByDom(n).exists(Cg.isOn)})}}}var o,r=Xo("channel-update-split-dropdown-display"),i=ye(Z),u={getApi:e,onSetup:t.onSetup};return zT.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:An({"aria-pressed":!1},tE(t.tooltip,n.providers))},onExecute:function(n){t.onAction(e(n))},onItemExecute:function(n,t,e){},splitDropdownBehaviours:ba([Eh(!1),Kd("split-dropdown-events",[lo(LT,vg.focus),Ep(u,i),Tp(u,i)]),Ew.config({})]),eventOrder:(o={},o[Ci()]=["alloy.base.behaviour","split-dropdown-events"],o),toggleClass:"tox-tbtn--enabled",lazySink:n.getSink,fetch:function(e,r,o){return function(t){return Vy(function(n){return r.fetch(n)}).map(function(n){return on.from(ub(Dn(Tv(Xo("menu-value"),n,function(n){r.onItemAction(e(t),n)},r.columns,r.presets,Fh.CLOSE_ON_EXECUTE,r.select.getOr(function(){return!1}),o),{movement:Bv(r.columns,r.presets),menuBehaviours:Jp("auto"!==r.columns?[]:[Ii(function(o,n){ip(o,4,sp(r.presets)).each(function(n){var t=n.numRows,e=n.numColumns;lg.setGridSize(o,t,e)})})])})))})}}(e,t,n.providers),parts:{menu:Sv(0,t.columns,t.presets)},components:[zT.parts().button(eE(t.icon,t.text,on.none(),on.some(r),on.some([Cg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),n.providers)),zT.parts().arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:ym("chevron-down",n.providers.icons)}}),zT.parts()["aria-descriptor"]({text:n.providers.translate("To open the popup, press Shift+Enter")})]})}function iE(o,r){return lo(ek,function(n,t){var e=function(n){return{hide:function(){return oo(n,hi())},getValue:function(){return Zf.getValue(n)}}}(o.get(n));r.onAction(e,t.event().buttonApi())})}function uE(n,t,e){var o={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===t.type?function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=et(QO(N(N({},r),{type:"togglebutton",onAction:function(){}})));return GT(i,e.backstage.shared.providers,[iE(n,t)])}(n,t,o):function(n,t,e){var o=t.original,r=(o.primary,c(o,["primary"])),i=et(JO(N(N({},r),{type:"button",onAction:function(){}})));return WT(i,e.backstage.shared.providers,[iE(n,t)])}(n,t,o)}function aE(n,t){var e=Math.max(t.x(),n.x()),o=n.right()-e,r=t.width()-(e-t.x());return{x:e,width:Math.min(o,r)}}function cE(n){var t=Pb(n)||zb(n)||Xb(n),e=vu(v.window),o=xu(we.fromDom(n.getContentAreaContainer()));return n.inline&&!t?function(n,t,e){var o=aE(t,e),r=o.x,i=o.width;return yu(r,e.y(),i,e.height())}(0,o,e):n.inline?function(n,t,e){var o=aE(t,e),r=o.x,i=o.width,u=we.fromDom(n.getContainer()),a=ku(u,".tox-editor-header").getOr(u),c=xu(a),s=e.height(),f=e.y();if(c.y()>=t.bottom()){var l=Math.min(s+f,c.y());return yu(r,f,i,l-f)}var d=Math.max(f,c.bottom());return yu(r,d,i,s-(d-f))}(n,o,e):function(n,t,e){var o=aE(t,e),r=o.x,i=o.width,u=we.fromDom(n.getContainer()),a=ku(u,".tox-editor-header").getOr(u),c=xu(u),s=xu(a),f=Math.max(e.y(),t.y(),s.bottom()),l=c.bottom()-f,d=e.height()-(f-e.y()),m=Math.min(l,d);return yu(r,f,i,m)}(n,o,e)}function sE(t,n){return Du(n,function(n){return n.predicate(t.dom())?on.some({toolbarApi:n,elem:t}):on.none()})}function fE(o,r){return function(t){function n(){t.setActive(o.formatter.match(r));var n=o.formatter.formatChanged(r,t.setActive).unbind;e.set(on.some(n))}var e=ye(on.none());return o.initialized?n():o.on("init",n),function(){return e.get().each(function(n){return n()})}}}function lE(t){return function(n){return function(){t.undoManager.transact(function(){t.focus(),t.execCommand("mceToggleFormat",!1,n.format)})}}}function dE(n,t,e){var o=e.dataset,r="basic"===o.type?function(){return S(o.data,function(n){return Yk(n,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:function(n,u,a){function r(n,t,e,o){var r=u.shared.providers.translate(n.title);if("separator"===n.type)return on.some({type:"separator",text:r});if("submenu"!==n.type)return on.some(N({type:"togglemenuitem",text:r,active:n.isSelected(o),disabled:e,onAction:a.onAction(n)},n.getStylePreview().fold(function(){return{}},function(n){return{meta:{style:n}}})));var i=B(n.getStyleItems(),function(n){return c(n,t,o)});return 0===t&&i.length<=0?on.none():on.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return B(n.getStyleItems(),function(n){return c(n,t,o)})}})}function i(n){var t=a.getCurrentValue(),e=a.shouldHide?0:1;return B(n,function(n){return c(n,e,t)})}var c=function(n,t,e){var o="formatter"===n.type&&a.isInvalid(n);return 0===t?o?[]:r(n,t,!1,e).toArray():r(n,t,o,e).toArray()};return{validateItems:i,getFetch:function(o,r){return function(n){var t=r(),e=i(t);n(NC(e,Fh.CLOSE_ON_EXECUTE,o,!1))}}}}(0,t,e),getStyleItems:r}}function mE(o,n,t){var e=dE(0,n,t),r=e.items,i=e.getStyleItems;return FC({text:t.icon.isSome()?on.none():on.some(""),icon:t.icon,tooltip:on.from(t.tooltip),role:on.none(),fetch:r.getFetch(n,i),onSetup:function(e){return t.setInitialValue.each(function(n){return n(e.getComponent())}),t.nodeChangeHandler.map(function(n){var t=n(e.getComponent());return o.on("NodeChange",t),function(){o.off("NodeChange",t)}}).getOr(Z)},getApi:function(n){return{getComponent:function(){return n}}},columns:1,presets:"normal",classes:t.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",n.shared)}var gE,pE,hE,vE,bE=Dl({name:"HtmlSelect",configFields:[ct("options"),Fs("selectBehaviours",[vg,Zf]),St("selectClasses",[]),St("selectAttributes",{}),ht("data")],factory:function(e,n){var t=S(e.options,function(n){return{dom:{tag:"option",value:n.value,innerHtml:n.text}}}),o=e.data.map(function(n){return q("initialValue",n)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:t,behaviours:Rs(e.selectBehaviours,[vg.config({}),Zf.config({store:N({mode:"manual",getValue:function(n){return Er(n.element())},setValue:function(n,t){E(e.options,function(n){return n.value===t}).isSome()&&Tr(n.element(),t)}},o)})])}}}),yE=/* */Object.freeze({events:function(n,t){var e=n.stream.streams.setup(n,t);return co([lo(n.event,e),Ri(function(){return t.cancel()})].concat(n.cancelEvent.map(function(n){return[lo(n,function(){return t.cancel()})]}).getOr([])))}}),xE=/* */Object.freeze({throttle:_k,init:function(n){return n.stream.streams.state(n)}}),wE=[st("stream",it("mode",{throttle:[ct("delay"),St("stopEvent",!0),Qu("streams",{setup:function(n,t){var e=n.stream,o=Jg(n.onStream,e.delay);return t.setTimer(o),function(n,t){o.throttle(n,t),e.stopEvent&&t.stop()}},state:_k})]})),St("event","input"),ht("cancelEvent"),Ju("onStream")],SE=ya({fields:wE,name:"streaming",active:yE,state:xE}),CE=function(n){Fk(n,function(n,t){return n.setSelectionRange(t.length,t.length)})},kE=nn("alloy.typeahead.itemexecute"),OE=nn([ht("lazySink"),ct("fetch"),St("minChars",5),St("responseTime",1e3),qu("onOpen"),St("getHotspot",on.some),St("getAnchorOverrides",nn({})),St("layouts",on.none()),St("eventOrder",{}),Dt("model",{},[St("getDisplayText",function(n){return n.meta!==undefined&&n.meta.text!==undefined?n.meta.text:n.value}),St("selectsOver",!0),St("populateFromBrowse",!0)]),qu("onSetValue"),Ku("onExecute"),qu("onItemExecute"),St("inputClasses",[]),St("inputAttributes",{}),St("inputStyles",{}),St("matchWidth",!0),St("useMinWidth",!1),St("dismissOnBlur",!0),Xu(["openClass"]),ht("initialData"),Fs("typeaheadBehaviours",[vg,Zf,SE,lg,Cg,Jy]),At("previewing",function(){return ye(!0)})].concat(by()).concat(ax())),EE=nn([wl({schema:[Gu()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(t,e){o.previewing.get()?t.getSystem().getByUid(o.uid).each(function(n){Ik(o.model,n,e).fold(function(){return ad.dehighlight(t,e)},function(n){return n()})}):t.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&Mk(o.model,n,e)}),o.previewing.set(!1)},onExecute:function(n,t){return n.getSystem().getByUid(o.uid).toOption().map(function(n){return ro(n,kE(),{item:t}),!0})},onHover:function(n,t){o.previewing.set(!1),n.getSystem().getByUid(o.uid).each(function(n){o.model.populateFromBrowse&&Mk(o.model,n,t)})}}}})]),TE=Al({name:"Typeahead",configFields:OE(),partFields:EE(),factory:function(r,n,t,i){function e(n,t,e){r.previewing.set(!1);var o=Jy.getCoupled(n,"sandbox");if(zf.isOpen(o))Zl.getCurrent(o).each(function(n){ad.getHighlighted(n).fold(function(){e(n)},function(){ao(o,n.element(),"keydown",t)})});else{nx(r,u(n),n,o,i,function(n){Zl.getCurrent(n).each(e)},Ay.HighlightFirst).get(Z)}}var o=Zb(r),u=function(o){return function(n){return n.map(function(n){var t=R(n.menus),e=B(t,function(n){return C(n.items,function(n){return"item"===n.type})});return Zf.getState(o).update(S(e,function(n){return n.data})),n})}},a=[vg.config({}),Zf.config({onSetValue:r.onSetValue,store:N({mode:"dataset",getDataKey:function(n){return Er(n.element())},getFallbackEntry:function(n){return{value:n,meta:{}}},setValue:function(n,t){Tr(n.element(),r.model.getDisplayText(t))}},r.initialData.map(function(n){return q("initialValue",n)}).getOr({}))}),SE.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(n,t){var e=Jy.getCoupled(n,"sandbox");if(vg.isFocused(n)&&Er(n.element()).length>=r.minChars){var o=Zl.getCurrent(e).bind(function(n){return ad.getHighlighted(n).map(Zf.getValue)});r.previewing.set(!0);nx(r,u(n),n,e,i,function(n){Zl.getCurrent(e).each(function(n){o.fold(function(){r.model.selectsOver&&ad.highlightFirst(n)},function(t){ad.highlightBy(n,function(n){return Zf.getValue(n).value===t.value}),ad.getHighlighted(n).orThunk(function(){return ad.highlightFirst(n),on.none()})})})},Ay.HighlightFirst).get(Z)}},cancelEvent:vi()}),lg.config({mode:"special",onDown:function(n,t){return e(n,t,ad.highlightFirst),on.some(!0)},onEscape:function(n){var t=Jy.getCoupled(n,"sandbox");return zf.isOpen(t)?(zf.close(t),on.some(!0)):on.none()},onUp:function(n,t){return e(n,t,ad.highlightLast),on.some(!0)},onEnter:function(t){var n=Jy.getCoupled(t,"sandbox"),e=zf.isOpen(n);if(e&&!r.previewing.get())return Zl.getCurrent(n).bind(function(n){return ad.getHighlighted(n)}).map(function(n){return ro(t,kE(),{item:n}),!0});var o=Zf.getValue(t);return oo(t,vi()),r.onExecute(n,t,o),e&&zf.close(n),on.some(!0)}}),Cg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),Jy.config({others:{sandbox:function(n){return ix(r,n,{onOpen:function(){return Cg.on(n)},onClose:function(){return Cg.off(n)}})}}}),Kd("typeaheadevents",[Ni(function(n){var t=Z;ex(r,u(n),n,i,t,Ay.HighlightFirst).get(Z)}),lo(kE(),function(n,t){var e=Jy.getCoupled(n,"sandbox");Mk(r.model,n,t.event().item()),oo(n,vi()),r.onItemExecute(n,e,t.event().item(),Zf.getValue(n)),zf.close(e),CE(n)})].concat(r.dismissOnBlur?[lo(si(),function(n){var t=Jy.getCoupled(n,"sandbox");Ca(t.element()).isNone()&&zf.close(t)})]:[]))];return{uid:r.uid,dom:ny(Dn(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:N(N({},o),Rs(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),BE=function(i){return N(N({},i),{toCached:function(){return BE(i.toCached())},bindFuture:function(t){return BE(i.bind(function(n){return n.fold(function(n){return Ny(an.error(n))},function(n){return t(n)})}))},bindResult:function(t){return BE(i.map(function(n){return n.bind(t)}))},mapResult:function(t){return BE(i.map(function(n){return n.map(t)}))},mapError:function(t){return BE(i.map(function(n){return n.mapError(t)}))},foldResult:function(t,e){return i.map(function(n){return n.fold(t,e)})},withTimeout:function(n,r){return BE(Vy(function(t){var e=!1,o=v.setTimeout(function(){e=!0,t(an.error(r()))},n);i.get(function(n){e||(v.clearTimeout(o),t(n))})}))}})},DE=Rk,AE={type:"separator"},_E=Xo("aria-invalid"),ME={bar:Uk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:S(n.items,t.interpreter)}}(n,t.shared)}),collection:Uk(function(n,t){return jk(n,t.shared.providers)}),alertbanner:Uk(function(n,t){return function(t,n){return Wb.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+t.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[Gg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:ym(t.icon,n.icons),attributes:{title:n.translate(t.iconTooltip)}},action:function(n){ro(n,sy,{name:"alert-banner",value:t.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:n.translate(t.text)}}]})}(n,t.shared.providers)}),input:Uk(function(n,t){return function(n,t){return Ak({name:n.name,multiline:!1,label:n.label,inputMode:n.inputMode,placeholder:n.placeholder,flex:!1,disabled:n.disabled,classname:"tox-textfield",validation:on.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),textarea:Uk(function(n,t){return function(n,t){return Ak({name:n.name,multiline:!0,label:n.label,inputMode:on.none(),placeholder:n.placeholder,flex:!0,disabled:n.disabled,classname:"tox-textarea",validation:on.none(),maximized:n.maximized},t)}(n,t.shared.providers)}),label:Uk(function(n,t){return function(n,t){var e={dom:{tag:"label",innerHtml:t.providers.translate(n.label),classes:["tox-label"]}},o=S(n.items,t.interpreter);return{dom:{tag:"div",classes:["tox-form__group"]},components:[e].concat(o),behaviours:ba([xS(),mg.config({}),BS(on.none()),lg.config({mode:"acyclic"})])}}(n,t.shared)}),iframe:(gE=function(n,t){return ww(n,t.shared.providers)},function(n,t,e){var o=Dn(t,{source:"dynamic"});return Uk(gE)(n,o,e)}),button:Uk(function(n,t){return qC(n,t.shared.providers)}),checkbox:Uk(function(n,t){return function(e,t){function n(n){return n.element().dom().click(),on.some(!0)}function o(n){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+n],innerHtml:ym("checked"===n?"selected":"unselected",t.icons)}}}var r=Zf.config({store:{mode:"manual",getValue:function(n){return n.element().dom().checked},setValue:function(n,t){n.element().dom().checked=t}}}),i=vy.parts().field({factory:{sketch:l},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:ba([xS(),Ch.config({disabled:e.disabled}),Gy.config({}),vg.config({}),r,lg.config({mode:"special",onEnter:n,onSpace:n,stopSpaceKeyup:!0}),Kd("checkbox-events",[lo(oi(),function(n,t){ro(n,uy,{name:e.name})})])])}),u=vy.parts().label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:t.translate(e.label)},behaviours:ba([Ew.config({})])}),a=vm({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[o("checked"),o("unchecked")]});return vy.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[i,a.asSpec(),u],fieldBehaviours:ba([Ch.config({disabled:e.disabled,disableClass:"tox-checkbox--disabled",onDisabled:function(n){vy.getField(n).each(Ch.disable)},onEnabled:function(n){vy.getField(n).each(Ch.enable)}})])})}(n,t.shared.providers)}),colorinput:Uk(function(n,t){return cx(n,t.shared,t.colorinput)}),colorpicker:Uk(function(n){function t(n){return"tox-"+n}var e=yS(pw,t),r=vm(e.sketch({dom:{tag:"div",classes:[t("color-picker-container")],attributes:{role:"presentation"}},onValidHex:function(n){ro(n,sy,{name:"hex-valid",value:!0})},onInvalidHex:function(n){ro(n,sy,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:ba([Zf.config({store:{mode:"manual",getValue:function(n){var t=r.get(n);return Zl.getCurrent(t).bind(function(n){return Zf.getValue(n).hex}).map(function(n){return"#"+n}).getOr("")},setValue:function(n,t){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(t),o=r.get(n);Zl.getCurrent(o).fold(function(){v.console.log("Can not find form")},function(n){Zf.setValue(n,{hex:on.from(e[1]).getOr("")}),dS.getField(n,"hex").each(function(n){oo(n,ei())})})}}}),xS()])}}),dropzone:Uk(function(n,t){return bw(n,t.shared.providers)}),grid:Uk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+n.columns+"col"]},components:S(n.items,t.interpreter)}}(n,t.shared)}),selectbox:Uk(function(n,t){return function(e,t){var n=S(e.items,function(n){return{text:t.translate(n.text),value:n.value}}),o=e.label.map(function(n){return Dy(n,t)}),r=vy.parts().field({dom:{},selectAttributes:{size:e.size},options:n,factory:bE,selectBehaviours:ba([Ch.config({disabled:e.disabled}),Gy.config({}),Kd("selectbox-change",[lo(oi(),function(n,t){ro(n,uy,{name:e.name})})])])}),i=1<e.size?on.none():on.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:ym("chevron-down",t.icons)}}),u={dom:{tag:"div",classes:["tox-selectfield"]},components:H([[r],i.toArray()])};return vy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:H([o.toArray(),[u]]),fieldBehaviours:ba([Ch.config({disabled:e.disabled,onDisabled:function(n){vy.getField(n).each(Ch.disable)},onEnabled:function(n){vy.getField(n).each(Ch.enable)}})])})}(n,t.shared.providers)}),sizeinput:Uk(function(n,t){return nk(n,t.shared.providers)}),urlinput:Uk(function(n,t){return Lk(n,t,t.urlinput)}),customeditor:Uk(function(e){var o=ye(on.none()),t=vm({dom:{tag:e.tag}}),r=ye(on.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:ba([Kd("editor-foo-events",[Ii(function(n){t.getOpt(n).each(function(t){(!function(n){return Object.prototype.hasOwnProperty.call(n,"init")}(e)?kS.load(e.scriptId,e.scriptUrl).then(function(n){return n(t.element().dom(),e.settings)}):e.init(t.element().dom())).then(function(t){r.get().each(function(n){t.setValue(n)}),r.set(on.none()),o.set(on.some(t))})})})]),Zf.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(n){return n.getValue()})},setValue:function(n,t){o.get().fold(function(){r.set(on.some(t))},function(n){return n.setValue(t)})}}}),xS()]),components:[t.asSpec()]}}),htmlpanel:Uk(function(n){return"presentation"===n.presets?Wb.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html}}):Wb.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:n.html,attributes:{role:"document"}},containerBehaviours:ba([Gy.config({}),vg.config({})])})}),imagetools:Uk(function(n,t){return Dk(n,t.shared.providers)}),table:Uk(function(n,t){return function(n,t){function e(n){return{dom:{tag:"th",innerHtml:t.translate(n)}}}function o(n){return{dom:{tag:"td",innerHtml:t.translate(n)}}}function r(n){return{dom:{tag:"tr"},components:S(n,o)}}var i,u;return{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(u=n.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:S(u,e)}]}),(i=n.cells,{dom:{tag:"tbody"},components:S(i,r)})],behaviours:ba([Gy.config({}),vg.config({})])}}(n,t.shared.providers)}),panel:Uk(function(n,t){return function(n,t){return{dom:{tag:"div",classes:n.classes},components:S(n.items,t.shared.interpreter)}}(n,t)})},FE={field:function(n,t){return t}},IE=function(t,e,o){return Nn(ME,e.type).fold(function(){return v.console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(n){return n(t,e,o)})},RE=nn(function(n,t){!function(n,t){var e=Vu.max(n,t,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]);yr(n,"max-width",e+"px")}(n,Math.floor(t))}),VE={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},NE=function(n,t,e){function o(){return we.fromDom(n.getBody())}var r=Kb(n);return{toolbar:function(n,t,e){return e?function(){return{anchor:"node",root:n(),node:on.from(n()),bubble:Oa(-12,-12,VE),layouts:{onRtl:function(){return[pm]},onLtr:function(){return[hm]}},overrides:{maxHeightFunction:Cf()}}}:function(){return{anchor:"hotspot",hotspot:t(),bubble:Oa(-12,12,VE),layouts:{onRtl:function(){return[ua]},onLtr:function(){return[aa]}},overrides:{maxHeightFunction:Cf()}}}}(o,t,r),toolbarOverflow:function(n){return function(){return{anchor:"hotspot",hotspot:n(),overrides:{maxWidthFunction:RE()},layouts:{onRtl:function(){return[ua,aa]},onLtr:function(){return[aa,ua]}}}}}(e),banner:function(n,t,e){return e?function(){return{anchor:"node",root:n(),node:on.from(n()),layouts:{onRtl:function(){return[Ug]},onLtr:function(){return[Ug]}}}}:function(){return{anchor:"hotspot",hotspot:t(),layouts:{onRtl:function(){return[rc]},onLtr:function(){return[rc]}}}}}(o,t,r),cursor:function(t,n){return function(){return{anchor:"selection",root:n(),getSelection:function(){var n=t.selection.getRng();return on.some(Tc.range(we.fromDom(n.startContainer),n.startOffset,we.fromDom(n.endContainer),n.endOffset))}}}}(n,o),node:function(t){return function(n){return{anchor:"node",root:t(),node:n}}}(o)}},HE=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",icon:"bold",format:"bold"},{title:"Italic",icon:"italic",format:"italic"},{title:"Underline",icon:"underline",format:"underline"},{title:"Strikethrough",icon:"strike-through",format:"strikethrough"},{title:"Superscript",icon:"superscript",format:"superscript"},{title:"Subscript",icon:"subscript",format:"subscript"},{title:"Code",icon:"code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}]}],PE=function(n){return O(n,function(n,t){if(function(n){return En(n,"items")}(t)){var e=PE(t.items);return{customFormats:n.customFormats.concat(e.customFormats),formats:n.formats.concat([{title:t.title,items:e.formats}])}}if(function(n){return En(n,"inline")}(t)||function(n){return En(n,"block")}(t)||function(n){return En(n,"selector")}(t)){var o="custom-"+t.title.toLowerCase();return{customFormats:n.customFormats.concat([{name:o,format:t}]),formats:n.formats.concat([{title:t.title,format:o,icon:t.icon}])}}return N(N({},n),{formats:n.formats.concat(t)})},{customFormats:[],formats:[]})},zE=yk.trim,LE=Kk("true"),jE=Kk("false"),UE=function(n){return function(n){for(;n=n.parentNode;){var t=n.contentEditable;if(t&&"inherit"!==t)return LE(n)}return!1}(n)&&!jE(n)},WE=function(n){var t=oO(n);return C(function(n){return S(C(n,nO),tO)}(t).concat(function(n){return S(C(n,Qk),eO)}(t)),rO)},GE="tinymce-url-history",XE=Object.prototype.hasOwnProperty,YE="contexttoolbar-hide",qE=nn([ct("dom"),St("shell",!0),Fs("toolbarBehaviours",[mg])]),KE=nn([Sl({name:"groups",overrides:function(n){return{behaviours:ba([mg.config({})])}}})]),JE=Al({name:"Toolbar",configFields:qE(),partFields:KE(),factory:function(t,n,e,o){var r=function(n){return t.shell?on.some(n):qs(n,t,"groups")},i=t.shell?{behaviours:[mg.config({})],components:[]}:{behaviours:[],components:n};return{uid:t.uid,dom:t.dom,components:i.components,behaviours:Rs(t.toolbarBehaviours,i.behaviours),apis:{setGroups:function(n,t){r(n).fold(function(){throw v.console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(n){mg.set(n,t)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(n,t,e){n.setGroups(t,e)}}}),$E=yo("within","extra","withinWidth"),QE=nn([ct("items"),Xu(["itemSelector"]),Fs("tgroupBehaviours",[lg])]),ZE=nn([Cl({name:"items",unit:"item"})]),nT=Al({name:"ToolbarGroup",configFields:QE(),partFields:ZE(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,behaviours:Rs(n.tgroupBehaviours,[lg.config({mode:"flow",selector:n.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),tT=nn([Fs("splitToolbarBehaviours",[Jy]),At("builtGroups",function(){return ye([])})]),eT=nn([Xu(["overflowToggledClass"]),ct("getAnchor"),xt("getOverflowBounds"),ct("lazySink")].concat(tT())),oT=nn([xl({factory:JE,schema:qE(),name:"primary"}),wl({factory:JE,schema:qE(),name:"overflow",overrides:function(t){return{toolbarBehaviours:ba([lg.config({mode:"cyclic",onEscape:function(n){return qs(n,t,"overflow-button").each(vg.focus),on.none()}})])}}}),wl({name:"overflow-button",overrides:function(n){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:ba([Cg.config({toggleClass:n.markers.overflowToggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),wl({name:"overflow-group"})]),rT=Al({name:"SplitFloatingToolbar",configFields:eT(),partFields:oT(),factory:function(t,n,e,o){return SO(t,n,0,o,{coupling:{sandbox:function(n){return function(o,e){var r=Eu();return{dom:{tag:"div",attributes:{id:r.id()}},behaviours:ba([lg.config({mode:"special",onEscape:function(n){return zf.close(n),on.some(!0)}}),zf.config({onOpen:function(n,t){OO(o,e),qs(o,e,"overflow-button").each(function(n){Cg.on(n),r.link(n.element())}),lg.focusIn(t)},onClose:function(){qs(o,e,"overflow-button").each(function(n){Cg.off(n),vg.focus(n),r.unlink(n.element())})},isPartOf:function(n,t,e){return Lu(t,e)||Lu(o,e)},getAttachPoint:function(){return e.lazySink(o).getOrDie()}}),lc.config({channels:N({},Ts({isExtraPart:nn(!1),doReposition:function(){return EO(o,e)}}))})])}}(n,t)}},apis:{refresh:function(n){return OO(n,t)},toggle:function(n){return function(n,t,e){var o=Jy.getCoupled(n,"sandbox");zf.isOpen(o)?zf.close(o):zf.open(o,e.overflow())}(n,0,o)},getOverflow:function(n){return zf.getState(Jy.getCoupled(n,"sandbox"))},reposition:function(n){return EO(n,t)}}})},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},reposition:function(n,t){n.reposition(t)},getMoreButton:function(n,t){return n.getMoreButton(t)},getOverflow:function(n,t){return n.getOverflow(t)},toggle:function(n,t){n.toggle(t)}}}),iT=/* */Object.freeze({refresh:function(n,t,e){if(e.isExpanded()){kr(n.element(),BO(t));var o=DO(t,n.element());yr(n.element(),BO(t),o)}},grow:function(n,t,e){e.isExpanded()||VO(n,t,e)},shrink:function(n,t,e){e.isExpanded()&&RO(n,t,e)},immediateShrink:function(n,t,e){e.isExpanded()&&FO(n,t,e)},hasGrown:function(n,t,e){return e.isExpanded()},hasShrunk:function(n,t,e){return e.isCollapsed()},isGrowing:NO,isShrinking:HO,isTransitioning:function(n,t,e){return!0===NO(n,t)||!0===HO(n,t)},toggleGrow:function(n,t,e){(e.isExpanded()?RO:VO)(n,t,e)},disableTransitions:AO}),uT=/* */Object.freeze({exhibit:function(n,t){var e=t.expanded;return Zo(e?{classes:[t.openClass],styles:{}}:{classes:[t.closedClass],styles:q(t.dimension.property,"0px")})},events:function(e,o){return co([bo(ii(),function(n,t){t.event().raw().propertyName===e.dimension.property&&(AO(n,e),o.isExpanded()&&kr(n.element(),e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(n))})])}}),aT=[ct("closedClass"),ct("openClass"),ct("shrinkingClass"),ct("growingClass"),ht("getAnimationRoot"),qu("onShrunk"),qu("onStartShrink"),qu("onGrown"),qu("onStartGrow"),St("expanded",!1),st("dimension",it("property",{width:[Qu("property","width"),Qu("getDimension",function(n){return mu(n)+"px"})],height:[Qu("property","height"),Qu("getDimension",function(n){return su(n)+"px"})]}))],cT=ya({fields:aT,name:"sliding",active:uT,apis:iT,state:/* */Object.freeze({init:function(n){var t=ye(n.expanded);return nu({isExpanded:function(){return!0===t.get()},isCollapsed:function(){return!1===t.get()},setCollapsed:d(t.set,!1),setExpanded:d(t.set,!0),readState:function(){return"expanded: "+t.get()}})}})}),sT=nn([Xu(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),qu("onOpened"),qu("onClosed")].concat(tT())),fT=nn([xl({factory:JE,schema:qE(),name:"primary"}),xl({factory:JE,schema:qE(),name:"overflow",overrides:function(t){return{toolbarBehaviours:ba([cT.config({dimension:{property:"height"},closedClass:t.markers.closedClass,openClass:t.markers.openClass,shrinkingClass:t.markers.shrinkingClass,growingClass:t.markers.growingClass,onShrunk:function(n){qs(n,t,"overflow-button").each(function(n){Cg.off(n),vg.focus(n)}),t.onClosed(n)},onGrown:function(n){lg.focusIn(n),t.onOpened(n)},onStartGrow:function(n){qs(n,t,"overflow-button").each(Cg.on)}}),lg.config({mode:"acyclic",onEscape:function(n){return qs(n,t,"overflow-button").each(vg.focus),on.some(!0)}})])}}}),wl({name:"overflow-button",overrides:function(n){return{buttonBehaviours:ba([Cg.config({toggleClass:n.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),wl({name:"overflow-group"})]),lT=function(n,t){var e=qs(n,t,"overflow");wO(n,t,e,PO),e.each(cT.refresh)},dT=Al({name:"SplitSlidingToolbar",configFields:sT(),partFields:fT(),factory:function(t,n,e,o){return SO(t,n,0,o,{coupling:{},apis:{refresh:function(n){return lT(n,t)},toggle:function(n){return function(t,e){qs(t,e,"overflow").each(function(n){lT(t,e),cT.toggleGrow(n)})}(n,t)},getOverflow:function(n){return qs(n,t,"overflow")}}})},apis:{setGroups:function(n,t,e){n.setGroups(t,e)},refresh:function(n,t){n.refresh(t)},getMoreButton:function(n,t){return n.getMoreButton(t)},getOverflow:function(n,t){return n.getOverflow(t)},toggle:function(n,t){n.toggle(t)}}}),mT=nn(Xo("toolbar-height-change")),gT="silver.readonly",pT=re([(pE="readonly",st(pE,pe))]),hT=[Tt("disabled",!1),yt("tooltip"),yt("icon"),yt("text"),Bt("onSetup",function(){return Z})],vT=re([ft("type"),dt("onAction")].concat(hT)),bT=[yt("text"),yt("tooltip"),yt("icon"),dt("fetch"),Bt("onSetup",function(){return Z})],yT=re(g([ft("type")],bT)),xT=re([ft("type"),yt("tooltip"),yt("icon"),yt("text"),xt("select"),dt("fetch"),Bt("onSetup",function(){return Z}),Et("presets","normal",["normal","color","listpreview"]),St("columns",1),dt("onAction"),dt("onItemAction")]),wT=[Tt("active",!1)].concat(hT),ST=re(wT.concat([ft("type"),dt("onAction")])),CT=[Bt("predicate",function(){return!1}),Et("scope","node",["node","editor"]),Et("position","selection",["node","selection","line"])],kT=hT.concat([St("type","contextformbutton"),St("primary",!1),dt("onAction"),At("original",l)]),OT=wT.concat([St("type","contextformbutton"),St("primary",!1),dt("onAction"),At("original",l)]),ET=hT.concat([St("type","contextformbutton")]),TT=wT.concat([St("type","contextformtogglebutton")]),BT=it("type",{contextformbutton:kT,contextformtogglebutton:OT}),DT=re([St("type","contextform"),Bt("initValue",function(){return""}),yt("label"),pt("commands",BT),vt("launch",it("type",{contextformbutton:ET,contextformtogglebutton:TT}))].concat(CT)),AT=re([St("type","contexttoolbar"),ft("items")].concat(CT)),_T=/* */Object.freeze({getState:function(n,t,e){return e}}),MT=/* */Object.freeze({events:function(i,u){function o(o,r){i.updateState.each(function(n){var t=n(o,r);u.set(t)}),i.renderComponents.each(function(n){var t=n(r,u.get()),e=S(t,o.getSystem().build);ps(o,e)})}return co([lo(li(),function(n,t){var e=i.channel;vn(t.channels(),e)&&o(n,t.data())}),Ii(function(t,n){i.initialData.each(function(n){o(t,n)})})])}}),FT=/* */Object.freeze({init:function(n){var t=ye(on.none());return{readState:function(){return t.get().fold(function(){return"none"},function(n){return n})},get:function(){return t.get()},set:function(n){return t.set(n)},clear:function(){return t.set(on.none())}}}}),IT=[ct("channel"),ht("renderComponents"),ht("updateState"),ht("initialData")],RT=ya({fields:IT,name:"reflecting",active:MT,apis:_T,state:FT}),VT=nn([ct("toggleClass"),ct("fetch"),Ju("onExecute"),St("getHotspot",on.some),St("getAnchorOverrides",nn({})),St("layouts",on.none()),Ju("onItemExecute"),ht("lazySink"),ct("dom"),qu("onOpen"),Fs("splitDropdownBehaviours",[Jy,lg,vg]),St("matchWidth",!1),St("useMinWidth",!1),St("eventOrder",{}),ht("role")].concat(ax())),NT=xl({factory:Gg,schema:[ct("dom")],name:"arrow",defaults:function(n){return{buttonBehaviours:ba([vg.revoke()])}},overrides:function(t){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(t.uid).each(io)},buttonBehaviours:ba([Cg.config({toggleOnExecute:!1,toggleClass:t.toggleClass})])}}}),HT=xl({factory:Gg,schema:[ct("dom")],name:"button",defaults:function(n){return{buttonBehaviours:ba([vg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(e.uid).each(function(n){e.onExecute(n,t)})}}}}),PT=nn([NT,HT,Sl({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:n.text}}}},schema:[ct("text")],name:"aria-descriptor"}),wl({schema:[Gu()],name:"menu",defaults:function(o){return{onExecute:function(t,e){t.getSystem().getByUid(o.uid).each(function(n){o.onItemExecute(n,t,e)})}}}}),Qy()]),zT=Al({name:"SplitDropdown",configFields:VT(),partFields:PT(),factory:function(o,n,t,e){function r(n){Zl.getCurrent(n).each(function(n){ad.highlightFirst(n),lg.focusIn(n)})}function i(n){ex(o,function(n){return n},n,e,r,Ay.HighlightFirst).get(Z)}function u(n){var t=Ks(n,o,"button");return io(t),on.some(!0)}var a=An(co([Ii(function(e,n){qs(e,o,"aria-descriptor").each(function(n){var t=Xo("aria");Po(n.element(),"id",t),Po(e.element(),"aria-describedby",t)})})]),rm(on.some(i))),c={repositionMenus:function(n){Cg.isOn(n)&&ux(n)}};return{uid:o.uid,dom:o.dom,components:n,apis:c,eventOrder:N(N({},o.eventOrder),{"alloy.execute":["disabling","toggling","alloy.base.behaviour"]}),events:a,behaviours:Rs(o.splitDropdownBehaviours,[Jy.config({others:{sandbox:function(n){var t=Ks(n,o,"arrow");return ix(o,n,{onOpen:function(){Cg.on(t),Cg.on(n)},onClose:function(){Cg.off(t),Cg.off(n)}})}}}),lg.config({mode:"special",onSpace:u,onEnter:u,onDown:function(n){return i(n),on.some(!0)}}),vg.config({}),Cg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(n,t){return n.repositionMenus(t)}}}),LT=Xo("focus-button"),jT=["checklist","ordered-list"],UT=["indent","outdent","table-insert-column-after","table-insert-column-before","unordered-list"],WT=function(n,t,e){return oE(n,{toolbarButtonBehaviours:[].concat(0<e.length?[Kd("toolbarButtonWith",e)]:[]),getApi:ZO,onSetup:n.onSetup},t)},GT=function(n,t,e){return Dn(oE(n,{toolbarButtonBehaviours:[mg.config({}),Cg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[Kd("toolbarToggleButtonWith",e)]:[]),getApi:nE,onSetup:n.onSetup},t))},XT=function(n,t,e){var o=t.label.fold(function(){return{}},function(n){return{"aria-label":n}}),r=vm(yy.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:o,selectOnFocus:!0,inputBehaviours:ba([lg.config({mode:"special",onEnter:function(n){return i.findPrimary(n).map(function(n){return io(n),!0})},onLeft:function(n,t){return t.cut(),on.none()},onRight:function(n,t){return t.cut(),on.none()}})])})),i=function(t,n,e){var o=S(n,function(n){return vm(uE(t,n,e))});return{asSpecs:function(){return S(o,function(n){return n.asSpec()})},findPrimary:function(e){return Du(n,function(n,t){return n.primary?on.from(o[t]).bind(function(n){return n.getOpt(e)}).filter(b(Ch.isDisabled)):on.none()})}}}(r,t.commands,e.shared.providers);return KO({type:n,uid:Xo("context-toolbar"),initGroups:[{title:on.none(),items:[r.asSpec()]},{title:on.none(),items:i.asSpecs()}],onEscape:on.none,cyclicKeying:!0,backstage:e,getSink:function(){return an.error("")}})},YT=function(t,e){function n(n){return n.dom()===e.getBody()}var o=we.fromDom(e.selection.getNode());return sE(o,t.inNodeScope).orThunk(function(){return sE(o,t.inEditorScope).orThunk(function(){return function(n,t,e){for(var o=n.dom(),r=dn(e)?e:nn(!1);o.parentNode;){o=o.parentNode;var i=we.fromDom(o),u=t(i);if(u.isSome())return u;if(r(i))break}return on.none()}(o,function(n){return sE(n,t.inNodeScope)},n)})})},qT=function(e,r){function o(t,e){var o=et(function(n){return tt("ContextForm",DT,n)}(e));(n[t]=o).launch.map(function(n){c["form:"+t]=N(N({},e.launch),{type:"contextformtogglebutton"===n.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?a.push(o):u.push(o),s[t]=o}function i(t,e){(function(n){return tt("ContextToolbar",AT,n)})(e).each(function(n){"editor"===e.scope?a.push(n):u.push(n),s[t]=n})}var n={},u=[],a=[],c={},s={},t=wn(e);return bn(t,function(n){var t=e[n];"contextform"===t.type?o(n,t):"contexttoolbar"===t.type&&i(n,t)}),{forms:n,inNodeScope:u,inEditorScope:a,lookupTable:s,formNavigators:c}},KT=Xo("forward-slide"),JT=Xo("backward-slide"),$T=Xo("change-slide-event"),QT="tox-pop--resizing";(vE=hE=hE||{})[vE.SemiColon=0]="SemiColon",vE[vE.Space=1]="Space";function ZT(n,t,e,o){return{type:"basic",data:function(n){return S(n,function(n){var t=n,e=n,o=n.split("=");return 1<o.length&&(t=o[0],e=o[1]),{title:t,format:e}})}(function(n,t){return t===hE.SemiColon?n.replace(/;$/,"").split(";"):n.split(" ")}(Nn(n.settings,t).getOr(e),o))}}function nB(e){function t(n){var t=E(RD,function(n){return e.formatter.match(n.format)}).fold(function(){return"left"},function(n){return n.title.toLowerCase()});ro(n,ik,{icon:"align-"+t})}var n=on.some(function(n){return function(){return t(n)}}),o=on.some(function(n){return t(n)}),r=function(n){return{type:"basic",data:n}}(RD);return{tooltip:"Align",icon:on.some("align-left"),isSelectedFor:function(n){return function(){return e.formatter.match(n)}},getCurrentValue:nn(on.none()),getPreviewFor:function(n){return function(){return on.none()}},onAction:lE(e),setInitialValue:o,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!e.formatter.canApply(n.format)}}}function tB(n){var t=n.split(/\s*,\s*/);return S(t,function(n){return n.replace(/^['"]+|['"]+$/g,"")})}function eB(r){function i(){function e(n){return n?tB(n)[0]:""}var n=r.queryCommandValue("FontName"),t=u.data,o=n?n.toLowerCase():"";return{matchOpt:E(t,function(n){var t=n.format;return t.toLowerCase()===o||e(t).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return function(n){var t;return 0===n.indexOf("-apple-system")&&(t=tB(n.toLowerCase()),D(VD,function(n){return-1<t.indexOf(n.toLowerCase())}))}(o)?on.from({title:"System Font",format:o}):on.none()}),font:n}}function t(n){var t=i(),e=t.matchOpt,o=t.font,r=e.fold(function(){return o},function(n){return n.title});ro(n,rk,{text:r})}var n=on.some(function(n){return function(){return t(n)}}),e=on.some(function(n){return t(n)}),u=ZT(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",hE.SemiColon);return{tooltip:"Fonts",icon:on.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(n){return function(){return on.some({tag:"div",styleAttr:-1===n.indexOf("dings")?"font-family:"+n:""})}},onAction:function(n){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,n.format)})}},setInitialValue:e,nodeChangeHandler:n,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function oB(n,t){return/[0-9.]+px$/.test(n)?function(n,t){var e=Math.pow(10,t);return Math.round(n*e)/e}(72*parseInt(n,10)/96,t||0)+"pt":n}function rB(e){function i(){var o=on.none(),r=u.data,i=e.queryCommandValue("FontSize");if(i)for(var n=function(n){var t=oB(i,n),e=function(n){return V(ND,n).getOr("")}(t);o=E(r,function(n){return n.format===i||n.format===t||n.format===e})},t=3;o.isNone()&&0<=t;t--)n(t);return{matchOpt:o,px:i}}function t(n){var t=i(),e=t.matchOpt,o=t.px,r=e.fold(function(){return o},function(n){return n.title});ro(n,rk,{text:r})}var n=nn(nn(on.none())),o=on.some(function(n){return function(){return t(n)}}),r=on.some(function(n){return t(n)}),u=ZT(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",hE.Space);return{tooltip:"Font sizes",icon:on.none(),isSelectedFor:function(t){return function(n){return n.exists(function(n){return n.format===t})}},getPreviewFor:n,getCurrentValue:function(){return i().matchOpt},onAction:function(n){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,n.format)})}},setInitialValue:r,nodeChangeHandler:o,dataset:u,shouldHide:!1,isInvalid:function(){return!1}}}function iB(e,n,t){var o=n();return Du(t,function(t){return E(o,function(n){return e.formatter.matchNode(t,n.format)})}).orThunk(function(){return e.formatter.match("p")?on.some({title:"Paragraph",format:"p"}):on.none()})}function uB(n){var t=n.selection.getStart(!0)||n.getBody();return n.dom.getParents(t,function(){return!0},n.getBody())}function aB(o){function e(n,t){var e=function(n){return iB(o,function(){return r.data},n)}(n).fold(function(){return"Paragraph"},function(n){return n.title});ro(t,rk,{text:e})}var n=on.some(function(t){return function(n){return e(n.parents,t)}}),t=on.some(function(n){var t=uB(o);e(t,n)}),r=ZT(o,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",hE.SemiColon);return{tooltip:"Blocks",icon:on.none(),isSelectedFor:function(n){return function(){return o.formatter.match(n)}},getCurrentValue:nn(on.none()),getPreviewFor:function(t){return function(){var n=o.formatter.get(t);return on.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styleAttr:o.formatter.getCssText(t)})}},onAction:lE(o),setInitialValue:t,nodeChangeHandler:n,dataset:r,shouldHide:!1,isInvalid:function(n){return!o.formatter.canApply(n.format)}}}function cB(i,n){function e(n,t){var e=function(n){var t=n.items;return t!==undefined&&0<t.length?B(t,e):[{title:n.title,format:n.format}]},o=B(Xk(i),e),r=iB(i,function(){return o},n).fold(function(){return"Paragraph"},function(n){return n.title});ro(t,rk,{text:r})}var t=on.some(function(t){return function(n){return e(n.parents,t)}}),o=on.some(function(n){var t=uB(i);e(t,n)});return{tooltip:"Formats",icon:on.none(),isSelectedFor:function(n){return function(){return i.formatter.match(n)}},getCurrentValue:nn(on.none()),getPreviewFor:function(t){return function(){var n=i.formatter.get(t);return n!==undefined?on.some({tag:0<n.length&&(n[0].inline||n[0].block)||"div",styleAttr:i.formatter.getCssText(t)}):on.none()}},onAction:lE(i),setInitialValue:o,nodeChangeHandler:t,shouldHide:i.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(n){return!i.formatter.canApply(n.format)},dataset:n}}function sB(o,r){return function(n,t){var e=o(n).mapError(function(n){return le(n)}).getOrDie();return r(e,t)}}function fB(n){var t=n.toolbar,e=n.buttons;return!1===t?[]:t===undefined||!0===t?function(e){var n=S(HD,function(n){var t=C(n.items,function(n){return En(e,n)||En(zD,n)});return{name:n.name,items:t}});return C(n,function(n){return 0<n.items.length})}(e):cn(t)?function(n){var t=n.split("|");return S(t,function(n){return{items:n.trim().split(" ")}})}(t):function(n){return h(n,function(n){return En(n,"name")&&En(n,"items")})}(t)?t:(v.console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])}function lB(t,e,o,r,n){return V(e,o.toLowerCase()).orThunk(function(){return n.bind(function(n){return Du(n,function(n){return V(e,n+o.toLowerCase())})})}).fold(function(){return V(zD,o.toLowerCase()).map(function(n){return n(t,r)}).orThunk(function(){return on.none()})},function(n){return function(t,e){return V(PD,t.type).fold(function(){return v.console.error("skipping button defined by",t),on.none()},function(n){return on.some(n(t,e))})}(n,r)})}function dB(e,o,r,i){var n=fB(o),t=S(n,function(n){var t=B(n.items,function(n){return 0===n.trim().length?[]:lB(e,o.buttons,n,r,i).toArray()});return{title:on.from(e.translate(n.name)),items:t}});return C(t,function(n){return 0<n.items.length})}function mB(e){return(Sr(e,"position").is("fixed")?on.none():To(e)).orThunk(function(){var n=we.fromTag("span");Ao(e,n);var t=To(n);return Pi(n),t}).map(du).getOrThunk(function(){return Iu(0,0)})}function gB(t){return function(n){return n.translate(-t.left(),-t.top())}}function pB(t){return function(n){return n.translate(t.left(),t.top())}}function hB(e){return function(n,t){return O(e,function(n,t){return t(n)},Iu(n,t))}}function vB(n,t,e){return n.fold(hB([pB(e),gB(t)]),hB([gB(t)]),hB([]))}function bB(n,t,e){return n.fold(hB([pB(e)]),hB([]),hB([pB(t)]))}function yB(n,t,e){return n.fold(hB([]),hB([gB(e)]),hB([pB(t),gB(e)]))}function xB(n,t,e){return n.fold(function(n,t){return{position:"absolute",left:n+"px",top:t+"px"}},function(n,t){return{position:"absolute",left:n-e.left()+"px",top:t-e.top()+"px"}},function(n,t){return{position:"fixed",left:n+"px",top:t+"px"}})}function wB(n,i,u,a){function t(o,r){return function(n,t){var e=o(i,u,a);return r(n.getOr(e.left()),t.getOr(e.top()))}}return n.fold(t(yB,ZD.offset),t(bB,ZD.absolute),t(vB,ZD.fixed))}function SB(n,t){var e=n.element();fr(e,t.transitionClass),dr(e,t.fadeOutClass),fr(e,t.fadeInClass),t.onShow(n)}function CB(n,t){var e=n.element();fr(e,t.transitionClass),dr(e,t.fadeInClass),fr(e,t.fadeOutClass),t.onHide(n)}function kB(n,t,e){return D(n,function(n){switch(n){case"bottom":return function(n,t){return n.bottom()<=t.bottom()}(t,e);case"top":return function(n,t){return n.y()>=t.y()}(t,e)}})}function OB(n,t){return Lo(n,t)?on.some(parseInt(zo(n,t),10)):on.none()}function EB(r,n){return OB(r,n.leftAttr).bind(function(o){return OB(r,n.topAttr).map(function(n){var t=mu(r),e=su(r);return yu(o,n,t,e)})})}function TB(n,t,e){var o=zo(n,t.positionAttr);switch(function(n,t){jo(n,t.leftAttr),jo(n,t.topAttr),jo(n,t.positionAttr)}(n,t),o){case"static":return on.some(oA["static"]());case"absolute":return on.some(oA.absolute(e.x(),e.y()));default:return on.none()}}function BB(n,t,e,o,r){var i=xu(n);if(kB(t.modes,i,e))return on.none();var u=r(),a=wr(n,"position");!function(n,t,e,o,r){Po(n,t.leftAttr,e),Po(n,t.topAttr,o),Po(n,t.positionAttr,r)}(n,t,i.x(),i.y(),a);var c=tA(i.x(),i.y()),s=vB(c,o,u),f=tA(e.x(),e.y()),l=vB(f,o,u),d=i.y()<=e.y()?l.top():l.top()+e.height()-i.height();return on.some(oA.fixed(s.left(),d))}function DB(n,t,e,o,r){var i=n.element();return Sr(i,"position").is("fixed")?function(t,e,o){return EB(t,e).filter(function(n){return kB(e.modes,n,o)}).bind(function(n){return TB(t,e,n)})}(i,t,e):BB(i,t,e,o,r)}function AB(t,n){bn(["left","top","position"],function(n){return kr(t.element(),n)}),n.onUndocked(t)}function _B(n,t,e,o,r){var i=xB(r,0,o);xr(n.element(),i),("fixed"===i.position?t.onDocked:t.onUndocked)(n)}function MB(o,n,r,i,u){void 0===u&&(u=!1),n.contextual.each(function(e){e.lazyContext(o).each(function(n){var t=function(n,t){return n.y()<t.bottom()&&n.bottom()>t.y()}(n,i);t!==r.isVisible()&&(r.setVisible(t),u&&!t?(gr(o.element(),[e.fadeOutClass]),e.onHide(o)):(t?SB:CB)(o,e))})})}function FB(r,i,n){var u=r.element();n.setDocked(!1),function(n,t){var e=n.element();return EB(e,t).bind(function(n){return TB(e,t,n)})}(r,i).each(function(n){n.fold(function(){return AB(r,i)},function(n,t){var e=Co(u),o=(pu(e),mB(u));_B(r,i,0,o,tA(n,t))},Z)}),n.setVisible(!0),i.contextual.each(function(n){pr(u,[n.fadeInClass,n.fadeOutClass,n.transitionClass]),n.onShow(r)}),rA(r,i,n)}function IB(n,t,e){e.isDocked()&&FB(n,t,e)}function RB(o){var r=o.element();Eo(r).each(function(n){if(cA.isDocked(o)){var t=mu(n);yr(r,"width",t+"px");var e=fu(r);yr(n,"padding-top",e+"px")}else kr(r,"width"),kr(n,"padding-top")})}function VB(n,t){t?(dr(n,sA.fadeOutClass),gr(n,[sA.transitionClass,sA.fadeInClass])):(dr(n,sA.fadeInClass),gr(n,[sA.fadeOutClass,sA.transitionClass]))}function NB(n,t){var e=we.fromDom(n.getContainer());t?(fr(e,fA),dr(e,lA)):(fr(e,lA),dr(e,fA))}function HB(i,e){function o(t){e().each(function(n){return t(n.element())})}function n(n){i.inline||RB(n),NB(i,cA.isDocked(n)),n.getSystem().broadcastOn([jf()],{}),e().each(function(n){return n.getSystem().broadcastOn([jf()],{})})}var r=ye(on.none()),t=i.inline?[]:function(){var n;return[lc.config({channels:(n={},n[mT()]={onReceive:RB},n)})]}();return g([vg.config({}),cA.config({leftAttr:"data-dock-left",topAttr:"data-dock-top",positionAttr:"data-dock-pos",contextual:N({lazyContext:function(n){var t=fu(n.element()),e=i.inline?i.getContentAreaContainer():i.getContainer(),o=xu(we.fromDom(e)),r=o.height()-t;return on.some(yu(o.x(),o.y(),o.width(),r))},onShow:function(){o(function(n){return VB(n,!0)})},onShown:function(t){o(function(n){return pr(n,[sA.transitionClass,sA.fadeInClass])}),r.get().each(function(n){!function(t,e){var o=Co(e);Sa(o).filter(function(n){return!jt(e,n)}).filter(function(n){return jt(n,we.fromDom(o.dom().body))||Ye(t,n)}).each(function(){return wa(e)})}(t.element(),n),r.set(on.none())})},onHide:function(n){r.set(function(n,t){return Ca(n).orThunk(function(){return t().toOption().bind(function(n){return Ca(n.element())})})}(n.element(),e)),o(function(n){return VB(n,!1)})},onHidden:function(){o(function(n){return pr(n,[sA.transitionClass])})}},sA),modes:["top"],onDocked:n,onUndocked:n})],t)}function PB(n){return"<alloy.field."+n+">"}function zB(n){return{element:function(){return n.element().dom()}}}function LB(e,o){var r=S(wn(o),function(n){var t=o[n],e=et(function(n){return tt("sidebar",CA,n)}(t));return{name:n,getApi:zB,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return S(r,function(n){var t=ye(Z);return e.slot(n.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Jp([Ep(n,t),Tp(n,t),lo(Bi(),function(t,n){var e=n.event();E(r,function(n){return n.name===e.name()}).each(function(n){(e.visible()?n.onShow:n.onHide)(n.getApi(t))})})])})})}function jB(n,t){Zl.getCurrent(n).each(function(n){return mg.set(n,[function(t){return SA.sketch(function(n){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:LB(n,t),slotBehaviours:Jp([Ii(function(n){return SA.hideAllSlots(n)})])}})}(t)])})}function UB(n){return Zl.getCurrent(n).bind(function(n){return cT.isGrowing(n)||cT.hasGrown(n)?Zl.getCurrent(n).bind(function(t){return E(SA.getSlotNames(t),function(n){return SA.isShowing(t,n)})}):on.none()})}function WB(n,t,e){var o=n.element();!0===t?(mg.set(n,[function(n){return{dom:{tag:"div",attributes:{"aria-label":n.translate("Loading...")},classes:["tox-throbber__busy-spinner"]},components:[{dom:cp('<div class="tox-spinner"><div></div><div></div><div></div></div>')}],behaviours:ba([lg.config({mode:"special",onTab:function(){return on.some(!0)},onShiftTab:function(){return on.some(!0)}}),vg.config({})])}}(e)]),kr(o,"display"),jo(o,"aria-hidden")):(mg.set(n,[]),yr(o,"display","none"),Po(o,"aria-hidden","true"))}function GB(n){return"string"==typeof n?n.split(" "):n}function XB(e,o){var r=An(IA,o.menus),t=0<wn(o.menus).length,n=o.menubar===undefined||!0===o.menubar?GB("file edit view insert format tools table help"):GB(!1===o.menubar?"":o.menubar),i=C(n,function(n){return t&&o.menus.hasOwnProperty(n)&&o.menus[n].hasOwnProperty("items")||IA.hasOwnProperty(n)}),u=S(i,function(n){var t=r[n];return function(n,e,t){var o=function(n){return n.getParam("removed_menuitems","")}(t).split(/[ ,]/);return{text:n.title,getItems:function(){return B(n.items,function(n){var t=n.toLowerCase();return 0===t.trim().length?[]:x(o,function(n){return n===t})?[]:"separator"===t||"|"===t?[{type:"separator"}]:e.menuItems[t]?[e.menuItems[t]]:[]})}}}({title:t.title,items:GB(t.items)},o,e)});return C(u,function(n){return 0<n.getItems().length&&x(n.getItems(),function(n){return"separator"!==n.type})})}function YB(n,t){var e,o=function(n){var t=n.settings,e=t.skin,o=t.skin_url;if(!1!==e){var r=e||"oxide";o=o?n.documentBaseURI.toAbsolute(o):Gb.baseURL+"/skins/ui/"+r}return o}(t);o&&(e=o+"/skin.min.css",t.contentCSS.push(o+(n?"/content.inline":"/content")+".min.css")),!1===function(n){return!1===n.getParam("skin")}(t)&&e?Rh.DOM.styleSheetLoader.load(e,RA(t)):RA(t)()}function qB(t,n,e,o){var r=n.outerContainer,i=e.toolbar,u=e.buttons;if(h(i,cn)){var a=i.map(function(n){return dB(t,{toolbar:n,buttons:u},{backstage:o},on.none())});FA.setToolbars(r,a)}else FA.setToolbar(r,dB(t,e,{backstage:o},on.none()))}function KB(n){return function(n){var t=Fb(n),e=Vb(n),o=Hb(n);return UA(t).map(function(n){return jA(n,e,o)})}(n).getOr(Fb(n))}function JB(n){var t=Ib(n),e=Rb(n),o=Nb(n);return UA(t).map(function(n){return jA(n,e,o)})}function $B(n,t){return function(){n.execCommand("mceToggleFormat",!1,t)}}function QB(n){!function(e){yk.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(n,t){e.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onSetup:fE(e,n.name),onAction:$B(e,n.name)})});for(var n=1;n<=6;n++){var t="h"+n;e.ui.registry.addToggleButton(t,{text:t.toUpperCase(),tooltip:"Heading "+n,onSetup:fE(e,t),onAction:$B(e,t)})}}(n),function(t){yk.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(n){t.ui.registry.addButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)}})})}(n),function(t){yk.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,icon:n.icon,onAction:function(){return t.execCommand(n.action)},onSetup:fE(t,n.name)})})}(n)}function ZB(n,t,e){function o(){return!!t.undoManager&&t.undoManager[e]()}function r(){n.setDisabled(t.readonly||!o())}return n.setDisabled(!o()),t.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return t.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}}function nD(n,t){return{anchor:"makeshift",x:n,y:t}}function tD(n){return"longpress"===n.type||0===n.type.indexOf("touch")}function eD(n,t){var e=Rh.DOM.getPos(n);return function(n,t,e){return nD(n.x+t,n.y+e)}(t,e.x,e.y)}function oD(n,t){return"contextmenu"===t.type?n.inline?function(n){if(tD(n)){var t=n.touches[0];return nD(t.pageX,t.pageY)}return nD(n.pageX,n.pageY)}(t):eD(n.getContentAreaContainer(),function(n){if(tD(n)){var t=n.touches[0];return nD(t.clientX,t.clientY)}return nD(n.clientX,n.clientY)}(t)):ZA(n)}function rD(n){return{anchor:"node",node:on.some(we.fromDom(n.selection.getNode())),root:we.fromDom(n.getBody())}}function iD(n,t,e,o,r,i){var u=e(),a=function(n,t,e){return e?rD(n):oD(n,t)}(n,t,i);NC(u,Fh.CLOSE_ON_EXECUTE,o,!1).map(function(n){t.preventDefault(),jg.showMenuAt(r,a,{menu:{markers:wv("normal")},data:n})})}function uD(t,e,n,o,r,i){var u=function(n,t){var e=t?rD(n):ZA(n);return N({bubble:Oa(0,12,t_),layouts:n_,overrides:{maxWidthFunction:RE(),maxHeightFunction:Cf()}},e)}(t,i);NC(n,Fh.CLOSE_ON_EXECUTE,o,!0).map(function(n){e.preventDefault(),jg.showMenuWithinBounds(r,u,{menu:{markers:wv("normal")},data:n,type:"horizontal"},function(){return on.some(cE(t))}),t.fire(YE)})}function aD(t,e,o,r,i,u){function n(){var n=o();uD(t,e,n,r,i,u)}var a=Ht(),c=a.os.isiOS(),s=a.os.isOSX(),f=a.os.isAndroid();if(!s&&!c||u)f&&!u&&t.selection.setCursorLocation(e.target,0),n();else{var l=function(){!function(n){function t(){qg.setEditorTimeout(n,function(){n.selection.setRng(e)},10),i()}var e=n.selection.getRng();n.once("touchend",t);function o(n){n.preventDefault(),n.stopImmediatePropagation()}n.on("mousedown",o,!0);function r(){return i()}n.once("longpresscancel",r);var i=function(){n.off("touchend",t),n.off("longpresscancel",r),n.off("mousedown",o)}}(t),n()};!function(n,t){var e=n.selection;if(e.isCollapsed()||t.touches.length<1)return!1;var o=t.touches[0],r=e.getRng();return Uc(n.getWin(),Tc.domRange(r)).exists(function(n){return n.left()<=o.clientX&&n.right()>=o.clientX&&n.top()<=o.clientY&&n.bottom()>=o.clientY})}(t,e)?(t.once("selectionchange",l),t.once("touchend",function(){return t.off("selectionchange",l)})):l()}}function cD(n){return"string"==typeof n?n.split(/[ ,]/):n}function sD(n){return cn(n)?"|"===n:"separator"===n.type}function fD(n,t){if(0===t.length)return n;var e=F(n).filter(function(n){return!sD(n)}).fold(function(){return[]},function(n){return[i_]});return n.concat(e).concat(t).concat([i_])}function lD(i,n,t){function e(n){return jg.hide(a)}function o(o){var n="longpress"===o.type;if(e_(i)&&o.preventDefault(),!function(n,t){return t.ctrlKey&&!e_(n)}(i,o)&&!r_(i)){var r=!n&&(2!==o.button||o.target===i.getBody());(u()?aD:iD)(i,o,function(){var n=r?i.selection.getStart(!0):o.target,t=i.ui.registry.getAll(),e=o_(i);return function(r,n,i){var t=O(n,function(n,t){if(En(r,t)){var e=r[t].update(i);if(cn(e))return fD(n,e.split(" "));if(0<e.length){var o=S(e,u_);return fD(n,o)}return n}return n.concat([t])},[]);return 0<t.length&&sD(t[t.length-1])&&t.pop(),t}(t.contextMenus,e,n)},t,a,r)}}var u=Ht().deviceType.isTouch,a=uu(jg.sketch({dom:{tag:"div"},lazySink:n,onEscape:function(){return i.focus()},onShow:function(){return t.setContextMenuState(!0)},onHide:function(){return t.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:ba([Kd("dismissContextMenu",[lo(Oi(),function(n,t){zf.close(n),i.focus()})])])}));i.on("init",function(){var n="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(u()?"":" ResizeWindow");i.on(n,e),i.on(u()?"longpress":"longpress contextmenu",o)})}function dD(n,t){n.getSystem().addToGui(t),function(n){Eo(n.element()).filter(Hr).each(function(t){Sr(t,"z-index").each(function(n){Po(t,a_,n)}),yr(t,"z-index",wr(n.element(),"z-index"))})}(t)}function mD(n){!function(n){Eo(n.element()).filter(Hr).each(function(n){var t=zo(n,a_);Lo(n,a_)?yr(n,"z-index",t):kr(n,"z-index"),jo(n,a_)})}(n),n.getSystem().removeFromGui(n)}function gD(n,t,e){return n.getSystem().build(Wb.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[t]},events:e}))}function pD(n,t,e,o){return function(n,t){var e=n.element(),o=parseInt(zo(e,t.leftAttr),10),r=parseInt(zo(e,t.topAttr),10);return isNaN(o)||isNaN(r)?on.none():on.some(Iu(o,r))}(n,t).fold(function(){return e},function(n){return eA(n.left()+o.left(),n.top()+o.top())})}function hD(n,t,e,o,r,i){var u=pD(n,t,e,o),a=t.mustSnap?f_(n,t,u,r,i):l_(n,t,u,r,i),c=vB(u,r,i);return function(n,t,e){var o=n.element();Po(o,t.leftAttr,e.left()+"px"),Po(o,t.topAttr,e.top()+"px")}(n,t,c),a.fold(function(){return{coord:eA(c.left(),c.top()),extra:on.none()}},function(n){return{coord:n.output(),extra:n.extra()}})}function vD(n,t){!function(n,t){var e=n.element();jo(e,t.leftAttr),jo(e,t.topAttr)}(n,t)}function bD(n,e,o,r){return Du(n,function(n){var t=n.sensor();return function(n,t,e,o,r,i){var u=bB(n,r,i),a=bB(t,r,i);return Math.abs(u.left()-a.left())<=e&&Math.abs(u.top()-a.top())<=o}(e,t,n.range().left(),n.range().top(),o,r)?on.some({output:nn(wB(n.output(),e,o,r)),extra:n.extra}):on.none()})}function yD(t){return function(n,t,e,o){return n.isSome()&&t.isSome()&&e.isSome()?on.some(o(n.getOrDie(),t.getOrDie(),e.getOrDie())):on.none()}(Sr(t,"left"),Sr(t,"top"),Sr(t,"position"),function(n,t,e){return("fixed"===e?eA:nA)(parseInt(n,10),parseInt(t,10))}).getOrThunk(function(){var n=du(t);return tA(n.left(),n.top())})}function xD(e,n,o,r,i,u,t){return function(n,t,e,o,r){var i=r.bounds,u=bB(t,e,o),a=us(u.left(),i.x(),i.x()+i.width()-r.width),c=us(u.top(),i.y(),i.y()+i.height()-r.height),s=tA(a,c);return t.fold(function(){var n=yB(s,e,o);return nA(n.left(),n.top())},function(){return s},function(){var n=vB(s,e,o);return eA(n.left(),n.top())})}(0,n.fold(function(){var n=function(n,e,o){return n.fold(function(n,t){return ZD.offset(n+e,t+o)},function(n,t){return ZD.absolute(n+e,t+o)},function(n,t){return ZD.fixed(n+e,t+o)})}(o,u.left(),u.top()),t=vB(n,r,i);return eA(t.left(),t.top())},function(t){var n=hD(e,t,o,u,r,i);return n.extra.each(function(n){t.onSensor(e,n)}),n.coord}),r,i,t)}function wD(n,t){return{bounds:n.getBounds(),height:fu(t.element()),width:gu(t.element())}}function SD(t,e,n,o,r){var i=n.update(o,r),u=n.getStartData().getOrThunk(function(){return wD(e,t)});i.each(function(n){!function(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=Co(n.element()),u=pu(i),a=mB(r),c=yD(r),s=xD(n,t.snaps,c,u,a,o,e),f=xB(s,0,a);xr(r,f)}t.onDrag(n,r,o)}(t,e,u,n)})}function CD(t,n,e,o){n.each(mD),e.snaps.each(function(n){vD(t,n)});var r=e.getTarget(t.element());o.reset(),e.onDrop(t,r)}function kD(n){return function(t,e){function o(n){e.setStartData(wD(t,n))}return co(g([lo(wi(),function(n){e.getStartData().each(function(){return o(n)})})],n(t,e,o)))}}function OD(u,a,c){return[lo(Yr(),function(t,n){if(0===n.event().raw().button){n.stop();var e=function(){return CD(t,on.some(i),u,a)},o=hb(e,200),r={drop:e,delayDrop:o.schedule,forceDrop:e,move:function(n){o.cancel(),SD(t,u,a,d_,n)}},i=gD(t,u.blockerClass,function(e){return co([lo(Yr(),e.forceDrop),lo(Jr(),e.drop),lo(qr(),function(n,t){e.move(t.event())}),lo(Kr(),e.delayDrop)])}(r));c(t),dD(t,i)}})]}function ED(i,u,a){var c=ye(on.none());return[lo(Ur(),function(t,n){n.stop();function e(){CD(t,c.get(),i,u),c.set(on.none())}var o={drop:e,delayDrop:function(){},forceDrop:e,move:function(n){SD(t,i,u,g_,n)}},r=gD(t,i.blockerClass,function(e){return co([lo(Ur(),e.forceDrop),lo(Gr(),e.drop),lo(Xr(),e.drop),lo(Wr(),function(n,t){e.move(t.event())})])}(o));c.set(on.some(r));a(t),dD(t,r)}),lo(Wr(),function(n,t){t.stop(),SD(n,i,u,g_,t.event())}),lo(Gr(),function(n,t){t.stop(),CD(n,c.get(),i,u),c.set(on.none())}),lo(Xr(),function(n){CD(n,c.get(),i,u),c.set(on.none())})]}function TD(n,r,i,u,t,e){return n.fold(function(){return w_.snap({sensor:tA(i-20,u-20),range:Iu(t,e),output:tA(on.some(i),on.some(u)),extra:{td:r}})},function(n){var t=i-20,e=u-20,o=n.element().dom().getBoundingClientRect();return w_.snap({sensor:tA(t,e),range:Iu(40,40),output:tA(on.some(i-o.width/2),on.some(u-o.height/2)),extra:{td:r}})})}function BD(n,o,r){return{getSnapPoints:n,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(n,t){var e=t.td;!function(n,t){return n.exists(function(n){return jt(n,t)})}(o.get(),e)&&(o.set(on.some(e)),r(e))},mustSnap:!0}}function DD(n){return vm(Gg.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:ba([w_.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:n}),Ew.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))}var AD,_D,MD,FD,ID,RD=[{title:"Left",icon:"align-left",format:"alignleft"},{title:"Center",icon:"align-center",format:"aligncenter"},{title:"Right",icon:"align-right",format:"alignright"},{title:"Justify",icon:"align-justify",format:"alignjustify"}],VD=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],ND={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},HD=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],PD={button:sB(JO,function(n,t){return function(n,t){return WT(n,t,[])}(n,t.backstage.shared.providers)}),togglebutton:sB(QO,function(n,t){return function(n,t){return GT(n,t,[])}(n,t.backstage.shared.providers)}),menubutton:sB($O,function(n,t){return PC(n,"tox-tbtn",t.backstage,on.none())}),splitbutton:sB(function(n){return tt("SplitButton",xT,n)},function(n,t){return rE(n,t.backstage.shared)}),styleSelectButton:function(n,t){return function(n,t){var e=N({type:"advanced"},t.styleselect);return mE(n,t,cB(n,e))}(n,t.backstage)},fontsizeSelectButton:function(n,t){return function(n,t){return mE(n,t,rB(n))}(n,t.backstage)},fontSelectButton:function(n,t){return function(n,t){return mE(n,t,eB(n))}(n,t.backstage)},formatButton:function(n,t){return function(n,t){return mE(n,t,aB(n))}(n,t.backstage)},alignMenuButton:function(n,t){return function(n,t){return mE(n,t,nB(n))}(n,t.backstage)}},zD={styleselect:PD.styleSelectButton,fontsizeselect:PD.fontsizeSelectButton,fontselect:PD.fontSelectButton,formatselect:PD.formatButton,align:PD.alignMenuButton},LD={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},jD={maxHeightFunction:Cf(),maxWidthFunction:RE()},UD={onLtr:function(){return[oc,rc,ca,ua,sa,aa,Ug,Wg,pm,mm,hm,gm]},onRtl:function(){return[oc,rc,sa,aa,ca,ua,Ug,Wg,hm,gm,pm,mm]}},WD={onLtr:function(){return[rc,ua,aa,ca,sa,oc,Ug,Wg,pm,mm,hm,gm]},onRtl:function(){return[rc,aa,ua,sa,ca,oc,Ug,Wg,hm,gm,pm,mm]}},GD=function(u,n,e,a){function c(){return cE(u)}function s(){if(l()&&a.backstage.isContextMenuOpen())return!0;var n=function(){var n=g.get().map(function(n){return n.getBoundingClientRect()}).getOrThunk(function(){return u.selection.getRng().getBoundingClientRect()}),t=u.inline?pu().top():wu(we.fromDom(u.getBody())).y();return{y:n.top+t,bottom:n.bottom+t}}(),t=c();return!function(n,t,e,o){return Math.max(n,e)<=Math.min(t,o)}(n.y,n.bottom,t.y(),t.bottom())}function t(){jg.hide(d)}function o(){m.get().each(function(n){var t=d.element();kr(t,"display"),s()?yr(t,"display","none"):Af.positionWithinBounds(e,n,d,on.some(c()))})}function f(n){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[n],behaviours:ba([lg.config({mode:"acyclic"}),Kd("pop-dialog-wrap-events",[Ii(function(n){u.shortcuts.add("ctrl+F9","focus statusbar",function(){return lg.focusIn(n)})}),Ri(function(n){u.shortcuts.remove("ctrl+F9")})])])}}var l=Ht().deviceType.isTouch,d=uu(function(n){var e=ye([]);return jg.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(n){e.set([]),jg.getContent(n).each(function(n){kr(n.element(),"visibility")}),dr(n.element(),QT),kr(n.element(),"width")},inlineBehaviours:ba([Kd("context-toolbar-events",[bo(ii(),function(n,t){jg.getContent(n).each(function(n){}),dr(n.element(),QT),kr(n.element(),"width")}),lo($T,function(t,e){kr(t.element(),"width");var n=mu(t.element());jg.setContent(t,e.event().contents()),fr(t.element(),QT);var o=mu(t.element());yr(t.element(),"width",n+"px"),jg.getContent(t).each(function(n){e.event().focus().bind(function(n){return wa(n),Ca(t.element())}).orThunk(function(){return lg.focusIn(n),Sa()})}),qg.setTimeout(function(){yr(t.element(),"width",o+"px")},0)}),lo(KT,function(n,t){jg.getContent(n).each(function(n){e.set(e.get().concat([{bar:n,focus:Sa()}]))}),ro(n,$T,{contents:t.event().forwardContents(),focus:on.none()})}),lo(JT,function(t,n){F(e.get()).each(function(n){e.set(e.get().slice(0,e.get().length-1)),ro(t,$T,{contents:au(n.bar),focus:n.focus})})})]),lg.config({mode:"special",onEscape:function(t){return F(e.get()).fold(function(){return n.onEscape()},function(n){return oo(t,JT),on.some(!0)})}})]),lazySink:function(){return an.value(n.sink)}})}({sink:e,onEscape:function(){return u.focus(),on.some(!0)}})),m=ye(on.none()),g=ye(on.none()),r=ye(null),p=L(function(){return qT(n,function(n){var t=h(n);ro(d,KT,{forwardContents:f(t)})})}),h=function(n){var t,e,o=u.ui.registry.getAll().buttons,r=Yb(u)===jb.scrolling?jb.scrolling:jb["default"],i=p();return"contexttoolbar"===n.type?(t=An(o,i.formNavigators),e=dB(u,{buttons:t,toolbar:n.items},a,on.some(["form:"])),KO({type:r,uid:Xo("context-toolbar"),initGroups:e,onEscape:on.none,cyclicKeying:!0,backstage:a.backstage,getSink:function(){return an.error("")}})):XT(r,n,a.backstage)};u.on("contexttoolbar-show",function(t){var n=p();Nn(n.lookupTable,t.toolbarKey).each(function(n){y(n,t.target===u?on.none():on.some(t)),jg.getContent(d).each(lg.focusIn)})});function v(n,t){var e="node"===n?a.backstage.shared.anchors.node(t):a.backstage.shared.anchors.cursor();return Dn(e,function(n,t){return"line"===n?{bubble:Oa(12,0,LD),layouts:{onLtr:function(){return[fa]},onRtl:function(){return[la]}},overrides:jD}:{bubble:Oa(0,12,LD),layouts:t?WD:UD,overrides:jD}}(n,l()))}function i(){var n=p();YT(n,u).fold(function(){m.set(on.none()),jg.hide(d)},function(n){y(n.toolbarApi,on.some(n.elem.dom()))})}function b(n){x(),r.set(n)}var y=function(n,t){if(x(),!l()||!a.backstage.isContextMenuOpen()){var e=h(n),o=t.map(we.fromDom),r=v(n.position,o);m.set(on.some(r)),g.set(t);var i=d.element();kr(i,"display"),jg.showWithinBounds(d,r,f(e),function(){return on.some(c())}),s()&&yr(i,"display","none")}},x=function(){var n=r.get();null!==n&&(qg.clearTimeout(n),r.set(null))};u.on("init",function(){u.on(YE,t),u.on("ScrollContent ScrollWindow longpress",o),u.on("click keyup SetContent ObjectResized ResizeEditor",function(n){b(qg.setEditorTimeout(u,i,0))}),u.on("focusout",function(n){qg.setEditorTimeout(u,function(){Ca(e.element()).isNone()&&Ca(d.element()).isNone()&&(m.set(on.none()),jg.hide(d))},0)}),u.on("SwitchMode",function(){u.readonly&&(m.set(on.none()),jg.hide(d))}),u.on("NodeChange",function(n){Ca(d.element()).fold(function(){b(qg.setEditorTimeout(u,i,0))},function(n){})})})},XD=function(n,o,r){function t(t,e){bn([o,r],function(n){n.broadcastEvent(t,e)})}function e(t,e){bn([o,r],function(n){n.broadcastOn([t],e)})}function i(n){return e(Lf(),{target:n.target()})}function u(n){return e(Lf(),{target:we.fromDom(n.target)})}function a(n){0===n.button&&e(Uf(),{target:we.fromDom(n.target)})}function c(n){return t(wi(),pb(n))}function s(n){e(jf(),{}),t(Si(),pb(n))}function f(){return e(jf(),{})}var l=sb(we.fromDom(v.document),"touchstart",i),d=sb(we.fromDom(v.document),"touchmove",function(n){return t(yi(),n)}),m=sb(we.fromDom(v.document),"touchend",function(n){return t(xi(),n)}),g=sb(we.fromDom(v.document),"mousedown",i),p=sb(we.fromDom(v.document),"mouseup",function(n){0===n.raw().button&&e(Uf(),{target:n.target()})});n.on("PostRender",function(){n.on("click",u),n.on("tap",u),n.on("mouseup",a),n.on("ScrollWindow",c),n.on("ResizeWindow",s),n.on("ResizeEditor",f)}),n.on("remove",function(){n.off("click",u),n.off("tap",u),n.off("mouseup",a),n.off("ScrollWindow",c),n.off("ResizeWindow",s),n.off("ResizeEditor",f),g.unbind(),l.unbind(),d.unbind(),m.unbind(),p.unbind()}),n.on("detach",function(){ws(o),ws(r),o.destroy(),r.destroy()})},YD=Tl,qD=Ol,KD=nn([St("shell",!1),ct("makeItem"),St("setupItem",Z),nl("listBehaviours",[mg])]),JD=Sl({name:"items",overrides:function(n){return{behaviours:ba([mg.config({})])}}}),$D=nn([JD]),QD=Al({name:nn("CustomList")(),configFields:KD(),partFields:$D(),factory:function(s,n,t,e){var o=s.shell?{behaviours:[mg.config({})],components:[]}:{behaviours:[],components:n},r=function(n){return s.shell?on.some(n):qs(n,s,"items")};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:Rs(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){r(a).fold(function(){throw v.console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(t){var n=mg.contents(t),e=c.length,o=e-n.length,r=0<o?function(n,t){for(var e=[],o=0;o<n;o++)e.push(t(o));return e}(o,function(){return s.makeItem()}):[],i=n.slice(e);bn(i,function(n){return mg.remove(t,n)}),bn(r,function(n){return mg.append(t,n)});var u=mg.contents(t);bn(u,function(n,t){s.setupItem(a,n,c[t],t)})})}}}},apis:{setItems:function(n,t,e){n.setItems(t,e)}}}),ZD=Tn([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),nA=ZD.offset,tA=ZD.absolute,eA=ZD.fixed,oA=Tn([{"static":[]},{absolute:["x","y"]},{fixed:["x","y"]}]),rA=function(n,t,e){n.getSystem().isConnected()&&function(e,o,r){var i=o.lazyViewport(e),n=e.element(),t=Co(n),u=pu(t),a=L(function(){return mB(n)}),c=r.isDocked();c&&MB(e,o,r,i),DB(e,o,i,u,a).each(function(n){r.setDocked(!c),n.fold(function(){return AB(e,o)},function(n,t){return _B(e,o,0,a(),tA(n,t))},function(n,t){MB(e,o,r,i,!0),_B(e,o,0,a(),eA(n,t))})})}(n,t,e)},iA=/* */Object.freeze({refresh:rA,reset:IB,isDocked:function(n,t,e){return e.isDocked()}}),uA=/* */Object.freeze({events:function(o,r){return co([bo(ii(),function(t,e){o.contextual.each(function(n){mr(t.element(),n.transitionClass)&&(pr(t.element(),[n.transitionClass,n.fadeInClass]),(r.isVisible()?n.onShown:n.onHidden)(t));e.stop()})}),lo(wi(),function(n,t){rA(n,o,r)}),lo(Si(),function(n,t){IB(n,o,r)})])}}),aA=[wt("contextual",[ft("fadeInClass"),ft("fadeOutClass"),ft("transitionClass"),dt("lazyContext"),qu("onShow"),qu("onShown"),qu("onHide"),qu("onHidden")]),Bt("lazyViewport",Su),ft("leftAttr"),ft("topAttr"),ft("positionAttr"),(AD="modes",_D=["top","bottom"],MD=ge,Ct(AD,_D,Kn(MD))),qu("onDocked"),qu("onUndocked")],cA=ya({fields:aA,name:"docking",active:uA,apis:iA,state:/* */Object.freeze({init:function(){var t=ye(!1),e=ye(!0);return nu({isDocked:function(){return t.get()},setDocked:function(n){return t.set(n)},isVisible:function(){return e.get()},setVisible:function(n){return e.set(n)},readState:function(){return"docked:  "+t.get()+", visible: "+e.get()}})}})}),sA={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},fA="tox-tinymce--toolbar-sticky-on",lA="tox-tinymce--toolbar-sticky-off",dA=/* */Object.freeze({setup:function(n,t){n.inline||(n.on("ResizeWindow ResizeEditor ResizeContent",function(){t().each(RB)}),n.on("SkinLoaded",function(){t().each(cA.reset)}),n.on("FullscreenStateChanged",function(){t().each(cA.refresh)})),n.on("PostRender",function(){NB(n,!1)})},isDocked:function(n){return n().map(cA.isDocked).getOr(!1)},getBehaviours:HB}),mA=Z,gA=u,pA=nn([]),hA=/* */Object.freeze({setup:mA,isDocked:gA,getBehaviours:pA}),vA=Dl({factory:function(t,o){var n={focus:lg.focusIn,setMenus:function(n,t){var e=S(t,function(t){var n={type:"menubutton",text:t.text,fetch:function(n){n(t.getItems())}},e=$O(n).mapError(function(n){return le(n)}).getOrDie();return PC(e,"tox-mbtn",o.backstage,on.some("menuitem"))});mg.set(n,e)}};return{uid:t.uid,dom:t.dom,components:[],behaviours:ba([mg.config({}),Kd("menubar-events",[Ii(function(n){t.onSetup(n)}),lo($r(),function(e,n){ku(e.element(),".tox-mbtn--active").each(function(t){Ou(n.event().target(),".tox-mbtn").each(function(n){jt(t,n)||e.getSystem().getByDom(t).each(function(t){e.getSystem().getByDom(n).each(function(n){Ow.expand(n),Ow.close(t),vg.focus(n)})})})})}),lo(Ti(),function(e,n){n.event().prevFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(t){n.event().newFocus().bind(function(n){return e.getSystem().getByDom(n).toOption()}).each(function(n){Ow.isOpen(t)&&(Ow.expand(n),Ow.close(t))})})})]),lg.config({mode:"flow",selector:".tox-mbtn",onEscape:function(n){return t.onEscape(n),on.some(!0)}}),Gy.config({})]),apis:n,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[ct("dom"),ct("uid"),ct("onEscape"),ct("backstage"),St("onSetup",Z)],apis:{focus:function(n,t){n.focus(t)},setMenus:function(n,t,e){n.setMenus(t,e)}}}),bA="container",yA=[Fs("slotBehaviours",[])],xA=function(r,n,t){function e(n){return Qs(r)}function o(e,o){return void 0===o&&(o=undefined),function(n,t){return qs(n,r,t).map(function(n){return e(n,t)}).getOr(o)}}function i(n,t){return"true"!==zo(n.element(),"aria-hidden")}var u,a=o(i,!1),c=o(function(n,t){if(i(n)){var e=n.element();yr(e,"display","none"),Po(e,"aria-hidden","true"),ro(n,Bi(),{name:t,visible:!1})}}),s=(u=c,function(t,n){bn(n,function(n){return u(t,n)})}),f=o(function(n,t){if(!i(n)){var e=n.element();kr(e,"display"),jo(e,"aria-hidden"),ro(n,Bi(),{name:t,visible:!0})}}),l={getSlotNames:e,getSlot:function(n,t){return qs(n,r,t)},isShowing:a,hideSlot:c,hideAllSlots:function(n){return s(n,e())},showSlot:f};return{uid:r.uid,dom:r.dom,components:n,behaviours:Is(r.slotBehaviours),apis:l}},wA=P({getSlotNames:function(n,t){return n.getSlotNames(t)},getSlot:function(n,t,e){return n.getSlot(t,e)},isShowing:function(n,t,e){return n.isShowing(t,e)},hideSlot:function(n,t,e){return n.hideSlot(t,e)},hideAllSlots:function(n,t){return n.hideAllSlots(t)},showSlot:function(n,t,e){return n.showSlot(t,e)}},$o),SA=N(N({},wA),{sketch:function(n){var e,t=(e=[],{slot:function(n,t){return e.push(n),Us(bA,PB(n),t)},record:function(){return e}}),o=n(t),r=t.record(),i=S(r,function(n){return xl({name:n,pname:PB(n)})});return of(bA,yA,i,xA,o)}}),CA=re([yt("icon"),yt("tooltip"),Bt("onShow",Z),Bt("onHide",Z),Bt("onSetup",function(){return Z})]),kA=Xo("FixSizeEvent"),OA=Xo("AutoSizeEvent"),EA=qD.optional({factory:vA,name:"menubar",schema:[ct("backstage")]}),TA=qD.optional({factory:{sketch:function(n){return QD.sketch({uid:n.uid,dom:n.dom,listBehaviours:ba([lg.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return KO({type:n.split,uid:Xo("multiple-toolbar-item"),backstage:n.backstage,cyclicKeying:!1,getSink:n.getSink,initGroups:[],onEscape:function(){return on.none()}})},setupItem:function(n,t,e,o){JE.setGroups(t,e)},shell:!0})}},name:"multiple-toolbar",schema:[ct("dom"),ct("onEscape")]}),BA=qD.optional({factory:{sketch:function(n){return function(n){return n.split===jb.sliding?qO:n.split===jb.floating?YO:KO}(n)({type:n.split,uid:n.uid,onEscape:function(){return n.onEscape(),on.some(!0)},cyclicKeying:!1,initGroups:[],getSink:n.getSink,backstage:n.backstage,moreDrawerData:{lazyToolbar:n.lazyToolbar,lazyMoreButton:n.lazyMoreButton,lazyHeader:n.lazyHeader}})}},name:"toolbar",schema:[ct("dom"),ct("onEscape"),ct("getSink")]}),DA=qD.optional({factory:{sketch:function(n){var t=n.editor,e=n.sticky?HB:pA;return{uid:n.uid,dom:n.dom,components:n.components,behaviours:ba(e(t,n.getSink))}}},name:"header",schema:[ct("dom")]}),AA=qD.optional({name:"socket",schema:[ct("dom")]}),_A=qD.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:ba([Gy.config({}),vg.config({}),cT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(n){Zl.getCurrent(n).each(SA.hideAllSlots),oo(n,OA)},onGrown:function(n){oo(n,OA)},onStartGrow:function(n){ro(n,kA,{width:Sr(n.element(),"width").getOr("")})},onStartShrink:function(n){ro(n,kA,{width:mu(n.element())+"px"})}}),mg.config({}),Zl.config({find:function(n){var t=mg.contents(n);return yn(t)}})])}],behaviours:ba([SS(0),Kd("sidebar-sliding-events",[lo(kA,function(n,t){yr(n.element(),"width",t.event().width())}),lo(OA,function(n,t){kr(n.element(),"width")})])])}}},name:"sidebar",schema:[ct("dom")]}),MA=qD.optional({factory:{sketch:function(n){return{uid:n.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:ba([mg.config({})]),components:[]}}},name:"throbber",schema:[ct("dom")]}),FA=Al({name:"OuterContainer",factory:function(e,n,t){var o={getSocket:function(n){return YD.getPart(n,e,"socket")},setSidebar:function(n,t){YD.getPart(n,e,"sidebar").each(function(n){return jB(n,t)})},toggleSidebar:function(n,t){YD.getPart(n,e,"sidebar").each(function(n){return function(n,e){Zl.getCurrent(n).each(function(t){Zl.getCurrent(t).each(function(n){cT.hasGrown(t)?SA.isShowing(n,e)?cT.shrink(t):(SA.hideAllSlots(n),SA.showSlot(n,e)):(SA.hideAllSlots(n),SA.showSlot(n,e),cT.grow(t))})})}(n,t)})},whichSidebar:function(n){return YD.getPart(n,e,"sidebar").bind(UB).getOrNull()},getHeader:function(n){return YD.getPart(n,e,"header")},getToolbar:function(n){return YD.getPart(n,e,"toolbar")},setToolbar:function(n,t){YD.getPart(n,e,"toolbar").each(function(n){n.getApis().setGroups(n,t)})},setToolbars:function(n,t){YD.getPart(n,e,"multiple-toolbar").each(function(n){QD.setItems(n,t)})},refreshToolbar:function(n){YD.getPart(n,e,"toolbar").each(function(n){return n.getApis().refresh(n)})},getMoreButton:function(n){return YD.getPart(n,e,"toolbar").bind(function(n){return n.getApis().getMoreButton(n)})},getThrobber:function(n){return YD.getPart(n,e,"throbber")},focusToolbar:function(n){YD.getPart(n,e,"toolbar").orThunk(function(){return YD.getPart(n,e,"multiple-toolbar")}).each(function(n){lg.focusIn(n)})},setMenubar:function(n,t){YD.getPart(n,e,"menubar").each(function(n){vA.setMenus(n,t)})},focusMenubar:function(n){YD.getPart(n,e,"menubar").each(function(n){vA.focus(n)})}};return{uid:e.uid,dom:e.dom,components:n,apis:o,behaviours:e.behaviours}},configFields:[ct("dom"),ct("behaviours")],partFields:[DA,EA,BA,TA,AA,_A,MA],apis:{getSocket:function(n,t){return n.getSocket(t)},setSidebar:function(n,t,e){n.setSidebar(t,e)},toggleSidebar:function(n,t,e){n.toggleSidebar(t,e)},whichSidebar:function(n,t){return n.whichSidebar(t)},getHeader:function(n,t){return n.getHeader(t)},getToolbar:function(n,t){return n.getToolbar(t)},setToolbar:function(n,t,e){var o=S(e,function(n){return WO(n)});n.setToolbar(t,o)},setToolbars:function(n,t,e){var o=S(e,function(n){return S(n,WO)});n.setToolbars(t,o)},getMoreButton:function(n,t){return n.getMoreButton(t)},refreshToolbar:function(n,t){return n.refreshToolbar(t)},getThrobber:function(n,t){return n.getThrobber(t)},setMenubar:function(n,t,e){n.setMenubar(t,e)},focusMenubar:function(n,t){n.focusMenubar(t)},focusToolbar:function(n,t){n.focusToolbar(t)}}}),IA={file:{title:"File",items:"newdocument restoredraft | preview | print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align | forecolor backcolor | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},RA=function(n){function t(){n._skinLoaded=!0,Nv(n)}return function(){n.initialized?t():n.on("init",t)}},VA=d(YB,!1),NA=d(YB,!0),HA=Rh.DOM,PA=Ht(),zA=PA.os.isiOS()&&PA.os.version.major<=12,LA={render:function(e,o,n,t,r){var i=ye(0);VA(e),function(n,t){If(n,t,_o)}(we.fromDom(r.targetNode),o.mothership),xs(zr(),o.uiMothership),e.on("PostRender",function(){qB(e,o,n,t),i.set(e.getWin().innerWidth),FA.setMenubar(o.outerContainer,XB(e,n)),FA.setSidebar(o.outerContainer,n.sidebar),function(r){function n(n){var t=r.getDoc().documentElement,e=u.get(),o=a.get();e.left()!==i.innerWidth||e.top()!==i.innerHeight?(u.set(Iu(i.innerWidth,i.innerHeight)),zv(r,n)):o.left()===t.offsetWidth&&o.top()===t.offsetHeight||(a.set(Iu(t.offsetWidth,t.offsetHeight)),zv(r,n))}function t(n){return Pv(r,n)}var i=r.getWin(),e=r.getDoc().documentElement,u=ye(Iu(i.innerWidth,i.innerHeight)),a=ye(Iu(e.offsetWidth,e.offsetHeight));HA.bind(i,"resize",n),HA.bind(i,"scroll",t);var o=fb(we.fromDom(r.getBody()),"load",n);r.on("remove",function(){o.unbind(),HA.unbind(i,"resize",n),HA.unbind(i,"scroll",t)})}(e)});var u=FA.getSocket(o.outerContainer).getOrDie("Could not find expected socket element");if(!0===zA){xr(u.element(),{overflow:"scroll","-webkit-overflow-scrolling":"touch"});var a=function(e,o){var r=null;return{cancel:function(){null!==r&&(v.clearTimeout(r),r=null)},throttle:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];null===r&&(r=v.setTimeout(function(){e.apply(null,n),r=null},o))}}}(function(){e.fire("ScrollContent")},20);sb(u.element(),"scroll",a.throttle)}LO(e,o),e.addCommand("ToggleSidebar",function(n,t){FA.toggleSidebar(o.outerContainer,t),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return FA.whichSidebar(o.outerContainer)});var c=Yb(e);return c!==jb.sliding&&c!==jb.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var n=e.getWin().innerWidth;n!==i.get()&&(FA.refreshToolbar(o.outerContainer),i.set(n))}),{iframeContainer:u.element().dom(),editorContainer:o.outerContainer.element().dom()}}},jA=function(t,n,e){var o=n.filter(function(n){return t<n}),r=e.filter(function(n){return n<t});return o.or(r).getOr(t)},UA=function(n){return/^[0-9\.]+(|px)$/i.test(""+n)?on.some(parseInt(""+n,10)):on.none()},WA=function(n){return mn(n)?n+"px":n},GA={render:function(t,i,e,o,n){var u,r=Rh.DOM,a=Kb(t),c=$b(t),s=we.fromDom(n.targetNode),f=Nb(t).or(JB(t)),l=Yb(t),d=l===jb.sliding||l===jb.floating;NA(t);function m(n){void 0===n&&(n=!1),d&&FA.refreshToolbar(i.outerContainer),a||function(n){var t=d?n.fold(function(){return 0},function(n){return 1<n.components().length?su(n.components()[1].element()):0}):0,e=du(s),o=e.top()-su(u.element())+t;xr(i.outerContainer.element(),{position:"absolute",top:Math.round(o)+"px",left:Math.round(e.left())+"px"});var r=f.getOrThunk(function(){var n=UA(wr(zr(),"margin-left")).getOr(0);return mu(zr())-e.left()+n});yr(u.element(),"max-width",r+"px")}(FA.getToolbar(i.outerContainer)),c&&(n?cA.reset(u):cA.refresh(u))}function g(){yr(i.outerContainer.element(),"display","flex"),r.addClass(t.getBody(),"mce-edit-focus"),kr(i.uiMothership.element(),"display"),m()}function p(){i.outerContainer&&(yr(i.outerContainer.element(),"display","none"),r.removeClass(t.getBody(),"mce-edit-focus")),yr(i.uiMothership.element(),"display","none")}function h(){if(u)g();else{u=FA.getHeader(i.outerContainer).getOrDie();var n=function(n){return qb(n).getOr(zr())}(t);xs(n,i.mothership),xs(n,i.uiMothership),qB(t,i,e,o),FA.setMenubar(i.outerContainer,XB(t,e)),g(),t.on("activate",g),t.on("deactivate",p),t.on("NodeChange SkinLoaded ResizeWindow",function(){t.hidden||m(!0)}),t.nodeChanged()}}return t.on("focus",h),t.on("blur hide",p),t.on("init",function(){t.hasFocus()&&h()}),LO(t,i),{editorContainer:i.outerContainer.element().dom()}}},XA=function(t){yk.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(n){t.ui.registry.addToggleButton(n.name,{tooltip:n.text,onAction:function(){return t.execCommand(n.cmd)},icon:n.icon,onSetup:fE(t,n.name)})});var n="alignnone",e="No alignment",o="JustifyNone",r="align-none";t.ui.registry.addButton(n,{tooltip:e,onAction:function(){return t.execCommand(o)},icon:r})},YA=function(n){QB(n),function(t){yk.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(n){t.ui.registry.addMenuItem(n.name,{text:n.text,icon:n.icon,shortcut:n.shortcut,onAction:function(){return t.execCommand(n.action)}})}),t.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:$B(t,"code")})}(n)},qA=function(n){!function(t){t.ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(n){return ZB(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(n){return ZB(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n),function(t){t.ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(n){return ZB(n,t,"hasUndo")},onAction:function(){return t.execCommand("undo")}}),t.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(n){return ZB(n,t,"hasRedo")},onAction:function(){return t.execCommand("redo")}})}(n)},KA=function(n){!function(n){n.ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return n.execCommand("mceToggleVisualAid")}})}(n),function(t){t.ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(n){return function(t,n){t.setActive(n.hasVisual);function e(n){t.setActive(n.hasVisual)}return n.on("VisualAid",e),function(){return n.off("VisualAid",e)}}(n,t)},onAction:function(){t.execCommand("mceToggleVisualAid")}})}(n)},JA=function(n){!function(t){t.ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(n){return function(n,t){n.setDisabled(!t.queryCommandState("outdent"));function e(){n.setDisabled(!t.queryCommandState("outdent"))}return t.on("NodeChange",e),function(){return t.off("NodeChange",e)}}(n,t)},onAction:function(){return t.execCommand("outdent")}}),t.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return t.execCommand("indent")}})}(n)},$A=function(n,t){!function(n,t){var e=dE(0,t,nB(n));n.ui.registry.addNestedMenuItem("align",{text:t.shared.providers.translate("Align"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=dE(0,t,eB(n));n.ui.registry.addNestedMenuItem("fontformats",{text:t.shared.providers.translate("Fonts"),getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=N({type:"advanced"},t.styleselect),o=dE(0,t,cB(n,e));n.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return o.items.validateItems(o.getStyleItems())}})}(n,t),function(n,t){var e=dE(0,t,aB(n));n.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t),function(n,t){var e=dE(0,t,rB(n));n.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return e.items.validateItems(e.getStyleItems())}})}(n,t)},QA=function(n,t){XA(n),YA(n),$A(n,t),qA(n),Uv.register(n),KA(n),JA(n)},ZA=function(n){return{anchor:"selection",root:we.fromDom(n.selection.getNode())}},n_={onLtr:function(){return[rc,ua,aa,ca,sa,oc,Ug,Wg,pm,mm,hm,gm]},onRtl:function(){return[rc,aa,ua,sa,ca,oc,Ug,Wg,hm,gm,pm,mm]}},t_={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},e_=function(n){return n.settings.contextmenu_never_use_native||!1},o_=function(n){return function(n,t,e){var o=n.ui.registry.getAll().contextMenus;return V(n.settings,t).map(cD).getOrThunk(function(){return C(cD(e),function(n){return En(o,n)})})}(n,"contextmenu","link linkchecker image imagetools table spellchecker configurepermanentpen")},r_=function(n){return!1===n.getParam("contextmenu")},i_={type:"separator"},u_=function(t){if(cn(t))return t;switch(t.type){case"separator":return i_;case"submenu":return{type:"nestedmenuitem",text:t.text,icon:t.icon,getSubmenuItems:function(){var n=t.getSubmenuItems();return cn(n)?n:S(n,u_)}};default:return{type:"menuitem",text:t.text,icon:t.icon,onAction:function(n){return function(){return n()}}(t.onAction)}}},a_="data-initial-z-index",c_=wt("snaps",[ct("getSnapPoints"),qu("onSensor"),ct("leftAttr"),ct("topAttr"),St("lazyViewport",Su),St("mustSnap",!1)]),s_=[St("useFixed",u),ct("blockerClass"),St("getTarget",l),St("onDrag",Z),St("repositionTarget",!0),St("onDrop",Z),Bt("getBounds",Su),c_],f_=function(n,t,r,i,u){var e=t.getSnapPoints(n);return bD(e,r,i,u).orThunk(function(){return O(e,function(t,e){var n=e.sensor(),o=function(n,t,e,o,r,i){var u=bB(n,r,i),a=bB(t,r,i),c=Math.abs(u.left()-a.left()),s=Math.abs(u.top()-a.top());return Iu(c,s)}(r,n,e.range().left(),e.range().top(),i,u);return t.deltas.fold(function(){return{deltas:on.some(o),snap:on.some(e)}},function(n){return(o.left()+o.top())/2<=(n.left()+n.top())/2?{deltas:on.some(o),snap:on.some(e)}:t})},{deltas:on.none(),snap:on.none()}).snap.map(function(n){return{output:nn(wB(n.output(),r,i,u)),extra:n.extra}})})},l_=function(n,t,e,o,r){var i=t.getSnapPoints(n);return bD(i,e,o,r)},d_=/* */Object.freeze({getData:function(n){return on.from(Iu(n.x(),n.y()))},getDelta:function(n,t){return Iu(t.left()-n.left(),t.top()-n.top())}}),m_=g(s_,[Qu("dragger",{handlers:kD(OD)})]),g_=/* */Object.freeze({getData:function(n){var t=n.raw().touches;return 1===t.length?function(n){var t=n[0];return on.some(Iu(t.clientX,t.clientY))}(t):on.none()},getDelta:function(n,t){return Iu(t.left()-n.left(),t.top()-n.top())}}),p_=m_,h_=g(s_,[Qu("dragger",{handlers:kD(ED)})]),v_=g(s_,[Qu("dragger",{handlers:kD(function(n,t,e){return g(OD(n,t,e),ED(n,t,e))})})]),b_=/* */Object.freeze({mouse:p_,touch:h_,mouseOrTouch:v_}),y_=/* */Object.freeze({init:function(){var o=on.none(),t=on.none(),n=nn({});return nu({readState:n,reset:function(){o=on.none(),t=on.none()},update:function(t,n){return t.getData(n).bind(function(n){return function(t,e){var n=o.map(function(n){return t.getDelta(n,e)});return o=on.some(e),n}(t,n)})},getStartData:function(){return t},setStartData:function(n){t=on.some(n)}})}}),x_=/* */Object.freeze({snapTo:function(n,t,e,o){var r=t.getTarget(n.element());if(t.repositionTarget){var i=Co(n.element()),u=pu(i),a=mB(r),c=function(n,t,e){return{coord:wB(n.output(),n.output(),t,e),extra:n.extra()}}(o,u,a),s=xB(c.coord,0,a);xr(r,s)}}}),w_=xa({branchKey:"mode",branches:b_,name:"dragging",active:{events:function(n,t){return n.dragger.handlers(n,t)}},extra:{snap:So(["sensor","range","output"],["extra"])},state:y_,apis:x_}),S_=Ht(),C_=function(c,e){function t(n){var t=wu(n);return TD(g.getOpt(e),n,t.x(),t.y(),t.width(),t.height())}function o(n){var t=wu(n);return TD(p.getOpt(e),n,t.right(),t.bottom(),t.width(),t.height())}function r(n,t,e,o){var r=e(t);w_.snapTo(n,r),function(n,t,e,o){var r=t.dom().getBoundingClientRect();kr(n.element(),"display");var i=Oo(we.fromDom(c.getBody())).dom().innerHeight,u=e(r),a=o(r,i);(u||a)&&yr(n.element(),"display","none")}(n,t,function(n){return n[o]<0},function(n,t){return n[o]>t})}function i(n){return r(h,n,t,"top")}function u(n){return r(v,n,o,"bottom")}var a=ye([]),s=ye([]),n=ye(!1),f=ye(on.none()),l=ye(on.none()),d=BD(function(){return S(a.get(),function(n){return t(n)})},f,function(t){l.get().each(function(n){c.fire("TableSelectorChange",{start:t,finish:n})})}),m=BD(function(){return S(s.get(),function(n){return o(n)})},l,function(t){f.get().each(function(n){c.fire("TableSelectorChange",{start:n,finish:t})})}),g=DD(d),p=DD(m),h=uu(g.asSpec()),v=uu(p.asSpec());S_.deviceType.isTouch()&&(c.on("TableSelectionChange",function(t){n.get()||(hs(e,h),hs(e,v),n.set(!0)),f.set(on.some(t.start)),l.set(on.some(t.finish)),t.otherCells.each(function(n){a.set(n.upOrLeftCells),s.set(n.downOrRightCells),i(t.start),u(t.finish)})}),c.on("ResizeEditor ResizeWindow ScrollContent",function(){f.get().each(i),l.get().each(u)}),c.on("TableSelectionClear",function(){n.get()&&(bs(h),bs(v),n.set(!1)),f.set(on.none()),l.set(on.none())}))};(ID=FD=FD||{})[ID.None=0]="None",ID[ID.Both=1]="Both",ID[ID.Vertical=2]="Vertical";function k_(n,t,e){var o=we.fromDom(n.getContainer()),r=function(n,t,e,o,r){var i={};return i.height=jA(o+t.top(),Vb(n),Hb(n)),e===FD.Both&&(i.width=jA(r+t.left(),Rb(n),Nb(n))),i}(n,t,e,su(o),mu(o));Cn(r,function(n,t){return yr(o,t,WA(n))}),Hv(n)}function O_(n){if(1===n.nodeType){if("BR"===n.nodeName||n.getAttribute("data-mce-bogus"))return!0;if("bookmark"===n.getAttribute("data-mce-type"))return!0}return!1}function E_(o,t){var r,n,e;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(n=function(){var n=[];return o.getParam("elementpath",!0,"boolean")&&n.push(pM(o,{})),Vt(o.settings.plugins,"wordcount")&&n.push(function(n,o){function r(n,t,e){return mg.set(n,[Ir(o.translate(["{0} "+e,t[e]]))])}return Gg.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:ba([Gy.config({}),mg.config({}),Zf.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),Kd("wordcount-events",[Ni(function(n){var t=Zf.getValue(n),e="words"===t.mode?"characters":"words";Zf.setValue(n,{mode:e,count:t.count}),r(n,t.count,e)}),Ii(function(e){n.on("wordCountUpdate",function(n){var t=Zf.getValue(e).mode;Zf.setValue(e,{mode:t,count:n.wordCount}),r(e,n.wordCount,t)})})])])})}(o,t)),o.getParam("branding",!0,"boolean")&&n.push(function(){var n=rh.translate(["Powered by {0}","Tiny"]);return{dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+n+'">'+n+"</a>"}}}()),0<n.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:n}]:[]}(),e=function(n){var t=!Vt(n.settings.plugins,"autoresize"),e=n.getParam("resize",t);return!1===e?FD.None:"both"===e?FD.Both:FD.Vertical}(o),e!==FD.None&&n.push((r=e,{dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},innerHtml:ym("resize-handle",t.icons)},behaviours:ba([w_.config({mode:"mouse",repositionTarget:!1,onDrag:function(n,t,e){k_(o,e,r)},blockerClass:"tox-blocker"})])})),n)}}function T_(n){return[ft("type"),function(n){return st(n,me)}("columns"),n]}function B_(t){return ce("items","items",Mn(),Kn(Zn(function(n){return tt("Checking item of "+t,gF,n).fold(function(n){return an.error(le(n))},function(n){return an.value(n)})})))}function D_(n){return cn(n.type)&&cn(n.name)}function A_(n){var t=function(n){return C(DF(n),D_)}(n),e=B(t,function(t){return function(n){return on.from(AF[n.type])}(t).fold(function(){return[]},function(n){return[st(t.name,n)]})});return re(e)}function __(n){return{internalDialog:et(function(n){return tt("dialog",BF,n)}(n)),dataValidator:A_(n),initialData:n.initialData}}function M_(n){var e=[],o={};return Cn(n,function(n,t){n.fold(function(){e.push(t)},function(n){o[t]=n})}),0<e.length?an.error(e):an.value(o)}function F_(n){return yn(function(n,t){var e=gn.call(n,0);return e.sort(t),e}(n,function(n,t){return t<n?-1:n<t?1:0}))}function I_(n,t){yr(n,"height",t+"px"),Ht().browser.isIE()?kr(n,"flex-basis"):yr(n,"flex-basis",t+"px")}function R_(n,o,r){Cu(n,'[role="dialog"]').each(function(e){ku(e,'[role="tablist"]').each(function(t){r.get().map(function(n){return yr(o,"height","0"),yr(o,"flex-basis","0"),Math.min(n,function(n,t,e){var o,r=ko(n).dom(),i=Cu(n,".tox-dialog-wrap").getOr(n);o="fixed"===wr(i,"position")?Math.max(r.clientHeight,v.window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight);var u=su(t),a=t.dom().offsetLeft>=e.dom().offsetLeft+mu(e)?Math.max(su(e),u):u,c=parseInt(wr(n,"margin-top"),10)||0,s=parseInt(wr(n,"margin-bottom"),10)||0;return o-(su(n)+c+s-a)}(e,o,t))}).each(function(n){I_(o,n)})})})}function V_(n){return ku(n,'[role="tabpanel"]')}function N_(r){var i;return{smartTabHeight:(i=ye(on.none()),{extraEvents:[Ii(function(n){var t=n.element();V_(t).each(function(o){yr(o,"visibility","hidden"),n.getSystem().getByDom(o).toOption().each(function(n){var t=function(o,r,i){return S(o,function(n,t){mg.set(i,o[t].view());var e=r.dom().getBoundingClientRect();return mg.set(i,[]),e.height})}(r,o,n),e=F_(t);i.set(e)}),R_(t,o,i),kr(o,"visibility"),function(n,t){yn(n).each(function(n){return WF.showTab(t,n.value)})}(r,n),qg.requestAnimationFrame(function(){R_(t,o,i)})})}),lo(Si(),function(n){var t=n.element();V_(t).each(function(n){R_(t,n,i)})}),lo(gy,function(n,t){var r=n.element();V_(r).each(function(t){var n=Sa();yr(t,"visibility","hidden");var e=Sr(t,"height").map(function(n){return parseInt(n,10)});kr(t,"height"),kr(t,"flex-basis");var o=t.dom().getBoundingClientRect().height;e.forall(function(n){return n<o})?(i.set(on.from(o)),R_(r,t,i)):e.each(function(n){I_(t,n)}),kr(t,"visibility"),n.each(wa)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}}function H_(n,t,e,o){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:N(N({},t.map(function(n){return{id:n}}).getOr({})),o?{"aria-live":"polite"}:{})},components:[],behaviours:ba([SS(0),RT.config({channel:KF,updateState:function(n,t){return on.some({isTabPanel:function(){return"tabpanel"===t.body.type}})},renderComponents:function(n){switch(n.body.type){case"tabpanel":return[function(n,e){function o(n){var t=Zf.getValue(n),e=M_(t).getOr({}),o=i.get(),r=Dn(o,e);i.set(r)}function r(n){var t=i.get();Zf.setValue(n,t)}var i=ye({}),u=ye(null),t=S(n.tabs,function(n){return{value:n.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:e.shared.providers.translate(n.title)},view:function(){return[dS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"]},components:S(n.items,function(n){return Wk(t,n,e)}),formBehaviours:ba([lg.config({mode:"acyclic",useTabstopAt:b(MS)}),Kd("TabView.form.events",[Ii(r),Ri(o)]),lc.config({channels:K([{key:GF,value:{onReceive:o}},{key:XF,value:{onReceive:r}}])})])}})]}}}),a=N_(t).smartTabHeight;return WF.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(n,t,e){var o=Zf.getValue(t);ro(n,my,{name:o,oldName:u.get()}),u.set(o)},tabs:t,components:[WF.parts().tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[HF.parts().tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:ba([Gy.config({})])}),WF.parts().tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:a.selectFirst,tabSectionBehaviours:ba([Kd("tabpanel",a.extraEvents),lg.config({mode:"acyclic"}),Zl.config({find:function(n){return yn(WF.getViewItems(n))}}),Zf.config({store:{mode:"manual",getValue:function(n){return n.getSystem().broadcastOn([GF],{}),i.get()},setValue:function(n,t){i.set(t),n.getSystem().broadcastOn([XF],{})}}})])})}(n.body,e)];default:return[function(n,e){var t=vm(dS.sketch(function(t){return{dom:{tag:"div",classes:["tox-form"].concat(n.classes)},components:S(n.items,function(n){return Wk(t,n,e)})}}));return{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[t.asSpec()]}],behaviours:ba([lg.config({mode:"acyclic",useTabstopAt:b(MS)}),wS(t),ES(t,{postprocess:function(n){return M_(n).fold(function(n){return v.console.error(n),{}},function(n){return n})}})])}}(n.body,e)]}},initialData:n})])}}function P_(n,e){return[ho(Qr(),FS),n(ay,function(n,t){e.onClose(),t.onClose()}),n(cy,function(n,t,e,o){t.onCancel(n),oo(o,ay)}),lo(dy,function(n,t){return e.onUnblock()}),lo(ly,function(n,t){return e.onBlock(t.event())})]}function z_(n,t){function e(n,t){return Wb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+n]},components:S(t,function(n){return n.memento.asSpec()})})}var o=function(n,t){for(var e=[],o=[],r=0,i=n.length;r<i;r++){var u=n[r];(t(u,r)?e:o).push(u)}return{pass:e,fail:o}}(t.map(function(n){return n.footerButtons}).getOr([]),function(n){return"start"===n.align});return[e("start",o.pass),e("end",o.fail)]}function L_(n,o){return{dom:cp('<div class="tox-dialog__footer"></div>'),components:[],behaviours:ba([RT.config({channel:JF,initialData:n,updateState:function(n,t){var e=S(t.buttons,function(n){var t=vm(function(n,t){return YC(n,n.type,t)}(n,o));return{name:n.name,align:n.align,memento:t}});return on.some({lookupByName:function(n,t){return function(t,n,e){return E(n,function(n){return n.name===e}).bind(function(n){return n.memento.getOpt(t)})}(n,e,t)},footerButtons:e})},renderComponents:z_})])}}function j_(n,t){return wM.parts().footer(L_(n,t))}function U_(t,e){if(t.getRoot().getSystem().isConnected()){var o=Zl.getCurrent(t.getFormWrapper()).getOr(t.getFormWrapper());return dS.getField(o,e).fold(function(){var n=t.getFooter();return RT.getState(n).get().bind(function(n){return n.lookupByName(o,e)})},function(n){return on.some(n)})}return on.none()}function W_(u,o,a){function n(n){var t=u.getRoot();t.getSystem().isConnected()&&n(t)}var c={getData:function(){var n=u.getRoot(),t=n.getSystem().isConnected()?u.getFormWrapper():n,e=Zf.getValue(t),o=P(a,function(n){return n.get()});return N(N({},e),o)},setData:function(i){n(function(n){var t=c.getData(),e=An(t,i),o=function(n,t){var e=n.getRoot();return RT.getState(e).get().map(function(n){return et(tt("data",n.dataValidator,t))}).getOr(t)}(u,e),r=u.getFormWrapper();Zf.setValue(r,o),Cn(a,function(n,t){En(e,t)&&n.set(e[t])})})},disable:function(n){U_(u,n).each(Ch.disable)},enable:function(n){U_(u,n).each(Ch.enable)},focus:function(n){U_(u,n).each(vg.focus)},block:function(t){if(!cn(t))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){ro(n,ly,{message:t})})},unblock:function(){n(function(n){oo(n,dy)})},showTab:function(e){n(function(n){var t=u.getBody();RT.getState(t).get().exists(function(n){return n.isTabPanel()})&&Zl.getCurrent(t).each(function(n){WF.showTab(n,e)})})},redial:function(e){n(function(n){var t=o(e);n.getSystem().broadcastOn([YF],t),n.getSystem().broadcastOn([qF],t.internalDialog),n.getSystem().broadcastOn([KF],t.internalDialog),n.getSystem().broadcastOn([JF],t.internalDialog),c.setData(t.initialData)})},close:function(){n(function(n){oo(n,ay)})}};return c}function G_(n,t){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[n,t]}}function X_(n,t){return wM.parts().close(Gg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close")}},action:n,buttonBehaviours:ba([Gy.config({})])}))}function Y_(){return wM.parts().title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})}function q_(n,t){return wM.parts().body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:cp("<p>"+t.translate(n)+"</p>")}]}]})}function K_(n){return wM.parts().footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:n})}function J_(n,t){return[Wb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:n}),Wb.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:t})]}function $_(t){var n,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return wM.sketch({lazySink:t.lazySink,onEscape:function(n){return t.onEscape(n),on.some(!0)},useTabstopAt:function(n){return!MS(n)},dom:{tag:"div",classes:[e].concat(t.extraClasses),styles:N({position:"relative"},t.extraStyles)},components:g([t.header,t.body],t.footer.toArray()),parts:{blocker:{dom:cp('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:nI?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:ba(g([vg.config({}),Kd("dialog-events",t.dialogEvents.concat([bo(Qr(),function(n,t){lg.focusIn(n)})])),Kd("scroll-lock",[Ii(function(){fr(zr(),i)}),Ri(function(){dr(zr(),i)})])],t.extraBehaviours)),eventOrder:N((n={},n[di()]=["dialog-events"],n[Ci()]=["scroll-lock","dialog-events","alloy.base.behaviour"],n[ki()]=["alloy.base.behaviour","dialog-events","scroll-lock"],n),t.eventOrder)})}function Q_(n){return Gg.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close"),title:n.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:'<svg width="24" height="24" xmlns="http://www.w3.org/2000/svg"><path d="M17.953 7.453L13.422 12l4.531 4.547-1.406 1.406L12 13.422l-4.547 4.531-1.406-1.406L10.578 12 6.047 7.453l1.406-1.406L12 10.578l4.547-4.531z" fill-rule="evenodd"></path></svg>'}}],action:function(n){oo(n,cy)}})}function Z_(n,t,e){function o(n){return[Ir(e.translate(n.title))]}return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:N({},t.map(function(n){return{id:n}}).getOr({}))},components:o(n),behaviours:ba([RT.config({channel:qF,renderComponents:o})])}}function nM(){return{dom:cp('<div class="tox-dialog__draghandle"></div>')}}function tM(n,t){return function(n,t){var e=wM.parts().title(Z_(n,on.none(),t)),o=wM.parts().draghandle(nM()),r=wM.parts().close(Q_(t)),i=[e].concat(n.draggable?[o]:[]).concat([r]);return Wb.sketch({dom:cp('<div class="tox-dialog__header"></div>'),components:i})}({title:t.shared.providers.translate(n),draggable:t.dialog.isDraggableModal()},t.shared.providers)}function eM(n,t){return{onClose:function(){return t.closeWindow()},onBlock:function(e){wM.setBusy(n(),function(n,t){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.message()},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:t,components:[{dom:cp('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}})},onUnblock:function(){wM.setIdle(n())}}}function oM(n,t,e,o){var r;return uu($_(N(N({},n),{lazySink:o.shared.getSink,extraBehaviours:g([RT.config({channel:YF,updateState:function(n,t){return on.some(t)},initialData:t}),DS({})],n.extraBehaviours),onEscape:function(n){oo(n,cy)},dialogEvents:e,eventOrder:(r={},r[li()]=["reflecting","receiving"],r[Ci()]=["scroll-lock","reflecting","messages","dialog-events","alloy.base.behaviour"],r[ki()]=["alloy.base.behaviour","dialog-events","messages","reflecting","scroll-lock"],r)})))}function rM(n){return S(n,function(n){return"menu"===n.type?function(n){var t=S(n.items,function(n){var t=ye(!1);return N(N({},n),{storage:t})});return N(N({},n),{items:t})}(n):n})}function iM(n){return O(n,function(n,t){return"menu"!==t.type?n:O(t.items,function(n,t){return n[t.name]=t.storage,n},n)},{})}function uM(n,t,e){var o=tM(n.internalDialog.title,e),r=function(n,t){var e=H_(n,on.none(),t,!1);return wM.parts().body(e)}({body:n.internalDialog.body},e),i=rM(n.internalDialog.buttons),u=iM(i),a=j_({buttons:i},e),c=ZF(function(){return d},eM(function(){return l},t)),s="normal"!==n.internalDialog.size?"large"===n.internalDialog.size?["tox-dialog--width-lg"]:["tox-dialog--width-md"]:[],f={header:o,body:r,footer:on.some(a),extraClasses:s,extraBehaviours:[],extraStyles:{}},l=oM(f,n,c,e),d=W_({getRoot:function(){return l},getBody:function(){return wM.getBody(l)},getFooter:function(){return wM.getFooter(l)},getFormWrapper:function(){var n=wM.getBody(l);return Zl.getCurrent(n).getOr(n)}},t.redial,u);return{dialog:l,instanceApi:d}}function aM(n,t,e,o){var r,i,u=Xo("dialog-label"),a=Xo("dialog-content"),c=vm(function(n,t,e){return Wb.sketch({dom:cp('<div class="tox-dialog__header"></div>'),components:[Z_(n,on.some(t),e),nM(),Q_(e)],containerBehaviours:ba([w_.config({mode:"mouse",blockerClass:"blocker",getTarget:function(n){return Ou(n,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])})}({title:n.internalDialog.title,draggable:!0},u,e.shared.providers)),s=vm(function(n,t,e,o){return H_(n,on.some(t),e,o)}({body:n.internalDialog.body},a,e,o)),f=rM(n.internalDialog.buttons),l=iM(f),d=vm(function(n,t){return L_(n,t)}({buttons:f},e)),m=ZF(function(){return p},{onBlock:function(){},onUnblock:function(){},onClose:function(){return t.closeWindow()}}),g=uu({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:(r={role:"dialog"},r["aria-labelledby"]=u,r["aria-describedby"]=""+a,r)},eventOrder:(i={},i[li()]=[RT.name(),lc.name()],i[di()]=["execute-on-form"],i[Ci()]=["reflecting","execute-on-form"],i),behaviours:ba([lg.config({mode:"cyclic",onEscape:function(n){return oo(n,ay),on.some(!0)},useTabstopAt:function(n){return!MS(n)&&("button"!==Ke(n)||"disabled"!==zo(n,"disabled"))}}),RT.config({channel:YF,updateState:function(n,t){return on.some(t)},initialData:n}),vg.config({}),Kd("execute-on-form",m.concat([bo(Qr(),function(n,t){lg.focusIn(n)})])),DS({})]),components:[c.asSpec(),s.asSpec(),d.asSpec()]}),p=W_({getRoot:function(){return g},getFooter:function(){return d.get(g)},getBody:function(){return s.get(g)},getFormWrapper:function(){var n=s.get(g);return Zl.getCurrent(n).getOr(n)}},t.redial,l);return{dialog:g,instanceApi:p}}function cM(n){return sn(n)&&-1!==eI.indexOf(n.mceAction)}function sM(e,n,o,t){var r,i=tM(e.title,t),u=function(n){var t={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[IS({dom:{tag:"iframe",attributes:{src:n.url}},behaviours:ba([Gy.config({}),vg.config({})])})]}],behaviours:ba([lg.config({mode:"acyclic",useTabstopAt:b(MS)})])};return wM.parts().body(t)}(e),a=e.buttons.bind(function(n){return 0===n.length?on.none():on.some(j_({buttons:n},t))}),c=QF(function(){return h},eM(function(){return p},n)),s=N(N({},e.height.fold(function(){return{}},function(n){return{height:n+"px","max-height":n+"px"}})),e.width.fold(function(){return{}},function(n){return{width:n+"px","max-width":n+"px"}})),f=e.width.isNone()&&e.height.isNone()?["tox-dialog--width-lg"]:[],l=new tI(e.url,{base_uri:new tI(v.window.location.href)}),d=l.protocol+"://"+l.host+(l.port?":"+l.port:""),m=ye(on.none()),g=[Kd("messages",[Ii(function(){var n=sb(we.fromDom(v.window),"message",function(n){if(l.isSameOrigin(new tI(n.raw().origin))){var t=n.raw().data;cM(t)?function(n,t,e){switch(e.mceAction){case"insertContent":n.insertContent(e.content);break;case"setContent":n.setContent(e.content);break;case"execCommand":var o=!!ln(e.ui)&&e.ui;n.execCommand(e.cmd,o,e.value);break;case"close":t.close();break;case"block":t.block(e.message);break;case"unblock":t.unblock()}}(o,h,t):function(n){return!cM(n)&&sn(n)&&En(n,"mceAction")}(t)&&e.onMessage(h,t)}});m.set(on.some(n))}),Ri(function(){m.get().each(function(n){return n.unbind()})})]),lc.config({channels:(r={},r[$F]={onReceive:function(n,t){ku(n.element(),"iframe").each(function(n){n.dom().contentWindow.postMessage(t,d)})}},r)})],p=oM({header:i,body:u,footer:a,extraClasses:f,extraBehaviours:g,extraStyles:s},e,c,t),h=function(t){function n(n){t.getSystem().isConnected()&&n(t)}return{block:function(t){if(!cn(t))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");n(function(n){ro(n,ly,{message:t})})},unblock:function(){n(function(n){oo(n,dy)})},close:function(){n(function(n){oo(n,ay)})},sendMessage:function(t){n(function(n){n.getSystem().broadcastOn([$F],t)})}}}(p);return{dialog:p,instanceApi:h}}var fM,lM,dM,mM,gM,pM=function(i,r){r.delimiter||(r.delimiter="\xbb");return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:ba([lg.config({mode:"flow",selector:"div[role=button]"}),Gy.config({}),mg.config({}),Kd("elementPathEvents",[Ii(function(e,n){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return lg.focusIn(e)}),i.on("NodeChange",function(n){var t=function(n){for(var t=[],e=n.length;0<e--;){var o=n[e];if(1===o.nodeType&&!O_(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||t.push({name:r.name,element:o}),r.isPropagationStopped())break}}return t}(n.parents);0<t.length&&mg.set(e,function(n){var t=S(n||[],function(t,n){return Gg.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":n,"tab-index":-1,"aria-level":n+1},innerHtml:t.name},action:function(n){i.focus(),i.selection.select(t.element),i.nodeChanged()}})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+r.delimiter+" "}};return O(t.slice(1),function(n,t){var e=n;return e.push(o),e.push(t),e},[t[0]])}(t))})})])]),components:[]}},hM=function(l){function d(){return e.bind(FA.getHeader)}function m(){return an.value(v)}function n(){return e.bind(function(n){return FA.getMoreButton(n)}).getOrDie("Could not find more button element")}function g(){return e.bind(function(n){return FA.getThrobber(n)}).getOrDie("Could not find throbber element")}var t=l.inline,p=t?GA:LA,h=$b(l)?dA:hA,e=on.none(),o=Ht(),r=o.browser.isIE()?["tox-platform-ie"]:[],i=o.deviceType.isTouch()?["tox-platform-touch"]:[],u=rh.isRtl()?{attributes:{dir:"rtl"}}:{},v=uu({dom:N({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(r).concat(i)},u),behaviours:ba([Af.config({useFixed:function(){return h.isDocked(d)}})])}),a=vm({dom:{tag:"div",classes:["tox-anchorbar"]}}),b=hO(v,l,function(){return e.bind(function(n){return a.getOpt(n)}).getOrDie("Could not find a anchor bar element")},n),c=FA.parts().menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:b,onEscape:function(){l.focus()}}),s=Yb(l),f=FA.parts().toolbar({dom:{tag:"div",classes:["tox-toolbar"]},getSink:m,backstage:b,onEscape:function(){l.focus()},split:s,lazyToolbar:function(){return e.bind(function(n){return FA.getToolbar(n)}).getOrDie("Could not find more toolbar element")},lazyMoreButton:n,lazyHeader:function(){return d().getOrDie("Could not find header element")}}),y=FA.parts()["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},onEscape:function(){},split:s}),x=FA.parts().socket({dom:{tag:"div",classes:["tox-edit-area"]}}),w=FA.parts().sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),S=FA.parts().throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:b}),C=l.getParam("statusbar",!0,"boolean")&&!t?on.some(E_(l,b.shared.providers)):on.none(),k={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[x,w]},O=Xb(l),E=zb(l),T=Pb(l),B=FA.parts().header({dom:{tag:"div",classes:["tox-editor-header"]},components:H([T?[c]:[],O?[y]:E?[f]:[],Kb(l)?[]:[a.asSpec()]]),sticky:$b(l),editor:l,getSink:m}),D=H([[B],t?[]:[k]]),A=H([[{dom:{tag:"div",classes:["tox-editor-container"]},components:D}],t?[]:C.toArray(),[S]]),_=Jb(l),M=N(N({role:"application"},rh.isRtl()?{dir:"rtl"}:{}),_?{"aria-hidden":"true"}:{}),F=uu(FA.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(t?["tox-tinymce-inline"]:[]).concat(i).concat(r),styles:N({visibility:"hidden"},_?{opacity:"0",border:"0"}:{}),attributes:M},components:A,behaviours:ba([lg.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a"})])}));e=on.some(F),l.shortcuts.add("alt+F9","focus menubar",function(){FA.focusMenubar(F)}),l.shortcuts.add("alt+F10","focus toolbar",function(){FA.focusToolbar(F)});var I=Mb(F),R=Mb(v);XD(l,I,R),iy(l);function V(){var n=WA(KB(l)),t=WA(function(n){return JB(n).getOr(Ib(n))}(l));return l.inline||(Cr("div","width",t)&&yr(F.element(),"width",t),Cr("div","height",n)?yr(F.element(),"height",n):yr(F.element(),"height","200px")),n}return{mothership:I,uiMothership:R,backstage:b,renderUI:function(){h.setup(l,d),QA(l,b),lD(l,m,b),function(o){var r=o.ui.registry.getAll().sidebars;bn(wn(r),function(t){function e(){return on.from(o.queryCommandValue("ToggleSidebar")).is(t)}var n=r[t];o.ui.registry.addToggleButton(t,{icon:n.icon,tooltip:n.tooltip,onAction:function(n){o.execCommand("ToggleSidebar",!1,t),n.setActive(e())},onSetup:function(n){function t(){return n.setActive(e())}return o.on("ToggleSidebar",t),function(){o.off("ToggleSidebar",t)}}})})}(l),function(e,t,o){function r(n){n!==i.get()&&(WB(t(),n,o.providers),i.set(n))}var i=ye(!1),u=ye(on.none());e.on("ProgressState",function(n){if(u.get().each(qg.clearTimeout),mn(n.time)){var t=qg.setEditorTimeout(e,function(){return r(n.state)},n.time);u.set(on.some(t))}else r(n.state),u.set(on.none())})}(l,g,b.shared);var n=l.ui.registry.getAll(),t=n.buttons,e=n.menuItems,o=n.contextToolbars,r=n.sidebars,i=Lb(l),u={menuItems:e,menus:l.settings.menu?P(l.settings.menu,function(n){return An(n,{items:n.items})}):{},menubar:l.settings.menubar,toolbar:i.getOrThunk(function(){return l.getParam("toolbar",!0)}),buttons:t,sidebar:r};GD(l,o,v,{backstage:b}),C_(l,v);var a=l.getElement(),c=V(),s={mothership:I,uiMothership:R,outerContainer:F},f={targetNode:a,height:c};return p.render(l,s,u,b,f)},getUi:function(){return{channels:{broadcastAll:R.broadcast,broadcastOn:R.broadcastOn,register:function(){}}}}}},vM=function(n,t){var e=on.from(zo(n,"id")).fold(function(){var n=Xo("dialog-label");return Po(t,"id",n),n},l);Po(n,"aria-labelledby",e)},bM=nn([ct("lazySink"),ht("dragBlockClass"),Bt("getBounds",Su),St("useTabstopAt",nn(!0)),St("eventOrder",{}),Fs("modalBehaviours",[lg]),Ku("onExecute"),$u("onEscape")]),yM={sketch:l},xM=nn([Sl({name:"draghandle",overrides:function(n,t){return{behaviours:ba([w_.config({mode:"mouse",getTarget:function(n){return Cu(n,'[role="dialog"]').getOr(n)},blockerClass:n.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(t,null,2)).message),getBounds:n.getDragBounds})])}}}),xl({schema:[ct("dom")],name:"title"}),xl({factory:yM,schema:[ct("dom")],name:"close"}),xl({factory:yM,schema:[ct("dom")],name:"body"}),Sl({factory:yM,schema:[ct("dom")],name:"footer"}),wl({factory:{sketch:function(n,t){return N(N({},n),{dom:t.dom,components:t.components})}},schema:[St("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),St("components",[])],name:"blocker"})]),wM=Al({name:"ModalDialog",configFields:bM(),partFields:xM(),factory:function(o,n,t,r){var a=Xo("alloy.dialog.busy"),c=Xo("alloy.dialog.idle"),s=ba([lg.config({mode:"special",onTab:function(){return on.some(!0)},onShiftTab:function(){return on.some(!0)}}),vg.config({})]),e=Xo("modal-events"),i=N(N({},o.eventOrder),{"alloy.system.attached":[e].concat(o.eventOrder["alloy.system.attached"]||[])});return{uid:o.uid,dom:o.dom,components:n,apis:{show:function(i){var n=o.lazySink(i).getOrDie(),u=ye(on.none()),t=r.blocker(),e=n.getSystem().build(N(N({},t),{components:t.components.concat([au(i)]),behaviours:ba([vg.config({}),Kd("dialog-blocker-events",[bo(Qr(),function(){lg.focusIn(i)}),lo(c,function(n,t){Lo(i.element(),"aria-busy")&&(jo(i.element(),"aria-busy"),u.get().each(function(n){return mg.remove(i,n)}))}),lo(a,function(n,t){Po(i.element(),"aria-busy","true");var e=t.event().getBusySpec();u.get().each(function(n){mg.remove(i,n)});var o=e(i,s),r=n.getSystem().build(o);u.set(on.some(r)),mg.append(i,au(r)),r.hasConfigured(lg)&&lg.focusIn(r)})])])}));hs(n,e),lg.focusIn(i)},hide:function(t){Eo(t.element()).each(function(n){t.getSystem().getByDom(n).each(function(n){bs(n)})})},getBody:function(n){return Ks(n,o,"body")},getFooter:function(n){return Ks(n,o,"footer")},setIdle:function(n){oo(n,c)},setBusy:function(n,t){ro(n,a,{getBusySpec:t})}},eventOrder:i,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Rs(o.modalBehaviours,[mg.config({}),lg.config({mode:"cyclic",onEnter:o.onExecute,onEscape:o.onEscape,useTabstopAt:o.useTabstopAt}),Kd(e,[Ii(function(n){vM(n.element(),Ks(n,o,"title").element()),function(n,t){var e=on.from(zo(n,"id")).fold(function(){var n=Xo("dialog-describe");return Po(t,"id",n),n},l);Po(n,"aria-describedby",e)}(n.element(),Ks(n,o,"body").element())})])])}},apis:{show:function(n,t){n.show(t)},hide:function(n,t){n.hide(t)},getBody:function(n,t){return n.getBody(t)},getFooter:function(n,t){return n.getFooter(t)},setBusy:function(n,t,e){n.setBusy(t,e)},setIdle:function(n,t){n.setIdle(t)}}}),SM=[ft("type"),ft("text"),lt("level",["info","warn","error","success"]),ft("icon"),St("url","")],CM=re(SM),kM=[ft("type"),ft("text"),Tt("disabled",!1),Tt("primary",!1),ce("name","name",In(function(){return Xo("button-name")}),ge),yt("icon"),Tt("borderless",!1)],OM=re(kM),EM=[ft("type"),ft("name"),ft("label"),Tt("disabled",!1)],TM=re(EM),BM=pe,DM=[ft("type"),ft("name")],AM=DM.concat([yt("label")]),_M=re(AM),MM=ge,FM=re(AM),IM=ge,RM=re(AM),VM=Kn(se),NM=AM.concat([Tt("sandboxed",!0)]),HM=re(NM),PM=ge,zM=AM.concat([yt("inputMode"),yt("placeholder"),Tt("maximized",!1),Tt("disabled",!1)]),LM=re(zM),jM=ge,UM=AM.concat([gt("items",[ft("text"),ft("value")]),kt("size",1),Tt("disabled",!1)]),WM=re(UM),GM=ge,XM=AM.concat([Tt("constrain",!0),Tt("disabled",!1)]),YM=re(XM),qM=re([ft("width"),ft("height")]),KM=AM.concat([yt("placeholder"),Tt("maximized",!1),Tt("disabled",!1)]),JM=re(KM),$M=ge,QM=AM.concat([Et("filetype","file",["image","media","file"]),St("disabled",!1)]),ZM=re(QM),nF=re([ft("value"),St("meta",{})]),tF=DM.concat([Ot("tag","textarea"),ft("scriptId"),ft("scriptUrl"),(fM="settings",lM=undefined,Ct(fM,lM,be))]),eF=DM.concat([Ot("tag","textarea"),dt("init")]),oF=Zn(function(n){return tt("customeditor.old",qn(eF),n).orThunk(function(){return tt("customeditor.new",qn(tF),n)})}),rF=ge,iF=[ft("type"),ft("html"),Et("presets","presentation",["presentation","document"])],uF=re(iF),aF=AM.concat([st("currentState",re([ct("blob"),ft("url")]))]),cF=re(aF),sF=AM.concat([St("columns","auto")]),fF=re(sF),lF=(dM=[ft("value"),ft("text"),ft("icon")],ue(dM)),dF=[ft("type"),pt("header",ge),pt("cells",Kn(ge))],mF=re(dF),gF=fe(function(){return rt("type",{alertbanner:CM,bar:re(function(n){return[ft("type"),n]}(B_("bar"))),button:OM,checkbox:TM,colorinput:_M,colorpicker:FM,dropzone:RM,grid:re(T_(B_("grid"))),iframe:HM,input:LM,selectbox:WM,sizeinput:YM,textarea:JM,urlinput:ZM,customeditor:oF,htmlpanel:uF,imagetools:cF,collection:fF,label:re(function(n){return[ft("type"),ft("label"),n]}(B_("label"))),table:mF,panel:hF})}),pF=[ft("type"),St("classes",[]),pt("items",gF)],hF=re(pF),vF=[ce("name","name",In(function(){return Xo("tab-name")}),ge),ft("title"),pt("items",gF)],bF=[ft("type"),gt("tabs",vF)],yF=re(bF),xF=re([ft("type"),ft("name"),Tt("active",!1)].concat(Wp)),wF=pe,SF=[ce("name","name",In(function(){return Xo("button-name")}),ge),yt("icon"),Et("align","end",["start","end"]),Tt("primary",!1),Tt("disabled",!1)],CF=g(SF,[ft("text")]),kF=g([lt("type",["submit","cancel","custom"])],CF),OF=g([lt("type",["menu"]),yt("text"),yt("tooltip"),yt("icon"),pt("items",xF),Bt("onSetup",function(){return Z})],SF),EF=CF,TF=it("type",{submit:kF,cancel:kF,custom:kF,menu:OF}),BF=re([ft("title"),st("body",rt("type",{panel:hF,tabpanel:yF})),Ot("size","normal"),pt("buttons",TF),St("initialData",{}),Bt("onAction",Z),Bt("onChange",Z),Bt("onSubmit",Z),Bt("onClose",Z),Bt("onCancel",Z),St("onTabChange",Z)]),DF=function(n){return sn(n)?[n].concat(B(R(n),DF)):fn(n)?B(n,DF):[]},AF={checkbox:BM,colorinput:MM,colorpicker:IM,dropzone:VM,input:jM,iframe:PM,sizeinput:qM,selectbox:GM,size:qM,textarea:$M,urlinput:nF,customeditor:rF,collection:lF,togglemenuitem:wF},_F=re(g([lt("type",["cancel","custom"])],EF)),MF=re([ft("title"),ft("url"),bt("height"),bt("width"),(mM="buttons",gM=_F,vt(mM,Kn(gM))),Bt("onAction",Z),Bt("onCancel",Z),Bt("onClose",Z),Bt("onMessage",Z)]),FF={open:function(n,t){var e=__(t);return n(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(n,t){return n(et(function(n){return tt("dialog",MF,n)}(t)))},redial:function(n){return __(n)}},IF=Dl({name:"TabButton",configFields:[St("uid",undefined),ct("value"),ce("dom","dom",Rn(function(n){return{attributes:{role:"tab",id:Xo("aria"),"aria-selected":"false"}}}),de()),ht("action"),St("domModification",{}),Fs("tabButtonBehaviours",[vg,lg,Zf]),ct("view")],factory:function(n,t){return{uid:n.uid,dom:n.dom,components:n.components,events:rm(n.action),behaviours:Rs(n.tabButtonBehaviours,[vg.config({}),lg.config({mode:"execution",useSpace:!0,useEnter:!0}),Zf.config({store:{mode:"memory",initialValue:n.value}})]),domModification:n.domModification}}}),RF=nn([ct("tabs"),ct("dom"),St("clickToDismiss",!1),Fs("tabbarBehaviours",[ad,lg]),Xu(["tabClass","selectedClass"])]),VF=Cl({factory:IF,name:"tabs",unit:"tab",overrides:function(o,n){function r(n,t){ad.dehighlight(n,t),ro(n,Ai(),{tabbar:n,button:t})}function i(n,t){ad.highlight(n,t),ro(n,Di(),{tabbar:n,button:t})}return{action:function(n){var t=n.getSystem().getByUid(o.uid).getOrDie(),e=ad.isHighlighted(t,n);(e&&o.clickToDismiss?r:e?Z:i)(t,n)},domModification:{classes:[o.markers.tabClass]}}}}),NF=nn([VF]),HF=Al({name:"Tabbar",configFields:RF(),partFields:NF(),factory:function(n,t,e,o){return{uid:n.uid,dom:n.dom,components:t,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Rs(n.tabbarBehaviours,[ad.config({highlightClass:n.markers.selectedClass,itemClass:n.markers.tabClass,onHighlight:function(n,t){Po(t.element(),"aria-selected","true")},onDehighlight:function(n,t){Po(t.element(),"aria-selected","false")}}),lg.config({mode:"flow",getInitial:function(n){return ad.getHighlighted(n).map(function(n){return n.element()})},selector:"."+n.markers.tabClass,executeOnMove:!0})])}}}),PF=Dl({name:"Tabview",configFields:[Fs("tabviewBehaviours",[mg])],factory:function(n,t){return{uid:n.uid,dom:n.dom,behaviours:Rs(n.tabviewBehaviours,[mg.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),zF=nn([St("selectFirst",!0),qu("onChangeTab"),qu("onDismissTab"),St("tabs",[]),Fs("tabSectionBehaviours",[])]),LF=xl({factory:HF,schema:[ct("dom"),mt("markers",[ct("tabClass"),ct("selectedClass")])],name:"tabbar",defaults:function(n){return{tabs:n.tabs}}}),jF=xl({factory:PF,name:"tabview"}),UF=nn([LF,jF]),WF=Al({name:"TabSection",configFields:zF(),partFields:UF(),factory:function(r,n,t,e){function o(n,t){qs(n,r,"tabbar").each(function(n){t(n).each(io)})}return{uid:r.uid,dom:r.dom,components:n,behaviours:Is(r.tabSectionBehaviours),events:co(H([r.selectFirst?[Ii(function(n,t){o(n,ad.getFirst)})]:[],[lo(Di(),function(n,t){!function(o){var t=Zf.getValue(o);qs(o,r,"tabview").each(function(e){E(r.tabs,function(n){return n.value===t}).each(function(n){var t=n.view();Po(e.element(),"aria-labelledby",zo(o.element(),"id")),mg.set(e,t),r.onChangeTab(e,o,t)})})}(t.event().button())}),lo(Ai(),function(n,t){var e=t.event().button();r.onDismissTab(n,e)})]])),apis:{getViewItems:function(n){return qs(n,r,"tabview").map(function(n){return mg.contents(n)}).getOr([])},showTab:function(n,e){o(n,function(t){var n=ad.getCandidates(t);return E(n,function(n){return Zf.getValue(n)===e}).filter(function(n){return!ad.isHighlighted(t,n)})})}}}},apis:{getViewItems:function(n,t){return n.getViewItems(t)},showTab:function(n,t,e){n.showTab(t,e)}}}),GF="send-data-to-section",XF="send-data-to-view",YF=Xo("update-dialog"),qF=Xo("update-title"),KF=Xo("update-body"),JF=Xo("update-footer"),$F=Xo("body-send-message"),QF=function(i,n){function t(n,r){return lo(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){RT.getState(t).get().each(function(n){e(n,t)})};return g(P_(t,n),[t(sy,function(n,t,e){t.onAction(n,{name:e.name()})})])},ZF=function(i,n){function t(n,r){return lo(n,function(e,o){u(e,function(n,t){r(i(),n,o.event(),e)})})}var u=function(t,e){RT.getState(t).get().each(function(n){e(n.internalDialog,t)})};return g(P_(t,n),[t(fy,function(n,t){return t.onSubmit(n)}),t(uy,function(n,t,e){t.onChange(n,{name:e.name()})}),t(sy,function(n,t,e,o){function r(){return lg.focusIn(o)}var i=Sa();t.onAction(n,{name:e.name(),value:e.value()}),Sa().fold(function(){r()},function(n){!Ye(o.element(),n)||Lo(n,"disabled")?r():Ye(n,i.getOrNull())&&Lo(i.getOrDie(),"disabled")&&r()})}),t(my,function(n,t,e){t.onTabChange(n,{newTabName:e.name(),oldTabName:e.oldName()})}),Ri(function(n){var t=i();Zf.setValue(n,t.getData())})])},nI=gh.deviceType.isTouch(),tI=tinymce.util.Tools.resolve("tinymce.util.URI"),eI=["insertContent","setContent","execCommand","close","block","unblock"],oI=function(n){var l=n.backstage,d=n.editor,m=$b(d),e=function(c){var s=c.backstage.shared;return{open:function(n,t){function e(){wM.hide(u),t()}var o=vm(YC({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:on.none()},"cancel",c.backstage)),r=Y_(),i=X_(e,s.providers),u=uu($_({lazySink:function(){return s.getSink()},header:G_(r,i),body:q_(n,s.providers),footer:on.some(K_(J_([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[lo(cy,e)],eventOrder:{}}));wM.show(u);var a=o.get(u);vg.focus(a)}}}(n),o=function(s){var f=s.backstage.shared;return{open:function(n,t){function e(n){wM.hide(a),t(n)}var o=vm(YC({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:on.none()},"submit",s.backstage)),r=YC({name:"no",text:"No",primary:!0,align:"end",disabled:!1,icon:on.none()},"cancel",s.backstage),i=Y_(),u=X_(function(){return e(!1)},f.providers),a=uu($_({lazySink:function(){return f.getSink()},header:G_(i,u),body:q_(n,f.providers),footer:on.some(K_(J_([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[lo(cy,function(){return e(!1)}),lo(fy,function(){return e(!0)})],eventOrder:{}}));wM.show(a);var c=o.get(a);vg.focus(c)}}}(n),r=function(n,e){return FF.openUrl(function(n){var t=sM(n,{closeWindow:function(){wM.hide(t.dialog),e(t.instanceApi)}},d,l);return wM.show(t.dialog),t.instanceApi},n)},i=function(n,i){return FF.open(function(n,t,e){var o=t,r=uM({dataValidator:e,initialData:o,internalDialog:n},{redial:FF.redial,closeWindow:function(){wM.hide(r.dialog),i(r.instanceApi)}},l);return wM.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},n)},u=function(n,c,s,f){return FF.open(function(n,t,e){function o(){return i.on(function(n){cA.refresh(n)})}var r=function(n,t){return et(tt("data",t,n))}(t,e),i=function(){var t=ye(on.none());return{clear:function(){t.set(on.none())},set:function(n){t.set(on.some(n))},isSet:function(){return t.get().isSome()},on:function(n){t.get().each(n)}}}(),u=aM({dataValidator:e,initialData:r,internalDialog:n},{redial:FF.redial,closeWindow:function(){i.on(jg.hide),d.off("ResizeEditor",o),i.clear(),s(u.instanceApi)}},l,f),a=uu(jg.sketch({lazySink:l.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{},inlineBehaviours:ba(g([Kd("window-manager-inline-events",[lo(Oi(),function(n,t){oo(u.dialog,cy)})])],function(n,t){return t?[]:[cA.config({contextual:{lazyContext:function(){return on.some(xu(we.fromDom(n.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},leftAttr:"data-dock-left",topAttr:"data-dock-top",positionAttr:"data-dock-pos",modes:["top"]})]}(d,m)))}));return i.set(a),jg.showWithin(a,c,au(u.dialog),on.some(zr())),m||(cA.refresh(a),d.on("ResizeEditor",o)),u.instanceApi.setData(r),lg.focusIn(u.dialog),u.instanceApi},n)};return{open:function(n,t,e){return t!==undefined&&"toolbar"===t.inline?u(n,l.shared.anchors.toolbar(),e,t.ariaAttrs):t!==undefined&&"cursor"===t.inline?u(n,l.shared.anchors.cursor(),e,t.ariaAttrs):i(n,e)},openUrl:function(n,t){return r(n,t)},alert:function(n,t){e.open(n,function(){t()})},close:function(n){n.close()},confirm:function(n,t){o.open(n,function(n){t(n)})}}};!function mI(){n.add("silver",function(n){var t=hM(n),e=t.uiMothership,o=t.backstage,r=t.renderUI,i=t.getUi;db(n,o.shared);var u=oI({editor:n,backstage:o});return{renderUI:r,getWindowManagerImpl:nn(u),getNotificationManagerImpl:function(){return Kg(0,{backstage:o},e)},ui:i()}})}()}(window);