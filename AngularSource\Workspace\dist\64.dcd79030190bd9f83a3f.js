(window.webpackJsonp=window.webpackJsonp||[]).push([[64],{ki7V:function(e,t,n){"use strict";n.r(t);var l=n("CcnG"),i=n("mrSG"),a=n("ZYCi"),r=n("447K"),o=n("xRo1"),u=(n("R1Kr"),function(e){function t(t,n){var l=e.call(this,n,t)||this;return l.commonService=t,l.element=n,l.scheduleData=[],l.scheduleXml="",l.swtalert=new r.bb(t),l}return i.d(t,e),t.prototype.ngOnInit=function(){this.scheduleGrid=this.scheduleCanvas.addChild(r.hb),this.scenarioIdLbl.text=r.Wb.getPredictMessage("scenario.scenarioId",null),this.runAt.text=r.Wb.getPredictMessage("scenario.runAt",null),this.runAtDesc.text=r.Wb.getPredictMessage("scenario.runAtDesc",null),this.cancelButton.label=r.Wb.getPredictMessage("button.cancel",null),this.okButton.label=r.Wb.getPredictMessage("button.ok",null),this.scenarioIdtxt.toolTip=r.Wb.getPredictMessage("scenario.schedule.tooltip.scenarioId",null),this.runAtTxt.toolTip=r.Wb.getPredictMessage("scenario.schedule.runAt",null),this.cancelButton.toolTip=r.Wb.getPredictMessage("tooltip.cancelbutton",null),this.okButton.toolTip=r.Wb.getPredictMessage("tooltip.ok",null),this.scheduleGrid.editable=!0,this.runAtTxt.required=!0},t.prototype.onLoad=function(){this.scheduleData=[],window.opener&&window.opener.instanceElement&&(this.paramsEventsFromParent=window.opener.instanceElement.sendGeneralDataToSchedule(),this.scenarioIdtxt.text=this.paramsEventsFromParent.scenarioId,this.scheduleGrid.CustomGrid(this.paramsEventsFromParent.gridData.metadata),this.gridParams=this.paramsEventsFromParent.xmlData,this.scheduleData=this.paramsEventsFromParent.scheduleGridData,this.methodName=this.paramsEventsFromParent.methodName,this.changedRow=this.paramsEventsFromParent.selectedItem,this.generalSchedGridData=this.paramsEventsFromParent.genaralGridData),this.populateData()},t.prototype.populateData=function(){if("add"==this.methodName)this.convertXml();else{var e=[];if(this.scheduleData=[],e=this.changedRow.parameters.content?this.changedRow.parameters.content.split(";"):"",this.runAtTxt.text=this.changedRow.time.content,e){for(var t=0;t<e.length;t++){var n=e[t].split("("),l=n[0],i=n[1].replace(")","");this.scheduleData.push({parameter:{clickable:!1,content:l,negative:!1},value:{clickable:!1,content:i,negative:!1}})}this.scheduleGrid.gridData={size:this.scheduleData.length,row:this.scheduleData},this.scheduleGrid.refresh()}else this.scheduleGrid.gridData={size:0,row:[]}}},t.prototype.convertXml=function(){if(this.scheduleData=[],this.gridParams){var e=o.xml2js(this.gridParams,{object:!1,reversible:!1,coerce:!1,sanitize:!0,trim:!0,arrayNotation:!1,alternateTextNode:!1,compact:!0}).requiredParameters.parameter;e.length||(e=[e]);for(var t=0;t<e.length;t++)this.scheduleData.push({parameter:{clickable:!1,content:e[t].name._cdata,negative:!1},value:{clickable:!1,content:"",negative:!1}})}this.scheduleGrid.gridData={size:this.scheduleData.length,row:this.scheduleData},this.scheduleGrid.refresh()},t.prototype.prepareData=function(){if(""==this.runAtTxt.text)this.swtalert.error(r.Wb.getPredictMessage("scenario.missingRunAtValue",null));else if("add"==this.methodName&&this.checkIfTimeExists(this.runAtTxt.text))this.swtalert.error(r.Wb.getPredictMessage("scenario.timeAlreadyExists",null));else{if(window.opener&&window.opener.instanceElement){for(var e,t=[],n=[],l=this.runAtTxt.text,i=0;i<this.scheduleGrid.gridData.length;i++)t.push(this.scheduleGrid.gridData[i].parameter+"("+this.scheduleGrid.gridData[i].value+");");e=t.toString().slice(0,-1).replace(/,/g,""),n.push({time:{clickable:!1,content:l,negative:!1},parameters:{clickable:!1,content:e,negative:!1}}),window.opener.instanceElement.generalGridData=n,window.opener.instanceElement.refreshGeneralGrid()}window.close()}},t.prototype.checkIfTimeExists=function(e){for(var t=[],n=0;n<this.generalSchedGridData.length;n++)this.generalSchedGridData[n].time==e?t.push("true"):t.push("false");return!!t.includes("true")},t.prototype.cancelHandler=function(){r.x.call("close")},t.prototype.validateTime=function(e){var t=r.Wb.getPredictMessage("alert.validTime",null);return e.text.endsWith(":")&&(e.text=e.text+"00"),e.text&&0==validateFormatTime(e)?(this.swtalert.warning(t,null),e.text="",!1):(e.text=e.text.substring(0,5),!0)},t}(r.yb)),d=[{path:"",component:u}],c=(a.l.forChild(d),function(){return function(){}}()),s=n("pMnS"),h=n("RChO"),b=n("t6HQ"),p=n("WFGK"),g=n("5FqG"),m=n("Ip0R"),w=n("gIcY"),R=n("t/Na"),f=n("sE5F"),v=n("OzfB"),I=n("T7CS"),x=n("S7LP"),k=n("6aHO"),T=n("WzUx"),D=n("A7o+"),C=n("zCE2"),G=n("Jg5P"),S=n("3R0m"),A=n("hhbb"),y=n("5rxC"),L=n("Fzqc"),B=n("21Lb"),J=n("hUWP"),_=n("3pJQ"),P=n("V9q+"),E=n("VDKW"),W=n("kXfT"),F=n("BGbe");n.d(t,"ScheduleDetailsModuleNgFactory",function(){return M}),n.d(t,"RenderType_ScheduleDetails",function(){return O}),n.d(t,"View_ScheduleDetails_0",function(){return N}),n.d(t,"View_ScheduleDetails_Host_0",function(){return H}),n.d(t,"ScheduleDetailsNgFactory",function(){return q});var M=l.Gb(c,[],function(e){return l.Qb([l.Rb(512,l.n,l.vb,[[8,[s.a,h.a,b.a,p.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,q]],[3,l.n],l.J]),l.Rb(4608,m.m,m.l,[l.F,[2,m.u]]),l.Rb(4608,w.c,w.c,[]),l.Rb(4608,w.p,w.p,[]),l.Rb(4608,R.j,R.p,[m.c,l.O,R.n]),l.Rb(4608,R.q,R.q,[R.j,R.o]),l.Rb(5120,R.a,function(e){return[e,new r.tb]},[R.q]),l.Rb(4608,R.m,R.m,[]),l.Rb(6144,R.k,null,[R.m]),l.Rb(4608,R.i,R.i,[R.k]),l.Rb(6144,R.b,null,[R.i]),l.Rb(4608,R.f,R.l,[R.b,l.B]),l.Rb(4608,R.c,R.c,[R.f]),l.Rb(4608,f.c,f.c,[]),l.Rb(4608,f.g,f.b,[]),l.Rb(5120,f.i,f.j,[]),l.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),l.Rb(4608,f.f,f.a,[]),l.Rb(5120,f.d,f.k,[f.h,f.f]),l.Rb(5120,l.b,function(e,t){return[v.j(e,t)]},[m.c,l.O]),l.Rb(4608,I.a,I.a,[]),l.Rb(4608,x.a,x.a,[]),l.Rb(4608,k.a,k.a,[l.n,l.L,l.B,x.a,l.g]),l.Rb(4608,T.c,T.c,[l.n,l.g,l.B]),l.Rb(4608,T.e,T.e,[T.c]),l.Rb(4608,D.l,D.l,[]),l.Rb(4608,D.h,D.g,[]),l.Rb(4608,D.c,D.f,[]),l.Rb(4608,D.j,D.d,[]),l.Rb(4608,D.b,D.a,[]),l.Rb(4608,D.k,D.k,[D.l,D.h,D.c,D.j,D.b,D.m,D.n]),l.Rb(4608,T.i,T.i,[[2,D.k]]),l.Rb(4608,T.r,T.r,[T.L,[2,D.k],T.i]),l.Rb(4608,T.t,T.t,[]),l.Rb(4608,T.w,T.w,[]),l.Rb(1073742336,a.l,a.l,[[2,a.r],[2,a.k]]),l.Rb(1073742336,m.b,m.b,[]),l.Rb(1073742336,w.n,w.n,[]),l.Rb(1073742336,w.l,w.l,[]),l.Rb(1073742336,C.a,C.a,[]),l.Rb(1073742336,G.a,G.a,[]),l.Rb(1073742336,w.e,w.e,[]),l.Rb(1073742336,S.a,S.a,[]),l.Rb(1073742336,D.i,D.i,[]),l.Rb(1073742336,T.b,T.b,[]),l.Rb(1073742336,R.e,R.e,[]),l.Rb(1073742336,R.d,R.d,[]),l.Rb(1073742336,f.e,f.e,[]),l.Rb(1073742336,A.b,A.b,[]),l.Rb(1073742336,y.b,y.b,[]),l.Rb(1073742336,v.c,v.c,[]),l.Rb(1073742336,L.a,L.a,[]),l.Rb(1073742336,B.d,B.d,[]),l.Rb(1073742336,J.c,J.c,[]),l.Rb(1073742336,_.a,_.a,[]),l.Rb(1073742336,P.a,P.a,[[2,v.g],l.O]),l.Rb(1073742336,E.b,E.b,[]),l.Rb(1073742336,W.a,W.a,[]),l.Rb(1073742336,F.b,F.b,[]),l.Rb(1073742336,r.Tb,r.Tb,[]),l.Rb(1073742336,c,c,[]),l.Rb(256,R.n,"XSRF-TOKEN",[]),l.Rb(256,R.o,"X-XSRF-TOKEN",[]),l.Rb(256,"config",{},[]),l.Rb(256,D.m,void 0,[]),l.Rb(256,D.n,void 0,[]),l.Rb(256,"popperDefaults",{},[]),l.Rb(1024,a.i,function(){return[[{path:"",component:u}]]},[])])}),z=[[""]],O=l.Hb({encapsulation:0,styles:z,data:{}});function N(e){return l.dc(0,[l.Zb(402653184,1,{_container:0}),l.Zb(402653184,2,{scenarioIdLbl:0}),l.Zb(402653184,3,{runAt:0}),l.Zb(402653184,4,{runAtDesc:0}),l.Zb(402653184,5,{scenarioIdtxt:0}),l.Zb(402653184,6,{runAtTxt:0}),l.Zb(402653184,7,{scheduleCanvas:0}),l.Zb(402653184,8,{okButton:0}),l.Zb(402653184,9,{cancelButton:0}),(e()(),l.Jb(9,0,null,null,43,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,n){var l=!0,i=e.component;"creationComplete"===t&&(l=!1!==i.onLoad()&&l);return l},g.ad,g.hb)),l.Ib(10,4440064,null,0,r.yb,[l.r,r.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),l.Jb(11,0,null,0,41,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),l.Ib(12,4440064,null,0,r.ec,[l.r,r.i,l.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(e()(),l.Jb(13,0,null,0,31,"SwtCanvas",[["height","90%"],["width","100%"]],null,null,null,g.Nc,g.U)),l.Ib(14,4440064,null,0,r.db,[l.r,r.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(15,0,null,0,29,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),l.Ib(16,4440064,null,0,r.ec,[l.r,r.i,l.T],{width:[0,"width"],height:[1,"height"]},null),(e()(),l.Jb(17,0,null,0,25,"Grid",[["height","20%"],["paddingTop","10"],["width","100%"]],null,null,null,g.Cc,g.H)),l.Ib(18,4440064,null,0,r.z,[l.r,r.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),l.Jb(19,0,null,0,9,"GridRow",[["paddingLeft","10"]],null,null,null,g.Bc,g.J)),l.Ib(20,4440064,null,0,r.B,[l.r,r.i],{paddingLeft:[0,"paddingLeft"]},null),(e()(),l.Jb(21,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),l.Ib(22,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(23,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(24,4440064,[[2,4],["scenarioIdLbl",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(25,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),l.Ib(26,4440064,null,0,r.A,[l.r,r.i],null,null),(e()(),l.Jb(27,0,null,0,1,"SwtTextInput",[["enabled","false"],["width","200"]],null,null,null,g.kd,g.sb)),l.Ib(28,4440064,[[5,4],["scenarioIdtxt",4]],0,r.Rb,[l.r,r.i],{width:[0,"width"],enabled:[1,"enabled"]},null),(e()(),l.Jb(29,0,null,0,13,"GridRow",[["paddingLeft","10"]],null,null,null,g.Bc,g.J)),l.Ib(30,4440064,null,0,r.B,[l.r,r.i],{paddingLeft:[0,"paddingLeft"]},null),(e()(),l.Jb(31,0,null,0,3,"GridItem",[["width","120"]],null,null,null,g.Ac,g.I)),l.Ib(32,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(33,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),l.Ib(34,4440064,[[3,4],["runAt",4]],0,r.vb,[l.r,r.i],null,null),(e()(),l.Jb(35,0,null,0,3,"GridItem",[["width","90"]],null,null,null,g.Ac,g.I)),l.Ib(36,4440064,null,0,r.A,[l.r,r.i],{width:[0,"width"]},null),(e()(),l.Jb(37,0,null,0,1,"SwtTextInput",[["maxChars","5"],["width","80"]],null,[[null,"focusOut"]],function(e,t,n){var i=!0,a=e.component;"focusOut"===t&&(i=!1!==a.validateTime(l.Tb(e,38))&&i);return i},g.kd,g.sb)),l.Ib(38,4440064,[[6,4],["runAtTxt",4]],0,r.Rb,[l.r,r.i],{maxChars:[0,"maxChars"],width:[1,"width"]},{onFocusOut_:"focusOut"}),(e()(),l.Jb(39,0,null,0,3,"GridItem",[],null,null,null,g.Ac,g.I)),l.Ib(40,4440064,null,0,r.A,[l.r,r.i],null,null),(e()(),l.Jb(41,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,g.Yc,g.fb)),l.Ib(42,4440064,[[4,4],["runAtDesc",4]],0,r.vb,[l.r,r.i],{fontWeight:[0,"fontWeight"]},null),(e()(),l.Jb(43,0,null,0,1,"SwtCanvas",[["height","75%"],["id","scheduleCanvas"],["width","100%"]],null,null,null,g.Nc,g.U)),l.Ib(44,4440064,[[7,4],["scheduleCanvas",4]],0,r.db,[l.r,r.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),l.Jb(45,0,null,0,7,"SwtCanvas",[["height","10%"],["paddingTop","8"],["width","100%"]],null,null,null,g.Nc,g.U)),l.Ib(46,4440064,null,0,r.db,[l.r,r.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(e()(),l.Jb(47,0,null,0,5,"HBox",[["horizontalGap","5"],["paddingLeft","10"],["width","100%"]],null,null,null,g.Dc,g.K)),l.Ib(48,4440064,null,0,r.C,[l.r,r.i],{horizontalGap:[0,"horizontalGap"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(e()(),l.Jb(49,0,null,0,1,"SwtButton",[["enabled","true"]],null,[[null,"click"]],function(e,t,n){var l=!0,i=e.component;"click"===t&&(l=!1!==i.prepareData()&&l);return l},g.Mc,g.T)),l.Ib(50,4440064,[[8,4],["okButton",4]],0,r.cb,[l.r,r.i],{enabled:[0,"enabled"]},{onClick_:"click"}),(e()(),l.Jb(51,0,null,0,1,"SwtButton",[["enabled","true"]],null,[[null,"click"]],function(e,t,n){var l=!0,i=e.component;"click"===t&&(l=!1!==i.cancelHandler()&&l);return l},g.Mc,g.T)),l.Ib(52,4440064,[[9,4],["cancelButton",4]],0,r.cb,[l.r,r.i],{enabled:[0,"enabled"]},{onClick_:"click"})],function(e,t){e(t,10,0,"100%","100%");e(t,12,0,"100%","100%","5","5","5","5");e(t,14,0,"100%","90%");e(t,16,0,"100%","100%");e(t,18,0,"100%","20%","10");e(t,20,0,"10");e(t,22,0,"120"),e(t,24,0),e(t,26,0);e(t,28,0,"200","false");e(t,30,0,"10");e(t,32,0,"120"),e(t,34,0);e(t,36,0,"90");e(t,38,0,"5","80"),e(t,40,0);e(t,42,0,"normal");e(t,44,0,"scheduleCanvas","100%","75%");e(t,46,0,"100%","10%","8");e(t,48,0,"5","100%","10");e(t,50,0,"true");e(t,52,0,"true")},null)}function H(e){return l.dc(0,[(e()(),l.Jb(0,0,null,null,1,"app-schedule-details",[],null,null,null,N,O)),l.Ib(1,4440064,null,0,u,[r.i,l.r],null,null)],function(e,t){e(t,1,0)},null)}var q=l.Fb("app-schedule-details",u,H,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);