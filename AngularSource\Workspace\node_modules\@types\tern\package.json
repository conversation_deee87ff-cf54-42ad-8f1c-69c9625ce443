{"_from": "@types/tern@*", "_id": "@types/tern@0.23.9", "_inBundle": false, "_integrity": "sha512-ypzHFE/wBzh+BlH6rrBgS5I/Z7RD21pGhZ2rltb/+ZrVM1awdZwjx7hE5XfuYgHWk9uvV5HLZN3SloevCAp3Bw==", "_location": "/@types/tern", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/tern@*", "name": "@types/tern", "escapedName": "@types%2ftern", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/codemirror"], "_resolved": "https://registry.npmjs.org/@types/tern/-/tern-0.23.9.tgz", "_shasum": "6f6093a4a9af3e6bb8dde528e024924d196b367c", "_spec": "@types/tern@*", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\@types\\codemirror", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/nkappler"}], "dependencies": {"@types/estree": "*"}, "deprecated": false, "description": "TypeScript definitions for tern", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/tern", "license": "MIT", "main": "", "name": "@types/tern", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/tern"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "574b4df6b61b782fca94b39f2d1ab020ac04284e7ebe5ca76dc9b20a81773dad", "version": "0.23.9"}