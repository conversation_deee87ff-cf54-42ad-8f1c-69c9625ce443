{"_from": "zone.js@0.8.29", "_id": "zone.js@0.8.29", "_inBundle": false, "_integrity": "sha512-mla2acNCMkWXBD+c+yeUrBUrzOxYMNFdQ6FGfigGGtEVBPJx07BQeJekjt9DmH1FtZek4E9rE1eRR9qQpxACOQ==", "_location": "/zone.js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "zone.js@0.8.29", "name": "zone.js", "escapedName": "zone.js", "rawSpec": "0.8.29", "saveSpec": null, "fetchSpec": "0.8.29"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/zone.js/-/zone.js-0.8.29.tgz", "_shasum": "8dce92aa0dd553b50bc5bfbb90af9986ad845a12", "_spec": "zone.js@0.8.29", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "<PERSON>"}, "browser": "dist/zone.js", "bugs": {"url": "https://github.com/angular/zone.js/issues"}, "bundleDependencies": false, "dependencies": {}, "deprecated": false, "description": "Zones for JavaScript", "devDependencies": {"@types/jasmine": "2.2.33", "@types/node": "^9.x", "@types/systemjs": "^0.19.30", "assert": "^1.4.1", "bluebird": "^3.5.1", "clang-format": "^1.2.3", "concurrently": "^2.2.0", "conventional-changelog": "^1.1.7", "core-js": "^2.5.7", "es6-promise": "^3.0.2", "google-closure-compiler": "^20170409.0.0", "gulp": "^3.8.11", "gulp-clang-format": "^1.0.25", "gulp-conventional-changelog": "^1.1.7", "gulp-rename": "^1.2.2", "gulp-rollup": "^2.16.1", "gulp-tsc": "^1.1.4", "gulp-tslint": "^7.0.1", "gulp-uglify": "^1.2.0", "gulp-util": "^3.0.7", "jasmine": "^2.9.1", "jasmine-core": "^2.9.1", "karma": "^0.13.14", "karma-chrome-launcher": "^0.2.1", "karma-firefox-launcher": "^0.1.4", "karma-jasmine": "^1.1.1", "karma-mocha": "^1.2.0", "karma-phantomjs-launcher": "^1.0.4", "karma-safari-launcher": "^0.1.1", "karma-sauce-launcher": "^0.2.10", "karma-sourcemap-loader": "^0.3.6", "mocha": "^3.1.2", "nodejs-websocket": "^1.2.0", "phantomjs": "^2.1.7", "promises-aplus-tests": "^2.1.2", "pump": "^1.0.1", "rxjs": "^6.2.1", "selenium-webdriver": "^3.4.0", "systemjs": "^0.19.37", "ts-loader": "^0.6.0", "tslint": "^4.1.1", "tslint-eslint-rules": "^3.1.0", "typescript": "^3.0.3", "vrsource-tslint-rules": "^4.0.0", "webdriver-manager": "^12.0.6", "webdriverio": "^4.8.0", "whatwg-fetch": "^2.0.1"}, "directories": {"lib": "lib", "test": "test"}, "files": ["lib", "dist"], "homepage": "https://github.com/angular/zone.js#readme", "license": "MIT", "main": "dist/zone-node.js", "name": "zone.js", "repository": {"type": "git", "url": "git://github.com/angular/zone.js.git"}, "scripts": {"changelog": "gulp changelog", "ci": "npm run lint && npm run format && npm run promisetest && npm run test:single && npm run test-node", "closure:test": "scripts/closure/closure_compiler.sh", "format": "gulp format:enforce", "karma-jasmine": "karma start karma-build-jasmine.conf.js", "karma-jasmine-phantomjs:autoclose": "npm run karma-jasmine:phantomjs && npm run ws-client", "karma-jasmine:autoclose": "npm run karma-jasmine:single && npm run ws-client", "karma-jasmine:es6": "karma start karma-build-jasmine.es6.conf.js", "karma-jasmine:phantomjs": "karma start karma-build-jasmine-phantomjs.conf.js --single-run", "karma-jasmine:single": "karma start karma-build-jasmine.conf.js --single-run", "lint": "gulp lint", "prepublish": "tsc && gulp build", "promisefinallytest": "mocha promise.finally.spec.js", "promisetest": "gulp promisetest", "serve": "python -m SimpleHTTPServer 8000", "test": "npm run tsc && concurrently \"npm run tsc:w\" \"npm run ws-server\" \"npm run karma-jasmine\"", "test-bluebird": "gulp test/bluebird", "test-dist": "concurrently \"npm run tsc:w\" \"npm run ws-server\" \"karma start karma-dist-jasmine.conf.js\"", "test-mocha": "npm run tsc && concurrently \"npm run tsc:w\" \"npm run ws-server\" \"karma start karma-build-mocha.conf.js\"", "test-node": "gulp test/node", "test:es6": "npm run tsc && concurrently \"npm run tsc:w\" \"npm run ws-server\" \"npm run karma-jasmine:es6\"", "test:phantomjs": "npm run tsc && concurrently \"npm run tsc:w\" \"npm run ws-server\" \"npm run karma-jasmine:phantomjs\"", "test:phantomjs-single": "npm run tsc && concurrently \"npm run ws-server\" \"npm run karma-jasmine-phantomjs:autoclose\"", "test:single": "npm run tsc && concurrently \"npm run ws-server\" \"npm run karma-jasmine:autoclose\"", "tsc": "tsc -p .", "tsc:w": "tsc -w -p .", "tslint": "tslint -c tslint.json 'lib/**/*.ts'", "webdriver-http": "node simple-server.js", "webdriver-sauce-test": "node test/webdriver/test.sauce.js", "webdriver-start": "webdriver-manager update && webdriver-manager start", "webdriver-test": "node test/webdriver/test.js", "ws-client": "node ./test/ws-client.js", "ws-server": "node ./test/ws-server.js"}, "typings": "dist/zone.js.d.ts", "unpkg": "dist/zone.js", "version": "0.8.29"}