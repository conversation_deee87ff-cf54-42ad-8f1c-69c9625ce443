/*!-----------------------------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * monaco-json version: 2.7.0(f3afc1b721188b32506d5b8a924561e3ec7534e3)
 * Released under the MIT license
 * https://github.com/Microsoft/monaco-json/blob/master/LICENSE.md
 *-----------------------------------------------------------------------------*/
define("vs/language/json/workerManager",["require","exports"],(function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=function(){function e(e){var t=this;this._defaults=e,this._worker=null,this._idleCheckInterval=setInterval((function(){return t._checkIfIdle()}),3e4),this._lastUsedTime=0,this._configChangeListener=this._defaults.onDidChange((function(){return t._stopWorker()}))}return e.prototype._stopWorker=function(){this._worker&&(this._worker.dispose(),this._worker=null),this._client=null},e.prototype.dispose=function(){clearInterval(this._idleCheckInterval),this._configChangeListener.dispose(),this._stopWorker()},e.prototype._checkIfIdle=function(){this._worker&&(Date.now()-this._lastUsedTime>12e4&&this._stopWorker())},e.prototype._getClient=function(){return this._lastUsedTime=Date.now(),this._client||(this._worker=monaco.editor.createWebWorker({moduleId:"vs/language/json/jsonWorker",label:this._defaults.languageId,createData:{languageSettings:this._defaults.diagnosticsOptions,languageId:this._defaults.languageId,enableSchemaRequest:this._defaults.diagnosticsOptions.enableSchemaRequest}}),this._client=this._worker.getProxy()),this._client},e.prototype.getLanguageServiceWorker=function(){for(var e,t=this,n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return this._getClient().then((function(t){e=t})).then((function(e){return t._worker.withSyncedResources(n)})).then((function(t){return e}))},e}();t.WorkerManager=n})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("vscode-languageserver-types/main",["require","exports"],e)}((function(e,t){"use strict";var n,r,o,i,a,s,c,u,f,d,l,g,p;Object.defineProperty(t,"__esModule",{value:!0}),function(e){e.create=function(e,t){return{line:e,character:t}},e.is=function(e){var t=e;return _.objectLiteral(t)&&_.number(t.line)&&_.number(t.character)}}(n=t.Position||(t.Position={})),function(e){e.create=function(e,t,r,o){if(_.number(e)&&_.number(t)&&_.number(r)&&_.number(o))return{start:n.create(e,t),end:n.create(r,o)};if(n.is(e)&&n.is(t))return{start:e,end:t};throw new Error("Range#create called with invalid arguments["+e+", "+t+", "+r+", "+o+"]")},e.is=function(e){var t=e;return _.objectLiteral(t)&&n.is(t.start)&&n.is(t.end)}}(r=t.Range||(t.Range={})),function(e){e.create=function(e,t){return{uri:e,range:t}},e.is=function(e){var t=e;return _.defined(t)&&r.is(t.range)&&(_.string(t.uri)||_.undefined(t.uri))}}(o=t.Location||(t.Location={})),function(e){e.create=function(e,t,n,r){return{targetUri:e,targetRange:t,targetSelectionRange:n,originSelectionRange:r}},e.is=function(e){var t=e;return _.defined(t)&&r.is(t.targetRange)&&_.string(t.targetUri)&&(r.is(t.targetSelectionRange)||_.undefined(t.targetSelectionRange))&&(r.is(t.originSelectionRange)||_.undefined(t.originSelectionRange))}}(t.LocationLink||(t.LocationLink={})),function(e){e.create=function(e,t,n,r){return{red:e,green:t,blue:n,alpha:r}},e.is=function(e){var t=e;return _.number(t.red)&&_.number(t.green)&&_.number(t.blue)&&_.number(t.alpha)}}(i=t.Color||(t.Color={})),function(e){e.create=function(e,t){return{range:e,color:t}},e.is=function(e){var t=e;return r.is(t.range)&&i.is(t.color)}}(t.ColorInformation||(t.ColorInformation={})),function(e){e.create=function(e,t,n){return{label:e,textEdit:t,additionalTextEdits:n}},e.is=function(e){var t=e;return _.string(t.label)&&(_.undefined(t.textEdit)||u.is(t))&&(_.undefined(t.additionalTextEdits)||_.typedArray(t.additionalTextEdits,u.is))}}(t.ColorPresentation||(t.ColorPresentation={})),function(e){e.Comment="comment",e.Imports="imports",e.Region="region"}(t.FoldingRangeKind||(t.FoldingRangeKind={})),function(e){e.create=function(e,t,n,r,o){var i={startLine:e,endLine:t};return _.defined(n)&&(i.startCharacter=n),_.defined(r)&&(i.endCharacter=r),_.defined(o)&&(i.kind=o),i},e.is=function(e){var t=e;return _.number(t.startLine)&&_.number(t.startLine)&&(_.undefined(t.startCharacter)||_.number(t.startCharacter))&&(_.undefined(t.endCharacter)||_.number(t.endCharacter))&&(_.undefined(t.kind)||_.string(t.kind))}}(t.FoldingRange||(t.FoldingRange={})),function(e){e.create=function(e,t){return{location:e,message:t}},e.is=function(e){var t=e;return _.defined(t)&&o.is(t.location)&&_.string(t.message)}}(a=t.DiagnosticRelatedInformation||(t.DiagnosticRelatedInformation={})),function(e){e.Error=1,e.Warning=2,e.Information=3,e.Hint=4}(t.DiagnosticSeverity||(t.DiagnosticSeverity={})),function(e){e.create=function(e,t,n,r,o,i){var a={range:e,message:t};return _.defined(n)&&(a.severity=n),_.defined(r)&&(a.code=r),_.defined(o)&&(a.source=o),_.defined(i)&&(a.relatedInformation=i),a},e.is=function(e){var t=e;return _.defined(t)&&r.is(t.range)&&_.string(t.message)&&(_.number(t.severity)||_.undefined(t.severity))&&(_.number(t.code)||_.string(t.code)||_.undefined(t.code))&&(_.string(t.source)||_.undefined(t.source))&&(_.undefined(t.relatedInformation)||_.typedArray(t.relatedInformation,a.is))}}(s=t.Diagnostic||(t.Diagnostic={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o={title:e,command:t};return _.defined(n)&&n.length>0&&(o.arguments=n),o},e.is=function(e){var t=e;return _.defined(t)&&_.string(t.title)&&_.string(t.command)}}(c=t.Command||(t.Command={})),function(e){e.replace=function(e,t){return{range:e,newText:t}},e.insert=function(e,t){return{range:{start:e,end:e},newText:t}},e.del=function(e){return{range:e,newText:""}},e.is=function(e){var t=e;return _.objectLiteral(t)&&_.string(t.newText)&&r.is(t.range)}}(u=t.TextEdit||(t.TextEdit={})),function(e){e.create=function(e,t){return{textDocument:e,edits:t}},e.is=function(e){var t=e;return _.defined(t)&&h.is(t.textDocument)&&Array.isArray(t.edits)}}(f=t.TextDocumentEdit||(t.TextDocumentEdit={})),function(e){e.create=function(e,t){var n={kind:"create",uri:e};return void 0===t||void 0===t.overwrite&&void 0===t.ignoreIfExists||(n.options=t),n},e.is=function(e){var t=e;return t&&"create"===t.kind&&_.string(t.uri)&&(void 0===t.options||(void 0===t.options.overwrite||_.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||_.boolean(t.options.ignoreIfExists)))}}(d=t.CreateFile||(t.CreateFile={})),function(e){e.create=function(e,t,n){var r={kind:"rename",oldUri:e,newUri:t};return void 0===n||void 0===n.overwrite&&void 0===n.ignoreIfExists||(r.options=n),r},e.is=function(e){var t=e;return t&&"rename"===t.kind&&_.string(t.oldUri)&&_.string(t.newUri)&&(void 0===t.options||(void 0===t.options.overwrite||_.boolean(t.options.overwrite))&&(void 0===t.options.ignoreIfExists||_.boolean(t.options.ignoreIfExists)))}}(l=t.RenameFile||(t.RenameFile={})),function(e){e.create=function(e,t){var n={kind:"delete",uri:e};return void 0===t||void 0===t.recursive&&void 0===t.ignoreIfNotExists||(n.options=t),n},e.is=function(e){var t=e;return t&&"delete"===t.kind&&_.string(t.uri)&&(void 0===t.options||(void 0===t.options.recursive||_.boolean(t.options.recursive))&&(void 0===t.options.ignoreIfNotExists||_.boolean(t.options.ignoreIfNotExists)))}}(g=t.DeleteFile||(t.DeleteFile={})),function(e){e.is=function(e){var t=e;return t&&(void 0!==t.changes||void 0!==t.documentChanges)&&(void 0===t.documentChanges||t.documentChanges.every((function(e){return _.string(e.kind)?d.is(e)||l.is(e)||g.is(e):f.is(e)})))}}(p=t.WorkspaceEdit||(t.WorkspaceEdit={}));var h,m,v,b,y=function(){function e(e){this.edits=e}return e.prototype.insert=function(e,t){this.edits.push(u.insert(e,t))},e.prototype.replace=function(e,t){this.edits.push(u.replace(e,t))},e.prototype.delete=function(e){this.edits.push(u.del(e))},e.prototype.add=function(e){this.edits.push(e)},e.prototype.all=function(){return this.edits},e.prototype.clear=function(){this.edits.splice(0,this.edits.length)},e}(),k=function(){function e(e){var t=this;this._textEditChanges=Object.create(null),e&&(this._workspaceEdit=e,e.documentChanges?e.documentChanges.forEach((function(e){if(f.is(e)){var n=new y(e.edits);t._textEditChanges[e.textDocument.uri]=n}})):e.changes&&Object.keys(e.changes).forEach((function(n){var r=new y(e.changes[n]);t._textEditChanges[n]=r})))}return Object.defineProperty(e.prototype,"edit",{get:function(){return this._workspaceEdit},enumerable:!0,configurable:!0}),e.prototype.getTextEditChange=function(e){if(h.is(e)){if(this._workspaceEdit||(this._workspaceEdit={documentChanges:[]}),!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.");var t=e;if(!(r=this._textEditChanges[t.uri])){var n={textDocument:t,edits:o=[]};this._workspaceEdit.documentChanges.push(n),r=new y(o),this._textEditChanges[t.uri]=r}return r}if(this._workspaceEdit||(this._workspaceEdit={changes:Object.create(null)}),!this._workspaceEdit.changes)throw new Error("Workspace edit is not configured for normal text edit changes.");var r;if(!(r=this._textEditChanges[e])){var o=[];this._workspaceEdit.changes[e]=o,r=new y(o),this._textEditChanges[e]=r}return r},e.prototype.createFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(d.create(e,t))},e.prototype.renameFile=function(e,t,n){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(l.create(e,t,n))},e.prototype.deleteFile=function(e,t){this.checkDocumentChanges(),this._workspaceEdit.documentChanges.push(g.create(e,t))},e.prototype.checkDocumentChanges=function(){if(!this._workspaceEdit||!this._workspaceEdit.documentChanges)throw new Error("Workspace edit is not configured for document changes.")},e}();t.WorkspaceChange=k,function(e){e.create=function(e){return{uri:e}},e.is=function(e){var t=e;return _.defined(t)&&_.string(t.uri)}}(t.TextDocumentIdentifier||(t.TextDocumentIdentifier={})),function(e){e.create=function(e,t){return{uri:e,version:t}},e.is=function(e){var t=e;return _.defined(t)&&_.string(t.uri)&&(null===t.version||_.number(t.version))}}(h=t.VersionedTextDocumentIdentifier||(t.VersionedTextDocumentIdentifier={})),function(e){e.create=function(e,t,n,r){return{uri:e,languageId:t,version:n,text:r}},e.is=function(e){var t=e;return _.defined(t)&&_.string(t.uri)&&_.string(t.languageId)&&_.number(t.version)&&_.string(t.text)}}(t.TextDocumentItem||(t.TextDocumentItem={})),function(e){e.PlainText="plaintext",e.Markdown="markdown"}(m=t.MarkupKind||(t.MarkupKind={})),function(e){e.is=function(t){var n=t;return n===e.PlainText||n===e.Markdown}}(m=t.MarkupKind||(t.MarkupKind={})),function(e){e.is=function(e){var t=e;return _.objectLiteral(e)&&m.is(t.kind)&&_.string(t.value)}}(v=t.MarkupContent||(t.MarkupContent={})),function(e){e.Text=1,e.Method=2,e.Function=3,e.Constructor=4,e.Field=5,e.Variable=6,e.Class=7,e.Interface=8,e.Module=9,e.Property=10,e.Unit=11,e.Value=12,e.Enum=13,e.Keyword=14,e.Snippet=15,e.Color=16,e.File=17,e.Reference=18,e.Folder=19,e.EnumMember=20,e.Constant=21,e.Struct=22,e.Event=23,e.Operator=24,e.TypeParameter=25}(t.CompletionItemKind||(t.CompletionItemKind={})),function(e){e.PlainText=1,e.Snippet=2}(t.InsertTextFormat||(t.InsertTextFormat={})),function(e){e.create=function(e){return{label:e}}}(t.CompletionItem||(t.CompletionItem={})),function(e){e.create=function(e,t){return{items:e||[],isIncomplete:!!t}}}(t.CompletionList||(t.CompletionList={})),function(e){e.fromPlainText=function(e){return e.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")},e.is=function(e){var t=e;return _.string(t)||_.objectLiteral(t)&&_.string(t.language)&&_.string(t.value)}}(b=t.MarkedString||(t.MarkedString={})),function(e){e.is=function(e){var t=e;return!!t&&_.objectLiteral(t)&&(v.is(t.contents)||b.is(t.contents)||_.typedArray(t.contents,b.is))&&(void 0===e.range||r.is(e.range))}}(t.Hover||(t.Hover={})),function(e){e.create=function(e,t){return t?{label:e,documentation:t}:{label:e}}}(t.ParameterInformation||(t.ParameterInformation={})),function(e){e.create=function(e,t){for(var n=[],r=2;r<arguments.length;r++)n[r-2]=arguments[r];var o={label:e};return _.defined(t)&&(o.documentation=t),_.defined(n)?o.parameters=n:o.parameters=[],o}}(t.SignatureInformation||(t.SignatureInformation={})),function(e){e.Text=1,e.Read=2,e.Write=3}(t.DocumentHighlightKind||(t.DocumentHighlightKind={})),function(e){e.create=function(e,t){var n={range:e};return _.number(t)&&(n.kind=t),n}}(t.DocumentHighlight||(t.DocumentHighlight={})),function(e){e.File=1,e.Module=2,e.Namespace=3,e.Package=4,e.Class=5,e.Method=6,e.Property=7,e.Field=8,e.Constructor=9,e.Enum=10,e.Interface=11,e.Function=12,e.Variable=13,e.Constant=14,e.String=15,e.Number=16,e.Boolean=17,e.Array=18,e.Object=19,e.Key=20,e.Null=21,e.EnumMember=22,e.Struct=23,e.Event=24,e.Operator=25,e.TypeParameter=26}(t.SymbolKind||(t.SymbolKind={})),function(e){e.create=function(e,t,n,r,o){var i={name:e,kind:t,location:{uri:r,range:n}};return o&&(i.containerName=o),i}}(t.SymbolInformation||(t.SymbolInformation={}));var C=function(){};t.DocumentSymbol=C,function(e){e.create=function(e,t,n,r,o,i){var a={name:e,detail:t,kind:n,range:r,selectionRange:o};return void 0!==i&&(a.children=i),a},e.is=function(e){var t=e;return t&&_.string(t.name)&&_.number(t.kind)&&r.is(t.range)&&r.is(t.selectionRange)&&(void 0===t.detail||_.string(t.detail))&&(void 0===t.deprecated||_.boolean(t.deprecated))&&(void 0===t.children||Array.isArray(t.children))}}(C=t.DocumentSymbol||(t.DocumentSymbol={})),t.DocumentSymbol=C,function(e){e.QuickFix="quickfix",e.Refactor="refactor",e.RefactorExtract="refactor.extract",e.RefactorInline="refactor.inline",e.RefactorRewrite="refactor.rewrite",e.Source="source",e.SourceOrganizeImports="source.organizeImports"}(t.CodeActionKind||(t.CodeActionKind={})),function(e){e.create=function(e,t){var n={diagnostics:e};return null!=t&&(n.only=t),n},e.is=function(e){var t=e;return _.defined(t)&&_.typedArray(t.diagnostics,s.is)&&(void 0===t.only||_.typedArray(t.only,_.string))}}(t.CodeActionContext||(t.CodeActionContext={})),function(e){e.create=function(e,t,n){var r={title:e};return c.is(t)?r.command=t:r.edit=t,void 0!==n&&(r.kind=n),r},e.is=function(e){var t=e;return t&&_.string(t.title)&&(void 0===t.diagnostics||_.typedArray(t.diagnostics,s.is))&&(void 0===t.kind||_.string(t.kind))&&(void 0!==t.edit||void 0!==t.command)&&(void 0===t.command||c.is(t.command))&&(void 0===t.edit||p.is(t.edit))}}(t.CodeAction||(t.CodeAction={})),function(e){e.create=function(e,t){var n={range:e};return _.defined(t)&&(n.data=t),n},e.is=function(e){var t=e;return _.defined(t)&&r.is(t.range)&&(_.undefined(t.command)||c.is(t.command))}}(t.CodeLens||(t.CodeLens={})),function(e){e.create=function(e,t){return{tabSize:e,insertSpaces:t}},e.is=function(e){var t=e;return _.defined(t)&&_.number(t.tabSize)&&_.boolean(t.insertSpaces)}}(t.FormattingOptions||(t.FormattingOptions={}));var E=function(){};t.DocumentLink=E,function(e){e.create=function(e,t,n){return{range:e,target:t,data:n}},e.is=function(e){var t=e;return _.defined(t)&&r.is(t.range)&&(_.undefined(t.target)||_.string(t.target))}}(E=t.DocumentLink||(t.DocumentLink={})),t.DocumentLink=E,t.EOL=["\n","\r\n","\r"],function(e){e.create=function(e,t,n,r){return new T(e,t,n,r)},e.is=function(e){var t=e;return!!(_.defined(t)&&_.string(t.uri)&&(_.undefined(t.languageId)||_.string(t.languageId))&&_.number(t.lineCount)&&_.func(t.getText)&&_.func(t.positionAt)&&_.func(t.offsetAt))},e.applyEdits=function(e,t){for(var n=e.getText(),r=function e(t,n){if(t.length<=1)return t;var r=t.length/2|0;var o=t.slice(0,r);var i=t.slice(r);e(o,n);e(i,n);var a=0;var s=0;var c=0;for(;a<o.length&&s<i.length;){var u=n(o[a],i[s]);t[c++]=u<=0?o[a++]:i[s++]}for(;a<o.length;)t[c++]=o[a++];for(;s<i.length;)t[c++]=i[s++];return t}(t,(function(e,t){var n=e.range.start.line-t.range.start.line;return 0===n?e.range.start.character-t.range.start.character:n})),o=n.length,i=r.length-1;i>=0;i--){var a=r[i],s=e.offsetAt(a.range.start),c=e.offsetAt(a.range.end);if(!(c<=o))throw new Error("Overlapping edit");n=n.substring(0,s)+a.newText+n.substring(c,n.length),o=s}return n}}(t.TextDocument||(t.TextDocument={})),function(e){e.Manual=1,e.AfterDelay=2,e.FocusOut=3}(t.TextDocumentSaveReason||(t.TextDocumentSaveReason={}));var _,T=function(){function e(e,t,n,r){this._uri=e,this._languageId=t,this._version=n,this._content=r,this._lineOffsets=null}return Object.defineProperty(e.prototype,"uri",{get:function(){return this._uri},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"languageId",{get:function(){return this._languageId},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"version",{get:function(){return this._version},enumerable:!0,configurable:!0}),e.prototype.getText=function(e){if(e){var t=this.offsetAt(e.start),n=this.offsetAt(e.end);return this._content.substring(t,n)}return this._content},e.prototype.update=function(e,t){this._content=e.text,this._version=t,this._lineOffsets=null},e.prototype.getLineOffsets=function(){if(null===this._lineOffsets){for(var e=[],t=this._content,n=!0,r=0;r<t.length;r++){n&&(e.push(r),n=!1);var o=t.charAt(r);n="\r"===o||"\n"===o,"\r"===o&&r+1<t.length&&"\n"===t.charAt(r+1)&&r++}n&&t.length>0&&e.push(t.length),this._lineOffsets=e}return this._lineOffsets},e.prototype.positionAt=function(e){e=Math.max(Math.min(e,this._content.length),0);var t=this.getLineOffsets(),r=0,o=t.length;if(0===o)return n.create(0,e);for(;r<o;){var i=Math.floor((r+o)/2);t[i]>e?o=i:r=i+1}var a=r-1;return n.create(a,e-t[a])},e.prototype.offsetAt=function(e){var t=this.getLineOffsets();if(e.line>=t.length)return this._content.length;if(e.line<0)return 0;var n=t[e.line],r=e.line+1<t.length?t[e.line+1]:this._content.length;return Math.max(Math.min(n+e.character,r),n)},Object.defineProperty(e.prototype,"lineCount",{get:function(){return this.getLineOffsets().length},enumerable:!0,configurable:!0}),e}();!function(e){var t=Object.prototype.toString;e.defined=function(e){return void 0!==e},e.undefined=function(e){return void 0===e},e.boolean=function(e){return!0===e||!1===e},e.string=function(e){return"[object String]"===t.call(e)},e.number=function(e){return"[object Number]"===t.call(e)},e.func=function(e){return"[object Function]"===t.call(e)},e.objectLiteral=function(e){return null!==e&&"object"==typeof e},e.typedArray=function(e,t){return Array.isArray(e)&&e.every(t)}}(_||(_={}))})),define("vscode-languageserver-types",["vscode-languageserver-types/main"],(function(e){return e})),define("vs/language/json/languageFeatures",["require","exports","vscode-languageserver-types"],(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0});monaco.Uri;var r=monaco.Range,o=function(){function e(e,t,n){var r=this;this._languageId=e,this._worker=t,this._disposables=[],this._listener=Object.create(null);var o=function(e){var t,n=e.getModeId();n===r._languageId&&(r._listener[e.uri.toString()]=e.onDidChangeContent((function(){clearTimeout(t),t=setTimeout((function(){return r._doValidate(e.uri,n)}),500)})),r._doValidate(e.uri,n))},i=function(e){monaco.editor.setModelMarkers(e,r._languageId,[]);var t=e.uri.toString(),n=r._listener[t];n&&(n.dispose(),delete r._listener[t])};this._disposables.push(monaco.editor.onDidCreateModel(o)),this._disposables.push(monaco.editor.onWillDisposeModel((function(e){i(e),r._resetSchema(e.uri)}))),this._disposables.push(monaco.editor.onDidChangeModelLanguage((function(e){i(e.model),o(e.model),r._resetSchema(e.model.uri)}))),this._disposables.push(n.onDidChange((function(e){monaco.editor.getModels().forEach((function(e){e.getModeId()===r._languageId&&(i(e),o(e))}))}))),this._disposables.push({dispose:function(){for(var e in monaco.editor.getModels().forEach(i),r._listener)r._listener[e].dispose()}}),monaco.editor.getModels().forEach(o)}return e.prototype.dispose=function(){this._disposables.forEach((function(e){return e&&e.dispose()})),this._disposables=[]},e.prototype._resetSchema=function(e){this._worker().then((function(t){t.resetSchema(e.toString())}))},e.prototype._doValidate=function(e,t){this._worker(e).then((function(n){return n.doValidation(e.toString()).then((function(n){var r=n.map((function(e){return n="number"==typeof(t=e).code?String(t.code):t.code,{severity:i(t.severity),startLineNumber:t.range.start.line+1,startColumn:t.range.start.character+1,endLineNumber:t.range.end.line+1,endColumn:t.range.end.character+1,message:t.message,code:n,source:t.source};var t,n})),o=monaco.editor.getModel(e);o&&o.getModeId()===t&&monaco.editor.setModelMarkers(o,t,r)}))})).then(void 0,(function(e){console.error(e)}))},e}();function i(e){switch(e){case n.DiagnosticSeverity.Error:return monaco.MarkerSeverity.Error;case n.DiagnosticSeverity.Warning:return monaco.MarkerSeverity.Warning;case n.DiagnosticSeverity.Information:return monaco.MarkerSeverity.Info;case n.DiagnosticSeverity.Hint:return monaco.MarkerSeverity.Hint;default:return monaco.MarkerSeverity.Info}}function a(e){if(e)return{character:e.column-1,line:e.lineNumber-1}}function s(e){if(e)return{start:{line:e.startLineNumber-1,character:e.startColumn-1},end:{line:e.endLineNumber-1,character:e.endColumn-1}}}function c(e){if(e)return new r(e.start.line+1,e.start.character+1,e.end.line+1,e.end.character+1)}function u(e){var t=monaco.languages.CompletionItemKind;switch(e){case n.CompletionItemKind.Text:return t.Text;case n.CompletionItemKind.Method:return t.Method;case n.CompletionItemKind.Function:return t.Function;case n.CompletionItemKind.Constructor:return t.Constructor;case n.CompletionItemKind.Field:return t.Field;case n.CompletionItemKind.Variable:return t.Variable;case n.CompletionItemKind.Class:return t.Class;case n.CompletionItemKind.Interface:return t.Interface;case n.CompletionItemKind.Module:return t.Module;case n.CompletionItemKind.Property:return t.Property;case n.CompletionItemKind.Unit:return t.Unit;case n.CompletionItemKind.Value:return t.Value;case n.CompletionItemKind.Enum:return t.Enum;case n.CompletionItemKind.Keyword:return t.Keyword;case n.CompletionItemKind.Snippet:return t.Snippet;case n.CompletionItemKind.Color:return t.Color;case n.CompletionItemKind.File:return t.File;case n.CompletionItemKind.Reference:return t.Reference}return t.Property}function f(e){if(e)return{range:c(e.range),text:e.newText}}t.DiagnosticsAdapter=o;var d=function(){function e(e){this._worker=e}return Object.defineProperty(e.prototype,"triggerCharacters",{get:function(){return[" ",":"]},enumerable:!0,configurable:!0}),e.prototype.provideCompletionItems=function(e,t,o,i){var s=e.uri;return this._worker(s).then((function(e){return e.doComplete(s.toString(),a(t))})).then((function(o){if(o){var i=e.getWordUntilPosition(t),a=new r(t.lineNumber,i.startColumn,t.lineNumber,i.endColumn),s=o.items.map((function(e){var t={label:e.label,insertText:e.insertText||e.label,sortText:e.sortText,filterText:e.filterText,documentation:e.documentation,detail:e.detail,range:a,kind:u(e.kind)};return e.textEdit&&(t.range=c(e.textEdit.range),t.insertText=e.textEdit.newText),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(f)),e.insertTextFormat===n.InsertTextFormat.Snippet&&(t.insertTextRules=monaco.languages.CompletionItemInsertTextRule.InsertAsSnippet),t}));return{isIncomplete:o.isIncomplete,suggestions:s}}}))},e}();function l(e){return"string"==typeof e?{value:e}:(t=e)&&"object"==typeof t&&"string"==typeof t.kind?"plaintext"===e.kind?{value:e.value.replace(/[\\`*_{}[\]()#+\-.!]/g,"\\$&")}:{value:e.value}:{value:"```"+e.language+"\n"+e.value+"\n```\n"};var t}function g(e){if(e)return Array.isArray(e)?e.map(l):[l(e)]}t.CompletionAdapter=d;var p=function(){function e(e){this._worker=e}return e.prototype.provideHover=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.doHover(r.toString(),a(t))})).then((function(e){if(e)return{range:c(e.range),contents:g(e.contents)}}))},e}();function h(e){var t=monaco.languages.SymbolKind;switch(e){case n.SymbolKind.File:return t.Array;case n.SymbolKind.Module:return t.Module;case n.SymbolKind.Namespace:return t.Namespace;case n.SymbolKind.Package:return t.Package;case n.SymbolKind.Class:return t.Class;case n.SymbolKind.Method:return t.Method;case n.SymbolKind.Property:return t.Property;case n.SymbolKind.Field:return t.Field;case n.SymbolKind.Constructor:return t.Constructor;case n.SymbolKind.Enum:return t.Enum;case n.SymbolKind.Interface:return t.Interface;case n.SymbolKind.Function:return t.Function;case n.SymbolKind.Variable:return t.Variable;case n.SymbolKind.Constant:return t.Constant;case n.SymbolKind.String:return t.String;case n.SymbolKind.Number:return t.Number;case n.SymbolKind.Boolean:return t.Boolean;case n.SymbolKind.Array:return t.Array}return t.Function}t.HoverAdapter=p;var m=function(){function e(e){this._worker=e}return e.prototype.provideDocumentSymbols=function(e,t){var n=e.uri;return this._worker(n).then((function(e){return e.findDocumentSymbols(n.toString())})).then((function(e){if(e)return e.map((function(e){return{name:e.name,detail:"",containerName:e.containerName,kind:h(e.kind),range:c(e.location.range),selectionRange:c(e.location.range),tags:[]}}))}))},e}();function v(e){return{tabSize:e.tabSize,insertSpaces:e.insertSpaces}}t.DocumentSymbolAdapter=m;var b=function(){function e(e){this._worker=e}return e.prototype.provideDocumentFormattingEdits=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.format(r.toString(),null,v(t)).then((function(e){if(e&&0!==e.length)return e.map(f)}))}))},e}();t.DocumentFormattingEditProvider=b;var y=function(){function e(e){this._worker=e}return e.prototype.provideDocumentRangeFormattingEdits=function(e,t,n,r){var o=e.uri;return this._worker(o).then((function(e){return e.format(o.toString(),s(t),v(n)).then((function(e){if(e&&0!==e.length)return e.map(f)}))}))},e}();t.DocumentRangeFormattingEditProvider=y;var k=function(){function e(e){this._worker=e}return e.prototype.provideDocumentColors=function(e,t){var n=e.uri;return this._worker(n).then((function(e){return e.findDocumentColors(n.toString())})).then((function(e){if(e)return e.map((function(e){return{color:e.color,range:c(e.range)}}))}))},e.prototype.provideColorPresentations=function(e,t,n){var r=e.uri;return this._worker(r).then((function(e){return e.getColorPresentations(r.toString(),t.color,s(t.range))})).then((function(e){if(e)return e.map((function(e){var t={label:e.label};return e.textEdit&&(t.textEdit=f(e.textEdit)),e.additionalTextEdits&&(t.additionalTextEdits=e.additionalTextEdits.map(f)),t}))}))},e}();t.DocumentColorAdapter=k;var C=function(){function e(e){this._worker=e}return e.prototype.provideFoldingRanges=function(e,t,r){var o=e.uri;return this._worker(o).then((function(e){return e.provideFoldingRanges(o.toString(),t)})).then((function(e){if(e)return e.map((function(e){var t={start:e.startLine+1,end:e.endLine+1};return void 0!==e.kind&&(t.kind=function(e){switch(e){case n.FoldingRangeKind.Comment:return monaco.languages.FoldingRangeKind.Comment;case n.FoldingRangeKind.Imports:return monaco.languages.FoldingRangeKind.Imports;case n.FoldingRangeKind.Region:return monaco.languages.FoldingRangeKind.Region}return}(e.kind)),t}))}))},e}();t.FoldingRangeAdapter=C})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/impl/scanner",["require","exports"],e)}((function(e,t){"use strict";function n(e){return 32===e||9===e||11===e||12===e||160===e||5760===e||e>=8192&&e<=8203||8239===e||8287===e||12288===e||65279===e}function r(e){return 10===e||13===e||8232===e||8233===e}function o(e){return e>=48&&e<=57}Object.defineProperty(t,"__esModule",{value:!0}),t.createScanner=function(e,t){void 0===t&&(t=!1);var i=0,a=e.length,s="",c=0,u=16,f=0,d=0,l=0,g=0,p=0;function h(t,n){for(var r=0,o=0;r<t||!n;){var a=e.charCodeAt(i);if(a>=48&&a<=57)o=16*o+a-48;else if(a>=65&&a<=70)o=16*o+a-65+10;else{if(!(a>=97&&a<=102))break;o=16*o+a-97+10}i++,r++}return r<t&&(o=-1),o}function m(){if(s="",p=0,c=i,d=f,g=l,i>=a)return c=a,u=17;var t=e.charCodeAt(i);if(n(t)){do{i++,s+=String.fromCharCode(t),t=e.charCodeAt(i)}while(n(t));return u=15}if(r(t))return i++,s+=String.fromCharCode(t),13===t&&10===e.charCodeAt(i)&&(i++,s+="\n"),f++,l=i,u=14;switch(t){case 123:return i++,u=1;case 125:return i++,u=2;case 91:return i++,u=3;case 93:return i++,u=4;case 58:return i++,u=6;case 44:return i++,u=5;case 34:return i++,s=function(){for(var t="",n=i;;){if(i>=a){t+=e.substring(n,i),p=2;break}var o=e.charCodeAt(i);if(34===o){t+=e.substring(n,i),i++;break}if(92!==o){if(o>=0&&o<=31){if(r(o)){t+=e.substring(n,i),p=2;break}p=6}i++}else{if(t+=e.substring(n,i),++i>=a){p=2;break}switch(o=e.charCodeAt(i++)){case 34:t+='"';break;case 92:t+="\\";break;case 47:t+="/";break;case 98:t+="\b";break;case 102:t+="\f";break;case 110:t+="\n";break;case 114:t+="\r";break;case 116:t+="\t";break;case 117:var s=h(4,!0);s>=0?t+=String.fromCharCode(s):p=4;break;default:p=5}n=i}}return t}(),u=10;case 47:var m=i-1;if(47===e.charCodeAt(i+1)){for(i+=2;i<a&&!r(e.charCodeAt(i));)i++;return s=e.substring(m,i),u=12}if(42===e.charCodeAt(i+1)){i+=2;for(var b=a-1,y=!1;i<b;){var k=e.charCodeAt(i);if(42===k&&47===e.charCodeAt(i+1)){i+=2,y=!0;break}i++,r(k)&&(13===k&&10===e.charCodeAt(i)&&i++,f++,l=i)}return y||(i++,p=1),s=e.substring(m,i),u=13}return s+=String.fromCharCode(t),i++,u=16;case 45:if(s+=String.fromCharCode(t),++i===a||!o(e.charCodeAt(i)))return u=16;case 48:case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return s+=function(){var t=i;if(48===e.charCodeAt(i))i++;else for(i++;i<e.length&&o(e.charCodeAt(i));)i++;if(i<e.length&&46===e.charCodeAt(i)){if(!(++i<e.length&&o(e.charCodeAt(i))))return p=3,e.substring(t,i);for(i++;i<e.length&&o(e.charCodeAt(i));)i++}var n=i;if(i<e.length&&(69===e.charCodeAt(i)||101===e.charCodeAt(i)))if((++i<e.length&&43===e.charCodeAt(i)||45===e.charCodeAt(i))&&i++,i<e.length&&o(e.charCodeAt(i))){for(i++;i<e.length&&o(e.charCodeAt(i));)i++;n=i}else p=3;return e.substring(t,n)}(),u=11;default:for(;i<a&&v(t);)i++,t=e.charCodeAt(i);if(c!==i){switch(s=e.substring(c,i)){case"true":return u=8;case"false":return u=9;case"null":return u=7}return u=16}return s+=String.fromCharCode(t),i++,u=16}}function v(e){if(n(e)||r(e))return!1;switch(e){case 125:case 93:case 123:case 91:case 34:case 58:case 44:case 47:return!1}return!0}return{setPosition:function(e){i=e,s="",c=0,u=16,p=0},getPosition:function(){return i},scan:t?function(){var e;do{e=m()}while(e>=12&&e<=15);return e}:m,getToken:function(){return u},getTokenValue:function(){return s},getTokenOffset:function(){return c},getTokenLength:function(){return i-c},getTokenStartLine:function(){return d},getTokenStartCharacter:function(){return c-g},getTokenError:function(){return p}}}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/impl/format",["require","exports","./scanner"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("./scanner");function r(e,t){for(var n="",r=0;r<t;r++)n+=e;return n}function o(e,t){return-1!=="\r\n".indexOf(e.charAt(t))}t.format=function(e,t,i){var a,s,c,u,f;if(t){for(u=t.offset,f=u+t.length,c=u;c>0&&!o(e,c-1);)c--;for(var d=f;d<e.length&&!o(e,d);)d++;s=e.substring(c,d),a=function(e,t){var n=0,r=0,o=t.tabSize||4;for(;n<e.length;){var i=e.charAt(n);if(" "===i)r++;else{if("\t"!==i)break;r+=o}n++}return Math.floor(r/o)}(s,i)}else s=e,a=0,c=0,u=0,f=e.length;var l,g=function(e,t){for(var n=0;n<t.length;n++){var r=t.charAt(n);if("\r"===r)return n+1<t.length&&"\n"===t.charAt(n+1)?"\r\n":"\r";if("\n"===r)return"\n"}return e&&e.eol||"\n"}(i,e),p=!1,h=0;l=i.insertSpaces?r(" ",i.tabSize||4):"\t";var m=n.createScanner(s,!1),v=!1;function b(){return g+r(l,a+h)}function y(){var e=m.scan();for(p=!1;15===e||14===e;)p=p||14===e,e=m.scan();return v=16===e||0!==m.getTokenError(),e}var k=[];function C(t,n,r){!v&&n<f&&r>u&&e.substring(n,r)!==t&&k.push({offset:n,length:r-n,content:t})}var E=y();if(17!==E){var _=m.getTokenOffset()+c;C(r(l,a),c,_)}for(;17!==E;){for(var T=m.getTokenOffset()+m.getTokenLength()+c,w=y(),x="";!p&&(12===w||13===w);){C(" ",T,m.getTokenOffset()+c),T=m.getTokenOffset()+m.getTokenLength()+c,x=12===w?b():"",w=y()}if(2===w)1!==E&&(h--,x=b());else if(4===w)3!==E&&(h--,x=b());else{switch(E){case 3:case 1:h++,x=b();break;case 5:case 12:x=b();break;case 13:x=p?b():" ";break;case 6:x=" ";break;case 10:if(6===w){x="";break}case 7:case 8:case 9:case 11:case 2:case 4:12===w||13===w?x=" ":5!==w&&17!==w&&(v=!0);break;case 16:v=!0}!p||12!==w&&13!==w||(x=b())}C(x,T,m.getTokenOffset()+c),E=w}return k},t.isEOL=o})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/impl/parser",["require","exports","./scanner"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n,r=e("./scanner");function o(e,t,n){return void 0===n&&(n=!1),t>=e.offset&&t<e.offset+e.length||n&&t===e.offset+e.length}function i(e,t,o){void 0===o&&(o=n.DEFAULT);var i=r.createScanner(e,!1);function a(e){return e?function(){return e(i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter())}:function(){return!0}}function s(e){return e?function(t){return e(t,i.getTokenOffset(),i.getTokenLength(),i.getTokenStartLine(),i.getTokenStartCharacter())}:function(){return!0}}var c=a(t.onObjectBegin),u=s(t.onObjectProperty),f=a(t.onObjectEnd),d=a(t.onArrayBegin),l=a(t.onArrayEnd),g=s(t.onLiteralValue),p=s(t.onSeparator),h=a(t.onComment),m=s(t.onError),v=o&&o.disallowComments,b=o&&o.allowTrailingComma;function y(){for(;;){var e=i.scan();switch(i.getTokenError()){case 4:k(14);break;case 5:k(15);break;case 3:k(13);break;case 1:v||k(11);break;case 2:k(12);break;case 6:k(16)}switch(e){case 12:case 13:v?k(10):h();break;case 16:k(1);break;case 15:case 14:break;default:return e}}}function k(e,t,n){if(void 0===t&&(t=[]),void 0===n&&(n=[]),m(e),t.length+n.length>0)for(var r=i.getToken();17!==r;){if(-1!==t.indexOf(r)){y();break}if(-1!==n.indexOf(r))break;r=y()}}function C(e){var t=i.getTokenValue();return e?g(t):u(t),y(),!0}function E(){switch(i.getToken()){case 3:return function(){d(),y();for(var e=!1;4!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){if(e||k(4,[],[]),p(","),y(),4===i.getToken()&&b)break}else e&&k(6,[],[]);E()||k(4,[],[4,5]),e=!0}return l(),4!==i.getToken()?k(8,[4],[]):y(),!0}();case 1:return function(){c(),y();for(var e=!1;2!==i.getToken()&&17!==i.getToken();){if(5===i.getToken()){if(e||k(4,[],[]),p(","),y(),2===i.getToken()&&b)break}else e&&k(6,[],[]);(10!==i.getToken()?(k(3,[],[2,5]),0):(C(!1),6===i.getToken()?(p(":"),y(),E()||k(4,[],[2,5])):k(5,[],[2,5]),1))||k(4,[],[2,5]),e=!0}return f(),2!==i.getToken()?k(7,[2],[]):y(),!0}();case 10:return C(!0);default:return function(){switch(i.getToken()){case 11:var e=0;try{"number"!=typeof(e=JSON.parse(i.getTokenValue()))&&(k(2),e=0)}catch(e){k(2)}g(e);break;case 7:g(null);break;case 8:g(!0);break;case 9:g(!1);break;default:return!1}return y(),!0}()}}return y(),17===i.getToken()||(E()?(17!==i.getToken()&&k(9,[],[]),!0):(k(4,[],[]),!1))}function a(e){switch(typeof e){case"boolean":return"boolean";case"number":return"number";case"string":return"string";default:return"null"}}!function(e){e.DEFAULT={allowTrailingComma:!1}}(n||(n={})),t.getLocation=function(e,t){var n=[],r=new Object,o=void 0,s={value:{},offset:0,length:0,type:"object",parent:void 0},c=!1;function u(e,t,n,r){s.value=e,s.offset=t,s.length=n,s.type=r,s.colonOffset=void 0,o=s}try{i(e,{onObjectBegin:function(e,i){if(t<=e)throw r;o=void 0,c=t>e,n.push("")},onObjectProperty:function(e,o,i){if(t<o)throw r;if(u(e,o,i,"property"),n[n.length-1]=e,t<=o+i)throw r},onObjectEnd:function(e,i){if(t<=e)throw r;o=void 0,n.pop()},onArrayBegin:function(e,i){if(t<=e)throw r;o=void 0,n.push(0)},onArrayEnd:function(e,i){if(t<=e)throw r;o=void 0,n.pop()},onLiteralValue:function(e,n,o){if(t<n)throw r;if(u(e,n,o,a(e)),t<=n+o)throw r},onSeparator:function(e,i,a){if(t<=i)throw r;if(":"===e&&o&&"property"===o.type)o.colonOffset=i,c=!1,o=void 0;else if(","===e){var s=n[n.length-1];"number"==typeof s?n[n.length-1]=s+1:(c=!0,n[n.length-1]=""),o=void 0}}})}catch(e){if(e!==r)throw e}return{path:n,previousNode:o,isAtPropertyKey:c,matches:function(e){for(var t=0,r=0;t<e.length&&r<n.length;r++)if(e[t]===n[r]||"*"===e[t])t++;else if("**"!==e[t])return!1;return t===e.length}}},t.parse=function(e,t,r){void 0===t&&(t=[]),void 0===r&&(r=n.DEFAULT);var o=null,a=[],s=[];function c(e){Array.isArray(a)?a.push(e):o&&(a[o]=e)}return i(e,{onObjectBegin:function(){var e={};c(e),s.push(a),a=e,o=null},onObjectProperty:function(e){o=e},onObjectEnd:function(){a=s.pop()},onArrayBegin:function(){var e=[];c(e),s.push(a),a=e,o=null},onArrayEnd:function(){a=s.pop()},onLiteralValue:c,onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},r),a[0]},t.parseTree=function(e,t,r){void 0===t&&(t=[]),void 0===r&&(r=n.DEFAULT);var o={type:"array",offset:-1,length:-1,children:[],parent:void 0};function s(e){"property"===o.type&&(o.length=e-o.offset,o=o.parent)}function c(e){return o.children.push(e),e}i(e,{onObjectBegin:function(e){o=c({type:"object",offset:e,length:-1,parent:o,children:[]})},onObjectProperty:function(e,t,n){(o=c({type:"property",offset:t,length:-1,parent:o,children:[]})).children.push({type:"string",value:e,offset:t,length:n,parent:o})},onObjectEnd:function(e,t){o.length=e+t-o.offset,o=o.parent,s(e+t)},onArrayBegin:function(e,t){o=c({type:"array",offset:e,length:-1,parent:o,children:[]})},onArrayEnd:function(e,t){o.length=e+t-o.offset,o=o.parent,s(e+t)},onLiteralValue:function(e,t,n){c({type:a(e),offset:t,length:n,parent:o,value:e}),s(t+n)},onSeparator:function(e,t,n){"property"===o.type&&(":"===e?o.colonOffset=t:","===e&&s(t))},onError:function(e,n,r){t.push({error:e,offset:n,length:r})}},r);var u=o.children[0];return u&&delete u.parent,u},t.findNodeAtLocation=function(e,t){if(e){for(var n=e,r=0,o=t;r<o.length;r++){var i=o[r];if("string"==typeof i){if("object"!==n.type||!Array.isArray(n.children))return;for(var a=!1,s=0,c=n.children;s<c.length;s++){var u=c[s];if(Array.isArray(u.children)&&u.children[0].value===i){n=u.children[1],a=!0;break}}if(!a)return}else{var f=i;if("array"!==n.type||f<0||!Array.isArray(n.children)||f>=n.children.length)return;n=n.children[f]}}return n}},t.getNodePath=function e(t){if(!t.parent||!t.parent.children)return[];var n=e(t.parent);if("property"===t.parent.type){var r=t.parent.children[0].value;n.push(r)}else if("array"===t.parent.type){var o=t.parent.children.indexOf(t);-1!==o&&n.push(o)}return n},t.getNodeValue=function e(t){switch(t.type){case"array":return t.children.map(e);case"object":for(var n=Object.create(null),r=0,o=t.children;r<o.length;r++){var i=o[r],a=i.children[1];a&&(n[i.children[0].value]=e(a))}return n;case"null":case"string":case"number":case"boolean":return t.value;default:return}},t.contains=o,t.findNodeAtOffset=function e(t,n,r){if(void 0===r&&(r=!1),o(t,n,r)){var i=t.children;if(Array.isArray(i))for(var a=0;a<i.length&&i[a].offset<=n;a++){var s=e(i[a],n,r);if(s)return s}return t}},t.visit=i,t.stripComments=function(e,t){var n,o,i=r.createScanner(e),a=[],s=0;do{switch(o=i.getPosition(),n=i.scan()){case 12:case 13:case 17:s!==o&&a.push(e.substring(s,o)),void 0!==t&&a.push(i.getTokenValue().replace(/[^\r\n]/g,t)),s=i.getPosition()}}while(17!==n);return a.join("")}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/impl/edit",["require","exports","./format","./parser"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("./format"),r=e("./parser");function o(e,t,n,o,a){for(var s,c=t.slice(),u=r.parseTree(e,[]),f=void 0,d=void 0;c.length>0&&(d=c.pop(),void 0===(f=r.findNodeAtLocation(u,c))&&void 0!==n);)"string"==typeof d?((s={})[d]=n,n=s):n=[n];if(f){if("object"===f.type&&"string"==typeof d&&Array.isArray(f.children)){var l=r.findNodeAtLocation(f,[d]);if(void 0!==l){if(void 0===n){if(!l.parent)throw new Error("Malformed AST");var g=f.children.indexOf(l.parent),p=void 0,h=l.parent.offset+l.parent.length;if(g>0)p=(C=f.children[g-1]).offset+C.length;else if(p=f.offset+1,f.children.length>1)h=f.children[1].offset;return i(e,{offset:p,length:h-p,content:""},o)}return i(e,{offset:l.offset,length:l.length,content:JSON.stringify(n)},o)}if(void 0===n)return[];var m=JSON.stringify(d)+": "+JSON.stringify(n),v=a?a(f.children.map((function(e){return e.children[0].value}))):f.children.length,b=void 0;return i(e,b=v>0?{offset:(C=f.children[v-1]).offset+C.length,length:0,content:","+m}:0===f.children.length?{offset:f.offset+1,length:0,content:m}:{offset:f.offset+1,length:0,content:m+","},o)}if("array"===f.type&&"number"==typeof d&&Array.isArray(f.children)){if(-1===d){m=""+JSON.stringify(n),b=void 0;if(0===f.children.length)b={offset:f.offset+1,length:0,content:m};else b={offset:(C=f.children[f.children.length-1]).offset+C.length,length:0,content:","+m};return i(e,b,o)}if(void 0===n&&f.children.length>=0){var y=d,k=f.children[y];b=void 0;if(1===f.children.length)b={offset:f.offset+1,length:f.length-2,content:""};else if(f.children.length-1===y){var C,E=(C=f.children[y-1]).offset+C.length;b={offset:E,length:f.offset+f.length-2-E,content:""}}else b={offset:k.offset,length:f.children[y+1].offset-k.offset,content:""};return i(e,b,o)}throw new Error("Array modification not supported yet")}throw new Error("Can not add "+("number"!=typeof d?"index":"property")+" to parent of type "+f.type)}if(void 0===n)throw new Error("Can not delete in empty document");return i(e,{offset:u?u.offset:0,length:u?u.length:0,content:JSON.stringify(n)},o)}function i(e,t,r){var o=a(e,t),i=t.offset,s=t.offset+t.content.length;if(0===t.length||0===t.content.length){for(;i>0&&!n.isEOL(o,i-1);)i--;for(;s<o.length&&!n.isEOL(o,s);)s++}for(var c=n.format(o,{offset:i,length:s-i},r),u=c.length-1;u>=0;u--){var f=c[u];o=a(o,f),i=Math.min(i,f.offset),s=Math.max(s,f.offset+f.length),s+=f.content.length-f.length}return[{offset:i,length:e.length-(o.length-s)-i,content:o.substring(i,s)}]}function a(e,t){return e.substring(0,t.offset)+t.content+e.substring(t.offset+t.length)}t.removeProperty=function(e,t,n){return o(e,t,void 0,n)},t.setProperty=o,t.applyEdit=a,t.isWS=function(e,t){return-1!=="\r\n \t".indexOf(e.charAt(t))}})),function(e){if("object"==typeof module&&"object"==typeof module.exports){var t=e(require,exports);void 0!==t&&(module.exports=t)}else"function"==typeof define&&define.amd&&define("jsonc-parser/main",["require","exports","./impl/format","./impl/edit","./impl/scanner","./impl/parser"],e)}((function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var n=e("./impl/format"),r=e("./impl/edit"),o=e("./impl/scanner"),i=e("./impl/parser");t.createScanner=o.createScanner,t.getLocation=i.getLocation,t.parse=i.parse,t.parseTree=i.parseTree,t.findNodeAtLocation=i.findNodeAtLocation,t.findNodeAtOffset=i.findNodeAtOffset,t.getNodePath=i.getNodePath,t.getNodeValue=i.getNodeValue,t.visit=i.visit,t.stripComments=i.stripComments,t.printParseErrorCode=function(e){switch(e){case 1:return"InvalidSymbol";case 2:return"InvalidNumberFormat";case 3:return"PropertyNameExpected";case 4:return"ValueExpected";case 5:return"ColonExpected";case 6:return"CommaExpected";case 7:return"CloseBraceExpected";case 8:return"CloseBracketExpected";case 9:return"EndOfFileExpected";case 10:return"InvalidCommentToken";case 11:return"UnexpectedEndOfComment";case 12:return"UnexpectedEndOfString";case 13:return"UnexpectedEndOfNumber";case 14:return"InvalidUnicode";case 15:return"InvalidEscapeCharacter";case 16:return"InvalidCharacter"}return"<unknown ParseErrorCode>"},t.format=function(e,t,r){return n.format(e,t,r)},t.modify=function(e,t,n,o){return r.setProperty(e,t,n,o.formattingOptions,o.getInsertionIndex)},t.applyEdits=function(e,t){for(var n=t.length-1;n>=0;n--)e=r.applyEdit(e,t[n]);return e}})),define("jsonc-parser",["jsonc-parser/main"],(function(e){return e})),define("vs/language/json/tokenization",["require","exports","jsonc-parser"],(function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.createTokenizationSupport=function(e){return{getInitialState:function(){return new r(null,null,!1)},tokenize:function(o,i,a,s){return function(e,o,i,a,s){void 0===a&&(a=0);var c=0,u=!1;switch(i.scanError){case 2:o='"'+o,c=1;break;case 1:o="/*"+o,c=2}var f,d,l=n.createScanner(o),g=i.lastWasColon;d={tokens:[],endState:i.clone()};for(;;){var p=a+l.getPosition(),h="";if(17===(f=l.scan()))break;if(p===a+l.getPosition())throw new Error("Scanner did not advance, next 3 characters are: "+o.substr(l.getPosition(),3));switch(u&&(p-=c),u=c>0,f){case 1:case 2:h=t.TOKEN_DELIM_OBJECT,g=!1;break;case 3:case 4:h=t.TOKEN_DELIM_ARRAY,g=!1;break;case 6:h=t.TOKEN_DELIM_COLON,g=!0;break;case 5:h=t.TOKEN_DELIM_COMMA,g=!1;break;case 8:case 9:h=t.TOKEN_VALUE_BOOLEAN,g=!1;break;case 7:h=t.TOKEN_VALUE_NULL,g=!1;break;case 10:h=g?t.TOKEN_VALUE_STRING:t.TOKEN_PROPERTY_NAME,g=!1;break;case 11:h=t.TOKEN_VALUE_NUMBER,g=!1}if(e)switch(f){case 12:h=t.TOKEN_COMMENT_LINE;break;case 13:h=t.TOKEN_COMMENT_BLOCK}d.endState=new r(i.getStateData(),l.getTokenError(),g),d.tokens.push({startIndex:p,scopes:h})}return d}(e,o,i,a)}}},t.TOKEN_DELIM_OBJECT="delimiter.bracket.json",t.TOKEN_DELIM_ARRAY="delimiter.array.json",t.TOKEN_DELIM_COLON="delimiter.colon.json",t.TOKEN_DELIM_COMMA="delimiter.comma.json",t.TOKEN_VALUE_BOOLEAN="keyword.json",t.TOKEN_VALUE_NULL="keyword.json",t.TOKEN_VALUE_STRING="string.value.json",t.TOKEN_VALUE_NUMBER="number.json",t.TOKEN_PROPERTY_NAME="string.key.json",t.TOKEN_COMMENT_BLOCK="comment.block.json",t.TOKEN_COMMENT_LINE="comment.line.json";var r=function(){function e(e,t,n){this._state=e,this.scanError=t,this.lastWasColon=n}return e.prototype.clone=function(){return new e(this._state,this.scanError,this.lastWasColon)},e.prototype.equals=function(t){return t===this||!!(t&&t instanceof e)&&(this.scanError===t.scanError&&this.lastWasColon===t.lastWasColon)},e.prototype.getStateData=function(){return this._state},e.prototype.setStateData=function(e){this._state=e},e}()})),define("vs/language/json/jsonMode",["require","exports","./workerManager","./languageFeatures","./tokenization"],(function(e,t,n,r,o){"use strict";function i(e){return{dispose:function(){return a(e)}}}function a(e){for(;e.length;)e.pop().dispose()}Object.defineProperty(t,"__esModule",{value:!0}),t.setupMode=function(e){var t=[],c=[],u=new n.WorkerManager(e);t.push(u);var f=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return u.getLanguageServiceWorker.apply(u,e)};function d(){var t=e.languageId,n=e.modeConfiguration;a(c),n.documentFormattingEdits&&c.push(monaco.languages.registerDocumentFormattingEditProvider(t,new r.DocumentFormattingEditProvider(f))),n.documentRangeFormattingEdits&&c.push(monaco.languages.registerDocumentRangeFormattingEditProvider(t,new r.DocumentRangeFormattingEditProvider(f))),n.completionItems&&c.push(monaco.languages.registerCompletionItemProvider(t,new r.CompletionAdapter(f))),n.hovers&&c.push(monaco.languages.registerHoverProvider(t,new r.HoverAdapter(f))),n.documentSymbols&&c.push(monaco.languages.registerDocumentSymbolProvider(t,new r.DocumentSymbolAdapter(f))),n.tokens&&c.push(monaco.languages.setTokensProvider(t,o.createTokenizationSupport(!0))),n.colors&&c.push(monaco.languages.registerColorProvider(t,new r.DocumentColorAdapter(f))),n.foldingRanges&&c.push(monaco.languages.registerFoldingRangeProvider(t,new r.FoldingRangeAdapter(f))),n.diagnostics&&c.push(new r.DiagnosticsAdapter(t,f,e))}d(),t.push(monaco.languages.setLanguageConfiguration(e.languageId,s));var l=e.modeConfiguration;return e.onDidChange((function(e){e.modeConfiguration!==l&&(l=e.modeConfiguration,d())})),t.push(i(c)),i(t)};var s={wordPattern:/(-?\d*\.\d\w*)|([^\[\{\]\}\:\"\,\s]+)/g,comments:{lineComment:"//",blockComment:["/*","*/"]},brackets:[["{","}"],["[","]"]],autoClosingPairs:[{open:"{",close:"}",notIn:["string"]},{open:"[",close:"]",notIn:["string"]},{open:'"',close:'"',notIn:["string"]}]}}));