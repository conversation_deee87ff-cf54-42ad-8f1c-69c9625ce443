{"_from": "@angular/platform-browser@7.2.4", "_id": "@angular/platform-browser@7.2.4", "_inBundle": false, "_integrity": "sha512-Klt8aKR5SP9bqfMfpSY5vQOY7AQEs8JGuZOk5Bfc2dUtYT2IEIvK2IqO8v2rcFRVO13HOPUxl328efyHqLgI7g==", "_location": "/@angular/platform-browser", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/platform-browser@7.2.4", "name": "@angular/platform-browser", "escapedName": "@angular%2fplatform-browser", "scope": "@angular", "rawSpec": "7.2.4", "saveSpec": null, "fetchSpec": "7.2.4"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@angular/platform-browser/-/platform-browser-7.2.4.tgz", "_shasum": "2cf5305878d0620d6b8c02eff00ac3ca8dbc5970", "_spec": "@angular/platform-browser@7.2.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "description": "Angular - library for using Angular in a web browser", "es2015": "./fesm2015/platform-browser.js", "esm2015": "./esm2015/platform-browser.js", "esm5": "./esm5/platform-browser.js", "fesm2015": "./fesm2015/platform-browser.js", "fesm5": "./fesm5/platform-browser.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "main": "./bundles/platform-browser.umd.js", "module": "./fesm5/platform-browser.js", "name": "@angular/platform-browser", "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/http", "@angular/platform-server", "@angular/platform-webworker", "@angular/platform-webworker-dynamic", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/service-worker"]}, "peerDependencies": {"@angular/core": "7.2.4", "@angular/common": "7.2.4"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "sideEffects": false, "typings": "./platform-browser.d.ts", "version": "7.2.4"}