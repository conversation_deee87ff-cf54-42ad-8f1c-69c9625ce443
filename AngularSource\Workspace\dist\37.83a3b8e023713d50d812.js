(window.webpackJsonp=window.webpackJsonp||[]).push([[37],{OD2O:function(t,e,l){"use strict";l.r(e);var n=l("CcnG"),i=l("mrSG"),a=l("447K"),u=l("ZYCi"),o=function(t){function e(e,l){var n=t.call(this,l,e)||this;return n.commonService=e,n.element=l,n.jsonReader=new a.L,n.inputData=new a.G(n.commonService),n.baseURL=a.Wb.getBaseURL(),n.actionMethod="",n.actionPath="",n.requestParams=[],n.invalidComms="",n.screenName=null,n.versionNumber="1.0",n.errorLocation=0,n.calledFrom=null,n.moduleId="Predict",n.swtAlert=new a.bb(e),n}return i.d(e,t),e.prototype.ngOnInit=function(){try{this.functionalGrpCombo.toolTip=a.Wb.getPredictMessage("tooltip.functGrp",null),this.functionalGrpLabel.text=a.Wb.getPredictMessage("functGrp.id",null),this.restrictionInput.toolTip=a.Wb.getPredictMessage("tooltip.restriction",null),this.restrictionLabel.text=a.Wb.getPredictMessage("restriction.id",null),this.attributeLabel.text=a.Wb.getPredictMessage("attribute.id",null),this.attributeCombo.toolTip=a.Wb.getPredictMessage("tooltip.attribute",null),this.displayOrderLabel.text=a.Wb.getPredictMessage("displayOrder.id",null),this.granTotalGroup.toolTip=a.Wb.getPredictMessage("attributeusagesummaryadd.grandTotal.tooltip",null),this.granTotalLabel.text=a.Wb.getPredictMessage("label.attributeusagesummaryadd.grandTotal",null),this.addGT.label=a.Wb.getPredictMessage("attributeusagesummaryadd.grandTotal.add",null),this.subGT.label=a.Wb.getPredictMessage("attributeusagesummaryadd.grandTotal.substract",null),this.noGT.label=a.Wb.getPredictMessage("attributeusagesummaryadd.grandTotal.noContribution",null),this.saveButton.label=a.Wb.getPredictMessage("button.save",null),this.saveButton.toolTip=a.Wb.getPredictMessage("tooltip.save",null),this.cancelButton.label=a.Wb.getPredictMessage("button.cancel",null),this.cancelButton.toolTip=a.Wb.getPredictMessage("tooltip.cancel",null),this.saveButton.visible=!0,"add"==this.screenName?(this.saveButton.enabled=!1,this.saveButton.buttonMode=!1):"change"==this.screenName||"view"==this.screenName&&(this.saveButton.visible=!1)}catch(t){a.Wb.logError(t,this.moduleId,"AttributeUsageAdd","ngOnInit",this.errorLocation)}},e.prototype.onLoad=function(){var t=this;try{this.requestParams=[],this.requestParams.attributeId=a.x.call("eval","attributeId"),this.requestParams.functionalGrp=a.x.call("eval","functionalGrp"),this.calledFrom=a.x.call("eval","calledFrom"),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="accountAttribute.do?",this.actionMethod="method=displayAttributeUsageSummaryAdd",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){a.Wb.logError(e,this.moduleId,"AttributeUsageAdd","onLoad",this.errorLocation)}},e.prototype.inputDataResult=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),JSON.stringify(this.lastReceivedJSON)!==JSON.stringify(this.prevReceivedJSON))if(this.jsonReader.getRequestReplyStatus()){this.functionalGrpCombo.setComboData(this.jsonReader.getSelects(),!1),this.selectedFunctGrp.text=this.functionalGrpCombo.selectedItem.value,this.attributeCombo.setComboData(this.jsonReader.getSelects(),!1),this.selectedAttribute.text=this.attributeCombo.selectedItem.value;var e=this.jsonReader.getScreenAttributes().restriction;this.setRestrictionText(e),null==this.attributeCombo.selectedItem.content?(this.swtAlert.error(a.Wb.getPredictMessage("attributeusagesummaryadd.noAttribute",null),a.Wb.getPredictMessage("screen.error",null)),this.saveButton.enabled=!1):(this.saveButton.enabled=!0,this.granTotalGroup.selectedValue="PLUS"),"N"!==e?(this.granTotalGroup.enabled=!1,this.granTotalGroup.selectedValue="NONE"):this.granTotalGroup.enabled=!0,this.displayOrderInput.text=this.jsonReader.getScreenAttributes().displayOrder,this.granTotalGroup.selectedValue=this.jsonReader.getScreenAttributes().grandTotal,"change"==this.calledFrom&&(this.functionalGrpCombo.enabled=!1,this.attributeCombo.enabled=!1,this.restrictionInput.enabled=!1),this.prevReceivedJSON=this.lastReceivedJSON}else this.swtAlert.error(a.Wb.getPredictMessage("label.errorContactSystemAdmin",null)+"\n"+this.jsonReader.getRequestReplyMessage(),a.Wb.getPredictMessage("screen.error",null))}catch(l){console.log("error:   ",l),a.Wb.logError(l,this.moduleId,"AttributeUsageAdd","inputDataResult",this.errorLocation)}},e.prototype.startOfComms=function(){try{this.loadingImage.setVisible(!0)}catch(t){a.Wb.logError(t,this.moduleId,"AttributeDefinition","startOfComms",this.errorLocation)}},e.prototype.endOfComms=function(){try{this.loadingImage.setVisible(!1)}catch(t){a.Wb.logError(t,this.moduleId,"AttributeDefinition","endOfComms",this.errorLocation)}},e.prototype.inputDataFault=function(t){this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.error(this.invalidComms)},e.prototype.setRestrictionText=function(t){this.restrictionInput.text="T"===t?a.Wb.getPredictMessage("type.text",null):"N"===t?a.Wb.getPredictMessage("type.numeric",null):"D"===t?a.Wb.getPredictMessage("type.date",null):""},e.prototype.keyDownEventHandler=function(t){try{var e=Object(a.ic.getFocus()).name;t.keyCode==a.N.ENTER&&("saveButton"==e?this.addChangeClickHandler():"closeButton"==e&&this.closeHandler())}catch(l){console.log(l,this.moduleId,"AttributeUsageAdd","keyDownEventHandler")}},e.prototype.addChangeClickHandler=function(){var t=this;this.requestParams.funcGroup=this.functionalGrpCombo.selectedItem.content,this.requestParams.attributeId=this.attributeCombo.selectedItem.content,this.requestParams.displayOrder=this.displayOrderInput.text,this.requestParams.calledFrom=this.calledFrom,this.requestParams.grandTotal=this.granTotalGroup.selectedValue,this.inputData.cbResult=function(e){t.saveDataResult(e)},this.actionMethod="method=saveAttributeUsageSummary",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.saveDataResult=function(t){if(this.inputData.isBusy())this.inputData.cbStop();else{this.lastReceivedJSON=t;var e=new a.L;e.setInputJSON(this.lastReceivedJSON),"Data fetch OK"==e.getRequestReplyMessage()?window.opener.instanceElement&&(window.opener.instanceElement.updateData(),this.closeHandler()):"errors.DataIntegrityViolationExceptioninAdd"==e.getRequestReplyMessage()&&this.swtAlert.warning(a.Wb.getPredictMessage("errors.DataIntegrityViolationExceptioninAdd",null))}},e.prototype.functGrpCombo=function(t){this.selectedAttribute.text=this.attributeCombo.selectedItem.value,this.updateData()},e.prototype.updateData=function(){var t=this;this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.requestParams.functionalGrp=this.functionalGrpCombo.selectedItem.content,this.actionMethod="method=displayAttributeUsageSummaryAdd",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.changeTotalGroup=function(){},e.prototype.closeHandler=function(){a.x.call("closeWindow")},e.prototype.doHelp=function(){try{a.x.call("help")}catch(t){a.Wb.logError(t,this.moduleId,"AttributeUsageAdd","doHelp",this.errorLocation)}},e}(a.yb),r=[{path:"",component:o}],d=(u.l.forChild(r),function(){return function(){}}()),s=l("pMnS"),b=l("RChO"),c=l("t6HQ"),h=l("WFGK"),g=l("5FqG"),p=l("Ip0R"),m=l("gIcY"),w=l("t/Na"),R=l("sE5F"),f=l("OzfB"),I=l("T7CS"),v=l("S7LP"),y=l("6aHO"),G=l("WzUx"),A=l("A7o+"),C=l("zCE2"),T=l("Jg5P"),S=l("3R0m"),D=l("hhbb"),L=l("5rxC"),x=l("Fzqc"),J=l("21Lb"),B=l("hUWP"),O=l("3pJQ"),k=l("V9q+"),P=l("VDKW"),M=l("kXfT"),W=l("BGbe");l.d(e,"AttributeUsageAddModuleNgFactory",function(){return N}),l.d(e,"RenderType_AttributeUsageAdd",function(){return U}),l.d(e,"View_AttributeUsageAdd_0",function(){return H}),l.d(e,"View_AttributeUsageAdd_Host_0",function(){return F}),l.d(e,"AttributeUsageAddNgFactory",function(){return E});var N=n.Gb(d,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[s.a,b.a,c.a,h.a,g.Cb,g.Pb,g.r,g.rc,g.s,g.Ab,g.Bb,g.Db,g.qd,g.Hb,g.k,g.Ib,g.Nb,g.Ub,g.yb,g.Jb,g.v,g.A,g.e,g.c,g.g,g.d,g.Kb,g.f,g.ec,g.Wb,g.bc,g.ac,g.sc,g.fc,g.lc,g.jc,g.Eb,g.Fb,g.mc,g.Lb,g.nc,g.Mb,g.dc,g.Rb,g.b,g.ic,g.Yb,g.Sb,g.kc,g.y,g.Qb,g.cc,g.hc,g.pc,g.oc,g.xb,g.p,g.q,g.o,g.h,g.j,g.w,g.Zb,g.i,g.m,g.Vb,g.Ob,g.Gb,g.Xb,g.t,g.tc,g.zb,g.n,g.qc,g.a,g.z,g.rd,g.sd,g.x,g.td,g.gc,g.l,g.u,g.ud,g.Tb,E]],[3,n.n],n.J]),n.Rb(4608,p.m,p.l,[n.F,[2,p.u]]),n.Rb(4608,m.c,m.c,[]),n.Rb(4608,m.p,m.p,[]),n.Rb(4608,w.j,w.p,[p.c,n.O,w.n]),n.Rb(4608,w.q,w.q,[w.j,w.o]),n.Rb(5120,w.a,function(t){return[t,new a.tb]},[w.q]),n.Rb(4608,w.m,w.m,[]),n.Rb(6144,w.k,null,[w.m]),n.Rb(4608,w.i,w.i,[w.k]),n.Rb(6144,w.b,null,[w.i]),n.Rb(4608,w.f,w.l,[w.b,n.B]),n.Rb(4608,w.c,w.c,[w.f]),n.Rb(4608,R.c,R.c,[]),n.Rb(4608,R.g,R.b,[]),n.Rb(5120,R.i,R.j,[]),n.Rb(4608,R.h,R.h,[R.c,R.g,R.i]),n.Rb(4608,R.f,R.a,[]),n.Rb(5120,R.d,R.k,[R.h,R.f]),n.Rb(5120,n.b,function(t,e){return[f.j(t,e)]},[p.c,n.O]),n.Rb(4608,I.a,I.a,[]),n.Rb(4608,v.a,v.a,[]),n.Rb(4608,y.a,y.a,[n.n,n.L,n.B,v.a,n.g]),n.Rb(4608,G.c,G.c,[n.n,n.g,n.B]),n.Rb(4608,G.e,G.e,[G.c]),n.Rb(4608,A.l,A.l,[]),n.Rb(4608,A.h,A.g,[]),n.Rb(4608,A.c,A.f,[]),n.Rb(4608,A.j,A.d,[]),n.Rb(4608,A.b,A.a,[]),n.Rb(4608,A.k,A.k,[A.l,A.h,A.c,A.j,A.b,A.m,A.n]),n.Rb(4608,G.i,G.i,[[2,A.k]]),n.Rb(4608,G.r,G.r,[G.L,[2,A.k],G.i]),n.Rb(4608,G.t,G.t,[]),n.Rb(4608,G.w,G.w,[]),n.Rb(1073742336,u.l,u.l,[[2,u.r],[2,u.k]]),n.Rb(1073742336,p.b,p.b,[]),n.Rb(1073742336,m.n,m.n,[]),n.Rb(1073742336,m.l,m.l,[]),n.Rb(1073742336,C.a,C.a,[]),n.Rb(1073742336,T.a,T.a,[]),n.Rb(1073742336,m.e,m.e,[]),n.Rb(1073742336,S.a,S.a,[]),n.Rb(1073742336,A.i,A.i,[]),n.Rb(1073742336,G.b,G.b,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,w.d,w.d,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,D.b,D.b,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,f.c,f.c,[]),n.Rb(1073742336,x.a,x.a,[]),n.Rb(1073742336,J.d,J.d,[]),n.Rb(1073742336,B.c,B.c,[]),n.Rb(1073742336,O.a,O.a,[]),n.Rb(1073742336,k.a,k.a,[[2,f.g],n.O]),n.Rb(1073742336,P.b,P.b,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,W.b,W.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,d,d,[]),n.Rb(256,w.n,"XSRF-TOKEN",[]),n.Rb(256,w.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,A.m,void 0,[]),n.Rb(256,A.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,u.i,function(){return[[{path:"",component:o}]]},[])])}),_=[[""]],U=n.Hb({encapsulation:0,styles:_,data:{}});function H(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{restrictionInput:0}),n.Zb(402653184,3,{granTotalGroup:0}),n.Zb(402653184,4,{addGT:0}),n.Zb(402653184,5,{subGT:0}),n.Zb(402653184,6,{noGT:0}),n.Zb(402653184,7,{functionalGrpCombo:0}),n.Zb(402653184,8,{attributeCombo:0}),n.Zb(402653184,9,{functionalGrpLabel:0}),n.Zb(402653184,10,{selectedFunctGrp:0}),n.Zb(402653184,11,{selectedAttribute:0}),n.Zb(402653184,12,{restrictionLabel:0}),n.Zb(402653184,13,{attributeLabel:0}),n.Zb(402653184,14,{displayOrderLabel:0}),n.Zb(402653184,15,{granTotalLabel:0}),n.Zb(402653184,16,{displayOrderInput:0}),n.Zb(402653184,17,{saveButton:0}),n.Zb(402653184,18,{cancelButton:0}),n.Zb(402653184,19,{loadingImage:0}),(t()(),n.Jb(19,0,null,null,90,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,l){var n=!0,i=t.component;"creationComplete"===e&&(n=!1!==i.onLoad()&&n);return n},g.ad,g.hb)),n.Ib(20,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(21,0,null,0,88,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(22,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(23,0,null,0,70,"SwtCanvas",[["height","80%"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(24,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(25,0,null,0,68,"VBox",[["height","100%"],["width","100%"]],null,null,null,g.od,g.vb)),n.Ib(26,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(27,0,null,0,66,"Grid",[["height","100%"],["width","100%"]],null,null,null,g.Cc,g.H)),n.Ib(28,4440064,null,0,a.z,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(29,0,null,0,13,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(30,4440064,null,0,a.B,[n.r,a.i],null,null),(t()(),n.Jb(31,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),n.Ib(32,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(33,0,null,0,1,"SwtLabel",[["text","Functional Group"]],null,null,null,g.Yc,g.fb)),n.Ib(34,4440064,[[9,4],["functionalGrpLabel",4]],0,a.vb,[n.r,a.i],{text:[0,"text"]},null),(t()(),n.Jb(35,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,g.Ac,g.I)),n.Ib(36,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(37,0,null,0,1,"SwtComboBox",[["dataLabel","functionalGrpList"],["id","functionalGrpCombo"],["width","227"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var i=!0,a=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,38).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==a.functGrpCombo(l)&&i);return i},g.Pc,g.W)),n.Ib(38,4440064,[[7,4],["functionalGrpCombo",4]],0,a.gb,[n.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(39,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),n.Ib(40,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(41,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["text",""],["textAlign","left"]],null,null,null,g.Yc,g.fb)),n.Ib(42,4440064,[[10,4],["selectedFunctGrp",4]],0,a.vb,[n.r,a.i],{textAlign:[0,"textAlign"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(43,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(44,4440064,null,0,a.B,[n.r,a.i],null,null),(t()(),n.Jb(45,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),n.Ib(46,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(47,0,null,0,1,"SwtLabel",[["text","Restriction"]],null,null,null,g.Yc,g.fb)),n.Ib(48,4440064,[[12,4],["restrictionLabel",4]],0,a.vb,[n.r,a.i],{text:[0,"text"]},null),(t()(),n.Jb(49,0,null,0,3,"GridItem",[["width","80%"]],null,null,null,g.Ac,g.I)),n.Ib(50,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(51,0,null,0,1,"SwtTextInput",[["editable","false"],["id","restrictionInput"],["width","75"]],null,null,null,g.kd,g.sb)),n.Ib(52,4440064,[[2,4],["restrictionInput",4]],0,a.Rb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],editable:[2,"editable"]},null),(t()(),n.Jb(53,0,null,0,13,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(54,4440064,null,0,a.B,[n.r,a.i],null,null),(t()(),n.Jb(55,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),n.Ib(56,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(57,0,null,0,1,"SwtLabel",[["text","Attribute"]],null,null,null,g.Yc,g.fb)),n.Ib(58,4440064,[[13,4],["attributeLabel",4]],0,a.vb,[n.r,a.i],{text:[0,"text"]},null),(t()(),n.Jb(59,0,null,0,3,"GridItem",[["width","40%"]],null,null,null,g.Ac,g.I)),n.Ib(60,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(61,0,null,0,1,"SwtComboBox",[["dataLabel","acctAttributHDRList"],["id","attributeCombo"],["width","227"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,l){var i=!0,a=t.component;"window:mousewheel"===e&&(i=!1!==n.Tb(t,62).mouseWeelEventHandler(l.target)&&i);"change"===e&&(i=!1!==a.functGrpCombo(l)&&i);return i},g.Pc,g.W)),n.Ib(62,4440064,[[8,4],["attributeCombo",4]],0,a.gb,[n.r,a.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(t()(),n.Jb(63,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),n.Ib(64,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(65,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["text",""],["textAlign","left"]],null,null,null,g.Yc,g.fb)),n.Ib(66,4440064,[[11,4],["selectedAttribute",4]],0,a.vb,[n.r,a.i],{textAlign:[0,"textAlign"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(t()(),n.Jb(67,0,null,0,9,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(68,4440064,null,0,a.B,[n.r,a.i],null,null),(t()(),n.Jb(69,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),n.Ib(70,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(71,0,null,0,1,"SwtLabel",[["text","Display Order"]],null,null,null,g.Yc,g.fb)),n.Ib(72,4440064,[[14,4],["displayOrderLabel",4]],0,a.vb,[n.r,a.i],{text:[0,"text"]},null),(t()(),n.Jb(73,0,null,0,3,"GridItem",[["width","80%"]],null,null,null,g.Ac,g.I)),n.Ib(74,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(75,0,null,0,1,"SwtNumericInput",[["id","displayOrderInput"],["textAlign","right"],["width","68"]],null,null,null,g.cd,g.jb)),n.Ib(76,4440064,[[16,4],["displayOrderInput",4]],0,a.Ab,[n.r,a.i],{id:[0,"id"],textAlign:[1,"textAlign"],width:[2,"width"]},null),(t()(),n.Jb(77,0,null,0,16,"GridRow",[],null,null,null,g.Bc,g.J)),n.Ib(78,4440064,null,0,a.B,[n.r,a.i],null,null),(t()(),n.Jb(79,0,null,0,3,"GridItem",[["width","20%"]],null,null,null,g.Ac,g.I)),n.Ib(80,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(81,0,null,0,1,"SwtLabel",[],null,null,null,g.Yc,g.fb)),n.Ib(82,4440064,[[15,4],["granTotalLabel",4]],0,a.vb,[n.r,a.i],null,null),(t()(),n.Jb(83,0,null,0,10,"GridItem",[["width","80%"]],null,null,null,g.Ac,g.I)),n.Ib(84,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(85,0,null,0,8,"SwtRadioButtonGroup",[["align","horizontal"],["id","#granTotalGroup"],["width","100%"]],null,[[null,"change"]],function(t,e,l){var n=!0,i=t.component;"change"===e&&(n=!1!==i.changeTotalGroup()&&n);return n},g.ed,g.lb)),n.Ib(86,4440064,[[3,4],["granTotalGroup",4]],1,a.Hb,[w.c,n.r,a.i],{id:[0,"id"],width:[1,"width"],align:[2,"align"]},{change_:"change"}),n.Zb(603979776,20,{radioItems:1}),(t()(),n.Jb(88,0,null,0,1,"SwtRadioItem",[["groupName","granTotalGroup"],["id","addGT"],["selected","true"],["value","PLUS"],["width","15%"]],null,null,null,g.fd,g.mb)),n.Ib(89,4440064,[[20,4],[4,4],["addGT",4]],0,a.Ib,[n.r,a.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),n.Jb(90,0,null,0,1,"SwtRadioItem",[["groupName","granTotalGroup"],["id","subGT"],["value","MINUS"],["width","15%"]],null,null,null,g.fd,g.mb)),n.Ib(91,4440064,[[20,4],[5,4],["subGT",4]],0,a.Ib,[n.r,a.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),n.Jb(92,0,null,0,1,"SwtRadioItem",[["groupName","granTotalGroup"],["id","noGT"],["value","NONE"],["width","25%"]],null,null,null,g.fd,g.mb)),n.Ib(93,4440064,[[20,4],[6,4],["noGT",4]],0,a.Ib,[n.r,a.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),n.Jb(94,0,null,0,15,"SwtCanvas",[["height","20%"],["id","canvasContainer"],["width","100%"]],null,null,null,g.Nc,g.U)),n.Ib(95,4440064,null,0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(96,0,null,0,13,"HBox",[["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(97,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(98,0,null,0,5,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,g.Dc,g.K)),n.Ib(99,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(100,0,null,0,1,"SwtButton",[["id","saveButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.addChangeClickHandler()&&n);"keyDown"===e&&(n=!1!==i.keyDownEventHandler(l)&&n);return n},g.Mc,g.T)),n.Ib(101,4440064,[[17,4],["saveButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(102,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.closeHandler()&&n);"keyDown"===e&&(n=!1!==i.keyDownEventHandler(l)&&n);return n},g.Mc,g.T)),n.Ib(103,4440064,[[18,4],["cancelButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(104,0,null,0,5,"HBox",[["horizontalAlign","right"]],null,null,null,g.Dc,g.K)),n.Ib(105,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"]},null),(t()(),n.Jb(106,0,null,0,1,"SwtHelpButton",[["enabled","true"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,l){var n=!0,i=t.component;"click"===e&&(n=!1!==i.doHelp()&&n);return n},g.Wc,g.db)),n.Ib(107,4440064,null,0,a.rb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(108,0,null,0,1,"SwtLoadingImage",[],null,null,null,g.Zc,g.gb)),n.Ib(109,114688,[[19,4],["loadingImage",4]],0,a.xb,[n.r],null,null)],function(t,e){t(e,20,0,"100%","100%");t(e,22,0,"100%","100%","5","5","5","5");t(e,24,0,"100%","80%");t(e,26,0,"100%","100%");t(e,28,0,"100%","100%"),t(e,30,0);t(e,32,0,"20%");t(e,34,0,"Functional Group");t(e,36,0,"40%");t(e,38,0,"functionalGrpList","227","functionalGrpCombo");t(e,40,0,"20%");t(e,42,0,"left","","normal"),t(e,44,0);t(e,46,0,"20%");t(e,48,0,"Restriction");t(e,50,0,"80%");t(e,52,0,"restrictionInput","75","false"),t(e,54,0);t(e,56,0,"20%");t(e,58,0,"Attribute");t(e,60,0,"40%");t(e,62,0,"acctAttributHDRList","227","attributeCombo");t(e,64,0,"20%");t(e,66,0,"left","","normal"),t(e,68,0);t(e,70,0,"20%");t(e,72,0,"Display Order");t(e,74,0,"80%");t(e,76,0,"displayOrderInput","right","68"),t(e,78,0);t(e,80,0,"20%"),t(e,82,0);t(e,84,0,"80%");t(e,86,0,"#granTotalGroup","100%","horizontal");t(e,89,0,"addGT","15%","granTotalGroup","PLUS","true");t(e,91,0,"subGT","15%","granTotalGroup","MINUS");t(e,93,0,"noGT","25%","granTotalGroup","NONE");t(e,95,0,"canvasContainer","100%","20%");t(e,97,0,"100%");t(e,99,0,"100%","5");t(e,101,0,"saveButton","70");t(e,103,0,"cancelButton","70","true");t(e,105,0,"right");t(e,107,0,"helpIcon","true",!0),t(e,109,0)},null)}function F(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-attribute-usage-add",[],null,null,null,H,U)),n.Ib(1,4440064,null,0,o,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var E=n.Fb("app-attribute-usage-add",o,F,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);