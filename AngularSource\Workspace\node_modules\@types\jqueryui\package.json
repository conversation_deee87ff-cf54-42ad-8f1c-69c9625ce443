{"_from": "@types/jqueryui@^1.12.7", "_id": "@types/jqueryui@1.12.24", "_inBundle": false, "_integrity": "sha512-E2sGULwzMhg4kAeOV+gYcXjg988RuPkviWCt09jLe6GGK9sHM7dTqS8H7JMuUWoZQBucIBzBAgM5o/ezKUFkeg==", "_location": "/@types/jqueryui", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/jqueryui@^1.12.7", "name": "@types/jqueryui", "escapedName": "@types%2fjqueryui", "scope": "@types", "rawSpec": "^1.12.7", "saveSpec": null, "fetchSpec": "^1.12.7"}, "_requiredBy": ["/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@types/jqueryui/-/jqueryui-1.12.24.tgz", "_shasum": "e78e862b0da13b9adb4f25ff8a00d7175c85860a", "_spec": "@types/jqueryui@^1.12.7", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/doberkofler"}], "dependencies": {"@types/jquery": "*"}, "deprecated": false, "description": "TypeScript definitions for jqueryui", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jqueryui", "license": "MIT", "main": "", "name": "@types/jqueryui", "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jqueryui"}, "scripts": {}, "typeScriptVersion": "5.0", "types": "index.d.ts", "typesPublisherContentHash": "6a64268171a23750c883a79b8f5cd4314ed96f38710265e5b19f7dfbcfc946bd", "version": "1.12.24"}