(window.webpackJsonp=window.webpackJsonp||[]).push([[49],{ciqg:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),o=i("mrSG"),a=i("447K"),s=i("jpKN"),l=i("ZYCi"),r=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.jsonReader=new a.L,n.inputData=new a.G(n.commonService),n.logicUpdate=new a.G(n.commonService),n.requestParams=[],n.baseURL=a.Wb.getBaseURL(),n.actionMethod="",n.actionPath="",n.errorLocation=0,n.moduleId="",n.swtAlert=new a.bb(e),n}return o.d(e,t),e.prototype.ngOnInit=function(){this.viewButton.label=a.Wb.getPredictMessage("button.view",null),this.viewButton.toolTip=a.Wb.getPredictMessage("tooltip.view",null),this.closeButton.label=a.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=a.Wb.getPredictMessage("tooltip.close",null)},e.prototype.onLoad=function(){var t=this;this.messageGrid=this.canvasGrid.addChild(a.hb);try{var e=a.x.call("eval","instanceId");this.actionMethod="method=getAlertInstMsg",this.actionPath="scenarioSummary.do?",this.requestParams.instanceId=e,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.messageGrid.onRowClick=function(e){t.cellClickEventHandler(e)}}catch(i){console.log(i,this.moduleId,"ClassName","onLoad")}},e.prototype.inputDataResult=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&this.jsonReader.getRequestReplyStatus())if(this.jsonReader.isDataBuilding())this.swtAlert.error(this.jsonReader.getRequestReplyMessage());else{var e={columns:this.lastRecievedJSON.scenarioSummary.msgGrid.metadata.columns};this.messageGrid.CustomGrid(e);var i=this.lastRecievedJSON.scenarioSummary.msgGrid.rows;i.size>0?(this.messageGrid.gridData=i,this.messageGrid.setRowSize=this.jsonReader.getRowSize()):this.messageGrid.gridData={size:0,row:[]},this.prevRecievedJSON=this.lastRecievedJSON}}catch(n){a.Wb.logError(n,this.moduleId,"ClassName","inputDataResult",this.errorLocation)}},e.prototype.doViewMessage=function(t){var e=this;try{this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.displayMsg(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="scenarioSummary.do?",this.actionMethod="method=getInstMsgBody",this.requestParams.messageId=this.messageId,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(i){a.Wb.logError(i,this.moduleId,"ClassName","doViewMessage",this.errorLocation)}},e.prototype.displayMsg=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON){var e=this.jsonReader.getSingletons().messageBody;this.win=a.Eb.createPopUp(this,s.a,{title:"Message Details",screenName:"view",messageId:this.messageId,messageBody:e}),this.win.width="680",this.win.height="330",this.win.top="2",this.win.enableResize=!1,this.win.showControls=!0,this.win.display(),this.prevRecievedJSON=this.lastRecievedJSON}}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error")}catch(i){a.Wb.logError(i,this.moduleId,"ClassName","displayMsg",this.errorLocation)}},e.prototype.cellClickEventHandler=function(t){try{this.messageGrid.selectedIndex>-1?(this.messageId=t.messageId.content,this.viewButton.enabled=!0):this.viewButton.enabled=!1}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","cellClickEventHandler",this.errorLocation)}},e.prototype.close=function(){window.close()},e.prototype.startOfComms=function(){try{this.loadingImage.setVisible(!0)}catch(t){a.Wb.logError(t,this.moduleId,"ClassName","startOfComms",this.errorLocation)}},e.prototype.endOfComms=function(){try{this.loadingImage.setVisible(!1)}catch(t){a.Wb.logError(t,this.moduleId,"ClassName","endOfComms",this.errorLocation)}},e.prototype.inputDataFault=function(t){try{this.swtAlert.error(t.fault.faultstring+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail)}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","inputDataFault",this.errorLocation)}},e}(a.yb),d=[{path:"",component:r}],c=(l.l.forChild(d),function(){return function(){}}()),u=i("pMnS"),h=i("RChO"),b=i("t6HQ"),g=i("WFGK"),p=i("5FqG"),m=i("Ip0R"),R=i("gIcY"),f=i("t/Na"),w=i("sE5F"),v=i("OzfB"),y=i("T7CS"),C=i("S7LP"),S=i("6aHO"),I=i("WzUx"),D=i("A7o+"),B=i("zCE2"),O=i("Jg5P"),k=i("3R0m"),N=i("hhbb"),L=i("5rxC"),M=i("Fzqc"),J=i("21Lb"),G=i("hUWP"),_=i("3pJQ"),T=i("V9q+"),P=i("VDKW"),q=i("kXfT"),x=i("BGbe");i.d(e,"AlertInstMsgDisplayModuleNgFactory",function(){return A}),i.d(e,"RenderType_AlertInstMsgDisplay",function(){return W}),i.d(e,"View_AlertInstMsgDisplay_0",function(){return j}),i.d(e,"View_AlertInstMsgDisplay_Host_0",function(){return z}),i.d(e,"AlertInstMsgDisplayNgFactory",function(){return F});var A=n.Gb(c,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[u.a,h.a,b.a,g.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,F]],[3,n.n],n.J]),n.Rb(4608,m.m,m.l,[n.F,[2,m.u]]),n.Rb(4608,R.c,R.c,[]),n.Rb(4608,R.p,R.p,[]),n.Rb(4608,f.j,f.p,[m.c,n.O,f.n]),n.Rb(4608,f.q,f.q,[f.j,f.o]),n.Rb(5120,f.a,function(t){return[t,new a.tb]},[f.q]),n.Rb(4608,f.m,f.m,[]),n.Rb(6144,f.k,null,[f.m]),n.Rb(4608,f.i,f.i,[f.k]),n.Rb(6144,f.b,null,[f.i]),n.Rb(4608,f.f,f.l,[f.b,n.B]),n.Rb(4608,f.c,f.c,[f.f]),n.Rb(4608,w.c,w.c,[]),n.Rb(4608,w.g,w.b,[]),n.Rb(5120,w.i,w.j,[]),n.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),n.Rb(4608,w.f,w.a,[]),n.Rb(5120,w.d,w.k,[w.h,w.f]),n.Rb(5120,n.b,function(t,e){return[v.j(t,e)]},[m.c,n.O]),n.Rb(4608,y.a,y.a,[]),n.Rb(4608,C.a,C.a,[]),n.Rb(4608,S.a,S.a,[n.n,n.L,n.B,C.a,n.g]),n.Rb(4608,I.c,I.c,[n.n,n.g,n.B]),n.Rb(4608,I.e,I.e,[I.c]),n.Rb(4608,D.l,D.l,[]),n.Rb(4608,D.h,D.g,[]),n.Rb(4608,D.c,D.f,[]),n.Rb(4608,D.j,D.d,[]),n.Rb(4608,D.b,D.a,[]),n.Rb(4608,D.k,D.k,[D.l,D.h,D.c,D.j,D.b,D.m,D.n]),n.Rb(4608,I.i,I.i,[[2,D.k]]),n.Rb(4608,I.r,I.r,[I.L,[2,D.k],I.i]),n.Rb(4608,I.t,I.t,[]),n.Rb(4608,I.w,I.w,[]),n.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),n.Rb(1073742336,m.b,m.b,[]),n.Rb(1073742336,R.n,R.n,[]),n.Rb(1073742336,R.l,R.l,[]),n.Rb(1073742336,B.a,B.a,[]),n.Rb(1073742336,O.a,O.a,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,k.a,k.a,[]),n.Rb(1073742336,D.i,D.i,[]),n.Rb(1073742336,I.b,I.b,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,f.d,f.d,[]),n.Rb(1073742336,w.e,w.e,[]),n.Rb(1073742336,N.b,N.b,[]),n.Rb(1073742336,L.b,L.b,[]),n.Rb(1073742336,v.c,v.c,[]),n.Rb(1073742336,M.a,M.a,[]),n.Rb(1073742336,J.d,J.d,[]),n.Rb(1073742336,G.c,G.c,[]),n.Rb(1073742336,_.a,_.a,[]),n.Rb(1073742336,T.a,T.a,[[2,v.g],n.O]),n.Rb(1073742336,P.b,P.b,[]),n.Rb(1073742336,q.a,q.a,[]),n.Rb(1073742336,x.b,x.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,c,c,[]),n.Rb(256,f.n,"XSRF-TOKEN",[]),n.Rb(256,f.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,D.m,void 0,[]),n.Rb(256,D.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,l.i,function(){return[[{path:"",component:r}]]},[])])}),E=[[""]],W=n.Hb({encapsulation:0,styles:E,data:{}});function j(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{canvasGrid:0}),n.Zb(402653184,3,{loadingImage:0}),n.Zb(402653184,4,{viewButton:0}),n.Zb(402653184,5,{closeButton:0}),(t()(),n.Jb(5,0,null,null,19,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,o=t.component;"creationComplete"===e&&(n=!1!==o.onLoad()&&n);return n},p.ad,p.hb)),n.Ib(6,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(7,0,null,0,17,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(8,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(9,0,null,0,1,"SwtCanvas",[["height","90%"],["id","canvasGrid"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(10,4440064,[[2,4],["canvasGrid",4]],0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(11,0,null,0,13,"SwtCanvas",[["height","40"],["id","canvasButtons"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(12,4440064,null,0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(13,0,null,0,11,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(14,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(15,0,null,0,5,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(16,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(17,0,null,0,1,"SwtButton",[["enabled","false"],["id","viewButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.doViewMessage(i)&&n);return n},p.Mc,p.T)),n.Ib(18,4440064,[[4,4],["viewButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(19,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.close()&&n);return n},p.Mc,p.T)),n.Ib(20,4440064,[[5,4],["closeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(21,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingRight","5"]],null,null,null,p.Dc,p.K)),n.Ib(22,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),n.Jb(23,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),n.Ib(24,114688,[[3,4],["loadingImage",4]],0,a.xb,[n.r],null,null)],function(t,e){t(e,6,0,"100%","100%");t(e,8,0,"100%","100%","5","5","5","5");t(e,10,0,"canvasGrid","100%","90%");t(e,12,0,"canvasButtons","100%","40");t(e,14,0,"100%");t(e,16,0,"100%","5");t(e,18,0,"viewButton","false",!0);t(e,20,0,"closeButton",!0);t(e,22,0,"right","5"),t(e,24,0)},null)}function z(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-alert-inst-msg-display",[],null,null,null,j,W)),n.Ib(1,4440064,null,0,r,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var F=n.Fb("app-alert-inst-msg-display",r,z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);