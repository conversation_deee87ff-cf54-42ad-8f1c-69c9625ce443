(window.webpackJsonp=window.webpackJsonp||[]).push([[80],{iB07:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),o=i("mrSG"),a=i("447K"),l=i("ZYCi"),s=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.jsonReader=new a.L,n.inputData=new a.G(n.commonService),n.requestParams=[],n.baseURL=a.Wb.getBaseURL(),n.menuAccessId=null,n.templateId=null,n.userId=null,n.templateName=null,n.isPublic=null,n.fourEyesRequired=!0,Window.Main=n,n.swtAlert=new a.bb(e),n}return o.d(e,t),e.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this},e.prototype.onLoad=function(){var t=this;this.requestParams=[],this.forecastMonitorTemplateGrid=this.forecastMonitorTemplateCanvas.addChild(a.hb),this.forecastMonitorTemplateGrid.onFilterChanged=this.disableButtons.bind(this),this.forecastMonitorTemplateGrid.onSortChanged=this.disableButtons.bind(this);try{this.menuAccessId=a.x.call("eval","menuAccessId"),"0"!=this.menuAccessId&&(this.addButton.enabled=!1),this.actionMethod="method=displayMonitorTemplate",this.actionPath="forecastMonitorTemplate.do?",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.requestParams.method="displayMonitorTemplate",this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.forecastMonitorTemplateGrid.onRowClick=function(e){t.cellLogic(e)},this.addButton.label=a.Wb.getPredictMessage("button.add",null),this.changeButton.label=a.Wb.getPredictMessage("button.change",null),this.deleteButton.label=a.Wb.getPredictMessage("button.delete",null),this.closeButton.label=a.Wb.getPredictMessage("button.close",null),this.addButton.toolTip=a.Wb.getPredictMessage("button.add",null),this.changeButton.toolTip=a.Wb.getPredictMessage("button.change",null),this.deleteButton.toolTip=a.Wb.getPredictMessage("button.delete",null),this.closeButton.toolTip=a.Wb.getPredictMessage("tooltip.close",null),a.v.subscribe(function(e){t.report(e)})}catch(e){console.log("errr",e)}},e.prototype.inputDataResult=function(t){try{if(this.inputData&&this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(!this.jsonReader.isDataBuilding()){var e={columns:this.jsonReader.getColumnData()};this.forecastMonitorTemplateGrid.CustomGrid(e),this.jsonReader.getGridData().size>0?(this.forecastMonitorTemplateGrid.gridData=this.jsonReader.getGridData(),this.forecastMonitorTemplateGrid.setRowSize=this.jsonReader.getRowSize()):(this.forecastMonitorTemplateGrid.dataProvider=[],this.forecastMonitorTemplateGrid.selectedIndex=-1)}this.prevRecievedJSON=this.lastRecievedJSON}}catch(i){console.log("error in inputData",i)}},e.prototype.refreshdetails=function(){var t=this;this.menuAccessId=a.x.call("eval","menuAccessId"),"0"!=this.menuAccessId&&(this.addButton.enabled=!1),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataRefresh(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="forecastMonitorTemplate.do?method=",this.actionMethod="displayMonitorTemplate",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataFault=function(){this.swtAlert.error("genericthis.exception")},e.prototype.addTemplate=function(){try{a.x.call("clearSessionInstance"),this.actionMethod="displayAddMonitorTemplate",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,a.x.call("openChildWindow",this.actionMethod)}catch(t){console.log("error add",t)}},e.prototype.changeTemplate=function(){try{var t=a.x.call("lockTemplate",this.templateId,this.templateName,this.userId,this.isPublic);null==t||""==t?(a.x.call("clearSessionInstance"),this.actionMethod="displayChangeMonitorTemplate",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.actionMethod=this.actionMethod+"&templateId="+this.templateId,this.actionMethod=this.actionMethod+"&templateName="+this.templateName,this.actionMethod=this.actionMethod+"&userId="+this.userId,this.actionMethod=this.actionMethod+"&isPublic="+this.isPublic,a.x.call("openChildWindow",this.actionMethod)):this.swtAlert.warning(a.Wb.getPredictMessage("alert.forecasttemplate.templatelocked",null)+t)}catch(e){console.log("error add",e)}},e.prototype.deleteTemplate=function(){this.swtAlert.question("Are you sure you want to delete this template?",null,a.c.OK|a.c.CANCEL,null,this.templateRemoveAlertListener.bind(this),null)},e.prototype.templateRemoveAlertListener=function(t){var e=this;if(this.requestParams=[],t.detail==a.c.OK){var i=a.x.call("lockTemplate",this.templateId,this.templateName,this.userId,this.isPublic);null==i||""==i?(this.actionMethod="method=deleteMonitorTemplate",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataRefresh(t)},this.inputData.cbFault=this.inputDataFault,this.inputData.encodeURL=!1,this.requestParams.templateId=this.templateId,this.requestParams.userId=this.userId,this.requestParams.templateName=this.templateName,this.requestParams.method="deleteMonitorTemplate",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.forecastMonitorTemplateGrid.selectedIndex=-1,this.changeButton.enabled=!1,this.deleteButton.enabled=!1):this.swtAlert.warning("Template ID is locked by "+i)}},e.prototype.closeHandler=function(){a.x.call("close")},e.prototype.doHelp=function(){a.x.call("help")},e.prototype.cellLogic=function(t){try{"0"==this.menuAccessId&&this.forecastMonitorTemplateGrid.selectedIndex>=0&&this.forecastMonitorTemplateGrid.selectedItem?(this.templateId=this.forecastMonitorTemplateGrid.selectedItem.templateid.content,this.userId=this.forecastMonitorTemplateGrid.selectedItem.userid.content,this.templateName=this.forecastMonitorTemplateGrid.selectedItem.templatename.content,this.isPublic=this.forecastMonitorTemplateGrid.selectedItem.ispublic.content,this.disableOrEnableButtons(!0)):this.disableOrEnableButtons(!1)}catch(e){console.log("error event click",e)}},e.prototype.disableOrEnableButtons=function(t){t?(this.changeButton.enabled=!0,this.deleteButton.enabled=!("*DEFAULT*"==this.templateId&&"*DEFAULT*"==this.userId)):(this.changeButton.enabled=!1,this.deleteButton.enabled=!1)},e.prototype.disableButtons=function(){-1==this.forecastMonitorTemplateGrid.selectedIndex&&(this.changeButton.enabled=!1,this.deleteButton.enabled=!1)},e.prototype.inputDataRefresh=function(t){try{if(this.inputData&&this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&!this.jsonReader.isDataBuilding()){var e={columns:this.jsonReader.getColumnData()};this.forecastMonitorTemplateGrid.CustomGrid(e),this.jsonReader.getGridData().size>0?(this.forecastMonitorTemplateGrid.gridData=this.jsonReader.getGridData(),this.forecastMonitorTemplateGrid.setRowSize=this.jsonReader.getRowSize(),this.changeButton.enabled=!1,this.deleteButton.enabled=!1):(this.forecastMonitorTemplateGrid.dataProvider=[],this.forecastMonitorTemplateGrid.selectedIndex=-1)}}catch(i){console.log("error in inputData",i)}},e.prototype.report=function(t){this.dataExport.convertData(this.lastRecievedJSON.forecastmonitor.grid.metadata.columns,this.forecastMonitorTemplateGrid,null,null,t,!1)},e}(a.yb),r=[{path:"",component:s}],d=(l.l.forChild(r),function(){return function(){}}()),c=i("pMnS"),u=i("RChO"),h=i("t6HQ"),b=i("WFGK"),p=i("5FqG"),m=i("Ip0R"),g=i("gIcY"),R=i("t/Na"),f=i("sE5F"),M=i("OzfB"),T=i("T7CS"),w=i("S7LP"),I=i("6aHO"),B=i("WzUx"),D=i("A7o+"),C=i("zCE2"),y=i("Jg5P"),v=i("3R0m"),k=i("hhbb"),S=i("5rxC"),G=i("Fzqc"),x=i("21Lb"),P=i("hUWP"),L=i("3pJQ"),O=i("V9q+"),A=i("VDKW"),_=i("kXfT"),J=i("BGbe");i.d(e,"ForecastMonitorTemplateModuleNgFactory",function(){return N}),i.d(e,"RenderType_ForecastMonitorTemplate",function(){return q}),i.d(e,"View_ForecastMonitorTemplate_0",function(){return E}),i.d(e,"View_ForecastMonitorTemplate_Host_0",function(){return j}),i.d(e,"ForecastMonitorTemplateNgFactory",function(){return W});var N=n.Gb(d,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[c.a,u.a,h.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,W]],[3,n.n],n.J]),n.Rb(4608,m.m,m.l,[n.F,[2,m.u]]),n.Rb(4608,g.c,g.c,[]),n.Rb(4608,g.p,g.p,[]),n.Rb(4608,R.j,R.p,[m.c,n.O,R.n]),n.Rb(4608,R.q,R.q,[R.j,R.o]),n.Rb(5120,R.a,function(t){return[t,new a.tb]},[R.q]),n.Rb(4608,R.m,R.m,[]),n.Rb(6144,R.k,null,[R.m]),n.Rb(4608,R.i,R.i,[R.k]),n.Rb(6144,R.b,null,[R.i]),n.Rb(4608,R.f,R.l,[R.b,n.B]),n.Rb(4608,R.c,R.c,[R.f]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.g,f.b,[]),n.Rb(5120,f.i,f.j,[]),n.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),n.Rb(4608,f.f,f.a,[]),n.Rb(5120,f.d,f.k,[f.h,f.f]),n.Rb(5120,n.b,function(t,e){return[M.j(t,e)]},[m.c,n.O]),n.Rb(4608,T.a,T.a,[]),n.Rb(4608,w.a,w.a,[]),n.Rb(4608,I.a,I.a,[n.n,n.L,n.B,w.a,n.g]),n.Rb(4608,B.c,B.c,[n.n,n.g,n.B]),n.Rb(4608,B.e,B.e,[B.c]),n.Rb(4608,D.l,D.l,[]),n.Rb(4608,D.h,D.g,[]),n.Rb(4608,D.c,D.f,[]),n.Rb(4608,D.j,D.d,[]),n.Rb(4608,D.b,D.a,[]),n.Rb(4608,D.k,D.k,[D.l,D.h,D.c,D.j,D.b,D.m,D.n]),n.Rb(4608,B.i,B.i,[[2,D.k]]),n.Rb(4608,B.r,B.r,[B.L,[2,D.k],B.i]),n.Rb(4608,B.t,B.t,[]),n.Rb(4608,B.w,B.w,[]),n.Rb(1073742336,l.l,l.l,[[2,l.r],[2,l.k]]),n.Rb(1073742336,m.b,m.b,[]),n.Rb(1073742336,g.n,g.n,[]),n.Rb(1073742336,g.l,g.l,[]),n.Rb(1073742336,C.a,C.a,[]),n.Rb(1073742336,y.a,y.a,[]),n.Rb(1073742336,g.e,g.e,[]),n.Rb(1073742336,v.a,v.a,[]),n.Rb(1073742336,D.i,D.i,[]),n.Rb(1073742336,B.b,B.b,[]),n.Rb(1073742336,R.e,R.e,[]),n.Rb(1073742336,R.d,R.d,[]),n.Rb(1073742336,f.e,f.e,[]),n.Rb(1073742336,k.b,k.b,[]),n.Rb(1073742336,S.b,S.b,[]),n.Rb(1073742336,M.c,M.c,[]),n.Rb(1073742336,G.a,G.a,[]),n.Rb(1073742336,x.d,x.d,[]),n.Rb(1073742336,P.c,P.c,[]),n.Rb(1073742336,L.a,L.a,[]),n.Rb(1073742336,O.a,O.a,[[2,M.g],n.O]),n.Rb(1073742336,A.b,A.b,[]),n.Rb(1073742336,_.a,_.a,[]),n.Rb(1073742336,J.b,J.b,[]),n.Rb(1073742336,a.Tb,a.Tb,[]),n.Rb(1073742336,d,d,[]),n.Rb(256,R.n,"XSRF-TOKEN",[]),n.Rb(256,R.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,D.m,void 0,[]),n.Rb(256,D.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,l.i,function(){return[[{path:"",component:s}]]},[])])}),F=[[""]],q=n.Hb({encapsulation:0,styles:F,data:{}});function E(t){return n.dc(0,[n.Zb(402653184,1,{_container:0}),n.Zb(402653184,2,{forecastMonitorTemplateCanvas:0}),n.Zb(402653184,3,{addButton:0}),n.Zb(402653184,4,{changeButton:0}),n.Zb(402653184,5,{deleteButton:0}),n.Zb(402653184,6,{closeButton:0}),n.Zb(402653184,7,{loadingImage:0}),n.Zb(402653184,8,{dataExport:0}),(t()(),n.Jb(8,0,null,null,27,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,o=t.component;"creationComplete"===e&&(n=!1!==o.onLoad()&&n);return n},p.ad,p.hb)),n.Ib(9,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(10,0,null,0,25,"VBox",[["height","100%"],["paddingBottom","10"],["paddingLeft","10"],["paddingRight","10"],["paddingTop","10"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(11,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(12,0,null,0,1,"SwtCanvas",[["height","88%"],["id","forecastMonitorTemplateCanvas"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(13,4440064,[[2,4],["forecastMonitorTemplateCanvas",4]],0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(14,0,null,0,21,"SwtCanvas",[["height","9%"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(15,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(16,0,null,0,19,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(17,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(18,0,null,0,9,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(19,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(20,0,null,0,1,"SwtButton",[["id","addButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.addTemplate()&&n);return n},p.Mc,p.T)),n.Ib(21,4440064,[[3,4],["addButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(22,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.changeTemplate()&&n);return n},p.Mc,p.T)),n.Ib(23,4440064,[[4,4],["changeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(24,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.deleteTemplate()&&n);return n},p.Mc,p.T)),n.Ib(25,4440064,[[5,4],["deleteButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"]},{onClick_:"click"}),(t()(),n.Jb(26,0,null,0,1,"SwtButton",[["id","closeButton"],["width","70"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.closeHandler()&&n);return n},p.Mc,p.T)),n.Ib(27,4440064,[[6,4],["closeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],width:[1,"width"]},{onClick_:"click"}),(t()(),n.Jb(28,0,null,0,7,"HBox",[["horizontalAlign","right"],["top","3"]],null,null,null,p.Dc,p.K)),n.Ib(29,4440064,null,0,a.C,[n.r,a.i],{top:[0,"top"],horizontalAlign:[1,"horizontalAlign"]},null),(t()(),n.Jb(30,0,null,0,1,"DataExport",[["id","dataExport"]],null,null,null,p.Sc,p.Z)),n.Ib(31,4440064,[[8,4],["dataExport",4]],0,a.kb,[a.i,n.r],{id:[0,"id"]},null),(t()(),n.Jb(32,0,null,0,1,"SwtHelpButton",[["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,o=t.component;"click"===e&&(n=!1!==o.doHelp()&&n);return n},p.Wc,p.db)),n.Ib(33,4440064,[["helpIcon",4]],0,a.rb,[n.r,a.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),n.Jb(34,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),n.Ib(35,114688,[[7,4],["loadingImage",4]],0,a.xb,[n.r],null,null)],function(t,e){t(e,9,0,"100%","100%");t(e,11,0,"100%","100%","10","10","10","10");t(e,13,0,"forecastMonitorTemplateCanvas","100%","88%");t(e,15,0,"100%","9%");t(e,17,0,"100%");t(e,19,0,"100%","5");t(e,21,0,"addButton","70",!0);t(e,23,0,"changeButton","70","false");t(e,25,0,"deleteButton","70","false");t(e,27,0,"closeButton","70");t(e,29,0,"3","right");t(e,31,0,"dataExport");t(e,33,0,"helpIcon"),t(e,35,0)},null)}function j(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-forcast-monitor",[],null,null,null,E,q)),n.Ib(1,4440064,null,0,s,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var W=n.Fb("app-forcast-monitor",s,j,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);