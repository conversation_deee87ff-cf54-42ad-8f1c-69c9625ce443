<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Advanced User Documentation - Matching Process</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 50%, #667eea 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 60px 40px;
            text-align: center;
            margin-bottom: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 3.5em;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 20px;
            font-weight: 700;
        }
        
        .header p {
            font-size: 1.3em;
            color: #666;
            margin-bottom: 30px;
            font-weight: 300;
        }
        
        .main-card {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 50px;
            margin-bottom: 30px;
            box-shadow: 0 15px 40px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .main-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #667eea, #764ba2, #667eea);
            background-size: 200% 100%;
            animation: gradient-flow 3s ease-in-out infinite;
        }
        
        @keyframes gradient-flow {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }
        
        .main-title {
            font-size: 2.5em;
            color: #333;
            margin-bottom: 25px;
            font-weight: 600;
        }
        
        .main-description {
            font-size: 1.2em;
            color: #666;
            line-height: 1.7;
            margin-bottom: 40px;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        
        .feature-card {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            border: 2px solid rgba(102, 126, 234, 0.2);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
            border-color: rgba(102, 126, 234, 0.5);
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 20px;
            display: block;
        }
        
        .feature-title {
            font-size: 1.3em;
            color: #333;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        .feature-description {
            color: #666;
            line-height: 1.6;
        }
        
        .cta-button {
            display: inline-block;
            padding: 20px 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-size: 1.2em;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            position: relative;
            overflow: hidden;
        }
        
        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }
        
        .cta-button:hover::before {
            left: 100%;
        }
        
        .cta-button:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        }
        
        .stats-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            padding: 40px;
            margin: 40px 0;
            text-align: center;
        }
        
        .stats-title {
            font-size: 2em;
            margin-bottom: 30px;
            font-weight: 300;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-number {
            font-size: 3em;
            font-weight: bold;
            display: block;
            margin-bottom: 10px;
        }
        
        .stat-label {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .footer {
            background: rgba(255,255,255,0.95);
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .footer h3 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }
        
        .footer p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 30px;
        }
        
        .footer-links {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .footer-link {
            padding: 12px 25px;
            background: rgba(102, 126, 234, 0.1);
            color: #667eea;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid rgba(102, 126, 234, 0.2);
        }
        
        .footer-link:hover {
            background: rgba(102, 126, 234, 0.2);
            border-color: rgba(102, 126, 234, 0.4);
            transform: translateY(-2px);
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5em;
            }
            
            .main-title {
                font-size: 2em;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .footer-links {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 Advanced User Documentation</h1>
            <p>Matching Process - Expert Level Insights</p>
        </div>
        
        <div class="main-card">
            <h2 class="main-title">🧠 Advanced Matching Process Guide</h2>
            <p class="main-description">
                Unlock the full potential of the Matching Process system with our comprehensive advanced user guide. 
                Designed for power users, business analysts, and advanced operators who need deep insights into 
                the sophisticated algorithms, strategic configurations, and optimization techniques that drive 
                the matching engine.
            </p>
            
            <div class="features-grid">
                <div class="feature-card">
                    <span class="feature-icon">🎯</span>
                    <h3 class="feature-title">Strategic Intelligence</h3>
                    <p class="feature-description">
                        Deep dive into the strategic decision-making processes and intelligent algorithms 
                        that power the matching engine.
                    </p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🔬</span>
                    <h3 class="feature-title">Advanced Analytics</h3>
                    <p class="feature-description">
                        Comprehensive analysis of quality assessment frameworks, performance optimization, 
                        and predictive capabilities.
                    </p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">⚙️</span>
                    <h3 class="feature-title">Expert Configuration</h3>
                    <p class="feature-description">
                        Master advanced configuration techniques, dynamic parameter tuning, and 
                        algorithmic customization options.
                    </p>
                </div>
                
                <div class="feature-card">
                    <span class="feature-icon">🚀</span>
                    <h3 class="feature-title">Performance Mastery</h3>
                    <p class="feature-description">
                        Learn sophisticated optimization strategies, troubleshooting techniques, 
                        and performance enhancement methods.
                    </p>
                </div>
            </div>
            
            <a href="Advanced_Matching_Process_Guide.html" class="cta-button">
                🎓 Explore Advanced Guide
            </a>
        </div>
        
        <div class="stats-section">
            <h2 class="stats-title">📊 Advanced Capabilities Overview</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-number">7+</span>
                    <div class="stat-label">Quality Dimensions</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">11</span>
                    <div class="stat-label">Position Levels</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">∞</span>
                    <div class="stat-label">Configuration Options</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">AI</span>
                    <div class="stat-label">Machine Learning</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <h3>🎯 Ready to Become an Expert?</h3>
            <p>
                The Advanced Matching Process Guide provides everything you need to master the system's 
                sophisticated capabilities. From strategic insights to technical deep-dives, this 
                comprehensive resource will transform you into a matching process expert.
            </p>
            
            <div class="footer-links">
                <a href="Advanced_Matching_Process_Guide.html" class="footer-link">📚 Start Learning</a>
                <a href="../customer_documentation/index.html" class="footer-link">👥 Customer Docs</a>
                <a href="../matching_process_flowcharts/index.html" class="footer-link">📊 Technical Charts</a>
                <a href="#" class="footer-link" onclick="alert('Contact your system administrator for advanced training opportunities.')">🎓 Training</a>
            </div>
        </div>
    </div>

    <script>
        // Add smooth scroll animations
        document.addEventListener('DOMContentLoaded', function() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };
            
            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);
            
            // Observe all feature cards for animation
            document.querySelectorAll('.feature-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(30px)';
                card.style.transition = 'all 0.6s ease';
                observer.observe(card);
            });
        });
    </script>
</body>
</html>
