(window.webpackJsonp=window.webpackJsonp||[]).push([[33],{"0Ctu":function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),l=i("mrSG"),a=i("447K"),o=i("ZYCi"),u=function(t){function e(e,i){var n=t.call(this,i,e)||this;return n.commonService=e,n.element=i,n.jsonReader=new a.L,n.versionNumber="1.0",n.versionDate="04/11/2019",n.inputData=new a.G(n.commonService),n.requestParams=[],n.baseURL=a.Wb.getBaseURL(),n.actionMethod="",n.actionPath="",n.invalidComms="",n.errorLocation=0,n.moduleId="Predict",n.screenVersion=new a.V(n.commonService),n.screenName=null,n.entityId="",n.entityName="",n.currencyCode="",n.accountId="",n.accountName="",n.attributeId="",n.effectiveDate="",n.swtAlert=new a.bb(e),window.Main=n,n}return l.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){instanceElement=this,this.screenName=a.Wb.getPredictMessage("label.accountattributelastvalues.title.window",null),this.displayButton.label=a.Wb.getPredictMessage("button.display",null),this.displayButton.toolTip=a.Wb.getPredictMessage("button.display",null),this.closeButton.label=a.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=a.Wb.getPredictMessage("button.close",null),this.entityLabel.text=a.Wb.getPredictMessage("entity.id",null),this.accountIdLabel.text=a.Wb.getPredictMessage("acct.id",null)},e.prototype.onLoad=function(){var t=this;try{this.entityId=a.x.call("eval","entityId"),this.entityName=a.x.call("eval","entityName"),this.accountId=a.x.call("eval","accountId"),this.accountName=a.x.call("eval","accountName"),this.currencyCode=a.x.call("eval","currencyCode"),this.accountAttributeLastValuesGrid=this.canvasGrid.addChild(a.hb),this.accountAttributeLastValuesGrid.uniqueColumn="sequenceKey",this.accountAttributeLastValuesGrid.onFilterChanged=this.cellLogic.bind(this),this.accountAttributeLastValuesGrid.onRowClick=function(e){t.cellLogic(e)},this.initializeMenus(),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionMethod="method=displayAttributesFlex",this.actionPath="accountAttribute.do?",this.requestParams=[],this.requestParams.entityId=this.entityId,this.requestParams.accountId=this.accountId,this.requestParams.loadFlex="true",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){console.log(e,this.moduleId,"accountAttributeLastValuesGrid","onLoad")}},e.prototype.initializeMenus=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.versionDate);var t=new a.n("Show JSON");t.MenuItemSelect=this.showGridJSON.bind(this),this.screenVersion.svContextMenu.customItems.push(t),this.contextMenu=this.screenVersion.svContextMenu},e.prototype.showGridJSON=function(t){this.showJSONPopup=a.Eb.createPopUp(this,a.M,{jsonData:this.lastReceivedJSON}),this.showJSONPopup.width="700",this.showJSONPopup.title="Last Received JSON",this.showJSONPopup.height="500",this.showJSONPopup.enableResize=!1,this.showJSONPopup.showControls=!0,this.showJSONPopup.display()},e.prototype.inputDataResult=function(t){try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastReceivedJSON=t,this.jsonReader.setInputJSON(this.lastReceivedJSON),JSON.stringify(this.lastReceivedJSON)!==JSON.stringify(this.prevReceivedJSON)&&this.jsonReader.getRequestReplyStatus()){if(!this.jsonReader.isDataBuilding()){this.entityIdField.text=this.entityId,this.accountIdField.text=this.accountId,this.entityNameLabel.text=this.entityName,this.accountNameLabel.text=this.accountName;var e=t.accountattribute.grid.metadata;this.accountAttributeLastValuesGrid.CustomGrid(e),this.accountAttributeLastValuesGrid.colWidthURL(this.baseURL+"accountAttribute.do?&screenName=accountAttributeValue"),this.accountAttributeLastValuesGrid.colOrderURL(this.baseURL+"accountAttribute.do?&screenName=accountAttributeValue"),this.accountAttributeLastValuesGrid.saveWidths=!0,this.accountAttributeLastValuesGrid.saveColumnOrder=!0,this.jsonReader.getGridData()&&this.jsonReader.getGridData().size>0?(this.accountAttributeLastValuesGrid.gridData=this.jsonReader.getGridData(),this.accountAttributeLastValuesGrid.setRowSize=this.jsonReader.getRowSize(),this.accountAttributeLastValuesGrid.refreshFilters()):(this.accountAttributeLastValuesGrid.dataProvider=null,this.accountAttributeLastValuesGrid.selectedIndex=-1)}this.prevReceivedJSON=this.lastReceivedJSON}}catch(i){a.Wb.logError(i,this.moduleId,"AccountAttributeLastValue","inputDataResult",this.errorLocation)}},e.prototype.inputDataFault=function(t){try{this.invalidComms=t.fault.faultString+"\n"+t.fault.faultCode+"\n"+t.fault.faultDetail,this.swtAlert.error("fault "+this.invalidComms)}catch(e){a.Wb.logError(e,this.moduleId,"AccountAttributeLastValue","inputDataFault",this.errorLocation)}},e.prototype.closeHandler=function(){a.x.call("close")},e.prototype.doHelp=function(){try{a.x.call("help")}catch(t){a.Wb.logError(t,this.moduleId,"accountAttributeLastValuesGrid","doHelp",this.errorLocation)}},e.prototype.cellLogic=function(t){try{this.accountAttributeLastValuesGrid.selectedIndex>-1&&this.accountAttributeLastValuesGrid.selectable?(this.attributeId=this.accountAttributeLastValuesGrid.selectedItem.attributeid.content,this.displayButton.enabled=!0):this.displayButton.enabled=!1}catch(e){a.Wb.logError(e,this.moduleId,"accountAttributeLastValuesGrid","cellLogic",this.errorLocation)}},e.prototype.displayButtonClickHandler=function(t){var e=this.accountAttributeLastValuesGrid.selectedIndex;this.attributeId=this.accountAttributeLastValuesGrid.selectedItem.attributeid.content,this.effectiveDate=this.accountAttributeLastValuesGrid.dataProvider[e].effectiveDate,this.actionMethod="accountAttributesMaintenance",this.actionMethod+="&entityId="+this.entityId,this.actionMethod+="&accountId="+this.accountId,this.actionMethod+="&attributeId="+this.attributeId,this.actionMethod+="&effectiveDate="+this.effectiveDate,this.actionMethod+="&parentScreen=accountAttributeLastvalue",this.actionMethod+="&menuAccessId=1",this.actionMethod+="&currencyCode="+this.currencyCode,a.x.call("openChildWindow",this.actionMethod)},e}(a.yb),d=[{path:"",component:u}],c=(o.l.forChild(d),function(){return function(){}}()),s=i("pMnS"),r=i("RChO"),h=i("t6HQ"),b=i("WFGK"),p=i("5FqG"),g=i("Ip0R"),m=i("gIcY"),R=i("t/Na"),f=i("sE5F"),w=i("OzfB"),L=i("T7CS"),y=i("S7LP"),I=i("6aHO"),v=i("WzUx"),N=i("A7o+"),A=i("zCE2"),C=i("Jg5P"),S=i("3R0m"),G=i("hhbb"),J=i("5rxC"),x=i("Fzqc"),B=i("21Lb"),k=i("hUWP"),V=i("3pJQ"),D=i("V9q+"),M=i("VDKW"),O=i("kXfT"),T=i("BGbe");i.d(e,"AccountAttributeLastValuesModuleNgFactory",function(){return F}),i.d(e,"RenderType_AccountAttributeLastValues",function(){return _}),i.d(e,"View_AccountAttributeLastValues_0",function(){return W}),i.d(e,"View_AccountAttributeLastValues_Host_0",function(){return z}),i.d(e,"AccountAttributeLastValuesNgFactory",function(){return H});var F=n.Gb(c,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[s.a,r.a,h.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,H]],[3,n.n],n.J]),n.Rb(4608,g.m,g.l,[n.F,[2,g.u]]),n.Rb(4608,m.c,m.c,[]),n.Rb(4608,m.p,m.p,[]),n.Rb(4608,R.j,R.p,[g.c,n.O,R.n]),n.Rb(4608,R.q,R.q,[R.j,R.o]),n.Rb(5120,R.a,function(t){return[t,new a.tb]},[R.q]),n.Rb(4608,R.m,R.m,[]),n.Rb(6144,R.k,null,[R.m]),n.Rb(4608,R.i,R.i,[R.k]),n.Rb(6144,R.b,null,[R.i]),n.Rb(4608,R.f,R.l,[R.b,n.B]),n.Rb(4608,R.c,R.c,[R.f]),n.Rb(4608,f.c,f.c,[]),n.Rb(4608,f.g,f.b,[]),n.Rb(5120,f.i,f.j,[]),n.Rb(4608,f.h,f.h,[f.c,f.g,f.i]),n.Rb(4608,f.f,f.a,[]),n.Rb(5120,f.d,f.k,[f.h,f.f]),n.Rb(5120,n.b,function(t,e){return[w.j(t,e)]},[g.c,n.O]),n.Rb(4608,L.a,L.a,[]),n.Rb(4608,y.a,y.a,[]),n.Rb(4608,I.a,I.a,[n.n,n.L,n.B,y.a,n.g]),n.Rb(4608,v.c,v.c,[n.n,n.g,n.B]),n.Rb(4608,v.e,v.e,[v.c]),n.Rb(4608,N.l,N.l,[]),n.Rb(4608,N.h,N.g,[]),n.Rb(4608,N.c,N.f,[]),n.Rb(4608,N.j,N.d,[]),n.Rb(4608,N.b,N.a,[]),n.Rb(4608,N.k,N.k,[N.l,N.h,N.c,N.j,N.b,N.m,N.n]),n.Rb(4608,v.i,v.i,[[2,N.k]]),n.Rb(4608,v.r,v.r,[v.L,[2,N.k],v.i]),n.Rb(4608,v.t,v.t,[]),n.Rb(4608,v.w,v.w,[]),n.Rb(**********,o.l,o.l,[[2,o.r],[2,o.k]]),n.Rb(**********,g.b,g.b,[]),n.Rb(**********,m.n,m.n,[]),n.Rb(**********,m.l,m.l,[]),n.Rb(**********,A.a,A.a,[]),n.Rb(**********,C.a,C.a,[]),n.Rb(**********,m.e,m.e,[]),n.Rb(**********,S.a,S.a,[]),n.Rb(**********,N.i,N.i,[]),n.Rb(**********,v.b,v.b,[]),n.Rb(**********,R.e,R.e,[]),n.Rb(**********,R.d,R.d,[]),n.Rb(**********,f.e,f.e,[]),n.Rb(**********,G.b,G.b,[]),n.Rb(**********,J.b,J.b,[]),n.Rb(**********,w.c,w.c,[]),n.Rb(**********,x.a,x.a,[]),n.Rb(**********,B.d,B.d,[]),n.Rb(**********,k.c,k.c,[]),n.Rb(**********,V.a,V.a,[]),n.Rb(**********,D.a,D.a,[[2,w.g],n.O]),n.Rb(**********,M.b,M.b,[]),n.Rb(**********,O.a,O.a,[]),n.Rb(**********,T.b,T.b,[]),n.Rb(**********,a.Tb,a.Tb,[]),n.Rb(**********,c,c,[]),n.Rb(256,R.n,"XSRF-TOKEN",[]),n.Rb(256,R.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,N.m,void 0,[]),n.Rb(256,N.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,o.i,function(){return[[{path:"",component:u}]]},[])])}),P=[[""]],_=n.Hb({encapsulation:0,styles:P,data:{}});function W(t){return n.dc(0,[n.Zb(*********,1,{_container:0}),n.Zb(*********,2,{accountIdLabel:0}),n.Zb(*********,3,{accountNameLabel:0}),n.Zb(*********,4,{entityLabel:0}),n.Zb(*********,5,{entityNameLabel:0}),n.Zb(*********,6,{entityIdField:0}),n.Zb(*********,7,{accountIdField:0}),n.Zb(*********,8,{canvasGrid:0}),n.Zb(*********,9,{displayButton:0}),n.Zb(*********,10,{closeButton:0}),(t()(),n.Jb(10,0,null,null,49,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var n=!0,l=t.component;"creationComplete"===e&&(n=!1!==l.onLoad()&&n);return n},p.ad,p.hb)),n.Ib(11,4440064,null,0,a.yb,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(12,0,null,0,47,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),n.Ib(13,4440064,null,0,a.ec,[n.r,a.i,n.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),n.Jb(14,0,null,0,29,"SwtCanvas",[["height","15%"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(15,4440064,null,0,a.db,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(16,0,null,0,27,"HBox",[["height","100%"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(17,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(18,0,null,0,25,"Grid",[["height","100%"],["verticalGap","6"],["width","100%"]],null,null,null,p.Cc,p.H)),n.Ib(19,4440064,null,0,a.z,[n.r,a.i],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(20,0,null,0,11,"GridRow",[["height","50%"],["paddingLeft","10"],["paddingTop","10"],["width","100%"]],null,null,null,p.Bc,p.J)),n.Ib(21,4440064,null,0,a.B,[n.r,a.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"]},null),(t()(),n.Jb(22,0,null,0,3,"GridItem",[["height","100%"],["width","10%"]],null,null,null,p.Ac,p.I)),n.Ib(23,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(24,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["text","Entity"]],null,null,null,p.Yc,p.fb)),n.Ib(25,4440064,[[4,4],["entityLabel",4]],0,a.vb,[n.r,a.i],{styleName:[0,"styleName"],text:[1,"text"]},null),(t()(),n.Jb(26,0,null,0,5,"GridItem",[["height","100%"],["width","70%"]],null,null,null,p.Ac,p.I)),n.Ib(27,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(28,0,null,0,1,"SwtTextInput",[["editable","false"],["id","entityIdField"]],null,null,null,p.kd,p.sb)),n.Ib(29,4440064,[[6,4],["entityIdField",4]],0,a.Rb,[n.r,a.i],{id:[0,"id"],editable:[1,"editable"]},null),(t()(),n.Jb(30,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","entityNameLabel"],["paddingLeft","10"],["styleName","labelLeft"],["width","100%"]],null,null,null,p.Yc,p.fb)),n.Ib(31,4440064,[[5,4],["entityNameLabel",4]],0,a.vb,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],paddingLeft:[3,"paddingLeft"],fontWeight:[4,"fontWeight"]},null),(t()(),n.Jb(32,0,null,0,11,"GridRow",[["height","50%"],["paddingLeft","10"],["width","100%"]],null,null,null,p.Bc,p.J)),n.Ib(33,4440064,null,0,a.B,[n.r,a.i],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"]},null),(t()(),n.Jb(34,0,null,0,3,"GridItem",[["height","100%"],["width","10%"]],null,null,null,p.Ac,p.I)),n.Ib(35,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(36,0,null,0,1,"SwtLabel",[["styleName","labelBold"],["text","Account"]],null,null,null,p.Yc,p.fb)),n.Ib(37,4440064,[[2,4],["accountIdLabel",4]],0,a.vb,[n.r,a.i],{styleName:[0,"styleName"],text:[1,"text"]},null),(t()(),n.Jb(38,0,null,0,5,"GridItem",[["height","100%"],["width","70%"]],null,null,null,p.Ac,p.I)),n.Ib(39,4440064,null,0,a.A,[n.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(40,0,null,0,1,"SwtTextInput",[["editable","false"],["id","accountIdField"],["toolTip","{accountId_field.text}"],["width","220"]],null,null,null,p.kd,p.sb)),n.Ib(41,4440064,[[7,4],["accountIdField",4]],0,a.Rb,[n.r,a.i],{id:[0,"id"],toolTip:[1,"toolTip"],width:[2,"width"],editable:[3,"editable"]},null),(t()(),n.Jb(42,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","accountNameLabel"],["paddingLeft","10"],["styleName","labelLeft"],["width","100%"]],null,null,null,p.Yc,p.fb)),n.Ib(43,4440064,[[3,4],["accountNameLabel",4]],0,a.vb,[n.r,a.i],{id:[0,"id"],styleName:[1,"styleName"],width:[2,"width"],paddingLeft:[3,"paddingLeft"],fontWeight:[4,"fontWeight"]},null),(t()(),n.Jb(44,0,null,0,1,"SwtCanvas",[["height","78%"],["id","canvasGrid"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(45,4440064,[[8,4],["canvasGrid",4]],0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(46,0,null,0,13,"SwtCanvas",[["height","7%"],["id","canvasButtons"],["width","100%"]],null,null,null,p.Nc,p.U)),n.Ib(47,4440064,null,0,a.db,[n.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),n.Jb(48,0,null,0,11,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(49,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"]},null),(t()(),n.Jb(50,0,null,0,5,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),n.Ib(51,4440064,null,0,a.C,[n.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(52,0,null,0,1,"SwtButton",[["enabled","false"],["id","displayButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.displayButtonClickHandler(i)&&n);return n},p.Mc,p.T)),n.Ib(53,4440064,[[9,4],["displayButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(54,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.closeHandler()&&n);return n},p.Mc,p.T)),n.Ib(55,4440064,[[10,4],["closeButton",4]],0,a.cb,[n.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),n.Jb(56,0,null,0,3,"HBox",[["horizontalAlign","right"],["paddingRight","5"],["paddingTop","5"]],null,null,null,p.Dc,p.K)),n.Ib(57,4440064,null,0,a.C,[n.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingTop:[1,"paddingTop"],paddingRight:[2,"paddingRight"]},null),(t()(),n.Jb(58,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","groups-of-rules"],["id","helpIcon"]],null,[[null,"click"]],function(t,e,i){var n=!0,l=t.component;"click"===e&&(n=!1!==l.doHelp()&&n);return n},p.Wc,p.db)),n.Ib(59,4440064,null,0,a.rb,[n.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"})],function(t,e){t(e,11,0,"100%","100%");t(e,13,0,"100%","100%","5","5","5","5");t(e,15,0,"100%","15%");t(e,17,0,"100%","100%");t(e,19,0,"6","100%","100%");t(e,21,0,"100%","50%","10","10");t(e,23,0,"10%","100%");t(e,25,0,"labelBold","Entity");t(e,27,0,"70%","100%");t(e,29,0,"entityIdField","false");t(e,31,0,"entityNameLabel","labelLeft","100%","10","normal");t(e,33,0,"100%","50%","10");t(e,35,0,"10%","100%");t(e,37,0,"labelBold","Account");t(e,39,0,"70%","100%");t(e,41,0,"accountIdField","{accountId_field.text}","220","false");t(e,43,0,"accountNameLabel","labelLeft","100%","10","normal");t(e,45,0,"canvasGrid","100%","78%");t(e,47,0,"canvasButtons","100%","7%");t(e,49,0,"100%");t(e,51,0,"100%","5");t(e,53,0,"displayButton","false",!0);t(e,55,0,"closeButton",!0);t(e,57,0,"right","5","5");t(e,59,0,"helpIcon","true",!0,"groups-of-rules")},null)}function z(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-attribute-lastvalues",[],null,null,null,W,_)),n.Ib(1,4440064,null,0,u,[a.i,n.r],null,null)],function(t,e){t(e,1,0)},null)}var H=n.Fb("app-attribute-lastvalues",u,z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);