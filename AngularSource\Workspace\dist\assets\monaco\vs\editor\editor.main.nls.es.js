/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.18.1(d7a26172c5955d29d2a8cca4377b53b28925c766)
 * Released under the MIT license
 * https://github.com/Microsoft/vscode/blob/master/LICENSE.txt
 *-----------------------------------------------------------*/
define("vs/editor/editor.main.nls.es",{"vs/base/browser/ui/actionbar/actionbar":["{0} ({1})"],"vs/base/browser/ui/aria/aria":["{0} (ocurrió de nuevo)","{0} (ocurrido {1} veces)"],"vs/base/browser/ui/findinput/findInput":["Entrada"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Coincidir mayúsculas y minúsculas","Solo palabras completas","Usar expresión regular"],"vs/base/browser/ui/findinput/replaceInput":["Entrada","Conservar may/min"],"vs/base/browser/ui/inputbox/inputBox":["Error: {0}","Advertencia: {0}","Información: {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Sin enlazar"],"vs/base/browser/ui/list/listWidget":["{0}. Para navegar utilice las teclas de navegación."],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Borrar","Desactivar filtro en tipo","Activar filtro en el tipo","No se encontraron elementos","{0} de {1} elementos coincidentes"],
"vs/base/common/keybindingLabels":["Ctrl","Mayús","Alt","Windows","Ctrl","Mayús","Alt","Super","Control","Mayús","Alt","Comando","Control","Mayús","Alt","Windows","Control","Mayús","Alt","Super"],"vs/base/common/severity":["Error","Advertencia","Información"],"vs/base/parts/quickopen/browser/quickOpenModel":["{0}, selector","selector"],"vs/base/parts/quickopen/browser/quickOpenWidget":["Selector rápido. Escriba para restringir los resultados.","Selector rápido","{0} resultados"],"vs/editor/browser/controller/coreCommands":["&&Seleccionar todo","&&Deshacer","&&Rehacer"],"vs/editor/browser/widget/codeEditorWidget":["El número de cursores se ha limitado a {0}."],"vs/editor/browser/widget/diffEditorWidget":["Los archivos no se pueden comparar porque uno de ellos es demasiado grande."],
"vs/editor/browser/widget/diffReview":["Cerrar","sin líneas","1 línea","{0} líneas","Diferencia {0} de {1}: original {2}, {3}, modificado {4}, {5}","vacío","original {0}, modificado {1}: {2}","+ modificado {0}: {1}","- original {0}: {1}","Ir a la siguiente diferencia","Ir a la diferencia anterior"],"vs/editor/browser/widget/inlineDiffMargin":["Copiar líneas eliminadas","Copiar línea eliminada","Copiar la línea eliminada ({0})","Revertir este cambio","Copiar la línea eliminada ({0})"],
"vs/editor/common/config/commonEditorConfig":["Editor","Controla la familia de fuentes.","Controla el grosor de la fuente.","Controla el tamaño de fuente en píxeles.","Controla la altura de línea. Usa 0 para utilizar la altura del tamaño de fuente.","Controla el espacio entre letras en pixels.","Los números de línea no se muestran.","Los números de línea se muestran como un número absoluto.","Los números de línea se muestran como distancia en líneas a la posición del cursor.","Los números de línea se muestran cada 10 líneas.","Controla la visualización de los números de línea.",'Controla el número mínimo de líneas iniciales y finales visibles que rodean al cursor. Se conoce como "scrollOff" o "scrollOffset" en algunos otros editores.',"Representar el número de la última línea cuando el archivo termina con un salto de línea.","Muestra reglas verticales después de un cierto número de caracteres monoespaciados. Usa múltiples valores para mostrar múltiples reglas. Si la matriz está vacía, no se muestran reglas.","Caracteres que se usarán como separadores de palabras al realizar operaciones o navegaciones relacionadas con palabras.",'El número de espacios a los que equivale una tabulación. Este valor se invalida en función del contenido del archivo cuando "#editor.detectIndentation#" está activado.','Insertar espacios al presionar "TAB". Este valor se invalida en función del contenido del archivo cuando "#editor.detectIndentation#" está activado. ','Controla si "#editor.tabSize#" y "#editor.insertSpaces#" se detectarán automáticamente al abrir un archivo en función del contenido de este.',"Controla si las selecciones deberían tener las esquinas redondeadas.","Controla si el editor seguirá haciendo scroll después de la última línea.","Controla el número de caracteres adicionales a partir del cual el editor se desplazará horizontalmente.","Controla si el editor se desplazará con una animación.","Controla si se muestra el minimapa.","Controla en qué lado se muestra el minimapa.","Controla si el control deslizante del minimapa es ocultado automáticamente.","Represente los caracteres reales en una línea, por oposición a los bloques de color.","Limite el ancho del minimapa para representar como mucho un número de columnas determinado.","Controla si se muestra la información al mantener el puntero sobre un elemento.","Controla el retardo en milisegundos después del cual se muestra la información al mantener el puntero sobre un elemento.","Controla si la información que aparece al mantener el puntero sobre un elemento permanece visible al mover el mouse sobre este.","Controla si la cadena de búsqueda del widget de búsqueda se inicializa desde la selección del editor.","Controla si la operación de búsqueda se lleva a cabo en el texto seleccionado o el archivo entero en el editor.","Controla si el widget de búsqueda debe leer o modificar el Portapapeles de búsqueda compartido en macOS.","Controla si Encontrar widget debe agregar más líneas en la parte superior del editor. Si es true, puede desplazarse más allá de la primera línea cuando Encontrar widget está visible.","Las líneas no se ajustarán nunca.","Las líneas se ajustarán en el ancho de la ventanilla.",'Las líneas se ajustarán al valor de "#editor.wordWrapColumn#". ','Las líneas se ajustarán al valor que sea inferior: el tamaño de la ventanilla o el valor de "#editor.wordWrapColumn#".',"Controla cómo deben ajustarse las líneas.",'Controla la columna de ajuste del editor cuando "#editor.wordWrap#" es "wordWrapColumn" o "bounded".',"No hay sangría. Las líneas ajustadas comienzan en la columna 1.","A las líneas ajustadas se les aplica la misma sangría que al elemento primario.","A las líneas ajustadas se les aplica una sangría de +1 respecto al elemento primario.","A las líneas ajustadas se les aplica una sangría de +2 respecto al elemento primario.","Controla la sangría de las líneas ajustadas.",'Se usará un multiplicador en los eventos de desplazamiento de la rueda del mouse "deltaX" y "deltaY". ','Multiplicador de la velocidad de desplazamiento al presionar "Alt".','Se asigna a "Control" en Windows y Linux y a "Comando" en macOS.','Se asigna a "Alt" en Windows y Linux y a "Opción" en macOS.',"El modificador que se usará para agregar varios cursores con el mouse. Los gestos del mouse Ir a definición y Abrir vínculo se adaptarán de modo que no entren en conflicto con el modificador multicursor. [Más información](https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Combinar varios cursores cuando se solapan.","Habilita sugerencias rápidas en las cadenas.","Habilita sugerencias rápidas en los comentarios.","Habilita sugerencias rápidas fuera de las cadenas y los comentarios.","Controla si deben mostrarse sugerencias automáticamente mientras se escribe.","Controla el retraso, en milisegundos, tras el cual aparecerán sugerencias rápidas.","Habilita un elemento emergente que muestra documentación de los parámetros e información de los tipos mientras escribe.","Controla si el menú de sugerencias de parámetros se cicla o se cierra al llegar al final de la lista.","Utilizar las configuraciones del lenguaje para determinar cuándo cerrar los corchetes automáticamente.","Cerrar automáticamente los corchetes cuando el cursor esté a la izquierda de un espacio en blanco.","Controla si el editor debe cerrar automáticamente los corchetes después de que el usuario agregue un corchete de apertura.","Utilizar las configuraciones del lenguaje para determinar cuándo cerrar las comillas automáticamente. ","Cerrar automáticamente las comillas cuando el cursor esté a la izquierda de un espacio en blanco. ","Controla si el editor debe cerrar automáticamente las comillas después de que el usuario agrega uma comilla de apertura.","Escriba siempre entre comillas o corchetes.","Escriba en las comillas o los corchetes solo si se insertaron automáticamente.","No escriba nunca en comillas o corchetes.","Controla si el editor debe escribir entre comillas o corchetes.","Use las configuraciones de idioma para determinar cuándo delimitar las selecciones automáticamente.","Envolver con corchetes, pero no con comillas.","Envolver con comillas, pero no con corchetes.","Controla si el editor debe delimitar automáticamente las selecciones.","Controla si el editor debe dar formato a la línea automáticamente después de escribirla.","Controla si el editor debe dar formato automáticamente al contenido pegado. Debe haber disponible un formateador capaz de aplicar formato a un rango dentro de un documento. ","Controla si el editor debe ajustar automáticamente la sangría cuando los usuarios escriben, pegan o mueven líneas. Debe haber disponibles extensiones con las reglas de sangría del idioma.","Controla si deben aparecer sugerencias de forma automática al escribir caracteres desencadenadores.",'Aceptar solo una sugerencia con "Entrar" cuando realiza un cambio textual.','Controla si las sugerencias deben aceptarse con "Entrar", además de "TAB". Ayuda a evitar la ambigüedad entre insertar nuevas líneas o aceptar sugerencias.','Controla si se deben aceptar sugerencias en los caracteres de confirmación. Por ejemplo, en Javascript, el punto y coma (";") puede ser un carácter de confirmación que acepta una sugerencia y escribe ese carácter.',"Mostrar sugerencias de fragmentos de código por encima de otras sugerencias.","Mostrar sugerencias de fragmentos de código por debajo de otras sugerencias.","Mostrar sugerencias de fragmentos de código con otras sugerencias.","No mostrar sugerencias de fragmentos de código.","Controla si se muestran los fragmentos de código con otras sugerencias y cómo se ordenan.","Controla si al copiar sin selección se copia la línea actual.","Controla si el resaltado de sintaxis debe ser copiado al portapapeles.","Habilita sugerencias basadas en palabras.","Seleccionar siempre la primera sugerencia.",'Seleccione sugerencias recientes a menos que al escribir más se seleccione una, por ejemplo, "console.| -> console.log" porque "log" se ha completado recientemente.','Seleccione sugerencias basadas en prefijos anteriores que han completado esas sugerencias, por ejemplo, "co -> console" y "con -> const".',"Controla cómo se preseleccionan las sugerencias cuando se muestra la lista,","Tamaño de la fuente para el widget de sugerencias. Cuando se establece a `0`, se utilizará el valor `#editor.fontSize#`.","Altura de la línea del widget de sugerencias. Cuando se establece a `0`, se utiliza el valor `#editor.lineHeight#`.","La pestaña se completará insertando la mejor sugerencia de coincidencia encontrada al presionar la pestaña","Deshabilitar los complementos para pestañas.","La pestaña se completa con fragmentos de código cuando su prefijo coincide. Funciona mejor cuando las 'quickSuggestions' no están habilitadas.","Habilita completar pestañas.","Controla si el filtrado y la ordenación de sugerencias se tienen en cuenta para los errores ortográficos pequeños.","Controla si la ordenación de palabras mejora lo que aparece cerca del cursor.",'Controla si las selecciones de sugerencias recordadas se comparten entre múltiples áreas de trabajo y ventanas (necesita "#editor.suggestSelection#").',"Controla si un fragmento de código activo impide las sugerencias rápidas.","Controla si mostrar u ocultar iconos en sugerencias.","Controla cuántas sugerencias mostrará IntelliSense antes de que aparezca una barra de desplazamiento (máximo 15).","Controla si algunos tipos de sugerencias se deben filtrar desde IntelliSense. Se puede encontrar una lista de tipos de sugerencias aquí: https://code.visualstudio.com/docs/editor/intellisense#_types-of-completions.",'Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "method".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "function".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "constructor".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "field".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "variable".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "class".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "struct".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "interface".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "module".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "property".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "event".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "operator".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "unit".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "value".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "constant".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "enum".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "enumMember".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "keyword".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "text".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "color".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "file".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "reference".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "customcolor".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "folder".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "typeParameter".','Cuando se establece en "false", IntelliSense nunca muestra sugerencias de "snippet".','Controla el comportamiento de los comandos "Ir a", como Ir a la definición, cuando existen varias ubicaciones de destino.',"Mostrar vista de inspección de los resultados (predeterminado)","Ir al resultado principal y mostrar una vista de inspección","Vaya al resultado principal y habilite la navegación sin peek para otros","Controla si el editor debe destacar las coincidencias similares a la selección.","Controla si el editor debe resaltar las apariciones de símbolos semánticos.","Controla el número de decoraciones que pueden aparecer en la misma posición en la regla de información general.","Controla si debe dibujarse un borde alrededor de la regla de información general.","Controla el estilo de animación del cursor.",'Ampliar la fuente del editor cuando se use la rueda del mouse mientras se presiona "Ctrl".',"Controla si la animación suave del cursor debe estar habilitada.","Controla el estilo del cursor.",'Controla el ancho del cursor cuando "#editor.cursorStyle#" se establece en "line".',"Habilita o deshabilita las ligaduras tipográficas.","Controla si el cursor debe ocultarse en la regla de información general.","Render whitespace characters except for single spaces between words.","Represente los caracteres de espacio en blanco solo en el texto seleccionado.","Controla la forma en que el editor debe representar los caracteres de espacio en blanco.","Controla si el editor debe representar caracteres de control.","Controla si el editor debe representar guías de sangría.","Controla si el editor debe resaltar la guía de sangría activa.","Resalta el medianil y la línea actual.","Controla cómo debe representar el editor el resaltado de línea actual.","Controla si el editor muestra CodeLens.","Controla si el editor tiene el plegado de código habilitado.",'Controla la estrategia para calcular los intervalos de plegado. "auto" usa una estrategia de plegado específica del idioma, si está disponible. "indentation" usa la estrategia de plegado basada en sangría.',"Controla cuándo los controles de plegado del margen son ocultados automáticamente.","Resaltar corchetes coincidentes cuando se seleccione uno de ellos.","Controla si el editor debe representar el margen de glifo vertical. El margen de glifo se usa, principalmente, para depuración.","La inserción y eliminación del espacio en blanco sigue a las tabulaciones.","Quitar el espacio en blanco final autoinsertado.",'Mantiene abiertos los editores interactivos, incluso al hacer doble clic en su contenido o presionar "Escape".',"Controla si el editor debe permitir mover las selecciones mediante arrastrar y colocar.","El editor usará API de plataforma para detectar cuándo está conectado un lector de pantalla.","El editor se optimizará de forma permanente para su uso con un editor de pantalla.","El editor nunca se optimizará para su uso con un lector de pantalla.","Controla si el editor se debe ejecutar en un modo optimizado para lectores de pantalla.","Controla el fundido de salida del código no usado.","Controla si el editor debe detectar vínculos y hacerlos interactivos.","Controla si el editor debe representar el Selector de colores y los elementos Decorator de color en línea.","Habilita la bombilla de acción de código en el editor.","Las lineas por encima de esta longitud no se tokenizarán por razones de rendimiento.","Controla si la acción para organizar las importaciones debe ejecutarse al guardar el archivo.","Controla si la acción de reparación automática se debe ejecutar al guardar el archivo.","Tipos de acción de código que se ejecutarán en guardar.","Tiempo de espera, en milisegundos, transcurrido el cual se cancelan las acciones de código que se ejecutan al guardar.","Controla si el portapapeles principal de Linux debe admitirse.","Controla si el editor de diferencias muestra las diferencias en paralelo o alineadas.","Controla si el editor de diferencias muestra los cambios de espacio inicial o espacio final como diferencias.","Manejo especial para archivos grandes para desactivar ciertas funciones de memoria intensiva.","Controla si el editor de diferencias muestra los indicadores +/- para los cambios agregados o quitados."],
"vs/editor/common/config/editorOptions":["No se puede acceder al editor en este momento. Presione Alt+F1 para ver opciones.","Contenido del editor"],"vs/editor/common/modes/modesRegistry":["Texto sin formato"],
"vs/editor/common/standaloneStrings":["Sin selección","Línea {0}, columna {1} ({2} seleccionadas)","Línea {0}, columna {1}","{0} selecciones ({1} caracteres seleccionados)","{0} selecciones",'Se cambiará ahora el valor "accessibilitySupport" a "activado".',"Se abrirá ahora la página de documentación de accesibilidad del editor.","en un panel de solo lectura de un editor de diferencias.","en un panel de un editor de diferencias.","en un editor de código de solo lectura"," en un editor de código","Para configurar el editor de forma que se optimice su uso con un lector de pantalla, presione ahora Comando+E.","Para configurar el editor de forma que se optimice su uso con un lector de pantalla, presione ahora Control+E.","El editor está configurado para optimizarse para su uso con un lector de pantalla.","El editor está configurado para que no se optimice nunca su uso con un lector de pantalla, que en este momento no es el caso.","Al presionar TAB en el editor actual, el foco se mueve al siguiente elemento activable. Presione {0} para activar o desactivar este comportamiento.","Al presionar TAB en el editor actual, el foco se mueve al siguiente elemento activable. El comando {0} no se puede desencadenar actualmente mediante un enlace de teclado.","Al presionar TAB en el editor actual, se insertará el carácter de tabulación. Presione {0} para activar o desactivar este comportamiento.","Al presionar TAB en el editor actual, se insertará el carácter de tabulación. El comando {0} no se puede desencadenar actualmente mediante un enlace de teclado.","Presione ahora Comando+H para abrir una ventana del explorador con más información relacionada con la accesibilidad del editor.","Presione ahora Control+H para abrir una ventana del explorador con más información relacionada con la accesibilidad del editor.","Para descartar esta información sobre herramientas y volver al editor, presione Esc o Mayús+Escape.","Mostrar ayuda de accesibilidad","Desarrollador: inspeccionar tokens","Ir a la línea {0} y al carácter {1}","Ir a la línea {0}","Escriba un número de línea comprendido entre 1 y {0} a la cual quiera navegar.","Escriba un carácter entre 1 y {0} para ir a","Línea actual: {0}. ir a la línea {1}.","Escriba un número de línea, seguido de un signo opcional de dos puntos y un número de caracteres para desplazarse a","Ir a la línea...","{0}, {1}, comandos","{0}, comandos","Escriba el nombre de una acción que desee ejecutar","Paleta de comandos","{0}, símbolos","Escriba el nombre de un identificador al que quiera ir","Ir a símbolo...","símbolos ({0})","módulos ({0})","clases ({0})","interfaces ({0})","métodos ({0})","funciones ({0})","propiedades ({0})","variables ({0})","variables ({0})","constructores ({0})","llama a ({0})","Contenido del editor","Presione Ctrl+F1 para ver las opciones de accesibilidad.","Presione Alt+F1 para ver las opciones de accesibilidad.","Alternar tema de contraste alto","{0} ediciones realizadas en {1} archivos"],
"vs/editor/common/view/editorColorRegistry":["Color de fondo para la línea resaltada en la posición del cursor.","Color de fondo del borde alrededor de la línea en la posición del cursor.","Color de fondo de rangos resaltados, como en abrir rápido y encontrar características. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo del borde alrededor de los intervalos resaltados.","Color del cursor del editor.","Color de fondo del cursor de edición. Permite personalizar el color del caracter solapado por el bloque del cursor.","Color de los caracteres de espacio en blanco del editor.","Color de las guías de sangría del editor.","Color de las guías de sangría activas del editor.","Color de números de línea del editor.","Color del número de línea activa en el editor","ID es obsoleto. Usar en lugar 'editorLineNumber.activeForeground'. ","Color del número de línea activa en el editor","Color de las reglas del editor","Color principal de lentes de código en el editor","Color de fondo tras corchetes coincidentes","Color de bloques con corchetes coincidentes","Color del borde de la regla de visión general.","Color de fondo del margen del editor. Este espacio contiene los márgenes de glifos y los números de línea.","Color del borde de código fuente innecesario (sin usar) en el editor.","Opacidad de código fuente innecesario (sin usar) en el editor. Por ejemplo, \"#000000c0\" representará el código con un 75 % de opacidad. Para temas de alto contraste, utilice el color del tema 'editorUnnecessaryCode.border' para resaltar el código innecesario en vez de atenuarlo.","Color de marcador de regla de información general para errores. ","Color de marcador de regla de información general para advertencias.","Color de marcador de regla de información general para mensajes informativos. "],
"vs/editor/contrib/bracketMatching/bracketMatching":["Resumen color de marcador de regla para corchetes.","Ir al corchete","Seleccionar para corchete","Ir al &&corchete"],"vs/editor/contrib/caretOperations/caretOperations":["Mover símbolo de inserción a la izquierda","Mover símbolo de inserción a la derecha"],"vs/editor/contrib/caretOperations/transpose":["Transponer letras"],"vs/editor/contrib/clipboard/clipboard":["Cortar","Cor&&tar","Copiar","C&&opiar","Pegar","&&Pegar","Copiar con resaltado de sintaxis"],"vs/editor/contrib/codeAction/codeActionCommands":["Corrección Rápida","No hay acciones de código disponibles","No hay acciones de código disponibles","Refactorizar...","No hay refactorizaciones disponibles","Acción de Origen...","No hay acciones de origen disponibles","Organizar Importaciones","No hay acciones de importación disponibles","Corregir todo","No está disponible la acción de corregir todo","Corregir automáticamente...","No hay autocorrecciones disponibles"],
"vs/editor/contrib/codeAction/lightBulbWidget":["Mostrar correcciones ({0})","Mostrar correcciones"],"vs/editor/contrib/comment/comment":["Alternar comentario de línea","&&Alternar comentario de línea","Agregar comentario de línea","Quitar comentario de línea","Alternar comentario de bloque","Alternar &&bloque de comentario"],"vs/editor/contrib/contextmenu/contextmenu":["Mostrar menú contextual del editor"],"vs/editor/contrib/cursorUndo/cursorUndo":["Deshacer la última confirmación"],"vs/editor/contrib/find/findController":["Buscar","&&Buscar","Buscar con selección","Buscar siguiente","Buscar siguiente","Buscar anterior","Buscar anterior","Buscar selección siguiente","Buscar selección anterior","Reemplazar","&&Reemplazar"],
"vs/editor/contrib/find/findWidget":["Buscar","Buscar","Coincidencia anterior","Próxima coincidencia","Buscar en selección","Cerrar","Reemplazar","Reemplazar","Reemplazar","Reemplazar todo","Alternar modo de reemplazar","Sólo los primeros {0} resultados son resaltados, pero todas las operaciones de búsqueda trabajan en todo el texto.","{0} de {1}","No hay resultados","Encontrados: {0}","Encontrados: {0} para {1}","Encontrados: {0} para {1} en {2}","Encontrados: {0} para {1}","Ctrl+Entrar ahora inserta un salto de línea en lugar de reemplazar todo. Puede modificar el enlace de claves para editor.action.replaceAll para invalidar este comportamiento."],"vs/editor/contrib/folding/folding":["Desplegar","Desplegar de forma recursiva","Plegar","Plegar de forma recursiva","Cerrar todos los comentarios de bloque","Plegar todas las regiones","Desplegar Todas las Regiones","Plegar todo","Desplegar todo","Nivel de plegamiento {0}"],
"vs/editor/contrib/fontZoom/fontZoom":["Acercarse a la tipografía del editor","Alejarse de la tipografía del editor","Restablecer alejamiento de la tipografía del editor"],"vs/editor/contrib/format/format":["1 edición de formato en la línea {0}","{0} ediciones de formato en la línea {1}","1 edición de formato entre las líneas {0} y {1}","{0} ediciones de formato entre las líneas {1} y {2}"],"vs/editor/contrib/format/formatActions":["Dar formato al documento","Dar formato a la selección"],
"vs/editor/contrib/goToDefinition/goToDefinitionCommands":['No se encontró ninguna definición para "{0}"',"No se encontró ninguna definición"," – {0} definiciones","Ir a definición","Abrir definición en el lateral","Ver la definición","No se encontró ninguna definición para '{0}'","No se encontró ninguna declaración","– {0} declaraciones","Ir a Definición","No se encontró ninguna definición para '{0}'","No se encontró ninguna declaración","– {0} declaraciones","Inspeccionar Definición",'No se encontró ninguna implementación para "{0}"',"No se encontró ninguna implementación","– {0} implementaciones","Ir a implementación","Inspeccionar implementación",'No se encontró ninguna definición de tipo para "{0}"',"No se encontró ninguna definición de tipo"," – {0} definiciones de tipo","Ir a la definición de tipo","Inspeccionar definición de tipo","Ir a &&definición","Ir a la definición de &&tipo","Ir a la &&implementación"],
"vs/editor/contrib/goToDefinition/goToDefinitionMouse":["Haga clic para mostrar {0} definiciones."],"vs/editor/contrib/goToDefinition/goToDefinitionResultsNavigation":["Símbolo {0} de {1}, {2} para el siguiente","Símbolo {0} de {1}"],"vs/editor/contrib/gotoError/gotoError":["Ir al siguiente problema (Error, Advertencia, Información)","Ir al problema anterior (Error, Advertencia, Información)","Ir al siguiente problema en Archivos (Error, Advertencia, Información)","Ir al problema anterior en Archivos (Error, Advertencia, Información)","Siguiente &&problema","Anterior &&problema"],"vs/editor/contrib/gotoError/gotoErrorWidget":["{0} de {1} problemas","{0} de {1} problema","Color de los errores del widget de navegación de marcadores del editor.","Color de las advertencias del widget de navegación de marcadores del editor.","Color del widget informativo marcador de navegación en el editor.","Fondo del widget de navegación de marcadores del editor."],
"vs/editor/contrib/hover/hover":["Mostrar al mantener el puntero"],"vs/editor/contrib/hover/modesContentHover":["Cargando...","Problema de pico","Buscando correcciones rápidas...","No hay correcciones rápidas disponibles","Corrección Rápida"],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Reemplazar con el valor anterior","Reemplazar con el valor siguiente"],
"vs/editor/contrib/linesOperations/linesOperations":["Copiar línea arriba","&&Copiar línea arriba","Copiar línea abajo","Co&&piar línea abajo","Mover línea hacia arriba","Mo&&ver línea arriba","Mover línea hacia abajo","Mover &&línea abajo","Ordenar líneas en orden ascendente","Ordenar líneas en orden descendente","Recortar espacio final","Eliminar línea","Sangría de línea","Anular sangría de línea","Insertar línea arriba","Insertar línea debajo","Eliminar todo a la izquierda","Eliminar todo lo que está a la derecha","Unir líneas","Transponer caracteres alrededor del cursor","Transformar a mayúsculas","Transformar a minúsculas","Transformar en Title Case"],"vs/editor/contrib/links/links":["Ejecutar comando","Seguir vínculo","cmd + clic","ctrl + clic","opción + clic","alt + clic","No se pudo abrir este vínculo porque no tiene un formato correcto: {0}","No se pudo abrir este vínculo porque falta el destino.","Abrir vínculo"],
"vs/editor/contrib/message/messageController":["No se puede editar en un editor de sólo lectura"],"vs/editor/contrib/multicursor/multicursor":["Agregar cursor arriba","&&Agregar cursor arriba","Agregar cursor debajo","A&&gregar cursor abajo","Añadir cursores a finales de línea","Agregar c&&ursores a extremos de línea","Añadir cursores a la parte inferior","Añadir cursores a la parte superior","Agregar selección hasta la siguiente coincidencia de búsqueda","Agregar &&siguiente repetición","Agregar selección hasta la anterior coincidencia de búsqueda","Agregar r&&epetición anterior","Mover última selección hasta la siguiente coincidencia de búsqueda","Mover última selección hasta la anterior coincidencia de búsqueda","Seleccionar todas las repeticiones de coincidencia de búsqueda","Seleccionar todas las &&repeticiones","Cambiar todas las ocurrencias"],"vs/editor/contrib/parameterHints/parameterHints":["Sugerencias para parámetros Trigger"],
"vs/editor/contrib/parameterHints/parameterHintsWidget":["{0}, sugerencia"],"vs/editor/contrib/referenceSearch/peekViewWidget":["Cerrar"],"vs/editor/contrib/referenceSearch/referenceSearch":[" – {0} referencias","Inspeccionar Referencias"],"vs/editor/contrib/referenceSearch/referencesController":["Cargando..."],"vs/editor/contrib/referenceSearch/referencesModel":["símbolo en {0} linea {1} en la columna {2}","1 símbolo en {0}, ruta de acceso completa {1}","{0} símbolos en {1}, ruta de acceso completa {2}","No se encontraron resultados","Encontró 1 símbolo en {0}","Encontró {0} símbolos en {1}","Encontró {0} símbolos en {1} archivos"],"vs/editor/contrib/referenceSearch/referencesTree":["Error al resolver el archivo.","{0} referencias","{0} referencia"],
"vs/editor/contrib/referenceSearch/referencesWidget":["vista previa no disponible","Referencias","No hay resultados","Referencias","Color de fondo del área de título de la vista de inspección.","Color del título de la vista de inpección.","Color de la información del título de la vista de inspección.","Color de los bordes y la flecha de la vista de inspección.","Color de fondo de la lista de resultados de vista de inspección.","Color de primer plano de los nodos de inspección en la lista de resultados.","Color de primer plano de los archivos de inspección en la lista de resultados.","Color de fondo de la entrada seleccionada en la lista de resultados de vista de inspección.","Color de primer plano de la entrada seleccionada en la lista de resultados de vista de inspección.","Color de fondo del editor de vista de inspección.","Color de fondo del margen en el editor de vista de inspección.","Buscar coincidencia con el color de resaltado de la lista de resultados de vista de inspección.","Buscar coincidencia del color de resultado del editor de vista de inspección.","Hacer coincidir el borde resaltado en el editor de vista previa."],
"vs/editor/contrib/rename/rename":["No hay ningún resultado.","Error desconocido al resolver el cambio de nombre de la ubicación","Nombre cambiado correctamente de '{0}' a '{1}'. Resumen: {2}","No se pudo cambiar el nombre.","Cambiar el nombre del símbolo"],"vs/editor/contrib/rename/renameInputField":["Cambie el nombre de la entrada. Escriba el nuevo nombre y presione Entrar para confirmar."],"vs/editor/contrib/smartSelect/smartSelect":["Expandir selección","&&Expandir selección","Reducir la selección","&&Reducir selección"],"vs/editor/contrib/snippet/snippetVariables":["Domingo","Lunes","Martes","Miércoles","Jueves","Viernes","Sábado","Dom","Lun","Mar","Mié","Jue","Vie","Sáb","Enero","Febrero","Marzo","Abril","May","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre","Ene","Feb","Mar","Abr","May","Jun","Jul","Ago","Sep","Oct","Nov","Dic"],"vs/editor/contrib/suggest/suggestController":['Aceptando "{0}" ediciones adicionales de {1} realizadas',"Sugerencias para Trigger"],
"vs/editor/contrib/suggest/suggestWidget":["Color de fondo del widget sugerido.","Color de borde del widget sugerido.","Color de primer plano del widget sugerido.","Color de fondo de la entrada seleccionada del widget sugerido.","Color del resaltado coincidido en el widget sugerido.","Leer más...{0}","Leer menos...{0}","Cargando...","Cargando...","No hay sugerencias.","Elemento {0}, documentos: {1}"],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["Alternar tecla de tabulación para mover el punto de atención","Presionando la pestaña ahora moverá el foco al siguiente elemento enfocable.","Presionando la pestaña ahora insertará el carácter de tabulación"],"vs/editor/contrib/tokenization/tokenization":["Desarrollador: forzar nueva aplicación de token"],
"vs/editor/contrib/wordHighlighter/wordHighlighter":["Color de fondo de un símbolo durante el acceso de lectura, como la lectura de una variable. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo de un símbolo durante el acceso de escritura, como escribir en una variable. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo de un símbolo durante el acceso de lectura; por ejemplo, cuando se lee una variable.","Color de fondo de un símbolo durante el acceso de escritura; por ejemplo, cuando se escribe una variable.","Color del marcador de regla general para destacados de símbolos. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de marcador de regla general para destacados de símbolos de acceso de escritura. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Ir al siguiente símbolo destacado","Ir al símbolo destacado anterior","Desencadenar los símbolos destacados"],
"vs/platform/configuration/common/configurationRegistry":["La configuración predeterminada se reemplaza","Establecer los valores de configuración que se reemplazarán para un lenguaje.",'No se puede registrar "{0}". Coincide con el patrón de propiedad \'\\\\[.*\\\\]$\' para describir la configuración del editor específica del lenguaje. Utilice la contribución "configurationDefaults".','No se puede registrar "{0}". Esta propiedad ya está registrada.'],"vs/platform/keybinding/common/abstractKeybindingService":["Se presionó ({0}). Esperando la siguiente tecla...","La combinación de teclas ({0}, {1}) no es ningún comando."],
"vs/platform/list/browser/listService":["Área de trabajo",'Se asigna a "Control" en Windows y Linux y a "Comando" en macOS.','Se asigna a "Alt" en Windows y Linux y a "Opción" en macOS.',"El modificador que se utilizará para agregar un elemento en los árboles y listas para una selección múltiple con el ratón (por ejemplo en el explorador, abiertos editores y vista de scm). Los gestos de ratón 'Abrir hacia' - si están soportados - se adaptarán de forma tal que no tenga conflicto con el modificador múltiple.","Controla cómo abrir elementos en árboles y listas usando el ratón (si está soportado). Para elementos padres con hijos en los árboles, esta configuración controlará si de un solo click o un doble click expande al elemento padre. Tenga en cuenta que algunos árboles y listas pueden optar por ignorar esta configuración si no se aplica.","Controla si las listas y los árboles admiten el desplazamiento horizontal en el área de trabajo.","Controla el esplazamiento horizontal de los árboles en la mesa de trabajo.",'Esta configuración está obsoleta, utilice "{0}" en su lugar.',"Controla la sangría de árbol en píxeles.","Controla si el árbol debe representar guías de sangría.","La navegación simple del teclado se centra en elementos que coinciden con la entrada del teclado. El emparejamiento se hace solo en prefijos.","Destacar la navegación del teclado resalta los elementos que coinciden con la entrada del teclado. Más arriba y abajo la navegación atravesará solo los elementos destacados.","La navegación mediante el teclado de filtro filtrará y ocultará todos los elementos que no coincidan con la entrada del teclado.","Controla el estilo de navegación del teclado para listas y árboles en el área de trabajo. Puede ser simple, resaltar y filtrar.",'Controla si la navegación del teclado en listas y árboles se activa automáticamente simplemente escribiendo. Si se establece en "false", la navegación con el teclado solo se activa al ejecutar el comando "list.toggleKeyboardNavigation", para el cual puede asignar un método abreviado de teclado.'],
"vs/platform/markers/common/markers":["Error","Advertencia","Información"],
"vs/platform/theme/common/colorRegistry":["Color de primer plano general. Este color solo se usa si un componente no lo invalida.","Color de primer plano general para los mensajes de erroe. Este color solo se usa si un componente no lo invalida.","Color de borde de los elementos con foco. Este color solo se usa si un componente no lo invalida.","Un borde adicional alrededor de los elementos para separarlos unos de otros y así mejorar el contraste.","Un borde adicional alrededor de los elementos activos para separarlos unos de otros y así mejorar el contraste.","Color de primer plano para los vínculos en el texto.","Color de fondo para los bloques de código en el texto.","Color de sombra de los widgets  dentro del editor, como buscar/reemplazar","Fondo de cuadro de entrada.","Primer plano de cuadro de entrada.","Borde de cuadro de entrada.","Color de borde de opciones activadas en campos de entrada.","Color de fondo de las opciones activadas en los campos de entrada.","Color de fondo de validación de entrada para gravedad de información.","Color de primer plano de validación de entrada para información de gravedad.","Color de borde de validación de entrada para gravedad de información.","Color de fondo de validación de entrada para gravedad de advertencia.","Color de primer plano de validación de entrada para información de advertencia.","Color de borde de validación de entrada para gravedad de advertencia.","Color de fondo de validación de entrada para gravedad de error.","Color de primer plano de validación de entrada para información de error.","Color de borde de valdación de entrada para gravedad de error.","Fondo de lista desplegable.","Primer plano de lista desplegable.","Color de fondo de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de fondo de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de fondo de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de primer plano de la lista o el árbol del elemento con el foco cuando la lista o el árbol están activos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de fondo de la lista o el árbol del elemento seleccionado cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, cuando están inactivos no.","Color de primer plano de la lista o el árbol del elemento con el foco cuando la lista o el árbol esta inactiva. Una lista o un árbol tiene el foco del teclado cuando está activo, cuando esta inactiva no.","Color de fondo de la lista o el árbol del elemento con el foco cuando la lista o el árbol están inactivos. Una lista o un árbol tienen el foco del teclado cuando están activos, pero no cuando están inactivos.","Fondo de la lista o el árbol al mantener el mouse sobre los elementos.","Color de primer plano de la lista o el árbol al pasar por encima de los elementos con el ratón.","Fondo de arrastrar y colocar la lista o el árbol al mover los elementos con el mouse.","Color de primer plano de la lista o el árbol de las coincidencias resaltadas al buscar dentro de la lista o el ábol.","Color de fondo del widget de filtro de tipo en listas y árboles.","Color de contorno del widget de filtro de tipo en listas y árboles.","Color de contorno del widget de filtro de tipo en listas y árboles, cuando no hay coincidencias.","Color de trazo de árbol para las guías de sangría.","Selector de color rápido para la agrupación de etiquetas.","Selector de color rápido para la agrupación de bordes.","Color de fondo de la insignia. Las insignias son pequeñas etiquetas de información, por ejemplo los resultados de un número de resultados.","Color de fondo de la insignia. Las insignias son pequeñas etiquetas de información, por ejemplo los resultados de un número de resultados.","Sombra de la barra de desplazamiento indica que la vista se ha despazado.","Color de fondo de control deslizante de barra de desplazamiento.","Color de fondo de barra de desplazamiento cursor cuando se pasar sobre el control.","Color de fondo de la barra de desplazamiento al hacer clic.","Color de fondo para la barra de progreso que se puede mostrar para las operaciones de larga duración.","Color del borde de los menús.","Color de primer plano de los elementos de menú.","Color de fondo de los elementos de menú. ","Color de primer plano del menu para el elemento del menú seleccionado.","Color de fondo del menu para el elemento del menú seleccionado.","Color del borde del elemento seleccionado en los menús.","Color del separador del menu para un elemento del menú.","Color de primer plano de squigglies de error en el editor.","Color del borde de los cuadros de error en el editor.","Color de primer plano de squigglies de advertencia en el editor.","Color del borde de los cuadros de advertencia en el editor.","Color de primer plano de los subrayados ondulados informativos en el editor.","Color del borde de los cuadros de información en el editor.","Color de primer plano de pista squigglies en el editor.","Color del borde de los cuadros de sugerencia en el editor.","Color de fondo del editor.","Color de primer plano predeterminado del editor.","Color de fondo del editor de widgets como buscar/reemplazar","Color de primer plano de los widgets del editor, como buscar y reemplazar.","Color de borde de los widgets del editor. El color solo se usa si el widget elige tener un borde y no invalida el color.","Color del borde de la barra de cambio de tamaño de los widgets del editor. El color se utiliza solo si el widget elige tener un borde de cambio de tamaño y si un widget no invalida el color.","Color de la selección del editor.","Color del texto seleccionado para alto contraste.","Color de la selección en un editor inactivo. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color en las regiones con el mismo contenido que la selección. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de borde de las regiones con el mismo contenido que la selección.","Color de la coincidencia de búsqueda actual.","Color de los otros resultados de la búsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de la gama que limita la búsqueda. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de borde de la coincidencia de búsqueda actual.","Color de borde de otra búsqueda que coincide.","Color del borde de la gama que limita la búsqueda. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Destacar debajo de la palabra para la que se muestra un mensaje al mantener el mouse. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de fondo al mantener el puntero en el editor.","Color del borde al mantener el puntero en el editor.","Color de fondo de la barra de estado al mantener el puntero en el editor.","Color de los vínculos activos.","Color de fondo para el texto que se insertó. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de fondo para el texto que se eliminó. El color no debe ser opaco para no ocultar decoraciones subyacentes.","Color de contorno para el texto insertado.","Color de contorno para el texto quitado.","Color del borde entre ambos editores de texto.","Resaltado del color de fondo para una ficha de un fragmento de código.","Resaltado del color del borde para una ficha de un fragmento de código.","Resaltado del color de fondo para la última ficha de un fragmento de código.","Resaltado del color del borde para la última ficha de un fragmento de código.","Color del marcador de regla general para buscar actualizaciones. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color del marcador de la regla general para los destacados de la selección. El color no debe ser opaco para no ocultar las decoraciones subyacentes.","Color de marcador de minimapa para coincidencias de búsqueda."]
});
//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.es.js.map