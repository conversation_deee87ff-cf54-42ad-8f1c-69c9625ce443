{"_from": "@types/dragula@^2.1.34", "_id": "@types/dragula@2.1.39", "_inBundle": false, "_integrity": "sha512-MC6C61MJ1+RZjyFujDXgLoslsFnY19Zoo5p13PNDU5X/t+gPychd2onam9IGHSc3Al36yw3jd4mcIaTX95RQCA==", "_location": "/@types/dragula", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/dragula@^2.1.34", "name": "@types/dragula", "escapedName": "@types%2fdragula", "scope": "@types", "rawSpec": "^2.1.34", "saveSpec": null, "fetchSpec": "^2.1.34"}, "_requiredBy": ["/ng2-dragula"], "_resolved": "https://registry.npmjs.org/@types/dragula/-/dragula-2.1.39.tgz", "_shasum": "adf718ac78594f33562651d591f22a2575559b18", "_spec": "@types/dragula@^2.1.34", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\ng2-dragula", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/pwelter34"}, {"name": "<PERSON>", "url": "https://github.com/abru<PERSON><PERSON>ig"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for dragula", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/dragula", "license": "MIT", "main": "", "name": "@types/dragula", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/dragula"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "bc7fc2944176f7ef9574ce1c86c01900c9ea34ee9f9d797e762adfd9877f88e3", "version": "2.1.39"}