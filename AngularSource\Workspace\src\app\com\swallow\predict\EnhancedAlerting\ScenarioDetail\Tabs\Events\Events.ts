import {Component, ElementRef, ViewChild, OnInit} from '@angular/core';
import {
  SwtAlert,
  SwtUtil,
  CommonService,
  SwtModule,
  SwtLabel,
  SwtNumericInput,
  SwtCanvas,
  SwtButton,
  SwtRadioButtonGroup,
  SwtRadioItem,
  SwtTextArea,
  SwtCommonGrid, ExternalInterface, SwtPopUpManager, XML, SwtMultiselectCombobox, SwtImage
} from 'swt-tool-box';
import { AttributeXML } from '../../../../AlertInstanceSummary/AttributeXML/AttributeXML';
declare var instanceElement2: any;
declare var require: any;
var parser = require('fast-xml-parser');
var prettyData = require('pretty-data');
@Component({
  selector: 'app-events',
  templateUrl: './Events.html',
  styleUrls: ['./Events.css']
})
export class Events extends SwtModule implements OnInit {

  @ViewChild('eventCanvas') eventCanvas: SwtCanvas;

  @ViewChild('imgUpButton') imgUpButton: SwtButton;
  @ViewChild('imgDownButton') imgDownButton: SwtButton;
  @ViewChild('addButton') addButton: SwtButton;
  @ViewChild('changeButton') changeButton: SwtButton; 
  @ViewChild('viewButton') viewButton: SwtButton; 
  @ViewChild('deleteButton') deleteButton: SwtButton;

  @ViewChild('afterLaunchLbl') afterLaunchLbl: SwtLabel;
  @ViewChild('resolutionQueryLbl') resolutionQueryLbl: SwtLabel;
  @ViewChild('refColumnLbl') refColumnLbl: SwtLabel;
  @ViewChild('resolutionLbl') resolutionLbl: SwtLabel;
  @ViewChild('minsLbl') minsLbl: SwtLabel;

  @ViewChild('triggeEventsRadioGroup') triggeEventsRadioGroup: SwtRadioButtonGroup;
  @ViewChild('radioActive') radioActive: SwtRadioItem;
  @ViewChild('radioIResolved') radioIResolved: SwtRadioItem;
  @ViewChild('radioPending') radioPending: SwtRadioItem;

  @ViewChild('refColumnCombo') refColumnCombo: SwtMultiselectCombobox;
  @ViewChild('resolutionQueryText') resolutionQueryText: SwtTextArea;

  @ViewChild('minsText') minsText: SwtNumericInput;

  @ViewChild('resolOverdueRadioGroup') resolOverdueRadioGroup: SwtRadioButtonGroup;
  @ViewChild('radioNever') radioNever: SwtRadioItem;
  @ViewChild('radioAfter') radioAfter: SwtRadioItem;

  @ViewChild('BtnLockImg') BtnLockImg: SwtImage;

  private baseURL = SwtUtil.getBaseURL();
  private swtalert: SwtAlert;
  public eventsGrid : SwtCommonGrid;
  public operation: string;
  private win: any;
  public gridEventXML;
  public eventXml;
  public unlockFlag=false;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.eventsGrid = <SwtCommonGrid>this.eventCanvas.addChild(SwtCommonGrid);
    this.addButton.label = SwtUtil.getPredictMessage("button.add", null);
    this.changeButton.label = SwtUtil.getPredictMessage("button.change", null);
    this.viewButton.label = SwtUtil.getPredictMessage("button.view", null);
    this.deleteButton.label = SwtUtil.getPredictMessage("button.delete", null);
    this.afterLaunchLbl.text = SwtUtil.getPredictMessage("scenario.events.afterLaunch", null);
    this.radioActive.label = SwtUtil.getPredictMessage("scenario.events.remainactive", null);
    this.radioIResolved.label = SwtUtil.getPredictMessage("scenario.events.resolved", null);
    this.radioPending.label = SwtUtil.getPredictMessage("scenario.events.pending", null);
    this.refColumnLbl.htmlText = SwtUtil.getPredictMessage("scenario.events.refColumn", null);
    this.resolutionQueryLbl.text = SwtUtil.getPredictMessage("scenario.events.resolutionQuery", null);
    this.resolutionLbl.text = SwtUtil.getPredictMessage("scenario.events.resolutionAfter", null);
    this.minsLbl.text = SwtUtil.getPredictMessage("scenario.events.mins", null);
    this.radioNever.label = SwtUtil.getPredictMessage('scenario.events.never', null);
    this.radioAfter.label = SwtUtil.getPredictMessage('scenario.events.after', null);
    //tooltips parts
    this.addButton.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.add", null);
    this.changeButton.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.change", null);
    this.viewButton.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.view", null);
    this.deleteButton.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.delete", null);
    this.radioActive.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.remainactive", null);
    this.radioIResolved.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.resolved", null);
    this.radioPending.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.pending", null);
    this.refColumnCombo.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.refColumn", null);
    this.resolutionQueryText.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.resolutionQuery", null);
    this.minsText.toolTip = SwtUtil.getPredictMessage("scenario.events.tooltip.mins", null);
    this.radioNever.toolTip = SwtUtil.getPredictMessage('scenario.events.tooltip.never', null);
    this.radioAfter.toolTip = SwtUtil.getPredictMessage('scenario.events.tooltip.after', null);
    this.refColumnCombo.placeholder="Select columns";
    /**MultiSelectCombo item limit */
      this.refColumnCombo.itemLimit=5;
    
  }
  public static ngOnDestroy(): any {
    instanceElement2 = null;
  }
  onLoad() {
    instanceElement2 = this;
    this.eventsGrid.onRowClick = () => {
      this.rowClick()
    }

    this.eventsGrid.ITEM_CLICK.subscribe((selectedRowData) => {
      this.cellLogic(selectedRowData);
    });
    /*this.refColumnCombo.ITEM_SELECT.subscribe((target) => {
      if (this.getTooltip(this.refColumnCombo.selects).length>40){
        this.refColumnCombo.itemLimit=2;
      }else{
        this.refColumnCombo.itemLimit=3;
      }
    });*/
  }
  rowClick() {
    //local variables
    let selectedIndex: number=0;
    let count: number=0;
    let currentSelectedItem: any;
    let prevItem: any;
    let nextItem: any;
    if(this.eventsGrid.selectedItem && this.eventsGrid.selectedItem.eventParams && this.eventsGrid.selectedItem.eventParams.code){
      this.gridEventXML = this.eventsGrid.selectedItem.eventParams.code;
    }
    else if (this.eventsGrid.selectedItem && this.eventsGrid.selectedItem.xmlColumn) {
      this.gridEventXML = this.eventsGrid.selectedItem.xmlColumn.content

    }
    if(this.eventsGrid.selectedIndex >= 0 && this.parentDocument.methodName != "view") {
      if(this.parentDocument.methodName == "change" && !this.unlockFlag){
      this.changeButton.enabled = false;
      this.deleteButton.enabled = false;
      }else{
      this.changeButton.enabled = true;
      this.deleteButton.enabled = true; 
      }
      this.viewButton.enabled= true;
      selectedIndex=this.eventsGrid.selectedIndex;
      count=this.eventsGrid.dataProvider.length;
      currentSelectedItem=this.eventsGrid.dataProvider[selectedIndex];
      if (currentSelectedItem )
      {
        if ((selectedIndex + 1) < count)
        {
          nextItem=this.eventsGrid.dataProvider[selectedIndex + 1];
        }
        if ((selectedIndex - 1) >= 0)
        {
          prevItem=this.eventsGrid.dataProvider[selectedIndex - 1];
        }

        this.imgUpButton.enabled=prevItem ;
        this.imgDownButton.enabled=nextItem ;
      }
      else
      {
        this.imgDownButton.enabled=false;
        this.imgUpButton.enabled=false;
      }
    } else {
      this.eventsGrid.selectedIndex=-1;
      this.changeButton.enabled = false;
      this.viewButton.enabled= false;
      this.deleteButton.enabled= false;
      this.imgUpButton.enabled=false;
      this.imgDownButton.enabled=false;
    }
  }

  addEvHandler() {
    this.operation = "add";
    ExternalInterface.call('subEvents', "add");
  }
  changeEvHandler() {
    this.operation = "change";
    ExternalInterface.call('subEvents', "change");
  }

  viewEvHandler() {
    this.operation = "view";
    ExternalInterface.call('subEvents', "view");
  }


  deleteEvHandler() {
    this.eventsGrid.removeSelected();
    //enable triggeEventsRadioGroup only when event grid has at least one record
    if (this.eventsGrid.gridData && this.eventsGrid.gridData.length > 0) {
      this.triggeEventsRadioGroup.enabled = true;
    } else {
      this.triggeEventsRadioGroup.enabled = false;
    }
    //decrement event seq in case of delete
    for (let i = 0; i < this.eventsGrid.gridData.length; i++) {

      this.eventsGrid.gridData[i].eventSeq = (i + 1).toString();
      this.eventsGrid.gridData[i].slickgrid_rowcontent.eventSeq.content = (i + 1).toString();
    }
    this.eventsGrid.refresh();
    this.imgUpButton.enabled=false;
    this.imgDownButton.enabled=false;
    this.changeButton.enabled = false;
    this.viewButton.enabled= false;
    this.deleteButton.enabled= false;
  }
  sendEventDataToSub() {
    let eventFacilityArray = [];
    for(let i =0; i< this.eventsGrid.dataProvider.length; i++) {
      eventFacilityArray.push(this.eventsGrid.dataProvider[i].eventId);
    }
    let params: any;
    if(this.operation =="add") {
      params = {
        "parentMethodName":this.parentDocument.methodName,
        "mapFromOptions": this.parentDocument.jsonReader.getSelects()['select'].find(x => x.id == "mapFromComboVal"),
        "listMapFrom":this.parentDocument.gridComboVal,
        "listTxtMapFrom":this.parentDocument.gridComboTxtVal,
        "listNbrMapFrom":this.parentDocument.gridComboNbrVal,
        "listDateMapFrom":this.parentDocument.gridComboDateVal,
        "gridData": this.parentDocument.lastRecievedJSON.scenarioDetails.subEventGrid,
        "emailGridData": this.parentDocument.lastRecievedJSON.scenarioDetails.emailGrid,
        "messageFormatsList": this.parentDocument.jsonReader.getSelects()['select'].find(x => x.id == "messageFormats"),
        "operation": this.operation,
        "scenarioId": ((this.parentDocument.scenarioIdTxt.text ? this.parentDocument.scenarioIdTxt.text : "")),
        "eventFacilityList": this.parentDocument.jsonReader.getSelects()['select'].find(x => x.id == "guiEventFacilityList"),
        "executeWhenList": this.parentDocument.jsonReader.getSelects()['select'].find(x => x.id == "executeWhenList"),
        "eventFacilityArray": eventFacilityArray,
        "eventSequence": this.eventsGrid.dataProvider.length +1
      };
    } else {
      let selectedFac = this.eventsGrid.selectedItem.eventId;
      eventFacilityArray =  eventFacilityArray.filter(function(ele){ return ele != selectedFac });

      params = {
        "parentMethodName":this.parentDocument.methodName,
        "mapFromOptions": this.parentDocument.jsonReader.getSelects()['select'].find(x => x.id == "mapFromComboVal"),
        "listMapFrom":this.parentDocument.gridComboVal,
        "listTxtMapFrom":this.parentDocument.gridComboTxtVal,
        "listNbrMapFrom":this.parentDocument.gridComboNbrVal,
        "listDateMapFrom":this.parentDocument.gridComboDateVal,
        "gridData": this.parentDocument.lastRecievedJSON.scenarioDetails.subEventGrid,
        "emailGridData": this.parentDocument.lastRecievedJSON.scenarioDetails.emailGrid,
        "operation": this.operation,
        "scenarioId": ((this.parentDocument.scenarioIdTxt.text ? this.parentDocument.scenarioIdTxt.text : "")),
        "eventFacilityList": this.parentDocument.jsonReader.getSelects()['select'].find(x => x.id == "guiEventFacilityList"),
        "executeWhenList": this.parentDocument.jsonReader.getSelects()['select'].find(x => x.id == "executeWhenList"),
        "messageFormatsList": this.parentDocument.jsonReader.getSelects()['select'].find(x => x.id == "messageFormats"),
        "selectedEventId": this.eventsGrid.selectedItem.eventId? this.eventsGrid.selectedItem.eventId.content:"",
        "userDescription": this.eventsGrid.selectedItem.eventDescription?this.eventsGrid.selectedItem.eventDescription.content:"",
        "parameterXML": this.eventsGrid.selectedItem.eventParams.code?this.eventsGrid.selectedItem.eventParams.code:this.eventsGrid.selectedItem.eventParams.content,
        "allowRepeat": this.eventsGrid.selectedItem.eventRepeat.content,
        "selectedExecuteWhen": this.eventsGrid.selectedItem.eventExecuteWhen.content,
        "eventFacilityArray":eventFacilityArray,
        "eventSequence": this.eventsGrid.selectedIndex +1 // this.eventsGrid.selectedItem.eventSeq.content
      };
    }
    return params;
  }
  refreshParent(id: string, description: string, xml: string, allowRepeat: string, executeWhen: string ) {
    this.gridEventXML=xml;
    if(this.operation =="add" && id != 'SEND_MESSAGE') {
      let item: any;
      item = {
        'facility' : {'content':id},
        'eventSeq': {'content': this.eventsGrid.dataProvider.length +1},
        'eventId' : {'content':id},
        'eventDescription': {'content': description},
        'eventParams': {'negative': false, 'code': String(xml), 'clickable': true, 'content': "XML"},
        'eventRepeat': {'content': allowRepeat},
        'xmlColumn': {'content': String(xml)},
        'eventExecuteWhen': {'content': executeWhen}
      };
      this.eventsGrid.appendRow(item, false,false);
      this.eventsGrid.refresh();
      this.changeButton.enabled=true;
      this.viewButton.enabled=true;
      this.deleteButton.enabled=true;
      this.imgUpButton.enabled=this.eventsGrid.gridData.length>1;
      this.imgDownButton.enabled=false;

    } else if(this.operation =="add" && id == 'SEND_MESSAGE') {
      let item: any;
      item = {
        'facility' : {'content':id},
        'eventSeq': {'content': this.eventsGrid.dataProvider.length +1},
        'eventId' : {'content':id},
        'eventDescription': {'content': description},
        'eventParams': {'negative': false, 'clickable': false, 'content': xml},
        'eventRepeat': {'content': allowRepeat},
        'xmlColumn': {'content': String(xml)},
        'eventExecuteWhen': {'content': executeWhen}
      };
      this.eventsGrid.appendRow(item, false,false);
      this.eventsGrid.refresh();      
      this.changeButton.enabled=true;
      this.viewButton.enabled=true;
      this.deleteButton.enabled=true;     
      this.imgUpButton.enabled = this.eventsGrid.gridData.length>1;
      this.imgDownButton.enabled = false;
      
    }else if (this.operation =="change" && id == 'SEND_MESSAGE'){
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventId= String(id);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventDescription= String(description);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventId.content= String(id);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventDescription.content= String(description);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventParams= xml;
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventParams.content= xml;
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].xmlColumn= xml;
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventRepeat= String(allowRepeat);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventRepeat.content= String(allowRepeat);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventExecuteWhen= String(executeWhen);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventExecuteWhen.content= String(executeWhen);
      this.eventsGrid.refresh();
    }else {
     /* this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventSeq= String(idDescription);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventSeq.content= String(idDescription);*/
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventId= String(id);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventDescription= String(description);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventId.content= String(id);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventDescription.content= String(description);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventParams= "XML";
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventParams.content= "XML";
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventParams.code= String(xml);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].xmlColumn= String(xml);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventRepeat= String(allowRepeat);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventRepeat.content= String(allowRepeat);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].eventExecuteWhen= String(executeWhen);
      this.eventsGrid.dataProvider[this.eventsGrid.selectedIndex].slickgrid_rowcontent.eventExecuteWhen.content= String(executeWhen);
      this.eventsGrid.refresh();
    }
    //enable triggeEventsRadioGroup only when event grid has at least one record
    if (this.eventsGrid.gridData && this.eventsGrid.gridData.length > 0) {
      this.triggeEventsRadioGroup.enabled = true;
    } else {
      this.triggeEventsRadioGroup.enabled = false;
    }
  }
  moveRecord(buttonPressed: string):void {

    let json: any;
    let gridJSON: any;
    let selectedIndex: number=this.eventsGrid.selectedIndex;
    this.parentDocument.eventGridJSON.row = [];

    for(let i=0; i< this.eventsGrid.dataProvider.length; i++) {
      this.parentDocument.eventGridJSON.row.push(this.eventsGrid.dataProvider[i]);
    }
    
    if (buttonPressed == "imgUpButton") {
      this.eventsGrid.updateRow(this.eventsGrid.selectedIndex, "eventSeq", this.eventsGrid.selectedIndex);
      this.eventsGrid.updateRow(this.eventsGrid.selectedIndex-1, "eventSeq", this.eventsGrid.selectedIndex+1);
      json= this.parentDocument.eventGridJSON.row[selectedIndex];
      this.parentDocument.eventGridJSON.row[selectedIndex]=this.parentDocument.eventGridJSON.row[selectedIndex - 1];
      this.parentDocument.eventGridJSON.row[selectedIndex - 1] = json;
       gridJSON = this.parentDocument.eventGridJSON;
      this.eventsGrid.gridData = gridJSON;
      this.eventsGrid.selectedIndex = selectedIndex - 1;
      this.eventsGrid.selectedItem = this.parentDocument.eventGridJSON.row[selectedIndex -1].slickgrid_rowcontent;
    } else {
      this.eventsGrid.updateRow(this.eventsGrid.selectedIndex, "eventSeq", this.eventsGrid.selectedIndex+2);
      this.eventsGrid.updateRow(this.eventsGrid.selectedIndex+1, "eventSeq", this.eventsGrid.selectedIndex+1);
      json=this.parentDocument.eventGridJSON.row[selectedIndex];
      this.parentDocument.eventGridJSON.row[selectedIndex]=this.parentDocument.eventGridJSON.row[selectedIndex + 1];
      this.parentDocument.eventGridJSON.row[selectedIndex + 1]=json;
      gridJSON = this.parentDocument.eventGridJSON;
      this.eventsGrid.gridData = gridJSON;
      this.eventsGrid.selectedIndex = selectedIndex + 1;
      this.eventsGrid.selectedItem = this.parentDocument.eventGridJSON.row[selectedIndex +1].slickgrid_rowcontent;



    }
    for(let i =0; i< this.eventsGrid.gridData.length; i++) {

      // this.eventsGrid.gridData[i].eventSeq = (i+1).toString();
      // this.eventsGrid.gridData[i].slickgrid_rowcontent.eventSeq.content = (i+1).toString();
    }
    // this.eventsGrid.sortGridColumnByColOrder();
    this.eventsGrid.refresh();
    this.rowClick();


  }

  cellLogic(selectedRowData): void {
    if(this.eventsGrid.selectedIndex>-1){
    let fieldName  = selectedRowData.target.field;
    let data = selectedRowData.target.data;
    this.gridEventXML= this.gridEventXML.replace(/<\!\[CDATA\[|\]\]>/g,'');

    this.gridEventXML=prettyData.pd.xml(this.gridEventXML);

    if( parser.validate(this.gridEventXML) == true){
      this.gridEventXML = this.htmlEntities(this.gridEventXML);
    }

    let isClickable = (data.slickgrid_rowcontent[fieldName]) ? data.slickgrid_rowcontent[fieldName].clickable : null;
    if(!isClickable && data.slickgrid_rowcontent['facility'] &&  data.slickgrid_rowcontent['facility'].content != 'SEND_MESSAGE' && 'eventParams' == fieldName)
    isClickable= true;
    if (isClickable) {
      this.clickLink();
    }
  }
  }


  htmlEntities(str) {
    try {
      return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
      replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
    } catch (e) {
      console.log('error', e, str)
    }
  }
  
  clickLink(): void {
    try {
      this.win =  SwtPopUpManager.createPopUp(this, AttributeXML, {
        title: "Grid event XML",
        attributeXmlText:this.gridEventXML,
      });
      this.win.isModal = true;
      this.win.enableResize = false;
      this.win.width = '400';
      this.win.height = '500';
      this.win.showControls = true;
      this.win.id = "GridEventXML";
      this.win.display();

    }
    catch (error) {
    }
  }

  getTooltip(selectedCols){
    let tooltipArray=[];
    let tooltip = "";
    for (let i=0; i<selectedCols.length; i++){
      tooltipArray.push(selectedCols[i].content);
    }
    tooltip=tooltipArray.toString();
    return tooltip;

  }

  enableDisableFields(){
    if(this.triggeEventsRadioGroup.selectedValue=='P'){
      this.resolutionQueryText.enabled=true;
      this.refColumnCombo.isDropdownDisabled=false;
      this.minsText.enabled=true;
      this.resolOverdueRadioGroup.enabled=true;
      if(this.resolOverdueRadioGroup.selectedValue=='A'){
        this.minsText.enabled=true;
      }else{
        this.minsText.enabled=false; 
      }

    }else{
      this.resolutionQueryText.enabled=false;
      this.refColumnCombo.isDropdownDisabled=true;
      this.minsText.enabled=false; 
      this.resolOverdueRadioGroup.enabled=false;

    }
  }

  enableDisableTxtInput() {
    if (this.resolOverdueRadioGroup.selectedValue == 'A') {
      this.minsText.enabled = true;
    } else {
      this.minsText.enabled = false;
    }
  }

  changeBtnStatus(btnImage) {
    if (btnImage.source == this.baseURL + this.parentDocument.lockIcon) {
      btnImage.source = this.baseURL + this.parentDocument.unlockIcon;
      this.addButton.enabled = true;
      this.unlockFlag=true;
      if(this.eventsGrid.selectedIndex>-1){
      this.changeButton.enabled = true;
      this.deleteButton.enabled = true;
      }else{
      this.changeButton.enabled = false;
      this.deleteButton.enabled = false;
      }

    } else {
      this.addButton.enabled = false;
      this.changeButton.enabled = false;
      this.deleteButton.enabled = false;
      this.unlockFlag=false;
      btnImage.source = this.baseURL + this.parentDocument.lockIcon;
    }
  }
  


}
