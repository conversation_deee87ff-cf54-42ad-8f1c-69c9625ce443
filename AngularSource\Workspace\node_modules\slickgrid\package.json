{"_from": "slickgrid@2.4.17", "_id": "slickgrid@2.4.17", "_inBundle": false, "_integrity": "sha512-saxVD9URoBD2M/Sl+7fLWE125/Cp1j0YhkRMPke4Hwdk31q/lihNv8I2o70cM5GRmoeWJKW7tnhNraDEe89jEg==", "_location": "/slickgrid", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "slickgrid@2.4.17", "name": "slickgrid", "escapedName": "slickgrid", "rawSpec": "2.4.17", "saveSpec": null, "fetchSpec": "2.4.17"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/slickgrid/-/slickgrid-2.4.17.tgz", "_shasum": "c46f429c3d0bca66a76aa440e4ed29227d1d31f6", "_spec": "slickgrid@2.4.17", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "<PERSON>", "email": "micha<PERSON>.<EMAIL>"}, "bugs": {"url": "https://github.com/6pac/SlickGrid/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"jquery": ">=1.8.0", "jquery-ui": ">=1.8.0"}, "deprecated": false, "description": "A lightning fast JavaScript grid/spreadsheet", "devDependencies": {"cypress": "^3.8.0", "eslint": "^6.7.2", "http-server": "^0.12.0"}, "directories": {"example": "examples", "test": "tests"}, "homepage": "https://github.com/6pac/SlickGrid#readme", "keywords": ["slickgrid", "grid"], "license": "MIT", "main": "slick.core.js", "name": "slickgrid", "repository": {"type": "git", "url": "git+https://github.com/6pac/SlickGrid.git"}, "scripts": {"cypress:ci": "node node_modules/cypress/bin/cypress run --reporter xunit --reporter-options output=testresult.xml", "cypress:open": "node node_modules/cypress/bin/cypress open", "serve": "http-server ./ -p 8080 -a localhost -s"}, "version": "2.4.17"}