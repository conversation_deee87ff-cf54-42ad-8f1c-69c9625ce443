(window.webpackJsonp=window.webpackJsonp||[]).push([[101],{icnt:function(e,t,i){"use strict";i.r(t);var o=i("CcnG"),l=i("mrSG"),n=i("ZYCi"),s=i("447K"),a=i("JFzx"),r=(i("EVdn"),i("R1Kr"),function(e){function t(t,i){var o=e.call(this,i,t)||this;return o.commonService=t,o.element=i,o.ordertData=new s.G(o.commonService),o.jsonReader=new s.L,o.inputData=new s.G(o.commonService),o.baseURL=s.Wb.getBaseURL(),o.actionMethod="",o.actionPath="",o.requestParams=[],o.logger=null,o.deletedRow=-1,o.enabledRow=-1,o.addColsData=[],o.xml="",o.additionalColsData=[],o.addColsForFilter=[],o.errorLocation=0,o.additionalColsList=[],o.columnsList=[],o.operationsList=[],o.swtAlert=new s.bb(t),o.logger=new s.R("Account Specific Sweep Format",o.commonService.httpclient),window.Main=o,o}return l.d(t,e),t.prototype.ngOnInit=function(){this.columnsGrid=this.colCanvas.addChild(s.hb),this.addButton.label=s.Wb.getPredictMessage("button.add",null),this.deleteButton.label=s.Wb.getPredictMessage("button.delete",null),this.closeButton.label=s.Wb.getPredictMessage("button.close",null),this.profileIdLbl.text=s.Wb.getPredictMessage("msd.profileId.label",null),this.addButton.toolTip=s.Wb.getPredictMessage("button.tooltip.msd.addColumn",null),this.deleteButton.toolTip=s.Wb.getPredictMessage("button.tooltip.msd.deleteColumn",null),this.saveProfileImage.toolTip=s.Wb.getPredictMessage("msdAdditionalColumns.saveProfileImageTooltip",null),this.revertProfileImage.toolTip=s.Wb.getPredictMessage("msdAdditionalColumns.reloadProfileTooltip",null),this.deleteProfileImage.toolTip=s.Wb.getPredictMessage("msdAdditionalColumns.deleteProfileImageTooltip",null),this.closeButton.toolTip=s.Wb.getPredictMessage("tooltip.close",null),this.profileCombo.toolTip=s.Wb.getPredictMessage("msdAdditionalColumns.profileComboTooltip",null),this.columnsGrid.editable=!0},t.prototype.onLoad=function(){var e=this;this.requestParams=[],this.menuAccessId=s.x.call("eval","menuAccessId"),this.profileId=s.x.call("eval","profileId"),this.entityId=window.opener.instanceElement.entityCombo.selectedLabel,this.source=s.x.call("eval","source"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="outstandingmovement.do?",this.actionMethod="method=displayColScreenData",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.profileId=this.profileId?this.profileId:"<None>",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.columnsGrid.ITEM_CHANGED.subscribe(function(t){e.methodName="change",e.updateColumnDetails(t)}),this.columnsGrid.onRowClick=function(t){e.cellClickEventHandler(t)}},t.prototype.updateData=function(){this.columnsGrid.gridData={size:0,row:[]},this.addColsData=[],this.profileCombo.selectedLabel="<None>",this.saveProfileImage.enabled=!0,this.revertProfileImage.enabled=!1,this.deleteProfileImage.enabled=!1,this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1,this.additionalColsList.splice(this.deletedProfile,1),this.profileCombo.dataProvider=this.additionalColsList,this.profileCombo.setComboData(this.additionalColsList),window.opener&&window.opener.instanceElement&&"msdScreen"==this.source&&window.opener.instanceElement.updateProfileCombo(this.additionalColsList,this.profileCombo.selectedLabel),window.opener&&window.opener.instanceElement2&&"settingScreen"==this.source&&window.opener.instanceElement2.updateProfileCombo(this.additionalColsList,this.profileCombo.selectedLabel)},t.prototype.inputDataResult=function(e){if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()){if(this.lastRecievedJSON!=this.prevRecievedJSON&&!this.jsonReader.isDataBuilding()){window.opener.instanceElement.openFlag=!0,this.addColsData=[],this.newComboVal="",this.oldComboVal="";var t=this.jsonReader.getSingletons().profileId;this.additionalColsList=this.jsonReader.getSelects().select[0].option,this.profileCombo.setComboData(this.jsonReader.getSelects()),this.profileCombo.selectedLabel=t;var i={columns:this.lastRecievedJSON.mvtAdditionalCol.additionalColumnGrid.metadata.columns};if(this.gridMetadata=this.lastRecievedJSON.mvtAdditionalCol.additionalColumnGrid.metadata,this.selectValues=this.lastRecievedJSON.mvtAdditionalCol.selects,this.columnsGrid.gridComboDataProviders(this.selectValues),this.gridColumns=this.lastRecievedJSON.mvtAdditionalCol.additionalColumnGrid.metadata.columns.column,this.columnsGrid.CustomGrid(i),this.colGridRows=this.lastRecievedJSON.mvtAdditionalCol.additionalColumnGrid.rows,this.colGridRows&&this.colGridRows.size>0){this.columnsGrid.gridData=this.colGridRows,this.columnsGrid.setRowSize=this.jsonReader.getRowSize(),this.columnsGrid.enableDisableCells=function(e,t){return"label"==t},this.columnsGrid.refresh();for(var o=0;o<this.colGridRows.size;o++){var l=this.columnsGrid.gridData[o].table,n=this.columnsGrid.gridData[o].column,s=this.columnsGrid.gridData[o].label,a=this.columnsGrid.gridData[o].sequence;this.addColsData.push({table:{clickable:!1,content:l,negative:!1},column:{clickable:!1,content:n,negative:!1},label:{clickable:!1,content:s,negative:!1},sequence:{clickable:!1,content:a,negative:!1}})}}else this.columnsGrid.gridData={size:0,row:[]};"<None>"==this.profileCombo.selectedLabel?(this.saveProfileImage.enabled=!0,this.deleteProfileImage.enabled=!1,this.revertProfileImage.enabled=!1):(this.saveProfileImage.enabled=!0,this.deleteProfileImage.enabled=!0,this.revertProfileImage.enabled=!1)}}else this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtAlert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error")},t.prototype.cellClickEventHandler=function(e){var t=0;try{this.deletedAccount=this.columnsGrid.selectedItem?this.columnsGrid.selectedItem.label.content:"",t=10,this.columnsGrid.selectedIndex>=0?(t=20,this.deleteButton.enabled=!0,this.deleteButton.buttonMode=!0):(t=30,this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1)}catch(i){this.logger.error("method [cellClickEventHandler] - error: ",i,"errorLocation: ",t),s.Wb.logError(i,s.Wb.PREDICT_MODULE_ID,"AdditionalColumns.ts","cellClickEventHandler",t)}},t.prototype.addHandler=function(){var e=this,t=0;try{if(this.revertProfileImage.enabled=!0,this.methodName="add",this.deletedRow=-1,this.enabledRow++,this.deleteButton.enabled=!0,this.deleteButton.buttonMode=!0,t=30,this.columnsGrid.gridData.length>0&&""==this.columnsGrid.gridData[0].table)this.swtAlert.error(s.Wb.getPredictMessage("additionalColumns.alert.emptyField",null)+" table name");else if(this.columnsGrid.gridData.length>0&&""==this.columnsGrid.gridData[0].column)this.swtAlert.error(s.Wb.getPredictMessage("additionalColumns.alert.emptyField",null)+" column name");else if(this.columnsGrid.gridData.length>0&&""==this.columnsGrid.gridData[0].label)this.swtAlert.error(s.Wb.getPredictMessage("additionalColumns.alert.emptyLabel",null));else{this.addColsData.splice(0,0,{table:{clickable:!1,content:"P_ACCOUNT",negative:!1},column:{clickable:!1,content:"",negative:!1},label:{clickable:!1,content:"",negative:!1},sequence:{clickable:!1,content:"",negative:!1}}),t=40;for(var i=0;i<this.gridMetadata.columns.column.length;i++)t=20,this.gridMetadata.columns.column[i].editable=!0;t=30,this.columnsGrid.CustomGrid(this.gridMetadata),this.columnsGrid.gridData={row:this.addColsData,size:this.addColsData.length},t=50,this.columnsGrid.enableDisableCells=function(t,i){return e.enableDisableRow(t,i)},this.columnsGrid.refresh(),t=60,this.profileContentChanged()}}catch(o){this.logger.error("method [addHandler] - error: ",o,"errorLocation: ",t),s.Wb.logError(o,s.Wb.PREDICT_MODULE_ID,"AdditionalColumns.ts","addHandler",t)}},t.prototype.enableDisableRow=function(e,t){var i=0;try{return 0==e.id?(i=10,!0):"label"==t}catch(o){this.logger.error("method [enableDisableRow] - error: ",o,"errorLocation: ",i),s.Wb.logError(o,s.Wb.PREDICT_MODULE_ID,"AdditionalColumns.ts","enableDisableRow",i)}},t.prototype.updateColumnDetails=function(e){var t=0;try{if(this.requestParams=[],e){if(this.rowIndex=e.rowIndex,t=10,this.dataField=e.dataField,t=20,this.oldComboVal=e.listData.oldValue,t=30,this.newComboVal=e.listData.newValue,!this.newComboVal&&"label"!=this.dataField)return this.swtAlert.error(s.Wb.getPredictMessage("additionalColumns.alert.emptyValue",null)),this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content=this.oldComboVal,this.columnsGrid.dataProvider[this.rowIndex][this.dataField]=this.oldComboVal,t=90,this.columnsGrid.refresh(),void(this.addColsData[this.rowIndex][this.dataField].content=this.oldComboVal);if("label"==this.dataField&&this.columnsGrid.dataProvider.length>0)for(var i=0;i<this.columnsGrid.dataProvider.length;i++){var o=this.columnsGrid.dataProvider[i].label;if(this.rowIndex!=i&&this.newComboVal&&o==this.newComboVal)return this.swtAlert.error(s.Wb.getPredictMessage("additionalColumns.alert.changeValues",null)),this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content=this.oldComboVal,this.columnsGrid.dataProvider[this.rowIndex][this.dataField]=this.oldComboVal,t=90,this.columnsGrid.refresh(),void(this.addColsData[this.rowIndex][this.dataField].content=this.oldComboVal);this.addColsData[this.rowIndex][this.dataField].content=this.newComboVal}else"table"==this.dataField?("Account"==this.newComboVal&&(this.newComboVal="P_ACCOUNT"),this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent.column.content="",this.columnsGrid.dataProvider[this.rowIndex].column="",this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent.label.content="",this.columnsGrid.dataProvider[this.rowIndex].label="",this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent[this.dataField].content=this.newComboVal,this.columnsGrid.dataProvider[this.rowIndex][this.dataField]=this.newComboVal,t=40,this.columnsGrid.refresh(),this.addColsData[this.rowIndex].column.content="",this.addColsData[this.rowIndex].label.content=""):"column"==this.dataField&&(this.columnsGrid.dataProvider[this.rowIndex].slickgrid_rowcontent.label.content="",this.columnsGrid.dataProvider[this.rowIndex].label="",this.addColsData[this.rowIndex].label.content="",this.columnsGrid.refresh()),this.addColsData[this.rowIndex][this.dataField].content=this.newComboVal;this.profileContentChanged()}}catch(l){this.logger.error("method [updateSweepAccountVal] - error: ",l,"errorLocation: ",t),s.Wb.logError(l,s.Wb.PREDICT_MODULE_ID,"AdditionalColumns.ts","updateSweepAccountVal",t)}},t.prototype.revertProfileClickHandler=function(e){this.swtAlert.show(s.Wb.getPredictMessage("additionalColumns.alertOverwriteProfile",null),s.Wb.getPredictMessage("alert_header.confirm"),s.c.OK|s.c.CANCEL,null,this.revertAlertListener.bind(this),s.c.CANCEL)},t.prototype.revertAlertListener=function(e){e.detail==s.c.OK&&this.changeProfile(null)},t.prototype.deleteHandler=function(){var e=0;try{s.c.yesLabel=s.Wb.getPredictMessage("alert.yes.label"),s.c.noLabel=s.Wb.getPredictMessage("alert.no.label"),e=10;var t=s.Z.substitute(s.Wb.getPredictMessage("additionalColumns.alert.deleteColumn",null));e=20,this.swtAlert.confirm(t,s.Wb.getPredictMessage("alert_header.confirm"),s.c.YES|s.c.NO,null,this.proceedWithDelete.bind(this))}catch(i){this.logger.error("method [deleteHandler] - error: ",i,"errorLocation: ",e),s.Wb.logError(i,s.Wb.PREDICT_MODULE_ID,"AdditionalColumns.ts","deleteHandler",e)}},t.prototype.proceedWithDelete=function(e){var t=this,i=0;try{if(e.detail==s.c.YES){i=0,this.revertProfileImage.enabled=!0,this.methodName="delete",this.deletedRow=this.columnsGrid.selectedIndex,i=10;this.columnsGrid.selectedItem.label.content;i=20,this.columnsGrid.removeSelected(),i=40,this.addColsData.splice(this.deletedRow,1),this.columnsGrid.gridData={row:this.addColsData,size:this.addColsData.length},i=50,this.columnsGrid.enableDisableCells=function(e,i){return t.enableDisableRow(e,i)},this.columnsGrid.refresh(),this.enabledRow--,this.deleteButton.enabled=!1,this.deleteButton.buttonMode=!1,this.columnsGrid.selectedIndex=-1,i=60,this.profileContentChanged(),window.opener&&window.opener.instanceElement&&"msdScreen"==this.source&&window.opener.instanceElement.updateProfileCombo(this.additionalColsList,this.profileCombo.selectedLabel),window.opener&&window.opener.instanceElement2&&"settingScreen"==this.source&&window.opener.instanceElement2.updateProfileCombo(this.additionalColsList,this.profileCombo.selectedLabel)}}catch(o){this.logger.error("method [proceedWithDelete] - error: ",o,"errorLocation: ",i),s.Wb.logError(o,s.Wb.PREDICT_MODULE_ID,"AddtionalColumns.ts","proceedWithDelete",i)}},t.prototype.closeHandler=function(){s.x.call("closeHandler")},t.prototype.closeAndUpdate=function(){if(this.profileCombo.selectedLabel.startsWith("*")){s.c.yesLabel=s.Wb.getPredictMessage("alert.yes.label"),s.c.noLabel=s.Wb.getPredictMessage("alert.no.label");var e=s.Z.substitute(s.Wb.getPredictMessage("entity.CloseConfirm",null));this.swtAlert.confirm(e,s.Wb.getPredictMessage("alert_header.confirm"),s.c.YES|s.c.NO,null,this.proceedWithClose.bind(this))}else s.x.call("closeHandler"),window.opener.instanceElement&&(window.opener.instanceElement.openFlag=!1),window.opener.instanceElement2&&"settingScreen"==this.source&&(window.opener.instanceElement2.addColsCombo.selectedLabel=this.profileCombo.selectedLabel,window.opener.instanceElement2.addColsGridData=this.gridColumns.gridData)},t.prototype.proceedWithClose=function(e){try{e.detail==s.c.YES&&s.x.call("closeHandler")}catch(t){this.logger.error("method [proceedWithClose] - error: ",t,"errorLocation: ",0),s.Wb.logError(t,s.Wb.PREDICT_MODULE_ID,"AddtionalColumns.ts","proceedWithClose",0)}},t.prototype.saveProfile=function(e){var t=this;-1!=this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content.toString().indexOf("*")&&(this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content=this.profileCombo.selectedLabel.replace("*",""),this.profileCombo.selectedLabel=this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content);var i=0;try{this.requestParams=[],i=100,this.menuAccessId=s.x.call("eval","menuAccessId"),i=110,this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),i=10,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.changeProfile(e)},i=20,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="outstandingmovement.do?",this.actionMethod="method=updateProfileAddCols",this.requestParams.additionalColumns=JSON.stringify(this.prepareGridData()),this.requestParams.profileId=e,this.requestParams.entityId=this.entityId,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,i=120,this.inputData.send(this.requestParams)}catch(o){this.logger.error("method [saveProfile] - error: ",o,"errorLocation: ",i),s.Wb.logError(o,s.Wb.PREDICT_MODULE_ID,"AdditionalColumns.ts","saveProfile",i)}},t.prototype.saveNewProfile=function(e){var t=this,i=0;try{this.requestParams=[],this.additionalColsList.length||(this.additionalColsList=[this.additionalColsList]),this.additionalColsList.push({type:"",value:e,selected:0,content:e}),this.additionalColsList.sort(function(e,t){return e.content.localeCompare(t.content)}),this.profileCombo.dataProvider=this.additionalColsList,this.profileCombo.setComboData(this.additionalColsList),this.profileCombo.selectedLabel=e,window.opener&&window.opener.instanceElement&&"msdScreen"==this.source&&window.opener.instanceElement.updateProfileCombo(this.additionalColsList,this.profileCombo.selectedLabel),window.opener&&window.opener.instanceElement2&&"settingScreen"==this.source&&window.opener.instanceElement2.updateProfileCombo(this.additionalColsList,this.profileCombo.selectedLabel),this.revertProfileImage.enabled=!1,this.saveProfileImage.enabled=!0,this.deleteProfileImage.enabled=!0,i=100,this.menuAccessId=s.x.call("eval","menuAccessId"),i=110,this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),i=10,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.swtAlert.show(s.Wb.getPredictMessage("additionalColumns.alertProfileSaved",null)),t.changeProfile(e)},i=20,this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="outstandingmovement.do?",this.actionMethod="method=saveProfileAddCols",this.requestParams.additionalColumns=JSON.stringify(this.prepareGridData()),this.requestParams.profileId=this.profileCombo.selectedValue,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,i=120,this.inputData.send(this.requestParams)}catch(o){this.logger.error("method [saveNew] - error: ",o,"errorLocation: ",i),s.Wb.logError(o,s.Wb.PREDICT_MODULE_ID,"AdditionalColumns.ts","saveNew",i)}},t.prototype.prepareGridData=function(){if(this.additionalColsData=[],this.columnsGrid.gridData.length>0)for(var e=0;e<this.columnsGrid.gridData.length;e++)this.additionalColsData.push({Table:this.columnsGrid.gridData[e].table,Column:this.columnsGrid.gridData[e].column,Label:this.columnsGrid.gridData[e].label,Sequence:this.columnsGrid.gridData[e].sequence});return this.additionalColsData},t.prototype.deleteProfileClickHandler=function(e){this.profileCombo.selectedIndex>0&&this.swtAlert.show(s.Wb.getPredictMessage("additionalColumns.alertDeleteProfile",null)+" "+this.profileCombo.selectedLabel.replace("*","")+"?",s.Wb.getPredictMessage("alert_header.confirm"),s.c.OK|s.c.CANCEL,this,this.deleteAlertListener.bind(this),null,s.c.CANCEL)},t.prototype.deleteAlertListener=function(e){e.detail==s.c.OK&&this.deleteProfile()},t.prototype.deleteProfile=function(){var e=this;this.requestParams=[],this.deletedProfile=this.profileCombo.selectedIndex,this.menuAccessId=s.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.updateData()},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="outstandingmovement.do?",this.actionMethod="method=deleteProfileData",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.entityId=this.entityId,this.requestParams.profileId=this.profileCombo.selectedLabel,this.requestParams.savedProfileId=window.opener.instanceElement.savedProfileId,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},t.prototype.addProfile=function(e){var t=this;this.prepareAddColsForFilter();var i=null,o=null,l=[];if(this.columnsGrid.gridData.length>0&&""==this.columnsGrid.gridData[0].table)this.swtAlert.error(s.Wb.getPredictMessage("additionalColumns.alert.emptyField",null)+" table name");else if(this.columnsGrid.gridData.length>0&&""==this.columnsGrid.gridData[0].column)this.swtAlert.error(s.Wb.getPredictMessage("additionalColumns.alert.emptyField",null)+" column name");else if(this.columnsGrid.gridData.length>0&&""==this.columnsGrid.gridData[0].label)this.swtAlert.error(s.Wb.getPredictMessage("additionalColumns.alert.emptyLabel",null));else{for(var n=0;n<this.profileCombo.dataProvider.length;n++)l.push(this.profileCombo.dataProvider[n].content.toString().replace("*",""));i=s.Wb.getPredictMessage("msdAdditionalColumns.noneProfile",null);var a=l.indexOf(i);a>-1&&l.splice(a,1),this.profileCombo.selectedIndex>0&&(o=this.profileCombo.selectedItem.content.toString().replace("*",""));try{this.saveProfilePopupWindow=s.Eb.createPopUp(this),this.saveProfilePopupWindow.title="Save Profile",this.saveProfilePopupWindow.isModal=!0,this.saveProfilePopupWindow.width="400",this.saveProfilePopupWindow.height="130",this.saveProfilePopupWindow.id="saveProfilePopupWindow",this.saveProfilePopupWindow.enableResize=!1,this.saveProfilePopupWindow.showControls=!0,this.saveProfilePopupWindow.saveProfileCollection=l,this.saveProfilePopupWindow.selectedProfileItem=o,this.saveProfilePopupWindow.msdAddColsCombo=this.profileCombo;var r=new s.T(this.commonService);r.addEventListener(s.S.READY,function(e){return t.moduleReadyEventHandler(e)}),r.loadModule("saveMsdProfilePopup")}catch(d){s.Wb.logError(d,"Predict","saveProfilePopupWindow","saveProfile",this.errorLocation)}}},t.prototype.moduleReadyEventHandler=function(e){this.saveProfilePopupWindow.addChild(e.target),this.saveProfilePopupWindow.display(),this.saveProfilePopupWindow.onClose.subscribe(function(){},function(e){console.log(e)})},t.prototype.changeProfile=function(e){var t=this;window.opener&&window.opener.instanceElement&&"settingScreen"==this.source&&window.opener.instanceElement.updateGridData(),this.selectedProfile=this.profileCombo.selectedLabel,"<None>"==this.profileCombo.selectedLabel?(this.saveProfileImage.enabled=!0,this.deleteProfileImage.enabled=!1,this.revertProfileImage.enabled=!0):(this.saveProfileImage.enabled=!0,this.deleteProfileImage.enabled=!0,this.revertProfileImage.enabled=!1),this.requestParams=[],this.menuAccessId=s.x.call("eval","menuAccessId"),this.menuAccessId&&""!==this.menuAccessId&&(this.menuAccessId=Number(this.menuAccessId)),this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="outstandingmovement.do?",this.actionMethod="method=displayColScreenData",this.requestParams.menuAccessId=this.menuAccessId,this.requestParams.profileId=-1!=this.profileCombo.selectedLabel.indexOf("*")?this.profileCombo.selectedLabel.substring(1):this.profileCombo.selectedLabel,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},t.prototype.profileContentChanged=function(){try{-1==this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content.toString().indexOf("*")&&(this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content="*"+this.profileCombo.selectedLabel,this.profileCombo.selectedLabel=this.profileCombo.dataProvider[this.profileCombo.selectedIndex].content),this.revertProfileImage.enabled=!0,this.deleteProfileImage.enabled=!1}catch(e){}},t.prototype.addColsGridChanges=function(){for(var e={},t=this.columnsGrid.changes.getValues(),i=0;i<t.length;i++)e={OPERATION:t[i].crud_operation.substring(0,1),Table:t[i].crud_data.table,Column:t[i].crud_data.column,Label:t[i].crud_data.label,Sequence:t[i].crud_data.sequence},this.operationsList.push(e)},t.prototype.comboBoxChangeHandler=function(e){try{var t;this.errorLocation=10;var i=null,o=null,l=null;if("ComboBoxItemRenderer"==e.target)if(this.errorLocation=20,e.listData.newValue,o=e.listData.newValue,t=e.dataField,e.rowIndex,i=e.listData.new_row,this.errorLocation=30,"rec_unit_id"==t){if(this.errorLocation=40,null==this.lastRecievedJSON.parties.grid1.selects.select.find(function(e){return-1!=String(e.id).indexOf("rec_unit_id|1054.1")}).option.length){var n=new Array;this.lastRecievedJSON.parties.grid1.selects.select.find(function(e){return-1!=String(e.id).indexOf("rec_unit_id|1054.1")}).option&&(n[0]=this.lastRecievedJSON.parties.grid1.selects.select.find(function(e){return-1!=String(e.id).indexOf("rec_unit_id|1054.1")}).option),this.lastRecievedJSON.parties.grid1.selects.select.find(function(e){return-1!=String(e.id).indexOf("rec_unit_id|1054.1")}).option=n}l=this.lastRecievedJSON.parties.grid1.selects.select.find(function(e){return-1!=String(e.id).indexOf("rec_unit_id|1054.1")}).option.find(function(e){return e.value==o}).attribute,this.errorLocation=45,i.attribute=s.Z.trim(l).length>0?l:"EMPTY",this.columnsGrid.updateRow(this.columnsGrid.selectedIndex,"attribute",i.attribute),String(i.rec_unit_id).length>0||(i.origin=""),this.columnsGrid.updateRow(this.columnsGrid.selectedIndex,"origin",i.origin),"EMPTY"==i.attribute&&(i.attribute=null),i.new_row||(i.item={content:"Y"})}else if("attribute"==t){var a=[],r=[];if(this.errorLocation=48,null==(a=this.lastRecievedJSON.parties.grid1.selects.select.find(function(e){return-1!=String(e.id).indexOf("rec_unit_id|1054.1")}).option).length){n=new Array;this.lastRecievedJSON.parties.grid1.selects.select.find(function(e){return-1!=String(e.id).indexOf("rec_unit_id|1054.1")}).option&&(n[0]=this.lastRecievedJSON.parties.grid1.selects.select.find(function(e){return-1!=String(e.id).indexOf("rec_unit_id|1054.1")}).option),a=n}this.errorLocation=49,a.filter(function(e){""!=o&&e.attribute!=o||r.push(e)}),this.errorLocation=491;var d=this.columnsGrid.columnDefinitions.find(function(e){return"rec_unit_id"==e.id});d&&(d.params.selectDataSource=r),this.errorLocation=50}}catch(c){}},t.prototype.prepareAddColsForFilter=function(){if(this.addColsForFilter=[],this.columnsGrid.gridData.length>0)for(var e=0;e<this.columnsGrid.gridData.length;e++)Object.assign("*","");return this.additionalColsData},t.prototype.startOfComms=function(){},t.prototype.endOfComms=function(){},t.prototype.inputDataFault=function(e){this._invalidComms=e.fault.faultString+"\n"+e.fault.faultCode+"\n"+e.fault.faultDetail,this.swtAlert.show("fault "+this._invalidComms)},t.prototype.addColumns=function(){try{this.win=s.Eb.createPopUp(this,a.a,{title:"Add Columns",selectCursor:this.jsonReader.getSelects()}),this.win.isModal=!0,this.win.enableResize=!1,this.win.width="300",this.win.height="180",this.win.showControls=!0,this.win.id="addCols",this.win.display()}catch(e){this.logger.error("method [addColumns] - error: ",e,"errorLocation: ",0),s.Wb.logError(e,s.Wb.PREDICT_MODULE_ID,"AdditionalColumns.ts","addColumns",0)}},t.prototype.removeColumns=function(){},t}(s.yb)),d=[{path:"",component:r}],c=(n.l.forChild(d),function(){return function(){}}()),h=i("pMnS"),u=i("RChO"),b=i("t6HQ"),m=i("WFGK"),p=i("5FqG"),g=i("Ip0R"),f=i("gIcY"),C=i("t/Na"),w=i("sE5F"),P=i("OzfB"),v=i("T7CS"),I=i("S7LP"),D=i("6aHO"),R=i("WzUx"),L=i("A7o+"),G=i("zCE2"),y=i("Jg5P"),A=i("3R0m"),S=i("hhbb"),_=i("5rxC"),W=i("Fzqc"),x=i("21Lb"),E=i("hUWP"),M=i("3pJQ"),O=i("V9q+"),k=i("VDKW"),N=i("kXfT"),T=i("BGbe");i.d(t,"AdditionalColumnsModuleNgFactory",function(){return B}),i.d(t,"RenderType_AdditionalColumns",function(){return J}),i.d(t,"View_AdditionalColumns_0",function(){return q}),i.d(t,"View_AdditionalColumns_Host_0",function(){return H}),i.d(t,"AdditionalColumnsNgFactory",function(){return z});var B=o.Gb(c,[],function(e){return o.Qb([o.Rb(512,o.n,o.vb,[[8,[h.a,u.a,b.a,m.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,z]],[3,o.n],o.J]),o.Rb(4608,g.m,g.l,[o.F,[2,g.u]]),o.Rb(4608,f.c,f.c,[]),o.Rb(4608,f.p,f.p,[]),o.Rb(4608,C.j,C.p,[g.c,o.O,C.n]),o.Rb(4608,C.q,C.q,[C.j,C.o]),o.Rb(5120,C.a,function(e){return[e,new s.tb]},[C.q]),o.Rb(4608,C.m,C.m,[]),o.Rb(6144,C.k,null,[C.m]),o.Rb(4608,C.i,C.i,[C.k]),o.Rb(6144,C.b,null,[C.i]),o.Rb(4608,C.f,C.l,[C.b,o.B]),o.Rb(4608,C.c,C.c,[C.f]),o.Rb(4608,w.c,w.c,[]),o.Rb(4608,w.g,w.b,[]),o.Rb(5120,w.i,w.j,[]),o.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),o.Rb(4608,w.f,w.a,[]),o.Rb(5120,w.d,w.k,[w.h,w.f]),o.Rb(5120,o.b,function(e,t){return[P.j(e,t)]},[g.c,o.O]),o.Rb(4608,v.a,v.a,[]),o.Rb(4608,I.a,I.a,[]),o.Rb(4608,D.a,D.a,[o.n,o.L,o.B,I.a,o.g]),o.Rb(4608,R.c,R.c,[o.n,o.g,o.B]),o.Rb(4608,R.e,R.e,[R.c]),o.Rb(4608,L.l,L.l,[]),o.Rb(4608,L.h,L.g,[]),o.Rb(4608,L.c,L.f,[]),o.Rb(4608,L.j,L.d,[]),o.Rb(4608,L.b,L.a,[]),o.Rb(4608,L.k,L.k,[L.l,L.h,L.c,L.j,L.b,L.m,L.n]),o.Rb(4608,R.i,R.i,[[2,L.k]]),o.Rb(4608,R.r,R.r,[R.L,[2,L.k],R.i]),o.Rb(4608,R.t,R.t,[]),o.Rb(4608,R.w,R.w,[]),o.Rb(1073742336,n.l,n.l,[[2,n.r],[2,n.k]]),o.Rb(1073742336,g.b,g.b,[]),o.Rb(1073742336,f.n,f.n,[]),o.Rb(1073742336,f.l,f.l,[]),o.Rb(1073742336,G.a,G.a,[]),o.Rb(1073742336,y.a,y.a,[]),o.Rb(1073742336,f.e,f.e,[]),o.Rb(1073742336,A.a,A.a,[]),o.Rb(1073742336,L.i,L.i,[]),o.Rb(1073742336,R.b,R.b,[]),o.Rb(1073742336,C.e,C.e,[]),o.Rb(1073742336,C.d,C.d,[]),o.Rb(1073742336,w.e,w.e,[]),o.Rb(1073742336,S.b,S.b,[]),o.Rb(1073742336,_.b,_.b,[]),o.Rb(1073742336,P.c,P.c,[]),o.Rb(1073742336,W.a,W.a,[]),o.Rb(1073742336,x.d,x.d,[]),o.Rb(1073742336,E.c,E.c,[]),o.Rb(1073742336,M.a,M.a,[]),o.Rb(1073742336,O.a,O.a,[[2,P.g],o.O]),o.Rb(1073742336,k.b,k.b,[]),o.Rb(1073742336,N.a,N.a,[]),o.Rb(1073742336,T.b,T.b,[]),o.Rb(1073742336,s.Tb,s.Tb,[]),o.Rb(1073742336,c,c,[]),o.Rb(256,C.n,"XSRF-TOKEN",[]),o.Rb(256,C.o,"X-XSRF-TOKEN",[]),o.Rb(256,"config",{},[]),o.Rb(256,L.m,void 0,[]),o.Rb(256,L.n,void 0,[]),o.Rb(256,"popperDefaults",{},[]),o.Rb(1024,n.i,function(){return[[{path:"",component:r}]]},[])])}),F=[[""]],J=o.Hb({encapsulation:0,styles:F,data:{}});function q(e){return o.dc(0,[o.Zb(402653184,1,{_container:0}),o.Zb(402653184,2,{colCanvas:0}),o.Zb(402653184,3,{addButton:0}),o.Zb(402653184,4,{deleteButton:0}),o.Zb(402653184,5,{saveProfileImage:0}),o.Zb(402653184,6,{revertProfileImage:0}),o.Zb(402653184,7,{deleteProfileImage:0}),o.Zb(402653184,8,{closeButton:0}),o.Zb(402653184,9,{profileCombo:0}),o.Zb(402653184,10,{generalProfileLbl:0}),o.Zb(402653184,11,{profileIdLbl:0}),o.Zb(402653184,12,{generalProfilecheck:0}),(e()(),o.Jb(12,0,null,null,33,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,i){var o=!0,l=e.component;"creationComplete"===t&&(o=!1!==l.onLoad()&&o);return o},p.ad,p.hb)),o.Ib(13,4440064,null,0,s.yb,[o.r,s.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),o.Jb(14,0,null,0,31,"VBox",[["height","100%"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,p.od,p.vb)),o.Ib(15,4440064,null,0,s.ec,[o.r,s.i,o.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(e()(),o.Jb(16,0,null,0,15,"HBox",[["height","30"],["width","100%"]],null,null,null,p.Dc,p.K)),o.Ib(17,4440064,null,0,s.C,[o.r,s.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),o.Jb(18,0,null,0,5,"HBox",[["horizontalGap","5"],["paddingRight","10"],["width","270"]],null,null,null,p.Dc,p.K)),o.Ib(19,4440064,null,0,s.C,[o.r,s.i],{horizontalGap:[0,"horizontalGap"],width:[1,"width"],paddingRight:[2,"paddingRight"]},null),(e()(),o.Jb(20,0,null,0,1,"SwtLabel",[["id","profileIdLbl"]],null,null,null,p.Yc,p.fb)),o.Ib(21,4440064,[[11,4],["profileIdLbl",4]],0,s.vb,[o.r,s.i],{id:[0,"id"]},null),(e()(),o.Jb(22,0,null,0,1,"SwtComboBox",[["dataLabel","profileList"],["id","profileCombo"],["width","200"]],null,[[null,"change"],["window","mousewheel"]],function(e,t,i){var l=!0,n=e.component;"window:mousewheel"===t&&(l=!1!==o.Tb(e,23).mouseWeelEventHandler(i.target)&&l);"change"===t&&(l=!1!==n.changeProfile(i)&&l);return l},p.Pc,p.W)),o.Ib(23,4440064,[[9,4],["profileCombo",4]],0,s.gb,[o.r,s.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"]},{change_:"change"}),(e()(),o.Jb(24,0,null,0,7,"HBox",[["horizontalGap","5"],["paddingTop","3"]],null,null,null,p.Dc,p.K)),o.Ib(25,4440064,null,0,s.C,[o.r,s.i],{horizontalGap:[0,"horizontalGap"],paddingTop:[1,"paddingTop"]},null),(e()(),o.Jb(26,0,null,0,1,"SwtButton",[["enabled","false"],["id","saveProfileImage"],["style","z-index: 4;"],["styleName","fileSaveIcon"]],null,[[null,"click"]],function(e,t,i){var o=!0,l=e.component;"click"===t&&(o=!1!==l.addProfile(i)&&o);return o},p.Mc,p.T)),o.Ib(27,4440064,[[5,4],["saveProfileImage",4]],0,s.cb,[o.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"]},{onClick_:"click"}),(e()(),o.Jb(28,0,null,0,1,"SwtButton",[["enabled","false"],["id","revertProfileImage"],["style","z-index: 4;"],["styleName","fileRevertIcon"]],null,[[null,"click"]],function(e,t,i){var o=!0,l=e.component;"click"===t&&(o=!1!==l.revertProfileClickHandler(i)&&o);return o},p.Mc,p.T)),o.Ib(29,4440064,[[6,4],["revertProfileImage",4]],0,s.cb,[o.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"]},{onClick_:"click"}),(e()(),o.Jb(30,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteProfileImage"],["style","z-index: 4;"],["styleName","fileDeleteIcon"]],null,[[null,"click"]],function(e,t,i){var o=!0,l=e.component;"click"===t&&(o=!1!==l.deleteProfileClickHandler(i)&&o);return o},p.Mc,p.T)),o.Ib(31,4440064,[[7,4],["deleteProfileImage",4]],0,s.cb,[o.r,s.i],{id:[0,"id"],styleName:[1,"styleName"],enabled:[2,"enabled"]},{onClick_:"click"}),(e()(),o.Jb(32,0,null,0,1,"SwtCanvas",[["height","90%"],["id","colCanvas"],["minHeight","100"],["minWidth","350"],["width","100%"]],null,null,null,p.Nc,p.U)),o.Ib(33,4440064,[[2,4],["colCanvas",4]],0,s.db,[o.r,s.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minHeight:[3,"minHeight"],minWidth:[4,"minWidth"]},null),(e()(),o.Jb(34,0,null,0,11,"SwtCanvas",[["height","35"],["minWidth","300"],["paddingTop","5"],["width","100%"]],null,null,null,p.Nc,p.U)),o.Ib(35,4440064,null,0,s.db,[o.r,s.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],paddingTop:[3,"paddingTop"]},null),(e()(),o.Jb(36,0,null,0,5,"HBox",[["horizontalGap","5"],["paddingLeft","10"],["width","100%"]],null,null,null,p.Dc,p.K)),o.Ib(37,4440064,null,0,s.C,[o.r,s.i],{horizontalGap:[0,"horizontalGap"],width:[1,"width"],paddingLeft:[2,"paddingLeft"]},null),(e()(),o.Jb(38,0,null,0,1,"SwtButton",[["enabled","true"],["id","addButton"]],null,[[null,"click"]],function(e,t,i){var o=!0,l=e.component;"click"===t&&(o=!1!==l.addHandler()&&o);return o},p.Mc,p.T)),o.Ib(39,4440064,[[3,4],["addButton",4]],0,s.cb,[o.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(e()(),o.Jb(40,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"]],null,[[null,"click"]],function(e,t,i){var o=!0,l=e.component;"click"===t&&(o=!1!==l.deleteHandler()&&o);return o},p.Mc,p.T)),o.Ib(41,4440064,[[4,4],["deleteButton",4]],0,s.cb,[o.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"}),(e()(),o.Jb(42,0,null,0,3,"HBox",[["horizontalAlign","right"],["horizontalGap","5"],["paddingRight","10"],["width","100%"]],null,null,null,p.Dc,p.K)),o.Ib(43,4440064,null,0,s.C,[o.r,s.i],{horizontalGap:[0,"horizontalGap"],horizontalAlign:[1,"horizontalAlign"],width:[2,"width"],paddingRight:[3,"paddingRight"]},null),(e()(),o.Jb(44,0,null,0,1,"SwtButton",[["enabled","true"],["id","closeButton"]],null,[[null,"click"]],function(e,t,i){var o=!0,l=e.component;"click"===t&&(o=!1!==l.closeAndUpdate()&&o);return o},p.Mc,p.T)),o.Ib(45,4440064,[[8,4],["closeButton",4]],0,s.cb,[o.r,s.i],{id:[0,"id"],enabled:[1,"enabled"]},{onClick_:"click"})],function(e,t){e(t,13,0,"100%","100%");e(t,15,0,"100%","100%","5","5","5");e(t,17,0,"100%","30");e(t,19,0,"5","270","10");e(t,21,0,"profileIdLbl");e(t,23,0,"profileList","200","profileCombo");e(t,25,0,"5","3");e(t,27,0,"saveProfileImage","fileSaveIcon","false");e(t,29,0,"revertProfileImage","fileRevertIcon","false");e(t,31,0,"deleteProfileImage","fileDeleteIcon","false");e(t,33,0,"colCanvas","100%","90%","100","350");e(t,35,0,"100%","35","300","5");e(t,37,0,"5","100%","10");e(t,39,0,"addButton","true");e(t,41,0,"deleteButton","false");e(t,43,0,"5","right","100%","10");e(t,45,0,"closeButton","true")},null)}function H(e){return o.dc(0,[(e()(),o.Jb(0,0,null,null,1,"app-additional-columns",[],null,null,null,q,J)),o.Ib(1,4440064,null,0,r,[s.i,o.r],null,null)],function(e,t){e(t,1,0)},null)}var z=o.Fb("app-additional-columns",r,H,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);