/*!-----------------------------------------------------------
 * Copyright (c) Microsoft Corporation. All rights reserved.
 * Version: 0.18.1(d7a26172c5955d29d2a8cca4377b53b28925c766)
 * Released under the MIT license
 * https://github.com/Microsoft/vscode/blob/master/LICENSE.txt
 *-----------------------------------------------------------*/
define("vs/editor/editor.main.nls.fr",{"vs/base/browser/ui/actionbar/actionbar":["{0} ({1})"],"vs/base/browser/ui/aria/aria":["{0} (s'est reproduit)","{0} (survenu {1} fois)"],"vs/base/browser/ui/findinput/findInput":["Entrée"],"vs/base/browser/ui/findinput/findInputCheckboxes":["Respecter la casse","Mot entier","Utiliser une expression régulière"],"vs/base/browser/ui/findinput/replaceInput":["Entrée","Préserver la casse"],"vs/base/browser/ui/inputbox/inputBox":["Erreur : {0}","Avertissement : {0}","Info : {0}"],"vs/base/browser/ui/keybindingLabel/keybindingLabel":["Indépendant"],"vs/base/browser/ui/list/listWidget":["{0}. Utiliser les touches de navigation pour naviguer."],"vs/base/browser/ui/menu/menu":["{0} ({1})"],"vs/base/browser/ui/tree/abstractTree":["Effacer","Désactiver le filtre sur le type","Activer le filtre sur le type","Aucun élément","{0} éléments sur {1} correspondants"],
"vs/base/common/keybindingLabels":["Ctrl","Maj","Alt","Windows","Ctrl","Maj","Alt","Super","Contrôle","Maj","Alt","Commande","Contrôle","Maj","Alt","Windows","Contrôle","Maj","Alt","Super"],"vs/base/common/severity":["Erreur","Avertissement","Info"],"vs/base/parts/quickopen/browser/quickOpenModel":["{0}, sélecteur","sélecteur"],"vs/base/parts/quickopen/browser/quickOpenWidget":["Sélecteur rapide. Tapez pour réduire les résultats.","Sélecteur rapide","{0} résultats"],"vs/editor/browser/controller/coreCommands":["&&Sélectionner tout","Ann&&uler","&&Rétablir"],"vs/editor/browser/widget/codeEditorWidget":["Le nombre de curseurs a été limité à {0}."],"vs/editor/browser/widget/diffEditorWidget":["Impossible de comparer les fichiers car l'un d'eux est trop volumineux."],
"vs/editor/browser/widget/diffReview":["Fermer","aucune ligne","1 ligne","{0} lignes","Différence {0} sur {1} : original {2}, {3}, modifié {4}, {5}","vide","{0} d'origine, {1} modifiées : {2}","+ {0} modifiées : {1}","- {0} d'origine : {1}","Accéder à la différence suivante","Accéder la différence précédente"],"vs/editor/browser/widget/inlineDiffMargin":["Copier les lignes supprimées","Copier la ligne supprimée","Copier la ligne supprimée ({0})","Annuler la modification","Copier la ligne supprimée ({0})"],
"vs/editor/common/config/commonEditorConfig":["Éditeur","Contrôle la famille de polices.","Contrôle l'épaisseur de police.","Contrôle la taille de police en pixels.","Contrôle la hauteur de ligne. Utilisez 0 pour calculer la hauteur de ligne de la taille de la police.","Contrôle l'espacement des lettres en pixels.","Les numéros de ligne ne sont pas affichés.","Les numéros de ligne sont affichés en nombre absolu.","Les numéros de ligne sont affichés sous la forme de distance en lignes à la position du curseur.","Les numéros de ligne sont affichés toutes les 10 lignes.","Contrôle l'affichage des numéros de ligne.","Contrôle le nombre minimum de lignes visibles avant et après le curseur. Appelé 'scrollOff' ou 'scrollOffset' dans d'autres éditeurs.","Affichez le dernier numéro de ligne quand le fichier se termine par un saut de ligne.","Rendre les règles verticales après un certain nombre de caractères à espacement fixe. Utiliser plusieurs valeurs pour plusieurs règles. Aucune règle n'est dessinée si le tableau est vide.","Caractères utilisés comme séparateurs de mots durant la navigation ou les opérations basées sur les mots","Le nombre d'espaces auxquels une tabulation est égale. Ce paramètre est substitué basé sur le contenu du fichier lorsque `#editor.detectIndentation#` est à 'on'.","Espaces insérés quand vous appuyez sur la touche Tab. Ce paramètre est remplacé en fonction du contenu du fichier quand '#editor.detectIndentation#' est activé.","Contrôle si '#editor.tabSize#' et '#editor.insertSpaces#' sont automatiquement détectés lors de l’ouverture d’un fichier en fonction de son contenu.","Contrôle si les sélections doivent avoir des angles arrondis.","Contrôle si l’éditeur défile au-delà de la dernière ligne.","Contrôle le nombre de caractères supplémentaires, au-delà duquel l’éditeur défile horizontalement.","Contrôle si l'éditeur défile en utilisant une animation.","Contrôle si la minimap est affichée.","Contrôle le côté où afficher la minimap.","Contrôle si le curseur de la minimap est automatiquement masqué.","Afficher les caractères réels sur une ligne par opposition aux blocs de couleur.","Limiter la largeur de la minimap pour afficher au plus un certain nombre de colonnes.","Contrôle si le pointage est affiché.","Contrôle le délai en millisecondes, après lequel le survol est affiché.","Contrôle si le pointage doit rester visible quand la souris est déplacée au-dessus.","Détermine si la chaîne de recherche dans le Widget Recherche est initialisée avec la sélection de l’éditeur.","Contrôle si l’opération de recherche est effectuée sur le texte sélectionné ou sur l’intégralité du fichier dans l’éditeur.","Détermine si le Widget Recherche devrait lire ou modifier le presse-papiers de recherche partagé sur macOS.","Contrôle si le widget Recherche doit ajouter des lignes supplémentaires en haut de l'éditeur. Quand la valeur est true, vous pouvez faire défiler au-delà de la première ligne si le widget Recherche est visible.","Le retour automatique à la ligne n'est jamais effectué.","Le retour automatique à la ligne s'effectue en fonction de la largeur de la fenêtre d'affichage.","Les lignes seront terminées à `#editor.wordWrapColumn#`.","Les lignes seront terminées au minimum du viewport et `#editor.wordWrapColumn#`.","Contrôle comment les lignes doivent être limitées.","Contrôle la colonne de terminaison de l’éditeur lorsque `#editor.wordWrap#` est à `wordWrapColumn` ou `bounded`.","Aucune mise en retrait. Les lignes enveloppées commencent à la colonne 1.","Les lignes enveloppées obtiennent la même mise en retrait que le parent.","Les lignes justifiées obtiennent une mise en retrait +1 vers le parent.","Les lignes justifiées obtiennent une mise en retrait +2 vers le parent. ","Contrôle la mise en retrait des lignes justifiées.","Un multiplicateur à utiliser sur les `deltaX` et `deltaY` des événements de défilement de roulette de souris.","Multiplicateur de vitesse de défilement quand vous appuyez sur 'Alt'.","Mappe vers 'Contrôle' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Le modificateur à utiliser pour ajouter plusieurs curseurs avec la souris. Les gestes de souris Atteindre la définition et Ouvrir le lien s'adapteront tels qu’ils n’entrent pas en conflit avec le modificateur multicursor. [Lire la suite] (https://code.visualstudio.com/docs/editor/codebasics#_multicursor-modifier).","Fusionnez plusieurs curseurs quand ils se chevauchent.","Activez les suggestions rapides dans les chaînes.","Activez les suggestions rapides dans les commentaires.","Activez les suggestions rapides en dehors des chaînes et des commentaires.","Contrôle si les suggestions doivent apparaître automatiquement pendant la saisie.","Contrôle le délai en millisecondes après lequel des suggestions rapides sont affichées.","Active une fenêtre contextuelle qui affiche de la documentation sur les paramètres et des informations sur les types à mesure que vous tapez.","Détermine si le menu de suggestions de paramètres se ferme ou reviens au début lorsque la fin de la liste est atteinte.","Utilisez les configurations de langage pour déterminer quand fermer automatiquement les parenthèses.","Fermer automatiquement les parenthèses uniquement lorsque le curseur est à gauche de l’espace.","Contrôle si l’éditeur doit fermer automatiquement les parenthèses quand l’utilisateur ajoute une parenthèse ouvrante.","Utilisez les configurations de langage pour déterminer quand fermer automatiquement les guillements.","Fermer automatiquement les guillements uniquement lorsque le curseur est à gauche de l’espace.","Contrôle si l’éditeur doit fermer automatiquement les guillemets après que l’utilisateur ajoute un guillemet ouvrant.","Tapez toujours avant les guillemets ou crochets fermants.","Tapez avant les guillemets ou les crochets fermants uniquement s'ils sont automatiquement insérés.","Ne tapez jamais avant les guillemets ou crochets fermants.","Contrôle si l'éditeur doit taper avant les guillemets ou crochets fermants.","Utilisez les configurations de langue pour déterminer quand entourer automatiquement les sélections.","Entourez avec des crochets et non des guillemets.","Entourez avec des guillemets et non des crochets.","Détermine si l'éditeur doit automatiquement entourer les sélections.","Contrôle si l’éditeur doit mettre automatiquement en forme la ligne après la saisie.","Détermine si l’éditeur doit automatiquement mettre en forme le contenu collé. Un formateur doit être disponible et être capable de mettre en forme une plage dans un document.","Contrôle si l’éditeur doit ajuster automatiquement la mise en retrait quand des utilisateurs tapent, collent ou déplacent des lignes. Les extensions avec des règles de mise en retrait du langage doivent être disponibles.","Contrôle si les suggestions devraient automatiquement s’afficher lorsque vous tapez les caractères de déclencheur.","Accepter uniquement une suggestion avec 'Entrée' quand elle effectue une modification textuelle.","Contrôle si les suggestions sont acceptées après appui sur 'Entrée', en plus de 'Tab'. Permet d’éviter toute ambiguïté entre l’insertion de nouvelles lignes et l'acceptation de suggestions.","Contrôle si les suggestions doivent être acceptées sur les caractères de validation. Par exemple, en JavaScript, le point-virgule (`;`) peut être un caractère de validation qui accepte une suggestion et tape ce caractère.","Afficher des suggestions d’extraits au-dessus d’autres suggestions.","Afficher des suggestions d’extraits en-dessous d’autres suggestions.","Afficher des suggestions d’extraits avec d’autres suggestions.","Ne pas afficher de suggestions d’extrait de code.","Contrôle si les extraits de code s'affichent en même temps que d'autres suggestions, ainsi que leur mode de tri.","Contrôle si la copie sans sélection permet de copier la ligne actuelle.","Contrôle si la coloration syntaxique doit être copiée dans le presse-papiers.","Contrôle si la saisie semi-automatique doit être calculée en fonction des mots présents dans le document.","Sélectionnez toujours la première suggestion.","Sélectionnez les suggestions récentes sauf si une entrée ultérieure en a sélectionné une, par ex., 'console.| -> console.log', car 'log' a été effectué récemment.","Sélectionnez des suggestions en fonction des préfixes précédents qui ont complété ces suggestions, par ex., 'co -> console' et 'con -> const'.","Contrôle comment les suggestions sont pré-sélectionnés lors de l’affichage de la liste de suggestion.","Taille de la police pour le widget de suggestion. Lorsque la valeur est à `0`, la valeur de `#editor.fontSize` est utilisée.","Hauteur de ligne pour le widget de suggestion. Lorsque la valeur est à `0`, la valeur de `#editor.lineHeight#` est utilisée.","La complétion par tabulation insérera la meilleure suggestion lorsque vous appuyez sur tab.","Désactiver les complétions par tabulation.","Compléter les extraits de code par tabulation lorsque leur préfixe correspond. Fonctionne mieux quand les 'quickSuggestions' ne sont pas activées.","Active les complétions par tabulation","Détermine si le filtre et le tri des suggestions doivent prendre en compte les fautes de frappes mineures.","Contrôle si le tri favorise trier les mots qui apparaissent près du curseur.","Contrôle si les sélections de suggestion mémorisées sont partagées entre plusieurs espaces de travail et fenêtres (nécessite '#editor.suggestSelection#').","Contrôler si un extrait de code actif empêche les suggestions rapides.","Contrôle s'il faut montrer ou masquer les icônes dans les suggestions.","Contrôle le nombre de suggestions IntelliSense affichées avant de montrer une barre de défilement (15 maximum).","Contrôle si certains types de suggestion doivent être filtrés dans IntelliSense. Une liste de types de suggestion est disponible ici : https://code.visualstudio.com/docs/editor/intellisense#_types-of-completions.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'method'.","Lorsque la valeur « false » est positionnée, IntelliSense n’affiche jamais de suggestion de « fonction ».","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'constructor'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'field'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'variable'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'class'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'struct'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'interface'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'module'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'property'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'event'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'operator'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'unit'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'value'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'constant'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'enum'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'enumMember'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'keyword'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'text'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'color'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'file'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'reference'.","Lorsque la valeur « false » est sélectionnée IntelliSense n’affiche jamais de suggestion pour « customcolor ».","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'folder'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'typeParameter'.","Quand la valeur est 'false', IntelliSense ne montre jamais de suggestions pour 'snippet'.","Contrôle le comportement des commandes 'Accéder à', comme Accéder à la définition, quand plusieurs emplacements cibles existent.","Montrer l'aperçu des résultats (par défaut)","Accéder au résultat principal et montrer un aperçu","Accéder au résultat principal et activer l'accès sans aperçu pour les autres","Contrôle si l'éditeur doit mettre en surbrillance les correspondances similaires à la sélection.","Contrôle si l'éditeur doit mettre en surbrillance les occurrences de symboles sémantiques.","Contrôle le nombre de décorations qui peuvent apparaître à la même position dans la règle de la vue d’ensemble.","Contrôle si une bordure doit être dessinée autour de la règle de la vue d'ensemble.","Contrôler le style d’animation du curseur.","Faire un zoom sur la police de l'éditeur quand l'utilisateur fait tourner la roulette de la souris tout en maintenant la touche 'Ctrl' enfoncée.","Contrôle si l'animation du point d'insertion doit être activée.","Contrôle le style du curseur.","Détermine la largeur du curseur lorsque `#editor.cursorStyle#` est à `line`.","Active/désactive les ligatures de police.","Contrôle si le curseur doit être masqué dans la règle de la vue d’ensemble.","Render whitespace characters except for single spaces between words.","Afficher les espaces blancs uniquement sur le texte sélectionné.","Contrôle la façon dont l’éditeur doit restituer les caractères espaces.","Contrôle si l’éditeur doit afficher les caractères de contrôle.","Contrôle si l’éditeur doit afficher les guides de mise en retrait.","Contrôle si l’éditeur doit mettre en surbrillance le guide de mise en retrait actif.","Met en surbrillance la gouttière et la ligne actuelle.","Contrôle la façon dont l’éditeur doit afficher la mise en surbrillance de la ligne actuelle.","Contrôle si l'éditeur affiche CodeLens.","Contrôle si l'éditeur a le pliage de code activé.","Contrôle la stratégie pour le calcul des plages de pliage. 'auto' utilise une stratégie de pliage spécifique au langage, le cas échéant. 'indentation' utilise la stratégie de pliage en fonction de la mise en retrait.","Définit si les contrôles de réduction sur la bordure sont cachés automatiquement","Met en surbrillance les crochets correspondants quand l'un d'eux est sélectionné.","Contrôle si l'éditeur doit afficher la marge de glyphes verticale. La marge de glyphes sert principalement au débogage.","L'insertion et la suppression des espaces blancs suit les taquets de tabulation.","Supprimer l'espace blanc de fin inséré automatiquement.","Garder les éditeurs d'aperçu ouverts même si l'utilisateur double-clique sur son contenu ou appuie sur la touche Échap. ","Contrôle si l’éditeur autorise le déplacement de sélections par glisser-déplacer.","L'éditeur utilise les API de la plateforme pour détecter si un lecteur d'écran est attaché.","L'éditeur est optimisé en permanence pour une utilisation avec un lecteur d'écran.","L'éditeur n'est jamais optimisé pour une utilisation avec un lecteur d'écran.","Contrôle si l'éditeur doit s'exécuter dans un mode optimisé pour les lecteurs d'écran.","Contrôle la disparition du code inutile.","Contrôle si l’éditeur doit détecter les liens et les rendre cliquables.","Contrôle si l'éditeur doit afficher les éléments décoratifs de couleurs inline et le sélecteur de couleurs.","Active l’ampoule d’action de code dans l’éditeur.","Les lignes plus longues que cette valeur ne sont pas tokenisées pour des raisons de performances","Contrôle si l'action Organiser les importations doit être exécutée au moment de l'enregistrement du fichier.","Contrôle si l'action de correction automatique doit être exécutée à l'enregistrement du fichier.","Types d'action de code à exécuter à l'enregistrement.","Délai d’attente en millisecondes après lequel les actions de code qui sont exécutées au moment de l'enregistrement sont annulées.","Contrôle si le presse-papiers principal Linux doit être pris en charge.","Contrôle si l'éditeur de différences affiche les différences en mode côte à côte ou inline.","Contrôle si l'éditeur de différences affiche les changements liés aux espaces blancs de début ou de fin comme des différences.","Traitement spécial des fichiers volumineux pour désactiver certaines fonctionnalités utilisant beaucoup de mémoire.","Contrôle si l'éditeur de différences affiche les indicateurs +/- pour les changements ajoutés/supprimés ."],
"vs/editor/common/config/editorOptions":["L'éditeur n'est pas accessible pour le moment. Appuyez sur Alt+F1 pour connaître les options.","Contenu d'éditeur"],"vs/editor/common/modes/modesRegistry":["Texte brut"],
"vs/editor/common/standaloneStrings":["Aucune sélection","Ligne {0}, colonne {1} ({2} sélectionné)","Ligne {0}, colonne {1}","{0} sélections ({1} caractères sélectionnés)","{0} sélections","Remplacement du paramètre 'accessibilitySupport' par 'on'.","Ouverture de la page de documentation sur l'accessibilité de l'éditeur.","dans un volet en lecture seule d'un éditeur de différences.","dans un volet d'un éditeur de différences."," dans un éditeur de code en lecture seule"," dans un éditeur de code","Pour configurer l'éditeur de manière à être optimisé en cas d'utilisation d'un lecteur d'écran, appuyez sur Commande+E maintenant.","Pour configurer l'éditeur de manière à être optimisé en cas d'utilisation d'un lecteur d'écran, appuyez sur Contrôle+E maintenant.","L'éditeur est configuré pour être optimisé en cas d'utilisation avec un lecteur d'écran.","L'éditeur est configuré pour ne jamais être optimisé en cas d'utilisation avec un lecteur d'écran, ce qui n'est pas le cas pour le moment.","Appuyez sur Tab dans l'éditeur pour déplacer le focus vers le prochain élément pouvant être désigné comme élément actif. Activez ou désactivez ce comportement en appuyant sur {0}.","Appuyez sur Tab dans l'éditeur pour déplacer le focus vers le prochain élément pouvant être désigné comme élément actif. La commande {0} ne peut pas être déclenchée par une combinaison de touches.","Appuyez sur Tab dans l'éditeur pour insérer le caractère de tabulation. Activez ou désactivez ce comportement en appuyant sur {0}.","Appuyez sur Tab dans l'éditeur pour insérer le caractère de tabulation. La commande {0} ne peut pas être déclenchée par une combinaison de touches.","Appuyez sur Commande+H maintenant pour ouvrir une fenêtre de navigateur avec plus d'informations sur l'accessibilité de l'éditeur.","Appuyez sur Contrôle+H maintenant pour ouvrir une fenêtre de navigateur avec plus d'informations sur l'accessibilité de l'éditeur.","Vous pouvez masquer cette info-bulle et revenir à l'éditeur en appuyant sur Échap ou Maj+Échap.","Afficher l'aide sur l'accessibilité","Développeur : Inspecter les jetons","Atteindre la ligne {0} et le caractère {1}","Atteindre la ligne {0}","Tapez un numéro de ligne à atteindre entre 1 et {0}","Taper un caractère entre 1 et {0} auquel accéder","Ligne actuelle : {0}. Accédez à la ligne {1}.","Taper un numéro de ligne, suivi d'un point-virgule facultatif et d'un numéro de caractère auquel accéder","Atteindre la ligne...","{0}, {1}, commandes","{0}, commandes","Taper le nom d'une action à exécuter","Palette de commandes","{0}, symboles","Taper le nom d'un identificateur auquel vous voulez accéder","Accéder au symbole...","symboles ({0})","modules ({0})","classes ({0})","interfaces ({0})","méthodes ({0})","fonctions ({0})","propriétés ({0})","variables ({0})","variables ({0})","constructeurs ({0})","appels ({0})","Contenu d'éditeur","Appuyez sur Ctrl+F1 pour voir les options d'accessibilité.","Appuyez sur Alt+F1 pour voir les options d'accessibilité.","Activer/désactiver le thème à contraste élevé","{0} modifications dans {1} fichiers"],
"vs/editor/common/view/editorColorRegistry":["Couleur d'arrière-plan de la mise en surbrillance de la ligne à la position du curseur.","Couleur d'arrière-plan de la bordure autour de la ligne à la position du curseur.","Couleur d'arrière-plan des plages mises en surbrillance, comme par les fonctionnalités de recherche et Quick Open. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan de la bordure autour des plages mises en surbrillance.","Couleur du curseur de l'éditeur.","La couleur de fond du curseur de l'éditeur. Permet de personnaliser la couleur d'un caractère survolé par un curseur de bloc.","Couleur des espaces blancs dans l'éditeur.","Couleur des repères de retrait de l'éditeur.","Couleur des guides d'indentation de l'éditeur actif","Couleur des numéros de ligne de l'éditeur.","Couleur des numéros de lignes actives de l'éditeur","L’ID est déprécié. Utilisez à la place 'editorLineNumber.activeForeground'.","Couleur des numéros de lignes actives de l'éditeur","Couleur des règles de l'éditeur","Couleur pour les indicateurs CodeLens","Couleur d'arrière-plan pour les accolades associées","Couleur pour le contour des accolades associées","Couleur de la bordure de la règle d'apperçu.","Couleur de fond pour la bordure de l'éditeur. La bordure contient les marges pour les symboles et les numéros de ligne.","Couleur de bordure du code source inutile (non utilisé) dans l'éditeur.","Opacité du code source inutile (non utilisé) dans l'éditeur. Par exemple, '#000000c0' affiche le code avec une opacité de 75 %. Pour les thèmes à fort contraste, utilisez la couleur de thème 'editorUnnecessaryCode.border' pour souligner le code inutile au lieu d'utiliser la transparence.","Couleur du marqueur de la règle d'aperçu pour les erreurs.","Couleur du marqueur de la règle d'aperçu pour les avertissements.","Couleur du marqueur de la règle d'aperçu pour les informations."],
"vs/editor/contrib/bracketMatching/bracketMatching":["Couleur du marqueur de la règle d'aperçu pour rechercher des parenthèses.","Atteindre le crochet","Select to Bracket","Accéder au &&crochet"],"vs/editor/contrib/caretOperations/caretOperations":["Déplacer le point d'insertion vers la gauche","Déplacer le point d'insertion vers la droite"],"vs/editor/contrib/caretOperations/transpose":["Transposer les lettres"],"vs/editor/contrib/clipboard/clipboard":["Couper","Co&&uper","Copier","&&Copier","Coller","Co&&ller","Copier avec la coloration syntaxique"],"vs/editor/contrib/codeAction/codeActionCommands":["Correction rapide...","Aucune action de code disponible","Aucune action de code disponible","Remanier...","Aucune refactorisation disponible","Action de la source","Aucune action n'est disponible","Organiser les Imports","Aucune action organiser les imports disponible","Tout corriger","Aucune action Tout corriger disponible","Corriger automatiquement...","Aucun correctif automatique disponible"],
"vs/editor/contrib/codeAction/lightBulbWidget":["Afficher les correctifs ({0})","Afficher les correctifs"],"vs/editor/contrib/comment/comment":["Activer/désactiver le commentaire de ligne","Afficher/masquer le commen&&taire de ligne","Ajouter le commentaire de ligne","Supprimer le commentaire de ligne","Activer/désactiver le commentaire de bloc","Afficher/masquer le commentaire de &&bloc"],"vs/editor/contrib/contextmenu/contextmenu":["Afficher le menu contextuel de l'éditeur"],"vs/editor/contrib/cursorUndo/cursorUndo":["Annulation réversible"],"vs/editor/contrib/find/findController":["Rechercher","&&Rechercher","Rechercher dans la sélection","Rechercher suivant","Rechercher suivant","Rechercher précédent","Rechercher précédent","Sélection suivante","Sélection précédente","Remplacer","&&Remplacer"],
"vs/editor/contrib/find/findWidget":["Rechercher","Rechercher","Correspondance précédente","Prochaine correspondance","Rechercher dans la sélection","Fermer","Remplacer","Remplacer","Remplacer","Tout remplacer","Changer le mode de remplacement","Seuls les {0} premiers résultats sont mis en évidence, mais toutes les opérations de recherche fonctionnent sur l’ensemble du texte.","{0} sur {1}","Aucun résultat","{0} trouvé(s)","{0} trouvé(s) pour {1}","{0} trouvé(s) pour {1} à {2}","{0} trouvé(s) pour {1}","La combinaison Ctrl+Entrée permet désormais d'ajouter un saut de ligne au lieu de tout remplacer. Vous pouvez modifier le raccourci clavier de editor.action.replaceAll pour redéfinir le comportement."],"vs/editor/contrib/folding/folding":["Déplier","Déplier de manière récursive","Plier","Plier de manière récursive","Replier tous les commentaires de bloc","Replier toutes les régions","Déplier toutes les régions","Plier tout","Déplier tout","Niveau de pliage {0}"],
"vs/editor/contrib/fontZoom/fontZoom":["Agrandissement de l'éditeur de polices de caractères","Rétrécissement de l'éditeur de polices de caractères","Remise à niveau du zoom de l'éditeur de polices de caractères"],"vs/editor/contrib/format/format":["1 modification de format effectuée à la ligne {0}","{0} modifications de format effectuées à la ligne {1}","1 modification de format effectuée entre les lignes {0} et {1}","{0} modifications de format effectuées entre les lignes {1} et {2}"],"vs/editor/contrib/format/formatActions":["Mettre le document en forme","Mettre la sélection en forme"],
"vs/editor/contrib/goToDefinition/goToDefinitionCommands":["Définition introuvable pour '{0}'","Définition introuvable"," – {0} définitions","Atteindre la définition","Ouvrir la définition sur le côté","Aperçu de définition","Aucune déclaration pour '{0}'","Aucune déclaration"," – {0} déclarations","Accéder à la déclaration","Aucune déclaration pour '{0}'","Aucune déclaration"," – {0} déclarations","Aperçu de la déclaration","Implémentation introuvable pour '{0}'","Implémentation introuvable","– Implémentations {0}","Accéder à l'implémentation","Aperçu de l'implémentation","Définition de type introuvable pour '{0}'","Définition de type introuvable"," – Définitions de type {0}","Atteindre la définition de type","Aperçu de la définition du type","Atteindre la &&définition","Accéder à la définition de &&type","Accéder à l'&&implémentation"],"vs/editor/contrib/goToDefinition/goToDefinitionMouse":["Cliquez pour afficher {0} définitions."],
"vs/editor/contrib/goToDefinition/goToDefinitionResultsNavigation":["Symbole {0} sur {1}, {2} pour le suivant","Symbole {0} sur {1}"],"vs/editor/contrib/gotoError/gotoError":["Aller au problème suivant (Erreur, Avertissement, Info)","Aller au problème précédent (Erreur, Avertissement, Info)","Aller au problème suivant dans Fichiers (Erreur, Avertissement, Info)","Aller au problème précédent dans Fichiers (Erreur, Avertissement, Info)","&&Problème suivant","&&Problème précédent"],"vs/editor/contrib/gotoError/gotoErrorWidget":["{0} problèmes sur {1}","{0} problème(s) sur {1}","Couleur d'erreur du widget de navigation dans les marqueurs de l'éditeur.","Couleur d'avertissement du widget de navigation dans les marqueurs de l'éditeur.","Couleur d’information du widget de navigation du marqueur de l'éditeur.","Arrière-plan du widget de navigation dans les marqueurs de l'éditeur."],"vs/editor/contrib/hover/hover":["Afficher par pointage"],
"vs/editor/contrib/hover/modesContentHover":["Chargement en cours...","Aperçu du problème","Recherche de correctifs rapides...","Aucune solution disponible dans l'immédiat","Correction rapide..."],"vs/editor/contrib/inPlaceReplace/inPlaceReplace":["Remplacer par la valeur précédente","Remplacer par la valeur suivante"],
"vs/editor/contrib/linesOperations/linesOperations":["Copier la ligne en haut","&&Copier la ligne en haut","Copier la ligne en bas","Co&&pier la ligne en bas","Déplacer la ligne vers le haut","Déplacer la ligne &&vers le haut","Déplacer la ligne vers le bas","Déplacer la &&ligne vers le bas","Trier les lignes dans l'ordre croissant","Trier les lignes dans l'ordre décroissant","Découper l'espace blanc de fin","Supprimer la ligne","Mettre en retrait la ligne","Ajouter un retrait négatif à la ligne","Insérer une ligne au-dessus","Insérer une ligne sous","Supprimer tout ce qui est à gauche","Supprimer tout ce qui est à droite","Joindre les lignes","Transposer les caractères autour du curseur","Transformer en majuscule","Transformer en minuscule",'Appliquer la casse "1re lettre des mots en majuscule"'],
"vs/editor/contrib/links/links":["Exécuter la commande","suivre le lien","cmd + clic","ctrl + clic","option + clic","alt + clic","Échec de l'ouverture de ce lien, car il n'est pas bien formé : {0}","Échec de l'ouverture de ce lien, car sa cible est manquante.","Ouvrir le lien"],"vs/editor/contrib/message/messageController":["Impossible de modifier dans l’éditeur en lecture seule"],
"vs/editor/contrib/multicursor/multicursor":["Ajouter un curseur au-dessus","&&Ajouter un curseur au-dessus","Ajouter un curseur en dessous","Aj&&outer un curseur en dessous","Ajouter des curseurs à la fin des lignes","Ajouter des c&&urseurs à la fin des lignes","Ajouter des curseurs en bas","Ajouter des curseurs en haut","Ajouter la sélection à la correspondance de recherche suivante","Ajouter l'occurrence suiva&&nte","Ajouter la sélection à la correspondance de recherche précédente","Ajouter l'occurrence p&&récédente","Déplacer la dernière sélection vers la correspondance de recherche suivante","Déplacer la dernière sélection à la correspondance de recherche précédente","Sélectionner toutes les occurrences des correspondances de la recherche","Sélectionner toutes les &&occurrences","Modifier toutes les occurrences"],"vs/editor/contrib/parameterHints/parameterHints":["Indicateurs des paramètres Trigger"],"vs/editor/contrib/parameterHints/parameterHintsWidget":["{0}, conseil"],
"vs/editor/contrib/referenceSearch/peekViewWidget":["Fermer"],"vs/editor/contrib/referenceSearch/referenceSearch":[" – {0} références","Aperçu des références"],"vs/editor/contrib/referenceSearch/referencesController":["Chargement en cours..."],"vs/editor/contrib/referenceSearch/referencesModel":["symbole dans {0} sur la ligne {1}, colonne {2}","1 symbole dans {0}, chemin complet {1}","{0} symboles dans {1}, chemin complet {2}","Résultats introuvables","1 symbole dans {0}","{0} symboles dans {1}","{0} symboles dans {1} fichiers"],"vs/editor/contrib/referenceSearch/referencesTree":["Échec de la résolution du fichier.","{0} références","{0} référence"],
"vs/editor/contrib/referenceSearch/referencesWidget":["aperçu non disponible","Références","Aucun résultat","Références","Couleur d'arrière-plan de la zone de titre de l'affichage d'aperçu.","Couleur du titre de l'affichage d'aperçu.","Couleur des informations sur le titre de l'affichage d'aperçu.","Couleur des bordures et de la flèche de l'affichage d'aperçu.","Couleur d'arrière-plan de la liste des résultats de l'affichage d'aperçu.","Couleur de premier plan des noeuds de lignes dans la liste des résultats de l'affichage d'aperçu.","Couleur de premier plan des noeuds de fichiers dans la liste des résultats de l'affichage d'aperçu.","Couleur d'arrière-plan de l'entrée sélectionnée dans la liste des résultats de l'affichage d'aperçu.","Couleur de premier plan de l'entrée sélectionnée dans la liste des résultats de l'affichage d'aperçu.","Couleur d'arrière-plan de l'éditeur d'affichage d'aperçu.","Couleur d'arrière-plan de la bordure de l'éditeur d'affichage d'aperçu.","Couleur de mise en surbrillance d'une correspondance dans la liste des résultats de l'affichage d'aperçu.","Couleur de mise en surbrillance d'une correspondance dans l'éditeur de l'affichage d'aperçu.","Bordure de mise en surbrillance d'une correspondance dans l'éditeur de l'affichage d'aperçu."],
"vs/editor/contrib/rename/rename":["Aucun résultat.","Une erreur inconnue s’est produite lors de la résolution de l'emplacement de renommage : {0}","'{0}' renommé en '{1}'. Récapitulatif : {2}","Échec de l'exécution du renommage.","Renommer le symbole"],"vs/editor/contrib/rename/renameInputField":["Renommez l'entrée. Tapez le nouveau nom et appuyez sur Entrée pour valider."],"vs/editor/contrib/smartSelect/smartSelect":["Étendre la sélection","Dév&&elopper la sélection","Réduire la sélection","&&Réduire la sélection"],"vs/editor/contrib/snippet/snippetVariables":["Dimanche","Lundi","Mardi","Mercredi","Jeudi","Vendredi","Samedi","Dim","Lun","Mar","Mer","Jeu","Ven","Sam","Janvier","Février","Mars","Avril","Mai","Juin","Juillet","Août","Septembre","Octobre","Novembre","Décembre","Jan","Fév","Mar","Avr","Mai","Juin","Jul","Aoû","Sept","Oct","Nov","Déc"],"vs/editor/contrib/suggest/suggestController":["L'acceptation de '{0}' a entraîné {1} modifications supplémentaires","Suggestions pour Trigger"],
"vs/editor/contrib/suggest/suggestWidget":["Couleur d'arrière-plan du widget de suggestion.","Couleur de bordure du widget de suggestion.","Couleur de premier plan du widget de suggestion.","Couleur d'arrière-plan de l'entrée sélectionnée dans le widget de suggestion.","Couleur de la surbrillance des correspondances dans le widget de suggestion.","En savoir plus...{0}","En savoir moins...{0}","Chargement en cours...","Chargement en cours...","Pas de suggestions.","Item {0}, docs: {1}"],"vs/editor/contrib/toggleTabFocusMode/toggleTabFocusMode":["Activer/désactiver l'utilisation de la touche Tab pour déplacer le focus","Appuyer sur Tab déplacera le focus vers le prochain élément pouvant être désigné comme élément actif","Appuyer sur Tab insérera le caractère de tabulation"],"vs/editor/contrib/tokenization/tokenization":["Développeur : forcer la retokenisation"],
"vs/editor/contrib/wordHighlighter/wordHighlighter":["Couleur d'arrière-plan d'un symbole pendant l'accès en lecture, comme la lecture d'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan d'un symbole pendant l'accès en écriture, comme l'écriture d'une variable. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure d'un symbole durant l'accès en lecture, par exemple la lecture d'une variable.","Couleur de bordure d'un symbole durant l'accès en écriture, par exemple l'écriture dans une variable.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des symboles. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des symboles d'accès en écriture. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Aller à la prochaine mise en évidence de symbole","Aller à la mise en évidence de symbole précédente","Déclencher la mise en évidence de symbole"],
"vs/platform/configuration/common/configurationRegistry":["Substitutions de configuration par défaut","Configurez les paramètres d'éditeur à remplacer pour un langage.","Impossible d'inscrire '{0}'. Ceci correspond au modèle de propriété '\\\\[.*\\\\]$' permettant de décrire les paramètres d'éditeur spécifiques à un langage. Utilisez la contribution 'configurationDefaults'.","Impossible d'inscrire '{0}'. Cette propriété est déjà inscrite."],"vs/platform/keybinding/common/abstractKeybindingService":["Touche ({0}) utilisée. En attente de la seconde touche pour la pression simultanée...","La combinaison de touches ({0}, {1}) n'est pas une commande."],
"vs/platform/list/browser/listService":["Banc d'essai","Mappe vers 'Contrôle' dans Windows et Linux, et vers 'Commande' dans macOS.","Mappe vers 'Alt' dans Windows et Linux, et vers 'Option' dans macOS.","Le modificateur à utiliser pour ajouter un élément dans les arbres et listes pour une sélection multiple avec la souris (par exemple dans l’Explorateur, les éditeurs ouverts et la vue scm). Les mouvements de la souris 'Ouvrir à côté' (si pris en charge) s'adapteront tels qu’ils n'entrent pas en conflit avec le modificateur multiselect.","Contrôle l’ouverture des éléments dans les arbres et listes à l’aide de la souris (si pris en charge). Pour les parents ayant des enfants dans les arbres, ce paramètre contrôlera si un simple clic déploie le parent ou un double-clic. Notez que certains arbres et listes peuvent choisir d’ignorer ce paramètre, si ce n’est pas applicable. ","Contrôle si les listes et les arborescences prennent en charge le défilement horizontal dans le banc d'essai.","Contrôle si les arborescences prennent en charge le défilement horizontal dans le plan de travail.","Ce paramètre est déprécié, utilisez '{0}' à la place.","Contrôle la mise en retrait de l'arborescence, en pixels.","Contrôle si l'arborescence doit afficher les repères de mise en retrait.","La navigation au clavier Simple place le focus sur les éléments qui correspondent à l'entrée de clavier. La mise en correspondance est effectuée sur les préfixes uniquement.","La navigation de mise en surbrillance au clavier met en surbrillance les éléments qui correspondent à l'entrée de clavier. La navigation ultérieure vers le haut ou vers le bas parcourt uniquement les éléments mis en surbrillance.","La navigation au clavier Filtrer filtre et masque tous les éléments qui ne correspondent pas à l'entrée de clavier.","Contrôle le style de navigation au clavier pour les listes et les arborescences dans le banc d'essai. Les options sont Simple, Mise en surbrillance et Filtrer.","Contrôle si la navigation au clavier dans les listes et les arborescences est automatiquement déclenchée simplement par la frappe. Si défini sur 'false', la navigation au clavier est seulement déclenchée avec l'exécution de la commande 'list.toggleKeyboardNavigation', à laquelle vous pouvez attribuer un raccourci clavier."],
"vs/platform/markers/common/markers":["Erreur","Avertissement","Info"],
"vs/platform/theme/common/colorRegistry":["Couleur de premier plan globale. Cette couleur est utilisée si elle n'est pas remplacée par un composant.","Couleur principale de premier plan pour les messages d'erreur. Cette couleur est utilisée uniquement si elle n'est pas redéfinie par un composant.","Couleur de bordure globale des éléments ayant le focus. Cette couleur est utilisée si elle n'est pas remplacée par un composant.","Bordure supplémentaire autour des éléments pour les séparer des autres et obtenir un meilleur contraste.","Bordure supplémentaire autour des éléments actifs pour les séparer des autres et obtenir un meilleur contraste.","Couleur des liens dans le texte.","Couleur d'arrière-plan des blocs de code dans le texte.","Couleur de l'ombre des widgets, comme rechercher/remplacer, au sein de l'éditeur.","Arrière-plan de la zone d'entrée.","Premier plan de la zone d'entrée.","Bordure de la zone d'entrée.","Couleur de la bordure des options activées dans les champs d'entrée.","Couleur d'arrière-plan des options activées dans les champs d'entrée.","Couleur d'arrière-plan de la validation d'entrée pour la gravité des informations.","Couleur de premier plan de validation de saisie pour la sévérité Information.","Couleur de bordure de la validation d'entrée pour la gravité des informations.","Couleur d'arrière-plan de la validation d'entrée pour la gravité de l'avertissement.","Couleur de premier plan de la validation de la saisie pour la sévérité Avertissement.","Couleur de bordure de la validation d'entrée pour la gravité de l'avertissement.","Couleur d'arrière-plan de la validation d'entrée pour la gravité de l'erreur.","Couleur de premier plan de la validation de saisie pour la sévérité Erreur.","Couleur de bordure de la validation d'entrée pour la gravité de l'erreur. ","Arrière-plan de la liste déroulante.","Premier plan de la liste déroulante.","Couleur d'arrière-plan de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/aborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/aborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arrière-plan de la liste/l'arborescence de l'élément sélectionné quand la liste/l'arborescence est active. Une liste/arborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est active. Une liste/aborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arrière-plan de la liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est inactive. Une liste/aborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur de premier plan de la liste/l'arborescence pour l'élément sélectionné quand la liste/l'arborescence est inactive. Une liste/aborescence active peut être sélectionnée au clavier, elle ne l'est pas quand elle est inactive.","Couleur d'arrière-plan de la liste/l'arborescence pour l'élément ayant le focus quand la liste/l'arborescence est active. Une liste/aborescence active peut être sélectionnée au clavier (elle ne l'est pas quand elle est inactive).","Arrière-plan de la liste/l'arborescence pendant le pointage sur des éléments avec la souris.","Premier plan de la liste/l'arborescence pendant le pointage sur des éléments avec la souris.","Arrière-plan de l'opération de glisser-déplacer dans une liste/arborescence pendant le déplacement d'éléments avec la souris.","Couleur de premier plan dans la liste/l'arborescence pour la surbrillance des correspondances pendant la recherche dans une liste/arborescence.","Couleur d'arrière-plan du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences.","Couleur de contour du widget de filtre de type dans les listes et les arborescences, en l'absence de correspondance.","Couleur de trait de l'arborescence pour les repères de mise en retrait.","Couleur du sélecteur rapide pour les étiquettes de regroupement.","Couleur du sélecteur rapide pour les bordures de regroupement.","Couleur de fond des badges. Les badges sont de courts libelés d'information, ex. le nombre de résultats de recherche.","Couleur des badges. Les badges sont de courts libelés d'information, ex. le nombre de résultats de recherche.","Ombre de la barre de défilement pour indiquer que la vue défile.","Couleur de fond du curseur de la barre de défilement.","Couleur de fond du curseur de la barre de défilement lors du survol.","Couleur d’arrière-plan de la barre de défilement lorsqu'on clique dessus.","Couleur de fond pour la barre de progression qui peut s'afficher lors d'opérations longues.","Couleur de bordure des menus.","Couleur de premier plan des éléments de menu.","Couleur d'arrière-plan des éléments de menu.","Couleur de premier plan de l'élément de menu sélectionné dans les menus.","Couleur d'arrière-plan de l'élément de menu sélectionné dans les menus.","Couleur de bordure de l'élément de menu sélectionné dans les menus.","Couleur d'un élément de menu séparateur dans les menus.","Couleur de premier plan de la ligne ondulée marquant les erreurs dans l'éditeur.","Couleur de bordure des zones d'erreur dans l'éditeur.","Couleur de premier plan de la ligne ondulée marquant les avertissements dans l'éditeur.","Couleur de bordure des zones d'avertissement dans l'éditeur.","Couleur de premier plan de la ligne ondulée marquant les informations dans l'éditeur.","Couleur de bordure des zones d'informations dans l'éditeur.","Couleur de premier plan de la ligne ondulée d'indication dans l'éditeur.","Couleur de bordure des zones d'indication dans l'éditeur.","Couleur d'arrière-plan de l'éditeur.","Couleur de premier plan par défaut de l'éditeur.","Couleur d'arrière-plan des gadgets de l'éditeur tels que rechercher/remplacer.","Couleur de premier plan des widgets de l'éditeur, notamment Rechercher/remplacer.","Couleur de bordure des widgets de l'éditeur. La couleur est utilisée uniquement si le widget choisit d'avoir une bordure et si la couleur n'est pas remplacée par un widget.","Couleur de bordure de la barre de redimensionnement des widgets de l'éditeur. La couleur est utilisée uniquement si le widget choisit une bordure de redimensionnement et si la couleur n'est pas remplacée par un widget.","Couleur de la sélection de l'éditeur.","Couleur du texte sélectionné pour le contraste élevé.","Couleur de la sélection dans un éditeur inactif. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur des régions dont le contenu est le même que celui de la sélection. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure des régions dont le contenu est identique à la sélection.","Couleur du résultat de recherche actif.","Couleur des autres correspondances de recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de bordure du résultat de recherche actif.","Couleur de bordure des autres résultats de recherche.","Couleur de bordure de la plage limitant la recherche. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Surlignage sous le mot sélectionné par pointage. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan du pointage de l'éditeur.","Couleur de bordure du pointage de l'éditeur.","Couleur d'arrière-plan de la barre d'état du pointage de l'éditeur.","Couleur des liens actifs.","Couleur d'arrière-plan du texte inséré. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur d'arrière-plan du texte supprimé. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de contour du texte inséré.","Couleur de contour du texte supprimé.","Couleur de bordure entre les deux éditeurs de texte.","Couleur d’arrière-plan de mise en surbrillance d’un extrait tabstop.","Couleur de bordure de mise en surbrillance d’un extrait tabstop.","Couleur d’arrière-plan de mise en surbrillance du tabstop final d’un extrait.","Couleur de la bordure de mise en surbrillance du tabstop final d’un extrait.","Couleur de marqueur de la règle d'aperçu pour rechercher les correspondances. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la règle d'aperçu pour la mise en surbrillance des sélections. La couleur ne doit pas être opaque pour ne pas masquer les ornements sous-jacents.","Couleur de marqueur de la minimap pour les correspondances."]
});
//# sourceMappingURL=../../../min-maps/vs/editor/editor.main.nls.fr.js.map