{"_from": "core-js@2.6.4", "_id": "core-js@2.6.4", "_inBundle": false, "_integrity": "sha512-05qQ5hXShcqGkPZpXEFLIpxayZscVD2kuMBZewxiIPPEagukO4mqgPA9CWhUvFBJfy3ODdK2p9xyHh7FTU9/7A==", "_location": "/core-js", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "core-js@2.6.4", "name": "core-js", "escapedName": "core-js", "rawSpec": "2.6.4", "saveSpec": null, "fetchSpec": "2.6.4"}, "_requiredBy": ["/", "/babel-runtime", "/karma", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/core-js/-/core-js-2.6.4.tgz", "_shasum": "b8897c062c4d769dd30a0ac5c73976c47f92ea0d", "_spec": "core-js@2.6.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "bugs": {"url": "https://github.com/zloirock/core-js/issues"}, "bundleDependencies": false, "deprecated": "core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.", "description": "Standard library", "devDependencies": {"LiveScript": "1.3.x", "es-observable-tests": "0.2.x", "eslint": "4.19.x", "eslint-plugin-import": "2.12.x", "grunt": "^1.0.2", "grunt-cli": "^1.2.0", "grunt-contrib-clean": "^1.1.0", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-uglify": "3.3.x", "grunt-contrib-watch": "^1.0.0", "grunt-karma": "^2.0.0", "grunt-livescript": "0.6.x", "karma": "^2.0.0", "karma-chrome-launcher": "^2.2.0", "karma-firefox-launcher": "^1.0.1", "karma-ie-launcher": "^1.0.0", "karma-phantomjs-launcher": "1.0.x", "karma-qunit": "^2.1.0", "phantomjs-prebuilt": "2.1.x", "promises-aplus-tests": "^2.1.2", "qunit": "2.6.x", "temp": "^0.8.3", "webpack": "^3.11.0"}, "homepage": "https://github.com/zloirock/core-js#readme", "keywords": ["ES3", "ES5", "ES6", "ES7", "ES2015", "ES2016", "ES2017", "ECMAScript 3", "ECMAScript 5", "ECMAScript 6", "ECMAScript 7", "ECMAScript 2015", "ECMAScript 2016", "ECMAScript 2017", "Harmony", "<PERSON><PERSON><PERSON>", "Map", "Set", "WeakMap", "WeakSet", "Promise", "Symbol", "TypedArray", "setImmediate", "Dict", "polyfill", "shim"], "license": "MIT", "main": "index.js", "name": "core-js", "repository": {"type": "git", "url": "git+https://github.com/zloirock/core-js.git"}, "scripts": {"grunt": "grunt", "lint": "eslint ./", "observables-tests": "node tests/observables/adapter && node tests/observables/adapter-library", "promises-tests": "promises-aplus-tests tests/promises-aplus/adapter", "test": "npm run grunt clean copy && npm run lint && npm run grunt livescript client karma:default && npm run grunt library karma:library && npm run promises-tests && npm run observables-tests && lsc tests/commonjs"}, "version": "2.6.4"}