{"_from": "ng5-slider@^1.1.14", "_id": "ng5-slider@1.2.6", "_inBundle": false, "_integrity": "sha512-xu3qfnCqVNndRY8DsdlsU4jKayJ6KTVlUQwSTWqRqplxY0d6QhK1V1mseWwcovZG7mOTqKBYymJL3zmX8+SbHQ==", "_location": "/ng5-slider", "_phantomChildren": {"symbol-observable": "1.0.1"}, "_requested": {"type": "range", "registry": true, "raw": "ng5-slider@^1.1.14", "name": "ng5-slider", "escapedName": "ng5-slider", "rawSpec": "^1.1.14", "saveSpec": null, "fetchSpec": "^1.1.14"}, "_requiredBy": ["/swt-tool-box"], "_resolved": "https://registry.npmjs.org/ng5-slider/-/ng5-slider-1.2.6.tgz", "_shasum": "1d3d6ee6b456334754e21d65dc9c2baa7062db96", "_spec": "ng5-slider@^1.1.14", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\bin", "author": {"name": "<PERSON><PERSON><PERSON>"}, "bugs": {"url": "https://github.com/angular-slider/ng5-slider/issues"}, "bundleDependencies": false, "dependencies": {"detect-passive-events": "^1.0.4", "rxjs": "^5.5.6", "rxjs-compat": "^6.5.2", "tslib": "^1.7.1"}, "deprecated": "ng5-slider is now replaced by @angular-slider/ngx-slider targeting Angular 6+ and rxjs 6+", "description": "Self-contained, mobile friendly slider component for Angular 5+ based on angularjs-slider", "es2015": "esm2015/ng5-slider.js", "homepage": "https://github.com/angular-slider/ng5-slider#readme", "keywords": ["slider", "ui", "component", "angular", "ng", "angular2", "ng2", "angular4", "ng4", "angular5", "ng5"], "license": "MIT", "main": "bundles/ng5-slider.umd.js", "metadata": "ng5-slider.metadata.json", "module": "esm5/ng5-slider.js", "name": "ng5-slider", "peerDependencies": {"@angular/core": ">=5.0.2", "@angular/common": ">=5.0.2", "@angular/forms": ">=5.0.2"}, "repository": {"type": "git", "url": "git+https://github.com/angular-slider/ng5-slider.git"}, "typings": "ng5-slider.d.ts", "version": "1.2.6"}