<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Matching Process Flowcharts - Index</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        .header p {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.9;
        }
        .content {
            padding: 40px 30px;
        }
        .flowchart-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        .flowchart-card {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 25px;
            border-left: 5px solid #007bff;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .flowchart-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-left-color: #28a745;
        }
        .flowchart-card h3 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 1.4em;
        }
        .flowchart-card p {
            margin: 0 0 15px 0;
            color: #666;
            line-height: 1.6;
        }
        .flowchart-card .features {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }
        .flowchart-card .features li {
            padding: 5px 0;
            color: #555;
            position: relative;
            padding-left: 20px;
        }
        .flowchart-card .features li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,123,255,0.3);
        }
        .overview {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 25px;
            margin-bottom: 30px;
            border-left: 5px solid #2196f3;
        }
        .overview h2 {
            margin: 0 0 15px 0;
            color: #1976d2;
        }
        .overview p {
            margin: 0;
            color: #424242;
            line-height: 1.6;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Matching Process Flowcharts</h1>
            <p>Comprehensive visualization of the PK_MATCHING_PROCESS package</p>
        </div>
        
        <div class="content">
            <div class="overview">
                <h2>📊 Overview</h2>
                <p>
                    These flowcharts provide a detailed visual representation of the main matching process from the 
                    PK_MATCHING_PROCESS Oracle package. Each flowchart focuses on different aspects of the complex 
                    matching logic, from high-level process flow to detailed quality calculations and decision points.
                </p>
            </div>
            
            <div class="flowchart-grid">
                <div class="flowchart-card" onclick="window.open('01_main_matching_process.html', '_blank')">
                    <h3>🔄 Main Matching Process</h3>
                    <p>High-level overview of the complete matching process flow including position level processing, source and target selection, and match completion logic.</p>
                    <ul class="features">
                        <li>11-position iteration cycle</li>
                        <li>Source and target cursor processing</li>
                        <li>Major decision points and branching</li>
                        <li>Match completion workflow</li>
                    </ul>
                    <a href="01_main_matching_process.html" class="btn" target="_blank">View Flowchart</a>
                </div>
                
                <div class="flowchart-card" onclick="window.open('02_detailed_conditions_decisions.html', '_blank')">
                    <h3>⚙️ Detailed Conditions & Decisions</h3>
                    <p>Comprehensive view of configuration parameters, position level logic, and critical decision points with all conditions and default values.</p>
                    <ul class="features">
                        <li>Configuration parameters & defaults</li>
                        <li>Position level determination logic</li>
                        <li>Target check strategy selection</li>
                        <li>Cross-reference validation rules</li>
                    </ul>
                    <a href="02_detailed_conditions_decisions.html" class="btn" target="_blank">View Flowchart</a>
                </div>
                
                <div class="flowchart-card" onclick="window.open('03_quality_calculation_matching.html', '_blank')">
                    <h3>🎯 Quality Calculation & Matching</h3>
                    <p>Detailed breakdown of the quality scoring system, match action processing, and the complete quality-based matching logic.</p>
                    <ul class="features">
                        <li>Quality scoring system (A=5 to E=1)</li>
                        <li>Multi-factor quality assessment</li>
                        <li>Match action logic (A,B,C,D,E,N)</li>
                        <li>Amount total processing</li>
                    </ul>
                    <a href="03_quality_calculation_matching.html" class="btn" target="_blank">View Flowchart</a>
                </div>
            </div>
            
            <div style="margin-top: 40px; padding: 25px; background: #f8f9fa; border-radius: 8px; text-align: center;">
                <h3>🔍 Key Process Highlights</h3>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                    <div>
                        <strong>Position Processing</strong><br>
                        <small>11 different position level configurations processed sequentially</small>
                    </div>
                    <div>
                        <strong>Quality Factors</strong><br>
                        <small>Date, Amount, Account, Counterparty, Beneficiary, Custodian, Book Code</small>
                    </div>
                    <div>
                        <strong>Match Actions</strong><br>
                        <small>Auto, Offer, Confirm, Decline, Exception, None</small>
                    </div>
                    <div>
                        <strong>Decision Points</strong><br>
                        <small>Scheduler status, cross-references, quality thresholds</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Add click handlers for better UX
        document.querySelectorAll('.flowchart-card').forEach(card => {
            card.addEventListener('click', function(e) {
                if (e.target.tagName !== 'A') {
                    const link = this.querySelector('a');
                    if (link) {
                        window.open(link.href, '_blank');
                    }
                }
            });
        });
    </script>
</body>
</html>
