(window.webpackJsonp=window.webpackJsonp||[]).push([[60],{d9SJ:function(e,t,i){"use strict";i.r(t);var s=i("CcnG"),a=i("mrSG"),n=i("ZYCi"),o=i("447K"),l=i("YCOA"),r=i("HXd3"),c=i("hM1c"),d=i("UqOt"),h=i("rMIR"),b=i("xRo1"),u=i("EVdn"),m=function(e){function t(t,i){var s=e.call(this,i,t)||this;return s.commonService=t,s.element=i,s.methodName="",s.defaultVal=[],s.savedAlertInstCols=[],s.savedResolutionRefCols=[],s.defaultValString="",s.selectedFacilityId="",s.inputData=new o.G(s.commonService),s.sendData=new o.G(s.commonService),s.requestParams=[],s.baseURL=o.Wb.getBaseURL(),s.actionPath="",s.actionMethod="",s.jsonReader=new o.L,s.jsonReader1=new o.L,s.selectedScenarioID="",s.selectedSystemFlag="",s.menuAccessId="",s.fromAdvanced="",s.operationsList=[],s.defaultQuery="",s.defaultHost="",s.defaultEntity="",s.defaultCcy="",s.defaultAmount="",s.defaultAccount="",s.defaultSign="",s.defaultValueDate="",s.defaultOtherId="",s.defaultOtherIdType="",s.defaultUniqueExp="",s.defaultMvt="",s.defaultMatchId="",s.defaultSweepId="",s.defaultPay="",s.defaultCustomTreeLevel1="",s.defaultCustomTreeLevel2="",s.defaultOptions=[],s.scheduleGenData=[],s.provider=[{type:"",value:0,selected:0,content:""}],s.savedTreeProvider=[{type:"",value:0,selected:0,content:""}],s.alertInstProvider=[],s.savedAlertInstProvider=[],s.savedTooltip="",s.eventItemLimit=3,s.swtalert=new o.bb(t),s}return a.d(t,e),t.prototype.ngOnInit=function(){this.scenarioIdLbl.text=o.Wb.getPredictMessage("scenario.scenarioId",null)+"*",this.titleLbl.text=o.Wb.getPredictMessage("scenario.title",null)+"*",this.desciptionLbl.text=o.Wb.getPredictMessage("scenario.description",null),this.systemLbl.text=o.Wb.getPredictMessage("scenario.system",null),this.activeLbl.text=o.Wb.getPredictMessage("scenario.active",null),this.saveButton.label=o.Wb.getPredictMessage("button.save",null),this.cancelButton.label=o.Wb.getPredictMessage("button.cancel",null),this.scenarioIdTxt.toolTip=o.Wb.getPredictMessage("tooltip.enterScenario",null),this.titleTxt.toolTip=o.Wb.getPredictMessage("tooltip.enterScenarioTitle",null),this.descriptionTxt.toolTip=o.Wb.getPredictMessage("tooltip.Description",null),this.activeCheck.toolTip=o.Wb.getPredictMessage("tooltip.activeFlag",null),this.systemCheck.toolTip=o.Wb.getPredictMessage("tooltip.systemFlag",null),this.saveButton.toolTip=o.Wb.getPredictMessage("tooltip.saveExit",null),this.cancelButton.toolTip=o.Wb.getPredictMessage("tooltip.closeWindow",null),this.scenarioIdTxt.required=!0,this.titleTxt.required=!0},t.prototype.enableDisableFields=function(e){"systemCheck"==e?(this.scenarioIdTxt.enabled=!1,this.titleTxt.enabled=!1,this.descriptionTxt.editable=!1,this.activeCheck.enabled=!1,this.activeLbl.enabled=!1,this.generalTab.recordCheck.enabled=!1,this.generalTab.categoryCombo.enabled=!1,this.generalTab.runTxt.enabled=!1,this.generalTab.startTxt.enabled=!1,this.generalTab.endTxt.enabled=!1,this.generalTab.cyclic.enabled=!1,this.generalTab.createInst.enabled=!1,this.generalTab.scheduled.enabled=!1,this.generalTab.defParams.enabled=!1,this.generalTab.defParams.buttonMode=!1,this.generalTab.addButton.enabled=!1,this.generalTab.addButton.buttonMode=!1,this.generalTab.changeButton.enabled=!1,this.generalTab.changeButton.buttonMode=!1,this.generalTab.deleteButton.enabled=!1,this.generalTab.deleteButton.buttonMode=!1,this.generalTab.configButton.enabled=!1,this.generalTab.configButton.buttonMode=!1,this.identificationTab.baseQuery.editable=!1,this.identificationTab.hostColCombo.enabled=!1,this.identificationTab.entityColCombo.enabled=!1,this.identificationTab.ccyColCombo.enabled=!1,this.identificationTab.amountColCombo.enabled=!1,this.identificationTab.genericCheck.enabled=!1,this.identificationTab.facilityCombo.enabled=!1,this.identificationTab.refColumnCombo.isDropdownDisabled=!0,this.identificationTab.paramValueTxt.enabled=!1,this.instancesTab.alertInstanceColumnCombo.isDropdownDisabled=!0,this.instancesTab.uniqueExpression.editable=!1,this.instancesTab.acctIdCombo.enabled=!1,this.instancesTab.valueDateCombo.enabled=!1,this.instancesTab.signColCombo.enabled=!1,this.instancesTab.mvtColCombo.enabled=!1,this.instancesTab.matchColCombo.enabled=!1,this.instancesTab.sweepColCombo.enabled=!1,this.instancesTab.payColCombo.enabled=!1,this.instancesTab.otherIdColCombo.enabled=!1,this.instancesTab.otherIdTypeCombo.enabled=!1,this.instancesTab.treeBreakDown1Combo.enabled=!1,this.instancesTab.treeBreakDown2Combo.enabled=!1,this.instancesTab.instExpTxt.enabled=!1,this.instancesTab.radioNo.enabled=!1,this.instancesTab.radioAfter.enabled=!1,this.instancesTab.afterMinTxt.enabled=!1):"change"==e?(this.scenarioIdTxt.enabled=!1,this.identificationTab.entityColCombo.enabled=(this.identificationTab.entityColCombo.selectedLabel.length>0||this.defaultHost)&&!this.systemCheck.selected,this.identificationTab.ccyColCombo.enabled=(this.identificationTab.ccyColCombo.selectedLabel.length>0||this.defaultEntity)&&!this.systemCheck.selected,this.identificationTab.refColumnCombo.isDropdownDisabled=!(this.defaultVal.length>0&&!this.systemCheck.selected),this.identificationTab.paramValueTxt.enabled=this.identificationTab.paramValueTxt.text.length>0&&!this.systemCheck.selected):"view"==e&&(this.scenarioIdTxt.enabled=!1,this.titleTxt.enabled=!1,this.descriptionTxt.editable=!1,this.activeCheck.enabled=!1,this.activeLbl.enabled=!1,this.saveButton.enabled=!1,this.generalTab.categoryCombo.enabled=!1,this.generalTab.orderTxt.enabled=!1,this.generalTab.runTxt.enabled=!1,this.generalTab.startTxt.enabled=!1,this.generalTab.endTxt.enabled=!1,this.generalTab.emailTxt.enabled=!1,this.generalTab.cyclic.enabled=!1,this.generalTab.scheduled.enabled=!1,this.generalTab.createInst.enabled=!1,this.generalTab.schedGrid.enabled=!1,this.generalTab.defParams.enabled=!1,this.generalTab.addButton.enabled=!1,this.generalTab.changeButton.enabled=!1,this.generalTab.deleteButton.enabled=!1,this.generalTab.configButton.enabled=!1,this.identificationTab.baseQuery.editable=!1,this.identificationTab.hostColCombo.enabled=!1,this.identificationTab.entityColCombo.enabled=!1,this.identificationTab.ccyColCombo.enabled=!1,this.identificationTab.amountColCombo.enabled=!1,this.identificationTab.defGroupCombo.enabled=!1,this.identificationTab.genericCheck.enabled=!1,this.identificationTab.facilityCombo.enabled=!1,this.identificationTab.refColumnCombo.isDropdownDisabled=!0,this.instancesTab.alertInstanceColumnCombo.isDropdownDisabled=!0,this.identificationTab.paramValueTxt.enabled=!1,this.identificationTab.testButton.enabled=!1,this.instancesTab.uniqueExpression.editable=!1,this.instancesTab.acctIdCombo.enabled=!1,this.instancesTab.signColCombo.enabled=!1,this.instancesTab.mvtColCombo.enabled=!1,this.instancesTab.matchColCombo.enabled=!1,this.instancesTab.sweepColCombo.enabled=!1,this.instancesTab.payColCombo.enabled=!1,this.instancesTab.otherIdColCombo.enabled=!1,this.instancesTab.otherIdTypeCombo.enabled=!1,this.instancesTab.treeBreakDown1Combo.enabled=!1,this.instancesTab.treeBreakDown2Combo.enabled=!1,this.instancesTab.valueDateCombo.enabled=!1,this.instancesTab.instExpTxt.enabled=!1,this.instancesTab.radioNo.enabled=!1,this.instancesTab.radioNo.enabled=!1,this.instancesTab.radioAfter.enabled=!1,this.instancesTab.afterMinTxt.enabled=!1,this.guiHighlightTab.guiGrid.enabled=!1,this.eventsTab.eventsGrid.enabled=!1,this.eventsTab.eventsGrid.selectable=!1,this.eventsTab.imgUpButton.enabled=!1,this.eventsTab.imgDownButton.enabled=!1,this.eventsTab.addButton.enabled=!1,this.eventsTab.changeButton.enabled=!1,this.eventsTab.deleteButton.enabled=!1,this.eventsTab.triggeEventsRadioGroup.enabled=!1,this.eventsTab.resolutionQueryText.enabled=!1,this.eventsTab.refColumnCombo.isDropdownDisabled=!0,this.eventsTab.minsText.enabled=!1)},t.prototype.dynamicCreation=function(){this.general=this.tabNavigator.addChild(o.Xb),this.identification=this.tabNavigator.addChild(o.Xb),this.instances=this.tabNavigator.addChild(o.Xb),this.guiHighlight=this.tabNavigator.addChild(o.Xb),this.events=this.tabNavigator.addChild(o.Xb),this.general.label=this.general.id=o.Wb.getPredictMessage("scenario.tab.general",null),this.identification.label=this.identification.id=o.Wb.getPredictMessage("scenario.tab.identification",null),this.instances.label=this.instances.id=o.Wb.getPredictMessage("scenario.tab.instances",null),this.guiHighlight.label=this.guiHighlight.id=o.Wb.getPredictMessage("scenario.tab.guiHighlight",null),this.events.label=this.events.id=o.Wb.getPredictMessage("scenario.tab.events",null),this.generalTab=this.general.addChild(r.a),this.identificationTab=this.identification.addChild(d.a),this.instancesTab=this.instances.addChild(h.a),this.guiHighlightTab=this.guiHighlight.addChild(c.a),this.eventsTab=this.events.addChild(l.a),this.generalTab.height="100%",this.identificationTab.height="100%",this.instancesTab.height="100%",this.guiHighlightTab.height="100%",this.eventsTab.height="100%",this.generalTab.parentDocument=this.identificationTab.parentDocument=this.instancesTab.parentDocument=this.guiHighlightTab.parentDocument=this.eventsTab.parentDocument=this},t.prototype.onLoad=function(){var e=this;this.dynamicCreation(),this.lockIcon=o.x.call("eval","lockImage"),this.unlockIcon=o.x.call("eval","unlockImage"),this.methodName=o.x.call("eval","methodName"),this.selectedScenarioID=o.x.call("eval","selectedScenarioID"),this.selectedSystemFlag=o.x.call("eval","selectedSystemFlag"),this.menuAccessId=o.x.call("eval","menuAccessId"),this.fromAdvanced="false",this.requestParams=[],this.actionPath="scenMaintenance.do?",this.actionMethod="method="+this.methodName,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.requestParams.fromAdvanced=this.fromAdvanced,this.requestParams.selectedScenarioID=this.selectedScenarioID,this.requestParams.selectedSystemFlag=this.selectedSystemFlag,this.requestParams.menuAccessId=this.menuAccessId,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},t.prototype.enableDisableRow=function(e,t){return"N"==e.colorflag},t.prototype.startOfComms=function(){this.loadingImg.setVisible(!0)},t.prototype.endOfComms=function(){this.loadingImg.setVisible(!1)},t.prototype.inputDataResult=function(e){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyStatus()&&this.lastRecievedJSON!=this.prevRecievedJSON&&(this.jsonReader.isDataBuilding()||(this.fillDetails(),this.fillGeneralTab(),this.fillIdentificationTab(),this.fillInstanceData(),this.fillGuiHighlightData(null),this.fillEventsData(),this.enableDisableFields(this.methodName),"change"==this.methodName&&this.checkIfInstExist())))},t.prototype.fillDetails=function(){"add"!=this.methodName?(this.scenarioIdTxt.text=this.jsonReader.getSingletons().scenarioId,this.titleTxt.text=this.jsonReader.getSingletons().scenarioTitle,this.descriptionTxt.text=this.jsonReader.getSingletons().scenarioDesc,this.activeCheck.selected="Y"==this.jsonReader.getSingletons().selectedActiveFlag,this.systemCheck.selected="Y"==this.jsonReader.getSingletons().selectedSystemFlag):this.activeCheck.selected=!1,this.systemCheck.selected&&this.enableDisableFields("systemCheck")},t.prototype.fillGeneralTab=function(){if(this.generalTab.schedGrid.CustomGrid(this.lastRecievedJSON.scenarioDetails.generalGrid.metadata),this.generalTab.categoryCombo.setComboData(this.jsonReader.getSelects(),!1),this.generalTab.selectedCategLbl.text=this.generalTab.categoryCombo.selectedValue,this.systemCheck.selected&&(this.scenarioIdTxt.editable=!1,this.titleTxt.editable=!1,this.descriptionTxt.editable=!1,this.generalTab.recordCheck.enabled=!1,this.generalTab.startTxt.editable=!1,this.generalTab.endTxt.editable=!1),"add"!=this.methodName){this.generalTab.recordCheck.enabled=!1,this.generalTab.recordCheck.selected="Y"==this.jsonReader.getSingletons().recordScenInstance;var e=this.jsonReader.getSingletons().scheduleParamsXml.replace(/#/g,">");e?this.prepareGenGridData(e):this.generalTab.schedGrid.gridData={row:[],size:0},this.typeSelectedValue=this.jsonReader.getSingletons().savedGenBasis?this.jsonReader.getSingletons().savedGenBasis:"C",this.generalTab.typeOptions.selectedValue=this.typeSelectedValue,this.populateRadioGrValue(this.typeSelectedValue),this.generalTab.savedApiParams=this.jsonReader.getSingletons().savedApiParams,this.generalTab.CreateInsDesc.text="[]"!=this.jsonReader.getSingletons().savedApiParams&&"A"==this.generalTab.typeOptions.selectedValue?o.Wb.getPredictMessage("scenario.CreateInsDescFull",null)+"<"+this.jsonReader.getSingletons().savedApiParams.replace("[","").replace("]","").replace(/['"]+/g,"")+">":o.Wb.getPredictMessage("scenario.CreateInsDesc",null),this.generalTab.xmlParams=this.jsonReader.getSingletons().requiredParamsXml?this.jsonReader.getSingletons().requiredParamsXml.replace(/#/g,">"):"",this.generalTab.orderTxt.text=this.jsonReader.getSingletons().displayOrder,this.generalTab.runTxt.text=this.jsonReader.getSingletons().runEvery,this.generalTab.startTxt.text=this.jsonReader.getSingletons().startTime,this.generalTab.endTxt.text=this.jsonReader.getSingletons().endTime,this.generalTab.emailTxt.text=this.jsonReader.getSingletons().emailWhenDiff,this.generalTab.categoryCombo.selectedLabel=this.jsonReader.getSingletons().selectedCategoryId,this.generalTab.selectedCategLbl.text=this.generalTab.categoryCombo.selectedValue}else{var t=this.generalTab.generalGridData?this.generalTab.generalGridData:[];this.generalTab.schedGrid.gridData={row:t,size:t.length},this.generalTab.recordCheck.selected?(this.generalTab.cyclic.enabled=!1,this.generalTab.scheduled.selected=!0,this.generalTab.scheduled.enabled=!0,this.generalTab.createInst.enabled=!0):(this.generalTab.cyclic.selected=!0,this.generalTab.cyclic.enabled=!0,this.generalTab.scheduled.enabled=!1,this.generalTab.createInst.enabled=!1)}},t.prototype.prepareGenGridData=function(e){for(var t=e.split(","),i=0;i<t.length;i++){var s=t[i].split("="),a=s[0].split("-")[0].trim(),n=s[0].split("-")[1].trim(),o=s[1];this.convertXml(a,n,o)}},t.prototype.convertXml=function(e,t,i){var s="";if(i){var a=b.xml2js(i,{object:!1,reversible:!1,coerce:!1,sanitize:!0,trim:!0,arrayNotation:!1,alternateTextNode:!1,compact:!0}).PARAMETERS.PARAMETER;if(a){a.length||(a=[a]);for(var n=0;n<a.length;n++){a[n].length||(a[n]=[a[n]]);for(var o=0;o<a[n].length;o++)a[n][o].NAME._cdata&&(s=s+a[n][o].NAME._cdata+"("+(a[n][o].VALUE._cdata?a[n][o].VALUE._cdata:"")+");")}this.scheduleGenData.push({schedulerId:{clickable:!1,content:e,negative:!1},time:{clickable:!1,content:t,negative:!1},parameters:{clickable:!1,content:s.slice(0,-1),negative:!1}})}else this.scheduleGenData.push({schedulerId:{clickable:!1,content:e,negative:!1},time:{clickable:!1,content:t,negative:!1},parameters:{clickable:!1,content:"",negative:!1}})}this.generalTab.schedGrid.gridData={row:this.scheduleGenData,size:this.scheduleGenData.length},this.generalTab.oldSchedRows=this.scheduleGenData,this.generalTab.schedGrid.refresh()},t.prototype.populateRadioGrValue=function(e){switch(e){case"C":this.generalTab.cyclic.selected=!0,this.generalTab.scheduled.enabled=!1,this.generalTab.createInst.enabled=!1;break;case"S":this.generalTab.scheduled.selected=!0,this.generalTab.cyclic.enabled=!1,this.generalTab.createInst.enabled=!1;break;case"A":this.generalTab.createInst.selected=!0,this.generalTab.cyclic.enabled=!1,this.generalTab.scheduled.enabled=!1}this.generalTab.enableDisableComponents()},t.prototype.fillIdentificationTab=function(){if(this.identificationTab.BaseQueryLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.identificationTab.hostLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.identificationTab.entityLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.identificationTab.ccyLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.identificationTab.amountLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.identificationTab.defGroupCombo.setComboData(this.jsonReader.getSelects(),!0),this.identificationTab.facilityCombo.setComboData(this.jsonReader.getSelects(),!1),"add"!=this.methodName){if(this.identificationTab.baseQuery.text=this.htmlEntities(o.t.decode64(this.jsonReader.getSingletons().queryText)),this.defaultQuery=o.t.decode64(this.jsonReader.getSingletons().queryText),this.defaultHost=this.jsonReader.getSingletons().hostCol,this.defaultEntity=this.jsonReader.getSingletons().entityCol,this.defaultAmount=this.jsonReader.getSingletons().amountCol,this.defaultCcy=this.jsonReader.getSingletons().currencyCol,this.columns=this.jsonReader.getSelects().select.find(function(e){return"queryColumns"==e.id}),this.nbrColumns=this.jsonReader.getSelects().select.find(function(e){return"numbersColumns"==e.id}),this.txtColumns=this.jsonReader.getSelects().select.find(function(e){return"textColumns"==e.id}),this.dateColumns=this.jsonReader.getSelects().select.find(function(e){return"dateColumns"==e.id}),this.txtColumns){var e=this.txtColumns.option;e&&!e.length&&(e=[e]),this.identificationTab.hostColCombo.setComboData(e),this.identificationTab.hostColCombo.dataProvider=e,this.identificationTab.entityColCombo.setComboData(e),this.identificationTab.entityColCombo.dataProvider=e,this.identificationTab.ccyColCombo.setComboData(e),this.identificationTab.ccyColCombo.dataProvider=e}if(this.dateColumns){var t=this.dateColumns.option;t&&!t.length&&(t=[t])}if(this.nbrColumns){var i=this.nbrColumns.option;i&&!i.length&&(i=[i]),this.identificationTab.amountColCombo.setComboData(i),this.identificationTab.amountColCombo.dataProvider=i}if(this.columns){var s=this.columns.option,a=u.extend(!0,[],s);a.splice(0,1),a.sort(function(e,t){return e.content.localeCompare(t.content)}),this.identificationTab.refColumnCombo.dataProvider=a}else{var n=[this.jsonReader.getSelects().select.find(function(e){return"selectedHost"==e.id}).option],l=[this.jsonReader.getSelects().select.find(function(e){return"selectedEntity"==e.id}).option],r=[this.jsonReader.getSelects().select.find(function(e){return"selectedCurrency"==e.id}).option],c=[this.jsonReader.getSelects().select.find(function(e){return"selectedAmount"==e.id}).option];this.identificationTab.hostColCombo.setComboData(n),this.identificationTab.hostColCombo.dataProvider=n,this.identificationTab.entityColCombo.setComboData(l),this.identificationTab.entityColCombo.dataProvider=l,this.identificationTab.ccyColCombo.setComboData(r),this.identificationTab.ccyColCombo.dataProvider=r,this.identificationTab.amountColCombo.setComboData(c),this.identificationTab.amountColCombo.dataProvider=c}this.identificationTab.hostColCombo.selectedLabel=this.defaultHost,this.identificationTab.entityColCombo.selectedLabel=this.defaultEntity,this.identificationTab.ccyColCombo.selectedLabel=this.defaultCcy,this.identificationTab.amountColCombo.selectedLabel=this.defaultAmount,this.identificationTab.genericCheck.selected="Y"==this.jsonReader.getSingletons().useGenericDisplay,this.defaultVal=this.jsonReader.getSelects().select.find(function(e){return"listSelections"==e.id}).option,this.savedAlertInstCols=this.jsonReader.getSelects().select.find(function(e){return"savedAlertInstCols"==e.id}).option,this.defaultVal?this.defaultVal.length||(this.defaultVal=[this.defaultVal]):this.defaultVal=[],this.savedAlertInstCols?this.savedAlertInstCols.length||(this.savedAlertInstCols=[this.savedAlertInstCols]):this.savedAlertInstCols=[],this.columns||(this.defaultVal.sort(function(e,t){return e.content.localeCompare(t.content)}),this.identificationTab.refColumnCombo.dataProvider=this.defaultVal),this.defaultVal.length>0&&(this.identificationTab.refColumnCombo.toolTip=this.getTooltip(this.defaultVal)),this.identificationTab.refColumnCombo.defaultSelectedItems=this.defaultVal,this.identificationTab.refColumnCombo.selects=this.defaultVal,this.savedAlertInstCols.length>0&&(this.instancesTab.alertInstanceColumnCombo.toolTip=this.getTooltip(this.savedAlertInstCols)),this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems=this.savedAlertInstCols,this.instancesTab.alertInstanceColumnCombo.selects=this.savedAlertInstCols,this.identificationTab.paramValueTxt.text=this.jsonReader.getSingletons().facilityParamVals,this.identificationTab.defGroupCombo.selectedValue=this.jsonReader.getSingletons().selectedSummaryGrouping,this.selectedFacilityId=this.jsonReader.getSingletons().selectedfacilityId,this.identificationTab.facilityCombo.selectedLabel=this.selectedFacilityId}"add"==this.methodName||"N"==this.identificationTab.defGroupCombo.selectedValue||"E"==this.identificationTab.defGroupCombo.selectedValue||this.identificationTab.defGroupCombo.selectedValue,this.savedQuery=this.identificationTab.baseQuery.text,this.identificationTab.fillFacilityProperties()},t.prototype.getTooltip=function(e){for(var t=[],i=0;i<e.length;i++)t.push(e[i].content);return this.savedTooltip=t.toString(),this.savedTooltip},t.prototype.getDefaultRefColumn=function(e){e=e.substring(1,e.length-1);for(var t=0;t<e.length;t++)"}"===e[t]&&e[t+1]&&(e=e.substr(0,t+1)+"*"+e.substr(t+2));this.defaultOptions=e.split("*");for(var i=0;i<this.defaultOptions.length;i++){var s=this.defaultOptions[i].split(",")[0].split(":")[1],a=this.defaultOptions[i].split(",")[1].split(":")[1];a=a.substr(1,a.length-3),this.defaultValString=a;var n={value:Number(s),content_text:a};this.defaultVal.push(n)}},t.prototype.fillInstanceData=function(){if(this.optionIdTypes=this.jsonReader.getSelects().select.find(function(e){return"otherIdTypeCombo"==e.id}).option,"add"!=this.methodName)if(this.instancesTab.uniqExpLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.instancesTab.acctLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.instancesTab.valDateLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.instancesTab.signLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.instancesTab.mvtLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.instancesTab.matchLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.instancesTab.sweepLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.instancesTab.payLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.instancesTab.otherIdLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.instancesTab.otherIdTypeLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.generalTab.recordCheck.selected){if(this.defaultAccount=this.jsonReader.getSingletons().accountIdColumn,this.defaultSign=this.jsonReader.getSingletons().signColumn,this.defaultValueDate=this.jsonReader.getSingletons().valueDateColumn,this.defaultOtherId=this.jsonReader.getSingletons().otherIdColumn,this.defaultCustomTreeLevel1=this.jsonReader.getSingletons().customTreeLevel1,this.defaultCustomTreeLevel2=this.jsonReader.getSingletons().customTreeLevel2,this.defaultMvt=this.jsonReader.getSingletons().mvtColumn,this.defaultMatchId=this.jsonReader.getSingletons().matchColumn,this.defaultSweepId=this.jsonReader.getSingletons().sweepColumn,this.defaultPay=this.jsonReader.getSingletons().payColumn,this.defaultOtherIdType=this.jsonReader.getSingletons().otherIdTypeColumn,this.prepareTreeBreakdownProvider(),this.prepareAlertInstProvider(),this.columns)this.columns.option;if(this.txtColumns){var e=this.txtColumns.option;e&&!e.length&&(e=Array.isArray(e)?e:[e]),this.instancesTab.acctIdCombo.setComboData(e),this.instancesTab.acctIdCombo.dataProvider=e,this.instancesTab.signColCombo.setComboData(e),this.instancesTab.signColCombo.dataProvider=e,this.instancesTab.otherIdColCombo.setComboData(e),this.instancesTab.otherIdColCombo.dataProvider=e}if(this.nbrColumns){var t=this.nbrColumns.option;t&&!t.length&&(t=Array.isArray(t)?t:[t]),this.instancesTab.mvtColCombo.setComboData(t),this.instancesTab.mvtColCombo.dataProvider=t,this.instancesTab.matchColCombo.setComboData(t),this.instancesTab.matchColCombo.dataProvider=t,this.instancesTab.sweepColCombo.setComboData(t),this.instancesTab.sweepColCombo.dataProvider=t,this.instancesTab.payColCombo.setComboData(t),this.instancesTab.payColCombo.dataProvider=t}if(this.dateColumns){var i=this.dateColumns.option;i&&!i.length&&(i=Array.isArray(i)?i:[i]),this.instancesTab.valueDateCombo.setComboData(i),this.instancesTab.valueDateCombo.dataProvider=i}this.instancesTab.otherIdTypeCombo.setComboData(this.optionIdTypes),this.instancesTab.otherIdTypeCombo.dataProvider=this.optionIdTypes,this.instancesTab.uniqueExpression.text=o.t.decode64(this.jsonReader.getSingletons().uniqueExpression),this.defaultUniqueExp=o.t.decode64(this.jsonReader.getSingletons().uniqueExpression),this.instancesTab.acctIdCombo.selectedLabel=this.defaultAccount,this.instancesTab.signColCombo.selectedLabel=this.defaultSign,this.instancesTab.mvtColCombo.selectedLabel=this.defaultMvt,this.instancesTab.matchColCombo.selectedLabel=this.defaultMatchId,this.instancesTab.sweepColCombo.selectedLabel=this.defaultSweepId,this.instancesTab.payColCombo.selectedLabel=this.defaultPay,this.instancesTab.treeBreakDown1Combo.selectedLabel=this.defaultCustomTreeLevel1,this.instancesTab.treeBreakDown2Combo.selectedLabel=this.defaultCustomTreeLevel2,this.instancesTab.valueDateCombo.selectedLabel=this.defaultValueDate,this.instancesTab.otherIdColCombo.selectedLabel=this.defaultOtherId,this.instancesTab.otherIdTypeCombo.selectedLabel=this.jsonReader.getSingletons().otherIdTypeColumn,this.instancesTab.otherIdTypeDesc.text=this.instancesTab.otherIdTypeCombo.selectedValue,this.instancesTab.instExpTxt.text=this.jsonReader.getSingletons().instanceExpiryMins,this.instancesTab.raiseRadioGroup.selectedValue=this.jsonReader.getSingletons().reRaiseAfterExpiry,this.instancesTab.afterMinTxt.text=this.jsonReader.getSingletons().minsAfterExpiry,"A"==this.jsonReader.getSingletons().reRaiseAfterExpiry?this.instancesTab.afterMinTxt.enabled=!0:this.instancesTab.afterMinTxt.enabled=!1}else this.disableInstAndEvtsTab(!1);else this.disableInstAndEvtsTab(!1)},t.prototype.fillInstanceCombo=function(e){this.queryColumns=e;try{if(e){e&&e.select&&e.select[0]&&e.select[0].option;var t=e&&e.select&&e.select[1]&&e.select[1].option||[],i=e&&e.select&&e.select[2]&&e.select[2].option||[],s=e&&e.select&&e.select[3]&&e.select[3].option||[];i=Array.isArray(i)?i:[i],t=Array.isArray(t)?t:[t],s=Array.isArray(s)?s:[s];var a={type:"",value:"",selected:0,content:""};i.length&&""===i[0].content||i.unshift(a),t.length&&""===t[0].content||t.unshift(a),s.length&&""===s[0].content||s.unshift(a),this.instancesTab.acctIdCombo.setComboData(i),this.instancesTab.acctIdCombo.dataProvider=i,this.instancesTab.acctIdCombo.selectedLabel=this.savedAcctId,this.instancesTab.signColCombo.setComboData(i),this.instancesTab.signColCombo.dataProvider=i,this.instancesTab.signColCombo.selectedLabel=this.savedSign,this.instancesTab.mvtColCombo.setComboData(t),this.instancesTab.mvtColCombo.dataProvider=t,this.instancesTab.mvtColCombo.selectedLabel=this.savedMvtId,this.instancesTab.matchColCombo.setComboData(t),this.instancesTab.matchColCombo.dataProvider=t,this.instancesTab.matchColCombo.selectedLabel=this.savedMatchId,this.instancesTab.sweepColCombo.setComboData(t),this.instancesTab.sweepColCombo.dataProvider=t,this.instancesTab.sweepColCombo.selectedLabel=this.savedSwpId,this.instancesTab.payColCombo.setComboData(t),this.instancesTab.payColCombo.dataProvider=t,this.instancesTab.payColCombo.selectedLabel=this.savedPayId,this.instancesTab.valueDateCombo.setComboData(s),this.instancesTab.valueDateCombo.dataProvider=s,this.instancesTab.valueDateCombo.selectedLabel=this.savedValueDate,this.instancesTab.otherIdColCombo.setComboData(i),this.instancesTab.otherIdColCombo.dataProvider=i,this.instancesTab.otherIdColCombo.selectedLabel=this.savedOtherId,this.instancesTab.otherIdTypeCombo.setComboData(this.optionIdTypes),this.instancesTab.otherIdTypeCombo.dataProvider=this.optionIdTypes,this.instancesTab.otherIdTypeCombo.selectedLabel=this.savedOtherIdType,this.instancesTab.otherIdTypeDesc.text=this.savedOtherIdTypeDesc}}catch(n){console.log("error",n)}},t.prototype.fillGuiHighlightData=function(e){var t=this,i=this.generalTab.recordCheck.selected;"add"!=this.methodName&&(this.guiHighlightTab.critGuiHighCheck.selected="Y"==this.jsonReader.getSingletons().criticalGuiHighlight),this.guiHighlightTab.guiGrid.forceHeaderRefresh=!0,this.guiHighlightTab.guiGrid.CustomGrid(this.lastRecievedJSON.scenarioDetails.guiGrid.metadata),this.guiHighlightTab.guiGrid.gridData=this.lastRecievedJSON.scenarioDetails.guiGrid.rows,this.guiHighlightTab.guiGrid.rowColorFunction=function(e,s,a){return t.changeColorBackground(e,s,a,i)},this.guiHighlightTab.guiGrid.customTooltipFunction=function(e){return t.setTooltipMessage(e)},this.guiHighlightTab.guiGrid.enableDisableCells=function(e,i){return t.enableDisableRow(e,i)}},t.prototype.fillEventsData=function(){if(this.eventsTab.BtnLockImg.source=this.baseURL+o.x.call("eval","lockImage"),this.eventsTab.eventsGrid.CustomGrid(this.lastRecievedJSON.scenarioDetails.eventGrid.metadata),this.eventsTab.eventsGrid.refresh(),"add"!==this.methodName)if(this.generalTab.recordCheck.selected){this.gridComboVal=this.columns?this.columns.option:[],this.gridComboTxtVal=this.txtColumns?this.txtColumns.option:[],this.gridComboNbrVal=this.nbrColumns?this.nbrColumns.option:[],this.gridComboDateVal=this.dateColumns?this.dateColumns.option:[],this.eventsTab.eventsGrid.gridData={row:this.lastRecievedJSON.scenarioDetails.eventGrid.rows.row,size:this.lastRecievedJSON.scenarioDetails.eventGrid.rows.size},this.eventGridJSON=this.lastRecievedJSON.scenarioDetails.eventGrid.rows,this.eventsTab.triggeEventsRadioGroup.selectedValue=this.jsonReader.getSingletons().afterTrigEvent,this.eventsTab.resolutionQueryText.text=this.htmlEntities(o.t.decode64(this.jsonReader.getSingletons().resolutionQueryText));var e=this.jsonReader.getSingletons().pendingResolutionTimeLimit;if(-1!=e?(this.eventsTab.resolOverdueRadioGroup.selectedValue="A",this.eventsTab.minsText.text=e):(this.eventsTab.resolOverdueRadioGroup.selectedValue="N",this.eventsTab.minsText.text=""),this.columns){var t=this.columns.option,i=u.extend(!0,[],t);i.splice(0,1),i.unshift({type:"",value:-1,selected:0,content:'"INSTANCE_ID"'}),i.sort(function(e,t){return e.content.localeCompare(t.content)}),this.eventsTab.refColumnCombo.dataProvider=i}this.savedResolutionRefCols=this.jsonReader.getSelects().select.find(function(e){return"savedResolRefCols"==e.id}).option,this.savedResolutionRefCols?this.savedResolutionRefCols.length||(this.savedResolutionRefCols=[this.savedResolutionRefCols]):this.savedResolutionRefCols=[],this.savedResolutionRefCols.length>0&&(this.eventsTab.refColumnCombo.toolTip=this.getTooltip(this.savedResolutionRefCols)),this.eventsTab.refColumnCombo.defaultSelectedItems=this.savedResolutionRefCols,this.eventsTab.refColumnCombo.selects=this.savedResolutionRefCols,this.eventsTab.eventsGrid.gridData&&this.eventsTab.eventsGrid.gridData.length>0?this.eventsTab.triggeEventsRadioGroup.enabled=!0:this.eventsTab.triggeEventsRadioGroup.enabled=!1}else this.disableInstAndEvtsTab(!1);else this.disableInstAndEvtsTab(!1),this.eventsTab.triggeEventsRadioGroup.enabled=!1;"P"==this.eventsTab.triggeEventsRadioGroup.selectedValue?(this.eventsTab.resolutionQueryText.enabled=!0,this.eventsTab.refColumnCombo.isDropdownDisabled=!1,this.eventsTab.minsText.enabled=!0,this.eventsTab.resolOverdueRadioGroup.enabled=!0,"A"==this.eventsTab.resolOverdueRadioGroup.selectedValue?this.eventsTab.minsText.enabled=!0:this.eventsTab.minsText.enabled=!1):(this.eventsTab.resolutionQueryText.enabled=!1,this.eventsTab.refColumnCombo.isDropdownDisabled=!0,this.eventsTab.minsText.enabled=!1,this.eventsTab.resolOverdueRadioGroup.enabled=!1)},t.prototype.inputDataFault=function(){this.swtalert.error(o.Wb.getPredictMessage("alert.generic_exception"))},t.prototype.validateFields=function(){var e=o.Wb.getPredictMessage("alert.pleaseFillAllMandatoryFields",null),t=o.Wb.getPredictMessage("scenario.sytemScenarioAlert",null),i=o.Wb.getPredictMessage("scenario.startEndTime.alert",null);return""==this.scenarioIdTxt.text||/^[^~]*$/.test(this.scenarioIdTxt.text)?""==this.scenarioIdTxt.text||""==this.titleTxt.text||""==this.instancesTab.uniqueExpression.text&&this.generalTab.recordCheck.selected?(this.swtalert.warning(e),!1):this.systemCheck.selected||0!=this.scenarioIdTxt.text.toLowerCase().indexOf("sys_")?""==this.generalTab.startTxt.text&&""!=this.generalTab.endTxt.text||""!=this.generalTab.startTxt.text&&""==this.generalTab.endTxt.text?(this.swtalert.warning(i),!1):"A"!=this.instancesTab.raiseRadioGroup.selectedValue||""!=this.instancesTab.afterMinTxt.text||void this.swtalert.warning("Please fill number of minutes after expiry"):(this.swtalert.warning(t),!1):(this.swtalert.show("Please enter a valid string without '~'"),!1)},t.prototype.guiHighlightChanges=function(){for(var e={},t=0;t<this.guiHighlightTab.guiGrid.gridData.length;t++)"Y"==this.guiHighlightTab.guiGrid.gridData[t].guiSelect&&(e={OPERATION:"I",TABLE:"GuiAlertMapping",SCENARIO_ID:this.scenarioIdTxt.text.trim(),GUI_FACILITY_ID:this.guiHighlightTab.guiGrid.gridData[t].guiIdDescripion.split("-")[0].trim(),PARAMETERS_XML:this.guiHighlightTab.guiGrid.gridData[t].guiRequiredParams.toString()},this.operationsList.push(e))},t.prototype.doRefreshGuiHighlight=function(e){var t=this,i=this.instancesTab.acctIdCombo.selectedLabel,s=this.generalTab.recordCheck.selected;""!=i&&(this.guiHighlightTab.guiGrid.rowColorFunction=function(e,i,a){return t.changeColorBackground(e,i,a,s)});this.instancesTab.valueDateCombo.selectedLabel,this.instancesTab.signColCombo.selectedLabel,this.instancesTab.mvtColCombo.selectedLabel,this.instancesTab.matchColCombo.selectedLabel,this.instancesTab.sweepColCombo.selectedLabel,this.instancesTab.payColCombo.selectedLabel,this.instancesTab.valueDateCombo.selectedLabel;this.guiHighlightTab.guiGrid.rowColorFunction=function(e,i,a){return t.changeColorBackground(e,i,a,s)},this.guiHighlightTab.guiGrid.customTooltipFunction=function(e){return t.setTooltipMessage(e)}},t.prototype.changeColorBackground=function(e,t,i,s){var a;try{var n,o="",l=void 0;if(n=e.slickgrid_rowcontent.guiScenInstance?e.slickgrid_rowcontent.guiScenInstance.content:"",""==(l=e.slickgrid_rowcontent.guiRequiredParams.content?e.slickgrid_rowcontent.guiRequiredParams.content:""))o="Y"==n?s?"N":"D":"N";else{var r=l.split(",");o="Y"==n?s&&this.checkRequiredColumns(r)?"N":"D":this.checkRequiredColumns(r)?"N":"D"}e.colorflag=o,e.slickgrid_rowcontent.colorflag.content=o,"D"==o?(a="#AAAAAA",e.guiSelect="N",e.slickgrid_rowcontent.guiSelect.content="N"):"N"==o&&(a="")}catch(c){console.log("error changeColorBackground ",c)}return a},t.prototype.checkRequiredColumns=function(e){for(var t=!0,i=0;i<e.length;i++)0==this.getComboLabelStatus(e[i])&&(t=!1);return t},t.prototype.getComboLabelStatus=function(e){var t=!1;switch(e){case"HOST_ID":t=this.validateValue(this.identificationTab.hostColCombo);break;case"ENTITY_ID":t=this.validateValue(this.identificationTab.entityColCombo);break;case"CURRENCY_CODE":t=this.validateValue(this.identificationTab.ccyColCombo);break;case"AMOUNT":t=this.validateValue(this.identificationTab.amountColCombo);break;case"ACCOUNT_ID":t=this.validateValue(this.instancesTab.acctIdCombo);break;case"SIGN":t=this.validateValue(this.instancesTab.signColCombo);break;case"MATCH_ID":t=this.validateValue(this.instancesTab.matchColCombo);break;case"SWEEP_ID":t=this.validateValue(this.instancesTab.sweepColCombo);break;case"PAYMENT_ID":t=this.validateValue(this.instancesTab.payColCombo);break;case"VALUE_DATE":t=this.validateValue(this.instancesTab.valueDateCombo);break;case"MOVEMENT_ID":t=this.validateValue(this.instancesTab.mvtColCombo);break;case"OTHER_ID":t=this.validateValue(this.instancesTab.otherIdColCombo)}return t},t.prototype.validateValue=function(e){return!!e.selectedLabel},t.prototype.refreshGridGuiHighlight=function(){var e=this,t=this.generalTab.recordCheck.selected;this.guiHighlightTab.guiGrid.customTooltipFunction=function(t){return e.setTooltipMessage(t)},this.guiHighlightTab.guiGrid.rowColorFunction=function(i,s,a){return e.changeColorBackground(i,s,a,t)}},t.prototype.setTooltipMessage=function(e){var t="Required$#$parameters:$#$";try{10,t=e.slickgrid_rowcontent.guiRequiredParams?t+e.slickgrid_rowcontent.guiRequiredParams.content:""}catch(i){}return t},t.prototype.eventsChanges=function(){for(var e={},t=this.eventsTab.eventsGrid.gridData,i=0;i<t.length;i++)e={OPERATION:"I",TABLE:"EventMapping",SCENARIO_ID:this.scenarioIdTxt.text.trim(),ORDINAL:t[i].eventSeq,MAP_KEY:t[i].mapKey?t[i].mapKey.toString():"",EVENT_FACILITY_ID:t[i].eventId,EVENT_FACILITY_DESC:t[i].eventDescription,PARAMETERS_XML:"XML"!=t[i].eventParams?t[i].eventParams.toString():t[i].xmlColumn?t[i].xmlColumn.toString():t[i].slickgrid_rowcontent.eventParams.code.toString(),REPEAT_ON_RERAISE:t[i].eventRepeat,EXECUTE_WHEN:t[i].eventExecuteWhen},this.operationsList.push(e)},t.prototype.saveHandler=function(){var e=this;if(this.validateFields()){this.guiHighlightChanges(),this.eventsChanges(),this.generalTab.gridParamsToXml();var t="add"==this.methodName?"save":"update";this.requestParams=[],this.actionPath="scenMaintenance.do?",this.actionMethod="method=save",this.sendData.cbStart=this.startOfComms.bind(this),this.sendData.cbStop=this.endOfComms.bind(this),this.sendData.cbResult=function(t){e.saveResult(t)},this.sendData.cbFault=this.inputDataFault.bind(this),this.sendData.encodeURL=!1,this.requestParams.saveOrUpdate=t,this.requestParams.selectedScenarioID=this.scenarioIdTxt.text.trim(),this.requestParams.selectedSystemFlag=this.systemCheck.selected?"Y":"",this.requestParams.fromAdvanced="true",this.requestParams.table="None"!=this.identificationTab.refTableTxt.text?this.identificationTab.refTableTxt.text:"",this.requestParams.scenTitle=this.titleTxt.text,this.requestParams.scenDescription=this.descriptionTxt.text,this.requestParams.scenSummaryGrouping="N"!=this.identificationTab.defGroupCombo.selectedValue?this.identificationTab.defGroupCombo.selectedValue:"",this.requestParams.scenBaseQuery=o.Z.encode64(this.identificationTab.baseQuery.text),this.requestParams.scenHostColumn=this.identificationTab.hostColCombo.selectedLabel,this.requestParams.scenEntityColumn=this.identificationTab.entityColCombo.selectedLabel,this.requestParams.scenCurrencyColumn=this.identificationTab.ccyColCombo.selectedLabel,this.requestParams.scenAmountColumn=this.identificationTab.amountColCombo.selectedLabel,this.requestParams.scenGenericDisplay=this.identificationTab.genericCheck.selected?"Y":"",this.requestParams.scenFacilityID="None"!=this.identificationTab.facilityCombo.selectedLabel?this.identificationTab.facilityCombo.selectedLabel:"",this.requestParams.scenRefcolumns=JSON.stringify(this.identificationTab.refColumnCombo.selects),this.requestParams.scenAlertInstcolumns=JSON.stringify(this.instancesTab.alertInstanceColumnCombo.selects),this.requestParams.scenParamValues=this.identificationTab.paramValueTxt.text,this.requestParams.selectedFacilityId="None"!=this.identificationTab.facilityCombo.selectedLabel?this.identificationTab.facilityCombo.selectedLabel:"",this.requestParams["scenarioMaintenance.id.scenarioId"]=this.scenarioIdTxt.text.trim(),this.requestParams["scenarioMaintenance.systemFlag"]=this.systemCheck.selected?"Y":"",this.requestParams["scenarioMaintenance.activeFlag"]=this.activeCheck.selected?"Y":"",this.requestParams["scenarioMaintenance.title"]=this.titleTxt.text,this.requestParams["scenarioMaintenance.description"]=this.descriptionTxt.text,this.requestParams["scenarioMaintenance.recordScenarioInstance"]=this.generalTab.recordCheck.selected?"Y":"N",this.requestParams["scenarioCategory.id.categoryid"]=this.generalTab.categoryCombo.selectedLabel,this.requestParams["scenarioMaintenance.displayOrder"]=this.generalTab.orderTxt.text,this.requestParams["scenarioMaintenance.runEvery"]=this.generalTab.runTxt.text,this.requestParams["scenarioMaintenance.startTime"]=this.generalTab.startTxt.text,this.requestParams["scenarioMaintenance.endTime"]=this.generalTab.endTxt.text,this.requestParams["scenarioMaintenance.emailWhenDiff"]=this.generalTab.emailTxt.text,this.requestParams["scenarioMaintenance.instanceUniqueExpression"]=o.t.encode64(this.instancesTab.uniqueExpression.text),this.requestParams["scenarioMaintenance.accountColumn"]=this.instancesTab.acctIdCombo.selectedLabel,this.requestParams["scenarioMaintenance.signCol"]=this.instancesTab.signColCombo.selectedLabel,this.requestParams["scenarioMaintenance.mvtColumn"]=this.instancesTab.mvtColCombo.selectedLabel,this.requestParams["scenarioMaintenance.matchColumn"]=this.instancesTab.matchColCombo.selectedLabel,this.requestParams["scenarioMaintenance.sweepColumn"]=this.instancesTab.sweepColCombo.selectedLabel,this.requestParams["scenarioMaintenance.paymentColumn"]=this.instancesTab.payColCombo.selectedLabel,this.requestParams["scenarioMaintenance.customTreeLevel1"]=this.instancesTab.treeBreakDown1Combo.selectedLabel,this.requestParams["scenarioMaintenance.customTreeLevel2"]=this.instancesTab.treeBreakDown2Combo.selectedLabel,this.requestParams["scenarioMaintenance.valueDateColumn"]=this.instancesTab.valueDateCombo.selectedLabel,this.requestParams["scenarioMaintenance.otherIdColumn"]=this.instancesTab.otherIdColCombo.selectedLabel,this.requestParams["scenarioMaintenance.otherIdTypeColumn"]=this.instancesTab.otherIdTypeCombo.selectedLabel,this.requestParams["scenarioMaintenance.instanceExpiryMins"]=this.instancesTab.instExpTxt.text,this.requestParams["scenarioMaintenance.allowReraiseAfterExpiry"]=this.instancesTab.raiseRadioGroup.selectedValue,"A"==this.instancesTab.raiseRadioGroup.selectedValue&&(this.requestParams["scenarioMaintenance.reraiseIntervalMins"]=this.instancesTab.afterMinTxt.text),this.requestParams["scenarioMaintenance.afterTrigEvent"]=this.eventsTab.triggeEventsRadioGroup.selectedValue,this.requestParams["scenarioMaintenance.scenarioResolutionQueryText"]=o.t.encode64(this.eventsTab.resolutionQueryText.text),this.requestParams["scenarioMaintenance.pendingResolutionTimeLimit"]="A"==this.eventsTab.resolOverdueRadioGroup.selectedValue?this.eventsTab.minsText.text:-1,this.requestParams.resolutionRefcolumns=JSON.stringify(this.eventsTab.refColumnCombo.selects),this.requestParams.operationsList=JSON.stringify(this.operationsList),this.requestParams.criticalGuiHighlight=this.guiHighlightTab.critGuiHighCheck.selected?"Y":"N",this.requestParams.requiredParamsXml=this.generalTab.xmlParams?this.generalTab.xmlParams:"",this.requestParams.genBasis=this.generalTab.selectedType,this.requestParams.scheduleParams=this.scheduleParams?JSON.stringify(this.scheduleParams):"",this.requestParams.apiRequiredCols=this.generalTab.apiRequiredCols&&"A"==this.generalTab.typeOptions.selectedValue?JSON.stringify(this.generalTab.apiRequiredCols):"",this.sendData.url=this.baseURL+this.actionPath+this.actionMethod,this.sendData.send(this.requestParams)}},t.prototype.saveResult=function(e){var t=o.Wb.getPredictMessage("errors.DataIntegrityViolationExceptioninAdd",null);this.sendData.isBusy()?this.sendData.cbStop():(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),this.jsonReader.getRequestReplyMessage()&&-1!=this.jsonReader.getRequestReplyMessage().indexOf("DataIntegrityViolation")?this.swtalert.error(t,null,o.c.OK,null,function(){}):this.jsonReader.getRequestReplyStatus()?o.x.call("refreshParent"):this.inputDataFault())},t.prototype.cancelHandler=function(){o.x.call("close")},t.prototype.updateTreeProvider=function(e,t){["acctId","valueDate","signCol","otherIdCol","entityCol","ccyCol"].includes(t)&&!this.provider.some(function(t){return t.content===e})&&(this.provider.push({type:"",value:this.getColumnIndex(e),selected:0,content:e}),this.provider.sort(function(e,t){return e.content.localeCompare(t.content)}),this.instancesTab.treeBreakDown1Combo.setComboData(this.provider),this.instancesTab.treeBreakDown1Combo.dataProvider=this.provider,this.instancesTab.treeBreakDown2Combo.setComboData(this.provider),this.instancesTab.treeBreakDown2Combo.dataProvider=this.provider,this.instancesTab.treeBreakDown1Combo.selectedLabel=this.defaultCustomTreeLevel1?this.defaultCustomTreeLevel1:this.instancesTab.selectedTreeBreakdown1,this.instancesTab.treeBreakDown2Combo.selectedLabel=this.defaultCustomTreeLevel2?this.defaultCustomTreeLevel2:this.instancesTab.selectedTreeBreakdown2),this.checkSummaryGrouping()},t.prototype.checkSummaryGrouping=function(){"N"!=this.identificationTab.defGroupCombo.selectedValue?(this.provider=this.provider.filter(function(e){return'"ENTITY_ID"'!=e.content}),this.provider=this.provider.filter(function(e){return'"CURRENCY_CODE"'!=e.content})):(!this.provider.some(function(e){return'"ENTITY_ID"'===e.content})&&this.identificationTab.entityColCombo.selectedLabel&&this.provider.push({type:"",value:4,selected:0,content:'"ENTITY_ID"'}),!this.provider.some(function(e){return'"CURRENCY_CODE"'===e.content})&&this.identificationTab.ccyColCombo.selectedLabel&&this.provider.push({type:"",value:3,selected:0,content:'"CURRENCY_CODE"'})),this.provider.sort(function(e,t){return e.content.localeCompare(t.content)}),this.instancesTab.treeBreakDown1Combo.setComboData(this.provider),this.instancesTab.treeBreakDown1Combo.dataProvider=this.provider,this.instancesTab.treeBreakDown2Combo.setComboData(this.provider),this.instancesTab.treeBreakDown2Combo.dataProvider=this.provider,this.instancesTab.treeBreakDown1Combo.selectedLabel=this.defaultCustomTreeLevel1?this.defaultCustomTreeLevel1:this.instancesTab.selectedTreeBreakdown1,this.instancesTab.treeBreakDown2Combo.selectedLabel=this.defaultCustomTreeLevel2?this.defaultCustomTreeLevel2:this.instancesTab.selectedTreeBreakdown2},t.prototype.updateAlertInstProvider=function(e){this.instancesTab.alertInstanceColumnCombo.isDropdownDisabled=!!this.systemCheck.selected,this.alertInstProvider.some(function(t){return t.content===e})||(this.alertInstProvider=this.alertInstProvider.concat({type:"",value:this.getColumnIndex(e),selected:0,content:e}),this.alertInstProvider.sort(function(e,t){return e.content.localeCompare(t.content)}),this.instancesTab.alertInstanceColumnCombo.dataProvider=this.alertInstProvider,this.instancesTab.alertInstanceColumnCombo.selects=[],this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems=[])},t.prototype.getColumnIndex=function(e){var t=null;switch(e){case'"HOST_ID"':t=11;break;case'"ENTITY_ID"':t=4;break;case'"CURRENCY_CODE"':t=3;break;case'"ACCOUNT_ID"':t=0;break;case'"AMOUNT"':t=10;break;case'"SIGN"':t=1;break;case'"MATCH_ID"':t=7;break;case'"SWEEP_ID"':t=8;break;case'"PAYMENT_ID"':t=9;break;case'"VALUE_DATE"':t=2;break;case'"MOVEMENT_ID"':t=6;break;default:t=5}return t},t.prototype.deleteColFromTreeProvider=function(e,t){this.provider=this.provider.filter(function(t){return t.content!=e}),this.instancesTab.treeBreakDown1Combo.setComboData(this.provider),this.instancesTab.treeBreakDown1Combo.dataProvider=this.provider,this.instancesTab.treeBreakDown2Combo.setComboData(this.provider),this.instancesTab.treeBreakDown2Combo.dataProvider=this.provider,this.instancesTab.treeBreakDown1Combo.selectedLabel=this.defaultCustomTreeLevel1?this.defaultCustomTreeLevel1:this.instancesTab.selectedTreeBreakdown1,this.instancesTab.treeBreakDown2Combo.selectedLabel=this.defaultCustomTreeLevel2?this.defaultCustomTreeLevel2:this.instancesTab.selectedTreeBreakdown2},t.prototype.deleteColFromAlertInstProvider=function(e,t){"otherIdCol"!=t&&this.alertInstProvider.some(function(t){return t.content===e}),this.alertInstProvider=this.alertInstProvider.filter(function(t){return t.content!=e}),this.instancesTab.alertInstanceColumnCombo.dataProvider=this.alertInstProvider,this.instancesTab.alertInstanceColumnCombo.selects=[],this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems=[]},t.prototype.prepareTreeBreakdownProvider=function(){var e=this;this.defaultAccount&&this.savedTreeProvider.push({type:"",value:0,selected:0,content:'"ACCOUNT_ID"'}),this.defaultSign&&this.savedTreeProvider.push({type:"",value:1,selected:0,content:'"SIGN"'}),this.defaultValueDate&&this.savedTreeProvider.push({type:"",value:2,selected:0,content:'"VALUE_DATE"'}),this.defaultCcy&&"N"==this.identificationTab.defGroupCombo.selectedValue&&this.savedTreeProvider.push({type:"",value:3,selected:0,content:'"CURRENCY_CODE"'}),this.defaultEntity&&"N"==this.identificationTab.defGroupCombo.selectedValue&&this.savedTreeProvider.push({type:"",value:4,selected:0,content:'"ENTITY_ID"'}),this.jsonReader.getSingletons().otherIdColumn&&!this.savedTreeProvider.some(function(t){return t.content===e.defaultOtherId})&&this.savedTreeProvider.push({type:"",value:5,selected:0,content:'"OTHER_ID"'}),this.savedTreeProvider.sort(function(e,t){return e.content.localeCompare(t.content)}),this.instancesTab.treeBreakDown1Combo.setComboData(this.savedTreeProvider),this.instancesTab.treeBreakDown1Combo.dataProvider=this.savedTreeProvider,this.instancesTab.treeBreakDown2Combo.setComboData(this.savedTreeProvider),this.instancesTab.treeBreakDown2Combo.dataProvider=this.savedTreeProvider,this.provider=this.savedTreeProvider},t.prototype.prepareAlertInstProvider=function(){var e=this;this.instancesTab.alertInstanceColumnCombo.isDropdownDisabled=!!this.systemCheck.selected,this.defaultAccount&&this.savedAlertInstProvider.push({type:"",value:0,selected:0,content:'"ACCOUNT_ID"'}),this.defaultAmount&&this.savedAlertInstProvider.push({type:"",value:10,selected:0,content:'"AMOUNT"'}),this.defaultCcy&&this.savedAlertInstProvider.push({type:"",value:3,selected:0,content:'"CURRENCY_CODE"'}),this.defaultEntity&&this.savedAlertInstProvider.push({type:"",value:4,selected:0,content:'"ENTITY_ID"'}),this.defaultHost&&this.savedAlertInstProvider.push({type:"",value:11,selected:0,content:'"HOST_ID"'}),this.defaultMatchId&&this.savedAlertInstProvider.push({type:"",value:7,selected:0,content:'"MATCH_ID"'}),this.defaultMvt&&this.savedAlertInstProvider.push({type:"",value:6,selected:0,content:'"MOVEMENT_ID"'}),this.jsonReader.getSingletons().otherIdColumn&&!this.savedAlertInstProvider.some(function(t){return t.content===e.defaultOtherId})&&this.savedAlertInstProvider.push({type:"",value:5,selected:0,content:'"OTHER_ID"'}),this.defaultPay&&this.savedAlertInstProvider.push({type:"",value:9,selected:0,content:'"PAYMENT_ID"'}),this.defaultSign&&this.savedAlertInstProvider.push({type:"",value:1,selected:0,content:'"SIGN"'}),this.defaultSweepId&&this.savedAlertInstProvider.push({type:"",value:8,selected:0,content:'"SWEEP_ID"'}),this.defaultValueDate&&this.savedAlertInstProvider.push({type:"",value:2,selected:0,content:'"VALUE_DATE"'}),this.instancesTab.alertInstanceColumnCombo.dataProvider=this.savedAlertInstProvider,this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems=this.savedAlertInstCols,this.alertInstProvider=this.savedAlertInstProvider},t.prototype.fillEventRefColsCombo=function(e){if(this.queryColumns=e,e){var t=e.select[0].option,i=u.extend(!0,[],t);i.splice(0,1),i.unshift({type:"",value:-1,selected:0,content:'"INSTANCE_ID"'}),i.sort(function(e,t){return e.content.localeCompare(t.content)}),this.eventsTab.refColumnCombo.dataProvider=i,this.eventsTab.refColumnCombo.defaultSelectedItems=this.savedEventRefCols}},t.prototype.disableInstAndEvtsTab=function(e){this.instancesTab.uniqueExpression.text="",this.instancesTab.acctIdCombo.selectedLabel="",this.instancesTab.valueDateCombo.selectedLabel="",this.instancesTab.signColCombo.selectedLabel="",this.instancesTab.mvtColCombo.selectedLabel="",this.instancesTab.matchColCombo.selectedLabel="",this.instancesTab.sweepColCombo.selectedLabel="",this.instancesTab.payColCombo.selectedLabel="",this.instancesTab.otherIdColCombo.selectedLabel="",this.instancesTab.otherIdTypeCombo.selectedLabel="",this.instancesTab.treeBreakDown1Combo.selectedLabel="",this.instancesTab.treeBreakDown2Combo.selectedLabel="",this.instancesTab.instExpTxt.text="60",this.instancesTab.radioNo.selected=!0,this.instancesTab.radioAfter.selected=!1,this.instancesTab.afterMinTxt.text="0",this.instancesTab.afterMinTxt.enabled=!1,this.instancesTab.alertInstanceColumnCombo.selects=[],this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems=[],this.eventsTab.resolutionQueryText.text="",this.eventsTab.refColumnCombo.selects=[],this.eventsTab.refColumnCombo.defaultSelectedItems=[],e?(this.instances.enabled=!0,this.events.enabled=!0):(this.instances.enabled=!1,this.events.enabled=!1)},t.prototype.getIsScheduledSelected=function(){return this.generalTab.scheduled.selected},t.prototype.clearTypeDesc=function(){this.instancesTab.otherIdTypeDesc.text=""},t.prototype.saveComboValues=function(){this.savedHostId=this.identificationTab.hostColCombo.selectedLabel,this.savedEntityId=this.identificationTab.entityColCombo.selectedLabel,this.savedCcyId=this.identificationTab.ccyColCombo.selectedLabel,this.savedAmount=this.identificationTab.amountColCombo.selectedLabel,this.savedIdentRefCols=this.identificationTab.refColumnCombo.defaultSelectedItems,this.savedAcctId=this.instancesTab.acctIdCombo.selectedLabel,this.savedValueDate=this.instancesTab.valueDateCombo.selectedLabel,this.savedSign=this.instancesTab.signColCombo.selectedLabel,this.savedMvtId=this.instancesTab.mvtColCombo.selectedLabel,this.savedMatchId=this.instancesTab.matchColCombo.selectedLabel,this.savedSwpId=this.instancesTab.sweepColCombo.selectedLabel,this.savedPayId=this.instancesTab.payColCombo.selectedLabel,this.savedOtherId=this.instancesTab.otherIdColCombo.selectedLabel,this.savedOtherIdType=this.instancesTab.otherIdTypeCombo.selectedLabel,this.savedTreeBreakDown1=this.instancesTab.treeBreakDown1Combo.selectedLabel,this.savedTreeBreakDown2=this.instancesTab.treeBreakDown2Combo.selectedLabel,this.savedAlertInstCols=this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems,this.savedEventRefCols=this.eventsTab.refColumnCombo.defaultSelectedItems,this.savedOtherIdTypeDesc=this.instancesTab.otherIdTypeDesc.text},t.prototype.checkIfInstExist=function(){var e=this;this.requestParams=[],this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.instExistResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="scenMaintenance.do?",this.actionMethod="method=checkIfInstExist",this.requestParams.scenarioId=this.scenarioIdTxt.text.trim(),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},t.prototype.instExistResult=function(e){this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON1=e,this.jsonReader1.setInputJSON(this.lastRecievedJSON1),this.jsonReader1.getRequestReplyStatus()?this.lastRecievedJSON1!=this.prevRecievedJSON1&&(this.jsonReader1.isDataBuilding()||("Y"==this.jsonReader1.getSingletons().isInstExist?(this.eventsTab.unlockFlag=!1,this.swtalert.warning(o.Wb.getPredictMessage("scenario.unresolvedInstancesAlert",null)),this.showLockIcon()):this.eventsTab.unlockFlag=!0,this.prevRecievedJSON1=this.lastRecievedJSON1)):this.lastRecievedJSON1.hasOwnProperty("request_reply")&&this.swtalert.error(this.jsonReader1.getRequestReplyMessage()+"\n"+this.jsonReader1.getRequestReplyLocation(),"Error"))},t.prototype.htmlEntities=function(e){try{return String(e).replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/ /g,"&nbsp;")}catch(t){console.log("error",t,e)}},t.prototype.confirmListener=function(e){e.detail==o.c.YES&&this.saveHandler()},t.prototype.checkIfBaseQuerySecChanged=function(){return this.identificationTab.baseQuery.text!=this.defaultQuery||this.identificationTab.hostColCombo.selectedLabel!=this.defaultHost||this.identificationTab.entityColCombo.selectedLabel!=this.defaultEntity||this.identificationTab.ccyColCombo.selectedLabel!=this.defaultCcy||this.identificationTab.amountColCombo.selectedLabel!=this.defaultAmount},t.prototype.checkIfInstTabFieldsChanged=function(){return this.instancesTab.uniqueExpression.text!=this.defaultUniqueExp||this.instancesTab.acctIdCombo.selectedLabel!=this.defaultAccount||this.instancesTab.valueDateCombo.selectedLabel!=this.defaultValueDate||this.instancesTab.signColCombo.selectedLabel!=this.defaultSign||this.instancesTab.mvtColCombo.selectedLabel!=this.defaultMvt||this.instancesTab.matchColCombo.selectedLabel!=this.defaultMatchId||this.instancesTab.sweepColCombo.selectedLabel!=this.defaultSweepId||this.instancesTab.payColCombo.selectedLabel!=this.defaultPay||this.instancesTab.otherIdColCombo.selectedLabel!=this.defaultOtherId||this.instancesTab.otherIdTypeCombo.selectedLabel!=this.defaultOtherIdType},t.prototype.showLockIcon=function(){this.identificationTab.BaseQueryLockImg.visible=!0,this.identificationTab.hostLockImg.visible=!0,this.identificationTab.entityLockImg.visible=!0,this.identificationTab.ccyLockImg.visible=!0,this.identificationTab.amountLockImg.visible=!0,this.identificationTab.baseQuery.editable=!1,this.identificationTab.hostColCombo.enabled=!1,this.identificationTab.entityColCombo.enabled=!1,this.identificationTab.ccyColCombo.enabled=!1,this.identificationTab.amountColCombo.enabled=!1,this.instancesTab.uniqExpLockImg.visible=!0,this.instancesTab.acctLockImg.visible=!0,this.instancesTab.valDateLockImg.visible=!0,this.instancesTab.signLockImg.visible=!0,this.instancesTab.mvtLockImg.visible=!0,this.instancesTab.matchLockImg.visible=!0,this.instancesTab.sweepLockImg.visible=!0,this.instancesTab.payLockImg.visible=!0,this.instancesTab.otherIdLockImg.visible=!0,this.instancesTab.otherIdTypeLockImg.visible=!0,this.instancesTab.uniqueExpression.editable=!1,this.instancesTab.acctIdCombo.enabled=!1,this.instancesTab.valueDateCombo.enabled=!1,this.instancesTab.signColCombo.enabled=!1,this.instancesTab.mvtColCombo.enabled=!1,this.instancesTab.matchColCombo.enabled=!1,this.instancesTab.sweepColCombo.enabled=!1,this.instancesTab.payColCombo.enabled=!1,this.instancesTab.otherIdColCombo.enabled=!1,this.instancesTab.otherIdTypeCombo.enabled=!1,this.eventsTab.BtnLockImg.visible=!0,this.eventsTab.BtnLockImg.includeInLayout=!0,this.eventsTab.changeButton.enabled=!1,this.eventsTab.addButton.enabled=!1},t}(o.yb),T=[{path:"",component:m}],g=(n.l.forChild(T),function(){return function(){}}()),C=i("pMnS"),p=i("RChO"),v=i("t6HQ"),f=i("WFGK"),I=i("5FqG"),y=i("Ip0R"),R=i("gIcY"),D=i("t/Na"),x=i("sE5F"),L=i("OzfB"),P=i("T7CS"),S=i("S7LP"),w=i("6aHO"),k=i("WzUx"),A=i("A7o+"),E=i("zCE2"),M=i("Jg5P"),N=i("3R0m"),G=i("hhbb"),B=i("5rxC"),q=i("Fzqc"),O=i("21Lb"),V=i("hUWP"),_=i("3pJQ"),j=i("V9q+"),H=i("VDKW"),J=i("kXfT"),F=i("BGbe");i.d(t,"ScenarioDetailModuleNgFactory",function(){return U}),i.d(t,"RenderType_ScenarioDetail",function(){return Y}),i.d(t,"View_ScenarioDetail_0",function(){return z}),i.d(t,"View_ScenarioDetail_Host_0",function(){return Q}),i.d(t,"ScenarioDetailNgFactory",function(){return Z});var U=s.Gb(g,[],function(e){return s.Qb([s.Rb(512,s.n,s.vb,[[8,[C.a,p.a,v.a,f.a,I.Cb,I.Pb,I.r,I.rc,I.s,I.Ab,I.Bb,I.Db,I.qd,I.Hb,I.k,I.Ib,I.Nb,I.Ub,I.yb,I.Jb,I.v,I.A,I.e,I.c,I.g,I.d,I.Kb,I.f,I.ec,I.Wb,I.bc,I.ac,I.sc,I.fc,I.lc,I.jc,I.Eb,I.Fb,I.mc,I.Lb,I.nc,I.Mb,I.dc,I.Rb,I.b,I.ic,I.Yb,I.Sb,I.kc,I.y,I.Qb,I.cc,I.hc,I.pc,I.oc,I.xb,I.p,I.q,I.o,I.h,I.j,I.w,I.Zb,I.i,I.m,I.Vb,I.Ob,I.Gb,I.Xb,I.t,I.tc,I.zb,I.n,I.qc,I.a,I.z,I.rd,I.sd,I.x,I.td,I.gc,I.l,I.u,I.ud,I.Tb,Z]],[3,s.n],s.J]),s.Rb(4608,y.m,y.l,[s.F,[2,y.u]]),s.Rb(4608,R.c,R.c,[]),s.Rb(4608,R.p,R.p,[]),s.Rb(4608,D.j,D.p,[y.c,s.O,D.n]),s.Rb(4608,D.q,D.q,[D.j,D.o]),s.Rb(5120,D.a,function(e){return[e,new o.tb]},[D.q]),s.Rb(4608,D.m,D.m,[]),s.Rb(6144,D.k,null,[D.m]),s.Rb(4608,D.i,D.i,[D.k]),s.Rb(6144,D.b,null,[D.i]),s.Rb(4608,D.f,D.l,[D.b,s.B]),s.Rb(4608,D.c,D.c,[D.f]),s.Rb(4608,x.c,x.c,[]),s.Rb(4608,x.g,x.b,[]),s.Rb(5120,x.i,x.j,[]),s.Rb(4608,x.h,x.h,[x.c,x.g,x.i]),s.Rb(4608,x.f,x.a,[]),s.Rb(5120,x.d,x.k,[x.h,x.f]),s.Rb(5120,s.b,function(e,t){return[L.j(e,t)]},[y.c,s.O]),s.Rb(4608,P.a,P.a,[]),s.Rb(4608,S.a,S.a,[]),s.Rb(4608,w.a,w.a,[s.n,s.L,s.B,S.a,s.g]),s.Rb(4608,k.c,k.c,[s.n,s.g,s.B]),s.Rb(4608,k.e,k.e,[k.c]),s.Rb(4608,A.l,A.l,[]),s.Rb(4608,A.h,A.g,[]),s.Rb(4608,A.c,A.f,[]),s.Rb(4608,A.j,A.d,[]),s.Rb(4608,A.b,A.a,[]),s.Rb(4608,A.k,A.k,[A.l,A.h,A.c,A.j,A.b,A.m,A.n]),s.Rb(4608,k.i,k.i,[[2,A.k]]),s.Rb(4608,k.r,k.r,[k.L,[2,A.k],k.i]),s.Rb(4608,k.t,k.t,[]),s.Rb(4608,k.w,k.w,[]),s.Rb(1073742336,n.l,n.l,[[2,n.r],[2,n.k]]),s.Rb(1073742336,y.b,y.b,[]),s.Rb(1073742336,R.n,R.n,[]),s.Rb(1073742336,R.l,R.l,[]),s.Rb(1073742336,E.a,E.a,[]),s.Rb(1073742336,M.a,M.a,[]),s.Rb(1073742336,R.e,R.e,[]),s.Rb(1073742336,N.a,N.a,[]),s.Rb(1073742336,A.i,A.i,[]),s.Rb(1073742336,k.b,k.b,[]),s.Rb(1073742336,D.e,D.e,[]),s.Rb(1073742336,D.d,D.d,[]),s.Rb(1073742336,x.e,x.e,[]),s.Rb(1073742336,G.b,G.b,[]),s.Rb(1073742336,B.b,B.b,[]),s.Rb(1073742336,L.c,L.c,[]),s.Rb(1073742336,q.a,q.a,[]),s.Rb(1073742336,O.d,O.d,[]),s.Rb(1073742336,V.c,V.c,[]),s.Rb(1073742336,_.a,_.a,[]),s.Rb(1073742336,j.a,j.a,[[2,L.g],s.O]),s.Rb(1073742336,H.b,H.b,[]),s.Rb(1073742336,J.a,J.a,[]),s.Rb(1073742336,F.b,F.b,[]),s.Rb(1073742336,o.Tb,o.Tb,[]),s.Rb(1073742336,g,g,[]),s.Rb(256,D.n,"XSRF-TOKEN",[]),s.Rb(256,D.o,"X-XSRF-TOKEN",[]),s.Rb(256,"config",{},[]),s.Rb(256,A.m,void 0,[]),s.Rb(256,A.n,void 0,[]),s.Rb(256,"popperDefaults",{},[]),s.Rb(1024,n.i,function(){return[[{path:"",component:m}]]},[])])}),W=[[""]],Y=s.Hb({encapsulation:0,styles:W,data:{}});function z(e){return s.dc(0,[s.Zb(402653184,1,{_container:0}),s.Zb(402653184,2,{scenarioIdLbl:0}),s.Zb(402653184,3,{systemLbl:0}),s.Zb(402653184,4,{titleLbl:0}),s.Zb(402653184,5,{activeLbl:0}),s.Zb(402653184,6,{desciptionLbl:0}),s.Zb(402653184,7,{scenarioIdTxt:0}),s.Zb(402653184,8,{titleTxt:0}),s.Zb(402653184,9,{descriptionTxt:0}),s.Zb(402653184,10,{systemCheck:0}),s.Zb(402653184,11,{activeCheck:0}),s.Zb(402653184,12,{tabNavigator:0}),s.Zb(402653184,13,{saveButton:0}),s.Zb(402653184,14,{cancelButton:0}),s.Zb(402653184,15,{loadingImg:0}),s.Zb(402653184,16,{generalTab:0}),s.Zb(402653184,17,{identificationTab:0}),s.Zb(402653184,18,{instancesTab:0}),s.Zb(402653184,19,{guiHighlightTab:0}),s.Zb(402653184,20,{eventsTab:0}),(e()(),s.Jb(20,0,null,null,66,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,i){var s=!0,a=e.component;"creationComplete"===t&&(s=!1!==a.onLoad()&&s);return s},I.ad,I.hb)),s.Ib(21,4440064,null,0,o.yb,[s.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(e()(),s.Jb(22,0,null,0,64,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,I.od,I.vb)),s.Ib(23,4440064,null,0,o.ec,[s.r,o.i,s.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(e()(),s.Jb(24,0,null,0,47,"Grid",[["height","100"],["minWidth","900"],["paddingLeft","10"],["paddingRight","10"],["width","100%"]],null,null,null,I.Cc,I.H)),s.Ib(25,4440064,null,0,o.z,[s.r,o.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"],paddingLeft:[3,"paddingLeft"],paddingRight:[4,"paddingRight"]},null),(e()(),s.Jb(26,0,null,0,17,"GridRow",[["width","100%"]],null,null,null,I.Bc,I.J)),s.Ib(27,4440064,null,0,o.B,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(28,0,null,0,3,"GridItem",[["width","240"]],null,null,null,I.Ac,I.I)),s.Ib(29,4440064,null,0,o.A,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(30,0,null,0,1,"SwtLabel",[],null,null,null,I.Yc,I.fb)),s.Ib(31,4440064,[[2,4],["scenarioIdLbl",4]],0,o.vb,[s.r,o.i],null,null),(e()(),s.Jb(32,0,null,0,3,"GridItem",[["width","300"]],null,null,null,I.Ac,I.I)),s.Ib(33,4440064,null,0,o.A,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(34,0,null,0,1,"SwtTextInput",[["maxChars","20"],["width","100%"]],null,null,null,I.kd,I.sb)),s.Ib(35,4440064,[[7,4],["scenarioIdTxt",4]],0,o.Rb,[s.r,o.i],{maxChars:[0,"maxChars"],width:[1,"width"]},null),(e()(),s.Jb(36,0,null,0,7,"GridItem",[["width","80%"]],null,null,null,I.Ac,I.I)),s.Ib(37,4440064,null,0,o.A,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(38,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,I.Dc,I.K)),s.Ib(39,4440064,null,0,o.C,[s.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(e()(),s.Jb(40,0,null,0,1,"SwtLabel",[["enabled","false"],["fontWeight","normal"]],null,null,null,I.Yc,I.fb)),s.Ib(41,4440064,[[3,4],["systemLbl",4]],0,o.vb,[s.r,o.i],{enabled:[0,"enabled"],fontWeight:[1,"fontWeight"]},null),(e()(),s.Jb(42,0,null,0,1,"SwtCheckBox",[["enabled","false"]],null,null,null,I.Oc,I.V)),s.Ib(43,4440064,[[10,4],["systemCheck",4]],0,o.eb,[s.r,o.i],{enabled:[0,"enabled"]},null),(e()(),s.Jb(44,0,null,0,17,"GridRow",[["width","100%"]],null,null,null,I.Bc,I.J)),s.Ib(45,4440064,null,0,o.B,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(46,0,null,0,3,"GridItem",[["width","240"]],null,null,null,I.Ac,I.I)),s.Ib(47,4440064,null,0,o.A,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(48,0,null,0,1,"SwtLabel",[],null,null,null,I.Yc,I.fb)),s.Ib(49,4440064,[[4,4],["titleLbl",4]],0,o.vb,[s.r,o.i],null,null),(e()(),s.Jb(50,0,null,0,3,"GridItem",[["width","300"]],null,null,null,I.Ac,I.I)),s.Ib(51,4440064,null,0,o.A,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(52,0,null,0,1,"SwtTextInput",[["maxChars","50"],["width","100%"]],null,null,null,I.kd,I.sb)),s.Ib(53,4440064,[[8,4],["titleTxt",4]],0,o.Rb,[s.r,o.i],{maxChars:[0,"maxChars"],width:[1,"width"]},null),(e()(),s.Jb(54,0,null,0,7,"GridItem",[["width","80%"]],null,null,null,I.Ac,I.I)),s.Ib(55,4440064,null,0,o.A,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(56,0,null,0,5,"HBox",[["horizontalAlign","right"],["width","100%"]],null,null,null,I.Dc,I.K)),s.Ib(57,4440064,null,0,o.C,[s.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(e()(),s.Jb(58,0,null,0,1,"SwtLabel",[["fontWeight","normal"]],null,null,null,I.Yc,I.fb)),s.Ib(59,4440064,[[5,4],["activeLbl",4]],0,o.vb,[s.r,o.i],{fontWeight:[0,"fontWeight"]},null),(e()(),s.Jb(60,0,null,0,1,"SwtCheckBox",[],null,null,null,I.Oc,I.V)),s.Ib(61,4440064,[[11,4],["activeCheck",4]],0,o.eb,[s.r,o.i],null,null),(e()(),s.Jb(62,0,null,0,9,"GridRow",[["width","100%"]],null,null,null,I.Bc,I.J)),s.Ib(63,4440064,null,0,o.B,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(64,0,null,0,3,"GridItem",[["width","240"]],null,null,null,I.Ac,I.I)),s.Ib(65,4440064,null,0,o.A,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(66,0,null,0,1,"SwtLabel",[],null,null,null,I.Yc,I.fb)),s.Ib(67,4440064,[[6,4],["desciptionLbl",4]],0,o.vb,[s.r,o.i],null,null),(e()(),s.Jb(68,0,null,0,3,"GridItem",[["horizontalAlign","right"],["width","90%"]],null,null,null,I.Ac,I.I)),s.Ib(69,4440064,null,0,o.A,[s.r,o.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(e()(),s.Jb(70,0,null,0,1,"SwtTextArea",[["height","40"],["maxChars","200"],["width","100%"]],null,null,null,I.jd,I.rb)),s.Ib(71,4440064,[[9,4],["descriptionTxt",4]],0,o.Qb,[s.r,o.i,s.L],{maxChars:[0,"maxChars"],width:[1,"width"],height:[2,"height"]},null),(e()(),s.Jb(72,0,null,0,2,"SwtTabNavigator",[["height","100%"],["id","tabNavigatorComponent"],["minHeight","590"],["minWidth","900"],["paddingBottom","3"],["width","100%"]],null,null,null,I.id,I.pb)),s.Ib(73,4440064,[[12,4],["tabNavigator",4]],1,o.Ob,[s.r,o.i,s.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minHeight:[3,"minHeight"],minWidth:[4,"minWidth"],paddingBottom:[5,"paddingBottom"]},null),s.Zb(603979776,21,{tabChildren:1}),(e()(),s.Jb(75,0,null,0,11,"SwtCanvas",[["height","40"],["minWidth","900"],["width","100%"]],null,null,null,I.Nc,I.U)),s.Ib(76,4440064,null,0,o.db,[s.r,o.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(e()(),s.Jb(77,0,null,0,5,"HBox",[["width","100%"]],null,null,null,I.Dc,I.K)),s.Ib(78,4440064,null,0,o.C,[s.r,o.i],{width:[0,"width"]},null),(e()(),s.Jb(79,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(e,t,i){var s=!0,a=e.component;"click"===t&&(s=!1!==a.saveHandler()&&s);return s},I.Mc,I.T)),s.Ib(80,4440064,[[13,4],["saveButton",4]],0,o.cb,[s.r,o.i],null,{onClick_:"click"}),(e()(),s.Jb(81,0,null,0,1,"SwtButton",[],null,[[null,"click"]],function(e,t,i){var s=!0,a=e.component;"click"===t&&(s=!1!==a.cancelHandler()&&s);return s},I.Mc,I.T)),s.Ib(82,4440064,[[14,4],["cancelButton",4]],0,o.cb,[s.r,o.i],null,{onClick_:"click"}),(e()(),s.Jb(83,0,null,0,3,"HBox",[["horizontalAlign","right"]],null,null,null,I.Dc,I.K)),s.Ib(84,4440064,null,0,o.C,[s.r,o.i],{horizontalAlign:[0,"horizontalAlign"]},null),(e()(),s.Jb(85,0,null,0,1,"SwtLoadingImage",[],null,null,null,I.Zc,I.gb)),s.Ib(86,114688,[[15,4],["loadingImg",4]],0,o.xb,[s.r],null,null)],function(e,t){e(t,21,0,"100%","100%");e(t,23,0,"100%","100%","5","5","5","5");e(t,25,0,"100%","100","900","10","10");e(t,27,0,"100%");e(t,29,0,"240"),e(t,31,0);e(t,33,0,"300");e(t,35,0,"20","100%");e(t,37,0,"80%");e(t,39,0,"right","100%");e(t,41,0,"false","normal");e(t,43,0,"false");e(t,45,0,"100%");e(t,47,0,"240"),e(t,49,0);e(t,51,0,"300");e(t,53,0,"50","100%");e(t,55,0,"80%");e(t,57,0,"right","100%");e(t,59,0,"normal"),e(t,61,0);e(t,63,0,"100%");e(t,65,0,"240"),e(t,67,0);e(t,69,0,"right","90%");e(t,71,0,"200","100%","40");e(t,73,0,"tabNavigatorComponent","100%","100%","590","900","3");e(t,76,0,"100%","40","900");e(t,78,0,"100%"),e(t,80,0),e(t,82,0);e(t,84,0,"right"),e(t,86,0)},null)}function Q(e){return s.dc(0,[(e()(),s.Jb(0,0,null,null,1,"app-scenario-detail",[],null,null,null,z,Y)),s.Ib(1,4440064,null,0,m,[o.i,s.r],null,null)],function(e,t){e(t,1,0)},null)}var Z=s.Fb("app-scenario-detail",m,Q,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);