import {Compo<PERSON>, ElementRef, ViewChild, NgModule, OnInit} from '@angular/core';
import {
  HTTPComms,
  SwtButton,
  JSONReader,
  Swt<PERSON>lert,
  SwtUtil,
  SwtTextInput,
  SwtLoadingImage,
  CommonService,
  SwtModule,
  SwtComboBox,
  Keyboard,
  focusManager,
  Alert,
  SwtToolBoxModule,
  SwtCommonGrid,
  SwtLabel,
  SwtPopUpManager,
  StringUtils, XML, TitleWindow
} from 'swt-tool-box';

import {Tab} from "swt-tool-box";
import {SwtTabNavigator} from "swt-tool-box";
import {GeneralTab} from "../tabs/GeneralTab/GeneralTab";
import {CutOffTab} from "../tabs/CutOffTab/CutOffTab";
import {SpreadingTab} from "../tabs/SpreadingTab/SpreadingTab";
import {LiquidityTab} from "../tabs/LiquidityTab/LiquidityTab";
import {FourEyesProcess} from "../../FourEyesProcess/FourEyesProcess";
import moment from "moment";
import {Router, RouterModule, Routes} from "@angular/router";
import {ModuleWithProviders} from "@angular/compiler/src/core";

@Component({
  selector: 'app-pcaccount-groups-maintenance-add',
  templateUrl: './AccountGroupsAdd.html',
  styleUrls: ['./AccountGroupsAdd.css']
})
export class AccountGroupsAdd extends SwtModule implements OnInit {
 
  public jsonReader = new JSONReader();
  public lastRecievedJSON;
  public lastRecievedAccountNotInGroupJSON;
  public prevRecievedJSON;
  private swtalert: SwtAlert;
  public currencyPattern: string;
  public lastOrdinal: string = null;
  private spreadIdFilterGrid:string = null;
  private win: TitleWindow;
  public spreadRows :any;
  public cutOffArray = [];
  public processTimesArray = [];


  /**
   * Communication Objects
   **/
  private inputData = new HTTPComms(this.commonService);
  private inputDataAcct = new HTTPComms(this.commonService);
  private logicUpdate = new HTTPComms(this.commonService);
  private baseURL = SwtUtil.getBaseURL();
  private actionMethod = "";
  private actionPath = "";
  private requestParams = [];
  public screenName: string = null;
  public title: string = null;
  public accountGroupId: string;
  public listOfOrder: any;
  public ordinalFromJson: string = null;
  public operationsList: XML = new XML( "<operationsList/>");
  public reserveList: any ;
  public cutOffList: any;

  public general: Tab;
  public cutOffs:Tab;
  public spreading:Tab;
  public liquidity:Tab;
  private isAccocuntDuplicated : boolean  = false;
  private firstLoad : boolean = true;
  private isFromMaintenanceEvent  = false;
  private isFromMaintenanceEventAmendMode  = false;
  private maintEventId = null;

  private parentMenuAccess = null;
  private  authOthers= null;
  private  canAmendFacility= false;
  private requireAuthorisation = true;
  /********Navigator******/
  @ViewChild('aggAccountNavigator') aggAccountNavigator: SwtTabNavigator;
  @ViewChild('currencyComboBox') currencyComboBox: SwtComboBox;
  /************SwtTextInput********/
  @ViewChild('accountGpId') accountGpId: SwtTextInput;
  @ViewChild('accountGpName') accountGpName: SwtTextInput;
  /****Label*********/
  @ViewChild('ccyLabel') ccyLabel: SwtLabel;
  @ViewChild('accountGpIdLabel') accountGpIdLabel: SwtLabel;
  @ViewChild('accountGrpNameLabel') accountGrpNameLabel: SwtLabel;
  @ViewChild('currencyLabel') currencyLabel: SwtLabel;

  /***LodingImage*******/
  @ViewChild('loadingImage') loadingImage: SwtLoadingImage;
  /*********SWtButton*************/
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  /******tabs************/
  @ViewChild('generalTab') generalTab: GeneralTab;
  @ViewChild('cutOffTab') cutOffTab: CutOffTab;
  @ViewChild('spreadingTab') spreadingTab: SpreadingTab;
  @ViewChild('liquidityTab') liquidityTab: LiquidityTab;
  /***Module****/
  @ViewChild('swtModule') swtModule: SwtModule;

  @ViewChild('acceptButton') acceptButton: SwtButton;
  @ViewChild('rejectButton') rejectButton: SwtButton;
  @ViewChild('amendButton') amendButton: SwtButton;

  @ViewChild('cancelAmendButton') cancelAmendButton: SwtButton;
  @ViewChild('closeButton') closeButton: SwtButton;
  

  

  constructor(private commonService: CommonService, private element: ElementRef, private router: Router) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }


  ngOnInit(): void {

    this.acceptButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.label', null);
    this.acceptButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.accept.tooltip', null);

    this.rejectButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.label', null);
    this.rejectButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.reject.tooltip', null);


    
    if(window.opener.instanceElement) {
      let params = window.opener.instanceElement.getParams();
      this.screenName  = params[0];
      if (params[1]) {
        this.accountGroupId= params[1] ;
      }
      if (params.length > 2 &&  params[3]) {
        this.isFromMaintenanceEvent = true;
        if("I" == params[3]){
          this.isFromMaintenanceEventAmendMode = true;
      }
      params.length > 2 &&  params[3]
        this.maintEventId = params[2];
        if(params.length> 3 &&  params[4])
          this.parentMenuAccess = params[4]; 

          if(params.length> 4 &&  params[5])
            this.authOthers = params[5]; 

            if(params.length> 5 &&  params[6])
              this.canAmendFacility = StringUtils.isTrue(params[6]); 
            

        
      }

      // this.isFromMaintenanceEvent = false;
     }
    this.accountGpIdLabel.text = SwtUtil.getPredictMessage('acctGroupsMaintenanceDetails.label.accountGrpId', null);
    this.accountGpId.toolTip = SwtUtil.getPredictMessage('acctGroupsMaintenanceDetails.tooltip.accountGrpId', null);
    this.accountGrpNameLabel.text = SwtUtil.getPredictMessage('acctGroupsMaintenanceDetails.label.accountGrpName', null);
    this.accountGpName.toolTip = SwtUtil.getPredictMessage('acctGroupsMaintenanceDetails.tooltip.accountGrpName', null);
    this.currencyLabel.text = SwtUtil.getPredictMessage('acctGroupsMaintenanceDetails.label.currency', null);
    this.currencyComboBox.toolTip = SwtUtil.getPredictMessage('acctGroupsMaintenanceDetails.tooltip.currency', null);
    this.saveButton.label = SwtUtil.getPredictMessage('button.save', null);
    this.cancelButton.label = SwtUtil.getPredictMessage('button.cancel', null);

        
    this.amendButton.label = SwtUtil.getPredictMessage('maintenanceevent.details.button.amend.label', null);
    this.amendButton.toolTip = SwtUtil.getPredictMessage('maintenanceevent.details.button.amend.tooltip', null);

    this.cancelAmendButton.label = SwtUtil.getPredictMessage('button.cancel', null);
    this.cancelAmendButton.toolTip = SwtUtil.getPredictMessage('tooltip.CancelChanges', null);

    this.closeButton.label = SwtUtil.getPredictMessage('button.close', null);
    this.closeButton.toolTip = SwtUtil.getPredictMessage('tooltip.entityMonitor.close', null);




  }

  destoyAllTooltips(){
    $(".ui-tooltip" ).each(function( index ) {
        $(this).remove();
    });
  }

  amendEventHandler(){
    this.destoyAllTooltips();
    window.opener.instanceElement.setViewOrAmendSubScreenFromChild('change');
    const currentUrl = this.router.url;
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigateByUrl("/AccountGroupDetail");
    });

  }

  cancelAmendEventHandler(){
    this.destoyAllTooltips();
    window.opener.instanceElement.setViewOrAmendSubScreenFromChild('view');
    const currentUrl = this.router.url;
    this.router.navigateByUrl('/', { skipLocationChange: true }).then(() => {
      this.router.navigateByUrl("/AccountGroupDetail");
    });

  }

  
  dynamicCreation(): void {
    /**dynamic creation*///


    this.general = <Tab> this.aggAccountNavigator.addChild(Tab);
    this.cutOffs = <Tab>this.aggAccountNavigator.addChild(Tab);
    this.spreading = <Tab>this.aggAccountNavigator.addChild(Tab);
    this.liquidity = <Tab>this.aggAccountNavigator.addChild(Tab);

    this.general.label = this.general.id = SwtUtil.getPredictMessage('tab.general', null);
    this.cutOffs.label  = this.cutOffs.id = SwtUtil.getPredictMessage('tab.cutOff', null);
    this.spreading.label=  this.spreading.id = SwtUtil.getPredictMessage('tab.spreading', null);
    this.liquidity.label= this.liquidity.id = SwtUtil.getPredictMessage('tab.liquidity', null);

    this.generalTab =<GeneralTab> this.general.addChild(GeneralTab);
    this.cutOffTab = <CutOffTab>this.cutOffs.addChild(CutOffTab);
    this.spreadingTab = <SpreadingTab>this.spreading.addChild(SpreadingTab);
    this.liquidityTab = <LiquidityTab>this.liquidity.addChild(LiquidityTab);

    this.generalTab.rightGrid = <SwtCommonGrid>this.generalTab.rightCanvas.addChild(SwtCommonGrid);
    this.generalTab.leftGrid = <SwtCommonGrid>this.generalTab.leftCanvas.addChild(SwtCommonGrid);
    this.cutOffTab.cutOffGrid = <SwtCommonGrid>this.cutOffTab.cutOffCanvas.addChild(SwtCommonGrid);
    this.spreadingTab.spreadGrid = <SwtCommonGrid>this.spreadingTab.spreadCanvas.addChild(SwtCommonGrid);
    this.liquidityTab.reserveGrid = <SwtCommonGrid>this.liquidityTab.reserveCanvas.addChild(SwtCommonGrid);

    this.general.id = "general";
    this.cutOffs.id ="cutOff";
    this.spreading.id= "spreading" ;
    this.liquidity.id = "liquidity";

    this.generalTab.parentDocument = this;
    this.cutOffTab.parentDocument = this.spreadingTab.parentDocument = this.liquidityTab.parentDocument = this;

    if (this.screenName == 'add') {
      this.accountGpId.required = true;
      this.accountGpName.required = true;
      this.currencyComboBox.required = true;
      this.cutOffTab.cobTimeInput.required = true;
    } else {
      this.currencyComboBox.enabled = false;
      this.accountGpId.enabled = false;
      if (this.screenName == "view") {
        this.enableDisableComponent(false);
      } else if (this.screenName == "change") {
        this.enableDisableComponent(true);
      }
    }

    this.generalTab.leftGrid.allowMultipleSelection = true;
    this.generalTab.rightGrid.allowMultipleSelection = true;
    this.generalTab.height = "100%";
    this.cutOffTab.height = "100%";
    this.spreadingTab.height = "100%";
    this.liquidityTab.height = "100%";

  }
  /**
   * onLoad
   * Initializer (multilingual, fetch list details )
   */
  onLoad(): void {


    try {
      this.requestParams = [];


      if(this.maintEventId && StringUtils.isTrue(this.authOthers) && this.screenName != "change"){
        this.rejectButton.visible = true;
        this.acceptButton.visible = true;
      }

      if(this.maintEventId  && this.screenName == "view"){
        this.amendButton.visible = true;
        this.amendButton.includeInLayout = true;
        this.saveButton.visible = false;
        this.saveButton.includeInLayout = false;
        if(StringUtils.isTrue(this.canAmendFacility)){
          this.amendButton.enabled = true;
          this.rejectButton.visible = true;
        }else {
          this.amendButton.enabled = false;
        }
      }else {
        if(this.maintEventId){
          this.saveButton.visible = true;
          this.saveButton.includeInLayout = true;
          this.cancelAmendButton.visible = true;
          this.cancelAmendButton.includeInLayout = true;

          this.closeButton.visible = true;
          this.closeButton.includeInLayout = true;

          this.cancelButton.visible = false;
          this.cancelButton.includeInLayout = false;

          this.amendButton.visible = false;
          this.amendButton.includeInLayout = false;
        }
      }

      
      this.actionMethod = 'method=add';
      this.actionPath = 'accountGroupsPCM.do?';
      this.inputData.cbResult = (event) => {
        this.inputDataResult(event);
      };
      this.requestParams['accountGroupId'] = this.accountGroupId;
      this.requestParams['screenName'] = this.screenName;
      this.requestParams['method'] = 'add';
      this.requestParams['isFromMaintenanceEventAmendMode'] = this.isFromMaintenanceEventAmendMode;
      this.requestParams['isFromMaintenanceEvent'] = this.isFromMaintenanceEvent;
      this.requestParams['maintEventId'] = this.maintEventId;
      

      this.inputData.cbStart = this.startOfComms.bind(this);
      this.inputData.cbStop = this.endOfComms.bind(this);
      this.inputData.cbFault = this.inputDataFault.bind(this);
      this.inputData.encodeURL = false;
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.inputData.send(this.requestParams);
      this.dynamicCreation();

    } catch (error) {

    }
  }

  /**
   * startOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  startOfComms(): void {
    this.loadingImage.setVisible(true);
  }

  /**
   * endOfComms
   * Part of a callback  to all for control of the loading swf from the HTTPComms Object
   */
  endOfComms(): void {
    this.loadingImage.setVisible(false);
  }

  /**
   * inputDataResult
   * param event: ResultEvent
   * This is a callback method, to handle result event
   *
   */
  inputDataResult(event): void {
    try {
      if (this.inputData.isBusy()) {
        this.inputData.cbStop();
      } else {
        this.lastRecievedJSON = event;
        this.jsonReader.setInputJSON(this.lastRecievedJSON);
        if (!(JSON.stringify(this.lastRecievedJSON) === JSON.stringify(this.prevRecievedJSON))) {
         
          if (this.jsonReader.getRequestReplyStatus()) {
            if (!this.jsonReader.isDataBuilding()) {
              this.currencyPattern  = this.jsonReader.getSingletons().currencyPattern;
              this.requireAuthorisation = this.jsonReader.getScreenAttributes()["requireAuthorisation"];

              setTimeout(() => {
                this.fillComboData();
                this.fillAccountsGridData();
                this.fillReserveGrid();
                this.fillCutOffGrid();
                this.fillAccountsInGroup();
                this.fillSpreadGridData();
                this.generalTab.enableMoveButtons();
              }, 0);




              setTimeout(()=> {
                this.swtModule.subscribeSpy([
                  this.cutOffTab.cutOffGrid,
                  this.liquidityTab.reserveGrid
                ]);
                this.swtModule.onSpyChange.subscribe(() => {
                  /**********General Tab cutoff  and p_rule tables*************/
                  this.reserveList = this.liquidityTab.reserveGrid.changes;
                  this.cutOffList = this.cutOffTab.cutOffGrid.changes;

                });

              }, 0);



            }
          }
        }
      }
    } catch (error) {
      console.log("error:   ", error);

    }
  }

  fillComboData(): void {
    /***********Combo*******/
    if (this.screenName ==  "add") {
      this.currencyComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(), false, "");
    } else {
      this.currencyComboBox.setComboData(this.jsonReader.getSelects(), false);
      this.ccyLabel.text = this.currencyComboBox.selectedItem.value;
    }
    this.generalTab.defaultCategoryCombo.setComboData(this.jsonReader.getSelects(), true);
    this.generalTab.quickCategoryCombo.setComboData(this.jsonReader.getSelects(), true);
    this.spreadingTab.targetCalculationCombo.setComboData(this.jsonReader.getSelects(), true);

    if (this.screenName !== "add") {
      this.accountGpId.text = this.jsonReader.getSingletons().AccGpId;
      this.accountGpName.text = this.jsonReader.getSingletons().accountGpName;
      /********GeneralTab*************/
      this.generalTab.ordinalNumericInput.text = this.jsonReader.getSingletons().ordinal;
      this.lastOrdinal = this.jsonReader.getSingletons().ordinal;
      this.currencyComboBox.selectedLabel = this.jsonReader.getSingletons().ccy;
      this.ccyLabel.text = this.currencyComboBox.selectedValue;
      this.generalTab.defaultCategoryCombo.selectedLabel = this.jsonReader.getSingletons().defaultCategory;
      if (this.jsonReader.getSingletons().quickCategory) {
        this.generalTab.quickCategoryCombo.selectedLabel = this.jsonReader.getSingletons().quickCategory;
      }
      /****CuOffTab*************/
      this.cutOffTab.kickoffTimeInput.text = this.jsonReader.getSingletons().kickoffTime;
      this.cutOffTab.cobTimeInput.text = this.jsonReader.getSingletons().COBTime;
      this.cutOffTab.eodBeginTimeInput.text = this.jsonReader.getSingletons().EODTime;
      /*****SpreadTab***********/

      this.spreadingTab.spreadProfileCombo.selectedLabel = this.jsonReader.getSingletons().spreadId;
      this.spreadIdFilterGrid = this.jsonReader.getSingletons().spreadId;
      this.spreadingTab.targetCalculationCombo.selectedLabel = (this.jsonReader.getSingletons().targetCalculation == 'O') ? 'Outstanding' : 'All';

      if (this.isFromMaintenanceEvent) {
        // this.ordinalNumericInput.toolTipPreviousValue="99999";
        // this.defaultCategoryCombo.toolTipPreviousValue = "-----Atefef----99"
        // this.quickCategoryCombo.toolTipPreviousValue = "-12-----99"

        this.accountGpId.text = this.jsonReader.getSingletons().AccGpId;
        if(this.jsonReader.getSingletons().accountGpName_oldValue != undefined && this.jsonReader.getSingletons().accountGpName_oldValue != this.jsonReader.getSingletons().accountGpName)
         this.accountGpName.toolTipPreviousValue = this.jsonReader.getSingletons().accountGpName_oldValue;
        /********GeneralTab*************/
        
        if(this.jsonReader.getSingletons().ordinal_oldValue != undefined && this.jsonReader.getSingletons().ordinal_oldValue != this.jsonReader.getSingletons().ordinal){
          this.generalTab.ordinalNumericInput.toolTipPreviousValue = this.jsonReader.getSingletons().ordinal_oldValue;
        }

        if(this.jsonReader.getSingletons().defaultCategory_oldValue != undefined && this.jsonReader.getSingletons().defaultCategory_oldValue != this.jsonReader.getSingletons().defaultCategory)
          this.generalTab.defaultCategoryCombo.toolTipPreviousValue = this.jsonReader.getSingletons().defaultCategory_oldValue;
       
        if(this.jsonReader.getSingletons().quickCategory_oldValue!= undefined && this.jsonReader.getSingletons().quickCategory_oldValue != this.jsonReader.getSingletons().quickCategory)
          this.generalTab.quickCategoryCombo.toolTipPreviousValue = this.jsonReader.getSingletons().quickCategory_oldValue;
        if(this.jsonReader.getSingletons().kickoffTime_oldValue != undefined && this.jsonReader.getSingletons().kickoffTime_oldValue != this.jsonReader.getSingletons().kickoffTime)
          this.cutOffTab.kickoffTimeInput.toolTipPreviousValue = this.jsonReader.getSingletons().kickoffTime_oldValue;
        if(this.jsonReader.getSingletons().COBTime_oldValue != undefined && this.jsonReader.getSingletons().COBTime_oldValue != this.jsonReader.getSingletons().COBTime)
          this.cutOffTab.cobTimeInput.toolTipPreviousValue = this.jsonReader.getSingletons().COBTime_oldValue;  
        if(this.jsonReader.getSingletons().EODTime_oldValue != undefined && this.jsonReader.getSingletons().EODTime_oldValue != this.jsonReader.getSingletons().EODTime)
          this.cutOffTab.eodBeginTimeInput.toolTipPreviousValue = this.jsonReader.getSingletons().EODTime_oldValue;
        if(this.jsonReader.getSingletons().targetCalculation_oldValue != undefined && this.jsonReader.getSingletons().targetCalculation_oldValue != this.jsonReader.getSingletons().targetCalculation){
          this.spreadingTab.targetCalculationCombo.toolTipPreviousValue = (this.jsonReader.getSingletons().targetCalculation_oldValue == 'O') ? 'Outstanding' : 'All';
        }


        if(this.jsonReader.getSingletons().spreadId_oldValue != undefined && this.jsonReader.getSingletons().spreadId_oldValue != this.jsonReader.getSingletons().spreadId)
        this.spreadingTab.spreadProfileCombo.toolTipPreviousValue = this.jsonReader.getSingletons().spreadId_oldValue;


  
  
    }
  }


 
  }

  fillAccountsGridData(): void {
    try {
      if(this.currencyComboBox.selectedItem) {
        this.actionMethod = 'method=getAvailableAccounts';
        this.actionPath = 'accountGroupsPCM.do?';
        this.inputDataAcct.cbResult = (data) => {
          this.inputDataResultAccountGrid(data);
        };
        this.requestParams['currencyCode'] = (this.currencyComboBox.selectedItem) ? this.currencyComboBox.selectedLabel : null;
        this.requestParams['method'] = 'getAvailableAccounts';
        this.requestParams['isFromMaintenanceEventAmendMode'] = this.isFromMaintenanceEventAmendMode;
        this.requestParams['isFromMaintenanceEvent'] = this.isFromMaintenanceEvent;
        this.requestParams['accountGroup'] = this.accountGroupId;
        this.requestParams['maintEventId'] = this.maintEventId;

        
        this.inputDataAcct.encodeURL = false;
        this.inputDataAcct.url = this.baseURL + this.actionPath + this.actionMethod;
        this.inputDataAcct.send(this.requestParams);
      }

    } catch (e) {
      console.log('eeeeeeeeee****************', e)
    }



  }
  inputDataResultAccountGrid(event): void {
    let sizeLeftGrid : number = 0;
    if (this.inputDataAcct.isBusy()) {
      this.inputDataAcct.cbStop();
    } else {
      /* Get result as xml */
      this.lastRecievedAccountNotInGroupJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedAccountNotInGroupJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        if (!this.jsonReader.isDataBuilding()) {
          if(this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.AccountsNotGrpGrid.rows.row) {
            sizeLeftGrid = this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.AccountsNotGrpGrid.rows.size;
            this.generalTab.leftGrid.gridData = {row: this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.AccountsNotGrpGrid.rows.row, size: sizeLeftGrid};
          }
          this.generalTab.leftGrid.allowMultipleSelection = true;
          //when chnage currency reset buttons
          this.generalTab.enableMoveButtons();
          this.generalTab.countIDNotInGroup.text = '('+ sizeLeftGrid+ ')';


          if(this.screenName == 'add') {
            this.spreadingTab.spreadProfileCombo.dataProvider = [];
            this.spreadingTab.spreadNameTextInput.text = '';
            this.spreadIdFilterGrid = null;
            this.fillSpreadGridData();
            this.spreadingTab.spreadProfileCombo.setComboDataAndForceSelected(this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.selects, true, "");

          }
          else {
            this.spreadingTab.spreadProfileCombo.setComboData(this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.selects, true);
            this.spreadingTab.spreadProfileCombo.selectedLabel = this.spreadIdFilterGrid;
            this.spreadingTab.spreadNameTextInput.text = this.spreadingTab.spreadProfileCombo.selectedValue;

          }
          this.listOfOrder = this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.singletons.ordinaList;
          if (this.screenName == 'add') {
            this.generalTab.ordinalNumericInput.text = this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.singletons.maxOrdinal;
            this.ordinalFromJson = this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.singletons.maxOrdinal;
          }

        }
      }

    }
  }
  fillAccountsInGroup(): void {
    try {
      this.generalTab.leftGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.acountsGrpGrid.metadata);
      this.generalTab.rightGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.acountsGrpGrid.metadata);
      if (this.screenName !== 'add') {
        this.generalTab.rightGrid.gridData = this.lastRecievedJSON.PCAccountMaintenance.acountsGrpGrid.rows;
      }
      this.generalTab.countIDInGroup.text = '('+ this.generalTab.rightGrid.dataProvider.length + ')';
      this.generalTab.rightGrid.allowMultipleSelection = true;
      this.generalTab.leftGrid.enableColumn('ilmCB', false);
      this.generalTab.rightGrid.enableColumn('ilmCB', false);

    } catch (e) {
      console.log('error fillAccountsInGroup', e)
    }

  }

  fillSpreadGridData(): void {
    /******Spreading Grid*************/
    let spreadList = [];
    this.spreadingTab.spreadGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.spreadGrid.metadata);
    this.spreadRows = this.lastRecievedJSON.PCAccountMaintenance.spreadGrid.rows;
    if(this.screenName != "add") {
      for(let i = 0; i < this.spreadRows.size; i++) {
        if(this.spreadRows.row[i].spreadId.content == this.spreadIdFilterGrid) {
          spreadList.push(this.spreadRows.row[i]);

        }
      }
      this.spreadingTab.spreadGrid.gridData = {row: spreadList, size: spreadList.length};
    }

  }

  fillReserveGrid(): void {
    this.liquidityTab.reserveGrid.currencyFormat = this.currencyPattern;
    this.liquidityTab.reserveGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.reserveGrid.metadata);
    this.liquidityTab.reserveGrid.enableColumn("useCL", false);
    if (this.screenName !== "add")
      this.liquidityTab.reserveGrid.gridData = this.lastRecievedJSON.PCAccountMaintenance.reserveGrid.rows;

  }

  fillCutOffGrid(): void {
    this.cutOffTab.cutOffGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.cutOffGrid.metadata);
    if (this.screenName !== 'add') {
      this.cutOffTab.cutOffGrid.gridData = this.lastRecievedJSON.PCAccountMaintenance.cutOffGrid.rows;

    }
  }
  changeComboCurrency(): void {
    let message = SwtUtil.getPredictMessage('alert.changeCurrency', null);
    this.ccyLabel.text = (this.currencyComboBox.selectedItem) ? this.currencyComboBox.selectedItem.value : "";
    this.generalTab.filterTextLeft.text = "";
    this.generalTab.rightTextOfInput = "";
    this.fillAccountsGridData();
    this.fillAccountsInGroup();
    if (!this.firstLoad) {
      this.swtalert.warning(message);
    }
    this.firstLoad = false;
  }

  inputDataFault(): void {
    this.swtalert.error('generic_exception');
  }


  /**
   * keyDownEventHandler
   * param event: KeyboardEvent
   * This is a key event listener, used to perform the operation
   * when hit the enter key based on the currently focused property(button)
   */
  keyDownEventHandler(event: KeyboardEvent): void {
    try {
      //Currently focussed property name
      let eventString: string = Object(focusManager.getFocus()).name;
      if ((event.keyCode == Keyboard.ENTER)) {
        if (eventString == "saveButton") {
          this.save();
        } else if (eventString == "cancelButton") {
          this.popupClosed();
        }
      }
    } catch (error) {
      // log the error in ERROR LOG

    }
  }

  /**
   * popupClosed
   * Method to close child windows when this screen is closed
   */
  popupClosed(): void {
    window.close();
    //SwtPopUpManager.getPopUpById("addGroupTitleWindow").close();

  }

  enableDisableComponent(value: boolean): void {
    this.accountGpName.enabled = value;
    this.saveButton.enabled = value;
    this.generalTab.ordinalNumericInput.enabled = value;
    this.generalTab.buttonsContainer.enabled = value;
    this.generalTab.defaultCategoryCombo.enabled = value;
    this.generalTab.quickCategoryCombo.enabled = value;
  }

  validateAccountIdHandler(): void {
    let accounIdArray = [];
    accounIdArray = this.existingAccountId();
    let messageOfDuplicatedId = SwtUtil.getPredictMessage('alert.accountIDExist', null);

    if (accounIdArray[0] != "" && accounIdArray.indexOf(this.accountGpId.text) !== -1 && this.screenName == 'add') {
      this.swtalert.confirm(messageOfDuplicatedId, 'Alert', Alert.OK, null);
      this.isAccocuntDuplicated = true;

    } else {
      this.isAccocuntDuplicated = false;
    }
  }

  validateAccountNameHandler(): void {
    let accountNameRestrictPattern: RegExp = /^[A-Za-z\d !"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~]*$/;
    let message = SwtUtil.getPredictMessage('alert.validateAccountName', null);
    if (!(accountNameRestrictPattern.test(this.accountGpName.text))) {
      this.swtalert.confirm(message, 'Alert', Alert.OK, null);
    }
  }

  existingAccountId(): any {
    let arryOfId: any;
    arryOfId = this.lastRecievedJSON.PCAccountMaintenance.singletons.groupIdList;
    arryOfId = arryOfId.replace("[", "").replace("]", "").replace(/ /g, "");
    arryOfId = arryOfId.split(',').map(String);
    return arryOfId;
  }

  /**
   * save()
   * Method called on save button clicked
   */
  xmlDataReserve() : any {

    let row = [];
    if(this.reserveList) {
      for (let i= 0; i < this.reserveList.getValues().length; i++) {
        row = [];
        row['TIME'] = this.reserveList.getValues()[i].crud_data.time;
        row['RESERVE'] = this.reserveList.getValues()[i].crud_data.reserve;
        row['CREDIT_LINE'] = this.reserveList.getValues()[i].crud_data.useCL;
        if (this.reserveList.getValues()[i].crud_operation == "I") {
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_RESERVE', 'I', 'M'));
        }
        if (this.reserveList.getValues()[i].crud_operation.substring(0,1) == "U") {
          row['ID_RESERVE'] = this.reserveList.getValues()[i].crud_data.idReserve;
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_RESERVE', 'U', 'M'));
        }
        if (this.reserveList.getValues()[i].crud_operation.substring(0,1) == "D") {
          row['ID_RESERVE'] = this.reserveList.getValues()[i].crud_data.idReserve;
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_RESERVE', 'D', 'M'));
        }

      }
    }

  }
  xmlDataCutOff(): any {
    let row = [];
    if(this.cutOffList) {
      for (let i= 0; i < this.cutOffList.getValues().length; i++) {
        row = [];
        row['CUTOFF_TIME'] = this.cutOffList.getValues()[i].crud_data.cutOffTime;
        row['ORDINAL'] = this.cutOffList.getValues()[i].crud_data.testOrder;
        row['LOG_TEXT'] = this.cutOffList.getValues()[i].crud_data.logText ;
        row['RULE_TYPE'] = "C";
        row['RULE_TEXT'] = this.cutOffList.getValues()[i].crud_data.ruleText;
        row['TAB_CONDITION']= this.cutOffList.getValues()[i].crud_data.ruleCondition;
        row['RULE_QUERY'] = this.cutOffList.getValues()[i].crud_data.ruleQuery;
        if (this.cutOffList.getValues()[i].crud_operation == "I") {

          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_CUTOFF', 'I', 'M'));

        } else if(this.cutOffList.getValues()[i].crud_operation.substring(0,1) == "U") {
          row['ID_CUTOFF'] = this.cutOffList.getValues()[i].crud_data.cutoffRuleId;
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_CUTOFF', 'U', 'M'));
        }  else if(this.cutOffList.getValues()[i].crud_operation.substring(0,1) == "D") {
          row['ID_CUTOFF'] = this.cutOffList.getValues()[i].crud_data.cutoffRuleId;
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_CUTOFF', 'D', 'M'));
        }
      }
    }
  }
  xmlAccountGroup() : void {
    let row = [];
    let arrayIdOriginal = [];
    let arrayIdCurrent = [];
    if(this.generalTab.rightGrid.dataProvider && this.generalTab.rightGrid.originalDataprovider) {
      for (let i = 0; i < this.generalTab.rightGrid.dataProvider.length; i++) {
        arrayIdCurrent.push(this.generalTab.rightGrid.dataProvider[i].account_id_name + this.generalTab.rightGrid.dataProvider[i].entity);
      }
      for (let i = 0; i < this.generalTab.rightGrid.originalDataprovider.length; i++) {
        arrayIdOriginal.push(this.generalTab.rightGrid.originalDataprovider[i].account_id_name + this.generalTab.rightGrid.originalDataprovider[i].entity);
      }
      if (this.screenName == "add") {
        for (let i = 0; i < this.generalTab.rightGrid.dataProvider.length; i++) {

          row = [];
          row['ACC_GROUP_ID'] = this.accountGpId.text;
          row['ACCOUNT_ID'] = this.generalTab.rightGrid.dataProvider[i].account_id_name.split(' -')[0];
          row['ENTITY'] = this.generalTab.rightGrid.dataProvider[i].entity;
          this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_ACCOUNTS_IN_GROUP', 'I', 'M'));

        }
      } else {
        for (let i = 0; i < this.generalTab.rightGrid.dataProvider.length; i++) {
          if (arrayIdOriginal.indexOf(this.generalTab.rightGrid.dataProvider[i].account_id_name + this.generalTab.rightGrid.dataProvider[i].entity) == -1) {
            row = [];
            row['ACC_GROUP_ID'] = this.accountGpId.text;
            row['ACCOUNT_ID'] = this.generalTab.rightGrid.dataProvider[i].account_id_name.split(' -')[0];
            row['ENTITY'] = this.generalTab.rightGrid.dataProvider[i].entity;
            this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_ACCOUNTS_IN_GROUP', 'I', 'M'));
          }
        }
        for (let i = 0; i < this.generalTab.rightGrid.originalDataprovider.length; i++) {
          if (arrayIdCurrent.indexOf(this.generalTab.rightGrid.originalDataprovider[i].account_id_name + this.generalTab.rightGrid.originalDataprovider[i].entity) == -1) {
            row = [];
            row['ACC_GROUP_ID'] = this.accountGpId.text;
            row['ACCOUNT_ID'] = this.generalTab.rightGrid.originalDataprovider[i].account_id_name.split(' -')[0];
            row['ENTITY'] = this.generalTab.rightGrid.originalDataprovider[i].entity;
            this.operationsList.appendChild(StringUtils.getKVTypeTabAsXML(row, 'PC_ACCOUNTS_IN_GROUP', 'D', 'M'));
          }
        }
      }
    }
  }
  verifyCutOff(): any {
    try {
      this.cutOffArray = [];
      if(this.cutOffTab.cutOffGrid.dataProvider.length >0) {
        for(let i = 0; i < this.cutOffTab.cutOffGrid.dataProvider.length; i++) {
          this.cutOffArray.push(this.cutOffTab.cutOffGrid.dataProvider[i].cutOffTime);
        }
        for(let i = 0; i < this.cutOffArray.length; i++) {
          if(!this.validateTime(this.cutOffArray[i])) {
            return false;
            break;
          }
        }
      }
      return true;
    } catch(e) {
    }
  }
  verifyProcessPointsTime(): any {
    try {
      this.processTimesArray = [];
      if(this.spreadingTab.spreadGrid.dataProvider.length >0) {
        for(let i = 0; i < this.spreadingTab.spreadGrid.dataProvider.length; i++) {
          this.processTimesArray.push(this.spreadingTab.spreadGrid.dataProvider[i].time);
        }
        for(let i = 0; i < this.processTimesArray.length; i++) {
          if(!this.validateProcessTime(this.processTimesArray[i])) {
            return false;
            break;
          }
        }
      }
      return true;
    } catch(e) {
    }
  }
  save(): void {
    Alert.yesLabel = SwtUtil.getPredictMessage('button.save', null);
    Alert.noLabel = SwtUtil.getPredictMessage('button.cancel', null);
    let message =  SwtUtil.getPredictMessage('alert.mandatoryField', null);
    let messageId = SwtUtil.getPredictMessage('alert.accountIDExist', null);
    let messageEmptyName = SwtUtil.getPredictMessage('alert.accountNameWithoutSpaces', null);
    let messageZeroAccount = SwtUtil.getPredictMessage('alert.zeroAccountsInclude', null);
    let messageNoSpread = SwtUtil.getPredictMessage('alert.noSpread', null);
    let messageEOD = SwtUtil.getPredictMessage('alert.eodMandatory', null);
    let messageOrder = SwtUtil.getPredictMessage('alert.orderHigherThanZero', null);
    let messageCategory = SwtUtil.getPredictMessage('alert.categoryMandatory', null);
    let accounIdArray = [];
    accounIdArray = (this.screenName == 'add') ? this.existingAccountId() : [];


    try {
      this.operationsList = new XML( "<operationsList/>");
      this.xmlDataReserve();
      this.xmlDataCutOff();
      this.xmlAccountGroup();
      if(!this.verifyCutOff()) {

      }
      else if((this.cutOffTab.cobTimeInput.text && this.cutOffTab.eodBeginTimeInput.text && this.cutOffTab.kickoffTimeInput.text) &&( !this.cutOffTab.validateTime(this.cutOffTab.cobTimeInput) || !this.cutOffTab.validateTime(this.cutOffTab.eodBeginTimeInput) || !this.cutOffTab.validateTime(this.cutOffTab.kickoffTimeInput))) {
      }
      else if (accounIdArray.indexOf(this.accountGpId.text) !== -1 && this.screenName == 'add') {
        this.swtalert.error(messageId);
      } else if (!this.accountGpId.text || !this.accountGpName.text   || !this.cutOffTab.cobTimeInput.text ||  this.currencyComboBox.selectedItem == undefined) {
        this.swtalert.error(message);
      } else if(this.accountGpName.text.trim() == "") {
        this.swtalert.error(messageEmptyName);
      }  else if(Number(this.generalTab.ordinalNumericInput.text) <= 0) {
        this.swtalert.error(messageOrder);
      }
      else if(this.emptyCombo(this.generalTab.quickCategoryCombo, this.generalTab.defaultCategoryCombo) ) {
        this.swtalert.error(messageCategory);
      } else if((this.spreadingTab.spreadProfileCombo.selectedItem != undefined && this.spreadingTab.spreadProfileCombo.selectedLabel != undefined ) && !this.cutOffTab.eodBeginTimeInput.text ) {
        this.swtalert.error(messageEOD);
      } else if(!this.verifyProcessPointsTime()) {

      } else{
        if(this.spreadingTab.spreadProfileCombo.selectedItem == undefined || this.spreadingTab.spreadProfileCombo.selectedValue == ""  ) {
          this.swtalert.question(messageNoSpread, null, Alert.YES | Alert.NO, null , this.confirmSaveWithoutSpread.bind(this), Alert.NO);
        }
        else if(this.generalTab.rightGrid.dataProvider.length == 0) {
          this.swtalert.warning(messageZeroAccount, null, Alert.YES | Alert.NO, null , this.confirmSave.bind(this), Alert.NO);
        }  else {
          this.checkRequiredForSave();
        }

      }

    }
    catch (error) {
      console.log('error in save', error);
      this.swtalert.error(SwtUtil.getPredictMessage('alert.errorSavingActGrp', null));
      // log the error in ERROR LOG
    }
  }
  confirmSave(event) : void {
    if(event.detail ==  Alert.YES) {
      this.checkRequiredForSave();
    }
    else {

    }

  }
  confirmSaveWithoutSpread(event) : void {
    let messageZeroAccount = SwtUtil.getPredictMessage('alert.zeroAccountsInclude', null);
    if(event.detail ==  Alert.YES) {
      if(this.generalTab.rightGrid.dataProvider.length == 0) {
        this.swtalert.warning(messageZeroAccount, null, Alert.YES | Alert.NO, null , this.confirmSave.bind(this), Alert.NO);
      }  else
        this.checkRequiredForSave();
    }
    else {

    }

  }

  checkRequiredForSave() {

    // if(this.fourEyesRequired)     {
    //   this.win =  SwtPopUpManager.createPopUp(this, FourEyesProcess, {
    //     title: SwtUtil.getPredictMessage("label.fourEyes", null),
    //   });

    //   this.win.enableResize = false;
    //   this.win.width = '510';
    //   this.win.height = '215';
    //   this.win.showControls = true;
    //   this.win.isModal = true;
    //   this.win.onClose.subscribe(() => {
    //     if (this.win.getChild().result) {
    //       if(this.win.getChild().result.login == "SUCCESS") {
    //         this.sendParamsToSave();
    //       }
    //     }

    //   });
    //   this.win.display();
    // }else {
      this.sendParamsToSave();
    // }

  }
  sendParamsToSave(): void {
    try {
      this.actionMethod = 'method=save';
      this.actionPath = 'accountGroupsPCM.do?';
      this.logicUpdate.cbResult = (event) => {
        this.logicUpdateResult(event);
      };
      /* if(this.cutOffTab.cutOffGrid.dataProvider.length == 0 && !this.cutOffTab.eodBeginTimeInput.text) {
         eodTime = this.cutOffTab.cobTimeInput.text;
       } else if (this.cutOffTab.cutOffGrid.dataProvider.length > 0 && !this.cutOffTab.eodBeginTimeInput.text) {
         eodTime = this.minCutOff;
       } else {
         eodTime = this.cutOffTab.eodBeginTimeInput.text;
         }*/
      this.requestParams['accountGroupId'] = this.accountGpId.text;
      this.requestParams['description'] = this.accountGpName.text;
      this.requestParams['ordinal'] = (this.generalTab.ordinalNumericInput.text) ? Number(this.generalTab.ordinalNumericInput.text) : "";
      this.requestParams['ccyCode'] = (this.currencyComboBox.selectedItem) ? this.currencyComboBox.selectedLabel : "";
      this.requestParams['kickOffTime'] = this.cutOffTab.kickoffTimeInput.text;
      this.requestParams['eodTime'] = this.cutOffTab.eodBeginTimeInput.text;
      this.requestParams['cobTime'] = this.cutOffTab.cobTimeInput.text;
      this.requestParams['targetPayementMethod'] = this.spreadingTab.targetCalculationCombo.selectedItem.value;
      this.requestParams['spreadProfileId'] = (this.spreadingTab.spreadProfileCombo.selectedItem) ? this.spreadingTab.spreadProfileCombo.selectedLabel : "";
      this.requestParams['defaultCategoryId'] = (this.generalTab.defaultCategoryCombo.selectedItem) ? this.generalTab.defaultCategoryCombo.selectedLabel : "";
      this.requestParams['quickCategoryId'] = (this.generalTab.quickCategoryCombo.selectedItem) ? this.generalTab.quickCategoryCombo.selectedLabel : "";
      if (this.operationsList.toString() != "<operationsList/>") {
        this.requestParams['xmlData'] = this.operationsList.toString();
      }
      this.requestParams['screenName'] = this.screenName;
      this.requestParams['method'] = 'save';
      // this.logicUpdate.cbFault = this.inputDataFault.bind(this);
      this.logicUpdate.encodeURL = false;
      this.logicUpdate.url = this.baseURL + this.actionPath + this.actionMethod;
      this.logicUpdate.send(this.requestParams);
      this.operationsList = new XML( "<operationsList/>");
    } catch(e) {
      this.swtalert.error(SwtUtil.getPredictMessage('alert.errorSavingActGrp', null));
    }
  }
  /**
   * logicUpdateResult
   *
   * @param event: ResultEvent
   *
   * Method to get result of the group rules
   */
  logicUpdateResult(event): void {
    try {
      let message = SwtUtil.getPredictMessage('alert.contactAdminForActGrp', null);
      if (this.logicUpdate.isBusy()) {
        this.logicUpdate.cbStop();
      } else {
        const JsonResponse = event;
        const JsonResult: JSONReader = new JSONReader();
        JsonResult.setInputJSON(JsonResponse);
        if (JsonResult.getRequestReplyMessage() == "ERROR_SAVE") {
          this.swtalert.error(message);
        } else {
          this.updateData();

          if(StringUtils.isTrue(this.requireAuthorisation)){
              this.swtalert.show(SwtUtil.getPredictMessage("maintenanceevent.details.alert.actionneedauthorisation", null), "Warning", Alert.OK, null, this.closeWindow.bind(this));
            }else {
              if(this.maintEventId){
                  if(window.opener && window.opener.opener && window.opener.opener.instanceElement) {
                    window.opener.opener.instanceElement.updateData();
                  }
                  if(window.opener && window.opener.instanceElement) {
                    window.opener.close();
                  }
              
                  window.close();
              }else {
                window.close();
              }
          }
        }
      }
    } catch (e) {
      // log the error in ERROR LOG
    }
  }


  /**
   * Update the data, this is called whenever a fresh of the data is required.
   **/
  public updateData(): void {
    try {
      window.opener.instanceElement.updateDataFromChild();
      //this.parentDocument.updateDataFromChild();
    } catch (e) {
      console.log('error updateData', e)
    }
  }
  emptyCombo(combo1, combo2): boolean {
    if(combo1.selectedItem == undefined && combo2.selectedItem == undefined) {
      return true;
    } else if(combo1.selectedItem == undefined && combo2.selectedItem != undefined &&  combo2.selectedItem.value == "") {
      return true;
    } else if( combo1.selectedItem != undefined && combo1.selectedItem.value == "" && combo2.selectedItem == undefined) {
      return true;
    } else return (combo1.selectedItem != undefined  && combo2.selectedItem != undefined  &&combo1.selectedItem.value == "" &&  combo2.selectedItem.value == "" );

  }

  validateTime(textInput): any {
    let bornInfTime;
    let cobAsTime;
    let cutOffAsTime;
    let messageCutOffRangeTime = SwtUtil.getPredictMessage('alert.cutOffRangeTime', null);
    let messageCutOffSupKickOff = SwtUtil.getPredictMessage('alert.cutOffSupKickOff', null);
    if (this.cutOffTab.eodBeginTimeInput.text)
      bornInfTime = moment(this.cutOffTab.eodBeginTimeInput.text, 'HH:mm');
    else if(!this.cutOffTab.eodBeginTimeInput.text && this.cutOffTab.kickoffTimeInput.text) {
      bornInfTime = moment(this.cutOffTab.kickoffTimeInput.text, 'HH:mm');
    } else {
      bornInfTime = moment("00:00", 'HH:mm');
    }


    if (this.cutOffTab.cobTimeInput.text)
      cobAsTime = moment(this.cutOffTab.cobTimeInput.text, 'HH:mm');
    if (textInput)
      cutOffAsTime = moment(textInput, 'HH:mm');

    if (cobAsTime !== undefined && cutOffAsTime !== undefined && (cobAsTime.isBefore(cutOffAsTime) || this.cutOffTab.cobTimeInput.text == textInput) ) {
      this.swtalert.error(messageCutOffRangeTime);
      return false;
    } else if (bornInfTime !== undefined && cutOffAsTime !== undefined && (cutOffAsTime.isBefore(bornInfTime)) ) {
      if(this.cutOffTab.eodBeginTimeInput.text) {
        this.swtalert.error(messageCutOffRangeTime);
      } else {
        this.swtalert.error(messageCutOffSupKickOff);
      }
      return false;
    } else
      return true;
  }
  validateProcessTime(textInput): any {
    let eodAsTime;
    let kickOffTime;
    let processTime;
    let processsupKickOff = SwtUtil.getPredictMessage('alert.processsupKickOff', null);
    let eodSupProcess = SwtUtil.getPredictMessage('alert.eodSupProcess', null);
    Alert.yesLabel =SwtUtil.getPredictMessage('button.save', null);
    Alert.noLabel = SwtUtil.getPredictMessage('button.cancel', null);
    if (this.cutOffTab.eodBeginTimeInput.text)
      eodAsTime = moment(this.cutOffTab.eodBeginTimeInput.text, 'HH:mm');

    if (this.cutOffTab.kickoffTimeInput.text)
      kickOffTime = moment(this.cutOffTab.kickoffTimeInput.text, 'HH:mm');
    else
      kickOffTime = moment("00:00", 'HH:mm');


    if (textInput)
      processTime = moment(textInput, 'HH:mm');

    if (kickOffTime !== undefined && processTime !== undefined && processTime.isBefore(kickOffTime) ) {
      this.swtalert.error(processsupKickOff);
      return false;
    } else if (eodAsTime !== undefined && processTime !== undefined && ( eodAsTime.isBefore(processTime) || this.cutOffTab.eodBeginTimeInput.text == textInput   )) {
      this.swtalert.error(eodSupProcess);
      return false;
    } else
      return true;
  }



  acceptEventEventHandler(): void {
    // const message = SwtUtil.getPredictMessage('alert.columndelete', null);
    const message = SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoaccept', null);
    this.swtalert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.acceptStatusHandler.bind(this), null);
}

rejectEventEventHandler(): void {
  // const message = SwtUtil.getPredictMessage('alert.columndelete', null);
  const message = SwtUtil.getPredictMessage('maintenanceevent.details.alert.areyousuretoreject', null);
  this.swtalert.confirm(message,SwtUtil.getPredictMessage('button.confirm', null),Alert.YES | Alert.NO, null, this.rejectStatusHandler.bind(this), null);
}

acceptStatusHandler(closeEvent): void {
    if (closeEvent.detail == Alert.YES) {
      if (window.opener && window.opener.instanceElement) {
        this.changeStatusHandler('A');
      }
    }
  }
  rejectStatusHandler(closeEvent): void {
    if (closeEvent.detail == Alert.YES) {
      if (window.opener && window.opener.instanceElement) {
        this.changeStatusHandler('R');
      }
    }
  }

  changeStatusHandler(action) {
    let errorLocation = 0;
    try {
      this.actionPath = "maintenanceEvent.do?";
      this.actionMethod = 'method=updateMaintenanceEventStatus';
      errorLocation = 50;
      this.requestParams = [];
      this.requestParams['menuAccessId'] = this.parentMenuAccess;
      errorLocation = 60;
      this.requestParams['maintEventId'] =  this.maintEventId;
      errorLocation = 70;
      this.requestParams['action'] =  action;
  
      this.inputData.cbResult = (event) => {
        this.updateMaintenanceEventStatusResult(event);
      };
      this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
      errorLocation = 100;
      this.inputData.send(this.requestParams);
    } catch (error) {
  
      SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "onLoad", errorLocation);
    }
  
  
  }


updateMaintenanceEventStatusResult(event):void {
  let errorLocation = 0;
  try {
  // Checks the inputData and stops the communication
  if (this.inputData.isBusy()) {
    this.inputData.cbStop();
  } else {
    this.jsonReader.setInputJSON(this.lastRecievedJSON);
    errorLocation = 10;
    //this.dataExport.enabled = true;
    if (this.jsonReader.getRequestReplyStatus()) {
      this.swtalert.show(SwtUtil.getPredictMessage("maintenanceevent.details.alert.actionperfermored", null), "Warning", Alert.OK, null, this.closeWindow.bind(this));
      
    } else {
      if (this.lastRecievedJSON.hasOwnProperty("request_reply")) {
        this.swtalert.error(this.jsonReader.getRequestReplyMessage() + "\n" + this.jsonReader.getRequestReplyLocation(), "Error");
        }

      }
    }

  } catch (error) {
    // log the error in ERROR LOG
    SwtUtil.logError(error, SwtUtil.PREDICT_MODULE_ID, 'AcctCcyPeriodMaintLog.ts', "cellClickEventHandler", errorLocation);
  }
    
}


closeWindow(event) {
  if(event.detail == Alert.OK) {
    //refresh parent
      if(this.maintEventId){
        if(window.opener && window.opener.opener && window.opener.opener.instanceElement) {
          window.opener.opener.instanceElement.updateData();
        }
        if(window.opener && window.opener.instanceElement) {
          window.opener.close();
        }
    
        window.close();
    }else {
      window.close();
    }
  }
}


}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: AccountGroupsAdd }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);
//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [AccountGroupsAdd],
  entryComponents: []
})
export class AccountGroupsAddModule {}
