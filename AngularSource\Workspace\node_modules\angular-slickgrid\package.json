{"_from": "angular-slickgrid@2.24.0", "_id": "angular-slickgrid@2.24.0", "_inBundle": false, "_integrity": "sha512-fJVBPlfSkwAlR0jJuJtl4ARMObB0T3zUkugMfgalbetS49VON1fHSaQTQaWYOuPqb/RpGiD4KcJ3AblE2sAuIA==", "_location": "/angular-slickgrid", "_phantomChildren": {"@types/sizzle": "2.3.9", "jquery-ui": "1.14.1"}, "_requested": {"type": "version", "registry": true, "raw": "angular-slickgrid@2.24.0", "name": "angular-slickgrid", "escapedName": "angular-slickgrid", "rawSpec": "2.24.0", "saveSpec": null, "fetchSpec": "2.24.0"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/angular-slickgrid/-/angular-slickgrid-2.24.0.tgz", "_shasum": "33024e067c86bb3d4a8976f77403afdf837408f5", "_spec": "angular-slickgrid@2.24.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "<PERSON><PERSON><PERSON>."}, "bugs": {"url": "https://github.com/ghiscoding/angular-slickgrid/issues"}, "bundleDependencies": false, "dependencies": {"@types/jquery": "^3.5.1", "dompurify": "^2.2.2", "excel-builder-webpacker": "^1.0.6", "flatpickr": "^4.6.6", "font-awesome": "^4.7.0", "jquery": "^3.5.1", "jquery-ui-dist": "^1.12.1", "lodash.isequal": "^4.5.0", "moment-mini": "^2.24.0", "rxjs": "^6.3.3", "slickgrid": "^2.4.32", "text-encoding-utf-8": "^1.0.2", "tslib": "^1.9.0"}, "deprecated": false, "description": "Slickgrid components made available in Angular", "devDependencies": {"@angular-builders/jest": "^7.4.4", "@angular-devkit/build-angular": "~0.803.29", "@angular/animations": "^8.2.14", "@angular/cli": "^8.3.29", "@angular/common": "^8.2.14", "@angular/compiler": "^8.2.14", "@angular/compiler-cli": "^8.2.14", "@angular/core": "^8.2.14", "@angular/forms": "^8.2.14", "@angular/language-service": "^8.2.14", "@angular/platform-browser": "^8.2.14", "@angular/platform-browser-dynamic": "^8.2.14", "@angular/router": "^8.2.14", "@ng-select/ng-select": "^2.15.3", "@ngx-translate/core": "^11.0.1", "@ngx-translate/http-loader": "^4.0.0", "@types/dompurify": "^2.0.1", "@types/flatpickr": "^3.1.2", "@types/jest": "^24.0.25", "@types/moment": "^2.13.0", "@types/node": "^13.13.4", "@types/text-encoding-utf-8": "^1.0.1", "babel-jest": "^24.9.0", "bootstrap": "3.4.1", "codecov": "^3.8.1", "codelyzer": "^5.0.1", "conventional-changelog": "^3.1.24", "copyfiles": "^2.4.1", "core-js": "^2.6.12", "cross-env": "^7.0.3", "custom-event-polyfill": "^1.0.7", "del": "^6.0.0", "del-cli": "^3.0.1", "gulp": "^4.0.2", "gulp-bump": "^3.2.0", "gulp-sass": "^4.1.0", "gulp-yuidoc": "^0.1.2", "http-server": "^0.12.3", "jest": "^24.9.0", "jest-extended": "^0.11.5", "jest-junit": "^6.4.0", "jest-preset-angular": "^6.0.1", "ng-packagr": "^5.7.1", "ngx-bootstrap": "^4.3.0", "node-sass": "4.14.1", "npm-run-all": "^4.1.5", "postcss-cli": "^7.1.1", "require-dir": "^1.2.0", "rimraf": "^3.0.2", "run-sequence": "^2.2.1", "standard-version": "^9.0.0", "ts-node": "~3.3.0", "tsickle": "^0.37.0", "tslib": "^2.0.1", "tslint": "^5.20.1", "typescript": "3.5.3", "uglify-js": "^3.12.1", "vinyl-paths": "^2.1.0", "yargs": "^15.4.1", "zone.js": "~0.9.1"}, "es2015": "fesm2015/angular-slickgrid.js", "esm2015": "esm2015/angular-slickgrid.js", "esm5": "esm5/angular-slickgrid.js", "fesm2015": "fesm2015/angular-slickgrid.js", "fesm5": "fesm5/angular-slickgrid.js", "homepage": "https://github.com/ghiscoding/angular-slickgrid#readme", "keywords": ["angular", "plugin", "datagrid", "datatable", "slickgrid"], "license": "MIT", "main": "bundles/angular-slickgrid.umd.js", "metadata": "angular-slickgrid.metadata.json", "module": "fesm5/angular-slickgrid.js", "name": "angular-slickgrid", "peerDependencies": {"@ngx-translate/core": ">=11.0.1", "@ngx-translate/http-loader": ">=4.0.0"}, "private": false, "repository": {"type": "git", "url": "git+ssh://**************/ghiscoding/angular-slickgrid.git"}, "sideEffects": false, "typings": "angular-slickgrid.d.ts", "version": "2.24.0"}