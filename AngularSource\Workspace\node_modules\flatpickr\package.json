{"_from": "flatpickr@^4.6.6", "_id": "flatpickr@4.6.13", "_inBundle": false, "_integrity": "sha512-97PMG/aywoYpB4IvbvUJi0RQi8vearvU0oov1WW3k0WZPBMrTQVqekSX5CjSG/M4Q3i6A/0FKXC7RyAoAUUSPw==", "_location": "/flatpickr", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "flatpickr@^4.6.6", "name": "flatpickr", "escapedName": "flatpickr", "rawSpec": "^4.6.6", "saveSpec": null, "fetchSpec": "^4.6.6"}, "_requiredBy": ["/angular-slickgrid"], "_resolved": "https://registry.npmjs.org/flatpickr/-/flatpickr-4.6.13.tgz", "_shasum": "8a029548187fd6e0d670908471e43abe9ad18d94", "_spec": "flatpickr@^4.6.6", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\angular-slickgrid", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "browserslist": ["ie >= 9", "last 2 versions", "safari >= 7"], "bugs": {"url": "https://github.com/chmln/flatpickr/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A lightweight, powerful javascript datetime picker", "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-proposal-object-rest-spread": "^7.12.1", "@babel/plugin-proposal-optional-catch-binding": "^7.12.1", "@babel/preset-env": "^7.12.11", "@types/acorn": "^4.0.5", "@types/fs-extra": "^9.0.5", "@types/glob": "7.1.3", "@types/jest": "^27.4.1", "@types/jquery": "^3.5.5", "@types/node": "^14.14.14", "@types/stylus": "^0.48.33", "autoprefixer-stylus": "latest", "babel-plugin-transform-object-rest-spread": "^6.26.0", "chokidar": "^3.4.3", "coveralls": "^3.1.0", "fs-extra": "^9.0.1", "glob": "^7.1.6", "jest": "^27.5.1", "npm-run-all": ">=4.1.5", "prettier": "^2.2.1", "rimraf": "^3.0.2", "rollup": "^2.35.1", "rollup-plugin-babel": "^4.4.0", "rollup-plugin-livereload": "^2.0.0", "rollup-plugin-serve": "1.1.0", "rollup-plugin-typescript": "^1.0.1", "stylus": "latest", "terser": "^5.5.1", "ts-jest": "^27.1.3", "ts-node": "^9.1.1", "tslib": "^2.0.3", "typescript": "^4.1.3"}, "homepage": "https://flatpickr.js.org", "keywords": ["javascript", "datetimepicker", "calendar", "date", "time", "picker", "lightweight"], "license": "MIT", "main": "dist/flatpickr.js", "module": "dist/esm/index.js", "name": "flatpickr", "repository": {"type": "git", "url": "git+https://github.com/chmln/flatpickr.git"}, "scripts": {"build": "run-s build:pre build:build build:esm build:types build:post", "build:build": "ts-node --transpile-only build.ts", "build:esm": "tsc -p tsconfig.esm.json", "build:post": "cp src/typings.d.ts dist/typings.d.ts", "build:pre": "<PERSON><PERSON><PERSON> dist", "build:types": "tsc -p tsconfig.declarations.json", "coveralls": "npm run test:unit -- --coverage && cat ./coverage/lcov.info | ./node_modules/coveralls/bin/coveralls.js", "fmt": "prettier --ignore-path .gitignore --trailing-comma es5 --write \"**/*.ts\"", "fmt:check": "prettier --ignore-path .gitignore --trailing-comma es5 --check \"**/*.ts\"", "start": "npm run build:build -- --dev", "test": "run-s test:typecheck test:unit", "test:typecheck": "tsc --noEmit --incremental", "test:unit": "jest --config config/jest.json"}, "style": "dist/flatpickr.css", "types": "./dist/typings.d.ts", "version": "4.6.13"}