(window.webpackJsonp=window.webpackJsonp||[]).push([[7],{TeoJ:function(t,e,i){"use strict";i.r(e);var n=i("CcnG"),a=i("mrSG"),o=i("447K"),s=i("+RGP"),r=i("8IZa"),l=i("qRIX"),c=i("L7sK"),u=i("wd/R"),d=i.n(u),h=i("ZYCi"),b=function(t){function e(e,i,n){var a=t.call(this,i,e)||this;return a.commonService=e,a.element=i,a.router=n,a.jsonReader=new o.L,a.lastOrdinal=null,a.spreadIdFilterGrid=null,a.cutOffArray=[],a.processTimesArray=[],a.inputData=new o.G(a.commonService),a.inputDataAcct=new o.G(a.commonService),a.logicUpdate=new o.G(a.commonService),a.baseURL=o.Wb.getBaseURL(),a.actionMethod="",a.actionPath="",a.requestParams=[],a.screenName=null,a.title=null,a.ordinalFromJson=null,a.operationsList=new o.hc("<operationsList/>"),a.isAccocuntDuplicated=!1,a.firstLoad=!0,a.isFromMaintenanceEvent=!1,a.isFromMaintenanceEventAmendMode=!1,a.maintEventId=null,a.parentMenuAccess=null,a.authOthers=null,a.canAmendFacility=!1,a.requireAuthorisation=!0,a.swtalert=new o.bb(e),a}return a.d(e,t),e.prototype.ngOnInit=function(){if(this.acceptButton.label=o.Wb.getPredictMessage("maintenanceevent.details.button.accept.label",null),this.acceptButton.toolTip=o.Wb.getPredictMessage("maintenanceevent.details.button.accept.tooltip",null),this.rejectButton.label=o.Wb.getPredictMessage("maintenanceevent.details.button.reject.label",null),this.rejectButton.toolTip=o.Wb.getPredictMessage("maintenanceevent.details.button.reject.tooltip",null),window.opener.instanceElement){var t=window.opener.instanceElement.getParams();this.screenName=t[0],t[1]&&(this.accountGroupId=t[1]),t.length>2&&t[3]&&(this.isFromMaintenanceEvent=!0,"I"==t[3]&&(this.isFromMaintenanceEventAmendMode=!0),t.length>2&&t[3],this.maintEventId=t[2],t.length>3&&t[4]&&(this.parentMenuAccess=t[4]),t.length>4&&t[5]&&(this.authOthers=t[5]),t.length>5&&t[6]&&(this.canAmendFacility=o.Z.isTrue(t[6])))}this.accountGpIdLabel.text=o.Wb.getPredictMessage("acctGroupsMaintenanceDetails.label.accountGrpId",null),this.accountGpId.toolTip=o.Wb.getPredictMessage("acctGroupsMaintenanceDetails.tooltip.accountGrpId",null),this.accountGrpNameLabel.text=o.Wb.getPredictMessage("acctGroupsMaintenanceDetails.label.accountGrpName",null),this.accountGpName.toolTip=o.Wb.getPredictMessage("acctGroupsMaintenanceDetails.tooltip.accountGrpName",null),this.currencyLabel.text=o.Wb.getPredictMessage("acctGroupsMaintenanceDetails.label.currency",null),this.currencyComboBox.toolTip=o.Wb.getPredictMessage("acctGroupsMaintenanceDetails.tooltip.currency",null),this.saveButton.label=o.Wb.getPredictMessage("button.save",null),this.cancelButton.label=o.Wb.getPredictMessage("button.cancel",null),this.amendButton.label=o.Wb.getPredictMessage("maintenanceevent.details.button.amend.label",null),this.amendButton.toolTip=o.Wb.getPredictMessage("maintenanceevent.details.button.amend.tooltip",null),this.cancelAmendButton.label=o.Wb.getPredictMessage("button.cancel",null),this.cancelAmendButton.toolTip=o.Wb.getPredictMessage("tooltip.CancelChanges",null),this.closeButton.label=o.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=o.Wb.getPredictMessage("tooltip.entityMonitor.close",null)},e.prototype.destoyAllTooltips=function(){$(".ui-tooltip").each(function(t){$(this).remove()})},e.prototype.amendEventHandler=function(){var t=this;this.destoyAllTooltips(),window.opener.instanceElement.setViewOrAmendSubScreenFromChild("change");this.router.url;this.router.navigateByUrl("/",{skipLocationChange:!0}).then(function(){t.router.navigateByUrl("/AccountGroupDetail")})},e.prototype.cancelAmendEventHandler=function(){var t=this;this.destoyAllTooltips(),window.opener.instanceElement.setViewOrAmendSubScreenFromChild("view");this.router.url;this.router.navigateByUrl("/",{skipLocationChange:!0}).then(function(){t.router.navigateByUrl("/AccountGroupDetail")})},e.prototype.dynamicCreation=function(){this.general=this.aggAccountNavigator.addChild(o.Xb),this.cutOffs=this.aggAccountNavigator.addChild(o.Xb),this.spreading=this.aggAccountNavigator.addChild(o.Xb),this.liquidity=this.aggAccountNavigator.addChild(o.Xb),this.general.label=this.general.id=o.Wb.getPredictMessage("tab.general",null),this.cutOffs.label=this.cutOffs.id=o.Wb.getPredictMessage("tab.cutOff",null),this.spreading.label=this.spreading.id=o.Wb.getPredictMessage("tab.spreading",null),this.liquidity.label=this.liquidity.id=o.Wb.getPredictMessage("tab.liquidity",null),this.generalTab=this.general.addChild(s.a),this.cutOffTab=this.cutOffs.addChild(r.a),this.spreadingTab=this.spreading.addChild(l.a),this.liquidityTab=this.liquidity.addChild(c.a),this.generalTab.rightGrid=this.generalTab.rightCanvas.addChild(o.hb),this.generalTab.leftGrid=this.generalTab.leftCanvas.addChild(o.hb),this.cutOffTab.cutOffGrid=this.cutOffTab.cutOffCanvas.addChild(o.hb),this.spreadingTab.spreadGrid=this.spreadingTab.spreadCanvas.addChild(o.hb),this.liquidityTab.reserveGrid=this.liquidityTab.reserveCanvas.addChild(o.hb),this.general.id="general",this.cutOffs.id="cutOff",this.spreading.id="spreading",this.liquidity.id="liquidity",this.generalTab.parentDocument=this,this.cutOffTab.parentDocument=this.spreadingTab.parentDocument=this.liquidityTab.parentDocument=this,"add"==this.screenName?(this.accountGpId.required=!0,this.accountGpName.required=!0,this.currencyComboBox.required=!0,this.cutOffTab.cobTimeInput.required=!0):(this.currencyComboBox.enabled=!1,this.accountGpId.enabled=!1,"view"==this.screenName?this.enableDisableComponent(!1):"change"==this.screenName&&this.enableDisableComponent(!0)),this.generalTab.leftGrid.allowMultipleSelection=!0,this.generalTab.rightGrid.allowMultipleSelection=!0,this.generalTab.height="100%",this.cutOffTab.height="100%",this.spreadingTab.height="100%",this.liquidityTab.height="100%"},e.prototype.onLoad=function(){var t=this;try{this.requestParams=[],this.maintEventId&&o.Z.isTrue(this.authOthers)&&"change"!=this.screenName&&(this.rejectButton.visible=!0,this.acceptButton.visible=!0),this.maintEventId&&"view"==this.screenName?(this.amendButton.visible=!0,this.amendButton.includeInLayout=!0,this.saveButton.visible=!1,this.saveButton.includeInLayout=!1,o.Z.isTrue(this.canAmendFacility)?(this.amendButton.enabled=!0,this.rejectButton.visible=!0):this.amendButton.enabled=!1):this.maintEventId&&(this.saveButton.visible=!0,this.saveButton.includeInLayout=!0,this.cancelAmendButton.visible=!0,this.cancelAmendButton.includeInLayout=!0,this.closeButton.visible=!0,this.closeButton.includeInLayout=!0,this.cancelButton.visible=!1,this.cancelButton.includeInLayout=!1,this.amendButton.visible=!1,this.amendButton.includeInLayout=!1),this.actionMethod="method=add",this.actionPath="accountGroupsPCM.do?",this.inputData.cbResult=function(e){t.inputDataResult(e)},this.requestParams.accountGroupId=this.accountGroupId,this.requestParams.screenName=this.screenName,this.requestParams.method="add",this.requestParams.isFromMaintenanceEventAmendMode=this.isFromMaintenanceEventAmendMode,this.requestParams.isFromMaintenanceEvent=this.isFromMaintenanceEvent,this.requestParams.maintEventId=this.maintEventId,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.dynamicCreation()}catch(e){}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0)},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1)},e.prototype.inputDataResult=function(t){var e=this;try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&this.jsonReader.getRequestReplyStatus()&&(this.jsonReader.isDataBuilding()||(this.currencyPattern=this.jsonReader.getSingletons().currencyPattern,this.requireAuthorisation=this.jsonReader.getScreenAttributes().requireAuthorisation,setTimeout(function(){e.fillComboData(),e.fillAccountsGridData(),e.fillReserveGrid(),e.fillCutOffGrid(),e.fillAccountsInGroup(),e.fillSpreadGridData(),e.generalTab.enableMoveButtons()},0),setTimeout(function(){e.swtModule.subscribeSpy([e.cutOffTab.cutOffGrid,e.liquidityTab.reserveGrid]),e.swtModule.onSpyChange.subscribe(function(){e.reserveList=e.liquidityTab.reserveGrid.changes,e.cutOffList=e.cutOffTab.cutOffGrid.changes})},0))))}catch(i){console.log("error:   ",i)}},e.prototype.fillComboData=function(){"add"==this.screenName?this.currencyComboBox.setComboDataAndForceSelected(this.jsonReader.getSelects(),!1,""):(this.currencyComboBox.setComboData(this.jsonReader.getSelects(),!1),this.ccyLabel.text=this.currencyComboBox.selectedItem.value),this.generalTab.defaultCategoryCombo.setComboData(this.jsonReader.getSelects(),!0),this.generalTab.quickCategoryCombo.setComboData(this.jsonReader.getSelects(),!0),this.spreadingTab.targetCalculationCombo.setComboData(this.jsonReader.getSelects(),!0),"add"!==this.screenName&&(this.accountGpId.text=this.jsonReader.getSingletons().AccGpId,this.accountGpName.text=this.jsonReader.getSingletons().accountGpName,this.generalTab.ordinalNumericInput.text=this.jsonReader.getSingletons().ordinal,this.lastOrdinal=this.jsonReader.getSingletons().ordinal,this.currencyComboBox.selectedLabel=this.jsonReader.getSingletons().ccy,this.ccyLabel.text=this.currencyComboBox.selectedValue,this.generalTab.defaultCategoryCombo.selectedLabel=this.jsonReader.getSingletons().defaultCategory,this.jsonReader.getSingletons().quickCategory&&(this.generalTab.quickCategoryCombo.selectedLabel=this.jsonReader.getSingletons().quickCategory),this.cutOffTab.kickoffTimeInput.text=this.jsonReader.getSingletons().kickoffTime,this.cutOffTab.cobTimeInput.text=this.jsonReader.getSingletons().COBTime,this.cutOffTab.eodBeginTimeInput.text=this.jsonReader.getSingletons().EODTime,this.spreadingTab.spreadProfileCombo.selectedLabel=this.jsonReader.getSingletons().spreadId,this.spreadIdFilterGrid=this.jsonReader.getSingletons().spreadId,this.spreadingTab.targetCalculationCombo.selectedLabel="O"==this.jsonReader.getSingletons().targetCalculation?"Outstanding":"All",this.isFromMaintenanceEvent&&(this.accountGpId.text=this.jsonReader.getSingletons().AccGpId,null!=this.jsonReader.getSingletons().accountGpName_oldValue&&this.jsonReader.getSingletons().accountGpName_oldValue!=this.jsonReader.getSingletons().accountGpName&&(this.accountGpName.toolTipPreviousValue=this.jsonReader.getSingletons().accountGpName_oldValue),null!=this.jsonReader.getSingletons().ordinal_oldValue&&this.jsonReader.getSingletons().ordinal_oldValue!=this.jsonReader.getSingletons().ordinal&&(this.generalTab.ordinalNumericInput.toolTipPreviousValue=this.jsonReader.getSingletons().ordinal_oldValue),null!=this.jsonReader.getSingletons().defaultCategory_oldValue&&this.jsonReader.getSingletons().defaultCategory_oldValue!=this.jsonReader.getSingletons().defaultCategory&&(this.generalTab.defaultCategoryCombo.toolTipPreviousValue=this.jsonReader.getSingletons().defaultCategory_oldValue),null!=this.jsonReader.getSingletons().quickCategory_oldValue&&this.jsonReader.getSingletons().quickCategory_oldValue!=this.jsonReader.getSingletons().quickCategory&&(this.generalTab.quickCategoryCombo.toolTipPreviousValue=this.jsonReader.getSingletons().quickCategory_oldValue),null!=this.jsonReader.getSingletons().kickoffTime_oldValue&&this.jsonReader.getSingletons().kickoffTime_oldValue!=this.jsonReader.getSingletons().kickoffTime&&(this.cutOffTab.kickoffTimeInput.toolTipPreviousValue=this.jsonReader.getSingletons().kickoffTime_oldValue),null!=this.jsonReader.getSingletons().COBTime_oldValue&&this.jsonReader.getSingletons().COBTime_oldValue!=this.jsonReader.getSingletons().COBTime&&(this.cutOffTab.cobTimeInput.toolTipPreviousValue=this.jsonReader.getSingletons().COBTime_oldValue),null!=this.jsonReader.getSingletons().EODTime_oldValue&&this.jsonReader.getSingletons().EODTime_oldValue!=this.jsonReader.getSingletons().EODTime&&(this.cutOffTab.eodBeginTimeInput.toolTipPreviousValue=this.jsonReader.getSingletons().EODTime_oldValue),null!=this.jsonReader.getSingletons().targetCalculation_oldValue&&this.jsonReader.getSingletons().targetCalculation_oldValue!=this.jsonReader.getSingletons().targetCalculation&&(this.spreadingTab.targetCalculationCombo.toolTipPreviousValue="O"==this.jsonReader.getSingletons().targetCalculation_oldValue?"Outstanding":"All"),null!=this.jsonReader.getSingletons().spreadId_oldValue&&this.jsonReader.getSingletons().spreadId_oldValue!=this.jsonReader.getSingletons().spreadId&&(this.spreadingTab.spreadProfileCombo.toolTipPreviousValue=this.jsonReader.getSingletons().spreadId_oldValue)))},e.prototype.fillAccountsGridData=function(){var t=this;try{this.currencyComboBox.selectedItem&&(this.actionMethod="method=getAvailableAccounts",this.actionPath="accountGroupsPCM.do?",this.inputDataAcct.cbResult=function(e){t.inputDataResultAccountGrid(e)},this.requestParams.currencyCode=this.currencyComboBox.selectedItem?this.currencyComboBox.selectedLabel:null,this.requestParams.method="getAvailableAccounts",this.requestParams.isFromMaintenanceEventAmendMode=this.isFromMaintenanceEventAmendMode,this.requestParams.isFromMaintenanceEvent=this.isFromMaintenanceEvent,this.requestParams.accountGroup=this.accountGroupId,this.requestParams.maintEventId=this.maintEventId,this.inputDataAcct.encodeURL=!1,this.inputDataAcct.url=this.baseURL+this.actionPath+this.actionMethod,this.inputDataAcct.send(this.requestParams))}catch(e){console.log("eeeeeeeeee****************",e)}},e.prototype.inputDataResultAccountGrid=function(t){var e=0;this.inputDataAcct.isBusy()?this.inputDataAcct.cbStop():(this.lastRecievedAccountNotInGroupJSON=t,this.jsonReader.setInputJSON(this.lastRecievedAccountNotInGroupJSON),this.jsonReader.getRequestReplyStatus()&&(this.jsonReader.isDataBuilding()||(this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.AccountsNotGrpGrid.rows.row&&(e=this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.AccountsNotGrpGrid.rows.size,this.generalTab.leftGrid.gridData={row:this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.AccountsNotGrpGrid.rows.row,size:e}),this.generalTab.leftGrid.allowMultipleSelection=!0,this.generalTab.enableMoveButtons(),this.generalTab.countIDNotInGroup.text="("+e+")","add"==this.screenName?(this.spreadingTab.spreadProfileCombo.dataProvider=[],this.spreadingTab.spreadNameTextInput.text="",this.spreadIdFilterGrid=null,this.fillSpreadGridData(),this.spreadingTab.spreadProfileCombo.setComboDataAndForceSelected(this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.selects,!0,"")):(this.spreadingTab.spreadProfileCombo.setComboData(this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.selects,!0),this.spreadingTab.spreadProfileCombo.selectedLabel=this.spreadIdFilterGrid,this.spreadingTab.spreadNameTextInput.text=this.spreadingTab.spreadProfileCombo.selectedValue),this.listOfOrder=this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.singletons.ordinaList,"add"==this.screenName&&(this.generalTab.ordinalNumericInput.text=this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.singletons.maxOrdinal,this.ordinalFromJson=this.lastRecievedAccountNotInGroupJSON.PCAccountMaintenance.singletons.maxOrdinal))))},e.prototype.fillAccountsInGroup=function(){try{this.generalTab.leftGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.acountsGrpGrid.metadata),this.generalTab.rightGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.acountsGrpGrid.metadata),"add"!==this.screenName&&(this.generalTab.rightGrid.gridData=this.lastRecievedJSON.PCAccountMaintenance.acountsGrpGrid.rows),this.generalTab.countIDInGroup.text="("+this.generalTab.rightGrid.dataProvider.length+")",this.generalTab.rightGrid.allowMultipleSelection=!0,this.generalTab.leftGrid.enableColumn("ilmCB",!1),this.generalTab.rightGrid.enableColumn("ilmCB",!1)}catch(t){console.log("error fillAccountsInGroup",t)}},e.prototype.fillSpreadGridData=function(){var t=[];if(this.spreadingTab.spreadGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.spreadGrid.metadata),this.spreadRows=this.lastRecievedJSON.PCAccountMaintenance.spreadGrid.rows,"add"!=this.screenName){for(var e=0;e<this.spreadRows.size;e++)this.spreadRows.row[e].spreadId.content==this.spreadIdFilterGrid&&t.push(this.spreadRows.row[e]);this.spreadingTab.spreadGrid.gridData={row:t,size:t.length}}},e.prototype.fillReserveGrid=function(){this.liquidityTab.reserveGrid.currencyFormat=this.currencyPattern,this.liquidityTab.reserveGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.reserveGrid.metadata),this.liquidityTab.reserveGrid.enableColumn("useCL",!1),"add"!==this.screenName&&(this.liquidityTab.reserveGrid.gridData=this.lastRecievedJSON.PCAccountMaintenance.reserveGrid.rows)},e.prototype.fillCutOffGrid=function(){this.cutOffTab.cutOffGrid.CustomGrid(this.lastRecievedJSON.PCAccountMaintenance.cutOffGrid.metadata),"add"!==this.screenName&&(this.cutOffTab.cutOffGrid.gridData=this.lastRecievedJSON.PCAccountMaintenance.cutOffGrid.rows)},e.prototype.changeComboCurrency=function(){var t=o.Wb.getPredictMessage("alert.changeCurrency",null);this.ccyLabel.text=this.currencyComboBox.selectedItem?this.currencyComboBox.selectedItem.value:"",this.generalTab.filterTextLeft.text="",this.generalTab.rightTextOfInput="",this.fillAccountsGridData(),this.fillAccountsInGroup(),this.firstLoad||this.swtalert.warning(t),this.firstLoad=!1},e.prototype.inputDataFault=function(){this.swtalert.error("generic_exception")},e.prototype.keyDownEventHandler=function(t){try{var e=Object(o.ic.getFocus()).name;t.keyCode==o.N.ENTER&&("saveButton"==e?this.save():"cancelButton"==e&&this.popupClosed())}catch(i){}},e.prototype.popupClosed=function(){window.close()},e.prototype.enableDisableComponent=function(t){this.accountGpName.enabled=t,this.saveButton.enabled=t,this.generalTab.ordinalNumericInput.enabled=t,this.generalTab.buttonsContainer.enabled=t,this.generalTab.defaultCategoryCombo.enabled=t,this.generalTab.quickCategoryCombo.enabled=t},e.prototype.validateAccountIdHandler=function(){var t=[];t=this.existingAccountId();var e=o.Wb.getPredictMessage("alert.accountIDExist",null);""!=t[0]&&-1!==t.indexOf(this.accountGpId.text)&&"add"==this.screenName?(this.swtalert.confirm(e,"Alert",o.c.OK,null),this.isAccocuntDuplicated=!0):this.isAccocuntDuplicated=!1},e.prototype.validateAccountNameHandler=function(){var t=o.Wb.getPredictMessage("alert.validateAccountName",null);/^[A-Za-z\d !"#$%&'()*+,\-.\/:;<=>?@[\\\]^_`{|}~]*$/.test(this.accountGpName.text)||this.swtalert.confirm(t,"Alert",o.c.OK,null)},e.prototype.existingAccountId=function(){return this.lastRecievedJSON.PCAccountMaintenance.singletons.groupIdList.replace("[","").replace("]","").replace(/ /g,"").split(",").map(String)},e.prototype.xmlDataReserve=function(){var t=[];if(this.reserveList)for(var e=0;e<this.reserveList.getValues().length;e++)(t=[]).TIME=this.reserveList.getValues()[e].crud_data.time,t.RESERVE=this.reserveList.getValues()[e].crud_data.reserve,t.CREDIT_LINE=this.reserveList.getValues()[e].crud_data.useCL,"I"==this.reserveList.getValues()[e].crud_operation&&this.operationsList.appendChild(o.Z.getKVTypeTabAsXML(t,"PC_RESERVE","I","M")),"U"==this.reserveList.getValues()[e].crud_operation.substring(0,1)&&(t.ID_RESERVE=this.reserveList.getValues()[e].crud_data.idReserve,this.operationsList.appendChild(o.Z.getKVTypeTabAsXML(t,"PC_RESERVE","U","M"))),"D"==this.reserveList.getValues()[e].crud_operation.substring(0,1)&&(t.ID_RESERVE=this.reserveList.getValues()[e].crud_data.idReserve,this.operationsList.appendChild(o.Z.getKVTypeTabAsXML(t,"PC_RESERVE","D","M")))},e.prototype.xmlDataCutOff=function(){var t=[];if(this.cutOffList)for(var e=0;e<this.cutOffList.getValues().length;e++)(t=[]).CUTOFF_TIME=this.cutOffList.getValues()[e].crud_data.cutOffTime,t.ORDINAL=this.cutOffList.getValues()[e].crud_data.testOrder,t.LOG_TEXT=this.cutOffList.getValues()[e].crud_data.logText,t.RULE_TYPE="C",t.RULE_TEXT=this.cutOffList.getValues()[e].crud_data.ruleText,t.TAB_CONDITION=this.cutOffList.getValues()[e].crud_data.ruleCondition,t.RULE_QUERY=this.cutOffList.getValues()[e].crud_data.ruleQuery,"I"==this.cutOffList.getValues()[e].crud_operation?this.operationsList.appendChild(o.Z.getKVTypeTabAsXML(t,"PC_CUTOFF","I","M")):"U"==this.cutOffList.getValues()[e].crud_operation.substring(0,1)?(t.ID_CUTOFF=this.cutOffList.getValues()[e].crud_data.cutoffRuleId,this.operationsList.appendChild(o.Z.getKVTypeTabAsXML(t,"PC_CUTOFF","U","M"))):"D"==this.cutOffList.getValues()[e].crud_operation.substring(0,1)&&(t.ID_CUTOFF=this.cutOffList.getValues()[e].crud_data.cutoffRuleId,this.operationsList.appendChild(o.Z.getKVTypeTabAsXML(t,"PC_CUTOFF","D","M")))},e.prototype.xmlAccountGroup=function(){var t=[],e=[],i=[];if(this.generalTab.rightGrid.dataProvider&&this.generalTab.rightGrid.originalDataprovider){for(var n=0;n<this.generalTab.rightGrid.dataProvider.length;n++)i.push(this.generalTab.rightGrid.dataProvider[n].account_id_name+this.generalTab.rightGrid.dataProvider[n].entity);for(n=0;n<this.generalTab.rightGrid.originalDataprovider.length;n++)e.push(this.generalTab.rightGrid.originalDataprovider[n].account_id_name+this.generalTab.rightGrid.originalDataprovider[n].entity);if("add"==this.screenName)for(n=0;n<this.generalTab.rightGrid.dataProvider.length;n++)(t=[]).ACC_GROUP_ID=this.accountGpId.text,t.ACCOUNT_ID=this.generalTab.rightGrid.dataProvider[n].account_id_name.split(" -")[0],t.ENTITY=this.generalTab.rightGrid.dataProvider[n].entity,this.operationsList.appendChild(o.Z.getKVTypeTabAsXML(t,"PC_ACCOUNTS_IN_GROUP","I","M"));else{for(n=0;n<this.generalTab.rightGrid.dataProvider.length;n++)-1==e.indexOf(this.generalTab.rightGrid.dataProvider[n].account_id_name+this.generalTab.rightGrid.dataProvider[n].entity)&&((t=[]).ACC_GROUP_ID=this.accountGpId.text,t.ACCOUNT_ID=this.generalTab.rightGrid.dataProvider[n].account_id_name.split(" -")[0],t.ENTITY=this.generalTab.rightGrid.dataProvider[n].entity,this.operationsList.appendChild(o.Z.getKVTypeTabAsXML(t,"PC_ACCOUNTS_IN_GROUP","I","M")));for(n=0;n<this.generalTab.rightGrid.originalDataprovider.length;n++)-1==i.indexOf(this.generalTab.rightGrid.originalDataprovider[n].account_id_name+this.generalTab.rightGrid.originalDataprovider[n].entity)&&((t=[]).ACC_GROUP_ID=this.accountGpId.text,t.ACCOUNT_ID=this.generalTab.rightGrid.originalDataprovider[n].account_id_name.split(" -")[0],t.ENTITY=this.generalTab.rightGrid.originalDataprovider[n].entity,this.operationsList.appendChild(o.Z.getKVTypeTabAsXML(t,"PC_ACCOUNTS_IN_GROUP","D","M")))}}},e.prototype.verifyCutOff=function(){try{if(this.cutOffArray=[],this.cutOffTab.cutOffGrid.dataProvider.length>0){for(var t=0;t<this.cutOffTab.cutOffGrid.dataProvider.length;t++)this.cutOffArray.push(this.cutOffTab.cutOffGrid.dataProvider[t].cutOffTime);for(t=0;t<this.cutOffArray.length;t++)if(!this.validateTime(this.cutOffArray[t]))return!1}return!0}catch(e){}},e.prototype.verifyProcessPointsTime=function(){try{if(this.processTimesArray=[],this.spreadingTab.spreadGrid.dataProvider.length>0){for(var t=0;t<this.spreadingTab.spreadGrid.dataProvider.length;t++)this.processTimesArray.push(this.spreadingTab.spreadGrid.dataProvider[t].time);for(t=0;t<this.processTimesArray.length;t++)if(!this.validateProcessTime(this.processTimesArray[t]))return!1}return!0}catch(e){}},e.prototype.save=function(){o.c.yesLabel=o.Wb.getPredictMessage("button.save",null),o.c.noLabel=o.Wb.getPredictMessage("button.cancel",null);var t=o.Wb.getPredictMessage("alert.mandatoryField",null),e=o.Wb.getPredictMessage("alert.accountIDExist",null),i=o.Wb.getPredictMessage("alert.accountNameWithoutSpaces",null),n=o.Wb.getPredictMessage("alert.zeroAccountsInclude",null),a=o.Wb.getPredictMessage("alert.noSpread",null),s=o.Wb.getPredictMessage("alert.eodMandatory",null),r=o.Wb.getPredictMessage("alert.orderHigherThanZero",null),l=o.Wb.getPredictMessage("alert.categoryMandatory",null),c=[];c="add"==this.screenName?this.existingAccountId():[];try{this.operationsList=new o.hc("<operationsList/>"),this.xmlDataReserve(),this.xmlDataCutOff(),this.xmlAccountGroup(),this.verifyCutOff()&&(!(this.cutOffTab.cobTimeInput.text&&this.cutOffTab.eodBeginTimeInput.text&&this.cutOffTab.kickoffTimeInput.text)||this.cutOffTab.validateTime(this.cutOffTab.cobTimeInput)&&this.cutOffTab.validateTime(this.cutOffTab.eodBeginTimeInput)&&this.cutOffTab.validateTime(this.cutOffTab.kickoffTimeInput))&&(-1!==c.indexOf(this.accountGpId.text)&&"add"==this.screenName?this.swtalert.error(e):this.accountGpId.text&&this.accountGpName.text&&this.cutOffTab.cobTimeInput.text&&null!=this.currencyComboBox.selectedItem?""==this.accountGpName.text.trim()?this.swtalert.error(i):Number(this.generalTab.ordinalNumericInput.text)<=0?this.swtalert.error(r):this.emptyCombo(this.generalTab.quickCategoryCombo,this.generalTab.defaultCategoryCombo)?this.swtalert.error(l):null==this.spreadingTab.spreadProfileCombo.selectedItem||null==this.spreadingTab.spreadProfileCombo.selectedLabel||this.cutOffTab.eodBeginTimeInput.text?this.verifyProcessPointsTime()&&(null==this.spreadingTab.spreadProfileCombo.selectedItem||""==this.spreadingTab.spreadProfileCombo.selectedValue?this.swtalert.question(a,null,o.c.YES|o.c.NO,null,this.confirmSaveWithoutSpread.bind(this),o.c.NO):0==this.generalTab.rightGrid.dataProvider.length?this.swtalert.warning(n,null,o.c.YES|o.c.NO,null,this.confirmSave.bind(this),o.c.NO):this.checkRequiredForSave()):this.swtalert.error(s):this.swtalert.error(t))}catch(u){console.log("error in save",u),this.swtalert.error(o.Wb.getPredictMessage("alert.errorSavingActGrp",null))}},e.prototype.confirmSave=function(t){t.detail==o.c.YES&&this.checkRequiredForSave()},e.prototype.confirmSaveWithoutSpread=function(t){var e=o.Wb.getPredictMessage("alert.zeroAccountsInclude",null);t.detail==o.c.YES&&(0==this.generalTab.rightGrid.dataProvider.length?this.swtalert.warning(e,null,o.c.YES|o.c.NO,null,this.confirmSave.bind(this),o.c.NO):this.checkRequiredForSave())},e.prototype.checkRequiredForSave=function(){this.sendParamsToSave()},e.prototype.sendParamsToSave=function(){var t=this;try{this.actionMethod="method=save",this.actionPath="accountGroupsPCM.do?",this.logicUpdate.cbResult=function(e){t.logicUpdateResult(e)},this.requestParams.accountGroupId=this.accountGpId.text,this.requestParams.description=this.accountGpName.text,this.requestParams.ordinal=this.generalTab.ordinalNumericInput.text?Number(this.generalTab.ordinalNumericInput.text):"",this.requestParams.ccyCode=this.currencyComboBox.selectedItem?this.currencyComboBox.selectedLabel:"",this.requestParams.kickOffTime=this.cutOffTab.kickoffTimeInput.text,this.requestParams.eodTime=this.cutOffTab.eodBeginTimeInput.text,this.requestParams.cobTime=this.cutOffTab.cobTimeInput.text,this.requestParams.targetPayementMethod=this.spreadingTab.targetCalculationCombo.selectedItem.value,this.requestParams.spreadProfileId=this.spreadingTab.spreadProfileCombo.selectedItem?this.spreadingTab.spreadProfileCombo.selectedLabel:"",this.requestParams.defaultCategoryId=this.generalTab.defaultCategoryCombo.selectedItem?this.generalTab.defaultCategoryCombo.selectedLabel:"",this.requestParams.quickCategoryId=this.generalTab.quickCategoryCombo.selectedItem?this.generalTab.quickCategoryCombo.selectedLabel:"","<operationsList/>"!=this.operationsList.toString()&&(this.requestParams.xmlData=this.operationsList.toString()),this.requestParams.screenName=this.screenName,this.requestParams.method="save",this.logicUpdate.encodeURL=!1,this.logicUpdate.url=this.baseURL+this.actionPath+this.actionMethod,this.logicUpdate.send(this.requestParams),this.operationsList=new o.hc("<operationsList/>")}catch(e){this.swtalert.error(o.Wb.getPredictMessage("alert.errorSavingActGrp",null))}},e.prototype.logicUpdateResult=function(t){try{var e=o.Wb.getPredictMessage("alert.contactAdminForActGrp",null);if(this.logicUpdate.isBusy())this.logicUpdate.cbStop();else{var i=t,n=new o.L;n.setInputJSON(i),"ERROR_SAVE"==n.getRequestReplyMessage()?this.swtalert.error(e):(this.updateData(),o.Z.isTrue(this.requireAuthorisation)?this.swtalert.show(o.Wb.getPredictMessage("maintenanceevent.details.alert.actionneedauthorisation",null),"Warning",o.c.OK,null,this.closeWindow.bind(this)):this.maintEventId?(window.opener&&window.opener.opener&&window.opener.opener.instanceElement&&window.opener.opener.instanceElement.updateData(),window.opener&&window.opener.instanceElement&&window.opener.close(),window.close()):window.close())}}catch(a){}},e.prototype.updateData=function(){try{window.opener.instanceElement.updateDataFromChild()}catch(t){console.log("error updateData",t)}},e.prototype.emptyCombo=function(t,e){return null==t.selectedItem&&null==e.selectedItem||(null==t.selectedItem&&null!=e.selectedItem&&""==e.selectedItem.value||(null!=t.selectedItem&&""==t.selectedItem.value&&null==e.selectedItem||null!=t.selectedItem&&null!=e.selectedItem&&""==t.selectedItem.value&&""==e.selectedItem.value))},e.prototype.validateTime=function(t){var e,i,n,a=o.Wb.getPredictMessage("alert.cutOffRangeTime",null),s=o.Wb.getPredictMessage("alert.cutOffSupKickOff",null);return e=this.cutOffTab.eodBeginTimeInput.text?d()(this.cutOffTab.eodBeginTimeInput.text,"HH:mm"):!this.cutOffTab.eodBeginTimeInput.text&&this.cutOffTab.kickoffTimeInput.text?d()(this.cutOffTab.kickoffTimeInput.text,"HH:mm"):d()("00:00","HH:mm"),this.cutOffTab.cobTimeInput.text&&(i=d()(this.cutOffTab.cobTimeInput.text,"HH:mm")),t&&(n=d()(t,"HH:mm")),void 0===i||void 0===n||!i.isBefore(n)&&this.cutOffTab.cobTimeInput.text!=t?void 0===e||void 0===n||!n.isBefore(e)||(this.cutOffTab.eodBeginTimeInput.text?this.swtalert.error(a):this.swtalert.error(s),!1):(this.swtalert.error(a),!1)},e.prototype.validateProcessTime=function(t){var e,i,n,a=o.Wb.getPredictMessage("alert.processsupKickOff",null),s=o.Wb.getPredictMessage("alert.eodSupProcess",null);return o.c.yesLabel=o.Wb.getPredictMessage("button.save",null),o.c.noLabel=o.Wb.getPredictMessage("button.cancel",null),this.cutOffTab.eodBeginTimeInput.text&&(e=d()(this.cutOffTab.eodBeginTimeInput.text,"HH:mm")),i=this.cutOffTab.kickoffTimeInput.text?d()(this.cutOffTab.kickoffTimeInput.text,"HH:mm"):d()("00:00","HH:mm"),t&&(n=d()(t,"HH:mm")),void 0!==i&&void 0!==n&&n.isBefore(i)?(this.swtalert.error(a),!1):void 0===e||void 0===n||!e.isBefore(n)&&this.cutOffTab.eodBeginTimeInput.text!=t||(this.swtalert.error(s),!1)},e.prototype.acceptEventEventHandler=function(){var t=o.Wb.getPredictMessage("maintenanceevent.details.alert.areyousuretoaccept",null);this.swtalert.confirm(t,o.Wb.getPredictMessage("button.confirm",null),o.c.YES|o.c.NO,null,this.acceptStatusHandler.bind(this),null)},e.prototype.rejectEventEventHandler=function(){var t=o.Wb.getPredictMessage("maintenanceevent.details.alert.areyousuretoreject",null);this.swtalert.confirm(t,o.Wb.getPredictMessage("button.confirm",null),o.c.YES|o.c.NO,null,this.rejectStatusHandler.bind(this),null)},e.prototype.acceptStatusHandler=function(t){t.detail==o.c.YES&&window.opener&&window.opener.instanceElement&&this.changeStatusHandler("A")},e.prototype.rejectStatusHandler=function(t){t.detail==o.c.YES&&window.opener&&window.opener.instanceElement&&this.changeStatusHandler("R")},e.prototype.changeStatusHandler=function(t){var e=this,i=0;try{this.actionPath="maintenanceEvent.do?",this.actionMethod="method=updateMaintenanceEventStatus",i=50,this.requestParams=[],this.requestParams.menuAccessId=this.parentMenuAccess,i=60,this.requestParams.maintEventId=this.maintEventId,i=70,this.requestParams.action=t,this.inputData.cbResult=function(t){e.updateMaintenanceEventStatusResult(t)},this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,i=100,this.inputData.send(this.requestParams)}catch(n){o.Wb.logError(n,o.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaintLog.ts","onLoad",i)}},e.prototype.updateMaintenanceEventStatusResult=function(t){var e=0;try{this.inputData.isBusy()?this.inputData.cbStop():(this.jsonReader.setInputJSON(this.lastRecievedJSON),e=10,this.jsonReader.getRequestReplyStatus()?this.swtalert.show(o.Wb.getPredictMessage("maintenanceevent.details.alert.actionperfermored",null),"Warning",o.c.OK,null,this.closeWindow.bind(this)):this.lastRecievedJSON.hasOwnProperty("request_reply")&&this.swtalert.error(this.jsonReader.getRequestReplyMessage()+"\n"+this.jsonReader.getRequestReplyLocation(),"Error"))}catch(i){o.Wb.logError(i,o.Wb.PREDICT_MODULE_ID,"AcctCcyPeriodMaintLog.ts","cellClickEventHandler",e)}},e.prototype.closeWindow=function(t){t.detail==o.c.OK&&(this.maintEventId?(window.opener&&window.opener.opener&&window.opener.opener.instanceElement&&window.opener.opener.instanceElement.updateData(),window.opener&&window.opener.instanceElement&&window.opener.close(),window.close()):window.close())},e}(o.yb),g=[{path:"",component:b}],p=(h.l.forChild(g),function(){return function(){}}()),m=i("pMnS"),f=i("RChO"),v=i("t6HQ"),T=i("WFGK"),C=i("5FqG"),y=i("Ip0R"),w=i("gIcY"),R=i("t/Na"),I=i("sE5F"),O=i("OzfB"),G=i("T7CS"),P=i("S7LP"),S=i("6aHO"),A=i("WzUx"),M=i("A7o+"),D=i("zCE2"),L=i("Jg5P"),B=i("3R0m"),N=i("hhbb"),_=i("5rxC"),k=i("Fzqc"),x=i("21Lb"),E=i("hUWP"),q=i("3pJQ"),j=i("V9q+"),V=i("VDKW"),W=i("kXfT"),J=i("BGbe");i.d(e,"AccountGroupsAddModuleNgFactory",function(){return F}),i.d(e,"RenderType_AccountGroupsAdd",function(){return U}),i.d(e,"View_AccountGroupsAdd_0",function(){return Z}),i.d(e,"View_AccountGroupsAdd_Host_0",function(){return K}),i.d(e,"AccountGroupsAddNgFactory",function(){return z});var F=n.Gb(p,[],function(t){return n.Qb([n.Rb(512,n.n,n.vb,[[8,[m.a,f.a,v.a,T.a,C.Cb,C.Pb,C.r,C.rc,C.s,C.Ab,C.Bb,C.Db,C.qd,C.Hb,C.k,C.Ib,C.Nb,C.Ub,C.yb,C.Jb,C.v,C.A,C.e,C.c,C.g,C.d,C.Kb,C.f,C.ec,C.Wb,C.bc,C.ac,C.sc,C.fc,C.lc,C.jc,C.Eb,C.Fb,C.mc,C.Lb,C.nc,C.Mb,C.dc,C.Rb,C.b,C.ic,C.Yb,C.Sb,C.kc,C.y,C.Qb,C.cc,C.hc,C.pc,C.oc,C.xb,C.p,C.q,C.o,C.h,C.j,C.w,C.Zb,C.i,C.m,C.Vb,C.Ob,C.Gb,C.Xb,C.t,C.tc,C.zb,C.n,C.qc,C.a,C.z,C.rd,C.sd,C.x,C.td,C.gc,C.l,C.u,C.ud,C.Tb,z]],[3,n.n],n.J]),n.Rb(4608,y.m,y.l,[n.F,[2,y.u]]),n.Rb(4608,w.c,w.c,[]),n.Rb(4608,w.p,w.p,[]),n.Rb(4608,R.j,R.p,[y.c,n.O,R.n]),n.Rb(4608,R.q,R.q,[R.j,R.o]),n.Rb(5120,R.a,function(t){return[t,new o.tb]},[R.q]),n.Rb(4608,R.m,R.m,[]),n.Rb(6144,R.k,null,[R.m]),n.Rb(4608,R.i,R.i,[R.k]),n.Rb(6144,R.b,null,[R.i]),n.Rb(4608,R.f,R.l,[R.b,n.B]),n.Rb(4608,R.c,R.c,[R.f]),n.Rb(4608,I.c,I.c,[]),n.Rb(4608,I.g,I.b,[]),n.Rb(5120,I.i,I.j,[]),n.Rb(4608,I.h,I.h,[I.c,I.g,I.i]),n.Rb(4608,I.f,I.a,[]),n.Rb(5120,I.d,I.k,[I.h,I.f]),n.Rb(5120,n.b,function(t,e){return[O.j(t,e)]},[y.c,n.O]),n.Rb(4608,G.a,G.a,[]),n.Rb(4608,P.a,P.a,[]),n.Rb(4608,S.a,S.a,[n.n,n.L,n.B,P.a,n.g]),n.Rb(4608,A.c,A.c,[n.n,n.g,n.B]),n.Rb(4608,A.e,A.e,[A.c]),n.Rb(4608,M.l,M.l,[]),n.Rb(4608,M.h,M.g,[]),n.Rb(4608,M.c,M.f,[]),n.Rb(4608,M.j,M.d,[]),n.Rb(4608,M.b,M.a,[]),n.Rb(4608,M.k,M.k,[M.l,M.h,M.c,M.j,M.b,M.m,M.n]),n.Rb(4608,A.i,A.i,[[2,M.k]]),n.Rb(4608,A.r,A.r,[A.L,[2,M.k],A.i]),n.Rb(4608,A.t,A.t,[]),n.Rb(4608,A.w,A.w,[]),n.Rb(**********,h.l,h.l,[[2,h.r],[2,h.k]]),n.Rb(**********,y.b,y.b,[]),n.Rb(**********,w.n,w.n,[]),n.Rb(**********,w.l,w.l,[]),n.Rb(**********,D.a,D.a,[]),n.Rb(**********,L.a,L.a,[]),n.Rb(**********,w.e,w.e,[]),n.Rb(**********,B.a,B.a,[]),n.Rb(**********,M.i,M.i,[]),n.Rb(**********,A.b,A.b,[]),n.Rb(**********,R.e,R.e,[]),n.Rb(**********,R.d,R.d,[]),n.Rb(**********,I.e,I.e,[]),n.Rb(**********,N.b,N.b,[]),n.Rb(**********,_.b,_.b,[]),n.Rb(**********,O.c,O.c,[]),n.Rb(**********,k.a,k.a,[]),n.Rb(**********,x.d,x.d,[]),n.Rb(**********,E.c,E.c,[]),n.Rb(**********,q.a,q.a,[]),n.Rb(**********,j.a,j.a,[[2,O.g],n.O]),n.Rb(**********,V.b,V.b,[]),n.Rb(**********,W.a,W.a,[]),n.Rb(**********,J.b,J.b,[]),n.Rb(**********,o.Tb,o.Tb,[]),n.Rb(**********,p,p,[]),n.Rb(256,R.n,"XSRF-TOKEN",[]),n.Rb(256,R.o,"X-XSRF-TOKEN",[]),n.Rb(256,"config",{},[]),n.Rb(256,M.m,void 0,[]),n.Rb(256,M.n,void 0,[]),n.Rb(256,"popperDefaults",{},[]),n.Rb(1024,h.i,function(){return[[{path:"",component:b}]]},[])])}),H=[[""]],U=n.Hb({encapsulation:0,styles:H,data:{}});function Z(t){return n.dc(0,[n.Zb(*********,1,{_container:0}),n.Zb(*********,2,{aggAccountNavigator:0}),n.Zb(*********,3,{currencyComboBox:0}),n.Zb(*********,4,{accountGpId:0}),n.Zb(*********,5,{accountGpName:0}),n.Zb(*********,6,{ccyLabel:0}),n.Zb(*********,7,{accountGpIdLabel:0}),n.Zb(*********,8,{accountGrpNameLabel:0}),n.Zb(*********,9,{currencyLabel:0}),n.Zb(*********,10,{loadingImage:0}),n.Zb(*********,11,{saveButton:0}),n.Zb(*********,12,{cancelButton:0}),n.Zb(*********,13,{generalTab:0}),n.Zb(*********,14,{cutOffTab:0}),n.Zb(*********,15,{spreadingTab:0}),n.Zb(*********,16,{liquidityTab:0}),n.Zb(*********,17,{swtModule:0}),n.Zb(*********,18,{acceptButton:0}),n.Zb(*********,19,{rejectButton:0}),n.Zb(*********,20,{amendButton:0}),n.Zb(*********,21,{cancelAmendButton:0}),n.Zb(*********,22,{closeButton:0}),(t()(),n.Jb(22,0,null,null,56,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"],[null,"close"]],function(t,e,i){var n=!0,a=t.component;"creationComplete"===e&&(n=!1!==a.onLoad()&&n);"close"===e&&(n=!1!==a.popupClosed()&&n);return n},C.ad,C.hb)),n.Ib(23,4440064,[[17,4],["swtModule",4]],0,o.yb,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),n.Jb(24,0,null,0,54,"VBox",[["height","95%"],["id","vbox1"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,C.od,C.vb)),n.Ib(25,4440064,null,0,o.ec,[n.r,o.i,n.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingBottom:[4,"paddingBottom"],paddingLeft:[5,"paddingLeft"],paddingRight:[6,"paddingRight"]},null),(t()(),n.Jb(26,0,null,0,30,"SwtCanvas",[["height","92%"],["width","100%"]],null,null,null,C.Nc,C.U)),n.Ib(27,4440064,null,0,o.db,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(28,0,null,0,28,"VBox",[["height","100%"],["width","100%"]],null,null,null,C.od,C.vb)),n.Ib(29,4440064,null,0,o.ec,[n.r,o.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(30,0,null,0,23,"VBox",[["height","12%"],["width","100%"]],null,null,null,C.od,C.vb)),n.Ib(31,4440064,null,0,o.ec,[n.r,o.i,n.T],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(32,0,null,0,13,"HBox",[["paddingLeft","10"],["width","100%"]],null,null,null,C.Dc,C.K)),n.Ib(33,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(34,0,null,0,5,"HBox",[["width","40%"]],null,null,null,C.Dc,C.K)),n.Ib(35,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(36,0,null,0,1,"SwtLabel",[["width","150"]],null,null,null,C.Yc,C.fb)),n.Ib(37,4440064,[[7,4],["accountGpIdLabel",4]],0,o.vb,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(38,0,null,0,1,"SwtTextInput",[["id","accountGpId"],["maxChars","20"],["restrict","a-zA-Z0-9\\-_"],["width","200"]],null,[[null,"focusOut"]],function(t,e,i){var n=!0,a=t.component;"focusOut"===e&&(n=!1!==a.validateAccountIdHandler()&&n);return n},C.kd,C.sb)),n.Ib(39,4440064,[[4,4],["accountGpId",4]],0,o.Rb,[n.r,o.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],width:[3,"width"]},{onFocusOut_:"focusOut"}),(t()(),n.Jb(40,0,null,0,5,"HBox",[["width","60%"]],null,null,null,C.Dc,C.K)),n.Ib(41,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(42,0,null,0,1,"SwtLabel",[["width","150"]],null,null,null,C.Yc,C.fb)),n.Ib(43,4440064,[[8,4],["accountGrpNameLabel",4]],0,o.vb,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(44,0,null,0,1,"SwtTextInput",[["id","accountGpName"],["maxChars","50"],["restrict","A-Za-z0-9\\d_ !\\\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^`{|}~"],["width","350"]],null,[[null,"focusOut"]],function(t,e,i){var n=!0,a=t.component;"focusOut"===e&&(n=!1!==a.validateAccountNameHandler()&&n);return n},C.kd,C.sb)),n.Ib(45,4440064,[[5,4],["accountGpName",4]],0,o.Rb,[n.r,o.i],{maxChars:[0,"maxChars"],restrict:[1,"restrict"],id:[2,"id"],width:[3,"width"]},{onFocusOut_:"focusOut"}),(t()(),n.Jb(46,0,null,0,7,"HBox",[["paddingLeft","10"],["width","100%"]],null,null,null,C.Dc,C.K)),n.Ib(47,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),n.Jb(48,0,null,0,1,"SwtLabel",[["width","150"]],null,null,null,C.Yc,C.fb)),n.Ib(49,4440064,[[9,4],["currencyLabel",4]],0,o.vb,[n.r,o.i],{width:[0,"width"]},null),(t()(),n.Jb(50,0,null,0,1,"SwtComboBox",[["dataLabel","currencyList"],["enabled","true"],["id","currencyComboBox"],["width","200"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var a=!0,o=t.component;"window:mousewheel"===e&&(a=!1!==n.Tb(t,51).mouseWeelEventHandler(i.target)&&a);"change"===e&&(a=!1!==o.changeComboCurrency()&&a);return a},C.Pc,C.W)),n.Ib(51,4440064,[[3,4],["currencyComboBox",4]],0,o.gb,[n.r,o.i],{dataLabel:[0,"dataLabel"],width:[1,"width"],id:[2,"id"],enabled:[3,"enabled"]},{change_:"change"}),(t()(),n.Jb(52,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["text"," "]],null,null,null,C.Yc,C.fb)),n.Ib(53,4440064,[[6,4],["ccyLabel",4]],0,o.vb,[n.r,o.i],{text:[0,"text"],fontWeight:[1,"fontWeight"]},null),(t()(),n.Jb(54,0,null,0,2,"SwtTabNavigator",[["height","90%"],["width","100%"]],null,null,null,C.id,C.pb)),n.Ib(55,4440064,[[2,4],["aggAccountNavigator",4]],1,o.Ob,[n.r,o.i,n.k],{width:[0,"width"],height:[1,"height"]},null),n.Zb(*********,23,{tabChildren:1}),(t()(),n.Jb(57,0,null,0,21,"SwtCanvas",[["id","canvasContainer"],["width","100%"]],null,null,null,C.Nc,C.U)),n.Ib(58,4440064,null,0,o.db,[n.r,o.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),n.Jb(59,0,null,0,13,"HBox",[["height","100%"],["width","100%"]],null,null,null,C.Dc,C.K)),n.Ib(60,4440064,null,0,o.C,[n.r,o.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),n.Jb(61,0,null,0,1,"SwtButton",[["id","saveButton"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.save()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},C.Mc,C.T)),n.Ib(62,4440064,[[11,4],["saveButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(63,0,null,0,1,"SwtButton",[["id","amendButton"],["includeInLayout","false"],["label","Amend"],["visible","false"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.amendEventHandler()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},C.Mc,C.T)),n.Ib(64,4440064,[[20,4],["amendButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],includeInLayout:[2,"includeInLayout"],visible:[3,"visible"],label:[4,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(65,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelAmendButton"],["includeInLayout","false"],["visible","false"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.cancelAmendEventHandler()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},C.Mc,C.T)),n.Ib(66,4440064,[[21,4],["cancelAmendButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],includeInLayout:[1,"includeInLayout"],visible:[2,"visible"],buttonMode:[3,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(67,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","closeButton"],["includeInLayout","false"],["marginLeft","5"],["visible","false"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.popupClosed()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},C.Mc,C.T)),n.Ib(68,4440064,[[22,4],["closeButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],includeInLayout:[1,"includeInLayout"],visible:[2,"visible"],marginLeft:[3,"marginLeft"],buttonMode:[4,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(69,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","cancelButton"],["marginLeft","5"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.popupClosed()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},C.Mc,C.T)),n.Ib(70,4440064,[[12,4],["cancelButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],marginLeft:[1,"marginLeft"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(71,0,null,0,1,"SwtLoadingImage",[],null,null,null,C.Zc,C.gb)),n.Ib(72,114688,[[10,4],["loadingImage",4]],0,o.xb,[n.r],null,null),(t()(),n.Jb(73,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","10"]],null,null,null,C.Dc,C.K)),n.Ib(74,4440064,null,0,o.C,[n.r,o.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(t()(),n.Jb(75,0,null,0,1,"SwtButton",[["id","acceptButton"],["label","Accept"],["visible","false"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.acceptEventEventHandler()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},C.Mc,C.T)),n.Ib(76,4440064,[[18,4],["acceptButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],visible:[2,"visible"],label:[3,"label"]},{onClick_:"click",onKeyDown_:"keyDown"}),(t()(),n.Jb(77,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","rejectButton"],["label","Reject"],["visible","false"],["width","70"]],null,[[null,"click"],[null,"keyDown"]],function(t,e,i){var n=!0,a=t.component;"click"===e&&(n=!1!==a.rejectEventEventHandler()&&n);"keyDown"===e&&(n=!1!==a.keyDownEventHandler(i)&&n);return n},C.Mc,C.T)),n.Ib(78,4440064,[[19,4],["rejectButton",4]],0,o.cb,[n.r,o.i],{id:[0,"id"],width:[1,"width"],visible:[2,"visible"],label:[3,"label"],buttonMode:[4,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"})],function(t,e){t(e,23,0,"100%","100%");t(e,25,0,"vbox1","100%","95%","5","5","5","5");t(e,27,0,"100%","92%");t(e,29,0,"100%","100%");t(e,31,0,"100%","12%");t(e,33,0,"100%","10");t(e,35,0,"40%");t(e,37,0,"150");t(e,39,0,"20","a-zA-Z0-9\\-_","accountGpId","200");t(e,41,0,"60%");t(e,43,0,"150");t(e,45,0,"50","A-Za-z0-9\\d_ !\\\"#$%&'()*+,\\-.\\/:;<=>?@[\\\\\\]^`{|}~","accountGpName","350");t(e,47,0,"100%","10");t(e,49,0,"150");t(e,51,0,"currencyList","200","currencyComboBox","true");t(e,53,0," ","normal");t(e,55,0,"100%","90%");t(e,58,0,"canvasContainer","100%");t(e,60,0,"100%","100%");t(e,62,0,"saveButton");t(e,64,0,"amendButton","70","false","false","Amend");t(e,66,0,"cancelAmendButton","false","false","true");t(e,68,0,"closeButton","false","false","5","true");t(e,70,0,"cancelButton","5","true"),t(e,72,0);t(e,74,0,"right","10");t(e,76,0,"acceptButton","70","false","Accept");t(e,78,0,"rejectButton","70","false","Reject","true")},null)}function K(t){return n.dc(0,[(t()(),n.Jb(0,0,null,null,1,"app-pcaccount-groups-maintenance-add",[],null,null,null,Z,U)),n.Ib(1,4440064,null,0,b,[o.i,n.r,h.k],null,null)],function(t,e){t(e,1,0)},null)}var z=n.Fb("app-pcaccount-groups-maintenance-add",b,K,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);