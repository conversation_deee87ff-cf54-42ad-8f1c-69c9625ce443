(window.webpackJsonp=window.webpackJsonp||[]).push([[25],{hMlN:function(e,t,n){"use strict";n.r(t);var i=n("CcnG"),o=n("mrSG"),a=n("447K"),r=n("ZYCi"),l=function(e){function t(t,n){var i=e.call(this,n,t)||this;return i.commonService=t,i.element=n,i.jsonReader=new a.L,i.jsonReader2=new a.L,i.inputData=new a.G(i.commonService),i.logicUpdate=new a.G(i.commonService),i.checkAuthData=new a.G(i.commonService),i.requestParams=[],i.baseURL=a.Wb.getBaseURL(),i.actionMethod="",i.actionPath="",i.moduleName="Spread Profile Maintenance",i.versionNumber="1.00.00",i.releaseDate="11 March 2019",i.moduleURL=null,i.menuAccess=2,i.programId="",i.componentId=!1,i.helpURL=null,i.message=null,i.title=null,i.errorLocation=0,i.moduleReportURL=null,i.moduleId="",i.searchQuery="",i.searchFlag=!1,i.queryToDisplay="",i.sQuery="",i.requireAuthorisation=!0,i.facilityId=null,i.doDeleterecordAction=!1,i.swtAlert=new a.bb(t),i}return o.d(t,e),t.prototype.ngOnDestroy=function(){instanceElement=null},t.prototype.ngOnInit=function(){instanceElement=this},t.prototype.disableButtons=function(){-1==this.spreadProfilesGrid.selectedIndex&&this.disableOrEnableButtons(!1)},t.prototype.onLoad=function(){var e=this;this.spreadProfilesGrid=this.canvasGrid.addChild(a.hb),this.spreadProfilesGrid.uniqueColumn="spreadId",this.spreadProfilesGrid.onFilterChanged=this.disableButtons.bind(this),this.spreadProfilesGrid.onSortChanged=this.disableButtons.bind(this);try{this.title=a.Wb.getAMLMessages("pcpriorityMaintenanceScreen.windowtitle.help_screen"),this.message=a.Wb.getAMLMessages("pcpriorityMaintenanceScreen.message.help_message"),this.actionPath="spreadProfilesPCM.do?",this.actionMethod="method=display",this.requestParams.moduleId=this.moduleId,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.spreadProfilesGrid.onRowClick=function(t){e.checkIfMaintenanceEventExist(t)},this.addButton.label="Add",this.changeButton.label="Change",this.viewButton.label="View",this.deleteButton.label="Delete",this.closeButton.label="Close",this.addButton.setFocus()}catch(t){}},t.prototype.startOfComms=function(){try{this.loadingImage.setVisible(!0)}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","startOfComms",this.errorLocation)}},t.prototype.endOfComms=function(){try{this.loadingImage.setVisible(!1),this.spreadProfilesGrid.selectedIndex=-1}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","endOfComms",this.errorLocation)}},t.prototype.inputDataResult=function(e){try{this.inputData.isBusy()?this.inputData.cbStop():(this.lastRecievedJSON=e,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&(this.jsonReader.getRequestReplyStatus()?(this.doDeleterecordAction&&(a.Z.isTrue(this.requireAuthorisation)&&this.swtAlert.show("This action needs second user authorisation","Warning",a.c.OK),this.doDeleterecordAction=!1),this.jsonReader.isDataBuilding()||(this.currencyComboBox.setComboData(this.jsonReader.getSelects(),!1),this.ccyLabel.text=this.currencyComboBox.selectedItem.value,this.componentId=this.lastRecievedJSON.screenid,this.spreadProfilesGrid.CustomGrid(e.SpreadProfilesMaintenance.grid.metadata),this.jsonReader.getGridData().size>0?(this.spreadProfilesGrid.gridData=this.jsonReader.getGridData(),this.spreadProfilesGrid.setRowSize=this.jsonReader.getRowSize(),this.spreadProfilesGrid.doubleClickEnabled=!0):(this.spreadProfilesGrid.dataProvider=[],this.spreadProfilesGrid.selectedIndex=-1),this.menuAccess=this.jsonReader.getScreenAttributes().menuaccess,this.requireAuthorisation=this.jsonReader.getScreenAttributes().requireAuthorisation,this.facilityId=this.jsonReader.getScreenAttributes().faciltiyId,0==this.menuAccess&&(this.addButton.enabled=!0),this.disableComponents()),this.spreadProfilesGrid.selectedIndex=-1,this.prevRecievedJSON=this.lastRecievedJSON):"errors.DataIntegrityViolationExceptioninDelete"==this.jsonReader.getRequestReplyMessage()?this.swtAlert.error("Unable to delete, this spread profile is linked to an existing account group"):this.swtAlert.error(a.Wb.getCommonMessages("alert.generic_exception"))))}catch(t){console.log("error inputDataResult",t)}},t.prototype.checkIfMaintenanceEventExist=function(e){var t=this;try{if(1===this.spreadProfilesGrid.selectedIndices.length&&this.spreadProfilesGrid.selectable){this.checkAuthData.cbResult=function(e){t.checkResult(e)},this.requestParams.recordId=this.spreadProfilesGrid.dataProvider[this.spreadProfilesGrid.selectedIndex].spreadId,this.requestParams.facilityId=this.facilityId,this.checkAuthData.cbFault=this.inputDataFault.bind(this),this.checkAuthData.encodeURL=!1,this.checkAuthData.url=this.baseURL+"maintenanceEvent.do?method=checkIfMaintenenanceEventExist",this.checkAuthData.send(this.requestParams)}else this.disableOrEnableButtons(!1)}catch(n){console.log(n)}},t.prototype.checkResult=function(e){try{var t=new a.L;if(this.checkAuthData&&this.checkAuthData.isBusy())this.checkAuthData.cbStop();else if(t.setInputJSON(e),"RECOD_EXIST"==t.getRequestReplyMessage()){var n=a.Wb.getPredictMessage("maintenanceEvent.alert.cannotBeAmended",null);this.swtAlert.error(n),this.disableOrEnableButtons(!0,!0)}else this.disableOrEnableButtons(!0,!1)}catch(i){console.log("error in inputData",i)}},t.prototype.inputDataFault=function(e){try{this.swtAlert.error(e.fault.faultstring+"\n"+e.fault.faultCode+"\n"+e.fault.faultDetail)}catch(t){a.Wb.logError(t,this.moduleId,"ClassName","inputDataFault",this.errorLocation)}},t.prototype.disableOrEnableButtons=function(e,t){void 0===t&&(t=!1),e?(this.enableChangeButton(0==this.menuAccess&&!t),this.enableViewButton(this.menuAccess<2),this.enableDeleteButton(0==this.menuAccess&&!t)):(this.enableChangeButton(!1),this.enableViewButton(!1),this.enableDeleteButton(!1))},t.prototype.enableAddButton=function(e){this.addButton.enabled=e,this.addButton.buttonMode=e},t.prototype.enableChangeButton=function(e){this.changeButton.enabled=e,this.changeButton.buttonMode=e},t.prototype.enableViewButton=function(e){this.viewButton.enabled=e,this.viewButton.buttonMode=e},t.prototype.enableDeleteButton=function(e){this.deleteButton.enabled=e,this.deleteButton.buttonMode=e},t.prototype.doAddSpreadProfilesMaintenance=function(e){try{this.screenName="add",a.x.call("openChildWindow","spreadProfilesAdd",this.screenName)}catch(t){console.log("error add",t),a.Wb.logError(t,this.moduleId,"ClassName","doAddSpreadProfilesMaintenance",this.errorLocation)}},t.prototype.doChangeSpreadProfilesMaintenance=function(e){try{this.screenName="change",a.x.call("openChildWindow","spreadProfilesAdd",this.screenName)}catch(t){a.Wb.logError(t,this.moduleId,"ClassName","doChangeSpreadProfilesMaintenance",this.errorLocation)}},t.prototype.doViewSpreadProfilesMaintenance=function(e){try{this.screenName="view",a.x.call("openChildWindow","spreadProfilesView",this.screenName)}catch(t){a.Wb.logError(t,this.moduleId,"ClassName","doViewSpreadProfilesMaintenance",this.errorLocation)}},t.prototype.doDeleteSpreadProfilesMaintenance=function(e){try{a.c.yesLabel="Yes",a.c.noLabel="No";var t=a.Z.substitute("Are you sure to delete selected spread profile ID: \n"+this.spreadProfilesGrid.selectedItem.spreadId.content);this.swtAlert.confirm(t,a.Wb.getCommonMessages("alert_header.confirm"),a.c.YES|a.c.NO,null,this.spreadProfileRemoveHandler.bind(this))}catch(n){a.Wb.logError(n,this.moduleId,"ClassName","doDeleteSpreadProfilesMaintenance",this.errorLocation)}},t.prototype.spreadProfileRemoveHandler=function(e){e.detail===a.c.YES&&this.removeSpreadProfile()},t.prototype.removeSpreadProfile=function(){try{this.doDeleterecordAction=!0,this.requestParams=[],this.requestParams.selectedSpreadId=this.spreadProfilesGrid.dataProvider[this.spreadProfilesGrid.selectedIndex].spreadId,this.actionMethod="method=deleteSpreadProfile",this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams),this.spreadProfilesGrid.selectedIndex=-1}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","removeSpreadProfile",this.errorLocation)}},t.prototype.logicUpdateResult=function(e){try{var t=e,n=new a.L;n.setInputJSON(t),"SUCCESS"==n.getRequestReplyMessage()?this.updateData():this.swtAlert.error(a.Wb.getCommonMessages("alert_header.error"))}catch(i){a.Wb.logError(i,this.moduleId,"ClassName","logicUpdateResult",this.errorLocation)}},t.prototype.updateData=function(){var e=this;try{this.requestParams=[],this.doDeleterecordAction=!1,this.actionMethod="method=display",this.requestParams.programId=this.programId,this.requestParams.moduleId=this.moduleId,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.send(this.requestParams),this.spreadProfilesGrid.selectedIndex=-1}catch(t){a.Wb.logError(t,a.Wb.AML_MODULE_ID,"ClassName","updateData",this.errorLocation)}},t.prototype.doHelp=function(){try{a.x.call("help")}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","doHelp",this.errorLocation)}},t.prototype.printPage=function(){try{this.actionMethod="type=pdf",this.actionMethod=this.actionMethod+"&action=EXPORT",this.actionMethod=this.actionMethod+"&currentModuleId="+this.moduleId,this.actionMethod=this.actionMethod+"&print=PAGE",a.x.call("getReports",this.actionPath+this.actionMethod)}catch(e){a.Wb.logError(e,this.moduleId,"className","printPage",this.errorLocation)}},t.prototype.keyDownEventHandler=function(e){try{var t=Object(a.ic.getFocus()).name;e.keyCode===a.N.ENTER&&("addButton"===t?this.doAddSpreadProfilesMaintenance(e):"changeButton"===t?this.doChangeSpreadProfilesMaintenance(e):"viewButton"===t?this.doViewSpreadProfilesMaintenance(e):"closeButton"===t?this.closeCurrentTab(e):"csv"===t?this.report("csv"):"excel"===t?this.report("xls"):"pdf"===t?this.report("pdf"):"helpIcon"===t?this.doHelp():"deleteButton"===t?this.doDeleteSpreadProfilesMaintenance(e):"printButton"===t&&this.printPage())}catch(n){a.Wb.logError(n,this.moduleId,"ClassName","keyDownEventHandler",this.errorLocation)}},t.prototype.closeCurrentTab=function(e){try{this.dispose()}catch(t){a.Wb.logError(t,a.Wb.SYSTEM_MODULE_ID,"ClassName","refreshGrid",this.errorLocation)}},t.prototype.dispose=function(){try{this.spreadProfilesGrid=null,this.requestParams=null,this.inputData=null,this.jsonReader=null,this.menuAccess=null,this.lastRecievedJSON=null,this.prevRecievedJSON=null,this.searchQuery="",this.searchFlag=!1,a.x.call("close")}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","dispose",this.errorLocation)}},t.prototype.report=function(e){var t=null,n="",i=null;try{i=this.moduleId,t=""!==this.spreadProfilesGrid.filteredGridColumns?this.spreadProfilesGrid.getFilteredGridColumns():"",n=this.spreadProfilesGrid.getSortedGridColumn(),this.actionMethod="method=displayReport",this.actionMethod=this.actionMethod+"&type="+e,this.actionMethod=this.actionMethod+"&action=EXPORT",this.actionMethod=this.actionMethod+"&selectedFilter="+t,this.actionMethod=this.actionMethod+"&selectedSort="+n,this.actionMethod=this.actionMethod+"&print=ALL",a.x.call("getReports",this.actionPath+this.actionMethod)}catch(o){a.Wb.logError(o,i,"ClassName","report",this.errorLocation)}},t.prototype.disableComponents=function(){try{this.disableOrEnableButtons(!1)}catch(e){a.Wb.logError(e,this.moduleId,"ClassName","disableComponents",this.errorLocation)}},t.prototype.changeComboCurrency=function(e){var t=this;this.ccyLabel.text=this.currencyComboBox.selectedItem.value,this.actionMethod="method=display",this.doDeleterecordAction=!1,this.requestParams.currencyCode=this.currencyComboBox.selectedLabel,this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},t.prototype.getParamsFromParent=function(){var e="";return this.spreadProfilesGrid.selectedIndex>-1&&(e=this.spreadProfilesGrid.selectedItem.spreadId.content),[{screenName:this.screenName,spreadId:e}]},t}(a.yb),s=[{path:"",component:l}],d=(r.l.forChild(s),function(){return function(){}}()),c=n("pMnS"),h=n("RChO"),u=n("t6HQ"),b=n("WFGK"),p=n("5FqG"),m=n("Ip0R"),g=n("gIcY"),f=n("t/Na"),y=n("sE5F"),R=n("OzfB"),w=n("T7CS"),C=n("S7LP"),D=n("6aHO"),I=n("WzUx"),P=n("A7o+"),v=n("zCE2"),M=n("Jg5P"),B=n("3R0m"),S=n("hhbb"),k=n("5rxC"),L=n("Fzqc"),E=n("21Lb"),A=n("hUWP"),x=n("3pJQ"),N=n("V9q+"),G=n("VDKW"),O=n("kXfT"),_=n("BGbe");n.d(t,"SpreadProfilesMaintenanceModuleNgFactory",function(){return T}),n.d(t,"RenderType_SpreadProfilesMaintenance",function(){return J}),n.d(t,"View_SpreadProfilesMaintenance_0",function(){return q}),n.d(t,"View_SpreadProfilesMaintenance_Host_0",function(){return F}),n.d(t,"SpreadProfilesMaintenanceNgFactory",function(){return U});var T=i.Gb(d,[],function(e){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[c.a,h.a,u.a,b.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,U]],[3,i.n],i.J]),i.Rb(4608,m.m,m.l,[i.F,[2,m.u]]),i.Rb(4608,g.c,g.c,[]),i.Rb(4608,g.p,g.p,[]),i.Rb(4608,f.j,f.p,[m.c,i.O,f.n]),i.Rb(4608,f.q,f.q,[f.j,f.o]),i.Rb(5120,f.a,function(e){return[e,new a.tb]},[f.q]),i.Rb(4608,f.m,f.m,[]),i.Rb(6144,f.k,null,[f.m]),i.Rb(4608,f.i,f.i,[f.k]),i.Rb(6144,f.b,null,[f.i]),i.Rb(4608,f.f,f.l,[f.b,i.B]),i.Rb(4608,f.c,f.c,[f.f]),i.Rb(4608,y.c,y.c,[]),i.Rb(4608,y.g,y.b,[]),i.Rb(5120,y.i,y.j,[]),i.Rb(4608,y.h,y.h,[y.c,y.g,y.i]),i.Rb(4608,y.f,y.a,[]),i.Rb(5120,y.d,y.k,[y.h,y.f]),i.Rb(5120,i.b,function(e,t){return[R.j(e,t)]},[m.c,i.O]),i.Rb(4608,w.a,w.a,[]),i.Rb(4608,C.a,C.a,[]),i.Rb(4608,D.a,D.a,[i.n,i.L,i.B,C.a,i.g]),i.Rb(4608,I.c,I.c,[i.n,i.g,i.B]),i.Rb(4608,I.e,I.e,[I.c]),i.Rb(4608,P.l,P.l,[]),i.Rb(4608,P.h,P.g,[]),i.Rb(4608,P.c,P.f,[]),i.Rb(4608,P.j,P.d,[]),i.Rb(4608,P.b,P.a,[]),i.Rb(4608,P.k,P.k,[P.l,P.h,P.c,P.j,P.b,P.m,P.n]),i.Rb(4608,I.i,I.i,[[2,P.k]]),i.Rb(4608,I.r,I.r,[I.L,[2,P.k],I.i]),i.Rb(4608,I.t,I.t,[]),i.Rb(4608,I.w,I.w,[]),i.Rb(1073742336,r.l,r.l,[[2,r.r],[2,r.k]]),i.Rb(1073742336,m.b,m.b,[]),i.Rb(1073742336,g.n,g.n,[]),i.Rb(1073742336,g.l,g.l,[]),i.Rb(1073742336,v.a,v.a,[]),i.Rb(1073742336,M.a,M.a,[]),i.Rb(1073742336,g.e,g.e,[]),i.Rb(1073742336,B.a,B.a,[]),i.Rb(1073742336,P.i,P.i,[]),i.Rb(1073742336,I.b,I.b,[]),i.Rb(1073742336,f.e,f.e,[]),i.Rb(1073742336,f.d,f.d,[]),i.Rb(1073742336,y.e,y.e,[]),i.Rb(1073742336,S.b,S.b,[]),i.Rb(1073742336,k.b,k.b,[]),i.Rb(1073742336,R.c,R.c,[]),i.Rb(1073742336,L.a,L.a,[]),i.Rb(1073742336,E.d,E.d,[]),i.Rb(1073742336,A.c,A.c,[]),i.Rb(1073742336,x.a,x.a,[]),i.Rb(1073742336,N.a,N.a,[[2,R.g],i.O]),i.Rb(1073742336,G.b,G.b,[]),i.Rb(1073742336,O.a,O.a,[]),i.Rb(1073742336,_.b,_.b,[]),i.Rb(1073742336,a.Tb,a.Tb,[]),i.Rb(1073742336,d,d,[]),i.Rb(256,f.n,"XSRF-TOKEN",[]),i.Rb(256,f.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,P.m,void 0,[]),i.Rb(256,P.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,r.i,function(){return[[{path:"",component:l}]]},[])])}),W=[[""]],J=i.Hb({encapsulation:0,styles:W,data:{}});function q(e){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{canvasGrid:0}),i.Zb(402653184,3,{loadingImage:0}),i.Zb(402653184,4,{currencyComboBox:0}),i.Zb(402653184,5,{addButton:0}),i.Zb(402653184,6,{changeButton:0}),i.Zb(402653184,7,{viewButton:0}),i.Zb(402653184,8,{printButton:0}),i.Zb(402653184,9,{deleteButton:0}),i.Zb(402653184,10,{closeButton:0}),i.Zb(402653184,11,{csv:0}),i.Zb(402653184,12,{excel:0}),i.Zb(402653184,13,{pdf:0}),i.Zb(402653184,14,{helpIcon:0}),i.Zb(402653184,15,{ccyLabel:0}),(e()(),i.Jb(15,0,null,null,37,"SwtModule",[["height","100%"],["paddingBottom","5"],["paddingTop","10"],["width","100%"]],null,[[null,"creationComplete"]],function(e,t,n){var i=!0,o=e.component;"creationComplete"===t&&(i=!1!==o.onLoad()&&i);return i},p.ad,p.hb)),i.Ib(16,4440064,null,0,a.yb,[i.r,a.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"]},{creationComplete:"creationComplete"}),(e()(),i.Jb(17,0,null,0,35,"VBox",[["height","100%"],["paddingLeft","10"],["paddingRight","10"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(18,4440064,null,0,a.ec,[i.r,a.i,i.T],{width:[0,"width"],height:[1,"height"],paddingLeft:[2,"paddingLeft"],paddingRight:[3,"paddingRight"]},null),(e()(),i.Jb(19,0,null,0,9,"SwtCanvas",[["height","9%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(20,4440064,null,0,a.db,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),i.Jb(21,0,null,0,7,"HBox",[["height","28"],["marginTop","8"],["paddingLeft","10"]],null,null,null,p.Dc,p.K)),i.Ib(22,4440064,null,0,a.C,[i.r,a.i],{height:[0,"height"],paddingLeft:[1,"paddingLeft"],marginTop:[2,"marginTop"]},null),(e()(),i.Jb(23,0,null,0,1,"SwtLabel",[["text","Currency"],["width","115"]],null,null,null,p.Yc,p.fb)),i.Ib(24,4440064,null,0,a.vb,[i.r,a.i],{width:[0,"width"],text:[1,"text"]},null),(e()(),i.Jb(25,0,null,0,1,"SwtComboBox",[["dataLabel","currencyList"],["id","currencyComboBox"],["toolTip","Currency Tooltip"],["width","100"]],null,[[null,"change"],["window","mousewheel"]],function(e,t,n){var o=!0,a=e.component;"window:mousewheel"===t&&(o=!1!==i.Tb(e,26).mouseWeelEventHandler(n.target)&&o);"change"===t&&(o=!1!==a.changeComboCurrency(n)&&o);return o},p.Pc,p.W)),i.Ib(26,4440064,[[4,4],["currencyComboBox",4]],0,a.gb,[i.r,a.i],{dataLabel:[0,"dataLabel"],toolTip:[1,"toolTip"],width:[2,"width"],id:[3,"id"]},{change_:"change"}),(e()(),i.Jb(27,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["paddingLeft","10"],["text"," "]],null,null,null,p.Yc,p.fb)),i.Ib(28,4440064,[[15,4],["ccyLabel",4]],0,a.vb,[i.r,a.i],{paddingLeft:[0,"paddingLeft"],text:[1,"text"],fontWeight:[2,"fontWeight"]},null),(e()(),i.Jb(29,0,null,0,1,"SwtCanvas",[["height","79%"],["id","canvasGrid"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(30,4440064,[[2,4],["canvasGrid",4]],0,a.db,[i.r,a.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(e()(),i.Jb(31,0,null,0,21,"SwtCanvas",[["height","7%"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(32,4440064,null,0,a.db,[i.r,a.i],{width:[0,"width"],height:[1,"height"]},null),(e()(),i.Jb(33,0,null,0,19,"HBox",[["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(34,4440064,null,0,a.C,[i.r,a.i],{width:[0,"width"]},null),(e()(),i.Jb(35,0,null,0,11,"HBox",[["paddingLeft","5"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(36,4440064,null,0,a.C,[i.r,a.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(e()(),i.Jb(37,0,null,0,1,"SwtButton",[["enabled","false"],["id","addButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,n){var i=!0,o=e.component;"click"===t&&(i=!1!==o.doAddSpreadProfilesMaintenance(n)&&i);"keyDown"===t&&(i=!1!==o.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(38,4440064,[[5,4],["addButton",4]],0,a.cb,[i.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),i.Jb(39,0,null,0,1,"SwtButton",[["enabled","false"],["id","changeButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,n){var i=!0,o=e.component;"click"===t&&(i=!1!==o.doChangeSpreadProfilesMaintenance(n)&&i);"keyDown"===t&&(i=!1!==o.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(40,4440064,[[6,4],["changeButton",4]],0,a.cb,[i.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),i.Jb(41,0,null,0,1,"SwtButton",[["id","viewButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,n){var i=!0,o=e.component;"click"===t&&(i=!1!==o.doViewSpreadProfilesMaintenance(n)&&i);"keyDown"===t&&(i=!1!==o.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(42,4440064,[[7,4],["viewButton",4]],0,a.cb,[i.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),i.Jb(43,0,null,0,1,"SwtButton",[["enabled","false"],["id","deleteButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,n){var i=!0,o=e.component;"click"===t&&(i=!1!==o.doDeleteSpreadProfilesMaintenance(n)&&i);"keyDown"===t&&(i=!1!==o.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(44,4440064,[[9,4],["deleteButton",4]],0,a.cb,[i.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),i.Jb(45,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"],[null,"keyDown"]],function(e,t,n){var i=!0,o=e.component;"click"===t&&(i=!1!==o.closeCurrentTab(n)&&i);"keyDown"===t&&(i=!1!==o.keyDownEventHandler(n)&&i);return i},p.Mc,p.T)),i.Ib(46,4440064,[[10,4],["closeButton",4]],0,a.cb,[i.r,a.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click",onKeyDown_:"keyDown"}),(e()(),i.Jb(47,0,null,0,5,"HBox",[["horizontalAlign","right"],["paddingRight","10"]],null,null,null,p.Dc,p.K)),i.Ib(48,4440064,null,0,a.C,[i.r,a.i],{horizontalAlign:[0,"horizontalAlign"],paddingRight:[1,"paddingRight"]},null),(e()(),i.Jb(49,0,null,0,1,"SwtLoadingImage",[],null,null,null,p.Zc,p.gb)),i.Ib(50,114688,[[3,4],["loadingImage",4]],0,a.xb,[i.r],null,null),(e()(),i.Jb(51,0,null,0,1,"SwtHelpButton",[["enabled","true"],["helpFile","spread-profile"],["id","helpIcon"]],null,[[null,"click"]],function(e,t,n){var i=!0,o=e.component;"click"===t&&(i=!1!==o.doHelp()&&i);return i},p.Wc,p.db)),i.Ib(52,4440064,null,0,a.rb,[i.r,a.i],{id:[0,"id"],enabled:[1,"enabled"],buttonMode:[2,"buttonMode"],helpFile:[3,"helpFile"]},{onClick_:"click"})],function(e,t){e(t,16,0,"100%","100%","10","5");e(t,18,0,"100%","100%","10","10");e(t,20,0,"100%","9%");e(t,22,0,"28","10","8");e(t,24,0,"115","Currency");e(t,26,0,"currencyList","Currency Tooltip","100","currencyComboBox");e(t,28,0,"10"," ","normal");e(t,30,0,"canvasGrid","100%","79%");e(t,32,0,"100%","7%");e(t,34,0,"100%");e(t,36,0,"100%","5");e(t,38,0,"addButton","false",!0);e(t,40,0,"changeButton","false",!0);e(t,42,0,"viewButton",!0);e(t,44,0,"deleteButton","false",!0);e(t,46,0,"closeButton",!0);e(t,48,0,"right","10"),e(t,50,0);e(t,52,0,"helpIcon","true",!0,"spread-profile")},null)}function F(e){return i.dc(0,[(e()(),i.Jb(0,0,null,null,1,"app-spread-profiles-maintenance",[],null,null,null,q,J)),i.Ib(1,4440064,null,0,l,[a.i,i.r],null,null)],function(e,t){e(t,1,0)},null)}var U=i.Fb("app-spread-profiles-maintenance",l,F,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);