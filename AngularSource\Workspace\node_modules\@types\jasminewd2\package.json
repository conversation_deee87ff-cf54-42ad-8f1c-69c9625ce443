{"_from": "@types/jasminewd2@2.0.3", "_id": "@types/jasminewd2@2.0.3", "_inBundle": false, "_integrity": "sha512-hYDVmQZT5VA2kigd4H4bv7vl/OhlympwREUemqBdOqtrYTo5Ytm12a5W5/nGgGYdanGVxj0x/VhZ7J3hOg/YKg==", "_location": "/@types/jasminewd2", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/jasminewd2@2.0.3", "name": "@types/jasminewd2", "escapedName": "@types%2fjasminewd2", "scope": "@types", "rawSpec": "2.0.3", "saveSpec": null, "fetchSpec": "2.0.3"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/@types/jasminewd2/-/jasminewd2-2.0.3.tgz", "_shasum": "0d2886b0cbdae4c0eeba55e30792f584bf040a95", "_spec": "@types/jasminewd2@2.0.3", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/sjelin"}], "dependencies": {"@types/jasmine": "*"}, "deprecated": false, "description": "TypeScript definitions for jasminewd2", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/jasminewd2", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.1", "typesPublisherContentHash": "ac90f505d8827f2a2c0441fb68cc0ee376fcf8c4d24d2f62cfda3daffddb8e55", "version": "2.0.3"}