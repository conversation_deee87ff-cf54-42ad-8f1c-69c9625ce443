REM please change PredictWebRoot and PredictAngularWorkspace before launching this batch file
REM PredictWebRoot : is the Predict WebRoot path
REM PredictAngularWorkspace : is the Angular Predict workspace path

call npm run build_watch
call createjsinclude.bat
Xcopy /E /I /d /Y C:\GitWorkspace\angular\workspace\AngularSource\Workspace\dist\*.* C:\GitWorkspace\java-migration\src\main\webapp\angularSources /EXCLUDE:exclude.txt

xcopy "C:\GitWorkspace\angular\workspace\AngularSource\Workspace\dist\*.*" "C:\GitWorkspace\java-migration\src\main\webapp\angularSources\" /K /D /H /Y

Xcopy /E /I /d /Y "C:\GitWorkspace\angular\workspace\AngularSource\Workspace\dist\assets" "D:\1066\JAVA\WebRoot\assets"