{"_from": "@types/codemirror@0.0.72", "_id": "@types/codemirror@0.0.72", "_inBundle": false, "_integrity": "sha512-dKtEP139Jxdrog/6zW8akX3LdhJ3V8NjYNCpIHdBVVo6ZiYpShPamtQoGpVhzrUzTOu5A6NlozOSiZipCEVCow==", "_location": "/@types/codemirror", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/codemirror@0.0.72", "name": "@types/codemirror", "escapedName": "@types%2fcodemirror", "scope": "@types", "rawSpec": "0.0.72", "saveSpec": null, "fetchSpec": "0.0.72"}, "_requiredBy": ["/@ctrl/ngx-codemirror"], "_resolved": "https://registry.npmjs.org/@types/codemirror/-/codemirror-0.0.72.tgz", "_shasum": "75e95e3b488717605d2591cd80c4d2ed88ff276e", "_spec": "@types/codemirror@0.0.72", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\@ctrl\\ngx-codemirror", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/mihailik"}, {"name": "nrbernard", "url": "https://github.com/nrbernard"}, {"name": "Pr1st0n", "url": "https://github.com/Pr1st0n"}, {"name": "r<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/rileymiller"}, {"name": "todd<PERSON>", "url": "https://github.com/toddself"}], "dependencies": {"@types/tern": "*"}, "deprecated": false, "description": "TypeScript definitions for CodeMirror", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/codemirror", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.8", "types": "index", "typesPublisherContentHash": "4ce44657dfd3884be3d2cce2f997d5e619d71936686b94410d124d5b9eeede33", "version": "0.0.72"}