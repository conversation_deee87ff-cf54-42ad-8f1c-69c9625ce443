{"_from": "@types/estree@*", "_id": "@types/estree@1.0.7", "_inBundle": false, "_integrity": "sha512-w28IoSUCJpidD/TGviZwwMJckNESJZXFu7NBZ5YJ4mEUnNraUn9Pm8HSZm/jDF1pDWYKspWE7oVphigUPRakIQ==", "_location": "/@types/estree", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/estree@*", "name": "@types/estree", "escapedName": "@types%2festree", "scope": "@types", "rawSpec": "*", "saveSpec": null, "fetchSpec": "*"}, "_requiredBy": ["/@types/tern"], "_resolved": "https://registry.npmjs.org/@types/estree/-/estree-1.0.7.tgz", "_shasum": "4158d3105276773d5b7695cd4834b1722e4f37a8", "_spec": "@types/estree@*", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\@types\\tern", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/RReverser"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for estree", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/estree", "license": "MIT", "main": "", "name": "@types/estree", "nonNpm": true, "peerDependencies": {}, "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/estree"}, "scripts": {}, "typeScriptVersion": "5.0", "types": "index.d.ts", "typesPublisherContentHash": "1ab11f4e78319f80655b4ca20a073da0dc035be5f3290fb0bfa1e08a055d0c7d", "version": "1.0.7"}