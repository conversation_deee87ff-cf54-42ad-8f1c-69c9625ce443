{"_from": "@types/jasmine@2.8.8", "_id": "@types/jasmine@2.8.8", "_inBundle": false, "_integrity": "sha512-OJSUxLaxXsjjhob2DBzqzgrkLmukM3+JMpRp0r0E4HTdT1nwDCWhaswjYxazPij6uOdzHCJfNbDjmQ1/rnNbCg==", "_location": "/@types/jasmine", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/jasmine@2.8.8", "name": "@types/jasmine", "escapedName": "@types%2fjasmine", "scope": "@types", "rawSpec": "2.8.8", "saveSpec": null, "fetchSpec": "2.8.8"}, "_requiredBy": ["#DEV:/", "/@types/jasminewd2"], "_resolved": "https://registry.npmjs.org/@types/jasmine/-/jasmine-2.8.8.tgz", "_shasum": "bf53a7d193ea8b03867a38bfdb4fbb0e0bf066c9", "_spec": "@types/jasmine@2.8.8", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/b<PERSON><PERSON>kov"}, {"name": "<PERSON>", "url": "https://github.com/theodorejb"}, {"name": "<PERSON>", "url": "https://github.com/david<PERSON>sson"}, {"name": "<PERSON>", "url": "https://github.com/gmoothart"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/lukas-zech-software"}, {"name": "<PERSON>", "url": "https://github.com/Engineer2B"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for Jasmine", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped#readme", "license": "MIT", "main": "", "name": "@types/jasmine", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git"}, "scripts": {}, "typeScriptVersion": "2.1", "typesPublisherContentHash": "af1f6aa3243e31a4253ec39bad706b4ee4ea462caa090f1942ccba30a7da99b7", "version": "2.8.8"}