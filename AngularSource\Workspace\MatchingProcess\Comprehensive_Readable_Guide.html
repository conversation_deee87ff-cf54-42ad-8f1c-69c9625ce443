<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comprehensive Readable Matching Process Guide</title>
    <style>
        body {
            font-family: 'Georgia', 'Times New Roman', serif;
            line-height: 1.8;
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
            background: #fafafa;
            color: #333;
        }
        
        .document-header {
            background: linear-gradient(135deg, #2c3e50, #3498db);
            color: white;
            padding: 60px 40px;
            border-radius: 15px;
            text-align: center;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .document-title {
            font-size: 2.8em;
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .document-subtitle {
            font-size: 1.3em;
            opacity: 0.9;
            font-weight: 300;
        }
        
        .content-section {
            background: white;
            padding: 40px;
            margin-bottom: 30px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border-left: 5px solid #3498db;
        }
        
        .section-title {
            font-size: 2.2em;
            color: #2c3e50;
            margin-bottom: 30px;
            border-bottom: 3px solid #3498db;
            padding-bottom: 15px;
        }
        
        .subsection-title {
            font-size: 1.6em;
            color: #34495e;
            margin: 30px 0 20px 0;
            font-weight: 600;
        }
        
        .condition-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 25px;
            margin: 20px 0;
            border-left: 4px solid #17a2b8;
        }
        
        .condition-title {
            font-size: 1.3em;
            color: #17a2b8;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .condition-description {
            font-size: 1.1em;
            line-height: 1.7;
            color: #495057;
        }
        
        .decision-flow {
            background: #e8f4f8;
            border: 2px solid #17a2b8;
            border-radius: 10px;
            padding: 25px;
            margin: 25px 0;
        }
        
        .decision-title {
            font-size: 1.4em;
            color: #17a2b8;
            font-weight: 600;
            margin-bottom: 15px;
        }
        
        .step-list {
            counter-reset: step-counter;
            list-style: none;
            padding: 0;
        }
        
        .step-list li {
            counter-increment: step-counter;
            margin: 15px 0;
            padding: 15px 20px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            position: relative;
        }
        
        .step-list li::before {
            content: "Step " counter(step-counter);
            font-weight: bold;
            color: #28a745;
            display: block;
            margin-bottom: 8px;
        }
        
        .quality-matrix {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 25px 0;
        }
        
        .quality-card {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            border-left: 4px solid #f39c12;
        }
        
        .quality-score {
            font-size: 1.2em;
            font-weight: bold;
            color: #f39c12;
            margin-bottom: 10px;
        }
        
        .example-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #17a2b8;
        }
        
        .example-title {
            font-weight: bold;
            color: #17a2b8;
            margin-bottom: 10px;
        }
        
        .warning-box {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #dc3545;
        }
        
        .warning-title {
            font-weight: bold;
            color: #dc3545;
            margin-bottom: 10px;
        }
        
        .info-box {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #28a745;
        }
        
        .info-title {
            font-weight: bold;
            color: #28a745;
            margin-bottom: 10px;
        }
        
        .table-container {
            overflow-x: auto;
            margin: 25px 0;
        }
        
        .readable-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
        }
        
        .readable-table th {
            background: #3498db;
            color: white;
            padding: 15px;
            text-align: left;
            font-weight: 600;
        }
        
        .readable-table td {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .readable-table tr:hover {
            background: #f8f9fa;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 600;
        }
        
        .toc {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 40px;
        }
        
        .toc-title {
            font-size: 1.5em;
            color: #2c3e50;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .toc-list {
            list-style: none;
            padding: 0;
        }
        
        .toc-list li {
            margin: 10px 0;
            padding: 8px 15px;
            border-radius: 5px;
            transition: background 0.3s ease;
        }
        
        .toc-list li:hover {
            background: #e9ecef;
        }
        
        .toc-list a {
            text-decoration: none;
            color: #3498db;
            font-weight: 500;
        }
        
        .toc-list a:hover {
            color: #2980b9;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 20px 10px;
            }
            
            .document-title {
                font-size: 2.2em;
            }
            
            .section-title {
                font-size: 1.8em;
            }
            
            .quality-matrix {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="document-header">
        <h1 class="document-title">📚 Comprehensive Matching Process Guide</h1>
        <p class="document-subtitle">Complete Readable Reference with All Conditions Explained</p>
    </div>

    <div class="toc">
        <h2 class="toc-title">📋 Table of Contents</h2>
        <ul class="toc-list">
            <li><a href="#overview">1. System Overview & Purpose</a></li>
            <li><a href="#initialization">2. System Initialization Process</a></li>
            <li><a href="#position-levels">3. Position Level Processing Logic</a></li>
            <li><a href="#source-selection">4. Source Movement Selection Criteria</a></li>
            <li><a href="#target-strategies">5. Target Identification Strategies</a></li>
            <li><a href="#quality-assessment">6. Quality Assessment Framework</a></li>
            <li><a href="#decision-logic">7. Match Decision Logic</a></li>
            <li><a href="#amount-processing">8. Amount Total Processing</a></li>
            <li><a href="#finalization">9. Match Finalization Process</a></li>
            <li><a href="#exception-handling">10. Exception Handling & Recovery</a></li>
            <li><a href="#configuration">11. Configuration Parameters</a></li>
            <li><a href="#troubleshooting">12. Common Issues & Solutions</a></li>
        </ul>
    </div>

    <div class="content-section" id="overview">
        <h2 class="section-title">1. 🎯 System Overview & Purpose</h2>
        
        <p style="font-size: 1.2em; line-height: 1.8; color: #2c3e50;">
            The Matching Process is an intelligent financial reconciliation system that automatically identifies 
            and links related movements across different position levels in your trading and settlement environment. 
            Think of it as a sophisticated detective that examines financial transactions and finds their matching 
            counterparts based on multiple clues and evidence.
        </p>

        <div class="info-box">
            <div class="info-title">🎯 Primary Objectives</div>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li><strong>Automation:</strong> Reduce manual reconciliation work by automatically finding matching transactions</li>
                <li><strong>Accuracy:</strong> Use sophisticated algorithms to ensure high-quality matches</li>
                <li><strong>Efficiency:</strong> Process thousands of movements quickly and reliably</li>
                <li><strong>Compliance:</strong> Maintain complete audit trails for regulatory requirements</li>
                <li><strong>Risk Management:</strong> Identify and flag potential issues before they become problems</li>
            </ul>
        </div>

        <h3 class="subsection-title">How the System Works</h3>
        <p>
            The matching process operates like a multi-stage investigation. It starts by examining movements at the 
            highest position levels (like bank statements) and works its way down to the most detailed settlement 
            confirmations. At each level, it applies different strategies and criteria to find the best possible matches.
        </p>

        <div class="example-box">
            <div class="example-title">📝 Real-World Example</div>
            Imagine you have a bank statement showing a $1,000,000 payment received, and you need to match it 
            with your internal trade confirmations. The system will look at the amount, date, account details, 
            counterparty information, and reference numbers to find the corresponding internal transaction that 
            represents the same business event.
        </div>
    </div>

    <div class="content-section" id="initialization">
        <h2 class="section-title">2. ⚙️ System Initialization Process</h2>
        
        <p>
            Before the matching process begins, the system performs several important setup tasks to ensure 
            optimal performance and accuracy. This initialization phase is like preparing for a complex investigation 
            by gathering all necessary tools and information.
        </p>

        <h3 class="subsection-title">Configuration Loading</h3>
        <div class="decision-flow">
            <div class="decision-title">🔧 System Configuration Steps</div>
            <ol class="step-list">
                <li>
                    <strong>Entity Settings Retrieval:</strong> The system loads specific settings for your organization, 
                    including position level thresholds, processing preferences, and business rules that govern 
                    how matching should be performed.
                </li>
                <li>
                    <strong>Currency-Specific Rules:</strong> Different currencies have different settlement cycles 
                    and processing requirements. For example, EUR transactions typically settle same-day, while 
                    USD transactions may have a two-day settlement cycle.
                </li>
                <li>
                    <strong>Quality Threshold Setting:</strong> The system establishes minimum quality scores 
                    required for automatic matching at each position level. Higher position levels typically 
                    require higher quality scores.
                </li>
                <li>
                    <strong>Processing Window Definition:</strong> Time windows are established for how far ahead 
                    the system should look for potential matches, considering business days and settlement cycles.
                </li>
            </ol>
        </div>

        <div class="table-container">
            <table class="readable-table">
                <thead>
                    <tr>
                        <th>Configuration Parameter</th>
                        <th>Default Value</th>
                        <th>Purpose</th>
                        <th>Impact</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Position Level Threshold</td>
                        <td>6</td>
                        <td>Minimum position level for processing</td>
                        <td>Determines which movements are eligible for matching</td>
                    </tr>
                    <tr>
                        <td>Debug Mode</td>
                        <td>No</td>
                        <td>Enable detailed logging for troubleshooting</td>
                        <td>Provides additional information for problem diagnosis</td>
                    </tr>
                    <tr>
                        <td>Pre-advice Search Always</td>
                        <td>No</td>
                        <td>Always search pre-advice positions</td>
                        <td>Affects whether preliminary notifications are always considered</td>
                    </tr>
                    <tr>
                        <td>EUR Days Ahead</td>
                        <td>0</td>
                        <td>Processing window for EUR transactions</td>
                        <td>Same-day settlement for European transactions</td>
                    </tr>
                    <tr>
                        <td>Other Currencies Days Ahead</td>
                        <td>7</td>
                        <td>Processing window for non-EUR transactions</td>
                        <td>Allows for longer settlement cycles in other currencies</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="warning-box">
            <div class="warning-title">⚠️ Important Considerations</div>
            The initialization phase is critical for system performance. Incorrect configuration can lead to
            missed matches, poor performance, or excessive false positives. Always ensure that configuration
            parameters align with your business requirements and market practices.
        </div>
    </div>

    <div class="content-section" id="position-levels">
        <h2 class="section-title">3. 📊 Position Level Processing Logic</h2>

        <p>
            The matching process operates on a hierarchical system of position levels, processing movements
            from the highest level (external bank statements) down to the lowest level (final settlement confirmations).
            This approach ensures that the most authoritative sources are matched first, creating a solid foundation
            for subsequent matching activities.
        </p>

        <h3 class="subsection-title">Position Level Hierarchy</h3>
        <div class="quality-matrix">
            <div class="quality-card">
                <div class="quality-score">🏢 High Levels (9-6)</div>
                <strong>External Positions</strong><br>
                Bank statements, external confirmations, and third-party notifications.
                These represent the most authoritative sources as they come from external institutions.
            </div>
            <div class="quality-card">
                <div class="quality-score">🏬 Mid Levels (5-3)</div>
                <strong>Internal Positions</strong><br>
                Trade confirmations, internal system notifications, and booking confirmations.
                These represent your organization's internal view of transactions.
            </div>
            <div class="quality-card">
                <div class="quality-score">🏪 Low Levels (2-1)</div>
                <strong>Settlement Positions</strong><br>
                Final settlement confirmations and detailed transaction records.
                These represent the final, confirmed state of transactions.
            </div>
            <div class="quality-card">
                <div class="quality-score">📨 Pre-advice</div>
                <strong>Preliminary Notifications</strong><br>
                Advance notices and preliminary transaction information.
                These provide early warning of upcoming transactions.
            </div>
        </div>

        <h3 class="subsection-title">Processing Sequence Logic</h3>
        <div class="decision-flow">
            <div class="decision-title">🔄 Eleven-Stage Processing Cycle</div>
            <p>The system processes position levels in a carefully orchestrated sequence:</p>

            <ol class="step-list">
                <li>
                    <strong>Stage 1 - Highest Internal Position:</strong> Starts with the highest internal position level
                    defined in your system configuration. This establishes the baseline for all subsequent matching.
                </li>
                <li>
                    <strong>Stage 2 - High External Positions (9-8):</strong> Processes external bank statements and
                    high-level confirmations. These often represent large, summary-level transactions.
                </li>
                <li>
                    <strong>Stage 3 - Mid-High External (8-7):</strong> Handles detailed external confirmations and
                    transaction-specific notifications from external parties.
                </li>
                <li>
                    <strong>Stage 4 - Mid External (7-6):</strong> Processes standard external confirmations and
                    routine transaction notifications.
                </li>
                <li>
                    <strong>Stage 5 - Mid-Low External (6-5):</strong> Handles detailed external transaction records
                    and specific confirmation details.
                </li>
                <li>
                    <strong>Stage 6 - Low External (5-4):</strong> Processes final external confirmations and
                    settlement-related notifications.
                </li>
                <li>
                    <strong>Stage 7 - High Internal (4-3):</strong> Handles internal trade confirmations and
                    booking notifications from your trading systems.
                </li>
                <li>
                    <strong>Stage 8 - Low Internal (3-2-1):</strong> Processes detailed internal records,
                    settlement instructions, and final confirmations.
                </li>
                <li>
                    <strong>Stage 9 - Second Pass Highest:</strong> Re-examines the highest internal position
                    with additional context from previous stages.
                </li>
                <li>
                    <strong>Stage 10 - Pre-advice Processing:</strong> Handles preliminary notifications and
                    advance transaction notices.
                </li>
                <li>
                    <strong>Stage 11 - Outstanding Items:</strong> Processes any remaining unmatched items
                    from other stages with relaxed criteria.
                </li>
            </ol>
        </div>

        <div class="example-box">
            <div class="example-title">📝 Processing Example</div>
            Consider a $5M bond trade: Stage 2 might match the bank statement showing the cash movement,
            Stage 7 would match your internal trade booking, and Stage 8 would match the detailed settlement
            instructions. Each stage adds more granular detail to the complete transaction picture.
        </div>

        <div class="info-box">
            <div class="info-title">🎯 Why This Sequence Matters</div>
            This hierarchical approach ensures that authoritative external sources are matched first,
            providing a reliable foundation. Internal confirmations are then matched against this foundation,
            and finally, detailed settlement records complete the picture. This prevents conflicts and
            ensures data integrity throughout the matching process.
        </div>
    </div>

    <div class="content-section" id="source-selection">
        <h2 class="section-title">4. 🎯 Source Movement Selection Criteria</h2>

        <p>
            For each position level, the system must first identify which movements are eligible to serve as
            "sources" for matching. A source movement is essentially the starting point for finding related
            transactions. The selection criteria ensure that only appropriate movements are considered.
        </p>

        <h3 class="subsection-title">Primary Selection Conditions</h3>
        <div class="condition-block">
            <div class="condition-title">📋 Outstanding Status Requirement</div>
            <div class="condition-description">
                The movement must have a match status of "Outstanding" (indicated by the letter 'L').
                This means the transaction has not yet been successfully matched with its counterpart.
                Movements that are already matched, cancelled, or in other states are excluded from processing.
            </div>
        </div>

        <div class="condition-block">
            <div class="condition-title">🔄 Predict Status Validation</div>
            <div class="condition-description">
                The movement must have a predict status of either "External" or "Internal" (indicated by 'E' or 'I').
                This classification helps the system understand the nature of the transaction and apply appropriate
                matching strategies. External movements typically come from banks or counterparties, while internal
                movements originate from your own systems.
            </div>
        </div>

        <div class="condition-block">
            <div class="condition-title">📅 Date Range Compliance</div>
            <div class="condition-description">
                The movement's "to match date" must be less than or equal to the current processing date.
                This ensures that the system only processes movements that are ready for matching and doesn't
                attempt to match future-dated transactions prematurely.
            </div>
        </div>

        <div class="condition-block">
            <div class="condition-title">🎚️ Stage Alignment</div>
            <div class="condition-description">
                The movement's "to match stage" must equal the current processing stage. This prevents the
                system from repeatedly processing the same movement and ensures that each movement progresses
                through the stages in the correct sequence.
            </div>
        </div>

        <div class="condition-block">
            <div class="condition-title">💰 Amount Threshold</div>
            <div class="condition-description">
                The movement amount must meet or exceed the configured threshold for the current position level.
                This filter helps focus processing resources on significant transactions and can improve
                performance by excluding small, less critical movements.
            </div>
        </div>

        <div class="condition-block">
            <div class="condition-title">📊 Position Level Match</div>
            <div class="condition-description">
                The movement's position level must exactly match the current processing level. This ensures
                that movements are processed in the correct hierarchical order and prevents cross-level
                contamination of matching logic.
            </div>
        </div>

        <h3 class="subsection-title">Additional Filtering Criteria</h3>
        <div class="decision-flow">
            <div class="decision-title">🔍 Advanced Selection Logic</div>
            <ol class="step-list">
                <li>
                    <strong>Currency Matching:</strong> The movement's currency must match the current processing
                    currency context, ensuring that currency-specific rules and settlement cycles are properly applied.
                </li>
                <li>
                    <strong>Entity Validation:</strong> The movement must belong to the correct business entity
                    being processed, preventing cross-entity matching errors.
                </li>
                <li>
                    <strong>Host System Verification:</strong> The movement must originate from an authorized
                    host system, ensuring data integrity and preventing processing of invalid or test data.
                </li>
                <li>
                    <strong>Value Date Considerations:</strong> The movement's value date must fall within
                    acceptable ranges based on currency-specific settlement cycles and business day calculations.
                </li>
            </ol>
        </div>

        <div class="warning-box">
            <div class="warning-title">⚠️ Selection Impact</div>
            The source selection criteria are critical for system performance and accuracy. Too restrictive
            criteria may cause valid movements to be skipped, while too lenient criteria may result in
            inappropriate matches or performance degradation. Regular monitoring of selection effectiveness
            is essential for optimal system operation.
        </div>
    </div>

    <div class="content-section" id="target-strategies">
        <h2 class="section-title">5. 🎯 Target Identification Strategies</h2>

        <p>
            Once a source movement is selected, the system must determine the best strategy for finding potential
            matching targets. The strategy selection is based on the characteristics of the source movement,
            the current position level, and various business rules. Think of this as choosing the right
            investigation approach based on the available evidence.
        </p>

        <h3 class="subsection-title">Strategy Selection Logic</h3>
        <div class="decision-flow">
            <div class="decision-title">🧭 Target Strategy Decision Tree</div>
            <p>The system evaluates several conditions to determine the optimal target search strategy:</p>

            <ol class="step-list">
                <li>
                    <strong>Pre-advice Strategy Check:</strong> If the source position is at or below the maximum
                    internal position level AND pre-advice search is enabled, the system will search for
                    pre-advice targets. This helps match preliminary notifications with confirmed transactions.
                </li>
                <li>
                    <strong>Higher Position Strategy:</strong> If the source position is above the maximum internal
                    position level AND the supplement flag is set to "Yes", the system searches for targets
                    at higher position levels. This is useful for matching detailed records with summary-level entries.
                </li>
                <li>
                    <strong>Both Positions Strategy:</strong> For sources at or below the maximum internal position
                    level under normal conditions, the system searches both higher and lower position levels
                    to find the best matches.
                </li>
                <li>
                    <strong>Lower Positions Strategy:</strong> For sources above the maximum internal position
                    level under normal conditions, the system focuses on lower position levels to find
                    detailed matching records.
                </li>
                <li>
                    <strong>No Target Strategy:</strong> In some cases, based on business rules or data quality,
                    the system may determine that no target processing is appropriate for the current source.
                </li>
            </ol>
        </div>

        <h3 class="subsection-title">Target Search Methods</h3>
        <div class="quality-matrix">
            <div class="quality-card">
                <div class="quality-score">📄 Reference-Based Search</div>
                <strong>When to Use:</strong> Source has strong reference data<br>
                <strong>Method:</strong> Searches for targets with matching reference numbers, transaction IDs, or cross-reference keys<br>
                <strong>Advantage:</strong> High precision, low false positives
            </div>
            <div class="quality-card">
                <div class="quality-score">💰 Amount-Based Search</div>
                <strong>When to Use:</strong> Source has reliable amount information<br>
                <strong>Method:</strong> Searches for targets with matching or complementary amounts within tolerance<br>
                <strong>Advantage:</strong> Good for transactions without strong reference data
            </div>
            <div class="quality-card">
                <div class="quality-score">📨 Pre-advice Search</div>
                <strong>When to Use:</strong> Processing pre-advice positions<br>
                <strong>Method:</strong> Searches for confirmed movements that match preliminary notifications<br>
                <strong>Advantage:</strong> Enables early matching of expected transactions
            </div>
            <div class="quality-card">
                <div class="quality-score">⬆️ Higher Position Search</div>
                <strong>When to Use:</strong> Source is from lower position levels<br>
                <strong>Method:</strong> Searches for summary-level or authoritative source records<br>
                <strong>Advantage:</strong> Maintains hierarchical data integrity
            </div>
        </div>

        <h3 class="subsection-title">Force Reference Selection</h3>
        <div class="condition-block">
            <div class="condition-title">🔒 Reference-Only Matching</div>
            <div class="condition-description">
                When the "Enforce Reference Selection" flag is enabled, the system will only consider targets
                that have matching reference data. This is a more restrictive approach that prioritizes precision
                over recall. It's particularly useful in environments where reference data quality is high and
                false positives must be minimized.
            </div>
        </div>

        <div class="example-box">
            <div class="example-title">📝 Strategy Selection Example</div>
            <strong>Scenario:</strong> A bank statement entry (position level 8) for $2M with a clear reference number.<br>
            <strong>Strategy Selected:</strong> Reference-based search at lower position levels (3-5).<br>
            <strong>Reasoning:</strong> The system will look for internal trade confirmations that have the same
            reference number, ensuring a high-confidence match between the external bank record and internal booking.
        </div>

        <div class="info-box">
            <div class="info-title">🎯 Strategy Optimization</div>
            The choice of target strategy significantly impacts both performance and accuracy. Reference-based
            strategies are faster and more accurate but may miss matches when reference data is incomplete.
            Amount-based strategies are more comprehensive but require careful tolerance settings to avoid
            false positives. The system's ability to dynamically select the best strategy based on data
            characteristics is key to optimal performance.
        </div>
    </div>

    <div class="content-section" id="quality-assessment">
        <h2 class="section-title">6. 🔬 Quality Assessment Framework</h2>

        <p>
            The quality assessment framework is the heart of the matching process. It evaluates potential matches
            across multiple dimensions to determine how confident the system should be that two movements represent
            the same business transaction. This is like a forensic analysis that examines all available evidence
            to determine the likelihood of a match.
        </p>

        <h3 class="subsection-title">Quality Scoring System</h3>
        <div class="table-container">
            <table class="readable-table">
                <thead>
                    <tr>
                        <th>Quality Grade</th>
                        <th>Numeric Score</th>
                        <th>Description</th>
                        <th>Typical Scenario</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="highlight">A - Perfect</span></td>
                        <td>5</td>
                        <td>Exact match across all criteria</td>
                        <td>Same amount, same date, same reference, same parties</td>
                    </tr>
                    <tr>
                        <td><span class="highlight">B - Good</span></td>
                        <td>4</td>
                        <td>Match within acceptable tolerances</td>
                        <td>Amount within 0.01%, date within 1 business day</td>
                    </tr>
                    <tr>
                        <td><span class="highlight">C - Fair</span></td>
                        <td>3</td>
                        <td>Reasonable match with some differences</td>
                        <td>Amount within 1%, date within 2 business days</td>
                    </tr>
                    <tr>
                        <td><span class="highlight">D - Poor</span></td>
                        <td>2</td>
                        <td>Weak match with significant differences</td>
                        <td>Amount within 5%, date within 1 week</td>
                    </tr>
                    <tr>
                        <td><span class="highlight">E - Bad</span></td>
                        <td>1</td>
                        <td>Very poor match, likely incorrect</td>
                        <td>Major discrepancies in multiple criteria</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h3 class="subsection-title">Quality Assessment Dimensions</h3>
        <div class="condition-block">
            <div class="condition-title">📅 Temporal Quality Assessment</div>
            <div class="condition-description">
                <strong>Perfect Match (A):</strong> Source and target have identical value dates.<br>
                <strong>Good Match (B):</strong> Dates are within the acceptable tolerance range (typically 1-2 business days).<br>
                <strong>Poor Match (E):</strong> Dates are outside the acceptable range or have significant discrepancies.<br><br>
                The system considers business day conventions, holiday calendars, and currency-specific settlement cycles
                when evaluating date matches. A "perfect" date match for EUR might be same-day, while for USD it might
                allow for T+2 settlement.
            </div>
        </div>

        <div class="condition-block">
            <div class="condition-title">💰 Amount Quality Assessment</div>
            <div class="condition-description">
                <strong>Perfect Match (A):</strong> Source and target amounts are exactly equal.<br>
                <strong>Good Match (B):</strong> Amounts are within the configured tolerance percentage (typically 0.01-0.1%).<br>
                <strong>Poor Match (E):</strong> Amounts differ by more than the acceptable tolerance.<br><br>
                The system accounts for rounding differences, currency conversion impacts, and fee structures.
                For example, a $1,000,000.00 source might perfectly match a $999,999.99 target if the difference
                is within rounding tolerance.
            </div>
        </div>

        <div class="condition-block">
            <div class="condition-title">🏦 Account Quality Assessment</div>
            <div class="condition-description">
                <strong>Perfect Match (A):</strong> Source and target use the exact same account number.<br>
                <strong>Good Match (B):</strong> Accounts are different but linked in the account relationship table.<br>
                <strong>Poor Match (E):</strong> Accounts are completely unrelated.<br><br>
                The system maintains a dynamic map of account relationships, including parent-child hierarchies,
                business entity connections, and operational account links. This allows matching between related
                accounts that represent the same underlying business relationship.
            </div>
        </div>

        <div class="condition-block">
            <div class="condition-title">👥 Party Quality Assessment</div>
            <div class="condition-description">
                <strong>Counterparty Matching:</strong> Evaluates whether the counterparties in both movements represent
                the same business entity, using exact name matching, alias resolution, and entity relationship analysis.<br><br>
                <strong>Beneficiary Matching:</strong> Validates that beneficiary information is consistent between
                source and target, considering account holder variations and naming conventions.<br><br>
                <strong>Custodian Matching:</strong> Ensures that custodian bank information aligns between movements,
                accounting for correspondent banking relationships and institutional hierarchies.
            </div>
        </div>

        <div class="condition-block">
            <div class="condition-title">🔗 Reference Quality Assessment</div>
            <div class="condition-description">
                <strong>Cross-Reference Validation:</strong> Examines system-generated cross-reference keys and
                relationship identifiers to establish connections between movements.<br><br>
                <strong>External Reference Matching:</strong> Analyzes client-provided reference numbers,
                transaction IDs, and business-specific identifiers for correlation.<br><br>
                <strong>Book Code Consistency:</strong> Validates that trading book codes and business unit
                identifiers are consistent between related movements.
            </div>
        </div>

        <div class="example-box">
            <div class="example-title">📝 Quality Assessment Example</div>
            <strong>Source:</strong> Bank statement - $1,000,000.00, Value Date: 2025-01-15, Account: 12345, Ref: TXN001<br>
            <strong>Target:</strong> Trade confirmation - $999,999.95, Value Date: 2025-01-15, Account: 12345-SUB, Ref: TXN001<br><br>
            <strong>Assessment:</strong><br>
            • Amount Quality: B (within 0.01% tolerance)<br>
            • Date Quality: A (exact match)<br>
            • Account Quality: B (linked accounts)<br>
            • Reference Quality: A (exact match)<br>
            <strong>Overall Quality: A (4.75/5.0)</strong>
        </div>
    </div>

    <div class="content-section" id="decision-logic">
        <h2 class="section-title">7. ⚖️ Match Decision Logic</h2>

        <p>
            After calculating the quality score for a potential match, the system must decide what action to take.
            This decision process balances automation efficiency with risk management, ensuring that high-confidence
            matches are processed automatically while questionable matches receive appropriate human oversight.
        </p>

        <h3 class="subsection-title">Match Action Framework</h3>
        <div class="table-container">
            <table class="readable-table">
                <thead>
                    <tr>
                        <th>Action Code</th>
                        <th>Action Name</th>
                        <th>Quality Threshold</th>
                        <th>System Behavior</th>
                        <th>User Involvement</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><span class="highlight">A</span></td>
                        <td>Auto Match</td>
                        <td>4.5 - 5.0</td>
                        <td>Automatically confirms the match</td>
                        <td>None - fully automated</td>
                    </tr>
                    <tr>
                        <td><span class="highlight">B</span></td>
                        <td>Offer Match</td>
                        <td>3.5 - 4.4</td>
                        <td>Presents as potential match</td>
                        <td>User review and approval</td>
                    </tr>
                    <tr>
                        <td><span class="highlight">C</span></td>
                        <td>Confirm Match</td>
                        <td>2.5 - 3.4</td>
                        <td>Requires manual confirmation</td>
                        <td>Detailed user validation</td>
                    </tr>
                    <tr>
                        <td><span class="highlight">D</span></td>
                        <td>Decline Match</td>
                        <td>< 2.5</td>
                        <td>Automatically rejects the match</td>
                        <td>None - automatic rejection</td>
                    </tr>
                    <tr>
                        <td><span class="highlight">E</span></td>
                        <td>Exception Match</td>
                        <td>Variable</td>
                        <td>Routes to exception queue</td>
                        <td>Specialist investigation</td>
                    </tr>
                    <tr>
                        <td><span class="highlight">N</span></td>
                        <td>No Action</td>
                        <td>N/A</td>
                        <td>No processing action taken</td>
                        <td>None - skipped</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <h3 class="subsection-title">Cross-Reference Validation Logic</h3>
        <div class="decision-flow">
            <div class="decision-title">🔍 Reference Validation Decision Tree</div>
            <p>Before finalizing any match decision, the system performs comprehensive cross-reference validation:</p>

            <ol class="step-list">
                <li>
                    <strong>Internal-External Movement Check:</strong> If the source is internal and the target is external,
                    the system automatically sets the insert flag to "Yes" because this represents a valid business
                    relationship between internal bookings and external confirmations.
                </li>
                <li>
                    <strong>Both Internal Movements:</strong> When both source and target are internal, the system
                    checks for existing cross-references. If cross-references exist, the match is allowed.
                    If not, additional validation is performed.
                </li>
                <li>
                    <strong>Pre-advice Validation:</strong> For targets that are not pre-advice movements, the system
                    checks whether higher position quality exists. If better quality matches are available at
                    higher levels, the current match may be deferred.
                </li>
                <li>
                    <strong>External Source Processing:</strong> When the source is external, the system performs
                    specialized reference validation to ensure that external references align with internal
                    business logic and regulatory requirements.
                </li>
            </ol>
        </div>

        <div class="condition-block">
            <div class="condition-title">🔄 Insert Flag Determination</div>
            <div class="condition-description">
                The insert flag determines whether a potential match should be included in the target processing table.
                This decision is based on several factors:<br><br>

                <strong>Insert Flag = Yes:</strong> When the match represents a valid business relationship and should
                be processed further. This includes internal-external pairs, movements with valid cross-references,
                and matches that pass higher position quality checks.<br><br>

                <strong>Insert Flag = No:</strong> When the match fails validation criteria, lacks proper cross-references,
                or is superseded by higher quality alternatives. These matches are excluded from further processing.
            </div>
        </div>

        <div class="warning-box">
            <div class="warning-title">⚠️ Decision Logic Considerations</div>
            The match decision logic must balance efficiency with accuracy. Overly aggressive auto-matching can
            lead to false positives and operational risk, while overly conservative settings can create excessive
            manual work and processing delays. Regular monitoring and adjustment of decision thresholds is
            essential for optimal system performance.
        </div>
    </div>

    <div class="content-section" id="amount-processing">
        <h2 class="section-title">8. 🧮 Amount Total Processing</h2>

        <p>
            For certain position levels and business scenarios, the system performs amount total processing to
            validate that the sum of matched movements balances correctly. This is like performing a mathematical
            proof that all the pieces of a complex transaction add up to the expected total.
        </p>

        <h3 class="subsection-title">When Amount Total Processing Occurs</h3>
        <div class="condition-block">
            <div class="condition-title">📊 Position-Level Requirements</div>
            <div class="condition-description">
                Amount total processing is triggered based on configuration flags for specific position levels.
                Not all position levels require this validation - it's typically used for:<br><br>

                • <strong>High-value position levels</strong> where balance validation is critical<br>
                • <strong>External position levels</strong> where discrepancies might indicate data issues<br>
                • <strong>Settlement position levels</strong> where final balance confirmation is required<br>
                • <strong>Multi-leg transactions</strong> where component parts must sum to the total
            </div>
        </div>

        <h3 class="subsection-title">Amount Total Calculation Process</h3>
        <div class="decision-flow">
            <div class="decision-title">🔢 Calculation Logic</div>
            <ol class="step-list">
                <li>
                    <strong>Aggregate by Position and Currency:</strong> The system groups all matched movements
                    by position level and currency, creating subtotals for each combination.
                </li>
                <li>
                    <strong>Debit/Credit Balance Validation:</strong> For each group, the system ensures that
                    debit amounts equal credit amounts, or that the net balance falls within acceptable tolerances.
                </li>
                <li>
                    <strong>Tolerance Application:</strong> Amount total tolerances are applied to account for
                    rounding differences, currency conversion impacts, and legitimate business variations.
                </li>
                <li>
                    <strong>Exception Identification:</strong> Any groups that fail balance validation are
                    flagged as exceptions and routed for investigation.
                </li>
            </ol>
        </div>

        <div class="example-box">
            <div class="example-title">📝 Amount Total Example</div>
            <strong>Scenario:</strong> A complex bond trade with multiple settlement legs<br>
            <strong>Components:</strong><br>
            • Principal payment: $10,000,000.00 (debit)<br>
            • Accrued interest: $125,000.00 (debit)<br>
            • Settlement fee: $500.00 (debit)<br>
            • Total cash movement: $10,125,500.00 (credit)<br><br>
            <strong>Validation:</strong> Total debits ($10,125,500.00) = Total credits ($10,125,500.00) ✓
        </div>

        <div class="info-box">
            <div class="info-title">🎯 Business Value</div>
            Amount total processing provides an additional layer of validation that helps identify data quality
            issues, system errors, and potential fraud. It's particularly valuable in complex trading environments
            where transactions may have multiple components or involve sophisticated financial instruments.
        </div>
    </div>

    <div class="content-section" id="finalization">
        <h2 class="section-title">9. 🎉 Match Finalization Process</h2>

        <p>
            When all validation checks pass and match decisions are finalized, the system performs a comprehensive
            finalization process to update all relevant tables, create audit trails, and ensure data consistency
            across the entire system.
        </p>

        <h3 class="subsection-title">Finalization Steps</h3>
        <div class="decision-flow">
            <div class="decision-title">✅ Completion Workflow</div>
            <ol class="step-list">
                <li>
                    <strong>Match Status Updates:</strong> The system updates the match status of all involved
                    movements to reflect their new state (matched, partially matched, or exception).
                </li>
                <li>
                    <strong>Cross-Reference Creation:</strong> New cross-reference records are created to establish
                    permanent links between matched movements, enabling future queries and reporting.
                </li>
                <li>
                    <strong>Audit Trail Recording:</strong> Complete details of the matching decision, including
                    quality scores, decision rationale, and processing timestamps, are recorded for compliance.
                </li>
                <li>
                    <strong>Lock Removal:</strong> Processing locks are removed from all movements to allow
                    future processing cycles and prevent deadlock situations.
                </li>
                <li>
                    <strong>Notification Generation:</strong> Appropriate notifications are sent to users,
                    downstream systems, and monitoring tools about the completed matches.
                </li>
            </ol>
        </div>

        <div class="info-box">
            <div class="info-title">🔒 Data Integrity Assurance</div>
            The finalization process includes comprehensive validation to ensure that all database updates
            maintain referential integrity and business rule compliance. If any step fails, the entire
            transaction is rolled back to prevent partial updates that could compromise data consistency.
        </div>
    </div>

    <div class="content-section" id="exception-handling">
        <h2 class="section-title">10. ⚠️ Exception Handling & Recovery</h2>

        <p>
            The matching process includes sophisticated exception handling mechanisms to deal with unexpected
            situations, data quality issues, and system errors. This ensures that problems are identified,
            documented, and resolved without compromising the integrity of the overall matching process.
        </p>

        <h3 class="subsection-title">Common Exception Scenarios</h3>
        <div class="quality-matrix">
            <div class="quality-card">
                <div class="quality-score">🚫 System Disabled</div>
                <strong>Cause:</strong> Scheduler status set to disabled<br>
                <strong>Action:</strong> Clean shutdown with status preservation<br>
                <strong>Recovery:</strong> Enable scheduler and restart processing
            </div>
            <div class="quality-card">
                <div class="quality-score">❓ Missing Configuration</div>
                <strong>Cause:</strong> No quality configuration for position level<br>
                <strong>Action:</strong> Skip position level processing<br>
                <strong>Recovery:</strong> Add required configuration parameters
            </div>
            <div class="quality-card">
                <div class="quality-score">🔗 Reference Conflicts</div>
                <strong>Cause:</strong> Conflicting cross-reference data<br>
                <strong>Action:</strong> Route to exception queue<br>
                <strong>Recovery:</strong> Manual investigation and resolution
            </div>
            <div class="quality-card">
                <div class="quality-score">⚖️ Balance Failures</div>
                <strong>Cause:</strong> Amount total validation failures<br>
                <strong>Action:</strong> Flag for investigation<br>
                <strong>Recovery:</strong> Data correction or tolerance adjustment
            </div>
        </div>

        <div class="warning-box">
            <div class="warning-title">⚠️ Exception Recovery Best Practices</div>
            <ul style="margin: 10px 0; padding-left: 20px;">
                <li>Always investigate the root cause before implementing fixes</li>
                <li>Document all exception resolutions for future reference</li>
                <li>Monitor exception patterns to identify systemic issues</li>
                <li>Implement preventive measures to reduce future exceptions</li>
                <li>Maintain clear escalation procedures for complex cases</li>
            </ul>
        </div>
    </div>

    <div class="content-section" id="configuration">
        <h2 class="section-title">11. ⚙️ Configuration Parameters Deep Dive</h2>

        <p>
            Understanding the configuration parameters is crucial for optimizing system performance and ensuring
            that the matching process aligns with your business requirements. Each parameter affects different
            aspects of the matching logic and should be carefully tuned based on your operational needs.
        </p>

        <div class="table-container">
            <table class="readable-table">
                <thead>
                    <tr>
                        <th>Parameter</th>
                        <th>Default</th>
                        <th>Impact</th>
                        <th>Tuning Guidance</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>Position Level Threshold</td>
                        <td>6</td>
                        <td>Determines minimum position level for processing</td>
                        <td>Lower values include more detailed positions; higher values focus on summary levels</td>
                    </tr>
                    <tr>
                        <td>Pre-advice Search Always</td>
                        <td>No</td>
                        <td>Controls whether pre-advice positions are always searched</td>
                        <td>Enable if you have reliable pre-advice data and want early matching</td>
                    </tr>
                    <tr>
                        <td>Pre-advice Predict Strategy</td>
                        <td>1</td>
                        <td>Defines the algorithm used for pre-advice matching</td>
                        <td>Strategy 1 is conservative; higher numbers are more aggressive</td>
                    </tr>
                    <tr>
                        <td>Account Linking Exemption Level</td>
                        <td>7</td>
                        <td>Position level above which account linking is not required</td>
                        <td>Higher values require stricter account matching at more levels</td>
                    </tr>
                    <tr>
                        <td>Enforce Reference Selection</td>
                        <td>No</td>
                        <td>Requires reference-based matching when enabled</td>
                        <td>Enable only if reference data quality is consistently high</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <div class="content-section" id="troubleshooting">
        <h2 class="section-title">12. 🔧 Common Issues & Solutions</h2>

        <div class="quality-matrix">
            <div class="quality-card">
                <div class="quality-score">🐌 Slow Performance</div>
                <strong>Symptoms:</strong> Long processing times, timeouts<br>
                <strong>Causes:</strong> Large data volumes, inefficient queries, resource constraints<br>
                <strong>Solutions:</strong> Optimize thresholds, add indexes, increase resources
            </div>
            <div class="quality-card">
                <div class="quality-score">❌ Low Match Rates</div>
                <strong>Symptoms:</strong> Many unmatched movements<br>
                <strong>Causes:</strong> Strict thresholds, poor data quality, configuration issues<br>
                <strong>Solutions:</strong> Adjust tolerances, improve data quality, review configuration
            </div>
            <div class="quality-card">
                <div class="quality-score">⚠️ High Exception Rates</div>
                <strong>Symptoms:</strong> Many items in exception queues<br>
                <strong>Causes:</strong> Data inconsistencies, system errors, business rule conflicts<br>
                <strong>Solutions:</strong> Investigate patterns, fix data issues, update rules
            </div>
            <div class="quality-card">
                <div class="quality-score">🔄 Processing Loops</div>
                <strong>Symptoms:</strong> Same movements processed repeatedly<br>
                <strong>Causes:</strong> Incorrect stage progression, lock issues<br>
                <strong>Solutions:</strong> Check stage logic, clear locks, restart processing
            </div>
        </div>

        <div class="info-box">
            <div class="info-title">📞 When to Seek Help</div>
            Contact technical support when you encounter persistent issues, unusual error patterns,
            or when system behavior doesn't match expectations. Provide detailed information about
            symptoms, error messages, and recent configuration changes to expedite resolution.
        </div>
    </div>

    <div style="text-align: center; padding: 40px; background: linear-gradient(135deg, #2c3e50, #3498db); color: white; border-radius: 15px; margin-top: 40px;">
        <h2 style="margin-bottom: 20px;">🎓 Congratulations!</h2>
        <p style="font-size: 1.2em; margin-bottom: 0;">
            You've completed the comprehensive matching process guide. You now have a deep understanding
            of how the system works, from initialization through finalization. Use this knowledge to
            optimize your matching operations and achieve exceptional results.
        </p>
    </div>

</body>
</html>
