{"_from": "@types/selenium-webdriver@^3.0.0", "_id": "@types/selenium-webdriver@3.0.26", "_inBundle": false, "_integrity": "sha512-dyIGFKXfUFiwkMfNGn1+F6b80ZjR3uSYv1j6xVJSDlft5waZ2cwkHW4e7zNzvq7hiEackcgvBpmnXZrI1GltPg==", "_location": "/@types/selenium-webdriver", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/selenium-webdriver@^3.0.0", "name": "@types/selenium-webdriver", "escapedName": "@types%2fselenium-webdriver", "scope": "@types", "rawSpec": "^3.0.0", "saveSpec": null, "fetchSpec": "^3.0.0"}, "_requiredBy": ["/protractor", "/webdriver-js-extender"], "_resolved": "https://registry.npmjs.org/@types/selenium-webdriver/-/selenium-webdriver-3.0.26.tgz", "_shasum": "fc7d87d580affa2e52685b2e881bc201819a5836", "_spec": "@types/selenium-webdriver@^3.0.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\protractor", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/BillArmstrong"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/Kuniwak"}, {"name": "<PERSON>", "url": "https://github.com/cnishina"}, {"name": "<PERSON>", "url": "https://github.com/SupernaviX"}, {"name": "<PERSON>", "url": "https://github.com/bendxn"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/oddui"}], "dependencies": {}, "deprecated": false, "description": "TypeScript definitions for selenium-webdriver", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/selenium-webdriver", "license": "MIT", "main": "", "name": "@types/selenium-webdriver", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/selenium-webdriver"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "fa3688820548519d3250cc924949bca3d2ac35c1e6219dfa95a7a76f77eeabe8", "version": "3.0.26"}