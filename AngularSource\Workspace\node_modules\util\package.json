{"_from": "util@^0.11.0", "_id": "util@0.11.1", "_inBundle": false, "_integrity": "sha512-HShAsny+zS2TZfaXxD9tYj4HQGlBezXZMZuM/S5PKLLoZkShZiGk9o5CzukI1LVHZvjdvZ2Sj1aW/Ndn2NB/HQ==", "_location": "/util", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "util@^0.11.0", "name": "util", "escapedName": "util", "rawSpec": "^0.11.0", "saveSpec": null, "fetchSpec": "^0.11.0"}, "_requiredBy": ["/node-libs-browser"], "_resolved": "https://registry.npmjs.org/util/-/util-0.11.1.tgz", "_shasum": "3236733720ec64bb27f6e26f421aaa2e1b588d61", "_spec": "util@^0.11.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\node-libs-browser", "author": {"name": "<PERSON><PERSON>", "url": "http://www.joyent.com"}, "browser": {"./support/isBuffer.js": "./support/isBufferBrowser.js"}, "bugs": {"url": "https://github.com/defunctzombie/node-util/issues"}, "bundleDependencies": false, "dependencies": {"inherits": "2.0.3"}, "deprecated": false, "description": "Node.JS util module", "devDependencies": {"airtap": "~0.1.0", "is-async-supported": "~1.2.0", "run-series": "~1.1.4", "tape": "~4.9.0"}, "files": ["util.js", "support"], "homepage": "https://github.com/defunctzombie/node-util", "keywords": ["util"], "license": "MIT", "main": "./util.js", "name": "util", "repository": {"type": "git", "url": "git://github.com/defunctzombie/node-util.git"}, "scripts": {"test": "node test/node/index.js", "test:browsers": "airtap test/browser/index.js"}, "version": "0.11.1"}