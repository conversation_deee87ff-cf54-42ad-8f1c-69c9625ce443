{"_from": "@types/chart.js@^2.9.24", "_id": "@types/chart.js@2.9.41", "_inBundle": false, "_integrity": "sha512-3dvkDvueckY83UyUXtJMalYoH6faOLkWQoaTlJgB4Djde3oORmNP0Jw85HtzTuXyliUHcdp704s0mZFQKio/KQ==", "_location": "/@types/chart.js", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "@types/chart.js@^2.9.24", "name": "@types/chart.js", "escapedName": "@types%2fchart.js", "scope": "@types", "rawSpec": "^2.9.24", "saveSpec": null, "fetchSpec": "^2.9.24"}, "_requiredBy": ["/ng2-charts"], "_resolved": "https://registry.npmjs.org/@types/chart.js/-/chart.js-2.9.41.tgz", "_shasum": "4148cdc87d4f98fad44b2883271cd0fa57f05e0d", "_spec": "@types/chart.js@^2.9.24", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\ng2-charts", "bugs": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped/issues"}, "bundleDependencies": false, "contributors": [{"name": "<PERSON>", "url": "https://github.com/anuti"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/FabienLavocat"}, {"name": "KentarouTakeda", "url": "https://github.com/KentarouTakeda"}, {"name": "<PERSON>", "url": "https://github.com/larrybahr"}, {"name": "<PERSON>", "url": "https://github.com/mernen"}, {"name": "<PERSON>", "url": "https://github.com/josefpaij"}, {"name": "<PERSON>", "url": "https://github.com/danmana"}, {"name": "<PERSON>", "url": "https://github.com/guillaume-ro-fr"}, {"name": "<PERSON>", "url": "https://github.com/archy-bold"}, {"name": "<PERSON>", "url": "https://github.com/braincore"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/alexdor"}, {"name": "<PERSON>", "url": "https://github.com/mahnuh"}, {"name": "<PERSON>", "url": "https://github.com/Conrad777"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/adripanico"}, {"name": "wert<PERSON>i", "url": "https://github.com/wertzui"}, {"name": "<PERSON>", "url": "https://github.com/lekoaf"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/ElianCordoba"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/indigolain"}, {"name": "<PERSON>", "url": "https://github.com/ricmello"}, {"name": "<PERSON>", "url": "https://github.com/rnicholus"}, {"name": "<PERSON>", "url": "https://github.com/mrjack88"}, {"name": "<PERSON>", "url": "https://github.com/canoceto"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/nobu222"}, {"name": "<PERSON>", "url": "https://github.com/Marcoru97"}, {"name": "<PERSON>", "url": "https://github.com/tonybadguy"}, {"name": "<PERSON>", "url": "https://github.com/Ilmarinen100"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/IVIosi"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/samar<PERSON>han"}], "dependencies": {"moment": "^2.10.2"}, "deprecated": false, "description": "TypeScript definitions for chart.js", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/chart.js", "license": "MIT", "main": "", "name": "@types/chart.js", "repository": {"type": "git", "url": "git+https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/chart.js"}, "scripts": {}, "typeScriptVersion": "4.5", "types": "index.d.ts", "typesPublisherContentHash": "2577c7a5b63f6e12990e9671791fb201d769f04ab51c79989089157278e95de9", "version": "2.9.41"}