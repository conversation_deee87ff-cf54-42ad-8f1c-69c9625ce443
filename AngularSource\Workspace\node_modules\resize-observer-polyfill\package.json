{"_from": "resize-observer-polyfill@^1.5.1", "_id": "resize-observer-polyfill@1.5.1", "_inBundle": false, "_integrity": "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==", "_location": "/resize-observer-polyfill", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "resize-observer-polyfill@^1.5.1", "name": "resize-observer-polyfill", "escapedName": "resize-observer-polyfill", "rawSpec": "^1.5.1", "saveSpec": null, "fetchSpec": "^1.5.1"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz", "_shasum": "0e9020dd3d21024458d4ebd27e23e40269810464", "_spec": "resize-observer-polyfill@^1.5.1", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "<PERSON>", "email": "<EMAIL>"}, "bugs": {"url": "https://github.com/que-etc/resize-observer-polyfill/issues"}, "bundleDependencies": false, "deprecated": false, "description": "A polyfill for the Resize Observer API", "devDependencies": {"babel-eslint": "10.0.1", "cpy-cli": "2.0.0", "eslint": "5.10.0", "jasmine": "2.8.0", "jasmine-core": "2.8.0", "karma": "3.1.3", "karma-chrome-launcher": "2.2.0", "karma-firefox-launcher": "1.1.0", "karma-jasmine": "1.1.2", "karma-jasmine-html-reporter": "0.2.2", "karma-rollup-preprocessor": "6.1.1", "karma-sauce-launcher": "1.2.0", "karma-sourcemap-loader": "0.3.7", "karma-spec-reporter": "0.0.32", "promise-polyfill": "8.1.0", "rollup": "0.67.4", "rollup-plugin-typescript": "1.0.0", "typescript": "3.2.2"}, "files": ["src/", "dist/"], "homepage": "https://github.com/que-etc/resize-observer-polyfill", "keywords": ["ResizeObserver", "resize", "observer", "util", "client", "browser", "polyfill", "ponyfill"], "license": "MIT", "main": "dist/ResizeObserver.js", "module": "dist/ResizeObserver.es.js", "name": "resize-observer-polyfill", "repository": {"type": "git", "url": "git+https://github.com/que-etc/resize-observer-polyfill.git"}, "scripts": {"build": "rollup -c && cpy src/index.js.flow dist --rename=ResizeObserver.js.flow", "test": "npm run test:lint && npm run test:spec", "test:ci": "npm run test:lint && npm run test:spec:sauce && npm run test:spec:node", "test:ci:pull": "npm run test:lint && karma start --browsers Firefox && npm run test:spec:node", "test:lint": "node ./node_modules/eslint/bin/eslint.js \"**/*.js\" --ignore-pattern \"/dist/\"", "test:spec": "karma start --browsers Chrome && npm run test:spec:node", "test:spec:custom": "karma start --no-browsers", "test:spec:native": "karma start --no-browsers --native", "test:spec:node": "npm run build && node tests/node/index.js", "test:spec:sauce": "karma start --sauce=windows && karma start --sauce=linux && karma start --sauce=osx"}, "types": "src/index.d.ts", "version": "1.5.1"}