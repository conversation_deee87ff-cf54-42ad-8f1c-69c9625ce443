(window.webpackJsonp=window.webpackJsonp||[]).push([[15],{"nio/":function(t,e,i){"use strict";i.r(e);var a=i("CcnG"),l=i("mrSG"),n=i("447K"),s=i("wd/R"),o=i.n(s),r=i("MA5k"),d=i("nxpO"),h=i("ZYCi"),u=function(t){function e(e,i){var a=t.call(this,i,e)||this;return a.commonService=e,a.element=i,a.inputData=new n.G(a.commonService),a.rateData=new n.G(a.commonService),a.faultData=new n.G(a.commonService),a.baseURL=n.Wb.getBaseURL(),a.actionMethod="",a.actionPath="",a.requestParams=[],a.selectedItemsListEntity=null,a.selectedItemsList=null,a.selectedTabId=0,a.states={},a.selectedItem=null,a.selectedRow=null,a.verticalPosition=0,a.refreshRate=30,a.refreshRateStr=null,a.tabIsChanged=!1,a.jsonReader=new n.L,a.interval=null,a.moduleId="",a.errorLocation=0,a.entity=null,a.currencyFormat="",a.dateFormat="",a.dateFormatUpper="",a.systemStatus="",a.moduleName="PCM Dashboard Monitor",a.versionNumber="1.00.00",a.releaseDate="20 May 2019",a.menuAccess=2,a.isBackValueClicked=!1,a.entityChanged=!1,a.swtAlert=new n.bb(e),a}return l.d(e,t),e.prototype.ngOnDestroy=function(){instanceElement=null},e.prototype.ngOnInit=function(){var t=this;instanceElement=this,this.entityLabel.text=n.Wb.getPredictMessage("label.entity",null),this.entityCombo.toolTip=n.Wb.getPredictMessage("dashboard.entity.tooltip",null),this.entityMoreItemsButton.label=n.Wb.getPredictMessage("label.moreItems",null),this.dateLabel.text=n.Wb.getPredictMessage("label.valueDate",null),this.applyThresholdCheck.toolTip=n.Wb.getPredictMessage("dashboard.currencyThreshold.tooltip",null),this.applyThresholdLabel.text=n.Wb.getPredictMessage("dashboard.currencyThreshold.label",null),this.applyMultiplierCheck.toolTip=n.Wb.getPredictMessage("dashboard.currencyMultiplier.tooltip",null),this.applyMultiplierLabel.text=n.Wb.getPredictMessage("dashboard.currencyMultiplier.label",null),this.spreadCheck.toolTip=n.Wb.getPredictMessage("dashboard.spreadOnly.tooltip",null),this.spreadLabel.text=n.Wb.getPredictMessage("dashboard.spreadOnly.label",null),this.displayValueGroup.toolTip=n.Wb.getPredictMessage("dashboard.displayValue.tooltip",null),this.valueVolume.text=n.Wb.getPredictMessage("dashboard.displayValue.label",null),this.value.label=n.Wb.getPredictMessage("dashboard.radioValue.label",null),this.volume.label=n.Wb.getPredictMessage("dashboard.radioVolume.label",null),this.refreshButton.label=n.Wb.getPredictMessage("button.refresh",null),this.refreshButton.toolTip=n.Wb.getPredictMessage("tooltip.refresh",null),this.rateButton.label=n.Wb.getPredictMessage("button.rate",null),this.rateButton.toolTip=n.Wb.getPredictMessage("tooltip.rate",null),this.closeButton.label=n.Wb.getPredictMessage("button.close",null),this.closeButton.toolTip=n.Wb.getPredictMessage("tooltip.close",null),this.lastRef.text=n.Wb.getPredictMessage("dashboard.lastRefresh.label",null),this.printIcon.toolTip=n.Wb.getPredictMessage("tooltip.print",null),this.printIcon.enabled=!0,this.entityMoreItemsButton.enabled=!1,this.refreshButton.setFocus(),n.v.subscribe(function(e){t.report(e)})},e.prototype.onLoad=function(){var t=this;try{this.advancedGrid.showTreeHeader=!0,this.advancedGrid.addEventListener(n.jc.ROW_CLICK,function(e){var i=e;t.selectedRow=i.getRowData()}),this.advancedGrid.addEventListener(n.jc.CELL_CLICK,function(e){if("link:num"===e.getItemRander().type){"root"===e.getParentRow().getParentItem().title?(t.level1=e.getParentRow().title,t.level2=null,t.level3=null):"root"===e.getParentRow().getParentItem().getParentItem().title?(t.level1=e.getParentRow().getParentItem().title,t.level2=e.getParentRow().title,t.level3=null):(t.level1=e.getParentRow().getParentItem().getParentItem().title,t.level2=e.getParentRow().getParentItem().title,t.level3=e.getParentRow().title),t.level1=t.level1.substr(0,3);var i=e.getColumnHeader().dataelement.split("_")[0];t.clickLinkHandler(i,e.getItemRander())}}),this.advancedGrid.groupItemRenderer=function(t){var e=t;"Y"===e.getRowData().highlighted&&e.setStyle("background","lightgray")},this.requestParams=[],this.actionMethod="method=display",this.actionPath="dashboardPCM.do?",this.title="PCM Dashboard Monitor",this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbResult=function(e){t.inputDataResult(e)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(e){console.log(e,this.moduleId,"PCM Monitor","onLoad")}},e.prototype.startOfComms=function(){this.loadingImage.setVisible(!0),this.dataExport.enabled=!1},e.prototype.endOfComms=function(){this.loadingImage.setVisible(!1),this.dataExport.enabled=!0},e.prototype.inputDataResult=function(t){var e=this;try{var i=0;if(this.errorLocation=0,this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=t,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON))if(this.jsonReader.getRequestReplyStatus()&&this.jsonReader.getRequestReplyStatus()){this.menuAccess=Number(this.jsonReader.getScreenAttributes().menuaccess),this.dateFormat=this.jsonReader.getScreenAttributes().dateformat,this.dateFormatUpper=this.dateFormat.toUpperCase(),this.currencyFormat=this.jsonReader.getScreenAttributes().currencyPattern,this.errorLocation=294,this.startDate.formatString=this.dateFormat.toLowerCase();var a=this.jsonReader.getScreenAttributes().lastRefTime;if(this.lastRefTime.text=a.replace(/\\u0028/g,"(").replace(/\\u0029/g,")"),this.refreshRateStr=this.jsonReader.getScreenAttributes().refresh,this.refreshRateStr&&(""!==this.refreshRateStr?this.refreshRate=Number(this.refreshRateStr):this.refreshRate=30),this.sysDateFrmSession=this.jsonReader.getSingletons().sysDateFrmSession,this.inputSince=this.jsonReader.getSingletons().inputSince,this.selectedDate=this.jsonReader.getSingletons().valueDate,this.errorLocation=312,this.startDate.text=this.selectedDate,this.systemStatus=this.jsonReader.getSingletons().systemStatus,"G"===this.systemStatus?this.divColor.nativeElement.style.backgroundColor="green":"R"===this.systemStatus?this.divColor.nativeElement.style.backgroundColor="red":this.divColor.nativeElement.style.backgroundColor="#FFBF00",this.errorLocation=319,this.valueVol=this.jsonReader.getSingletons().volume,this.value.selected="N"===this.valueVol,this.volume.selected="Y"===this.valueVol,this.spreadOnly=this.jsonReader.getSingletons().spreadOnly,this.spreadCheck.selected="Y"===this.spreadOnly,this.ccyThreshold=this.jsonReader.getSingletons().applyCurrencyThreshold,this.applyThresholdCheck.selected="Y"===this.ccyThreshold,this.ccyMultiplier=this.jsonReader.getSingletons().applyCurrencyMultiplier,this.applyMultiplierCheck.selected="Y"===this.ccyMultiplier,this.errorLocation=330,this.entityChanged&&this.tabs.getTabChildren().length>0)for(var l=this.tabs.getTabChildren().slice(),s=0;s<l.length;s++)this.tabs.removeChild(l[s]);if(this.entityChanged=!1,0==this.tabs.getTabChildren().length)this.lastRecievedJSON.dashboard.advancedGrid&&this.lastRecievedJSON.dashboard.advancedGrid.tabs&&(setTimeout(function(){e.displayContainerToday=e.tabs.addChild(n.Xb),e.displayContainerTodayPlus=e.tabs.addChild(n.Xb),e.displayContainerTodayPlusPlus=e.tabs.addChild(n.Xb),e.displayContainerTodayPlusThree=e.tabs.addChild(n.Xb),e.displayContainerTodayPlusFour=e.tabs.addChild(n.Xb),e.displayContainerTodayPlusFive=e.tabs.addChild(n.Xb),e.displayContainerTodayPlusSix=e.tabs.addChild(n.Xb),e.displayContainerTodayPlusSeven=e.tabs.addChild(n.Xb),e.displayContainerSelected=e.tabs.addChild(n.Xb)},0),this.errorLocation=345,setTimeout(function(){i=8;for(var t=0;t<e.lastRecievedJSON.dashboard.advancedGrid.tabs.row.length;t++)e.tabs.getChildAt(t).id=t,e.tabs.getChildAt(t).label=e.lastRecievedJSON.dashboard.advancedGrid.tabs.row[t].dateLabel,e.tabs.getChildAt(t).businessday=e.lastRecievedJSON.dashboard.advancedGrid.tabs.row[t].businessday,e.selectedDate.startsWith(e.lastRecievedJSON.dashboard.advancedGrid.tabs.row[t].dateLabel)&&(i=t),e.tabs.getChildAt(t).dateValue=e.lastRecievedJSON.dashboard.advancedGrid.tabs.row[t].content,0==e.tabs.getChildAt(t).businessday?e.tabs.getChildAt(t).setTabHeaderStyle("color","darkgray"):e.tabs.getChildAt(t).setTabHeaderStyle("color","black");e.tabs.getChildAt(e.tabs.getTabChildren().length-1).label="Selected",setTimeout(function(){e.selectedTabId=i,e.tabs.selectedIndex=e.selectedTabId},0)},0));else{i=8;for(s=0;s<this.lastRecievedJSON.dashboard.advancedGrid.tabs.row.length;s++)this.selectedDate.startsWith(this.lastRecievedJSON.dashboard.advancedGrid.tabs.row[s].dateLabel)&&(i=s);this.selectedTabId=i,this.tabs.selectedIndex=this.selectedTabId,this.errorLocation=364}if(!this.jsonReader.isDataBuilding()){this.entityCombo.setComboData(this.jsonReader.getSelects(),!1),this.jsonReader.getSingletons().selectedEntity&&(this.selectedItemsListEntity=this.jsonReader.getSingletons().selectedEntity,-1!=this.jsonReader.getSingletons().selectedEntity.indexOf(",")?(this.entityMoreItemsButton.enabled=!0,this.selectedEntity.text=this.get2FirstItemsFromList(this.jsonReader.getSingletons().selectedEntity),this.selectedEntity.toolTip=this.jsonReader.getSingletons().selectedEntity):(this.selectedEntity.text=this.entityCombo.selectedItem.value,this.selectedItemsList=null,this.entityMoreItemsButton.enabled=!1)),this.errorLocation=384,this.lastRecievedJSON.dashboard.advancedGrid&&(this.printIcon.enabled=this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size>0,this.dataExport.enabled=this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size>0,this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size>0?(this.advancedGrid.dataProvider=this.lastRecievedJSON.dashboard.advancedGrid,this.tabs.getSelectedTab()?this.states[this.tabs.getSelectedTab().id]&&this.advancedGrid.openSavedTreeState(this.states[this.tabs.getSelectedTab().id]):this.states[0]&&this.advancedGrid.openSavedTreeState(this.states[0])):this.advancedGrid.dataProvider=this.lastRecievedJSON.dashboard.advancedGrid,null!=this.selectedItem&&this.advancedGrid.setSelectedRow(this.selectedItem),this.advancedGrid.verticalScrollPosition=this.verticalPosition),this.errorLocation=412;var o=this.refreshRate;clearInterval(this.interval),this.interval=setInterval(function(){e.dataRefresh(),e.errorLocation=417},1e3*o)}this.prevRecievedJSON=this.lastRecievedJSON}else this.swtAlert.error(n.Wb.getPredictMessage("label.errorContactSystemAdmin",null)+" \n"+this.jsonReader.getRequestReplyMessage()),this.actionPath="dashboardPCM.do?",this.actionMethod="method=saveLogError",this.requestParams=[],this.requestParams.error="PCM Monitor inputDataResult["+this.errorLocation+"]",this.requestParams.text=this.jsonReader.getRequestReplyMessage(),this.faultData.encodeURL=!1,this.faultData.url=this.baseURL+this.actionPath+this.actionMethod,this.faultData.send(this.requestParams),this.faultData.cbResult=function(t){e.faultDateResult(t)};else this.jsonReader.getRequestReplyStatus()?this.jsonReader.isDataBuilding()||(this.lastRecievedJSON.dashboard.advancedGrid&&(this.printIcon.enabled=this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size>0,this.dataExport.enabled=this.lastRecievedJSON.dashboard.advancedGrid.grid_data.rows.size>0),this.errorLocation=445):this.dataExport.enabled=!1}catch(r){this.swtAlert.error(n.Wb.getPredictMessage("label.errorContactSystemAdmin",null)+" \n["+this.errorLocation+"]:"+r),this.actionPath="dashboardPCM.do?",this.actionMethod="method=saveLogError",this.requestParams=[],this.requestParams.error="PCM Monitor inputDataResult["+this.errorLocation+"]",this.requestParams.text=r,this.faultData.encodeURL=!1,this.faultData.url=this.baseURL+this.actionPath+this.actionMethod,this.faultData.send(this.requestParams),this.faultData.cbResult=function(t){e.faultDateResult(t)}}},e.prototype.optionsHandler=function(){var t=this;try{clearInterval(this.interval),this.states[this.selectedTabId]=this.advancedGrid.saveOpenTreeState(),this.win=n.Eb.createPopUp(this,d.a,{title:"Auto-refresh Rate",refreshText:this.refreshRate}),this.win.width="340",this.win.height="150",this.win.id="myOptionsPopUp",this.win.enableResize=!1,this.win.showControls=!0,this.win.isModal=!0,this.win.onClose.subscribe(function(){t.autoRefreshAfterStop()},function(t){console.log(t)}),this.win.display()}catch(e){n.Wb.logError(e,this.moduleId,"Dashboard","optionsHandler",this.errorLocation)}},e.prototype.clickLinkHandler=function(t,e){try{if("repair"==t||"suppressed"==t||"rejected"==t){var i;switch(t){case"repair":i="10";break;case"rejected":i="4";break;case"suppressed":i="9"}this.goToPCMInputException(i,e.text)}else this.isBackValueClicked="Back"==t,this.goToPCDashboardDetails(t.substring(0,1).toUpperCase())}catch(a){n.Wb.logError(a,this.moduleId,"Dashboard","clickLinkHandler",this.errorLocation)}},e.prototype.changeRadioGroup=function(){try{this.updateData("no")}catch(t){n.Wb.logError(t,this.moduleId,"Dashboard","changeRadioGroup",this.errorLocation)}},e.prototype.goToPCMInputException=function(t,e){try{this.status=t,this.actionPath="inputexceptionsmessages.do?",this.actionMethod="fromPCM=yes",this.actionMethod=this.actionMethod+"&fromDashboard=yes",this.actionMethod=this.actionMethod+"&currencyCode="+this.level1,this.actionMethod=this.actionMethod+"&p=1",this.actionMethod=this.actionMethod+"&fromFlex=true",this.actionMethod=this.actionMethod+"&fromDate="+this.startDate.text,this.actionMethod=this.actionMethod+"&status="+t,this.actionMethod=this.actionMethod+"&m="+e.toString(),this.actionMethod=this.actionMethod+"&toDate="+this.startDate.text,this.actionMethod=this.actionMethod+"&n=50",this.actionMethod=this.actionMethod+"&type=All",n.x.call("openChildWindowExceptions",this.actionPath+this.actionMethod)}catch(i){n.Wb.logError(i,this.moduleId,"Dashboard","goToPCMInputException",this.errorLocation)}},e.prototype.goToPCDashboardDetails=function(t){try{this.status=t,n.x.call("openChildWindow","dashboardDetails")}catch(e){n.Wb.logError(e,this.moduleId,"Dashboard","goToPCDashboardDetails",this.errorLocation)}},e.prototype.getParamsFromParent=function(){return this.entity=this.selectedRow.ENTITY_ID?this.selectedRow.ENTITY_ID:this.entityCombo.selectedItem.content.indexOf("<")>-1?this.selectedItemsList:this.entityCombo.selectedItem.content,[{screenName:"dashboard",status:this.status,currencyCode:this.level1,accountGroup:this.level2,account:this.level3,entity:this.entity,listEntity:this.selectedItemsList,valueDate:this.startDate.text,ccyThreshold:this.applyThresholdCheck.selected?"Y":"N",spreadOnly:this.spreadCheck.selected?"Y":"N",ccyMultiplier:this.applyMultiplierCheck.selected?"Y":"N",inputSince:this.inputSince,isBackValueClicked:this.isBackValueClicked}]},e.prototype.saveRefreshRate=function(t){var e=this;try{this.actionPath="dashboardPCM.do?",this.actionMethod="method=saveRefreshRate",this.requestParams=[],this.requestParams.refresh=t,this.rateData.encodeURL=!1,this.rateData.url=this.baseURL+this.actionPath+this.actionMethod,this.rateData.cbResult=function(){e.optionDateResult()},this.rateData.send(this.requestParams)}catch(i){console.log(i,this.moduleId,"PCM Monitor","saveRefreshRate")}},e.prototype.optionDateResult=function(){this.updateData("no")},e.prototype.autoRefreshAfterStop=function(){var t=this,e=this.refreshRate;clearInterval(this.interval),this.interval=setInterval(function(){t.dataRefresh()},1e3*e)},e.prototype.report=function(t){try{n.x.call("report",this.selectedItemsListEntity,this.selectedEntity.text,t,this.dateFormat,this.startDate.text,this.applyThresholdCheck.selected?"Y":"N",this.applyMultiplierCheck.selected?"Y":"N",this.spreadCheck.selected?"Y":"N",this.value.selected?"Y":"N")}catch(e){console.log(e,this.moduleId,"PCM Monitor","report"),n.Wb.logError(e,this.moduleId,"Dashboard","report",this.errorLocation)}},e.prototype.printPage=function(t){try{n.x.call("printPage")}catch(e){n.Wb.logError(e,this.moduleId,"Dashboard","printPage",this.errorLocation)}},e.prototype.dataRefresh=function(){try{this.updateData("no")}catch(t){n.Wb.logError(t,this.moduleId,"Dashboard","dataRefresh",this.errorLocation)}},e.prototype.tabIndexChangeHandler=function(){var t=this.tabs.getChildAt(this.tabs.selectedIndex);t.id!==this.selectedTabId&&(this.states[this.selectedTabId]=this.advancedGrid.saveOpenTreeState());var e=new Date(n.j.parseDate(t.dateValue,"YYYY-MM-DD"));e.setHours(12,0,0),this.tabIsChanged=!0,"Selected"!==this.tabs.selectedLabel&&(this.startDate.selectedDate=e),this.updateData("no")},e.prototype.get2FirstItemsFromList=function(t){var e=(t.match(new RegExp(",","g"))||[]).length;return t&&-1!==t.indexOf(",")&&e>=2?t.split(",")[0].toString()+" ,"+t.split(",")[1].toString()+",...":t},e.prototype.changeCombo=function(t,e,i){try{"<<Multiple Values>>"==t.selectedValue?(e.enabled=!0,i.text="",this.selectedItemsListEntity=""):(this.selectedItemsListEntity=t.selectedItem.content,e.enabled=!1,i.text=t.selectedValue,this.entityChanged=!0,this.updateData("no"))}catch(a){console.log(a,this.moduleId,"PCM Monitor","changeCombo")}},e.prototype.multipleListSelect=function(t,e){var i=this;try{this.win=n.Eb.createPopUp(this,r.a,{title:"Entity",operation:"in",dataSource:"fromDashboard",columnLabel:"entityList",columnCode:"entityList",viewOnly:!1}),this.win.enableResize=!1,this.win.id="listValuesPopup",this.win.width="500",this.win.height="500",this.win.showControls=!0,this.win.isModal=!0,this.win.onClose.subscribe(function(){if(i.win.getChild().result){i.selectedItemsList=i.selectedItemsListEntity;var t=(i.selectedItemsList.match(new RegExp(",","g"))||[]).length;i.selectedItemsList&&-1!==i.selectedItemsList.indexOf(",")&&t>=2?e.text=i.selectedItemsList.split(",")[0].toString()+" ,"+i.selectedItemsList.split(",")[1].toString()+",...":e.text=i.selectedItemsList,i.entityChanged=!0,i.updateData("no")}}),this.win.display()}catch(a){console.log(a,this.moduleId,"PCM Monitor","multipleListSelect")}},e.prototype.updateData=function(t){var e=this;try{this.states[this.selectedTabId]=this.advancedGrid.saveOpenTreeState(),this.verticalPosition=this.advancedGrid.verticalScrollPosition,this.selectedItem=this.advancedGrid.getSelectedRow(),this.actionPath="dashboardPCM.do?",this.actionMethod="method=display",this.requestParams=[],this.requestParams.dateAsString=this.startDate.text,this.requestParams.formatDate=this.dateFormat,this.requestParams.entity=this.selectedItemsListEntity,this.requestParams.applyCurrencyThreshold=this.applyThresholdCheck.selected?"Y":"N",this.requestParams.applyCurrencyMultiplier=this.applyMultiplierCheck.selected?"Y":"N",this.requestParams.spreadOnly=this.spreadCheck.selected?"Y":"N",this.requestParams.volume=this.value.selected?"N":"Y",this.requestParams.tabIsChanged=this.tabIsChanged,this.requestParams.entityChanged=this.entityChanged,this.requestParams.autoRefresh=t?"yes":t,this.inputData.cbStart=this.startOfComms.bind(this),this.inputData.cbStop=this.endOfComms.bind(this),this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.inputData.cbResult=function(t){e.inputDataResult(t)},this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)}catch(i){console.log(i,this.moduleId,"PCM Monitor","updateData"),n.Wb.logError(i,n.Wb.AML_MODULE_ID,"Dashboard","updateData",this.errorLocation)}},e.prototype.handleDate=function(){var t=!1;if(this.validateDateField(this.startDate)){var e=o()(this.sysDateFrmSession,this.dateFormat.toUpperCase()),i=o()(this.startDate.text,this.dateFormat.toUpperCase());if(i)for(var a=0;a<this.tabs.getTabChildren().length-1;a++){var l=this.tabs.getChildAt(0);if(e=o()(l.dateValue,"YYYY-MM-DD").add(a,"days"),0==i.diff(e)){t=!0,this.tabs.selectedIndex=a;break}}this.tabIsChanged=!0,t||(this.tabs.selectedIndex=this.tabs.getTabChildren().length-1),this.tabs.getSelectedTab().id!==this.selectedTabId&&(this.states[this.selectedTabId]=this.advancedGrid.saveOpenTreeState()),this.updateData("no")}},e.prototype.validateDateField=function(t){try{var e=void 0;if(t.text&&!(e=o()(t.text,this.dateFormatUpper,!0)).isValid())return this.swtAlert.warning("Date must be in the format "+this.dateFormatUpper),!1;t.selectedDate=e.toDate()}catch(i){n.Wb.logError(i,n.Wb.SYSTEM_MODULE_ID,"PCM Monitor"," validateDateField",this.errorLocation)}return!0},e.prototype.doHelp=function(t){n.x.call("help")},e.prototype.closeHandler=function(){try{this.dispose()}catch(t){n.Wb.logError(t,n.Wb.SYSTEM_MODULE_ID,"PCM Monitor"," closeHandler",this.errorLocation)}},e.prototype.dispose=function(){try{this.advancedGrid=null,this.requestParams=null,this.inputData=null,this.jsonReader=null,this.menuAccess=null,this.selectedItem=null,this.lastRecievedJSON=null,this.prevRecievedJSON=null,n.x.call("close")}catch(t){n.Wb.logError(t,this.moduleId,"PCM Monitor","dispose",this.errorLocation)}},e.prototype.inputDataFault=function(t){var e=this;try{this.actionPath="dashboardPCM.do?",this.actionMethod="method=saveLogError",this.requestParams=[],this.requestParams.error=t.error.error,this.requestParams.text=t.error.text,this.faultData.encodeURL=!1,this.faultData.url=this.baseURL+this.actionPath+this.actionMethod,this.faultData.send(this.requestParams),this.faultData.cbResult=function(t){e.faultDateResult(t)},this.swtAlert.error(n.Wb.getPredictMessage("label.genericException",null))}catch(i){console.log(i,this.moduleId,"PCM Monitor","inputDataFault")}},e.prototype.faultDateResult=function(t){},e}(n.yb),c=[{path:"",component:u}],b=(h.l.forChild(c),function(){return function(){}}()),p=i("pMnS"),g=i("RChO"),m=i("t6HQ"),y=i("WFGK"),v=i("5FqG"),w=i("Ip0R"),R=i("gIcY"),f=i("t/Na"),C=i("sE5F"),I=i("OzfB"),M=i("T7CS"),D=i("S7LP"),S=i("6aHO"),P=i("WzUx"),L=i("A7o+"),T=i("zCE2"),x=i("Jg5P"),k=i("3R0m"),J=i("hhbb"),B=i("5rxC"),E=i("Fzqc"),G=i("21Lb"),O=i("hUWP"),N=i("3pJQ"),W=i("V9q+"),A=i("VDKW"),F=i("kXfT"),_=i("BGbe");i.d(e,"PCMMonitorModuleNgFactory",function(){return q}),i.d(e,"RenderType_PCMMonitor",function(){return Z}),i.d(e,"View_PCMMonitor_0",function(){return j}),i.d(e,"View_PCMMonitor_Host_0",function(){return H}),i.d(e,"PCMMonitorNgFactory",function(){return Y});var q=a.Gb(b,[],function(t){return a.Qb([a.Rb(512,a.n,a.vb,[[8,[p.a,g.a,m.a,y.a,v.Cb,v.Pb,v.r,v.rc,v.s,v.Ab,v.Bb,v.Db,v.qd,v.Hb,v.k,v.Ib,v.Nb,v.Ub,v.yb,v.Jb,v.v,v.A,v.e,v.c,v.g,v.d,v.Kb,v.f,v.ec,v.Wb,v.bc,v.ac,v.sc,v.fc,v.lc,v.jc,v.Eb,v.Fb,v.mc,v.Lb,v.nc,v.Mb,v.dc,v.Rb,v.b,v.ic,v.Yb,v.Sb,v.kc,v.y,v.Qb,v.cc,v.hc,v.pc,v.oc,v.xb,v.p,v.q,v.o,v.h,v.j,v.w,v.Zb,v.i,v.m,v.Vb,v.Ob,v.Gb,v.Xb,v.t,v.tc,v.zb,v.n,v.qc,v.a,v.z,v.rd,v.sd,v.x,v.td,v.gc,v.l,v.u,v.ud,v.Tb,Y]],[3,a.n],a.J]),a.Rb(4608,w.m,w.l,[a.F,[2,w.u]]),a.Rb(4608,R.c,R.c,[]),a.Rb(4608,R.p,R.p,[]),a.Rb(4608,f.j,f.p,[w.c,a.O,f.n]),a.Rb(4608,f.q,f.q,[f.j,f.o]),a.Rb(5120,f.a,function(t){return[t,new n.tb]},[f.q]),a.Rb(4608,f.m,f.m,[]),a.Rb(6144,f.k,null,[f.m]),a.Rb(4608,f.i,f.i,[f.k]),a.Rb(6144,f.b,null,[f.i]),a.Rb(4608,f.f,f.l,[f.b,a.B]),a.Rb(4608,f.c,f.c,[f.f]),a.Rb(4608,C.c,C.c,[]),a.Rb(4608,C.g,C.b,[]),a.Rb(5120,C.i,C.j,[]),a.Rb(4608,C.h,C.h,[C.c,C.g,C.i]),a.Rb(4608,C.f,C.a,[]),a.Rb(5120,C.d,C.k,[C.h,C.f]),a.Rb(5120,a.b,function(t,e){return[I.j(t,e)]},[w.c,a.O]),a.Rb(4608,M.a,M.a,[]),a.Rb(4608,D.a,D.a,[]),a.Rb(4608,S.a,S.a,[a.n,a.L,a.B,D.a,a.g]),a.Rb(4608,P.c,P.c,[a.n,a.g,a.B]),a.Rb(4608,P.e,P.e,[P.c]),a.Rb(4608,L.l,L.l,[]),a.Rb(4608,L.h,L.g,[]),a.Rb(4608,L.c,L.f,[]),a.Rb(4608,L.j,L.d,[]),a.Rb(4608,L.b,L.a,[]),a.Rb(4608,L.k,L.k,[L.l,L.h,L.c,L.j,L.b,L.m,L.n]),a.Rb(4608,P.i,P.i,[[2,L.k]]),a.Rb(4608,P.r,P.r,[P.L,[2,L.k],P.i]),a.Rb(4608,P.t,P.t,[]),a.Rb(4608,P.w,P.w,[]),a.Rb(1073742336,h.l,h.l,[[2,h.r],[2,h.k]]),a.Rb(1073742336,w.b,w.b,[]),a.Rb(1073742336,R.n,R.n,[]),a.Rb(1073742336,R.l,R.l,[]),a.Rb(1073742336,T.a,T.a,[]),a.Rb(1073742336,x.a,x.a,[]),a.Rb(1073742336,R.e,R.e,[]),a.Rb(1073742336,k.a,k.a,[]),a.Rb(1073742336,L.i,L.i,[]),a.Rb(1073742336,P.b,P.b,[]),a.Rb(1073742336,f.e,f.e,[]),a.Rb(1073742336,f.d,f.d,[]),a.Rb(1073742336,C.e,C.e,[]),a.Rb(1073742336,J.b,J.b,[]),a.Rb(1073742336,B.b,B.b,[]),a.Rb(1073742336,I.c,I.c,[]),a.Rb(1073742336,E.a,E.a,[]),a.Rb(1073742336,G.d,G.d,[]),a.Rb(1073742336,O.c,O.c,[]),a.Rb(1073742336,N.a,N.a,[]),a.Rb(1073742336,W.a,W.a,[[2,I.g],a.O]),a.Rb(1073742336,A.b,A.b,[]),a.Rb(1073742336,F.a,F.a,[]),a.Rb(1073742336,_.b,_.b,[]),a.Rb(1073742336,n.Tb,n.Tb,[]),a.Rb(1073742336,b,b,[]),a.Rb(256,f.n,"XSRF-TOKEN",[]),a.Rb(256,f.o,"X-XSRF-TOKEN",[]),a.Rb(256,"config",{},[]),a.Rb(256,L.m,void 0,[]),a.Rb(256,L.n,void 0,[]),a.Rb(256,"popperDefaults",{},[]),a.Rb(1024,h.i,function(){return[[{path:"",component:u}]]},[])])}),V=[[".square[_ngcontent-%COMP%]{padding:10px;margin:5px;border:1px solid #fff;box-shadow:1px 1px 1px 1px #888;color:#fff}.myClass[_ngcontent-%COMP%]{border:1px solid red}"]],Z=a.Hb({encapsulation:0,styles:V,data:{}});function j(t){return a.dc(0,[a.Zb(402653184,1,{_container:0}),a.Zb(402653184,2,{advancedGrid:0}),a.Zb(402653184,3,{tabs:0}),a.Zb(402653184,4,{displayContainerToday:0}),a.Zb(402653184,5,{displayContainerTodayPlus:0}),a.Zb(402653184,6,{displayContainerTodayPlusPlus:0}),a.Zb(402653184,7,{displayContainerTodayPlusThree:0}),a.Zb(402653184,8,{displayContainerTodayPlusFour:0}),a.Zb(402653184,9,{displayContainerTodayPlusFive:0}),a.Zb(402653184,10,{displayContainerTodayPlusSix:0}),a.Zb(402653184,11,{displayContainerTodayPlusSeven:0}),a.Zb(402653184,12,{displayContainerSelected:0}),a.Zb(402653184,13,{loadingImage:0}),a.Zb(402653184,14,{entityCombo:0}),a.Zb(402653184,15,{displayValueGroup:0}),a.Zb(402653184,16,{value:0}),a.Zb(402653184,17,{volume:0}),a.Zb(402653184,18,{dateLabel:0}),a.Zb(402653184,19,{entityLabel:0}),a.Zb(402653184,20,{selectedEntity:0}),a.Zb(402653184,21,{applyThresholdLabel:0}),a.Zb(402653184,22,{applyMultiplierLabel:0}),a.Zb(402653184,23,{spreadLabel:0}),a.Zb(402653184,24,{valueVolume:0}),a.Zb(402653184,25,{startDate:0}),a.Zb(402653184,26,{lastRef:0}),a.Zb(402653184,27,{lastRefTime:0}),a.Zb(402653184,28,{applyThresholdCheck:0}),a.Zb(402653184,29,{applyMultiplierCheck:0}),a.Zb(402653184,30,{spreadCheck:0}),a.Zb(402653184,31,{rateButton:0}),a.Zb(402653184,32,{closeButton:0}),a.Zb(402653184,33,{refreshButton:0}),a.Zb(402653184,34,{divColor:0}),a.Zb(402653184,35,{entityMoreItemsButton:0}),a.Zb(402653184,36,{printIcon:0}),a.Zb(402653184,37,{dataExport:0}),(t()(),a.Jb(37,0,null,null,111,"SwtModule",[["height","100%"],["width","100%"]],null,[[null,"creationComplete"]],function(t,e,i){var a=!0,l=t.component;"creationComplete"===e&&(a=!1!==l.onLoad()&&a);return a},v.ad,v.hb)),a.Ib(38,4440064,null,0,n.yb,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},{creationComplete:"creationComplete"}),(t()(),a.Jb(39,0,null,0,109,"VBox",[["height","100%"],["paddingBottom","5"],["paddingLeft","5"],["paddingRight","5"],["paddingTop","5"],["width","100%"]],null,null,null,v.od,v.vb)),a.Ib(40,4440064,null,0,n.ec,[a.r,n.i,a.T],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"],paddingBottom:[3,"paddingBottom"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(t()(),a.Jb(41,0,null,0,107,"Grid",[["height","100%"],["width","100%"]],null,null,null,v.Cc,v.H)),a.Ib(42,4440064,null,0,n.z,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(43,0,null,0,59,"GridRow",[["height","18%"],["width","100%"]],null,null,null,v.Bc,v.J)),a.Ib(44,4440064,null,0,n.B,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(45,0,null,0,57,"GridItem",[["height","100%"],["width","100%"]],null,null,null,v.Ac,v.I)),a.Ib(46,4440064,null,0,n.A,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(47,0,null,0,55,"SwtCanvas",[["height","100%"],["minWidth","750"],["width","100%"]],null,null,null,v.Nc,v.U)),a.Ib(48,4440064,null,0,n.db,[a.r,n.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),a.Jb(49,0,null,0,53,"HBox",[["height","100%"],["width","100%"]],null,null,null,v.Dc,v.K)),a.Ib(50,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(51,0,null,0,9,"HBox",[["width","100%"]],null,null,null,v.Dc,v.K)),a.Ib(52,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"]},null),(t()(),a.Jb(53,0,null,0,1,"SwtLabel",[["id","entityLabel"],["width","80"]],null,null,null,v.Yc,v.fb)),a.Ib(54,4440064,[[19,4],["entityLabel",4]],0,n.vb,[a.r,n.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),a.Jb(55,0,null,0,1,"SwtComboBox",[["dataLabel","entityList"],["id","entityCombo"]],null,[[null,"change"],["window","mousewheel"]],function(t,e,i){var l=!0,n=t.component;"window:mousewheel"===e&&(l=!1!==a.Tb(t,56).mouseWeelEventHandler(i.target)&&l);"change"===e&&(l=!1!==n.changeCombo(a.Tb(t,56),a.Tb(t,58),a.Tb(t,60))&&l);return l},v.Pc,v.W)),a.Ib(56,4440064,[[14,4],["entityCombo",4]],0,n.gb,[a.r,n.i],{dataLabel:[0,"dataLabel"],id:[1,"id"]},{change_:"change"}),(t()(),a.Jb(57,0,null,0,1,"SwtButton",[["buttonMode","false"],["enabled","false"],["id","entityMoreItemsButton"],["width","22"]],null,[[null,"click"]],function(t,e,i){var l=!0,n=t.component;"click"===e&&(l=!1!==n.multipleListSelect(a.Tb(t,56).id,a.Tb(t,60))&&l);return l},v.Mc,v.T)),a.Ib(58,4440064,[[35,4],["entityMoreItemsButton",4]],0,n.cb,[a.r,n.i],{id:[0,"id"],width:[1,"width"],enabled:[2,"enabled"],buttonMode:[3,"buttonMode"]},{onClick_:"click"}),(t()(),a.Jb(59,0,null,0,1,"SwtLabel",[["id","selectedEntity"],["paddingLeft","10"],["text",""],["textAlign","left"]],null,null,null,v.Yc,v.fb)),a.Ib(60,4440064,[[20,4],["selectedEntity",4]],0,n.vb,[a.r,n.i],{id:[0,"id"],textAlign:[1,"textAlign"],paddingLeft:[2,"paddingLeft"],text:[3,"text"]},null),(t()(),a.Jb(61,0,null,0,41,"VBox",[["height","100%"],["verticalGap","0"],["width","50%"]],null,null,null,v.od,v.vb)),a.Ib(62,4440064,null,0,n.ec,[a.r,n.i,a.T],{verticalGap:[0,"verticalGap"],width:[1,"width"],height:[2,"height"]},null),(t()(),a.Jb(63,0,null,0,10,"HBox",[["height","20%"],["width","100%"]],null,null,null,v.Dc,v.K)),a.Ib(64,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(65,0,null,0,5,"HBox",[["height","100%"],["width","90%"]],null,null,null,v.Dc,v.K)),a.Ib(66,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(67,0,null,0,1,"SwtLabel",[["id","dateLabel"],["width","82"]],null,null,null,v.Yc,v.fb)),a.Ib(68,4440064,[[18,4],["dateLabel",4]],0,n.vb,[a.r,n.i],{id:[0,"id"],width:[1,"width"]},null),(t()(),a.Jb(69,0,null,0,1,"SwtDateField",[["id","startDate"],["restrict","0-9/"],["width","70"]],null,[[null,"change"]],function(t,e,i){var a=!0,l=t.component;"change"===e&&(a=!1!==l.handleDate()&&a);return a},v.Tc,v.ab)),a.Ib(70,4308992,[[25,4],["startDate",4]],0,n.lb,[a.r,n.i,a.T],{restrict:[0,"restrict"],toolTip:[1,"toolTip"],id:[2,"id"],width:[3,"width"]},{changeEventOutPut:"change"}),(t()(),a.Jb(71,0,null,0,2,"HBox",[["height","20%"],["width","10%"]],null,null,null,v.Dc,v.K)),a.Ib(72,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(73,0,[[34,0],["divColor",1]],0,0,"div",[["class","square"],["id","divColor"]],null,null,null,null,null)),(t()(),a.Jb(74,0,null,0,5,"HBox",[["height","20%"],["width","100%"]],null,null,null,v.Dc,v.K)),a.Ib(75,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(76,0,null,0,1,"SwtLabel",[["width"," 172"]],null,null,null,v.Yc,v.fb)),a.Ib(77,4440064,[[21,4],["applyThresholdLabel",4]],0,n.vb,[a.r,n.i],{width:[0,"width"]},null),(t()(),a.Jb(78,0,null,0,1,"SwtCheckBox",[["id","applyThresholdCheck"]],null,[[null,"change"]],function(t,e,i){var a=!0,l=t.component;"change"===e&&(a=!1!==l.updateData(i)&&a);return a},v.Oc,v.V)),a.Ib(79,4440064,[[28,4],["applyThresholdCheck",4]],0,n.eb,[a.r,n.i],{id:[0,"id"]},{change_:"change"}),(t()(),a.Jb(80,0,null,0,5,"HBox",[["height","20%"],["width","100%"]],null,null,null,v.Dc,v.K)),a.Ib(81,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(82,0,null,0,1,"SwtLabel",[["width","172"]],null,null,null,v.Yc,v.fb)),a.Ib(83,4440064,[[22,4],["applyMultiplierLabel",4]],0,n.vb,[a.r,n.i],{width:[0,"width"]},null),(t()(),a.Jb(84,0,null,0,1,"SwtCheckBox",[["id","applyMultiplierCheck"]],null,[[null,"change"]],function(t,e,i){var a=!0,l=t.component;"change"===e&&(a=!1!==l.updateData(i)&&a);return a},v.Oc,v.V)),a.Ib(85,4440064,[[29,4],["applyMultiplierCheck",4]],0,n.eb,[a.r,n.i],{id:[0,"id"]},{change_:"change"}),(t()(),a.Jb(86,0,null,0,5,"HBox",[["height","20%"],["width","100%"]],null,null,null,v.Dc,v.K)),a.Ib(87,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(88,0,null,0,1,"SwtLabel",[["width","172"]],null,null,null,v.Yc,v.fb)),a.Ib(89,4440064,[[23,4],["spreadLabel",4]],0,n.vb,[a.r,n.i],{width:[0,"width"]},null),(t()(),a.Jb(90,0,null,0,1,"SwtCheckBox",[["id","spreadCheck"],["width","150"]],null,[[null,"change"]],function(t,e,i){var a=!0,l=t.component;"change"===e&&(a=!1!==l.updateData(i)&&a);return a},v.Oc,v.V)),a.Ib(91,4440064,[[30,4],["spreadCheck",4]],0,n.eb,[a.r,n.i],{id:[0,"id"],width:[1,"width"]},{change_:"change"}),(t()(),a.Jb(92,0,null,0,10,"HBox",[["height","20%"],["width","100%"]],null,null,null,v.Dc,v.K)),a.Ib(93,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(94,0,null,0,1,"SwtLabel",[["width","172"]],null,null,null,v.Yc,v.fb)),a.Ib(95,4440064,[[24,4],["valueVolume",4]],0,n.vb,[a.r,n.i],{width:[0,"width"]},null),(t()(),a.Jb(96,0,null,0,6,"SwtRadioButtonGroup",[["align","horizontal"],["id","displayValueGroup"]],null,[[null,"change"]],function(t,e,i){var a=!0,l=t.component;"change"===e&&(a=!1!==l.changeRadioGroup()&&a);return a},v.ed,v.lb)),a.Ib(97,4440064,[[15,4],["displayValueGroup",4]],1,n.Hb,[f.c,a.r,n.i],{id:[0,"id"],align:[1,"align"]},{change_:"change"}),a.Zb(603979776,38,{radioItems:1}),(t()(),a.Jb(99,0,null,0,1,"SwtRadioItem",[["groupName","displayValueGroup"],["id","value"],["selected","true"],["value","N"],["width","90"]],null,null,null,v.fd,v.mb)),a.Ib(100,4440064,[[38,4],[16,4],["value",4]],0,n.Ib,[a.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"],selected:[4,"selected"]},null),(t()(),a.Jb(101,0,null,0,1,"SwtRadioItem",[["groupName","displayValueGroup"],["id","volume"],["value","Y"],["width","90"]],null,null,null,v.fd,v.mb)),a.Ib(102,4440064,[[38,4],[17,4],["volume",4]],0,n.Ib,[a.r,n.i],{id:[0,"id"],width:[1,"width"],groupName:[2,"groupName"],value:[3,"value"]},null),(t()(),a.Jb(103,0,null,0,6,"GridRow",[["height","3%"],["width","100%"]],null,null,null,v.Bc,v.J)),a.Ib(104,4440064,null,0,n.B,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(105,0,null,0,4,"GridItem",[["height","100%"],["paddingTop","5"],["width","100%"]],null,null,null,v.Ac,v.I)),a.Ib(106,4440064,null,0,n.A,[a.r,n.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),a.Jb(107,0,null,0,2,"SwtTabNavigator",[["height","100%"],["id","tabs"],["width","100%"]],null,[[null,"onChange"]],function(t,e,i){var a=!0,l=t.component;"onChange"===e&&(a=!1!==l.tabIndexChangeHandler()&&a);return a},v.id,v.pb)),a.Ib(108,4440064,[[3,4],["tabs",4]],1,n.Ob,[a.r,n.i,a.k],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},{onChange_:"onChange"}),a.Zb(603979776,39,{tabChildren:1}),(t()(),a.Jb(110,0,null,0,7,"GridRow",[["height","73%"],["width","100%"]],null,null,null,v.Bc,v.J)),a.Ib(111,4440064,null,0,n.B,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(112,0,null,0,5,"GridItem",[["height","100%"],["width","100%"]],null,null,null,v.Ac,v.I)),a.Ib(113,4440064,null,0,n.A,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(114,0,null,0,3,"SwtCanvas",[["height","100%"],["minWidth","750"],["width","100%"]],null,null,null,v.Nc,v.U)),a.Ib(115,4440064,null,0,n.db,[a.r,n.i],{width:[0,"width"],height:[1,"height"],minWidth:[2,"minWidth"]},null),(t()(),a.Jb(116,0,null,0,1,"AdvancedDataGrid",[["height","100%"],["id","advancedDataGrid"],["width","100%"]],null,null,null,v.uc,v.B)),a.Ib(117,4440064,[[2,4],["advancedDataGrid",4]],0,n.a,[a.r,n.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(t()(),a.Jb(118,0,null,0,30,"GridRow",[["height","6%"],["width","100%"]],null,null,null,v.Bc,v.J)),a.Ib(119,4440064,null,0,n.B,[a.r,n.i],{width:[0,"width"],height:[1,"height"]},null),(t()(),a.Jb(120,0,null,0,28,"GridItem",[["height","100%"],["paddingTop","5"],["width","100%"]],null,null,null,v.Ac,v.I)),a.Ib(121,4440064,null,0,n.A,[a.r,n.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(t()(),a.Jb(122,0,null,0,26,"SwtCanvas",[["height","100%"],["id","canvasButtons"],["minWidth","750"],["width","100%"]],null,null,null,v.Nc,v.U)),a.Ib(123,4440064,null,0,n.db,[a.r,n.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],minWidth:[3,"minWidth"]},null),(t()(),a.Jb(124,0,null,0,24,"HBox",[["width","100%"]],null,null,null,v.Dc,v.K)),a.Ib(125,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"]},null),(t()(),a.Jb(126,0,null,0,7,"HBox",[["paddingLeft","5"],["width","76%"]],null,null,null,v.Dc,v.K)),a.Ib(127,4440064,null,0,n.C,[a.r,n.i],{width:[0,"width"],paddingLeft:[1,"paddingLeft"]},null),(t()(),a.Jb(128,0,null,0,1,"SwtButton",[["id","refreshButton"]],null,[[null,"click"]],function(t,e,i){var a=!0,l=t.component;"click"===e&&(a=!1!==l.updateData(i)&&a);return a},v.Mc,v.T)),a.Ib(129,4440064,[[33,4],["refreshButton",4]],0,n.cb,[a.r,n.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(t()(),a.Jb(130,0,null,0,1,"SwtButton",[["id","rateButton"]],null,[[null,"click"]],function(t,e,i){var a=!0,l=t.component;"click"===e&&(a=!1!==l.optionsHandler()&&a);return a},v.Mc,v.T)),a.Ib(131,4440064,[[31,4],["rateButton",4]],0,n.cb,[a.r,n.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),a.Jb(132,0,null,0,1,"SwtButton",[["id","closeButton"]],null,[[null,"click"]],function(t,e,i){var a=!0,l=t.component;"click"===e&&(a=!1!==l.closeHandler()&&a);return a},v.Mc,v.T)),a.Ib(133,4440064,[[32,4],["closeButton",4]],0,n.cb,[a.r,n.i],{id:[0,"id"]},{onClick_:"click"}),(t()(),a.Jb(134,0,null,0,14,"HBox",[["horizontalAlign","right"],["width","22%"]],null,null,null,v.Dc,v.K)),a.Ib(135,4440064,null,0,n.C,[a.r,n.i],{horizontalAlign:[0,"horizontalAlign"],width:[1,"width"]},null),(t()(),a.Jb(136,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","lastRef"]],null,null,null,v.Yc,v.fb)),a.Ib(137,4440064,[[26,4],["lastRef",4]],0,n.vb,[a.r,n.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),a.Jb(138,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","lastRefTime"]],null,null,null,v.Yc,v.fb)),a.Ib(139,4440064,[[27,4],["lastRefTime",4]],0,n.vb,[a.r,n.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(t()(),a.Jb(140,0,null,0,1,"SwtLoadingImage",[],null,null,null,v.Zc,v.gb)),a.Ib(141,114688,[[13,4],["loadingImage",4]],0,n.xb,[a.r],null,null),(t()(),a.Jb(142,0,null,0,2,"div",[],null,null,null,null,null)),(t()(),a.Jb(143,0,null,null,1,"DataExport",[["id","dataExport"]],null,null,null,v.Sc,v.Z)),a.Ib(144,4440064,[[37,4],["dataExport",4]],0,n.kb,[n.i,a.r],{id:[0,"id"]},null),(t()(),a.Jb(145,0,null,0,1,"SwtButton",[["id","printIcon"],["styleName","printIcon"]],null,[[null,"click"]],function(t,e,i){var a=!0,l=t.component;"click"===e&&(a=!1!==l.printPage(i)&&a);return a},v.Mc,v.T)),a.Ib(146,4440064,[[36,4],["printIcon",4]],0,n.cb,[a.r,n.i],{id:[0,"id"],styleName:[1,"styleName"],buttonMode:[2,"buttonMode"]},{onClick_:"click"}),(t()(),a.Jb(147,0,null,0,1,"HBox",[],null,null,null,v.Dc,v.K)),a.Ib(148,4440064,null,0,n.C,[a.r,n.i],null,null)],function(t,e){var i=e.component;t(e,38,0,"100%","100%");t(e,40,0,"100%","100%","5","5","5","5");t(e,42,0,"100%","100%");t(e,44,0,"100%","18%");t(e,46,0,"100%","100%");t(e,48,0,"100%","100%","750");t(e,50,0,"100%","100%");t(e,52,0,"100%");t(e,54,0,"entityLabel","80");t(e,56,0,"entityList","entityCombo");t(e,58,0,"entityMoreItemsButton","22","false","false");t(e,60,0,"selectedEntity","left","10","");t(e,62,0,"0","50%","100%");t(e,64,0,"100%","20%");t(e,66,0,"90%","100%");t(e,68,0,"dateLabel","82");t(e,70,0,"0-9/",a.Lb(1,"Enter value Date ",i.dateFormatUpper,""),"startDate","70");t(e,72,0,"10%","20%");t(e,75,0,"100%","20%");t(e,77,0," 172");t(e,79,0,"applyThresholdCheck");t(e,81,0,"100%","20%");t(e,83,0,"172");t(e,85,0,"applyMultiplierCheck");t(e,87,0,"100%","20%");t(e,89,0,"172");t(e,91,0,"spreadCheck","150");t(e,93,0,"100%","20%");t(e,95,0,"172");t(e,97,0,"displayValueGroup","horizontal");t(e,100,0,"value","90","displayValueGroup","N","true");t(e,102,0,"volume","90","displayValueGroup","Y");t(e,104,0,"100%","3%");t(e,106,0,"100%","100%","5");t(e,108,0,"tabs","100%","100%");t(e,111,0,"100%","73%");t(e,113,0,"100%","100%");t(e,115,0,"100%","100%","750");t(e,117,0,"advancedDataGrid","100%","100%");t(e,119,0,"100%","6%");t(e,121,0,"100%","100%","5");t(e,123,0,"canvasButtons","100%","100%","750");t(e,125,0,"100%");t(e,127,0,"76%","5");t(e,129,0,"refreshButton",!0);t(e,131,0,"rateButton");t(e,133,0,"closeButton");t(e,135,0,"right","22%");t(e,137,0,"lastRef","normal");t(e,139,0,"lastRefTime","normal"),t(e,141,0);t(e,144,0,"dataExport");t(e,146,0,"printIcon","printIcon",!0),t(e,148,0)},null)}function H(t){return a.dc(0,[(t()(),a.Jb(0,0,null,null,1,"app-pcdashboard2",[],null,null,null,j,Z)),a.Ib(1,4440064,null,0,u,[n.i,a.r],null,null)],function(t,e){t(e,1,0)},null)}var Y=a.Fb("app-pcdashboard2",u,H,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);