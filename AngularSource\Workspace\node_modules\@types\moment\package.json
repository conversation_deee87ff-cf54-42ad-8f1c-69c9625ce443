{"_from": "@types/moment@2.13.0", "_id": "@types/moment@2.13.0", "_inBundle": false, "_integrity": "sha512-DyuyYGpV6r+4Z1bUznLi/Y7HpGn4iQ4IVcGn8zrr1P4KotKLdH0sbK1TFR6RGyX6B+G8u83wCzL+bpawKU/hdQ==", "_location": "/@types/moment", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@types/moment@2.13.0", "name": "@types/moment", "escapedName": "@types%2fmoment", "scope": "@types", "rawSpec": "2.13.0", "saveSpec": null, "fetchSpec": "2.13.0"}, "_requiredBy": ["#DEV:/"], "_resolved": "https://registry.npmjs.org/@types/moment/-/moment-2.13.0.tgz", "_shasum": "604ebd189bc3bc34a1548689404e61a2a4aac896", "_spec": "@types/moment@2.13.0", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": "", "bugs": {"url": "https://github.com/moment/moment/issues"}, "bundleDependencies": false, "dependencies": {"moment": "*"}, "deprecated": "This is a stub types definition for Moment (https://github.com/moment/moment). Moment provides its own type definitions, so you don't need @types/moment installed!", "description": "Stub TypeScript definitions entry for Moment, which provides its own types definitions", "homepage": "https://github.com/moment/moment#readme", "license": "MIT", "main": "", "name": "@types/moment", "repository": {"type": "git", "url": "git+https://github.com/moment/moment.git"}, "scripts": {}, "version": "2.13.0"}