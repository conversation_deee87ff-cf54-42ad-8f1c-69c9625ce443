{"_from": "@angular/common@7.2.4", "_id": "@angular/common@7.2.4", "_inBundle": false, "_integrity": "sha512-3/i8RtnLTx/90gJHk5maE8zwsSiHgHvLItaa0qVfNlWiU0eCId/PL6TgDkut5vN9SQYL0oxhxFaVd35HmwsmuQ==", "_location": "/@angular/common", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/common@7.2.4", "name": "@angular/common", "escapedName": "@angular%2fcommon", "scope": "@angular", "rawSpec": "7.2.4", "saveSpec": null, "fetchSpec": "7.2.4"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@angular/common/-/common-7.2.4.tgz", "_shasum": "9f1ed530e5dc7613a263e015c203ead390d50336", "_spec": "@angular/common@7.2.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "angular"}, "bugs": {"url": "https://github.com/angular/angular/issues"}, "bundleDependencies": false, "dependencies": {"tslib": "^1.9.0"}, "deprecated": false, "description": "Angular - commonly needed directives and services", "es2015": "./fesm2015/common.js", "esm2015": "./esm2015/common.js", "esm5": "./esm5/common.js", "fesm2015": "./fesm2015/common.js", "fesm5": "./fesm5/common.js", "homepage": "https://github.com/angular/angular#readme", "license": "MIT", "locales": "locales", "main": "./bundles/common.umd.js", "module": "./fesm5/common.js", "name": "@angular/common", "ng-update": {"packageGroup": ["@angular/core", "@angular/bazel", "@angular/common", "@angular/compiler", "@angular/compiler-cli", "@angular/animations", "@angular/elements", "@angular/platform-browser", "@angular/platform-browser-dynamic", "@angular/forms", "@angular/http", "@angular/platform-server", "@angular/platform-webworker", "@angular/platform-webworker-dynamic", "@angular/upgrade", "@angular/router", "@angular/language-service", "@angular/service-worker"]}, "peerDependencies": {"rxjs": "^6.0.0", "@angular/core": "7.2.4"}, "repository": {"type": "git", "url": "git+https://github.com/angular/angular.git"}, "sideEffects": false, "typings": "./common.d.ts", "version": "7.2.4"}