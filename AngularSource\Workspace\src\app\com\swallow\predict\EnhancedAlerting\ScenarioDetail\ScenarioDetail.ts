import { Component, ElementRef, ModuleWithProviders, NgModule, OnInit, ViewChild } from '@angular/core';
import { RouterModule, Routes } from "@angular/router";
import { Alert, CommonService, ExternalInterface, HTTPComms,Encryptor, JSONReader, StringUtils, SwtAlert, SwtButton, SwtCheckBox, SwtLabel, SwtLoadingImage, SwtModule, SwtTabNavigator, SwtTextArea, SwtTextInput, SwtToolBoxModule, SwtUtil, Tab } from 'swt-tool-box';
import { Events } from "./Tabs/Events/Events";
import { General } from "./Tabs/General/General";
import { GuiHighlight } from "./Tabs/GuiHighlight/GuiHighlight";
import { Identification } from "./Tabs/Identification/Identification";
import { Instance } from './Tabs/Instance/Instance';
declare var require: any;
var convert = require('xml-js');
const $ = require('jquery');
@Component({
  selector: 'app-scenario-detail',
  templateUrl: './ScenarioDetail.html',
  styleUrls: ['./ScenarioDetail.css']
})
export class ScenarioDetail extends SwtModule implements OnInit {

  @ViewChild('scenarioIdLbl') scenarioIdLbl: SwtLabel;
  @ViewChild('systemLbl') systemLbl: SwtLabel;
  @ViewChild('titleLbl') titleLbl: SwtLabel;
  @ViewChild('activeLbl') activeLbl: SwtLabel;
  @ViewChild('desciptionLbl') desciptionLbl: SwtLabel;

  @ViewChild('scenarioIdTxt') scenarioIdTxt: SwtTextInput;
  @ViewChild('titleTxt') titleTxt: SwtTextInput;
  @ViewChild('descriptionTxt') descriptionTxt: SwtTextArea;

  @ViewChild('systemCheck') systemCheck: SwtCheckBox;
  @ViewChild('activeCheck') activeCheck: SwtCheckBox;

  @ViewChild('tabNavigator') tabNavigator: SwtTabNavigator;
  @ViewChild('saveButton') saveButton: SwtButton;
  @ViewChild('cancelButton') cancelButton: SwtButton;
  @ViewChild('loadingImg') loadingImg: SwtLoadingImage;

  @ViewChild('generalTab') generalTab: General;
  @ViewChild('identificationTab') identificationTab: Identification;
  @ViewChild('instancesTab') instancesTab: Instance;
  @ViewChild('guiHighlightTab') guiHighlightTab: GuiHighlight;
  @ViewChild('eventsTab') eventsTab: Events;


  private swtalert: SwtAlert;
  public general: Tab;
  public identification: Tab;
  public instances: Tab;
  public guiHighlight: Tab;
  public events: Tab;
  public methodName: string = "";
  public defaultVal = [];
  public savedAlertInstCols = [];
  public savedResolutionRefCols = [];
  public defaultValString: string = "";
  public selectedFacilityId: string = "";

  /**
   * Communication Objects
   */
  private inputData: HTTPComms = new HTTPComms(this.commonService);
  private sendData: HTTPComms = new HTTPComms(this.commonService);
  private requestParams = [];
  public baseURL = SwtUtil.getBaseURL();
  private actionPath = "";
  private actionMethod = "";
  public jsonReader: JSONReader = new JSONReader();
  public lastRecievedJSON;
  public prevRecievedJSON;
  public jsonReader1: JSONReader = new JSONReader();
  public lastRecievedJSON1;
  public prevRecievedJSON1;
  private selectedScenarioID: string = "";
  private selectedSystemFlag: string = "";
  private menuAccessId: string = "";
  private fromAdvanced: string = "";
  public operationsList = [];
  public defaultQuery="";
  //to be used for combo dataprovider/ multiselect combo
  public defaultHost="";
  public defaultEntity="";
  public defaultCcy="";
  public defaultAmount="";
  public defaultAccount="";
  public defaultSign="";
  public defaultValueDate="";
  public defaultOtherId="";
  public defaultOtherIdType="";
  public defaultUniqueExp="";
   //to be used for multiselect combo
  public defaultMvt="";
  public defaultMatchId="";
  public defaultSweepId="";
  public defaultPay="";
  public defaultCustomTreeLevel1="";
  public defaultCustomTreeLevel2="";
  public gridComboVal; 
  public gridComboTxtVal; 
  public gridComboNbrVal; 
  public gridComboDateVal; 
  public defaultOptions = [];
  private scheduleParams;
  private columns;
  private nbrColumns;
  private txtColumns;
  private dateColumns;
  private scheduleGenData = [];
  private typeSelectedValue;
  private queryColumns;
  private optionIdTypes;
  public provider = [{ type: "", value: 0, selected: 0, content: "" }];
  public savedTreeProvider = [{ type: "", value: 0, selected: 0, content: "" }];
  public alertInstProvider = [];
  public savedAlertInstProvider = [];
  private savedTooltip="";
  private eventItemLimit=3;
  private savedQuery;
  public savedHostId;
  public savedEntityId;
  public savedCcyId;
  public savedAmount;
  public savedIdentRefCols;
  public savedAcctId;
  public savedValueDate;
  public savedSign;
  public savedMvtId;
  public savedMatchId;
  public  savedSwpId;
  public savedPayId;
  public savedOtherId;
  public savedOtherIdType;
  public savedTreeBreakDown1;
  public savedTreeBreakDown2;
  public savedAlertInstanceCols;
  public savedEventRefCols;
  public savedOtherIdTypeDesc;
  public lockIcon;
  public unlockIcon;
  public comboSelects;
  constructor(private commonService: CommonService, private element: ElementRef) {
    super(element, commonService);
    this.swtalert = new SwtAlert(commonService);
  }

  ngOnInit() {
    this.scenarioIdLbl.text = SwtUtil.getPredictMessage("scenario.scenarioId", null)+"*";
    this.titleLbl.text = SwtUtil.getPredictMessage("scenario.title", null)+"*";
    this.desciptionLbl.text = SwtUtil.getPredictMessage("scenario.description", null);
    this.systemLbl.text = SwtUtil.getPredictMessage("scenario.system", null);
    this.activeLbl.text = SwtUtil.getPredictMessage("scenario.active", null);
    this.saveButton.label = SwtUtil.getPredictMessage("button.save", null);
    this.cancelButton.label = SwtUtil.getPredictMessage("button.cancel", null);
    /***Tooltips***/
    this.scenarioIdTxt.toolTip = SwtUtil.getPredictMessage("tooltip.enterScenario", null);
    this.titleTxt.toolTip = SwtUtil.getPredictMessage("tooltip.enterScenarioTitle", null);
    this.descriptionTxt.toolTip = SwtUtil.getPredictMessage("tooltip.Description", null);
    this.activeCheck.toolTip = SwtUtil.getPredictMessage("tooltip.activeFlag", null);
    this.systemCheck.toolTip = SwtUtil.getPredictMessage("tooltip.systemFlag", null);
    this.saveButton.toolTip = SwtUtil.getPredictMessage("tooltip.saveExit", null);
    this.cancelButton.toolTip = SwtUtil.getPredictMessage("tooltip.closeWindow", null);

    this.scenarioIdTxt.required = true;
    this.titleTxt.required = true;


  }

  enableDisableFields(status: string) {
    if (status == "systemCheck") {
      this.scenarioIdTxt.enabled = false;
      this.titleTxt.enabled = false;
      this.descriptionTxt.editable = false;
      this.activeCheck.enabled = false;
      this.activeLbl.enabled = false;
      /**GeneralTab**/
      this.generalTab.recordCheck.enabled = false;
      this.generalTab.categoryCombo.enabled = false;
      this.generalTab.runTxt.enabled = false;
      this.generalTab.startTxt.enabled = false;
      this.generalTab.endTxt.enabled = false;
      this.generalTab.cyclic.enabled= false;
      this.generalTab.createInst.enabled = false;
      this.generalTab.scheduled.enabled= false;
      this.generalTab.defParams.enabled = false;
      this.generalTab.defParams.buttonMode = false;
      this.generalTab.addButton.enabled = false;
      this.generalTab.addButton.buttonMode = false;
      this.generalTab.changeButton.enabled = false;
      this.generalTab.changeButton.buttonMode = false;
      this.generalTab.deleteButton.enabled = false;
      this.generalTab.deleteButton.buttonMode = false;
      this.generalTab.configButton.enabled = false;
      this.generalTab.configButton.buttonMode = false;
      /***identificationTab**/
      this.identificationTab.baseQuery.editable = false;
      this.identificationTab.hostColCombo.enabled = false;
      this.identificationTab.entityColCombo.enabled = false;
      this.identificationTab.ccyColCombo.enabled = false;
      this.identificationTab.amountColCombo.enabled = false;
      this.identificationTab.genericCheck.enabled = false;
      this.identificationTab.facilityCombo.enabled = false;
      this.identificationTab.refColumnCombo.isDropdownDisabled = true;
      this.identificationTab.paramValueTxt.enabled = false;
      this.instancesTab.alertInstanceColumnCombo.isDropdownDisabled = true;
      /***instanceTab**/
      this.instancesTab.uniqueExpression.editable = false;
      this.instancesTab.acctIdCombo.enabled = false;
      this.instancesTab.valueDateCombo.enabled = false;
      this.instancesTab.signColCombo.enabled = false;
      this.instancesTab.mvtColCombo.enabled = false;
      this.instancesTab.matchColCombo.enabled = false;
      this.instancesTab.sweepColCombo.enabled = false;
      this.instancesTab.payColCombo.enabled = false;
      this.instancesTab.otherIdColCombo.enabled = false;
      this.instancesTab.otherIdTypeCombo.enabled = false;
      this.instancesTab.treeBreakDown1Combo.enabled = false;
      this.instancesTab.treeBreakDown2Combo.enabled = false;
      this.instancesTab.instExpTxt.enabled = false;
      this.instancesTab.radioNo.enabled = false;
      this.instancesTab.radioAfter.enabled = false;
      this.instancesTab.afterMinTxt.enabled = false;
      /***instanceTab**/
    } else if (status == "change") {
      this.scenarioIdTxt.enabled = false;
      /**IdentificationTab***/
      this.identificationTab.entityColCombo.enabled = (this.identificationTab.entityColCombo.selectedLabel.length > 0 ||this.defaultHost ) && !this.systemCheck.selected;
      this.identificationTab.ccyColCombo.enabled = (this.identificationTab.ccyColCombo.selectedLabel.length > 0 || this.defaultEntity) && !this.systemCheck.selected;
      this.identificationTab.refColumnCombo.isDropdownDisabled = !(this.defaultVal.length>0 && !this.systemCheck.selected);
      this.identificationTab.paramValueTxt.enabled = this.identificationTab.paramValueTxt.text.length > 0 && !this.systemCheck.selected;
    } else if (status == "view") {
      this.scenarioIdTxt.enabled = false;
      this.titleTxt.enabled = false;
      this.descriptionTxt.editable = false;
      this.activeCheck.enabled = false;
      this.activeLbl.enabled = false;
      this.saveButton.enabled = false;
      /**General Tab**/
      this.generalTab.categoryCombo.enabled = false;
      this.generalTab.orderTxt.enabled = false;
      this.generalTab.runTxt.enabled = false;
      this.generalTab.startTxt.enabled = false;
      this.generalTab.endTxt.enabled = false;
      this.generalTab.emailTxt.enabled = false;
      this.generalTab.cyclic.enabled = false;
      this.generalTab.scheduled.enabled = false;
      this.generalTab.createInst.enabled = false;
      this.generalTab.schedGrid.enabled = false;
      this.generalTab.defParams.enabled = false;
      this.generalTab.addButton.enabled = false;
      this.generalTab.changeButton.enabled = false;
      this.generalTab.deleteButton.enabled = false;
      this.generalTab.configButton.enabled = false;
      /***identifiacationTab**/
      this.identificationTab.baseQuery.editable = false;
      this.identificationTab.hostColCombo.enabled = false;
      this.identificationTab.entityColCombo.enabled = false;
      this.identificationTab.ccyColCombo.enabled = false;
      this.identificationTab.amountColCombo.enabled = false;
      this.identificationTab.defGroupCombo.enabled = false;
      this.identificationTab.genericCheck.enabled = false;
      this.identificationTab.facilityCombo.enabled = false;
      this.identificationTab.refColumnCombo.isDropdownDisabled = true;
      this.instancesTab.alertInstanceColumnCombo.isDropdownDisabled = true;
      this.identificationTab.paramValueTxt.enabled = false;
      this.identificationTab.testButton.enabled = false;
      //Instance tab
      this.instancesTab.uniqueExpression.editable = false;
      this.instancesTab.acctIdCombo.enabled = false;
      this.instancesTab.signColCombo.enabled = false;
      this.instancesTab.mvtColCombo.enabled = false;
      this.instancesTab.matchColCombo.enabled = false;
      this.instancesTab.sweepColCombo.enabled = false;
      this.instancesTab.payColCombo.enabled = false;
      this.instancesTab.otherIdColCombo.enabled = false;
      this.instancesTab.otherIdTypeCombo.enabled = false;
      this.instancesTab.treeBreakDown1Combo.enabled = false;
      this.instancesTab.treeBreakDown2Combo.enabled = false;
      this.instancesTab.valueDateCombo.enabled = false;

      this.instancesTab.instExpTxt.enabled = false;
      this.instancesTab.radioNo.enabled = false;
      this.instancesTab.radioNo.enabled = false;
      this.instancesTab.radioAfter.enabled = false;
      this.instancesTab.afterMinTxt.enabled = false;
      //Gui-highlight tab
      this.guiHighlightTab.guiGrid.enabled = false;
      // this.guiHighlightTab.addButton.enabled = false;
      // this.guiHighlightTab.changeButton.enabled = false;
      // this.guiHighlightTab.deleteButton.enabled = false;
      //Event Tab
      this.eventsTab.eventsGrid.enabled= false;
      this.eventsTab.eventsGrid.selectable= false
      this.eventsTab.imgUpButton.enabled = false;
      this.eventsTab.imgDownButton.enabled = false;
      this.eventsTab.addButton.enabled = false;
      this.eventsTab.changeButton.enabled = false;
      this.eventsTab.deleteButton.enabled = false;
      this.eventsTab.triggeEventsRadioGroup.enabled = false;
      this.eventsTab.resolutionQueryText.enabled = false;
      this.eventsTab.refColumnCombo.isDropdownDisabled = true;
      this.eventsTab.minsText.enabled = false;
    }

  }

  dynamicCreation() {

    this.general = <Tab>this.tabNavigator.addChild(Tab);
    this.identification = <Tab>this.tabNavigator.addChild(Tab);
    this.instances = <Tab>this.tabNavigator.addChild(Tab);
    this.guiHighlight = <Tab>this.tabNavigator.addChild(Tab);
    this.events = <Tab>this.tabNavigator.addChild(Tab);

    this.general.label = this.general.id = SwtUtil.getPredictMessage('scenario.tab.general', null);
    this.identification.label = this.identification.id = SwtUtil.getPredictMessage('scenario.tab.identification', null);
    this.instances.label = this.instances.id = SwtUtil.getPredictMessage('scenario.tab.instances', null);
    this.guiHighlight.label = this.guiHighlight.id = SwtUtil.getPredictMessage('scenario.tab.guiHighlight', null);
    this.events.label = this.events.id = SwtUtil.getPredictMessage('scenario.tab.events', null);

    this.generalTab = <General>this.general.addChild(General);
    this.identificationTab = <Identification>this.identification.addChild(Identification);
    this.instancesTab = <Instance>this.instances.addChild(Instance);
    this.guiHighlightTab = <GuiHighlight>this.guiHighlight.addChild(GuiHighlight);
    this.eventsTab = <Events>this.events.addChild(Events);

    this.generalTab.height = "100%";
    this.identificationTab.height = "100%";
    this.instancesTab.height = "100%";
    this.guiHighlightTab.height = "100%";
    this.eventsTab.height = "100%";

    this.generalTab.parentDocument = this.identificationTab.parentDocument = this.instancesTab.parentDocument = this.guiHighlightTab.parentDocument = this.eventsTab.parentDocument = this;
  }


  onLoad() {
    this.dynamicCreation();
    this.lockIcon = ExternalInterface.call('eval', 'lockImage');
    this.unlockIcon = ExternalInterface.call('eval', 'unlockImage');
    this.methodName = ExternalInterface.call('eval', 'methodName');
    this.selectedScenarioID = ExternalInterface.call('eval', 'selectedScenarioID');
    this.selectedSystemFlag = ExternalInterface.call('eval', 'selectedSystemFlag');
    this.menuAccessId = ExternalInterface.call('eval', 'menuAccessId');
    this.fromAdvanced = "false";// ExternalInterface.call('eval', 'fromAdvanced');
    this.requestParams = [];
    this.actionPath = 'scenMaintenance.do?';
    this.actionMethod = 'method=' + this.methodName;
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (data) => {
      this.inputDataResult(data);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.requestParams["fromAdvanced"] = this.fromAdvanced;
    this.requestParams["selectedScenarioID"] = this.selectedScenarioID;
    this.requestParams["selectedSystemFlag"] = this.selectedSystemFlag;
    this.requestParams["menuAccessId"] = this.menuAccessId;
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  private enableDisableRow(row: any, field: string): boolean {
    if (row.colorflag == "N") {
      return true;
    } else {
      return false;
    }
  }

  startOfComms(): void {
    this.loadingImg.setVisible(true);
  }

  endOfComms(): void {
    this.loadingImg.setVisible(false);
  }

  inputDataResult(event) {
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);
      if (this.jsonReader.getRequestReplyStatus()) {
        if (this.lastRecievedJSON != this.prevRecievedJSON) {
          if (!this.jsonReader.isDataBuilding()) {
            this.fillDetails();
            this.fillGeneralTab();
            this.fillIdentificationTab();
            this.fillInstanceData();
            this.fillGuiHighlightData(null);
            this.fillEventsData();
            this.enableDisableFields(this.methodName);
            if(this.methodName=="change"){
            this.checkIfInstExist();
            }
          }
        }
      }
    }


  }

  fillDetails() {
    if (this.methodName != "add") {
      this.scenarioIdTxt.text = this.jsonReader.getSingletons().scenarioId;
      this.titleTxt.text = this.jsonReader.getSingletons().scenarioTitle;
      this.descriptionTxt.text = this.jsonReader.getSingletons().scenarioDesc;
      this.activeCheck.selected = this.jsonReader.getSingletons().selectedActiveFlag == "Y";
      this.systemCheck.selected = this.jsonReader.getSingletons().selectedSystemFlag == "Y";
    } else {
      this.activeCheck.selected = false; //default selectd add screen
    }

    if (this.systemCheck.selected)
      this.enableDisableFields('systemCheck')
  }

  fillGeneralTab() {
    this.generalTab.schedGrid.CustomGrid(this.lastRecievedJSON.scenarioDetails.generalGrid.metadata);
    this.generalTab.categoryCombo.setComboData(this.jsonReader.getSelects(), false);
    this.generalTab.selectedCategLbl.text = this.generalTab.categoryCombo.selectedValue;
    if (this.systemCheck.selected){
      this.scenarioIdTxt.editable=false;
      this.titleTxt.editable=false;
      this.descriptionTxt.editable=false;
      this.generalTab.recordCheck.enabled=false;
      this.generalTab.startTxt.editable=false;
      this.generalTab.endTxt.editable=false;
      

    }
    if (this.methodName != "add") {
      this.generalTab.recordCheck.enabled=false;
      this.generalTab.recordCheck.selected = this.jsonReader.getSingletons().recordScenInstance == "Y";
      let gridData = (this.jsonReader.getSingletons().scheduleParamsXml).replace(/#/g, ">");
      if (gridData) {
        this.prepareGenGridData(gridData);
      }else{
        this.generalTab.schedGrid.gridData = { row: [], size: 0 }; 
      }
      this.typeSelectedValue = this.jsonReader.getSingletons().savedGenBasis ? this.jsonReader.getSingletons().savedGenBasis : "C";
      this.generalTab.typeOptions.selectedValue = this.typeSelectedValue;
      this.populateRadioGrValue(this.typeSelectedValue);
      this.generalTab.savedApiParams = this.jsonReader.getSingletons().savedApiParams;
      this.generalTab.CreateInsDesc.text = this.jsonReader.getSingletons().savedApiParams!="[]" && this.generalTab.typeOptions.selectedValue == 'A' ? SwtUtil.getPredictMessage('scenario.CreateInsDescFull', null) + "<" + this.jsonReader.getSingletons().savedApiParams.replace("[", "").replace("]", "").replace(/['"]+/g, '') + ">" : SwtUtil.getPredictMessage('scenario.CreateInsDesc', null);
      this.generalTab.xmlParams = this.jsonReader.getSingletons().requiredParamsXml ? (this.jsonReader.getSingletons().requiredParamsXml).replace(/#/g, ">") : "";
      this.generalTab.orderTxt.text = this.jsonReader.getSingletons().displayOrder;
      this.generalTab.runTxt.text = this.jsonReader.getSingletons().runEvery;
      this.generalTab.startTxt.text = this.jsonReader.getSingletons().startTime;
      this.generalTab.endTxt.text = this.jsonReader.getSingletons().endTime;
      this.generalTab.emailTxt.text = this.jsonReader.getSingletons().emailWhenDiff;
      this.generalTab.categoryCombo.selectedLabel = this.jsonReader.getSingletons().selectedCategoryId;
      this.generalTab.selectedCategLbl.text = this.generalTab.categoryCombo.selectedValue;
    } else {
      let data = this.generalTab.generalGridData ? this.generalTab.generalGridData : [];
      this.generalTab.schedGrid.gridData = { row: data, size: data.length };
      if(!this.generalTab.recordCheck.selected){
        this.generalTab.cyclic.selected=true;
        this.generalTab.cyclic.enabled=true;
        this.generalTab.scheduled.enabled=false;
        this.generalTab.createInst.enabled=false;
      }else{
        this.generalTab.cyclic.enabled=false;
        this.generalTab.scheduled.selected=true;
        this.generalTab.scheduled.enabled=true;
        this.generalTab.createInst.enabled=true; 
      }
    }


  }

  prepareGenGridData(parameters) {
    let rows = parameters.split(",");
    for (let i = 0; i < rows.length; i++) {
      let row = rows[i].split("=");
      let schedulerId = row[0].split('-')[0].trim();
      let time = row[0].split('-')[1].trim();
      let xml = row[1];
      this.convertXml(schedulerId,time, xml);
    }

  }

  convertXml(schedulerId, time, xmlAsString) {
    let parameters = "";
    if (xmlAsString) {
      let options = {
        object: false,
        reversible: false,
        coerce: false,
        sanitize: true,
        trim: true,
        arrayNotation: false,
        alternateTextNode: false,
        compact: true
      };
      let jsonData = convert.xml2js(xmlAsString, options);
      let data = jsonData.PARAMETERS.PARAMETER;
      if(data){
      if (!data.length)
        data = [data];
      for (let i = 0; i < data.length; i++) {
        if (!data[i].length)
          data[i] = [data[i]];
        for (let j = 0; j < data[i].length; j++) {
          if (data[i][j].NAME._cdata ){//&& data[i][j].VALUE._cdata) {
            parameters = parameters + data[i][j].NAME._cdata + "(" + (data[i][j].VALUE._cdata?data[i][j].VALUE._cdata:"") + ");";
          }
        }
      }
      this.scheduleGenData.push({ schedulerId: { clickable: false, content: schedulerId, negative: false },time: { clickable: false, content: time, negative: false }, parameters: { clickable: false, content: parameters.slice(0, -1), negative: false } });
    }else{
      this.scheduleGenData.push({ schedulerId: { clickable: false, content: schedulerId, negative: false },time: { clickable: false, content: time, negative: false }, parameters: { clickable: false, content: "", negative: false } });
    }
    }
    this.generalTab.schedGrid.gridData = { row: this.scheduleGenData, size: this.scheduleGenData.length };
    this.generalTab.oldSchedRows = this.scheduleGenData;
    this.generalTab.schedGrid.refresh();
  }

  populateRadioGrValue(selectedValue) {
    switch (selectedValue) {
      case "C":
        this.generalTab.cyclic.selected = true;
        this.generalTab.scheduled.enabled = false;
        this.generalTab.createInst.enabled = false;
        break;
      case "S":
        this.generalTab.scheduled.selected = true;
        this.generalTab.cyclic.enabled = false;
        this.generalTab.createInst.enabled = false;
        break;
      case "A":
        this.generalTab.createInst.selected = true;
        this.generalTab.cyclic.enabled = false;
        this.generalTab.scheduled.enabled = false;
        break;
      default:
        break;
    }
    this.generalTab.enableDisableComponents();

  }

  fillIdentificationTab() {
    this.identificationTab.BaseQueryLockImg.source =this.baseURL + ExternalInterface.call('eval', 'lockImage');
    this.identificationTab.hostLockImg.source =this.baseURL + ExternalInterface.call('eval', 'lockImage');
    this.identificationTab.entityLockImg.source =this.baseURL + ExternalInterface.call('eval', 'lockImage');
    this.identificationTab.ccyLockImg.source =this.baseURL + ExternalInterface.call('eval', 'lockImage');
    this.identificationTab.amountLockImg.source =this.baseURL + ExternalInterface.call('eval', 'lockImage');

    this.identificationTab.defGroupCombo.setComboData(this.jsonReader.getSelects(), true);

    this.identificationTab.facilityCombo.setComboData(this.jsonReader.getSelects(), false);
    if (this.methodName != "add") {
      
      this.identificationTab.baseQuery.text = this.htmlEntities(Encryptor.decode64(this.jsonReader.getSingletons().queryText));;

      this.defaultQuery= Encryptor.decode64(this.jsonReader.getSingletons().queryText);
      this.defaultHost = this.jsonReader.getSingletons().hostCol;
      this.defaultEntity = this.jsonReader.getSingletons().entityCol;
      this.defaultAmount = this.jsonReader.getSingletons().amountCol;
      this.defaultCcy = this.jsonReader.getSingletons().currencyCol;
      this.columns = this.jsonReader.getSelects()['select'].find(x => x.id == "queryColumns");
      this.nbrColumns = this.jsonReader.getSelects()['select'].find(x => x.id == "numbersColumns");
      this.txtColumns = this.jsonReader.getSelects()['select'].find(x => x.id == "textColumns");
      this.dateColumns = this.jsonReader.getSelects()['select'].find(x => x.id == "dateColumns");
      if (this.txtColumns) {
      let txtOptions = this.txtColumns.option;
      if (txtOptions && !txtOptions.length)
      txtOptions = [txtOptions];
      this.identificationTab.hostColCombo.setComboData(txtOptions);
      this.identificationTab.hostColCombo.dataProvider = txtOptions;
      this.identificationTab.entityColCombo.setComboData(txtOptions);
      this.identificationTab.entityColCombo.dataProvider = txtOptions;
      this.identificationTab.ccyColCombo.setComboData(txtOptions);
      this.identificationTab.ccyColCombo.dataProvider = txtOptions;
      }
      if (this.dateColumns) {
        let dateOptions = this.dateColumns.option;  
        if (dateOptions && !dateOptions.length)
        dateOptions = [dateOptions];
      }

      if (this.nbrColumns) {
        let nbrOptions = this.nbrColumns.option; 
        if (nbrOptions && !nbrOptions.length)
        nbrOptions = [nbrOptions];
        this.identificationTab.amountColCombo.setComboData(nbrOptions);
        this.identificationTab.amountColCombo.dataProvider = nbrOptions;
      }

      if (this.columns) {
        let options = this.columns.option;
        const copyData = $.extend(true, [], options);
        copyData.splice(0, 1);
        copyData.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
        this.identificationTab.refColumnCombo.dataProvider = copyData;
      } else {
        let hostList = [this.jsonReader.getSelects()['select'].find(x => x.id == "selectedHost").option];
        let entityList = [this.jsonReader.getSelects()['select'].find(x => x.id == "selectedEntity").option];
        let ccyList = [this.jsonReader.getSelects()['select'].find(x => x.id == "selectedCurrency").option];
        let amountList = [this.jsonReader.getSelects()['select'].find(x => x.id == "selectedAmount").option];
        this.identificationTab.hostColCombo.setComboData(hostList);
        this.identificationTab.hostColCombo.dataProvider = hostList;
        this.identificationTab.entityColCombo.setComboData(entityList);
        this.identificationTab.entityColCombo.dataProvider = entityList;
        this.identificationTab.ccyColCombo.setComboData(ccyList);
        this.identificationTab.ccyColCombo.dataProvider = ccyList;
        this.identificationTab.amountColCombo.setComboData(amountList);
        this.identificationTab.amountColCombo.dataProvider = amountList;
      }
      this.identificationTab.hostColCombo.selectedLabel = this.defaultHost;
      this.identificationTab.entityColCombo.selectedLabel = this.defaultEntity;
      this.identificationTab.ccyColCombo.selectedLabel = this.defaultCcy;
      this.identificationTab.amountColCombo.selectedLabel = this.defaultAmount;
      this.identificationTab.genericCheck.selected = this.jsonReader.getSingletons().useGenericDisplay == 'Y';
      //get the list of refColumnCombo saved options 
      this.defaultVal = this.jsonReader.getSelects()['select'].find(x => x.id == "listSelections").option;
      //get the list of alertInstanceColumnCombo saved options    
      this.savedAlertInstCols = this.jsonReader.getSelects()['select'].find(x => x.id == "savedAlertInstCols").option;
      // to avoid issue when we select only one option
      if (this.defaultVal) {
        if (!this.defaultVal.length)
          this.defaultVal = [this.defaultVal];
      } else {
        this.defaultVal = [];
      }

      if (this.savedAlertInstCols) {
        if (!this.savedAlertInstCols.length)
          this.savedAlertInstCols = [this.savedAlertInstCols];
      } else {
        this.savedAlertInstCols = [];
      }
      //if no query set the default selected values in the data provider
      if (!this.columns) {
        this.defaultVal.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
        this.identificationTab.refColumnCombo.dataProvider = this.defaultVal;
      }
      //set refColumnCombo saved options
      if(this.defaultVal.length>0)
      this.identificationTab.refColumnCombo.toolTip=this.getTooltip(this.defaultVal);
      this.identificationTab.refColumnCombo.defaultSelectedItems = this.defaultVal;
      this.identificationTab.refColumnCombo.selects = this.defaultVal;
      //set alertInstanceColumnCombo saved options
      if(this.savedAlertInstCols.length>0)
      this.instancesTab.alertInstanceColumnCombo.toolTip=this.getTooltip(this.savedAlertInstCols);
      this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems=this.savedAlertInstCols;
      this.instancesTab.alertInstanceColumnCombo.selects = this.savedAlertInstCols;

      this.identificationTab.paramValueTxt.text = this.jsonReader.getSingletons().facilityParamVals;
      this.identificationTab.defGroupCombo.selectedValue = this.jsonReader.getSingletons().selectedSummaryGrouping;
      this.selectedFacilityId = this.jsonReader.getSingletons().selectedfacilityId;
      this.identificationTab.facilityCombo.selectedLabel = this.selectedFacilityId;
    }
    if (this.methodName == "add" || this.identificationTab.defGroupCombo.selectedValue == "N") {
      //this.identificationTab.defGroupCombo.disabledItems = [0, 1, 2];
    } else {
      if (this.identificationTab.defGroupCombo.selectedValue == "E") {
        /*this.identificationTab.defGroupCombo.disableItem(0);
        this.identificationTab.defGroupCombo.disableItem(2);
        this.identificationTab.defGroupCombo.enableItem(1);*/
      } else if (this.identificationTab.defGroupCombo.selectedValue == "C") {
        /*this.identificationTab.defGroupCombo.disableItem(0);
        this.identificationTab.defGroupCombo.enableItem(1);
        this.identificationTab.defGroupCombo.enableItem(2);*/
      }
    }
    this.savedQuery= this.identificationTab.baseQuery.text;
    // to avoid empty facility id
    this.identificationTab.fillFacilityProperties();
  }


  getTooltip(savedColumns){
    let tooltipArray=[];
    for (let i=0; i<savedColumns.length; i++){
      tooltipArray.push(savedColumns[i].content);
    }
    this.savedTooltip=tooltipArray.toString();
    return this.savedTooltip;

  }

  getDefaultRefColumn(defaultselected) {
    defaultselected = defaultselected.substring(1, defaultselected.length - 1);
    for (var i = 0; i < defaultselected.length; i++) {
      if (defaultselected[i] === "}") {
        if (defaultselected[i + 1]) {
          defaultselected = (defaultselected.substr(0, i + 1) + '*' + defaultselected.substr(i + 2));
        }
      }
    }
    this.defaultOptions = defaultselected.split('*');
    let row = {};
    for (let i = 0; i < this.defaultOptions.length; i++) {
      var index: number = (this.defaultOptions[i].split(',')[0]).split(':')[1];
      let col = (this.defaultOptions[i].split(',')[1]).split(':')[1];
      col = col.substr(1, col.length - 3);
      this.defaultValString = col;
      let row = { value: Number(index), content_text: col };
      this.defaultVal.push(row);
    }

  }


  fillInstanceData() {
    //get option ID types combo values
    this.optionIdTypes = this.jsonReader.getSelects()['select'].find(x => x.id == "otherIdTypeCombo").option;
    if (this.methodName != "add") {
      this.instancesTab.uniqExpLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      this.instancesTab.acctLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      this.instancesTab.valDateLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      this.instancesTab.signLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      this.instancesTab.mvtLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      this.instancesTab.matchLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      this.instancesTab.sweepLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      this.instancesTab.payLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      this.instancesTab.otherIdLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      this.instancesTab.otherIdTypeLockImg.source = this.baseURL + ExternalInterface.call('eval', 'lockImage');
      if(this.generalTab.recordCheck.selected){
      this.defaultAccount = this.jsonReader.getSingletons().accountIdColumn;
      this.defaultSign = this.jsonReader.getSingletons().signColumn;
      this.defaultValueDate = this.jsonReader.getSingletons().valueDateColumn;
      this.defaultOtherId = this.jsonReader.getSingletons().otherIdColumn;
      this.defaultCustomTreeLevel1 = this.jsonReader.getSingletons().customTreeLevel1;
      this.defaultCustomTreeLevel2 = this.jsonReader.getSingletons().customTreeLevel2;
      this.defaultMvt = this.jsonReader.getSingletons().mvtColumn;
      this.defaultMatchId = this.jsonReader.getSingletons().matchColumn;
      this.defaultSweepId = this.jsonReader.getSingletons().sweepColumn;
      this.defaultPay = this.jsonReader.getSingletons().payColumn;
      this.defaultOtherIdType= this.jsonReader.getSingletons().otherIdTypeColumn;
      // get saved tree breakdown combo dataprovider
      this.prepareTreeBreakdownProvider();
      this.prepareAlertInstProvider();
    if(this.columns){
        let options = this.columns.option;
    }
      if (this.txtColumns) {
        let txtOptions = this.txtColumns.option;
        if (txtOptions && !txtOptions.length)
        txtOptions = Array.isArray(txtOptions) ? txtOptions : [txtOptions];
        this.instancesTab.acctIdCombo.setComboData(txtOptions);
        this.instancesTab.acctIdCombo.dataProvider = txtOptions;
        this.instancesTab.signColCombo.setComboData(txtOptions);
        this.instancesTab.signColCombo.dataProvider = txtOptions;
        this.instancesTab.otherIdColCombo.setComboData(txtOptions);
        this.instancesTab.otherIdColCombo.dataProvider = txtOptions
      }
      if (this.nbrColumns) {
        let nbrOptions = this.nbrColumns.option;
        if (nbrOptions && !nbrOptions.length)
        nbrOptions = Array.isArray(nbrOptions) ? nbrOptions : [nbrOptions];
        this.instancesTab.mvtColCombo.setComboData(nbrOptions);
        this.instancesTab.mvtColCombo.dataProvider = nbrOptions;
        this.instancesTab.matchColCombo.setComboData(nbrOptions);
        this.instancesTab.matchColCombo.dataProvider = nbrOptions;
        this.instancesTab.sweepColCombo.setComboData(nbrOptions);
        this.instancesTab.sweepColCombo.dataProvider = nbrOptions;
        this.instancesTab.payColCombo.setComboData(nbrOptions);
        this.instancesTab.payColCombo.dataProvider = nbrOptions;
      }

      if (this.dateColumns) {
        let dateOptions = this.dateColumns.option;
        if (dateOptions && !dateOptions.length){
          dateOptions = Array.isArray(dateOptions) ? dateOptions : [dateOptions];
        }
        this.instancesTab.valueDateCombo.setComboData(dateOptions);
        this.instancesTab.valueDateCombo.dataProvider = dateOptions
      }
        this.instancesTab.otherIdTypeCombo.setComboData(this.optionIdTypes);
        this.instancesTab.otherIdTypeCombo.dataProvider = this.optionIdTypes
      
      //this.instancesTab.recScenarioCheck.selected = this.jsonReader.getSingletons().recordScenInstance == "Y";
      this.instancesTab.uniqueExpression.text = Encryptor.decode64(this.jsonReader.getSingletons().uniqueExpression);
      this.defaultUniqueExp= Encryptor.decode64(this.jsonReader.getSingletons().uniqueExpression);
      this.instancesTab.acctIdCombo.selectedLabel = this.defaultAccount;
      this.instancesTab.signColCombo.selectedLabel = this.defaultSign;
      //Intance Tab Fields added in v0.5
      this.instancesTab.mvtColCombo.selectedLabel = this.defaultMvt;
      this.instancesTab.matchColCombo.selectedLabel = this.defaultMatchId;
      this.instancesTab.sweepColCombo.selectedLabel = this.defaultSweepId;
      this.instancesTab.payColCombo.selectedLabel = this.defaultPay;
      this.instancesTab.treeBreakDown1Combo.selectedLabel = this.defaultCustomTreeLevel1;
      this.instancesTab.treeBreakDown2Combo.selectedLabel = this.defaultCustomTreeLevel2;
      this.instancesTab.valueDateCombo.selectedLabel = this.defaultValueDate;
      this.instancesTab.otherIdColCombo.selectedLabel = this.defaultOtherId;
      this.instancesTab.otherIdTypeCombo.selectedLabel = this.jsonReader.getSingletons().otherIdTypeColumn;
      this.instancesTab.otherIdTypeDesc.text= this.instancesTab.otherIdTypeCombo.selectedValue; 

      this.instancesTab.instExpTxt.text = this.jsonReader.getSingletons().instanceExpiryMins;
      this.instancesTab.raiseRadioGroup.selectedValue = this.jsonReader.getSingletons().reRaiseAfterExpiry;
      this.instancesTab.afterMinTxt.text = this.jsonReader.getSingletons().minsAfterExpiry;
      //enable afterMinTxt if re-raise after expiry is selected
      if(this.jsonReader.getSingletons().reRaiseAfterExpiry=='A'){
        this.instancesTab.afterMinTxt.enabled=true;
      }else{
        this.instancesTab.afterMinTxt.enabled=false;
      }
    }else{
      this.disableInstAndEvtsTab(false)
    }
    }else{
      this.disableInstAndEvtsTab(false);
    }

  }

  fillInstanceCombo(col) {
    this.queryColumns = col;
    try {
      
    
    if (col) {
          let listValues = (col && col.select && col.select[0] && col.select[0].option) || [];
          let numberValues = (col && col.select && col.select[1] && col.select[1].option) || [];
          let txtValues = (col && col.select && col.select[2] && col.select[2].option) || [];
          let dateValues = (col && col.select && col.select[3] && col.select[3].option) || [];
          
          txtValues = Array.isArray(txtValues) ? txtValues : [txtValues];
          numberValues = Array.isArray(numberValues) ? numberValues : [numberValues];
          dateValues = Array.isArray(dateValues) ? dateValues : [dateValues];

          const emptyValue = {type: "", value: '', selected: 0, content: ""};

          if (!txtValues.length || txtValues[0].content !== "") {
            txtValues.unshift(emptyValue);
        }
        if (!numberValues.length || numberValues[0].content !== "") {
            numberValues.unshift(emptyValue);
        }
        if (!dateValues.length || dateValues[0].content !== "") {
            dateValues.unshift(emptyValue);
        }


      this.instancesTab.acctIdCombo.setComboData(txtValues);
      this.instancesTab.acctIdCombo.dataProvider = txtValues;
      this.instancesTab.acctIdCombo.selectedLabel=this.savedAcctId;
      this.instancesTab.signColCombo.setComboData(txtValues);
      this.instancesTab.signColCombo.dataProvider = txtValues;
      this.instancesTab.signColCombo.selectedLabel=this.savedSign;
      this.instancesTab.mvtColCombo.setComboData(numberValues);
      this.instancesTab.mvtColCombo.dataProvider = numberValues;
      this.instancesTab.mvtColCombo.selectedLabel=this.savedMvtId
      this.instancesTab.matchColCombo.setComboData(numberValues);
      this.instancesTab.matchColCombo.dataProvider = numberValues;
      this.instancesTab.matchColCombo.selectedLabel=this.savedMatchId
      this.instancesTab.sweepColCombo.setComboData(numberValues);
      this.instancesTab.sweepColCombo.dataProvider = numberValues;
      this.instancesTab.sweepColCombo.selectedLabel=this.savedSwpId;
      this.instancesTab.payColCombo.setComboData(numberValues);
      this.instancesTab.payColCombo.dataProvider = numberValues;
      this.instancesTab.payColCombo.selectedLabel=this.savedPayId;
      this.instancesTab.valueDateCombo.setComboData(dateValues);
      this.instancesTab.valueDateCombo.dataProvider = dateValues;
      this.instancesTab.valueDateCombo.selectedLabel=this.savedValueDate;
      this.instancesTab.otherIdColCombo.setComboData(txtValues);
      this.instancesTab.otherIdColCombo.dataProvider = txtValues;
      this.instancesTab.otherIdColCombo.selectedLabel=this.savedOtherId;
      this.instancesTab.otherIdTypeCombo.setComboData(this.optionIdTypes);
      this.instancesTab.otherIdTypeCombo.dataProvider = this.optionIdTypes;
      this.instancesTab.otherIdTypeCombo.selectedLabel=this.savedOtherIdType;
      this.instancesTab.otherIdTypeDesc.text=this.savedOtherIdTypeDesc;

    }

      } catch (error) {
        console.log('error',error)
      }
  }



  fillGuiHighlightData(col) {
   let recScenarioCheck: boolean = this.generalTab.recordCheck.selected;
    if (this.methodName != "add") {
      this.guiHighlightTab.critGuiHighCheck.selected = this.jsonReader.getSingletons().criticalGuiHighlight == "Y";
      /*this.guiHighlightTab.guiGrid.gridComboDataProviders(this.lastRecievedJSON.scenarioDetails.selects);

    } else {
      if (col) {
        this.guiHighlightTab.guiGrid.gridComboDataProviders(col);

      }*/
    }
    this.guiHighlightTab.guiGrid.forceHeaderRefresh = true;
    this.guiHighlightTab.guiGrid.CustomGrid(this.lastRecievedJSON.scenarioDetails.guiGrid.metadata);
    this.guiHighlightTab.guiGrid.gridData = this.lastRecievedJSON.scenarioDetails.guiGrid.rows;
    this.guiHighlightTab.guiGrid.rowColorFunction = (dataContext, dataIndex, color) => {
      return this.changeColorBackground(dataContext, dataIndex, color, recScenarioCheck);
    };
    //set row tooltip: list of required columns
    this.guiHighlightTab.guiGrid.customTooltipFunction = (dataContext) => {
      return this.setTooltipMessage(dataContext);
    };
    this.guiHighlightTab.guiGrid.enableDisableCells=(row, field) => {
      return this.enableDisableRow(row, field);
    };
  }

  public eventGridJSON: any;
  fillEventsData() {  
    this.eventsTab.BtnLockImg.source =this.baseURL + ExternalInterface.call('eval', 'lockImage');  
    this.eventsTab.eventsGrid.CustomGrid(this.lastRecievedJSON.scenarioDetails.eventGrid.metadata);
    this.eventsTab.eventsGrid.refresh();
    if (this.methodName !== "add") {
      if(this.generalTab.recordCheck.selected){
      //fill sub event grid combo
      this.gridComboVal = this.columns ? this.columns.option : [];
      this.gridComboTxtVal = this.txtColumns ? this.txtColumns.option : [];
      this.gridComboNbrVal = this.nbrColumns ? this.nbrColumns.option : [];
      this.gridComboDateVal = this.dateColumns ? this.dateColumns.option : [];

      this.eventsTab.eventsGrid.gridData = { row: this.lastRecievedJSON.scenarioDetails.eventGrid.rows.row, size: this.lastRecievedJSON.scenarioDetails.eventGrid.rows.size };
      this.eventGridJSON = this.lastRecievedJSON.scenarioDetails.eventGrid.rows;
      this.eventsTab.triggeEventsRadioGroup.selectedValue = this.jsonReader.getSingletons().afterTrigEvent;
      this.eventsTab.resolutionQueryText.text = this.htmlEntities(Encryptor.decode64(this.jsonReader.getSingletons().resolutionQueryText));
      let resolOverdueVal= this.jsonReader.getSingletons().pendingResolutionTimeLimit;
      if(resolOverdueVal!=-1){
      this.eventsTab.resolOverdueRadioGroup.selectedValue='A';
      this.eventsTab.minsText.text = resolOverdueVal;
      }else{
      this.eventsTab.resolOverdueRadioGroup.selectedValue='N';
      this.eventsTab.minsText.text = "";
      }
      if (this.columns) {
        let options = this.columns.option;
        const copyData = $.extend(true, [], options);
        copyData.splice(0, 1);
        copyData.unshift({ type: "", value: -1, selected: 0, content: '"INSTANCE_ID"' });
        copyData.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
        this.eventsTab.refColumnCombo.dataProvider = copyData;
      } 

      //get the list of resolution ref columns Combo saved options    
       this.savedResolutionRefCols = this.jsonReader.getSelects()['select'].find(x => x.id == "savedResolRefCols").option;
       if (this.savedResolutionRefCols) {
        if (!this.savedResolutionRefCols.length)
          this.savedResolutionRefCols = [this.savedResolutionRefCols];
      } else {
        this.savedResolutionRefCols = [];
      }
      //set eventTab refColumnCombo  saved options
            if(this.savedResolutionRefCols.length>0)
            this.eventsTab.refColumnCombo.toolTip=this.getTooltip(this.savedResolutionRefCols);
            this.eventsTab.refColumnCombo.defaultSelectedItems=this.savedResolutionRefCols;
            this.eventsTab.refColumnCombo.selects = this.savedResolutionRefCols;
      //enable triggeEventsRadioGroup only when event grid has at least one record
        if (this.eventsTab.eventsGrid.gridData && this.eventsTab.eventsGrid.gridData.length>0) {
          this.eventsTab.triggeEventsRadioGroup.enabled = true;
        } else {
          this.eventsTab.triggeEventsRadioGroup.enabled = false;
        }

    }else{
      this.disableInstAndEvtsTab(false)
    }
    }else{
      this.disableInstAndEvtsTab(false);
      this.eventsTab.triggeEventsRadioGroup.enabled = false;
    }

    if(this.eventsTab.triggeEventsRadioGroup.selectedValue=='P'){
      this.eventsTab.resolutionQueryText.enabled=true;
      this.eventsTab.refColumnCombo.isDropdownDisabled=false;
      this.eventsTab.minsText.enabled=true;
      this.eventsTab.resolOverdueRadioGroup.enabled=true;
      if(this.eventsTab.resolOverdueRadioGroup.selectedValue=='A'){
        this.eventsTab.minsText.enabled=true;
      }else{
        this.eventsTab.minsText.enabled=false; 
      }

    }else{
      this.eventsTab.resolutionQueryText.enabled=false;
      this.eventsTab.refColumnCombo.isDropdownDisabled=true;
      this.eventsTab.minsText.enabled=false; 
      this.eventsTab.resolOverdueRadioGroup.enabled=false;

    }
  }

  inputDataFault() {
    this.swtalert.error(SwtUtil.getPredictMessage('alert.generic_exception'));
  }

  validateFields(): boolean {
    let message = "Please enter a valid string without '~'";
    let mandatoryMsg = SwtUtil.getPredictMessage('alert.pleaseFillAllMandatoryFields', null);
    let diffScenMsg = SwtUtil.getPredictMessage('scenario.sytemScenarioAlert', null);
    let scenStartEndMsg = SwtUtil.getPredictMessage('scenario.startEndTime.alert', null);
    let afterExpiryMinsMsg = "Please fill number of minutes after expiry";
    let regex: RegExp = /^[^~]*$/;
    if (this.scenarioIdTxt.text != "" && !regex.test(this.scenarioIdTxt.text)) {
      this.swtalert.show(message);
      return false;
    } else if (this.scenarioIdTxt.text == "" || this.titleTxt.text == "" 
    ||(this.instancesTab.uniqueExpression.text=="" && this.generalTab.recordCheck.selected)) {
      this.swtalert.warning(mandatoryMsg);
      return false;
    } else if (!this.systemCheck.selected && this.scenarioIdTxt.text.toLowerCase().indexOf("sys_") == 0) {
      this.swtalert.warning(diffScenMsg);
      return false;
    } else if ((this.generalTab.startTxt.text == "" && this.generalTab.endTxt.text != "") || (this.generalTab.startTxt.text != "" && this.generalTab.endTxt.text == "")) {
      this.swtalert.warning(scenStartEndMsg);
      return false;
    } else if (this.instancesTab.raiseRadioGroup.selectedValue == "A" && this.instancesTab.afterMinTxt.text == "") {
      this.swtalert.warning(afterExpiryMinsMsg);
    }
    else {
      return true;
    }
  }
  /*guiHighlightChanges() {
    let row = {};
    let operation="";
    let gridChanges = this.guiHighlightTab.guiGrid.changes.getValues();
    for (let i = 0; i < gridChanges.length; i++) {
      row = { 'OPERATION': "I", 'TABLE': "GuiAlertMapping", 'SCENARIO_ID': this.scenarioIdTxt.text, 
      'GUI_FACILITY_ID': gridChanges[i].crud_data.guiIdDescripion.split('-')[0].trim(), 'PARAMETERS_XML': gridChanges[i].crud_data.guiRequiredParams.toString()};
      //'OTHER_ID': gridChanges[i].crud_data.guiOtherId};
      this.operationsList.push(row)
    }
}*/


  guiHighlightChanges() {
    let row = {};
    for (let i = 0; i < this.guiHighlightTab.guiGrid.gridData.length; i++) {
      if (this.guiHighlightTab.guiGrid.gridData[i].guiSelect == "Y") {
        row = {
          'OPERATION': "I", 'TABLE': "GuiAlertMapping", 'SCENARIO_ID': (this.scenarioIdTxt.text).trim(),
          'GUI_FACILITY_ID': this.guiHighlightTab.guiGrid.gridData[i].guiIdDescripion.split('-')[0].trim(), 'PARAMETERS_XML': this.guiHighlightTab.guiGrid.gridData[i].guiRequiredParams.toString()
        };
        //'OTHER_ID': gridChanges[i].crud_data.guiOtherId};
        this.operationsList.push(row);
      }
    }
  }



  doRefreshGuiHighlight(field: string): void {
    //instance TAB
    let acctId: string = this.instancesTab.acctIdCombo.selectedLabel;
    let recScenarioCheck: boolean = this.generalTab.recordCheck.selected;
    if (acctId != "") {
      this.guiHighlightTab.guiGrid.rowColorFunction = (dataContext, dataIndex, color) => {
        return this.changeColorBackground(dataContext, dataIndex, color, recScenarioCheck);
      };
    }

    let valueDate: string = this.instancesTab.valueDateCombo.selectedLabel;
    let signCol: string = this.instancesTab.signColCombo.selectedLabel;
    let mvtCol: string = this.instancesTab.mvtColCombo.selectedLabel;
    let matchCol: string = this.instancesTab.matchColCombo.selectedLabel;
    let sweepCol: string = this.instancesTab.sweepColCombo.selectedLabel;
    let payCol: string = this.instancesTab.payColCombo.selectedLabel;
    let valueDateCol: string = this.instancesTab.valueDateCombo.selectedLabel;

    //identification TAB

    this.guiHighlightTab.guiGrid.rowColorFunction = (dataContext, dataIndex, color) => {
      return this.changeColorBackground(dataContext, dataIndex, color, recScenarioCheck);
    };

    //set row tooltip: list of required columns
    this.guiHighlightTab.guiGrid.customTooltipFunction = (dataContext) => {
      return this.setTooltipMessage(dataContext);
    };
  }



  changeColorBackground(dataContext, dataIndex, color, recScenarioCheck): string {
    let rColor: string;
    try {
      let colorFlag = "";
      let guiScenInstance: string;
      let guiRequiredParams: string;
      guiScenInstance = (dataContext.slickgrid_rowcontent.guiScenInstance) ? dataContext.slickgrid_rowcontent.guiScenInstance.content : "";
      guiRequiredParams = (dataContext.slickgrid_rowcontent.guiRequiredParams.content) ? dataContext.slickgrid_rowcontent.guiRequiredParams.content : "";
      if (guiRequiredParams == "") {
        if (guiScenInstance == 'Y') {
          if (recScenarioCheck) {
            colorFlag = "N";
          } else {
            colorFlag = "D";
          }
        } else {
          colorFlag = "N";
        }
      } else {
        let listCols = guiRequiredParams.split(","); 
        if (guiScenInstance == 'Y') {
          if (recScenarioCheck) {
            if(this.checkRequiredColumns(listCols)){
              colorFlag = "N";
              } else{
              colorFlag = "D";
              }
          } else {
            colorFlag = "D";
          }
        } else {
          if(this.checkRequiredColumns(listCols)){
            colorFlag = "N";
            } else{
            colorFlag = "D";
            }
        }
      }
      dataContext.colorflag = colorFlag;
      dataContext.slickgrid_rowcontent.colorflag.content = colorFlag;
      if (colorFlag == "D") {
        rColor = "#AAAAAA";
        //in case of grey column deselect row if it was selected...
        dataContext.guiSelect = 'N';
        dataContext.slickgrid_rowcontent.guiSelect.content = 'N';
      } else if (colorFlag == "N") {
        rColor = "";
      }
    } catch (error) {
      console.log('error changeColorBackground ', error)
    }
    return rColor;
  }

  checkRequiredColumns(listCols) {
    let flag = true;
    for (let i = 0; i < listCols.length; i++) {
      if (this.getComboLabelStatus(listCols[i]) == false) {
        flag = false;
      }
    }
    return flag;
  }

  getComboLabelStatus(column){
    let populatedFlag = false;

    switch (column) {
    case 'HOST_ID':
      populatedFlag = this.validateValue(this.identificationTab.hostColCombo);
      break;
    case 'ENTITY_ID':
      populatedFlag = this.validateValue(this.identificationTab.entityColCombo);
      break;
    case 'CURRENCY_CODE':
      populatedFlag = this.validateValue(this.identificationTab.ccyColCombo);
      break;
    case 'AMOUNT':
      populatedFlag = this.validateValue(this.identificationTab.amountColCombo);
      break;
    case 'ACCOUNT_ID':
      populatedFlag = this.validateValue(this.instancesTab.acctIdCombo);
      break;
    case 'SIGN':
      populatedFlag = this.validateValue(this.instancesTab.signColCombo);
      break;
    case 'MATCH_ID':
      populatedFlag = this.validateValue(this.instancesTab.matchColCombo);
      break;
    case 'SWEEP_ID':
      populatedFlag = this.validateValue(this.instancesTab.sweepColCombo);
      break;
    case 'PAYMENT_ID':
      populatedFlag = this.validateValue(this.instancesTab.payColCombo);
      break;
    case 'VALUE_DATE':
      populatedFlag = this.validateValue(this.instancesTab.valueDateCombo);
      break;
    case 'MOVEMENT_ID':
      populatedFlag = this.validateValue(this.instancesTab.mvtColCombo);
      break;
    case 'OTHER_ID':
      populatedFlag = this.validateValue(this.instancesTab.otherIdColCombo);
      break;
    default:
      break;
    }

    return populatedFlag; 
  }

  validateValue(combo) {
    if (combo.selectedLabel) {
      return true;
    } else {
      return false;
    }
  }

  public refreshGridGuiHighlight(): void {
    let recScenarioCheck: boolean = this.generalTab.recordCheck.selected;

    //set row tooltip: list of required columns
    this.guiHighlightTab.guiGrid.customTooltipFunction = (dataContext) => {
      return this.setTooltipMessage(dataContext);
    };
    this.guiHighlightTab.guiGrid.rowColorFunction = (dataContext, dataIndex, color) => {
      return this.changeColorBackground(dataContext, dataIndex, color, recScenarioCheck);

    };
  }

  private setTooltipMessage(dataContext): any {
    // Variable to hold error location
    var errorLocation: number = 0;
    var requiredCols= "Required$#$parameters:$#$";
    try {
      errorLocation = 10;
      requiredCols=dataContext.slickgrid_rowcontent.guiRequiredParams? requiredCols + dataContext.slickgrid_rowcontent.guiRequiredParams.content: "";
    }
    catch (error) {
      // log the error in ERROR LOG
      //SwtUtil.logError(error, this.moduleId, this.getQualifiedClassName(this), "setTooltipMessage", errorLocation);
    }
    return requiredCols;
  }

  eventsChanges() {
    let row = {};
      let data = this.eventsTab.eventsGrid.gridData;
      for (let i = 0; i < data.length; i++) {
        row = {
          'OPERATION': 'I', 'TABLE': "EventMapping", 'SCENARIO_ID': (this.scenarioIdTxt.text).trim(), 'ORDINAL': data[i].eventSeq,'MAP_KEY':data[i].mapKey?data[i].mapKey.toString():"",
          'EVENT_FACILITY_ID': data[i].eventId, 'EVENT_FACILITY_DESC': data[i].eventDescription,'PARAMETERS_XML': data[i].eventParams!="XML"? data[i].eventParams.toString() : (data[i].xmlColumn?data[i].xmlColumn.toString():data[i].slickgrid_rowcontent.eventParams.code.toString()),
          'REPEAT_ON_RERAISE': data[i].eventRepeat, 'EXECUTE_WHEN': data[i].eventExecuteWhen
        };
        this.operationsList.push(row)
      }
  }

  saveHandler() {
    if (this.validateFields()) {
      this.guiHighlightChanges();
      this.eventsChanges();
      this.generalTab.gridParamsToXml();
      let method = (this.methodName == "add") ? 'save' : 'update';
      this.requestParams = [];
      this.actionPath = 'scenMaintenance.do?';
      this.actionMethod = 'method=' + 'save';
      this.sendData.cbStart = this.startOfComms.bind(this);
      this.sendData.cbStop = this.endOfComms.bind(this);
      this.sendData.cbResult = (data) => {
        this.saveResult(data);
      };
      this.sendData.cbFault = this.inputDataFault.bind(this);
      this.sendData.encodeURL = false;
      this.requestParams["saveOrUpdate"] = method;
      this.requestParams["selectedScenarioID"] = (this.scenarioIdTxt.text).trim();
      this.requestParams["selectedSystemFlag"] = this.systemCheck.selected ? "Y" : "";
      this.requestParams["fromAdvanced"] = "true";
      this.requestParams["table"] = this.identificationTab.refTableTxt.text != "None" ? this.identificationTab.refTableTxt.text : "";
      this.requestParams["scenTitle"] = this.titleTxt.text;
      this.requestParams["scenDescription"] = this.descriptionTxt.text;
      this.requestParams["scenSummaryGrouping"] = this.identificationTab.defGroupCombo.selectedValue != "N" ? this.identificationTab.defGroupCombo.selectedValue : "";
      this.requestParams["scenBaseQuery"] = StringUtils.encode64(this.identificationTab.baseQuery.text);
      this.requestParams["scenHostColumn"] = this.identificationTab.hostColCombo.selectedLabel;
      this.requestParams["scenEntityColumn"] = this.identificationTab.entityColCombo.selectedLabel;
      this.requestParams["scenCurrencyColumn"] = this.identificationTab.ccyColCombo.selectedLabel;
      this.requestParams["scenAmountColumn"] = this.identificationTab.amountColCombo.selectedLabel;
      this.requestParams["scenGenericDisplay"] = this.identificationTab.genericCheck.selected ? "Y" : "";
      this.requestParams["scenFacilityID"] = this.identificationTab.facilityCombo.selectedLabel != "None" ? this.identificationTab.facilityCombo.selectedLabel : "";
      this.requestParams["scenRefcolumns"] = JSON.stringify(this.identificationTab.refColumnCombo.selects);
      this.requestParams["scenAlertInstcolumns"] = JSON.stringify(this.instancesTab.alertInstanceColumnCombo.selects);
      this.requestParams["scenParamValues"] = this.identificationTab.paramValueTxt.text;
      this.requestParams["selectedFacilityId"] = this.identificationTab.facilityCombo.selectedLabel != "None" ? this.identificationTab.facilityCombo.selectedLabel : "";
      this.requestParams["scenarioMaintenance.id.scenarioId"] = (this.scenarioIdTxt.text).trim();
      this.requestParams["scenarioMaintenance.systemFlag"] = this.systemCheck.selected ? "Y" : "";
      this.requestParams["scenarioMaintenance.activeFlag"] = this.activeCheck.selected ? "Y" : "";
      this.requestParams["scenarioMaintenance.title"] = this.titleTxt.text;
      this.requestParams["scenarioMaintenance.description"] = this.descriptionTxt.text;
      //general Tab
      this.requestParams["scenarioMaintenance.recordScenarioInstance"] = this.generalTab.recordCheck.selected ? "Y" : "N";
      this.requestParams["scenarioCategory.id.categoryid"] = this.generalTab.categoryCombo.selectedLabel;
      this.requestParams["scenarioMaintenance.displayOrder"] = this.generalTab.orderTxt.text;
      this.requestParams["scenarioMaintenance.runEvery"] = this.generalTab.runTxt.text;
      this.requestParams["scenarioMaintenance.startTime"] = this.generalTab.startTxt.text;
      this.requestParams["scenarioMaintenance.endTime"] = this.generalTab.endTxt.text;
      this.requestParams["scenarioMaintenance.emailWhenDiff"] = this.generalTab.emailTxt.text;
      //Instance Tab
      this.requestParams["scenarioMaintenance.instanceUniqueExpression"] = Encryptor.encode64(this.instancesTab.uniqueExpression.text);
      this.requestParams["scenarioMaintenance.accountColumn"] = this.instancesTab.acctIdCombo.selectedLabel;
      this.requestParams["scenarioMaintenance.signCol"] = this.instancesTab.signColCombo.selectedLabel;
      //Istance Tab fields added in v0.5
      this.requestParams["scenarioMaintenance.mvtColumn"] = this.instancesTab.mvtColCombo.selectedLabel;
      this.requestParams["scenarioMaintenance.matchColumn"] = this.instancesTab.matchColCombo.selectedLabel;
      this.requestParams["scenarioMaintenance.sweepColumn"] = this.instancesTab.sweepColCombo.selectedLabel;
      this.requestParams["scenarioMaintenance.paymentColumn"] = this.instancesTab.payColCombo.selectedLabel;
      this.requestParams["scenarioMaintenance.customTreeLevel1"] = this.instancesTab.treeBreakDown1Combo.selectedLabel;
      this.requestParams["scenarioMaintenance.customTreeLevel2"] = this.instancesTab.treeBreakDown2Combo.selectedLabel;
      this.requestParams["scenarioMaintenance.valueDateColumn"] = this.instancesTab.valueDateCombo.selectedLabel;
      this.requestParams["scenarioMaintenance.otherIdColumn"] = this.instancesTab.otherIdColCombo.selectedLabel;
      this.requestParams["scenarioMaintenance.otherIdTypeColumn"] = this.instancesTab.otherIdTypeCombo.selectedLabel;

      this.requestParams["scenarioMaintenance.instanceExpiryMins"] = this.instancesTab.instExpTxt.text;
      this.requestParams["scenarioMaintenance.allowReraiseAfterExpiry"] = this.instancesTab.raiseRadioGroup.selectedValue;
      if (this.instancesTab.raiseRadioGroup.selectedValue == "A")
        this.requestParams["scenarioMaintenance.reraiseIntervalMins"] = this.instancesTab.afterMinTxt.text;
      //EventTab missing Ref Columns(Bind variables)
      this.requestParams["scenarioMaintenance.afterTrigEvent"] = this.eventsTab.triggeEventsRadioGroup.selectedValue;
      this.requestParams["scenarioMaintenance.scenarioResolutionQueryText"] = Encryptor.encode64(this.eventsTab.resolutionQueryText.text);
      this.requestParams["scenarioMaintenance.pendingResolutionTimeLimit"] = this.eventsTab.resolOverdueRadioGroup.selectedValue=='A'?this.eventsTab.minsText.text:-1;
      this.requestParams["resolutionRefcolumns"] = JSON.stringify(this.eventsTab.refColumnCombo.selects);
      //HighlightGrid
      this.requestParams["operationsList"] = JSON.stringify(this.operationsList);
      this.requestParams["criticalGuiHighlight"] = this.guiHighlightTab.critGuiHighCheck.selected ? "Y" : "N";
      //GeneralTab: save required parameters xml
      this.requestParams["requiredParamsXml"] = this.generalTab.xmlParams ? this.generalTab.xmlParams : "";
      //GeneralTab: save schedule parameters xml
      this.requestParams["genBasis"] = this.generalTab.selectedType;
      this.requestParams["scheduleParams"] = this.scheduleParams ? JSON.stringify(this.scheduleParams) : "";
      this.requestParams["apiRequiredCols"] = this.generalTab.apiRequiredCols  && this.generalTab.typeOptions.selectedValue=='A' ? JSON.stringify(this.generalTab.apiRequiredCols) : "";
      this.sendData.url = this.baseURL + this.actionPath + this.actionMethod;
      this.sendData.send(this.requestParams);
    }


  }

  saveResult(event) {
    let message = SwtUtil.getPredictMessage('errors.DataIntegrityViolationExceptioninAdd', null);
    if (this.sendData.isBusy()) {
      this.sendData.cbStop();
    } else {
      this.lastRecievedJSON = event;
      this.jsonReader.setInputJSON(this.lastRecievedJSON);

      if (this.jsonReader.getRequestReplyMessage() && this.jsonReader.getRequestReplyMessage().indexOf("DataIntegrityViolation") != -1) {
        this.swtalert.error(message, null, Alert.OK, null, () => {
         // ExternalInterface.call('close');
        });
      } else if (!this.jsonReader.getRequestReplyStatus()) {
        this.inputDataFault();
      } else {
        //refresh parent
        ExternalInterface.call("refreshParent");
      }
    }

  }

  cancelHandler() {
    ExternalInterface.call('close')
  }


  updateTreeProvider(col, comboCol) {
    //this.provider.push({ type: "", value: -1, selected: 0, content: "" });
    let requiredCol = ['acctId', 'valueDate', 'signCol', 'otherIdCol', 'entityCol', 'ccyCol'];
    if ((requiredCol.includes(comboCol)) && (!this.provider.some(e => e.content === col))) {
      this.provider.push({ type: "", value: this.getColumnIndex(col), selected: 0, content: col });
      this.provider.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
      this.instancesTab.treeBreakDown1Combo.setComboData(this.provider);
      this.instancesTab.treeBreakDown1Combo.dataProvider = this.provider;
      this.instancesTab.treeBreakDown2Combo.setComboData(this.provider);
      this.instancesTab.treeBreakDown2Combo.dataProvider = this.provider;
      this.instancesTab.treeBreakDown1Combo.selectedLabel = this.defaultCustomTreeLevel1?this.defaultCustomTreeLevel1:this.instancesTab.selectedTreeBreakdown1;
      this.instancesTab.treeBreakDown2Combo.selectedLabel = this.defaultCustomTreeLevel2?this.defaultCustomTreeLevel2:this.instancesTab.selectedTreeBreakdown2;
    }
    this.checkSummaryGrouping();
  }

  checkSummaryGrouping() {
    if (this.identificationTab.defGroupCombo.selectedValue != "N") {
      this.provider = this.provider.filter(x => x.content != '"ENTITY_ID"');
      this.provider = this.provider.filter(x => x.content != '"CURRENCY_CODE"');
    }else{
      if((!this.provider.some(e => e.content === '"ENTITY_ID"')) && (this.identificationTab.entityColCombo.selectedLabel))
      this.provider.push({ type: "", value: 4, selected: 0, content: '"ENTITY_ID"' });
      if((!this.provider.some(e => e.content === '"CURRENCY_CODE"'))  && (this.identificationTab.ccyColCombo.selectedLabel))
      this.provider.push({ type: "", value: 3, selected: 0, content: '"CURRENCY_CODE"' });
        }

    this.provider.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
    this.instancesTab.treeBreakDown1Combo.setComboData(this.provider);
    this.instancesTab.treeBreakDown1Combo.dataProvider = this.provider;
    this.instancesTab.treeBreakDown2Combo.setComboData(this.provider);
    this.instancesTab.treeBreakDown2Combo.dataProvider = this.provider;
    this.instancesTab.treeBreakDown1Combo.selectedLabel =  this.defaultCustomTreeLevel1?this.defaultCustomTreeLevel1:this.instancesTab.selectedTreeBreakdown1;
    this.instancesTab.treeBreakDown2Combo.selectedLabel =  this.defaultCustomTreeLevel2?this.defaultCustomTreeLevel2:this.instancesTab.selectedTreeBreakdown2;
  }

  updateAlertInstProvider(col) {
    this.instancesTab.alertInstanceColumnCombo.isDropdownDisabled = !this.systemCheck.selected?false:true;
    if (!this.alertInstProvider.some(e => e.content === col)) {
      this.alertInstProvider = this.alertInstProvider.concat({ type: "", value: this.getColumnIndex(col), selected: 0, content: col });
      this.alertInstProvider.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
      this.instancesTab.alertInstanceColumnCombo.dataProvider = this.alertInstProvider;
      //to avoid keeping the deleted column selected
      this.instancesTab.alertInstanceColumnCombo.selects=[];
      this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems=[];
    }
  }

  getColumnIndex(column){
      let value = null;

      switch (column) {
      case '"HOST_ID"':
        value = 11;
        break;
      case '"ENTITY_ID"':
        value = 4;
        break;
      case '"CURRENCY_CODE"':
        value =3;
        break;
      case '"ACCOUNT_ID"':
        value = 0;
        break;
      case '"AMOUNT"':
        value = 10;
        break;
      case '"SIGN"':
        value = 1;
        break;
      case '"MATCH_ID"':
        value = 7;
        break;
      case '"SWEEP_ID"':
        value = 8;
        break;
      case '"PAYMENT_ID"':
        value = 9;
        break;
      case '"VALUE_DATE"':
        value = 2;
        break;
      case '"MOVEMENT_ID"':
        value = 6;
        break;
      default:
        value = 5;
        break;
      }
  
      return value; 

  }

  deleteColFromTreeProvider(col, comboCol) {
    //if ((comboCol != 'otherIdCol') && (this.provider.some(x => x.content === col))) {
      this.provider = this.provider.filter(x => x.content != col);
  /* } else {
      this.provider = this.provider.filter(x => x.content != this.instancesTab.otherIdOldVal);
    }*/
    this.instancesTab.treeBreakDown1Combo.setComboData(this.provider);
    this.instancesTab.treeBreakDown1Combo.dataProvider = this.provider;
    this.instancesTab.treeBreakDown2Combo.setComboData(this.provider);
    this.instancesTab.treeBreakDown2Combo.dataProvider = this.provider;

    this.instancesTab.treeBreakDown1Combo.selectedLabel = this.defaultCustomTreeLevel1?this.defaultCustomTreeLevel1:this.instancesTab.selectedTreeBreakdown1;
    this.instancesTab.treeBreakDown2Combo.selectedLabel = this.defaultCustomTreeLevel2?this.defaultCustomTreeLevel2:this.instancesTab.selectedTreeBreakdown2;
  }

  deleteColFromAlertInstProvider(col, comboCol) {
    if ((comboCol != 'otherIdCol') && (this.alertInstProvider.some(x => x.content === col))) {
      this.alertInstProvider = this.alertInstProvider.filter(x => x.content != col);
    } else {
      this.alertInstProvider = this.alertInstProvider.filter(x => x.content != col);
    }
    this.instancesTab.alertInstanceColumnCombo.dataProvider = this.alertInstProvider;
    //to avoid keeping the deleted column selected
    this.instancesTab.alertInstanceColumnCombo.selects = [];
    this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems = [];
  }


  prepareTreeBreakdownProvider() {
    if (this.defaultAccount)
      this.savedTreeProvider.push({ type: "", value: 0, selected: 0, content: '"ACCOUNT_ID"' });

    if (this.defaultSign)
      this.savedTreeProvider.push({ type: "", value: 1, selected: 0, content: '"SIGN"' });

    if (this.defaultValueDate)
      this.savedTreeProvider.push({ type: "", value: 2, selected: 0, content: '"VALUE_DATE"' });

    if (this.defaultCcy && this.identificationTab.defGroupCombo.selectedValue=="N")
      this.savedTreeProvider.push({ type: "", value: 3, selected: 0, content: '"CURRENCY_CODE"' });

    if (this.defaultEntity && this.identificationTab.defGroupCombo.selectedValue=="N")
      this.savedTreeProvider.push({ type: "", value: 4, selected: 0, content: '"ENTITY_ID"' });

    if (this.jsonReader.getSingletons().otherIdColumn && !this.savedTreeProvider.some(x => x.content === this.defaultOtherId ))
      this.savedTreeProvider.push({ type: "", value: 5, selected: 0, content: '"OTHER_ID"'});
    

    /*if (this.jsonReader.getSingletons().otherIdColumn && !this.savedTreeProvider.some(x => x.content === this.defaultOtherId ))
      this.savedTreeProvider.push({ type: "", value: 5, selected: 0, content: this.defaultOtherId });
    */
    this.savedTreeProvider.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
    this.instancesTab.treeBreakDown1Combo.setComboData(this.savedTreeProvider);
    this.instancesTab.treeBreakDown1Combo.dataProvider = this.savedTreeProvider;
    this.instancesTab.treeBreakDown2Combo.setComboData(this.savedTreeProvider);
    this.instancesTab.treeBreakDown2Combo.dataProvider = this.savedTreeProvider;
    this.provider = this.savedTreeProvider;

  }


  prepareAlertInstProvider() {
    this.instancesTab.alertInstanceColumnCombo.isDropdownDisabled=!this.systemCheck.selected?false:true;
    if (this.defaultAccount)
    this.savedAlertInstProvider.push({ type: "", value: 0, selected: 0, content: '"ACCOUNT_ID"' });
    if (this.defaultAmount)
    this.savedAlertInstProvider.push({ type: "", value: 10, selected: 0, content: '"AMOUNT"' });
    if (this.defaultCcy)
    this.savedAlertInstProvider.push({ type: "", value: 3, selected: 0, content: '"CURRENCY_CODE"' });
     if (this.defaultEntity)
     this.savedAlertInstProvider.push({ type: "", value: 4, selected: 0, content: '"ENTITY_ID"' });
    if (this.defaultHost)
    this.savedAlertInstProvider.push({ type: "", value: 11, selected: 0, content: '"HOST_ID"' });    
    if (this.defaultMatchId)
    this.savedAlertInstProvider.push({ type: "", value: 7, selected: 0, content: '"MATCH_ID"' });    
    if (this.defaultMvt)
    this.savedAlertInstProvider.push({ type: "", value: 6, selected: 0, content: '"MOVEMENT_ID"' });
    if (this.jsonReader.getSingletons().otherIdColumn  && !this.savedAlertInstProvider.some(x => x.content === this.defaultOtherId ))
    this.savedAlertInstProvider.push({ type: "", value: 5, selected: 0, content: '"OTHER_ID"'});   
    if (this.defaultPay)
    this.savedAlertInstProvider.push({ type: "", value: 9, selected: 0, content: '"PAYMENT_ID"' });
    if (this.defaultSign)
    this.savedAlertInstProvider.push({ type: "", value: 1, selected: 0, content: '"SIGN"' });   
    if (this.defaultSweepId)
    this.savedAlertInstProvider.push({ type: "", value: 8, selected: 0, content: '"SWEEP_ID"' });
    if (this.defaultValueDate)
    this.savedAlertInstProvider.push({ type: "", value: 2, selected: 0, content: '"VALUE_DATE"' });

    //this.savedAlertInstProvider.push({ type: "", value: 5, selected: 0, content: this.defaultOtherId });

    this.instancesTab.alertInstanceColumnCombo.dataProvider = this.savedAlertInstProvider;
    this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems = this.savedAlertInstCols;
    this.alertInstProvider = this.savedAlertInstProvider;
  }

  fillEventRefColsCombo(col) {
    this.queryColumns = col;
    if (col) {
      let listValues = col.select[0].option;
      const copyData = $.extend(true, [], listValues);
      copyData.splice(0, 1);
      copyData.unshift({ type: "", value: -1, selected: 0, content: '"INSTANCE_ID"' });
      copyData.sort((a, b) => a.content.localeCompare(b.content)); // Sort alphabetically by content
      this.eventsTab.refColumnCombo.dataProvider = copyData;
      this.eventsTab.refColumnCombo.defaultSelectedItems = this.savedEventRefCols;
    }
  }

  disableInstAndEvtsTab(recordCheckFlg){
    //reset instance tab fields values
    this.instancesTab.uniqueExpression.text = "";
    this.instancesTab.acctIdCombo.selectedLabel = "";
    this.instancesTab.valueDateCombo.selectedLabel = "";
    this.instancesTab.signColCombo.selectedLabel = "";
    this.instancesTab.mvtColCombo.selectedLabel = "";
    this.instancesTab.matchColCombo.selectedLabel = "";
    this.instancesTab.sweepColCombo.selectedLabel = "";
    this.instancesTab.payColCombo.selectedLabel = "";
    this.instancesTab.otherIdColCombo.selectedLabel = "";
    this.instancesTab.otherIdTypeCombo.selectedLabel = "";
    this.instancesTab.treeBreakDown1Combo.selectedLabel = "";
    this.instancesTab.treeBreakDown2Combo.selectedLabel = "";
    this.instancesTab.instExpTxt.text = "60";
    this.instancesTab.radioNo.selected = true;
    this.instancesTab.radioAfter.selected = false;
    this.instancesTab.afterMinTxt.text = "0";
    this.instancesTab.afterMinTxt.enabled=false;
    this.instancesTab.alertInstanceColumnCombo.selects=[];
    this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems=[];
   //reset events tab fields values
   this.eventsTab.resolutionQueryText.text="";
   this.eventsTab.refColumnCombo.selects=[];
   this.eventsTab.refColumnCombo.defaultSelectedItems=[];
    if(!recordCheckFlg){
    this.instances.enabled=false;
    this.events.enabled=false;
    }else{
      this.instances.enabled=true;
      this.events.enabled=true;
    } 
  }


  
  getIsScheduledSelected(){
  return this.generalTab.scheduled.selected;
}

clearTypeDesc(){
    this.instancesTab.otherIdTypeDesc.text="";
  }

  saveComboValues(){
    this.savedHostId= this.identificationTab.hostColCombo.selectedLabel;
    this.savedEntityId= this.identificationTab.entityColCombo.selectedLabel;
    this.savedCcyId= this.identificationTab.ccyColCombo.selectedLabel;
    this.savedAmount= this.identificationTab.amountColCombo.selectedLabel;
    this.savedIdentRefCols= this.identificationTab.refColumnCombo.defaultSelectedItems;
    this.savedAcctId= this.instancesTab.acctIdCombo.selectedLabel;
    this.savedValueDate= this.instancesTab.valueDateCombo.selectedLabel; 
    this.savedSign= this.instancesTab.signColCombo.selectedLabel;
    this.savedMvtId= this.instancesTab.mvtColCombo.selectedLabel;
    this.savedMatchId= this.instancesTab.matchColCombo.selectedLabel;
    this.savedSwpId= this.instancesTab.sweepColCombo.selectedLabel;
    this.savedPayId= this.instancesTab.payColCombo.selectedLabel;
    this.savedOtherId= this.instancesTab.otherIdColCombo.selectedLabel;
    this.savedOtherIdType= this.instancesTab.otherIdTypeCombo.selectedLabel;
    this.savedTreeBreakDown1= this.instancesTab.treeBreakDown1Combo.selectedLabel;
    this.savedTreeBreakDown2= this.instancesTab.treeBreakDown2Combo.selectedLabel;
    this.savedAlertInstCols= this.instancesTab.alertInstanceColumnCombo.defaultSelectedItems;
    this.savedEventRefCols= this.eventsTab.refColumnCombo.defaultSelectedItems;
    this.savedOtherIdTypeDesc= this.instancesTab.otherIdTypeDesc.text;
  }


  checkIfInstExist(){
    this.requestParams = [];
    this.inputData.cbStart = this.startOfComms.bind(this);
    this.inputData.cbStop = this.endOfComms.bind(this);
    this.inputData.cbResult = (event) => {
      this.instExistResult(event);
    };
    this.inputData.cbFault = this.inputDataFault.bind(this);
    this.inputData.encodeURL = false;
    this.actionPath = 'scenMaintenance.do?';
    this.actionMethod = 'method=checkIfInstExist';
    this.requestParams['scenarioId'] = (this.scenarioIdTxt.text).trim();
    this.inputData.url = this.baseURL + this.actionPath + this.actionMethod;
    this.inputData.send(this.requestParams);
  }

  instExistResult(event) {
    let flag;
    // Checks the inputData and stops the communication
    if (this.inputData.isBusy()) {
      this.inputData.cbStop();
    } else {
      this.lastRecievedJSON1 = event;
      this.jsonReader1.setInputJSON(this.lastRecievedJSON1);

      if (this.jsonReader1.getRequestReplyStatus()) {
        if ((this.lastRecievedJSON1 != this.prevRecievedJSON1)) {
          if (!this.jsonReader1.isDataBuilding()) {
            flag = this.jsonReader1.getSingletons().isInstExist == "Y" ? true : false;
            if(flag){
              this.eventsTab.unlockFlag= false;
              this.swtalert.warning(SwtUtil.getPredictMessage('scenario.unresolvedInstancesAlert', null));
              this.showLockIcon();
            }else{
              this.eventsTab.unlockFlag= true;
            }

            this.prevRecievedJSON1 = this.lastRecievedJSON1;

          }
        }
      } else {
        if (this.lastRecievedJSON1.hasOwnProperty("request_reply")) {
          this.swtalert.error(this.jsonReader1.getRequestReplyMessage() + "\n" + this.jsonReader1.getRequestReplyLocation(), "Error");
        }
      }

    }   
  }
  htmlEntities(str) {
    try {
    return String(str).replace(/&/g, '&amp;').replace(/</g, '&lt;').
    replace(/>/g, '&gt;').replace(/"/g, '&quot;').replace(/ /g, '&nbsp;');
    } catch (e) {
      console.log('error', e, str)
    }
  }

  confirmListener(event) {
    /* Condition to check Ok button is selected */
    if (event.detail == Alert.YES) {
      this.saveHandler();
    }
  }

  checkIfBaseQuerySecChanged() {
    if (this.identificationTab.baseQuery.text != this.defaultQuery || this.identificationTab.hostColCombo.selectedLabel != this.defaultHost
      || this.identificationTab.entityColCombo.selectedLabel != this.defaultEntity
      || this.identificationTab.ccyColCombo.selectedLabel != this.defaultCcy
      || this.identificationTab.amountColCombo.selectedLabel != this.defaultAmount) {
      return true;
    } else {
      return false;
    }
  }

  checkIfInstTabFieldsChanged() {
    if (this.instancesTab.uniqueExpression.text != this.defaultUniqueExp
      || this.instancesTab.acctIdCombo.selectedLabel != this.defaultAccount
      || this.instancesTab.valueDateCombo.selectedLabel != this.defaultValueDate
      || this.instancesTab.signColCombo.selectedLabel != this.defaultSign
      || this.instancesTab.mvtColCombo.selectedLabel != this.defaultMvt
      || this.instancesTab.matchColCombo.selectedLabel != this.defaultMatchId
      || this.instancesTab.sweepColCombo.selectedLabel != this.defaultSweepId
      || this.instancesTab.payColCombo.selectedLabel != this.defaultPay
      || this.instancesTab.otherIdColCombo.selectedLabel != this.defaultOtherId
      || this.instancesTab.otherIdTypeCombo.selectedLabel != this.defaultOtherIdType) {
      return true;
    } else {
      return false;
    }

  }

  showLockIcon(){
    this.identificationTab.BaseQueryLockImg.visible= true;
    this.identificationTab.hostLockImg.visible= true;
    this.identificationTab.entityLockImg.visible= true;
    this.identificationTab.ccyLockImg.visible= true;
    this.identificationTab.amountLockImg.visible= true;

    this.identificationTab.baseQuery.editable= false;
    this.identificationTab.hostColCombo.enabled= false;
    this.identificationTab.entityColCombo.enabled= false;
    this.identificationTab.ccyColCombo.enabled= false;
    this.identificationTab.amountColCombo.enabled= false;

    this.instancesTab.uniqExpLockImg.visible = true;
    this.instancesTab.acctLockImg.visible = true;
    this.instancesTab.valDateLockImg.visible = true;
    this.instancesTab.signLockImg.visible = true;
    this.instancesTab.mvtLockImg.visible = true;
    this.instancesTab.matchLockImg.visible = true;
    this.instancesTab.sweepLockImg.visible = true;
    this.instancesTab.payLockImg.visible = true;
    this.instancesTab.otherIdLockImg.visible = true;
    this.instancesTab.otherIdTypeLockImg.visible = true;

    this.instancesTab.uniqueExpression.editable = false;
    this.instancesTab.acctIdCombo.enabled = false;
    this.instancesTab.valueDateCombo.enabled = false;
    this.instancesTab.signColCombo.enabled = false;
    this.instancesTab.mvtColCombo.enabled = false;
    this.instancesTab.matchColCombo.enabled = false;
    this.instancesTab.sweepColCombo.enabled = false;
    this.instancesTab.payColCombo.enabled = false;
    this.instancesTab.otherIdColCombo.enabled = false;
    this.instancesTab.otherIdTypeCombo.enabled = false;
   
    this.eventsTab.BtnLockImg.visible = true;
    this.eventsTab.BtnLockImg.includeInLayout = true;

    this.eventsTab.changeButton.enabled = false;  
    this.eventsTab.addButton.enabled = false; 
  }

}

//Define lazy loading routes
const routes: Routes = [
  { path: '', component: ScenarioDetail }
];
export const routing: ModuleWithProviders = RouterModule.forChild(routes);

//Define an NgModule that will be loaded by app module
@NgModule({
  imports: [routing, SwtToolBoxModule],
  declarations: [ScenarioDetail],
  entryComponents: []
})
export class ScenarioDetailModule {
}
