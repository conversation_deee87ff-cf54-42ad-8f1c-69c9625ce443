{"_from": "worker-timers@7.0.7", "_id": "worker-timers@7.0.7", "_inBundle": false, "_integrity": "sha512-8gqDT0bx987YBqf7xBF8dNo0M1jtQtkonT/GPtEShep9YdF+H5E/i+yPwiPBuuCgjFiHDayXXuo62i0bOAvK/g==", "_location": "/worker-timers", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "worker-timers@7.0.7", "name": "worker-timers", "escapedName": "worker-timers", "rawSpec": "7.0.7", "saveSpec": null, "fetchSpec": "7.0.7"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/worker-timers/-/worker-timers-7.0.7.tgz", "_shasum": "055898a792ee418c5cfd04ddf46ef7b4e2bb1f6b", "_spec": "worker-timers@7.0.7", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "author": {"name": "<PERSON>"}, "bugs": {"url": "https://github.com/chrisguttandin/worker-timers/issues"}, "bundleDependencies": false, "config": {"commitizen": {"path": "cz-conventional-changelog"}}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {"@babel/runtime": "^7.12.5", "tslib": "^2.0.3", "worker-timers-broker": "^6.0.29", "worker-timers-worker": "^7.0.4"}, "deprecated": false, "description": "A replacement for setInterval() and setTimeout() which works in unfocused windows.", "devDependencies": {"@babel/core": "^7.12.7", "@babel/plugin-external-helpers": "^7.12.1", "@babel/plugin-transform-runtime": "^7.12.1", "@babel/preset-env": "^7.12.7", "@commitlint/cli": "^11.0.0", "@commitlint/config-angular": "^11.0.0", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-replace": "^2.3.4", "babel-loader": "^8.2.1", "chai": "^4.2.0", "commitizen": "^4.2.2", "cz-conventional-changelog": "^3.3.0", "eslint": "^7.14.0", "eslint-config-holy-grail": "^48.0.3", "grunt": "^1.3.0", "grunt-cli": "^1.3.2", "grunt-contrib-clean": "^2.0.0", "grunt-replace": "^1.0.1", "grunt-sh": "^0.2.0", "grunt-webpack": "^4.0.2", "husky": "^4.3.0", "karma": "^5.2.3", "karma-chrome-launcher": "^3.1.0", "karma-firefox-launcher": "^2.1.0", "karma-mocha": "^2.0.1", "karma-safari-launcher": "^1.0.0", "karma-sauce-launcher": "^4.3.3", "karma-sinon-chai": "^2.0.2", "karma-webpack": "^4.0.2", "load-grunt-config": "^3.0.1", "memfs": "^3.2.0", "mocha": "^8.2.1", "prettier": "^2.2.0", "pretty-quick": "^3.1.0", "rollup": "^2.33.3", "sinon": "^9.2.1", "sinon-chai": "^3.5.0", "terser-webpack-plugin": "^4.2.3", "ts-loader": "^8.0.11", "tsconfig-holy-grail": "^11.0.34", "tslint": "^6.1.3", "tslint-config-holy-grail": "^53.0.8", "typescript": "^4.1.2", "webpack": "^4.44.2"}, "files": ["build/es2019/", "build/es5/", "src/"], "homepage": "https://github.com/chrisguttandin/worker-timers", "husky": {"hooks": {"commit-msg": "commitlint --edit --extends @commitlint/config-angular", "pre-commit": "pretty-quick --config config/prettier/config.json --staged && grunt lint"}}, "keywords": ["Web Workers", "WindowTimers", "clearInterval", "clearTimeout", "interval", "setInterval", "setTimeout"], "license": "MIT", "main": "build/es5/bundle.js", "module": "build/es2019/module.js", "name": "worker-timers", "repository": {"type": "git", "url": "git+https://github.com/chrisguttandin/worker-timers.git"}, "scripts": {"prepublishOnly": "grunt build", "test": "grunt lint && grunt test"}, "types": "build/es2019/module.d.ts", "version": "7.0.7"}