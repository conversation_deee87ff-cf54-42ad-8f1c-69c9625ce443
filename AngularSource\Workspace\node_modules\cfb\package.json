{"_from": "cfb@^1.1.4", "_id": "cfb@1.2.2", "_inBundle": false, "_integrity": "sha512-KfdUZsSOw19/ObEWasvBP/Ac4reZvAGauZhs6S/gqNhXhI7cKwvlH7ulj+dOEYnca4bm4SGo8C1bTAQvnTjgQA==", "_location": "/cfb", "_phantomChildren": {}, "_requested": {"type": "range", "registry": true, "raw": "cfb@^1.1.4", "name": "cfb", "escapedName": "cfb", "rawSpec": "^1.1.4", "saveSpec": null, "fetchSpec": "^1.1.4"}, "_requiredBy": ["/xlsx"], "_resolved": "https://registry.npmjs.org/cfb/-/cfb-1.2.2.tgz", "_shasum": "94e687628c700e5155436dac05f74e08df23bc44", "_spec": "cfb@^1.1.4", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace\\node_modules\\xlsx", "author": {"name": "sheetjs"}, "browser": {"node": false, "process": false, "fs": false}, "bugs": {"url": "https://github.com/SheetJS/js-cfb/issues"}, "bundleDependencies": false, "config": {"blanket": {"pattern": "cfb.js"}}, "dependencies": {"adler-32": "~1.3.0", "crc-32": "~1.2.0"}, "deprecated": false, "description": "Compound File Binary File Format extractor", "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.10.25", "acorn": "7.4.1", "alex": "8.1.1", "blanket": "~1.2.3", "dtslint": "~0.1.2", "eslint": "7.23.0", "eslint-plugin-html": "^6.1.2", "eslint-plugin-json": "^2.1.2", "jscs": "3.0.7", "jshint": "2.13.4", "mocha": "~2.5.3", "typescript": "2.2.0"}, "engines": {"node": ">=0.8"}, "files": ["LICENSE", "README.md", "dist/", "types/index.d.ts", "types/tsconfig.json", "cfb.js", "xlscfb.flow.js"], "homepage": "http://sheetjs.com/", "keywords": ["cfb", "compression", "office"], "license": "Apache-2.0", "main": "./cfb", "name": "cfb", "repository": {"type": "git", "url": "git://github.com/SheetJS/js-cfb.git"}, "scripts": {"dtslint": "dtslint types", "pretest": "make init", "test": "make test"}, "types": "types", "version": "1.2.2"}