{"_from": "@angular/cdk@7.3.2", "_id": "@angular/cdk@7.3.2", "_inBundle": false, "_integrity": "sha512-jnthvY1Kt+DpJTrkgyKTiVuYgBdp4iG7QDeZJPBQm0e8mL2K0Pi9AqFbo01E4CGPqZpvtEggvqM0OJpR8J+amw==", "_location": "/@angular/cdk", "_phantomChildren": {}, "_requested": {"type": "version", "registry": true, "raw": "@angular/cdk@7.3.2", "name": "@angular/cdk", "escapedName": "@angular%2fcdk", "scope": "@angular", "rawSpec": "7.3.2", "saveSpec": null, "fetchSpec": "7.3.2"}, "_requiredBy": ["/", "/swt-tool-box"], "_resolved": "https://registry.npmjs.org/@angular/cdk/-/cdk-7.3.2.tgz", "_shasum": "33f525051d71e2525d29b2aa798c817334973a7b", "_spec": "@angular/cdk@7.3.2", "_where": "C:\\GitWorkspace\\angular\\workspace\\AngularSource\\Workspace", "bugs": {"url": "https://github.com/angular/material2/issues"}, "bundleDependencies": false, "dependencies": {"parse5": "^5.0.0", "tslib": "^1.7.1"}, "deprecated": false, "description": "Angular Material Component Development Kit", "es2015": "./esm2015/cdk.js", "homepage": "https://github.com/angular/material2#readme", "keywords": ["angular", "cdk", "component", "development", "kit"], "license": "MIT", "main": "./bundles/cdk.umd.js", "module": "./esm5/cdk.es5.js", "name": "@angular/cdk", "ng-update": {"migrations": "./schematics/migration.json"}, "optionalDependencies": {"parse5": "^5.0.0"}, "peerDependencies": {"@angular/core": ">=7.0.0", "@angular/common": ">=7.0.0"}, "releaseGitBranch": "7.3.x", "releaseGitCommitSha": "6b699ce9ccee58249d3b5919af614101744a85da", "releaseGitUser": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git+https://github.com/angular/material2.git"}, "schematics": "./schematics/collection.json", "sideEffects": false, "typings": "./cdk.d.ts", "version": "7.3.2"}