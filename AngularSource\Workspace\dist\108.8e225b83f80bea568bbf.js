(window.webpackJsonp=window.webpackJsonp||[]).push([[108],{EDk3:function(l,t,e){"use strict";e.r(t);var i=e("CcnG"),n=e("mrSG"),d=e("447K"),u=e("ZYCi"),h=function(l){function t(t,e){var i=l.call(this,e,t)||this;return i.commonService=t,i.element=e,i.jsonReader=new d.L,i.inputData=new d.G(i.commonService),i.baseURL=d.Wb.getBaseURL(),i.actionMethod="",i.actionPath="",i.requestParams=[],i.logic=new d.h,i.menuAccessIdParent=0,i.fileId=null,i.screenName="Scheduled Report History Details",i.versionNumber="1.00.00",i.releaseDate="25 September 2019",i.screenVersion=new d.V(i.commonService),i.logger=new d.R("Scheduled Report History Details",i.commonService.httpclient),i.swtAlert=new d.bb(t),i}return n.d(t,l),t.prototype.ngOnInit=function(){this.screenVersion.loadScreenVersion(this,this.screenName,this.versionNumber,this.releaseDate),this.lblFileId.text=d.Wb.getPredictMessage("label.schedReportHist.fileId",null)+" *",this.fileIdTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.fileId",null),this.lblScheduleId.text=d.Wb.getPredictMessage("label.schedReportHist.scheduleId",null),this.scheduleIdTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.scheduleId",null),this.lblJobId.text=d.Wb.getPredictMessage("label.schedReportHist.jobId",null),this.jobIdTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.jobId",null),this.lblReportTypeId.text=d.Wb.getPredictMessage("label.schedReportHist.reportTypeId",null),this.reportTypeIdTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.reportTypeId",null),this.lblReportName.text=d.Wb.getPredictMessage("label.schedreporthist.column.reportName",null),this.reportNameTextField.toolTip=d.Wb.getPredictMessage("tooltip.schedreporthist.reportName",null),this.lblFileName.text=d.Wb.getPredictMessage("label.schedReportHist.fileName",null),this.fileNameTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.fileName",null),this.lblOutputLocation.text=d.Wb.getPredictMessage("label.schedReportHist.outputLocation",null),this.outputLocationTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.outputLocation",null),this.lblRunDate.text=d.Wb.getPredictMessage("label.schedReportHist.runDate",null),this.runDateTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.runDate",null),this.lblElapsedTime.text=d.Wb.getPredictMessage("label.schedReportHist.elapsedTime",null),this.elapsedTimeTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.elapsedTime",null),this.lblFileSize.text=d.Wb.getPredictMessage("label.schedReportHist.fileSize",null),this.fileSizeTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.fileSize",null),this.lblExportStatus.text=d.Wb.getPredictMessage("label.schedReportHist.exportStatus",null),this.exportStatusTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.exportStatus",null),this.lblMailStatus.text=d.Wb.getPredictMessage("label.schedReportHist.mailStatus",null),this.mailStatusTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.mailStatus",null),this.lblExportError.text=d.Wb.getPredictMessage("label.schedReportHist.exportError",null),this.exportErrorTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.exportError",null),this.mailResultLabel.text=d.Wb.getPredictMessage("label.schedReportHist.mailRsult",null),this.mailRsultTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.mailRsult",null),this.btnCancel.label=d.Wb.getPredictMessage("button.close",null),this.btnCancel.toolTip=d.Wb.getPredictMessage("tooltip.close",null)},t.prototype.onLoad=function(){var l=this;this.baseURL=d.Wb.getBaseURL(),this.requestParams=[],this.logic.dateFormat=d.x.call("eval","dateFormat"),this.logic.testDate=d.x.call("eval","dbDate"),this.menuAccessIdParent=d.x.call("eval","menuAccessIdParent"),this.screenName=d.x.call("eval","screenName"),this.fileId=d.x.call("eval","fileId"),this.inputData.cbResult=function(t){l.inputDataResult(t)},this.inputData.cbFault=this.inputDataFault.bind(this),this.inputData.encodeURL=!1,this.actionPath="schedReportHist.do?",this.actionMethod="method=displaySchedReportHist",this.actionMethod+="&loadFlex=true",this.actionMethod+="&screenName="+this.screenName,null!=this.screenName&&(this.actionMethod+="&fileId="+this.fileId),this.inputData.url=this.baseURL+this.actionPath+this.actionMethod,this.inputData.send(this.requestParams)},t.prototype.endOfComms=function(){},t.prototype.startOfComms=function(){this.disableInterface()},t.prototype.inputDataFault=function(){var l=d.Wb.getPredictMessage("label.genericException",null);this.swtAlert.error(l)},t.prototype.inputDataResult=function(l){this.logger.info("method [inputDataResult] - START ");try{if(this.inputData.isBusy())this.inputData.cbStop();else if(this.lastRecievedJSON=l,this.jsonReader.setInputJSON(this.lastRecievedJSON),JSON.stringify(this.lastRecievedJSON)!==JSON.stringify(this.prevRecievedJSON)&&this.jsonReader.getRequestReplyStatus())if(this.jsonReader.isDataBuilding()){var t=d.Wb.getPredictMessage("label.genericException",null);this.swtAlert.error(t)}else null!=this.screenName&&"addScreen"!=this.screenName&&(this.fileIdTextField.enabled=!1,this.fileIdTextField.text=this.jsonReader.getSingletons().fileId,this.scheduleIdTextField.text=this.jsonReader.getSingletons().scheduleId,this.selectedScheduleId.text=this.jsonReader.getSingletons().scheduleName,this.jobIdTextField.text=this.jsonReader.getSingletons().jobId,this.selectedJobId.text=this.jsonReader.getSingletons().jobName,this.reportTypeIdTextField.text=this.jsonReader.getSingletons().reportTypeId,this.selectedReportType.text=this.jsonReader.getSingletons().reportTypeName,this.runDateTextField.text=this.jsonReader.getSingletons().runDate,this.reportNameTextField.text=this.jsonReader.getSingletons().reportName,this.elapsedTimeTextField.text=this.jsonReader.getSingletons().elapsedTime,this.fileNameTextField.text=this.jsonReader.getSingletons().fileName,this.outputLocationTextField.text=this.jsonReader.getSingletons().outputLocation,this.fileSizeTextField.text=this.jsonReader.getSingletons().fileSize,this.exportStatusTextField.text=this.jsonReader.getSingletons().exportStatus,this.mailStatusTextField.text=this.jsonReader.getSingletons().mailStatusAsString,this.exportErrorTextField.text=this.jsonReader.getSingletons().exportError,null!=this.jsonReader.getSingletons().mailStatus&&"S"==this.jsonReader.getSingletons().mailStatus?(this.mailRsultTextField.text=this.jsonReader.getSingletons().mailRsult,this.mailResultLabel.text=d.Wb.getPredictMessage("label.schedReportHist.mailRsult",null),this.mailRsultTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.mailRsult",null)):(this.mailRsultTextField.text=this.jsonReader.getSingletons().mailError,this.mailResultLabel.text=d.Wb.getPredictMessage("label.schedReportHist.errorDescription",null),this.mailRsultTextField.toolTip=d.Wb.getPredictMessage("tip.schedReportHist.errorDescription",null))),null!=this.screenName&&"viewScreen"==this.screenName&&this.disableInterface()}catch(e){this.logger.error("method [inputDataResult] - error ",e)}},t.prototype.closeHandler=function(l){d.x.call("close")},t.prototype.disableInterface=function(){this.scheduleIdTextField.editable=!1,this.fileIdTextField.editable=!1,this.jobIdTextField.editable=!1,this.reportTypeIdTextField.editable=!1,this.runDateTextField.editable=!1,this.elapsedTimeTextField.editable=!1,this.fileNameTextField.editable=!1,this.outputLocationTextField.editable=!1,this.fileSizeTextField.editable=!1,this.exportStatusTextField.editable=!1,this.mailStatusTextField.editable=!1,this.exportErrorTextField.editable=!1,this.mailRsultTextField.editable=!1,this.reportNameTextField.editable=!1},t.prototype.enableInterface=function(){this.fileIdTextField.enabled=!0,this.jobIdTextField.enabled=!0,this.reportTypeIdTextField.enabled=!0,this.runDateTextField.enabled=!0,this.elapsedTimeTextField.enabled=!0,this.fileNameTextField.enabled=!0,this.outputLocationTextField.enabled=!0,this.fileSizeTextField.enabled=!0,this.exportStatusTextField.enabled=!0,this.mailStatusTextField.enabled=!0,this.exportErrorTextField.enabled=!0,this.mailRsultTextField.enabled=!0},t.prototype.doHelp=function(){d.x.call("help")},t}(d.yb),o=[{path:"",component:h}],r=(u.l.forChild(o),function(){return function(){}}()),b=e("pMnS"),a=e("RChO"),s=e("t6HQ"),c=e("WFGK"),p=e("5FqG"),g=e("Ip0R"),m=e("gIcY"),I=e("t/Na"),w=e("sE5F"),x=e("OzfB"),R=e("T7CS"),T=e("S7LP"),f=e("6aHO"),S=e("WzUx"),F=e("A7o+"),J=e("zCE2"),A=e("Jg5P"),W=e("3R0m"),C=e("hhbb"),v=e("5rxC"),L=e("Fzqc"),y=e("21Lb"),N=e("hUWP"),G=e("3pJQ"),M=e("V9q+"),H=e("VDKW"),B=e("kXfT"),D=e("BGbe");e.d(t,"SchedReportHistAddModuleNgFactory",function(){return P}),e.d(t,"RenderType_SchedReportHistAdd",function(){return j}),e.d(t,"View_SchedReportHistAdd_0",function(){return E}),e.d(t,"View_SchedReportHistAdd_Host_0",function(){return z}),e.d(t,"SchedReportHistAddNgFactory",function(){return Z});var P=i.Gb(r,[],function(l){return i.Qb([i.Rb(512,i.n,i.vb,[[8,[b.a,a.a,s.a,c.a,p.Cb,p.Pb,p.r,p.rc,p.s,p.Ab,p.Bb,p.Db,p.qd,p.Hb,p.k,p.Ib,p.Nb,p.Ub,p.yb,p.Jb,p.v,p.A,p.e,p.c,p.g,p.d,p.Kb,p.f,p.ec,p.Wb,p.bc,p.ac,p.sc,p.fc,p.lc,p.jc,p.Eb,p.Fb,p.mc,p.Lb,p.nc,p.Mb,p.dc,p.Rb,p.b,p.ic,p.Yb,p.Sb,p.kc,p.y,p.Qb,p.cc,p.hc,p.pc,p.oc,p.xb,p.p,p.q,p.o,p.h,p.j,p.w,p.Zb,p.i,p.m,p.Vb,p.Ob,p.Gb,p.Xb,p.t,p.tc,p.zb,p.n,p.qc,p.a,p.z,p.rd,p.sd,p.x,p.td,p.gc,p.l,p.u,p.ud,p.Tb,Z]],[3,i.n],i.J]),i.Rb(4608,g.m,g.l,[i.F,[2,g.u]]),i.Rb(4608,m.c,m.c,[]),i.Rb(4608,m.p,m.p,[]),i.Rb(4608,I.j,I.p,[g.c,i.O,I.n]),i.Rb(4608,I.q,I.q,[I.j,I.o]),i.Rb(5120,I.a,function(l){return[l,new d.tb]},[I.q]),i.Rb(4608,I.m,I.m,[]),i.Rb(6144,I.k,null,[I.m]),i.Rb(4608,I.i,I.i,[I.k]),i.Rb(6144,I.b,null,[I.i]),i.Rb(4608,I.f,I.l,[I.b,i.B]),i.Rb(4608,I.c,I.c,[I.f]),i.Rb(4608,w.c,w.c,[]),i.Rb(4608,w.g,w.b,[]),i.Rb(5120,w.i,w.j,[]),i.Rb(4608,w.h,w.h,[w.c,w.g,w.i]),i.Rb(4608,w.f,w.a,[]),i.Rb(5120,w.d,w.k,[w.h,w.f]),i.Rb(5120,i.b,function(l,t){return[x.j(l,t)]},[g.c,i.O]),i.Rb(4608,R.a,R.a,[]),i.Rb(4608,T.a,T.a,[]),i.Rb(4608,f.a,f.a,[i.n,i.L,i.B,T.a,i.g]),i.Rb(4608,S.c,S.c,[i.n,i.g,i.B]),i.Rb(4608,S.e,S.e,[S.c]),i.Rb(4608,F.l,F.l,[]),i.Rb(4608,F.h,F.g,[]),i.Rb(4608,F.c,F.f,[]),i.Rb(4608,F.j,F.d,[]),i.Rb(4608,F.b,F.a,[]),i.Rb(4608,F.k,F.k,[F.l,F.h,F.c,F.j,F.b,F.m,F.n]),i.Rb(4608,S.i,S.i,[[2,F.k]]),i.Rb(4608,S.r,S.r,[S.L,[2,F.k],S.i]),i.Rb(4608,S.t,S.t,[]),i.Rb(4608,S.w,S.w,[]),i.Rb(1073742336,u.l,u.l,[[2,u.r],[2,u.k]]),i.Rb(1073742336,g.b,g.b,[]),i.Rb(1073742336,m.n,m.n,[]),i.Rb(1073742336,m.l,m.l,[]),i.Rb(1073742336,J.a,J.a,[]),i.Rb(1073742336,A.a,A.a,[]),i.Rb(1073742336,m.e,m.e,[]),i.Rb(1073742336,W.a,W.a,[]),i.Rb(1073742336,F.i,F.i,[]),i.Rb(1073742336,S.b,S.b,[]),i.Rb(1073742336,I.e,I.e,[]),i.Rb(1073742336,I.d,I.d,[]),i.Rb(1073742336,w.e,w.e,[]),i.Rb(1073742336,C.b,C.b,[]),i.Rb(1073742336,v.b,v.b,[]),i.Rb(1073742336,x.c,x.c,[]),i.Rb(1073742336,L.a,L.a,[]),i.Rb(1073742336,y.d,y.d,[]),i.Rb(1073742336,N.c,N.c,[]),i.Rb(1073742336,G.a,G.a,[]),i.Rb(1073742336,M.a,M.a,[[2,x.g],i.O]),i.Rb(1073742336,H.b,H.b,[]),i.Rb(1073742336,B.a,B.a,[]),i.Rb(1073742336,D.b,D.b,[]),i.Rb(1073742336,d.Tb,d.Tb,[]),i.Rb(1073742336,r,r,[]),i.Rb(256,I.n,"XSRF-TOKEN",[]),i.Rb(256,I.o,"X-XSRF-TOKEN",[]),i.Rb(256,"config",{},[]),i.Rb(256,F.m,void 0,[]),i.Rb(256,F.n,void 0,[]),i.Rb(256,"popperDefaults",{},[]),i.Rb(1024,u.i,function(){return[[{path:"",component:h}]]},[])])}),k=[[""]],j=i.Hb({encapsulation:0,styles:k,data:{}});function E(l){return i.dc(0,[i.Zb(402653184,1,{_container:0}),i.Zb(402653184,2,{selectedScheduleId:0}),i.Zb(402653184,3,{selectedJobId:0}),i.Zb(402653184,4,{selectedReportType:0}),i.Zb(402653184,5,{mailResultLabel:0}),i.Zb(402653184,6,{lblFileId:0}),i.Zb(402653184,7,{lblScheduleId:0}),i.Zb(402653184,8,{lblJobId:0}),i.Zb(402653184,9,{lblReportTypeId:0}),i.Zb(402653184,10,{lblReportName:0}),i.Zb(402653184,11,{lblFileName:0}),i.Zb(402653184,12,{lblOutputLocation:0}),i.Zb(402653184,13,{lblRunDate:0}),i.Zb(402653184,14,{lblElapsedTime:0}),i.Zb(402653184,15,{lblFileSize:0}),i.Zb(402653184,16,{lblExportStatus:0}),i.Zb(402653184,17,{lblMailStatus:0}),i.Zb(402653184,18,{lblExportError:0}),i.Zb(402653184,19,{fileIdTextField:0}),i.Zb(402653184,20,{scheduleIdTextField:0}),i.Zb(402653184,21,{jobIdTextField:0}),i.Zb(402653184,22,{reportTypeIdTextField:0}),i.Zb(402653184,23,{reportNameTextField:0}),i.Zb(402653184,24,{fileNameTextField:0}),i.Zb(402653184,25,{outputLocationTextField:0}),i.Zb(402653184,26,{runDateTextField:0}),i.Zb(402653184,27,{elapsedTimeTextField:0}),i.Zb(402653184,28,{fileSizeTextField:0}),i.Zb(402653184,29,{exportStatusTextField:0}),i.Zb(402653184,30,{mailStatusTextField:0}),i.Zb(402653184,31,{mailRsultTextField:0}),i.Zb(402653184,32,{exportErrorTextField:0}),i.Zb(402653184,33,{btnCancel:0}),(l()(),i.Jb(33,0,null,null,175,"SwtModule",[["height","100%"],["id","mainModule"],["width","100%"]],null,[[null,"creationComplete"]],function(l,t,e){var i=!0,n=l.component;"creationComplete"===t&&(i=!1!==n.onLoad()&&i);return i},p.ad,p.hb)),i.Ib(34,4440064,null,0,d.yb,[i.r,d.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},{creationComplete:"creationComplete"}),(l()(),i.Jb(35,0,null,0,173,"VBox",[["height","100%"],["id","vbParent"],["paddingLeft","17"],["paddingRight","17"],["paddingTop","15"],["width","100%"]],null,null,null,p.od,p.vb)),i.Ib(36,4440064,null,0,d.ec,[i.r,d.i,i.T],{id:[0,"id"],width:[1,"width"],height:[2,"height"],paddingTop:[3,"paddingTop"],paddingLeft:[4,"paddingLeft"],paddingRight:[5,"paddingRight"]},null),(l()(),i.Jb(37,0,null,0,161,"SwtCanvas",[["height","92%"],["id","attAccContainer"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(38,4440064,null,0,d.db,[i.r,d.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"]},null),(l()(),i.Jb(39,0,null,0,159,"Grid",[["height","100%"],["paddingTop","5"],["width","100%"]],null,null,null,p.Cc,p.H)),i.Ib(40,4440064,null,0,d.z,[i.r,d.i],{width:[0,"width"],height:[1,"height"],paddingTop:[2,"paddingTop"]},null),(l()(),i.Jb(41,0,null,0,9,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(42,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(43,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(44,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(45,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblFileId"],["width","150"]],null,null,null,p.Yc,p.fb)),i.Ib(46,4440064,[[6,4],["lblFileId",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(l()(),i.Jb(47,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(48,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(49,0,null,0,1,"SwtTextInput",[["id","fileIdTextField"],["maxChars","100"],["width","100"]],null,null,null,p.kd,p.sb)),i.Ib(50,4440064,[[19,4],["fileIdTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(51,0,null,0,15,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(52,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(53,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(54,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(55,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(56,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(57,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblScheduleId"]],null,null,null,p.Yc,p.fb)),i.Ib(58,4440064,[[7,4],["lblScheduleId",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(59,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(60,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(61,0,null,0,1,"SwtTextInput",[["id","scheduleIdTextField"],["maxChars","100"],["width","100"]],null,null,null,p.kd,p.sb)),i.Ib(62,4440064,[[20,4],["scheduleIdTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(63,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(64,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(65,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedScheduleId"]],null,null,null,p.Yc,p.fb)),i.Ib(66,4440064,[[2,4],["selectedScheduleId",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(67,0,null,0,15,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(68,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(69,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(70,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(71,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(72,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(73,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblJobId"],["width","150"]],null,null,null,p.Yc,p.fb)),i.Ib(74,4440064,[[8,4],["lblJobId",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(l()(),i.Jb(75,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(76,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(77,0,null,0,1,"SwtTextInput",[["id","jobIdTextField"],["maxChars","100"],["width","100"]],null,null,null,p.kd,p.sb)),i.Ib(78,4440064,[[21,4],["jobIdTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(79,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(80,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(81,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedJobId"]],null,null,null,p.Yc,p.fb)),i.Ib(82,4440064,[[3,4],["selectedJobId",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(83,0,null,0,15,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(84,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(85,0,null,0,9,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(86,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(87,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(88,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(89,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblReportTypeId"],["width","150"]],null,null,null,p.Yc,p.fb)),i.Ib(90,4440064,[[9,4],["lblReportTypeId",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],width:[1,"width"],fontWeight:[2,"fontWeight"]},null),(l()(),i.Jb(91,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(92,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(93,0,null,0,1,"SwtTextInput",[["id","reportTypeIdTextField"],["maxChars","150"],["width","150"]],null,null,null,p.kd,p.sb)),i.Ib(94,4440064,[[22,4],["reportTypeIdTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(95,0,null,0,3,"GridItem",[["width","50%"]],null,null,null,p.Ac,p.I)),i.Ib(96,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(97,0,null,0,1,"SwtLabel",[["fontWeight","normal"],["id","selectedReportType"]],null,null,null,p.Yc,p.fb)),i.Ib(98,4440064,[[4,4],["selectedReportType",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(99,0,null,0,9,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(100,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(101,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(102,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(103,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblReportName"]],null,null,null,p.Yc,p.fb)),i.Ib(104,4440064,[[10,4],["lblReportName",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(105,0,null,0,3,"GridItem",[["width","100%"]],null,null,null,p.Ac,p.I)),i.Ib(106,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(107,0,null,0,1,"SwtTextInput",[["id","reportNameTextField"],["maxChars","150"],["width","80%"]],null,null,null,p.kd,p.sb)),i.Ib(108,4440064,[[23,4],["reportNameTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(109,0,null,0,9,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(110,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(111,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(112,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(113,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblFileName"]],null,null,null,p.Yc,p.fb)),i.Ib(114,4440064,[[11,4],["lblFileName",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(115,0,null,0,3,"GridItem",[["width","100%"]],null,null,null,p.Ac,p.I)),i.Ib(116,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(117,0,null,0,1,"SwtTextInput",[["id","fileNameTextField"],["maxChars","100"],["width","80%"]],null,null,null,p.kd,p.sb)),i.Ib(118,4440064,[[24,4],["fileNameTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(119,0,null,0,9,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(120,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(121,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(122,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(123,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblOutputLocation"]],null,null,null,p.Yc,p.fb)),i.Ib(124,4440064,[[12,4],["lblOutputLocation",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(125,0,null,0,3,"GridItem",[["width","100%"]],null,null,null,p.Ac,p.I)),i.Ib(126,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(127,0,null,0,1,"SwtTextInput",[["id","outputLocationTextField"],["maxChars","100"],["width","80%"]],null,null,null,p.kd,p.sb)),i.Ib(128,4440064,[[25,4],["outputLocationTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(129,0,null,0,9,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(130,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(131,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(132,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(133,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblRunDate"]],null,null,null,p.Yc,p.fb)),i.Ib(134,4440064,[[13,4],["lblRunDate",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(135,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(136,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(137,0,null,0,1,"SwtTextInput",[["id","runDateTextField"],["maxChars","150"],["width","200"]],null,null,null,p.kd,p.sb)),i.Ib(138,4440064,[[26,4],["runDateTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(139,0,null,0,9,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(140,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(141,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(142,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(143,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblElapsedTime"]],null,null,null,p.Yc,p.fb)),i.Ib(144,4440064,[[14,4],["lblElapsedTime",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(145,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(146,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(147,0,null,0,1,"SwtTextInput",[["id","elapsedTimeTextField"],["maxChars","150"],["width","200"]],null,null,null,p.kd,p.sb)),i.Ib(148,4440064,[[27,4],["elapsedTimeTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(149,0,null,0,9,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(150,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(151,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(152,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(153,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblFileSize"]],null,null,null,p.Yc,p.fb)),i.Ib(154,4440064,[[15,4],["lblFileSize",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(155,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(156,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(157,0,null,0,1,"SwtTextInput",[["id","fileSizeTextField"],["maxChars","100"],["width","200"]],null,null,null,p.kd,p.sb)),i.Ib(158,4440064,[[28,4],["fileSizeTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(159,0,null,0,9,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(160,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(161,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(162,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(163,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblExportStatus"]],null,null,null,p.Yc,p.fb)),i.Ib(164,4440064,[[16,4],["lblExportStatus",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(165,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(166,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(167,0,null,0,1,"SwtTextInput",[["id","exportStatusTextField"],["maxChars","100"],["width","200"]],null,null,null,p.kd,p.sb)),i.Ib(168,4440064,[[29,4],["exportStatusTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(169,0,null,0,9,"GridRow",[["height","5%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(170,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(171,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(172,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(173,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblMailStatus"]],null,null,null,p.Yc,p.fb)),i.Ib(174,4440064,[[17,4],["lblMailStatus",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(175,0,null,0,3,"GridItem",[],null,null,null,p.Ac,p.I)),i.Ib(176,4440064,null,0,d.A,[i.r,d.i],null,null),(l()(),i.Jb(177,0,null,0,1,"SwtTextInput",[["id","mailStatusTextField"],["maxChars","100"],["width","200"]],null,null,null,p.kd,p.sb)),i.Ib(178,4440064,[[30,4],["mailStatusTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"]},null),(l()(),i.Jb(179,0,null,0,9,"GridRow",[["height","19%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(180,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(181,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(182,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(183,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","lblExportError"]],null,null,null,p.Yc,p.fb)),i.Ib(184,4440064,[[18,4],["lblExportError",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(185,0,null,0,3,"GridItem",[["height","100%"],["width","100%"]],null,null,null,p.Ac,p.I)),i.Ib(186,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(187,0,null,0,1,"SwtTextInput",[["height","100"],["id","exportErrorTextField"],["maxChars","100"],["width","100%"]],null,null,null,p.kd,p.sb)),i.Ib(188,4440064,[[32,4],["exportErrorTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"],height:[3,"height"]},null),(l()(),i.Jb(189,0,null,0,9,"GridRow",[["height","19%"],["width","100%"]],null,null,null,p.Bc,p.J)),i.Ib(190,4440064,null,0,d.B,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(191,0,null,0,3,"GridItem",[["width","150"]],null,null,null,p.Ac,p.I)),i.Ib(192,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"]},null),(l()(),i.Jb(193,0,null,0,1,"SwtLabel",[["fontWeight","bold"],["id","mailResultLabel"]],null,null,null,p.Yc,p.fb)),i.Ib(194,4440064,[[5,4],["mailResultLabel",4]],0,d.vb,[i.r,d.i],{id:[0,"id"],fontWeight:[1,"fontWeight"]},null),(l()(),i.Jb(195,0,null,0,3,"GridItem",[["height","100%"],["width","100%"]],null,null,null,p.Ac,p.I)),i.Ib(196,4440064,null,0,d.A,[i.r,d.i],{width:[0,"width"],height:[1,"height"]},null),(l()(),i.Jb(197,0,null,0,1,"SwtTextInput",[["height","100"],["id","mailRsultTextField"],["maxChars","100"],["width","100%"]],null,null,null,p.kd,p.sb)),i.Ib(198,4440064,[[31,4],["mailRsultTextField",4]],0,d.Rb,[i.r,d.i],{maxChars:[0,"maxChars"],id:[1,"id"],width:[2,"width"],height:[3,"height"]},null),(l()(),i.Jb(199,0,null,0,9,"SwtCanvas",[["height","5%"],["id","swtButtonBar"],["marginTop","5"],["width","100%"]],null,null,null,p.Nc,p.U)),i.Ib(200,4440064,null,0,d.db,[i.r,d.i],{id:[0,"id"],width:[1,"width"],height:[2,"height"],marginTop:[3,"marginTop"]},null),(l()(),i.Jb(201,0,null,0,3,"HBox",[["height","100%"],["horizontalAlign","left"],["left","11"],["paddingLeft","2"],["verticalAlign","middle"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(202,4440064,null,0,d.C,[i.r,d.i],{left:[0,"left"],horizontalAlign:[1,"horizontalAlign"],verticalAlign:[2,"verticalAlign"],width:[3,"width"],height:[4,"height"],paddingLeft:[5,"paddingLeft"]},null),(l()(),i.Jb(203,0,null,0,1,"SwtButton",[["buttonMode","true"],["id","btnCancel"]],null,[[null,"click"]],function(l,t,e){var i=!0,n=l.component;"click"===t&&(i=!1!==n.closeHandler(e)&&i);return i},p.Mc,p.T)),i.Ib(204,4440064,[[33,4],["btnCancel",4]],0,d.cb,[i.r,d.i],{id:[0,"id"],buttonMode:[1,"buttonMode"]},{onClick_:"click"}),(l()(),i.Jb(205,0,null,0,3,"HBox",[["height","100%"],["horizontalAlign","right"],["paddingRight","30"],["paddingTop","3"],["verticalAlign","middle"],["width","100%"]],null,null,null,p.Dc,p.K)),i.Ib(206,4440064,null,0,d.C,[i.r,d.i],{horizontalAlign:[0,"horizontalAlign"],verticalAlign:[1,"verticalAlign"],width:[2,"width"],height:[3,"height"],paddingTop:[4,"paddingTop"],paddingRight:[5,"paddingRight"]},null),(l()(),i.Jb(207,0,null,0,1,"SwtHelpButton",[["right","30"]],null,[[null,"click"]],function(l,t,e){var i=!0,n=l.component;"click"===t&&(i=!1!==n.doHelp()&&i);return i},p.Wc,p.db)),i.Ib(208,4440064,null,0,d.rb,[i.r,d.i],{right:[0,"right"]},{onClick_:"click"})],function(l,t){l(t,34,0,"mainModule","100%","100%");l(t,36,0,"vbParent","100%","100%","15","17","17");l(t,38,0,"attAccContainer","100%","92%");l(t,40,0,"100%","100%","5");l(t,42,0,"100%","5%");l(t,44,0,"150");l(t,46,0,"lblFileId","150","bold"),l(t,48,0);l(t,50,0,"100","fileIdTextField","100");l(t,52,0,"100%","5%");l(t,54,0,"50%");l(t,56,0,"150");l(t,58,0,"lblScheduleId","bold"),l(t,60,0);l(t,62,0,"100","scheduleIdTextField","100");l(t,64,0,"50%");l(t,66,0,"selectedScheduleId","normal");l(t,68,0,"100%","5%");l(t,70,0,"50%"),l(t,72,0);l(t,74,0,"lblJobId","150","bold"),l(t,76,0);l(t,78,0,"100","jobIdTextField","100");l(t,80,0,"50%");l(t,82,0,"selectedJobId","normal");l(t,84,0,"100%","5%");l(t,86,0,"50%"),l(t,88,0);l(t,90,0,"lblReportTypeId","150","bold"),l(t,92,0);l(t,94,0,"150","reportTypeIdTextField","150");l(t,96,0,"50%");l(t,98,0,"selectedReportType","normal");l(t,100,0,"100%","5%");l(t,102,0,"150");l(t,104,0,"lblReportName","bold");l(t,106,0,"100%");l(t,108,0,"150","reportNameTextField","80%");l(t,110,0,"100%","5%");l(t,112,0,"150");l(t,114,0,"lblFileName","bold");l(t,116,0,"100%");l(t,118,0,"100","fileNameTextField","80%");l(t,120,0,"100%","5%");l(t,122,0,"150");l(t,124,0,"lblOutputLocation","bold");l(t,126,0,"100%");l(t,128,0,"100","outputLocationTextField","80%");l(t,130,0,"100%","5%");l(t,132,0,"150");l(t,134,0,"lblRunDate","bold"),l(t,136,0);l(t,138,0,"150","runDateTextField","200");l(t,140,0,"100%","5%");l(t,142,0,"150");l(t,144,0,"lblElapsedTime","bold"),l(t,146,0);l(t,148,0,"150","elapsedTimeTextField","200");l(t,150,0,"100%","5%");l(t,152,0,"150");l(t,154,0,"lblFileSize","bold"),l(t,156,0);l(t,158,0,"100","fileSizeTextField","200");l(t,160,0,"100%","5%");l(t,162,0,"150");l(t,164,0,"lblExportStatus","bold"),l(t,166,0);l(t,168,0,"100","exportStatusTextField","200");l(t,170,0,"100%","5%");l(t,172,0,"150");l(t,174,0,"lblMailStatus","bold"),l(t,176,0);l(t,178,0,"100","mailStatusTextField","200");l(t,180,0,"100%","19%");l(t,182,0,"150");l(t,184,0,"lblExportError","bold");l(t,186,0,"100%","100%");l(t,188,0,"100","exportErrorTextField","100%","100");l(t,190,0,"100%","19%");l(t,192,0,"150");l(t,194,0,"mailResultLabel","bold");l(t,196,0,"100%","100%");l(t,198,0,"100","mailRsultTextField","100%","100");l(t,200,0,"swtButtonBar","100%","5%","5");l(t,202,0,"11","left","middle","100%","100%","2");l(t,204,0,"btnCancel","true");l(t,206,0,"right","middle","100%","100%","3","30");l(t,208,0,"30")},null)}function z(l){return i.dc(0,[(l()(),i.Jb(0,0,null,null,1,"app-sched-report-hist-add",[],null,null,null,E,j)),i.Ib(1,4440064,null,0,h,[d.i,i.r],null,null)],function(l,t){l(t,1,0)},null)}var Z=i.Fb("app-sched-report-hist-add",h,z,{maxChars:"maxChars",restrict:"restrict",id:"id",dropShadowEnabled:"dropShadowEnabled",cornerRadius:"cornerRadius",borderThickness:"borderThickness",borderStyle:"borderStyle",borderColor:"borderColor",backGroundColor:"backGroundColor",right:"right",left:"left",bottom:"bottom",top:"top",horizontalGap:"horizontalGap",verticalGap:"verticalGap",textAlign:"textAlign",toolTip:"toolTip",toolTipPreviousValue:"toolTipPreviousValue",textDictionaryId:"tooltipDictionaryId",name:"name",styleName:"styleName",horizontalAlign:"horizontalAlign",verticalAlign:"verticalAlign",width:"width",showScrollBar:"showScrollBar",height:"height",minHeight:"minHeight",minWidth:"minWidth",maxHeight:"maxHeight",maxWidth:"maxWidth",includeInLayout:"includeInLayout",visible:"visible",enabled:"enabled",paddingTop:"paddingTop",paddingBottom:"paddingBottom",paddingLeft:"paddingLeft",paddingRight:"paddingRight",marginTop:"marginTop",marginBottom:"marginBottom",marginLeft:"marginLeft",marginRight:"marginRight",contextMenu:"contextMenu"},{onClick_:"click",dbClick_:"dbClick",doubleClick_:"doubleClick",itemDoubleClick_:"itemDoubleClick",onKeyDown_:"keyDown",onKeyUp_:"keyUp",mouseUp_:"mouseUp",mouseOver_:"mouseOver",mouseDown_:"mouseDown",mouseEnter_:"mouseEnter",mouseLeave_:"mouseLeave",mouseOut_:"mouseOut",mouseIn_:"mouseIn",mouseMove_:"mouseMove",focus_:"focus",focusIn_:"focusIn",onFocusOut_:"focusOut",keyFocusChange_:"keyFocusChange",change_:"change",onSpyChange:"onSpyChange",onSpyNoChange:"onSpyNoChange",scroll_:"scroll",creationComplete:"creationComplete",preinitialize:"preinitialize"},[])}}]);